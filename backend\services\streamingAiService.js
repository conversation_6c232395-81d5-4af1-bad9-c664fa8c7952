/**
 * Streaming AI Service Implementation
 *
 * Based on ai-system.md recommendations:
 * - Reduces perceived latency from 140s to 1-3s for first response
 * - Implements WebSocket/SSE for token-by-token streaming
 * - Uses Google Gemini streaming capabilities
 *
 * This addresses the critical performance issue identified in the system.
 */

const { GoogleGenerativeAI } = require("@google/generative-ai");
const logger = require("../utils/logger");
const { EventEmitter } = require("events");
const scraperService = require("../scrapers/ScraperService");
const Crawl4AIService = require("../scrapers/crawl4ai/crawl4ai-service");
const priceLookupService = require("./priceLookupService");

class StreamingAiService extends EventEmitter {
  constructor() {
    super();
    this.genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY);

    // Optimized model configuration per ai-system.md
    this.models = {
      flash: this.genAI.getGenerativeModel({
        model: "models/gemini-2.0-flash", // Fast initial estimates
        generationConfig: {
          temperature: 0.2, // Low temperature for consistent pricing
          maxOutputTokens: 8000, // Increased for complex JSON responses
          topP: 0.8,
          topK: 40,
        },
      }),
      pro: this.genAI.getGenerativeModel({
        model: "models/gemini-2.5-pro-preview-03-25", // Detailed calculations
        generationConfig: {
          temperature: 0.1, // Very low for pricing accuracy
          maxOutputTokens: 8000, // Increased for complex JSON responses
          topP: 0.9,
          topK: 50,
        },
      }),
    };

    // Use Map for session storage (sessionId -> session object)
    this.streamingSessions = new Map();

    // Keep a separate Map for session management (sessionId -> weak reference key)
    this.sessionRegistry = new Map();

    // Track active timers for cleanup
    this.activeTimers = new Set();

    // Session configuration with improved defaults
    this.sessionConfig = {
      maxSessionAge: 30 * 60 * 1000, // 30 minutes total session lifetime
      maxIdleTime: 15 * 60 * 1000, // Increased from 10 to 15 minutes idle timeout
      cleanupInterval: 60 * 1000, // Check every 1 minute (more frequent)
      maxConcurrentSessions: 50, // Reduced from 100 for better resource management
      sessionWarningThreshold: 80, // Alert when sessions reach 80%
      maxConnectionAttempts: 3, // Max SSE connection attempts
      connectionTimeout: 30 * 1000, // 30 seconds connection timeout
      heartbeatInterval: 15 * 1000, // 15 seconds heartbeat
      gracefulShutdownTimeout: 5 * 1000, // 5 seconds for graceful shutdown
    };

    // Enhanced session monitoring and metrics
    this.sessionStats = {
      totalSessions: 0,
      peakSessions: 0,
      sessionsCreated: 0,
      sessionsCleaned: 0,
      sessionsExpired: 0,
      connectionsLost: 0,
      lastCleanupTime: Date.now(),
      averageSessionDuration: 0,
      connectionErrors: 0,
      heartbeatsMissed: 0,
    };

    // Start periodic cleanup
    this.startPeriodicCleanup();

    // Graceful shutdown handler
    this.setupGracefulShutdown();
  }

  /**
   * Create a new streaming session with enhanced validation and session limit enforcement
   */
  createSession(sessionId, formData) {
    // Check concurrent session limit before creating new session
    if (this.sessionRegistry.size >= this.sessionConfig.maxConcurrentSessions) {
      // Clean up expired sessions first
      this.performSessionCleanup();

      // Check again after cleanup
      if (
        this.sessionRegistry.size >= this.sessionConfig.maxConcurrentSessions
      ) {
        // Force cleanup of oldest sessions
        this.cleanupOldestSessions(
          Math.ceil(this.sessionConfig.maxConcurrentSessions * 0.1)
        );

        if (
          this.sessionRegistry.size >= this.sessionConfig.maxConcurrentSessions
        ) {
          throw new Error(
            `Session limit reached (${this.sessionConfig.maxConcurrentSessions}). Please try again later.`
          );
        }
      }
    }

    // Generate sessionId if not provided with better uniqueness
    if (!sessionId) {
      const timestamp = Date.now();
      const randomStr = Math.random().toString(36).substr(2, 12);
      const processId = process.pid || Math.random().toString(36).substr(2, 4);
      sessionId = `ses_${timestamp}_${processId}_${randomStr}`;
    }

    // Validate sessionId
    if (
      typeof sessionId !== "string" ||
      sessionId.length < 3 ||
      sessionId.length > 128
    ) {
      throw new Error(
        `Invalid sessionId: must be string between 3-128 chars, got ${typeof sessionId}, length: ${
          sessionId?.length
        }`
      );
    }

    // Check if session already exists - handle race conditions
    if (this.sessionRegistry.has(sessionId)) {
      logger.warn(
        `[StreamingAI] Session ${sessionId} already exists, checking if expired`
      );
      const existingSession = this.streamingSessions.get(sessionId);

      if (existingSession) {
        const now = Date.now();
        const sessionAge = now - existingSession.startTime;
        const idleTime =
          now - (existingSession.lastActivity || existingSession.startTime);

        // If existing session is expired, clean it up, otherwise throw error
        if (
          sessionAge > this.sessionConfig.maxSessionAge ||
          idleTime > this.sessionConfig.maxIdleTime
        ) {
          logger.info(
            `[StreamingAI] Cleaning up expired existing session ${sessionId}`
          );
          this.cleanupSession(sessionId);
        } else {
          throw new Error(
            `Session ${sessionId} already exists and is still active. Use a different sessionId.`
          );
        }
      }
    }

    // Validate that sessionRegistry is still a Map (defensive programming)
    if (!(this.sessionRegistry instanceof Map)) {
      logger.error(`[StreamingAI] sessionRegistry corrupted, reinitializing`);
      this.sessionRegistry = new Map();
    }

    if (!(this.streamingSessions instanceof Map)) {
      logger.error(`[StreamingAI] streamingSessions corrupted, reinitializing`);
      this.streamingSessions = new Map();
    }

    const now = Date.now();
    const session = {
      sessionId: sessionId,
      startTime: now,
      lastActivity: now,
      status: "initialized",
      progress: 0,
      formData: formData,
      sseResponse: null,
      streamingStarted: false,
      cleanupScheduled: false,
      connectionAttempts: 0,
      lastHeartbeat: null,
      createdAt: new Date().toISOString(),
      userAgent: formData?.userAgent || "unknown",
      ipAddress: formData?.ipAddress || "unknown",
      // Add protection flags to prevent premature cleanup
      protectedUntil: now + 5 * 60 * 1000, // Protected for 5 minutes
      isNewSession: true,
    };

    // Store in both session storage and registry atomically
    try {
      this.streamingSessions.set(sessionId, session);
      this.sessionRegistry.set(sessionId, true);

      // Update session stats
      this.sessionStats.sessionsCreated++;
      this.sessionStats.totalSessions = this.sessionRegistry.size;
      this.sessionStats.peakSessions = Math.max(
        this.sessionStats.peakSessions,
        this.sessionRegistry.size
      );

      logger.info(
        `[StreamingAI] Created session ${sessionId} (${this.sessionRegistry.size}/${this.sessionConfig.maxConcurrentSessions} active sessions)`
      );

      // Warn if approaching session limit
      if (
        this.sessionRegistry.size >=
        this.sessionConfig.maxConcurrentSessions *
          (this.sessionConfig.sessionWarningThreshold / 100)
      ) {
        logger.warn(
          `[StreamingAI] Session usage high: ${this.sessionRegistry.size}/${this.sessionConfig.maxConcurrentSessions} sessions active`
        );
      }

      return sessionId;
    } catch (error) {
      logger.error(
        `[StreamingAI] Failed to create session ${sessionId}:`,
        error
      );
      // Cleanup any partial state
      this.streamingSessions.delete(sessionId);
      this.sessionRegistry.delete(sessionId);
      throw error;
    }
  }

  /**
   * Attach SSE response to session with connection monitoring
   */
  attachSSEResponse(sessionId, res) {
    const session = this.streamingSessions.get(sessionId);
    if (!session) {
      logger.warn(
        `[StreamingAI] Cannot attach SSE to non-existent session ${sessionId}`
      );
      return false;
    }

    // Store the response object
    session.sseResponse = res;
    session.lastActivity = Date.now();
    session.isNewSession = false; // Mark as no longer new once SSE connects
    session.sseConnectedAt = Date.now(); // Track when SSE was established
    // Extend protection period once SSE is connected to allow for streaming completion
    session.protectedUntil = Date.now() + 10 * 60 * 1000; // Protected for 10 minutes after SSE connection

    // Store event listener references for cleanup
    const closeHandler = () => {
      logger.debug(
        `[StreamingAI] SSE connection closed for session ${sessionId}`
      );
      this.handleConnectionClose(sessionId);
    };

    const errorHandler = (error) => {
      logger.error(
        `[StreamingAI] SSE connection error for session ${sessionId}:`,
        error
      );
      this.handleConnectionError(sessionId, error);
    };

    // Store handlers on session for cleanup
    session._eventHandlers = {
      close: closeHandler,
      error: errorHandler,
    };

    // Set up connection monitoring with stored references
    res.on("close", closeHandler);
    res.on("error", errorHandler);

    return true;
  }

  /**
   * Detach SSE response from session
   */
  detachSSEResponse(sessionId) {
    const session = this.streamingSessions.get(sessionId);
    if (session) {
      // Remove event listeners if they exist
      if (session.sseResponse && session._eventHandlers) {
        session.sseResponse.removeListener(
          "close",
          session._eventHandlers.close
        );
        session.sseResponse.removeListener(
          "error",
          session._eventHandlers.error
        );
        delete session._eventHandlers;
      }

      session.sseResponse = null;
      session.lastActivity = Date.now();
    }
  }

  /**
   * Send SSE event to client with enhanced error handling and validation
   */
  sendSSEEvent(sessionId, eventType, data) {
    if (!sessionId || typeof sessionId !== "string") {
      logger.warn(
        `[StreamingAI] Invalid sessionId for SSE event: ${sessionId}`
      );
      return false;
    }

    const session = this.streamingSessions.get(sessionId);
    if (!session) {
      logger.warn(`[StreamingAI] Session ${sessionId} not found for SSE event`);
      return false;
    }

    if (!session.sseResponse) {
      logger.debug(
        `[StreamingAI] No active SSE connection for session ${sessionId}`
      );
      return false;
    }

    try {
      const res = session.sseResponse;

      // Comprehensive connection state check
      if (res.destroyed || res.finished || res.writableEnded || !res.writable) {
        logger.warn(
          `[StreamingAI] SSE connection in invalid state for session ${sessionId}`,
          {
            destroyed: res.destroyed,
            finished: res.finished,
            writableEnded: res.writableEnded,
            writable: res.writable,
          }
        );
        this.handleConnectionClose(sessionId);
        return false;
      }

      // Validate data before sending
      if (data && typeof data === "object") {
        try {
          // Test JSON serialization
          const testSerialization = JSON.stringify(data);
          if (testSerialization.length > 64 * 1024) {
            // 64KB limit
            logger.warn(
              `[StreamingAI] Data payload too large for session ${sessionId}: ${testSerialization.length} bytes`
            );
            data = {
              ...data,
              truncated: true,
              originalSize: testSerialization.length,
            };
          }
        } catch (jsonError) {
          logger.error(
            `[StreamingAI] Data not serializable for session ${sessionId}:`,
            jsonError
          );
          data = { error: "Data serialization failed", type: typeof data };
        }
      }

      // Update session activity
      const now = Date.now();
      session.lastActivity = now;
      session.lastHeartbeat = now;

      // Send event with proper SSE format and error handling
      const eventLine =
        eventType && eventType !== "message" ? `event: ${eventType}\n` : "";
      const dataLine = `data: ${JSON.stringify(data)}\n\n`;
      const fullMessage = eventLine + dataLine;

      // Write with error handling
      const writeSuccess = res.write(fullMessage);
      if (!writeSuccess) {
        logger.warn(
          `[StreamingAI] SSE write buffer full for session ${sessionId}`
        );
        // Connection might be slow, but still return true as data is queued
      }

      // Flush to ensure immediate delivery
      if (res.flush) {
        try {
          res.flush();
        } catch (flushError) {
          logger.debug(
            `[StreamingAI] SSE flush failed for session ${sessionId}:`,
            flushError.message
          );
          // Non-critical error, continue
        }
      }

      // Reduced debug logging - only log important events, not every chunk
      if (eventType !== 'chunk' || data.type === 'error' || data.type === 'completed' || data.type?.includes('complete')) {
        logger.debug(
          `[StreamingAI] SSE event sent to session ${sessionId}: ${eventType} (${data.type || 'unknown'})`
        );
      }
      return true;
    } catch (error) {
      logger.error(
        `[StreamingAI] Failed to send SSE event for ${sessionId}:`,
        error
      );
      // Handle connection error gracefully
      this.handleConnectionError(sessionId, error);
      return false;
    }
  }

  /**
   * Start streaming quote generation with progressive updates
   *
   * @param {string} sessionId - Unique session identifier
   */
  async startStreamingQuoteGeneration(sessionId) {
    const session = this.streamingSessions.get(sessionId);
    if (!session) {
      throw new Error(`Session ${sessionId} not found`);
    }

    try {
      logger.info(`[StreamingAI] Starting streaming session ${sessionId}`);

      // Update session status
      session.status = "starting";
      const formData = session.formData;

      // Phase 1: Quick initial estimate using Flash model (1-3 seconds)
      await this.streamPhase1QuickEstimate(sessionId, formData);

      // 🆕 PHASE 4: Clarification Check - NEW STREAMING CLARIFICATION SUPPORT
      const needsClarification = await this.streamPhase1_5ClarificationCheck(
        sessionId,
        formData
      );

      if (needsClarification) {
        // Streaming will pause here and wait for user clarification
        // The session remains active and will resume via processClarificationAnswers()
        logger.info(
          `[StreamingAI] Session ${sessionId} paused for clarification`
        );
        return; // Exit early - streaming will resume when answers are received
      }

      // Phase 2: Detailed analysis using Pro model (progressive)
      await this.streamPhase2DetailedAnalysis(sessionId, formData);

      // Phase 3: Price lookup with streaming updates
      await this.streamPhase3PriceLookup(sessionId, formData);

      // Complete the session
      session.status = "completed";
      session.endTime = Date.now();
      session.totalTime = session.endTime - session.startTime;

      logger.info(
        `[StreamingAI] Session ${sessionId} completed in ${session.totalTime}ms`
      );

      // Send completion event
      this.sendSSEEvent(sessionId, "chunk", {
        type: "completed",
        sessionId,
        totalTime: session.totalTime,
        status: "completed",
        data: session.finalResults,
      });

      // Signal client to close connection gracefully
      this.sendSSEEvent(sessionId, "close", {
        message: "Stream finished. Closing connection.",
      });

      // Close SSE connection
      if (session.sseResponse) {
        setTimeout(() => {
          if (session.sseResponse) {
            session.sseResponse.end();
          }
          this.cleanupSession(sessionId);
        }, 10000); // 10-second grace period for diagnostics for diagnostics
      }
    } catch (error) {
      logger.error(`[StreamingAI] Session ${sessionId} failed:`, error);

      // Determine error type for better client handling
      const errorType = this.categorizeError(error);

      // Send error event with categorization
      this.sendSSEEvent(sessionId, "error", {
        type: "error",
        errorType: errorType,
        error: error.message,
        retryable: errorType === "network" || errorType === "rate_limit",
        timestamp: new Date().toISOString(),
      });

      // Close SSE connection gracefully
      if (session && session.sseResponse) {
        try {
          session.sseResponse.end();
        } catch (closeError) {
          logger.error(
            `[StreamingAI] Error closing SSE connection for ${sessionId}:`,
            closeError
          );
        }
      }

      this.cleanupSession(sessionId);
    }
  }

  /**
   * Phase 1: Quick initial estimate (1-3 second response)
   */
  async streamPhase1QuickEstimate(sessionId, formData) {
    logger.debug(
      `[StreamingAI] Phase 1: Quick estimate for session ${sessionId}`
    );

    const session = this.streamingSessions.get(sessionId);
    session.status = "quick_estimate";
    session.progress = 10;

    // Send initial progress
    this.sendSSEEvent(sessionId, "chunk", {
      type: "phase_1_progress",
      phase: "quick_estimate",
      progress: 10,
      message: "Analyzing project requirements...",
      timestamp: new Date().toISOString(),
    });

    try {
      const promptData = this.buildQuickEstimatePrompt(formData);

      // Prepare content for Gemini with images if available
      let content = [{ text: promptData.text }];
      if (promptData.images && promptData.images.length > 0) {
        logger.info(
          `[StreamingAI] Phase 1: Including ${promptData.images.length} images in analysis`
        );
        content = content.concat(promptData.images);
      }

      // Stream response from Flash model with enhanced error handling
      const result = await this.models.flash.generateContentStream(content);

      let quickEstimate = "";
      let chunkCount = 0;
      const maxChunks = 150; // Increased limit for complex responses

      try {
        for await (const chunk of result.stream) {
          // Safety check for session existence
          if (!this.streamingSessions.has(sessionId)) {
            logger.warn(
              `[StreamingAI] Session ${sessionId} was cleaned up during streaming`
            );
            break;
          }

          // Prevent infinite streaming
          if (++chunkCount > maxChunks) {
            logger.warn(
              `[StreamingAI] Max chunks reached for session ${sessionId}`
            );
            break;
          }

          const chunkText = chunk.text();
          if (!chunkText) continue; // Skip empty chunks

          quickEstimate += chunkText;

          // Send incremental updates with connection check
          const sent = this.sendSSEEvent(sessionId, "chunk", {
            type: "phase_1_progress",
            phase: "quick_estimate",
            progress: Math.min(30, session.progress + 2),
            message: "Processing estimate...",
            content: chunkText,
            accumulated: quickEstimate,
            timestamp: new Date().toISOString(),
          });

          // Break if connection is lost
          if (!sent) {
            logger.info(
              `[StreamingAI] Client disconnected during Phase 1 for session ${sessionId}`
            );
            break;
          }

          session.progress = Math.min(30, session.progress + 2);

          // Add small delay to prevent overwhelming the client
          await new Promise((resolve) => setTimeout(resolve, 10));
        }
      } catch (streamError) {
        logger.error(
          `[StreamingAI] Streaming error in Phase 1 for session ${sessionId}:`,
          streamError
        );
        throw new Error(`Streaming failed: ${streamError.message}`);
      }

      // Parse quick estimate
      const parsedEstimate = this.parseQuickEstimate(quickEstimate);

      this.sendSSEEvent(sessionId, "chunk", {
        type: "phase_1_complete",
        phase: "quick_estimate",
        progress: 30,
        data: parsedEstimate,
        message: "Initial estimate complete. Generating detailed analysis...",
        timestamp: new Date().toISOString(),
      });

      session.quickEstimate = parsedEstimate;
      session.progress = 30;
    } catch (error) {
      logger.error(
        `[StreamingAI] Phase 1 error for session ${sessionId}:`,
        error
      );
      this.sendSSEEvent(sessionId, "chunk", {
        type: "error",
        phase: "quick_estimate",
        error: error.message,
        timestamp: new Date().toISOString(),
      });
    }
  }

  /**
   * 🆕 PHASE 4: Phase 1.5 - Clarification Detection for Streaming AI
   *
   * This method implements interactive clarification support in streaming mode,
   * addressing the architecture gap identified in the repository wiki.
   *
   * @param {string} sessionId - Session identifier
   * @param {Map} formData - Original form data
   * @returns {boolean} - true if clarification needed, false to continue streaming
   */
  async streamPhase1_5ClarificationCheck(sessionId, formData) {
    logger.debug(
      `[StreamingAI] Phase 1.5: Clarification check for session ${sessionId}`
    );

    const session = this.streamingSessions.get(sessionId);
    if (!session) {
      logger.error(
        `[StreamingAI] Session ${sessionId} not found in clarification check`
      );
      throw new Error(`Session ${sessionId} not found`);
    }

    session.status = "checking_clarification";
    session.progress = 32;

    // Send clarification check progress
    this.sendSSEEvent(sessionId, "chunk", {
      type: "clarification_check_progress",
      phase: "clarification_check",
      progress: 32,
      message: "Analyzing if additional information is needed...",
      timestamp: new Date().toISOString(),
    });

    try {
      // Build clarification detection prompt
      const clarificationPrompt = this.buildClarificationDetectionPrompt(
        formData,
        session.quickEstimate
      );

      logger.debug(
        `[StreamingAI] Sending clarification detection prompt for session ${sessionId}`
      );

      // Use Flash model for quick clarification detection
      const result = await this.models.flash.generateContent(
        clarificationPrompt
      );
      const response = result.response.text();

      logger.debug(
        `[StreamingAI] Clarification detection response for session ${sessionId}:`,
        response.substring(0, 200)
      );

      // Parse the response to determine if clarification is needed
      const clarificationAnalysis = this.parseClarificationResponse(response);

      if (
        clarificationAnalysis.needsClarification &&
        clarificationAnalysis.questions.length > 0
      ) {
        logger.info(
          `[StreamingAI] Session ${sessionId} requires clarification: ${clarificationAnalysis.questions.length} questions`
        );

        // Store clarification data in session
        session.clarificationQuestions = clarificationAnalysis.questions;
        session.clarificationReason = clarificationAnalysis.reason;
        session.awaitingClarification = true;
        session.pausedAtPhase = "after_phase_1";
        session.status = "pending_clarification";

        // Send clarification required event
        this.sendSSEEvent(sessionId, "chunk", {
          type: "clarification_required",
          phase: "clarification_required",
          progress: 35,
          questions: clarificationAnalysis.questions,
          reason: clarificationAnalysis.reason,
          message: "Additional information needed to provide accurate quote",
          sessionId: sessionId,
          timestamp: new Date().toISOString(),
        });

        // Return true to indicate clarification is needed (streaming pauses)
        return true;
      } else {
        logger.info(
          `[StreamingAI] Session ${sessionId} has sufficient information, proceeding with detailed analysis`
        );

        // Send clarification not needed event
        this.sendSSEEvent(sessionId, "chunk", {
          type: "clarification_not_needed",
          phase: "clarification_check",
          progress: 35,
          message:
            "Sufficient information available, proceeding with detailed analysis...",
          timestamp: new Date().toISOString(),
        });

        // Return false to continue with normal streaming flow
        return false;
      }
    } catch (error) {
      logger.error(
        `[StreamingAI] Clarification check error for session ${sessionId}:`,
        error
      );

      // Send error event but continue streaming (fallback behavior)
      this.sendSSEEvent(sessionId, "chunk", {
        type: "clarification_check_error",
        phase: "clarification_check",
        progress: 35,
        error: `Clarification check failed: ${error.message}`,
        message: "Proceeding without clarification check...",
        timestamp: new Date().toISOString(),
      });

      // Return false to continue streaming even if clarification check fails
      return false;
    }
  }

  /**
   * Phase 2: Detailed analysis with streaming
   */
  async streamPhase2DetailedAnalysis(sessionId, formData) {
    logger.debug(
      `[StreamingAI] Phase 2: Detailed analysis for session ${sessionId}`
    );

    const session = this.streamingSessions.get(sessionId);
    if (!session) {
      logger.error(`[StreamingAI] Session ${sessionId} not found in Phase 2`);
      throw new Error(`Session ${sessionId} not found`);
    }
    session.status = "detailed_analysis";
    session.progress = 35;

    this.sendSSEEvent(sessionId, "chunk", {
      type: "phase_2_progress",
      phase: "detailed_analysis",
      progress: 35,
      message: "Generating detailed project breakdown...",
      timestamp: new Date().toISOString(),
    });

    try {
      const promptData = this.buildDetailedAnalysisPrompt(
        formData,
        session.quickEstimate
      );

      // Prepare content for Gemini with images if available
      let content = [{ text: promptData.text }];
      if (promptData.images && promptData.images.length > 0) {
        logger.info(
          `[StreamingAI] Phase 2: Including ${promptData.images.length} images in detailed analysis`
        );
        content = content.concat(promptData.images);
      }

      // Stream response from Pro model with enhanced error handling
      const result = await this.models.pro.generateContentStream(content);

      let detailedAnalysis = "";
      let chunkCount = 0;
      const maxChunks = 200; // Increased limit for detailed analysis

      try {
        for await (const chunk of result.stream) {
          // Safety check for session existence
          if (!this.streamingSessions.has(sessionId)) {
            logger.warn(
              `[StreamingAI] Session ${sessionId} was cleaned up during streaming`
            );
            break;
          }

          // Prevent infinite streaming
          if (++chunkCount > maxChunks) {
            logger.warn(
              `[StreamingAI] Max chunks reached for session ${sessionId}`
            );
            break;
          }

          const chunkText = chunk.text();
          if (!chunkText) continue; // Skip empty chunks

          detailedAnalysis += chunkText;

          // Send incremental updates with connection check
          const sent = this.sendSSEEvent(sessionId, "chunk", {
            type: "phase_2_progress",
            phase: "detailed_analysis",
            progress: Math.min(70, session.progress + 1),
            message: "Analyzing details...",
            data: { partial: detailedAnalysis },
            timestamp: new Date().toISOString(),
          });

          // Break if connection is lost
          if (!sent) {
            logger.info(
              `[StreamingAI] Client disconnected during Phase 2 for session ${sessionId}`
            );
            break;
          }

          session.progress = Math.min(70, session.progress + 1);

          // Add small delay to prevent overwhelming the client
          await new Promise((resolve) => setTimeout(resolve, 15));
        }
      } catch (streamError) {
        logger.error(
          `[StreamingAI] Streaming error in Phase 2 for session ${sessionId}:`,
          streamError
        );
        throw new Error(`Streaming failed: ${streamError.message}`);
      }

      // Parse detailed analysis
      const parsedAnalysis = this.parseDetailedAnalysis(detailedAnalysis);

      this.sendSSEEvent(sessionId, "chunk", {
        type: "phase_2_complete",
        phase: "detailed_analysis",
        progress: 70,
        data: parsedAnalysis,
        message: "Detailed analysis complete. Looking up current pricing...",
        timestamp: new Date().toISOString(),
      });

      session.detailedAnalysis = parsedAnalysis;
      session.progress = 70;
    } catch (error) {
      logger.error(
        `[StreamingAI] Phase 2 error for session ${sessionId}:`,
        error
      );
      this.sendSSEEvent(sessionId, "chunk", {
        type: "error",
        phase: "detailed_analysis",
        error: error.message,
        timestamp: new Date().toISOString(),
      });
    }
  }

  /**
   * Phase 3: Price lookup with streaming updates
   */
  async streamPhase3PriceLookup(sessionId, formData) {
    logger.debug(
      `[StreamingAI] Phase 3: Price lookup for session ${sessionId}`
    );

    const session = this.streamingSessions.get(sessionId);
    if (!session) {
      logger.error(`[StreamingAI] Session ${sessionId} not found in Phase 3`);
      throw new Error(`Session ${sessionId} not found`);
    }
    session.status = "price_lookup";
    session.progress = 75;

    const items = session.detailedAnalysis?.items || [];
    const totalItems = items.length;
    let processedItems = 0;

    for (let i = 0; i < items.length; i++) {
      const item = items[i];

      this.sendSSEEvent(sessionId, "chunk", {
        type: "phase_3_progress",
        phase: "price_lookup",
        progress: 75 + (processedItems / totalItems) * 20,
        message: `Looking up pricing for: ${item.name || item.description}`,
        timestamp: new Date().toISOString(),
      });

      try {
        // Simulate price lookup with confidence scoring (as per ai-system.md)
        const priceResult = await this.lookupItemPriceWithConfidence(item);

        items[i] = { ...item, ...priceResult };

        this.sendSSEEvent(sessionId, "chunk", {
          type: "phase_3_item_complete",
          phase: "price_lookup",
          progress: 75 + ((processedItems + 1) / totalItems) * 20,
          itemId: `item_${i}`,
          data: items[i],
          message: `Price found for ${item.name || item.description}`,
          timestamp: new Date().toISOString(),
        });
      } catch (error) {
        logger.warn(`[StreamingAI] Price lookup failed for item ${i}:`, error);
        items[i].priceStatus = "lookup_failed";
        items[i].confidence = 0;
      }

      processedItems++;
    }

    session.progress = 95;
    session.finalResults = {
      ...session.detailedAnalysis,
      items: items,
    };

    this.sendSSEEvent(sessionId, "chunk", {
      type: "phase_3_complete",
      phase: "price_lookup",
      progress: 95,
      data: session.finalResults,
      message: "Price lookup complete. Finalizing quote...",
      timestamp: new Date().toISOString(),
    });
  }

  /**
   * Build optimized prompt for quick estimate
   */
  buildQuickEstimatePrompt(formData) {
    // Handle both FormData and regular objects
    const getFormValue = (key, defaultValue = "") => {
      if (formData && typeof formData.get === "function") {
        return formData.get(key) || defaultValue;
      }
      return formData?.[key] || defaultValue;
    };

    const projectOverview =
      getFormValue("projectOverview") ||
      getFormValue("description") ||
      "Electrical project";
    const jobType = getFormValue("jobType", "electrical");
    const location = getFormValue("location", "");

    // Check for uploaded files/images
    const files = getFormValue("files");
    const hasImages = files && files.length > 0;

    let imagePromptAddition = "";
    if (hasImages) {
      imagePromptAddition = `\n\nIMAGES PROVIDED: ${files.length} image(s) uploaded. Analyze these images carefully for electrical equipment, conditions, and requirements.`;
    }

    return {
      text: `You are a ${jobType} estimator. Provide a QUICK initial estimate for this project:

PROJECT: ${projectOverview}
LOCATION: ${location}
TYPE: ${jobType}${imagePromptAddition}

Respond with JSON format:
{
  "estimate_range": "$X,XXX - $Y,YYY",
  "complexity": "low|medium|high",
  "time_estimate": "X-Y days",
  "major_categories": ["category1", "category2", "category3"]
}

Keep response under 100 tokens for speed.`,
      images: hasImages ? this.prepareImagesForGemini(files) : [],
    };
  }

  /**
   * Prepare images for Gemini API
   */
  prepareImagesForGemini(files) {
    const fs = require("fs");
    const images = [];

    if (!files || !Array.isArray(files)) {
      return images;
    }

    for (const file of files) {
      try {
        // Read the image file
        const imageBuffer = fs.readFileSync(file.path);

        images.push({
          inlineData: {
            data: imageBuffer.toString("base64"),
            mimeType: file.mimetype,
          },
        });

        logger.debug(
          `[StreamingAI] Prepared image for Gemini: ${file.originalname} (${file.size} bytes)`
        );
      } catch (error) {
        logger.error(
          `[StreamingAI] Error preparing image ${file.originalname}:`,
          error
        );
      }
    }

    return images;
  }

  /**
   * Build detailed analysis prompt
   */
  buildDetailedAnalysisPrompt(formData, quickEstimate) {
    // Handle both FormData and regular objects
    const getFormValue = (key, defaultValue = "") => {
      if (formData && typeof formData.get === "function") {
        return formData.get(key) || defaultValue;
      }
      return formData?.[key] || defaultValue;
    };

    const projectOverview =
      getFormValue("projectOverview") ||
      getFormValue("description") ||
      "Electrical project";
    const location = getFormValue("location", "");
    const timeline = getFormValue("timeline", "");
    const budget = getFormValue("budget", "");
    const specialRequirements = getFormValue("specialRequirements", "");
    const jobType = getFormValue("jobType", "electrical");

    // Check for uploaded files/images
    const files = getFormValue("files");
    const hasImages = files && files.length > 0;

    let imagePromptAddition = "";
    if (hasImages) {
      imagePromptAddition = `\n\nIMAGES PROVIDED: ${files.length} image(s) uploaded showing electrical equipment and conditions. Carefully examine these images for:\n- Equipment brands, types, and conditions\n- Visible wiring conditions and issues\n- Safety hazards and code violations\n- Electrical panel details and labeling\n- Installation environment and constraints\n\nIncorporate your detailed image analysis into the project assessment without referencing the images directly in client-facing content.`;
    }

    return {
      text: `You are an expert ${jobType} estimator. Create a DETAILED analysis for this project:

PROJECT DETAILS:
- Overview: ${projectOverview}
- Location: ${location}
- Timeline: ${timeline}
- Budget: ${budget}
- Special Requirements: ${specialRequirements}
- Job Type: ${jobType}${imagePromptAddition}

QUICK ESTIMATE CONTEXT:
${JSON.stringify(quickEstimate, null, 2)}

Provide detailed breakdown in JSON format:
{
  "breakdown": {
    "materials": [
      {
        "description": "item description",
        "quantity": number,
        "unit": "unit type",
        "unitPrice": number,
        "totalPrice": number
      }
    ],
    "labor": {
      "hours": number,
      "rate": number
    }
  },
  "total": number,
  "projectOverview": "detailed project summary",
  "scopeOfWork": "comprehensive scope description",
  "materialsIncluded": "detailed materials list",
  "items": [
    {
      "description": "item description",
      "quantity": number,
      "unit": "unit type",
      "unitPrice": number,
      "totalPrice": number
    }
  ],
  "laborHours": number,
  "materialsCost": number,
  "totalCost": number
}

Be thorough and specific.`,
      images: hasImages ? this.prepareImagesForGemini(files) : [],
    };
  }

  /**
   * Parse quick estimate response
   */
  parseQuickEstimate(response) {
    try {
      // Extract JSON from response
      const jsonMatch = response.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        const parsed = JSON.parse(jsonMatch[0]);
        // Map to expected test properties
        return {
          estimate:
            parsed.estimate_range ||
            parsed.estimate ||
            "Estimate in progress...",
          timeframe: parsed.time_estimate || parsed.timeframe || "TBD",
          confidence: parsed.confidence || "medium",
          complexity: parsed.complexity || "medium",
          major_categories: parsed.major_categories || ["Analysis in progress"],
        };
      }
    } catch (error) {
      logger.warn("[StreamingAI] Failed to parse quick estimate JSON:", error);
    }

    // Fallback parsing with expected properties
    return {
      estimate: "Estimate in progress...",
      timeframe: "TBD",
      confidence: "medium",
      complexity: "medium",
      major_categories: ["Analysis in progress"],
      rawResponse: response,
    };
  }

  /**
   * Parse detailed analysis response
   */
  parseDetailedAnalysis(response) {
    try {
      // First try to parse the entire response as JSON
      if (response.trim().startsWith("{")) {
        try {
          const parsed = JSON.parse(response);
          // Ensure expected test properties exist
          return {
            breakdown: parsed.breakdown || {
              materials: parsed.materials || [],
              labor: parsed.labor || { hours: 0, rate: 0 },
            },
            total: parsed.total || 0,
            ...parsed,
          };
        } catch (e) {
          // Continue to fallback methods
        }
      }

      // Try to extract JSON from markdown code blocks
      const codeBlockMatch = response.match(/```(?:json)?\s*([\s\S]*?)```/);
      if (codeBlockMatch) {
        try {
          return JSON.parse(codeBlockMatch[1]);
        } catch (e) {
          logger.warn(
            "[StreamingAI] Failed to parse JSON from code block:",
            e.message
          );
        }
      }

      // Try to find JSON object in the response
      const jsonMatch = response.match(/\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\}/);
      if (jsonMatch) {
        try {
          // Clean up common JSON issues
          let cleanJson = jsonMatch[0]
            .replace(/,\s*([}\]])/g, "$1") // Remove trailing commas
            .replace(/(['"])([^'"]*?)\1/g, (match, quote, content) => {
              // Escape unescaped quotes inside strings
              return `"${content.replace(/"/g, '\\"')}"`;
            });
          return JSON.parse(cleanJson);
        } catch (e) {
          logger.warn(
            "[StreamingAI] Failed to parse extracted JSON:",
            e.message
          );
        }
      }

      // Try to build structured data from text analysis
      const lines = response.split("\n").filter((line) => line.trim());
      const result = {
        projectOverview: "",
        scopeOfWork: "",
        materialsIncluded: "",
        items: [],
      };

      // Extract sections from text
      let currentSection = null;
      for (const line of lines) {
        if (line.toLowerCase().includes("project overview")) {
          currentSection = "projectOverview";
        } else if (line.toLowerCase().includes("scope of work")) {
          currentSection = "scopeOfWork";
        } else if (line.toLowerCase().includes("materials")) {
          currentSection = "materialsIncluded";
        } else if (
          line.toLowerCase().includes("items:") ||
          line.toLowerCase().includes("line items")
        ) {
          currentSection = "items";
        } else if (currentSection && currentSection !== "items") {
          result[currentSection] +=
            (result[currentSection] ? " " : "") + line.trim();
        }
      }

      // If we got some meaningful data, return it
      if (
        result.projectOverview ||
        result.scopeOfWork ||
        result.materialsIncluded
      ) {
        logger.info("[StreamingAI] Parsed detailed analysis from text format");
        return result;
      }
    } catch (error) {
      logger.warn(
        "[StreamingAI] Failed to parse detailed analysis JSON:",
        error
      );
    }

    // Return default structure if all parsing attempts fail with expected test properties
    return {
      breakdown: {
        materials: [],
        labor: { hours: 0, rate: 0 },
      },
      total: 0,
      projectOverview: "Analysis in progress...",
      scopeOfWork: "Detailed scope being generated...",
      materialsIncluded: "Material list being compiled...",
      items: [],
      rawResponse: response,
    };
  }

  /**
   * Lookup item price with confidence scoring using real crawl4ai integration
   * Provides detailed SSE progress updates during the lookup process
   */
  async lookupItemPriceWithConfidence(
    itemData,
    sessionId = null,
    itemIndex = 0
  ) {
    // Handle both single item object and separate parameters
    let item, itemCategory, itemDescription;
    if (typeof itemData === "object") {
      item = itemData.name || itemData.description || "Unknown Item";
      itemCategory = itemData.category || "general";
      itemDescription = itemData.description || itemData.name || "Unknown Item";
    } else {
      item = itemData || "Unknown Item";
      itemCategory = "general";
      itemDescription = itemData || "Unknown Item";
    }

    // Enhanced logging for price scraping diagnostics
    logger.info(
      `[PRICE_SCRAPING] Starting real price lookup for: "${item}" (category: ${itemCategory})`
    );
    logger.info(
      `[PRICE_SCRAPING] Session ID: ${
        sessionId || "none"
      }, Item Index: ${itemIndex}`
    );

    try {
      // Step 1: Initialize price lookup with detailed logging
      if (sessionId) {
        this.sendSSEEvent(sessionId, "chunk", {
          type: "price_lookup_step",
          phase: "price_lookup",
          step: "initializing",
          itemIndex,
          message: `Initializing price lookup for "${item}"...`,
          timestamp: new Date().toISOString(),
        });
      }

      // Log scraper service availability
      const scraperService = require("../scrapers/ScraperService");
      logger.info(
        `[PRICE_SCRAPING] ScraperService status: initialized=${scraperService.initialized}, scraperCount=${scraperService.scrapers.size}`
      );

      if (!scraperService.initialized) {
        logger.error(
          `[PRICE_SCRAPING] ScraperService not initialized - price lookup will fail`
        );
      }

      // Step 2: Search for the item using scrapers
      if (sessionId) {
        this.sendSSEEvent(sessionId, "chunk", {
          type: "price_lookup_step",
          phase: "price_lookup",
          step: "searching_suppliers",
          itemIndex,
          message: `Searching supplier databases for "${item}"...`,
          timestamp: new Date().toISOString(),
        });
      }

      // Try Home Depot first for electrical materials
      let searchResults = [];
      let primarySource = "Home Depot";

      // Step 3: Search Home Depot
      if (sessionId) {
        this.sendSSEEvent(sessionId, "chunk", {
          type: "price_lookup_step",
          phase: "price_lookup",
          step: "searching_homedepot",
          itemIndex,
          message: `Searching Home Depot for "${item}"...`,
          timestamp: new Date().toISOString(),
        });
      }

      try {
        // Get Home Depot scraper using the correct API
        const homeDepotSourceId = scraperService.sourceTypeMap.get("HOME_DEPOT");
        const homeDepotScraper = homeDepotSourceId ? scraperService.scrapers.get(homeDepotSourceId) : null;
        if (homeDepotScraper) {
          logger.debug(
            `[StreamingAI] Searching Home Depot for: "${itemDescription}"`
          );
          searchResults = await homeDepotScraper.searchByDescription(
            itemDescription,
            { limit: 3 }
          );
          logger.debug(
            `[StreamingAI] Home Depot search returned ${searchResults.length} results`
          );

          if (sessionId) {
            this.sendSSEEvent(sessionId, "chunk", {
              type: "price_lookup_step",
              phase: "price_lookup",
              step: "homedepot_results",
              itemIndex,
              message: `Home Depot search returned ${searchResults.length} results`,
              resultsCount: searchResults.length,
              timestamp: new Date().toISOString(),
            });
          }
        }
      } catch (homeDepotError) {
        logger.warn(
          `[StreamingAI] Home Depot search failed: ${homeDepotError.message}`
        );

        if (sessionId) {
          this.sendSSEEvent(sessionId, "chunk", {
            type: "price_lookup_step",
            phase: "price_lookup",
            step: "homedepot_failed",
            itemIndex,
            message: `Home Depot search failed, trying Platt Electric...`,
            error: homeDepotError.message,
            timestamp: new Date().toISOString(),
          });
        }

        // Try Platt Electric as fallback
        try {
          // Get Platt scraper using the correct API
          const plattSourceId = scraperService.sourceTypeMap.get("PLATT");
          const plattScraper = plattSourceId ? scraperService.scrapers.get(plattSourceId) : null;
          if (plattScraper) {
            if (sessionId) {
              this.sendSSEEvent(sessionId, "chunk", {
                type: "price_lookup_step",
                phase: "price_lookup",
                step: "searching_platt",
                itemIndex,
                message: `Searching Platt Electric for "${item}"...`,
                timestamp: new Date().toISOString(),
              });
            }

            logger.debug(
              `[StreamingAI] Fallback to Platt Electric for: "${itemDescription}"`
            );
            searchResults = await plattScraper.searchByDescription(
              itemDescription,
              { limit: 3 }
            );
            primarySource = "Platt Electric";
            logger.debug(
              `[StreamingAI] Platt Electric search returned ${searchResults.length} results`
            );

            if (sessionId) {
              this.sendSSEEvent(sessionId, "chunk", {
                type: "price_lookup_step",
                phase: "price_lookup",
                step: "platt_results",
                itemIndex,
                message: `Platt Electric search returned ${searchResults.length} results`,
                resultsCount: searchResults.length,
                timestamp: new Date().toISOString(),
              });
            }
          }
        } catch (plattError) {
          logger.warn(
            `[StreamingAI] Platt Electric search also failed: ${plattError.message}`
          );

          if (sessionId) {
            this.sendSSEEvent(sessionId, "chunk", {
              type: "price_lookup_step",
              phase: "price_lookup",
              step: "suppliers_failed",
              itemIndex,
              message: `Both supplier searches failed, trying web search...`,
              timestamp: new Date().toISOString(),
            });
          }
        }
      }

      // Step 2: Process search results
      if (searchResults && searchResults.length > 0) {
        if (sessionId) {
          this.sendSSEEvent(sessionId, "chunk", {
            type: "price_lookup_step",
            phase: "price_lookup",
            step: "found_results",
            itemIndex,
            message: `Found ${searchResults.length} potential matches from ${primarySource}`,
            timestamp: new Date().toISOString(),
          });
        }

        // Get the best match (first result is usually most relevant)
        const bestMatch = searchResults[0];

        // Calculate confidence based on name similarity and price availability
        let confidence = this.calculateConfidenceScore(item, bestMatch);

        if (sessionId) {
          this.sendSSEEvent(sessionId, "chunk", {
            type: "price_lookup_step",
            phase: "price_lookup",
            step: "analyzing_match",
            itemIndex,
            message: `Analyzing best match: "${bestMatch.name}" (${Math.round(
              confidence * 100
            )}% confidence)`,
            timestamp: new Date().toISOString(),
          });
        }

        // Return result based on confidence level
        const result = {
          item,
          category: itemCategory,
          price: bestMatch.price || bestMatch.numerical_price || null,
          currency: "USD",
          confidence,
          source: primarySource,
          productName: bestMatch.name,
          sku: bestMatch.sku,
          productUrl: bestMatch.productUrl,
          timestamp: new Date().toISOString(),
        };

        // Set status based on confidence
        if (confidence >= 0.9) {
          result.status = "exact_match";
        } else if (confidence >= 0.7) {
          result.status = "similar_product";
        } else if (confidence >= 0.5) {
          result.status = "category_estimate";
        } else {
          result.status = "needs_manual_review";
        }

        logger.info(
          `[StreamingAI] Price lookup successful for "${item}": $${
            result.price
          } (${Math.round(confidence * 100)}% confidence)`
        );
        return result;
      } else {
        // No results found - try generic web search with crawl4ai
        if (sessionId) {
          this.sendSSEEvent(sessionId, "chunk", {
            type: "price_lookup_step",
            phase: "price_lookup",
            step: "web_search",
            itemIndex,
            message: `No supplier matches found. Searching web for "${item}"...`,
            timestamp: new Date().toISOString(),
          });
        }

        const webSearchResult = await this.performWebSearchLookup(
          item,
          sessionId,
          itemIndex
        );
        if (webSearchResult) {
          return webSearchResult;
        }

        // Fallback to category-based estimate
        logger.warn(
          `[StreamingAI] No price found for "${item}", using category estimate`
        );
        return {
          item,
          category: itemCategory,
          price: this.getCategoryEstimate(itemCategory),
          currency: "USD",
          confidence: 0.3,
          status: "category_estimate",
          source: "Category Average",
          timestamp: new Date().toISOString(),
        };
      }
    } catch (error) {
      // Enhanced error logging for price scraping diagnostics
      logger.error(
        `[PRICE_SCRAPING] Price lookup failed for "${item}": ${error.message}`
      );
      logger.error(`[PRICE_SCRAPING] Error stack: ${error.stack}`);

      // Check if this is a Crawl4AI specific error
      if (
        error.message.includes("Python") ||
        error.message.includes("crawl4ai")
      ) {
        logger.error(
          `[PRICE_SCRAPING] ❌ CRAWL4AI ERROR DETECTED - This is why price scraping is not working!`
        );
        logger.error(`[PRICE_SCRAPING] Error type: ${error.name}`);
        logger.error(
          `[PRICE_SCRAPING] To fix: Install Python 3.8+ and run 'pip install crawl4ai'`
        );
      }

      // Check if this is a scraper service error
      if (
        error.message.includes("ScraperService") ||
        error.message.includes("not initialized")
      ) {
        logger.error(
          `[PRICE_SCRAPING] ❌ SCRAPER SERVICE ERROR - Service initialization failed`
        );
        logger.error(
          `[PRICE_SCRAPING] Check material sources in database and Crawl4AI setup`
        );
      }

      if (sessionId) {
        this.sendSSEEvent(sessionId, "chunk", {
          type: "price_lookup_step",
          phase: "price_lookup",
          step: "error",
          itemIndex,
          message: `Price lookup failed for "${item}": ${error.message}`,
          error: {
            message: error.message,
            type: error.name,
            isCrawl4AIError:
              error.message.includes("Python") ||
              error.message.includes("crawl4ai"),
            isScraperError:
              error.message.includes("ScraperService") ||
              error.message.includes("not initialized"),
          },
          timestamp: new Date().toISOString(),
        });
      }

      // Return fallback result with detailed error information
      return {
        item,
        category: itemCategory,
        price: null,
        currency: "USD",
        confidence: 0,
        status: "lookup_failed",
        source: "Error",
        error: error.message,
        errorType: error.name,
        isCrawl4AIError:
          error.message.includes("Python") ||
          error.message.includes("crawl4ai"),
        isScraperError:
          error.message.includes("ScraperService") ||
          error.message.includes("not initialized"),
        timestamp: new Date().toISOString(),
      };
    }
  }

  /**
   * Get session status
   */
  getSessionStatus(sessionId) {
    const session = this.streamingSessions.get(sessionId);
    if (!session) {
      return null;
    }

    return {
      sessionId,
      status: session.status,
      progress: session.progress,
      startTime: session.startTime,
      elapsedTime: Date.now() - session.startTime,
    };
  }

  /**
   * Get session info
   */
  getSessionInfo(sessionId) {
    const session = this.streamingSessions.get(sessionId);
    if (!session) {
      return null;
    }

    return {
      sessionId,
      status: session.status,
      progress: session.progress,
      startTime: session.startTime,
      elapsedTime: Date.now() - session.startTime,
      quickEstimate: session.quickEstimate,
      detailedAnalysis: session.detailedAnalysis,
      finalResults: session.finalResults,
    };
  }

  /**
   * Get raw session object with enhanced validation and recovery
   */
  getSession(sessionId, options = {}) {
    if (!sessionId || typeof sessionId !== "string") {
      logger.warn(`[StreamingAI] Invalid sessionId provided: ${sessionId}`);
      return null;
    }

    // Check if session exists in both storage and registry
    const session = this.streamingSessions.get(sessionId);
    const inRegistry = this.sessionRegistry.has(sessionId);

    // Handle inconsistent state
    if (session && !inRegistry) {
      logger.warn(
        `[StreamingAI] Session ${sessionId} exists in storage but not in registry - fixing`
      );
      this.sessionRegistry.set(sessionId, true);
      this.sessionStats.totalSessions = this.sessionRegistry.size;
    } else if (!session && inRegistry) {
      logger.warn(
        `[StreamingAI] Session ${sessionId} exists in registry but not in storage - cleaning up`
      );
      this.sessionRegistry.delete(sessionId);
      this.sessionStats.totalSessions = this.sessionRegistry.size;
      return null;
    }

    if (!session) {
      if (options.includeExpired) {
        logger.debug(
          `[StreamingAI] Session ${sessionId} not found - may have been cleaned up`
        );
      }
      return null;
    }

    // Validate session integrity and check expiration
    const now = Date.now();
    const sessionAge = now - session.startTime;
    const idleTime = now - (session.lastActivity || session.startTime);

    // Check if session is expired but allow grace period for recovery
    const isExpired =
      sessionAge > this.sessionConfig.maxSessionAge ||
      idleTime > this.sessionConfig.maxIdleTime;
    const isProtected = session.protectedUntil && now < session.protectedUntil;

    if (isExpired && !isProtected && !options.allowExpired) {
      logger.warn(`[StreamingAI] Session ${sessionId} is expired`, {
        sessionAge: Math.round(sessionAge / 1000),
        idleTime: Math.round(idleTime / 1000),
        maxAge: Math.round(this.sessionConfig.maxSessionAge / 1000),
        maxIdle: Math.round(this.sessionConfig.maxIdleTime / 1000),
        isProtected,
        status: session.status,
      });

      if (options.autoCleanup !== false) {
        // Schedule cleanup but allow current operation to complete
        setTimeout(() => this.cleanupSession(sessionId), 100);
        return null;
      }
    }

    // Update last access time for active sessions
    if (!isExpired || options.allowExpired) {
      session.lastActivity = now;

      // Extend protection if session is actively being used
      if (session.sseResponse && !session.protectedUntil) {
        session.protectedUntil = now + 2 * 60 * 1000; // 2 minute protection
      }
    }

    return session;
  }

  /**
   * Attempt to recover or provide diagnostics for a missing session
   */
  recoverSession(sessionId, fallbackData = null) {
    logger.info(`[StreamingAI] Attempting session recovery for ${sessionId}`);

    // First check if session exists with extended options
    let session = this.getSession(sessionId, {
      allowExpired: true,
      autoCleanup: false,
      includeExpired: true,
    });

    if (session) {
      logger.info(
        `[StreamingAI] Session ${sessionId} found during recovery - may have been temporarily unavailable`
      );
      return {
        found: true,
        session: session,
        status: "recovered",
        details: {
          sessionAge: Date.now() - session.startTime,
          idleTime: Date.now() - (session.lastActivity || session.startTime),
          hasSSE: !!session.sseResponse,
          status: session.status,
        },
      };
    }

    // Check registry state for diagnostics
    const inRegistry = this.sessionRegistry.has(sessionId);
    const registrySize = this.sessionRegistry.size;
    const sessionMapSize = this.streamingSessions.size;

    logger.warn(`[StreamingAI] Session ${sessionId} recovery failed - session not found`, {
      inRegistry,
      registrySize,
      sessionMapSize,
      mismatch: registrySize !== sessionMapSize,
      recentCleanup: Date.now() - this.sessionStats.lastCleanupTime < 60000,
      possibleCause: registrySize !== sessionMapSize ? 'registry_mismatch' : 'session_expired'
    });

    // Attempt to create a placeholder session if fallback data is provided
    if (fallbackData && fallbackData.createFallback) {
      try {
        logger.info(`[StreamingAI] Creating fallback session for ${sessionId}`);
        const newSessionId = this.createSession(
          null,
          fallbackData.formData || new Map()
        );

        return {
          found: false,
          session: this.streamingSessions.get(newSessionId),
          status: "fallback_created",
          newSessionId,
          details: {
            originalSessionId: sessionId,
            fallbackReason: "original_session_not_found",
          },
        };
      } catch (fallbackError) {
        logger.error(
          `[StreamingAI] Failed to create fallback session:`,
          fallbackError
        );
      }
    }

    return {
      found: false,
      session: null,
      status: "not_recoverable",
      details: {
        sessionId,
        inRegistry,
        activeSessions: registrySize,
        possibleCauses: [
          registrySize !== sessionMapSize ? "registry_mismatch" : null,
          Date.now() - this.sessionStats.lastCleanupTime < 60000
            ? "recent_cleanup"
            : null,
          registrySize >= this.sessionConfig.maxConcurrentSessions * 0.9
            ? "high_capacity"
            : null,
        ].filter(Boolean),
        suggestion: "Start a new session",
      },
    };
  }

  /**
   * Clean up session with enhanced error handling and race condition prevention
   */
  cleanupSession(sessionId) {
    if (!sessionId || typeof sessionId !== "string") {
      logger.warn(`[StreamingAI] Invalid sessionId for cleanup: ${sessionId}`);
      return;
    }

    const session = this.streamingSessions.get(sessionId);
    if (!session) {
      // Clean up registry entry if it exists
      this.sessionRegistry.delete(sessionId);
      logger.debug(
        `[StreamingAI] Session ${sessionId} already cleaned up or never existed`
      );
      return; // Already cleaned up
    }

    // Prevent multiple cleanup attempts
    if (session.cleanupScheduled) {
      logger.debug(
        `[StreamingAI] Session ${sessionId} cleanup already scheduled`
      );
      return;
    }
    session.cleanupScheduled = true;

    logger.info(`[StreamingAI] Cleaning up session ${sessionId}`);

    try {
      // Remove event listeners if they exist
      if (session.sseResponse && session._eventHandlers) {
        session.sseResponse.removeListener(
          "close",
          session._eventHandlers.close
        );
        session.sseResponse.removeListener(
          "error",
          session._eventHandlers.error
        );
        delete session._eventHandlers;
      }

      // Close SSE connection gracefully
      if (session.sseResponse) {
        try {
          const res = session.sseResponse;
          if (!res.destroyed && !res.finished && !res.writableEnded) {
            // Send final close event
            res.write(`event: close\n`);
            res.write(
              `data: ${JSON.stringify({
                type: "session_cleanup",
                sessionId,
                timestamp: new Date().toISOString(),
              })}\n\n`
            );
            res.end();
          }
        } catch (error) {
          logger.error(
            `[StreamingAI] Error closing SSE for session ${sessionId}:`,
            error
          );
        }
      }

      // Calculate session duration for metrics
      const duration = Date.now() - session.startTime;

      // Remove from both session storage and registry
      this.streamingSessions.delete(sessionId);
      this.sessionRegistry.delete(sessionId);

      // Update session stats
      this.sessionStats.sessionsCleaned++;

      logger.info(
        `[StreamingAI] Session ${sessionId} cleaned up after ${duration}ms (${this.sessionRegistry.size} active sessions)`
      );
    } catch (error) {
      logger.error(
        `[StreamingAI] Error during session cleanup for ${sessionId}:`,
        error
      );
      // Force removal even if cleanup failed
      this.streamingSessions.delete(sessionId);
      this.sessionRegistry.delete(sessionId);
    }
  }

  /**
   * Perform immediate session cleanup to free up resources
   */
  performSessionCleanup() {
    const now = Date.now();
    const sessionsToCleanup = [];

    logger.debug(
      `[StreamingAI] Performing immediate session cleanup (${this.sessionRegistry.size} active sessions)`
    );

    // Check all active sessions for expiration
    for (const [sessionId] of this.sessionRegistry.entries()) {
      const session = this.streamingSessions.get(sessionId);
      if (!session) {
        // Registry entry without session data - clean up
        sessionsToCleanup.push(sessionId);
        continue;
      }

      const age = now - session.startTime;
      const idleTime = now - (session.lastActivity || session.startTime);

      // Check if session is still protected from cleanup
      const isProtected =
        session.protectedUntil && now < session.protectedUntil;

      // Mark expired sessions for cleanup (but respect protection period)
      if (
        !isProtected &&
        (age > this.sessionConfig.maxSessionAge ||
          idleTime > this.sessionConfig.maxIdleTime)
      ) {
        sessionsToCleanup.push(sessionId);
      }
    }

    // Clean up expired sessions
    for (const sessionId of sessionsToCleanup) {
      try {
        this.cleanupSession(sessionId);
        this.sessionStats.sessionsExpired++;
      } catch (error) {
        logger.error(
          `[StreamingAI] Error cleaning up expired session ${sessionId}:`,
          error
        );
      }
    }

    logger.debug(
      `[StreamingAI] Cleanup completed: removed ${sessionsToCleanup.length} expired sessions`
    );
    return sessionsToCleanup.length;
  }

  /**
   * Clean up oldest sessions when limit is reached
   */
  cleanupOldestSessions(count) {
    const now = Date.now();
    const sessions = Array.from(this.sessionRegistry.entries())
      .map(([sessionId]) => {
        const session = this.streamingSessions.get(sessionId);
        return session ? [sessionId, session] : null;
      })
      .filter((entry) => entry !== null)
      // Filter out protected sessions
      .filter(([, session]) => {
        const isProtected =
          session.protectedUntil && now < session.protectedUntil;
        return !isProtected;
      })
      .sort(([, a], [, b]) => a.startTime - b.startTime); // Sort by start time (oldest first)

    const sessionsToRemove = sessions.slice(
      0,
      Math.min(count, sessions.length)
    );

    logger.warn(
      `[StreamingAI] Force cleaning ${sessionsToRemove.length} oldest unprotected sessions due to capacity limits`
    );

    for (const [sessionId] of sessionsToRemove) {
      try {
        this.cleanupSession(sessionId);
      } catch (error) {
        logger.error(
          `[StreamingAI] Error force-cleaning session ${sessionId}:`,
          error
        );
      }
    }

    return sessionsToRemove.length;
  }

  /**
   * Get session statistics for monitoring
   */
  getSessionStats() {
    return {
      ...this.sessionStats,
      activeSessions: this.sessionRegistry.size,
      currentTime: Date.now(),
      uptime: Date.now() - this.sessionStats.lastCleanupTime,
    };
  }

  /**
   * Periodic cleanup of stale sessions with enhanced logic
   */
  startPeriodicCleanup() {
    this.cleanupTimer = setInterval(() => {
      const now = Date.now();
      const sessionsToCleanup = [];

      // Iterate through active sessions using the sessionRegistry
      // Cross-reference with session storage to ensure consistency
      const activeSessions = [];
      for (const [sessionId] of this.sessionRegistry.entries()) {
        const session = this.streamingSessions.get(sessionId);
        if (session) {
          activeSessions.push([sessionId, session]);
        } else {
          // Clean up stale registry entries
          this.sessionRegistry.delete(sessionId);
        }
      }

      for (const [sessionId, session] of activeSessions) {
        const age = now - session.startTime;
        const idleTime = now - session.lastActivity;

        // Check for various cleanup conditions, but respect protection flags
        const isProtected =
          session.protectedUntil && now < session.protectedUntil;

        if (isProtected) {
          // Don't cleanup sessions that are still protected
          logger.debug(
            `[StreamingAI] Session ${sessionId} is protected from cleanup (age: ${age}ms, protected until: ${new Date(
              session.protectedUntil
            ).toISOString()})`
          );
          continue;
        }

        if (age > this.sessionConfig.maxSessionAge) {
          logger.info(
            `[StreamingAI] Session ${sessionId} exceeded max age (${age}ms)`
          );
          sessionsToCleanup.push(sessionId);
        } else if (idleTime > this.sessionConfig.maxIdleTime) {
          logger.info(
            `[StreamingAI] Session ${sessionId} exceeded idle timeout (${idleTime}ms)`
          );
          sessionsToCleanup.push(sessionId);
        } else if (session.status === "completed" && age > 60000) {
          // 1 minute after completion
          logger.info(
            `[StreamingAI] Cleaning up completed session ${sessionId}`
          );
          sessionsToCleanup.push(sessionId);
        } else if (!session.sseResponse && age > 300000 && !isProtected) {
          // 5 minutes without SSE connection, but not if protected
          logger.info(
            `[StreamingAI] Cleaning up orphaned session ${sessionId}`
          );
          sessionsToCleanup.push(sessionId);
        }
      }

      // Clean up identified sessions
      for (const sessionId of sessionsToCleanup) {
        this.cleanupSession(sessionId);
      }

      // Log session statistics
      if (activeSessions.length > 0) {
        logger.debug(`[StreamingAI] Active sessions: ${activeSessions.length}`);

        // Check session count monitoring
        if (
          activeSessions.length >= this.sessionConfig.sessionWarningThreshold
        ) {
          logger.warn(
            `[StreamingAI] High session count warning: ${activeSessions.length} active sessions`
          );
        }

        // Update session stats
        this.sessionStats.totalSessions = activeSessions.length;
        this.sessionStats.peakSessions = Math.max(
          this.sessionStats.peakSessions,
          activeSessions.length
        );
        this.sessionStats.lastCleanupTime = now;
      }
    }, this.sessionConfig.cleanupInterval);

    // Store timer reference for cleanup
    this.activeTimers.add(this.cleanupTimer);
  }

  /**
   * Calculate confidence score based on name similarity and product data quality
   */
  calculateConfidenceScore(searchTerm, productMatch) {
    if (!productMatch || !productMatch.name) {
      return 0.1;
    }

    const searchLower = searchTerm.toLowerCase().trim();
    const productLower = productMatch.name.toLowerCase().trim();

    // Exact match
    if (searchLower === productLower) {
      return productMatch.price ? 0.95 : 0.85;
    }

    // Calculate similarity score
    let similarity = 0;

    // Check for word matches
    const searchWords = searchLower.split(/\s+/);
    const productWords = productLower.split(/\s+/);

    let matchingWords = 0;
    for (const searchWord of searchWords) {
      if (searchWord.length > 2) {
        // Ignore very short words
        for (const productWord of productWords) {
          if (
            productWord.includes(searchWord) ||
            searchWord.includes(productWord)
          ) {
            matchingWords++;
            break;
          }
        }
      }
    }

    similarity =
      matchingWords / Math.max(searchWords.length, productWords.length);

    // Boost confidence if price is available
    if (productMatch.price || productMatch.numerical_price) {
      similarity += 0.1;
    }

    // Boost confidence if SKU is available
    if (productMatch.sku) {
      similarity += 0.05;
    }

    // Cap at 0.95 for non-exact matches
    return Math.min(similarity, 0.95);
  }

  /**
   * Perform web search lookup using crawl4ai when supplier searches fail
   */
  async performWebSearchLookup(item, sessionId, itemIndex) {
    try {
      // Use a generic search query for the item
      const searchQuery = `${item} price electrical supply`;
      const searchUrl = `https://www.google.com/search?q=${encodeURIComponent(
        searchQuery
      )}`;

      if (sessionId) {
        this.sendSSEEvent(sessionId, "chunk", {
          type: "price_lookup_step",
          phase: "price_lookup",
          step: "preparing_web_search",
          itemIndex,
          message: `Preparing web search for "${item}"...`,
          searchQuery,
          timestamp: new Date().toISOString(),
        });
      }

      if (sessionId) {
        this.sendSSEEvent(sessionId, "chunk", {
          type: "price_lookup_step",
          phase: "price_lookup",
          step: "crawling_web",
          itemIndex,
          message: `Crawling web results with Crawl4AI...`,
          timestamp: new Date().toISOString(),
        });
      }

      // Use crawl4ai to get web content
      const crawlResult = await Crawl4AIService.crawl(searchUrl, {
        word_count_threshold: 10,
        extraction_strategy: "NoExtractionStrategy",
        chunking_strategy: "RegexChunking",
        bypass_cache: true,
      });

      if (sessionId) {
        this.sendSSEEvent(sessionId, "chunk", {
          type: "price_lookup_step",
          phase: "price_lookup",
          step: "crawl_complete",
          itemIndex,
          message: `Web crawling complete, parsing content...`,
          contentLength: crawlResult?.markdown?.length || 0,
          timestamp: new Date().toISOString(),
        });
      }

      if (crawlResult && crawlResult.markdown) {
        if (sessionId) {
          this.sendSSEEvent(sessionId, "chunk", {
            type: "price_lookup_step",
            phase: "price_lookup",
            step: "parsing_content",
            itemIndex,
            message: `Parsing ${Math.round(
              crawlResult.markdown.length / 1000
            )}KB of content for products...`,
            timestamp: new Date().toISOString(),
          });
        }

        // Parse the markdown content for product information
        const products =
          await priceLookupService.parseGenericMarkdownForProducts(
            crawlResult.markdown,
            searchUrl
          );

        if (sessionId) {
          this.sendSSEEvent(sessionId, "chunk", {
            type: "price_lookup_step",
            phase: "price_lookup",
            step: "parsing_complete",
            itemIndex,
            message: `Found ${
              products?.length || 0
            } potential products in web content`,
            productsFound: products?.length || 0,
            timestamp: new Date().toISOString(),
          });
        }

        if (products && products.length > 0) {
          const bestMatch = products[0];
          const confidence = this.calculateConfidenceScore(item, bestMatch);

          if (sessionId) {
            this.sendSSEEvent(sessionId, "chunk", {
              type: "price_lookup_step",
              phase: "price_lookup",
              step: "web_match_found",
              itemIndex,
              message: `Best web match: "${bestMatch.name}" (${Math.round(
                confidence * 100
              )}% confidence)`,
              confidence: Math.round(confidence * 100),
              timestamp: new Date().toISOString(),
            });
          }

          return {
            item,
            category: "general",
            price: bestMatch.price || bestMatch.numerical_price || null,
            currency: "USD",
            confidence,
            status: confidence >= 0.7 ? "similar_product" : "category_estimate",
            source: "Web Search",
            productName: bestMatch.name,
            timestamp: new Date().toISOString(),
          };
        } else {
          if (sessionId) {
            this.sendSSEEvent(sessionId, "chunk", {
              type: "price_lookup_step",
              phase: "price_lookup",
              step: "no_web_matches",
              itemIndex,
              message: `No suitable products found in web content`,
              timestamp: new Date().toISOString(),
            });
          }
        }
      } else {
        if (sessionId) {
          this.sendSSEEvent(sessionId, "chunk", {
            type: "price_lookup_step",
            phase: "price_lookup",
            step: "crawl_failed",
            itemIndex,
            message: `Web crawling returned no content`,
            timestamp: new Date().toISOString(),
          });
        }
      }

      return null;
    } catch (error) {
      logger.warn(
        `[StreamingAI] Web search lookup failed for "${item}": ${error.message}`
      );

      if (sessionId) {
        this.sendSSEEvent(sessionId, "chunk", {
          type: "price_lookup_step",
          phase: "price_lookup",
          step: "web_search_error",
          itemIndex,
          message: `Web search failed: ${error.message}`,
          error: error.message,
          timestamp: new Date().toISOString(),
        });
      }

      return null;
    }
  }

  /**
   * Get category-based price estimate as fallback
   */
  getCategoryEstimate(category) {
    const categoryPrices = {
      electrical: 45,
      wiring: 25,
      conduit: 35,
      outlet: 15,
      switch: 12,
      breaker: 85,
      panel: 250,
      fixture: 75,
      cable: 30,
      connector: 8,
      general: 50,
    };

    const categoryLower = category.toLowerCase();

    // Find matching category or use general
    for (const [cat, price] of Object.entries(categoryPrices)) {
      if (categoryLower.includes(cat)) {
        return price;
      }
    }

    return categoryPrices.general;
  }

  /**
   * Handle connection close events
   */
  handleConnectionClose(sessionId) {
    const session = this.streamingSessions.get(sessionId);
    if (session) {
      // Remove event listeners if they exist
      if (session.sseResponse && session._eventHandlers) {
        session.sseResponse.removeListener(
          "close",
          session._eventHandlers.close
        );
        session.sseResponse.removeListener(
          "error",
          session._eventHandlers.error
        );
        delete session._eventHandlers;
      }

      session.sseResponse = null;
      session.lastActivity = Date.now();

      // Schedule cleanup after a longer delay to allow for reconnection
      // Increased from 30 seconds to 60 seconds to prevent premature cleanup
      const cleanupTimer = setTimeout(() => {
        const currentSession = this.streamingSessions.get(sessionId);
        if (currentSession && !currentSession.sseResponse) {
          logger.info(
            `[StreamingAI] Cleaning up disconnected session ${sessionId}`
          );
          this.cleanupSession(sessionId);
        }
      }, 60000); // 60 seconds grace period (increased from 30 seconds)

      // Store timer reference for cleanup
      this.activeTimers.add(cleanupTimer);
    }
  }

  /**
   * Handle connection error events
   */
  handleConnectionError(sessionId, error) {
    logger.error(
      `[StreamingAI] Connection error for session ${sessionId}:`,
      error
    );
    const session = this.streamingSessions.get(sessionId);
    if (session) {
      // Remove event listeners if they exist
      if (session.sseResponse && session._eventHandlers) {
        session.sseResponse.removeListener(
          "close",
          session._eventHandlers.close
        );
        session.sseResponse.removeListener(
          "error",
          session._eventHandlers.error
        );
        delete session._eventHandlers;
      }

      session.sseResponse = null;
      session.lastActivity = Date.now();

      // Immediate cleanup on connection errors
      const cleanupTimer = setTimeout(
        () => this.cleanupSession(sessionId),
        1000
      );

      // Store timer reference for cleanup
      this.activeTimers.add(cleanupTimer);
    }
  }

  /**
   * Clean up oldest sessions when limit is reached
   */
  cleanupOldestSessions(count) {
    const sessions = Array.from(this.sessionRegistry.entries())
      .map(([sessionId]) => {
        const session = this.streamingSessions.get(sessionId);
        return session ? [sessionId, session] : null;
      })
      .filter(Boolean)
      .sort(([, a], [, b]) => a.startTime - b.startTime)
      .slice(0, count);

    for (const [sessionId] of sessions) {
      logger.info(`[StreamingAI] Cleaning up oldest session ${sessionId}`);
      this.cleanupSession(sessionId);
    }
  }

  /**
   * Setup graceful shutdown handling
   */
  setupGracefulShutdown() {
    const gracefulShutdown = () => {
      logger.info("[StreamingAI] Graceful shutdown initiated");

      // Clear all active timers
      for (const timer of this.activeTimers) {
        if (typeof timer === "number") {
          clearTimeout(timer);
          clearInterval(timer);
        }
      }
      this.activeTimers.clear();

      // Clear main cleanup timer
      if (this.cleanupTimer) {
        clearInterval(this.cleanupTimer);
      }

      // Clean up all active sessions
      const sessionIds = Array.from(this.sessionRegistry.keys());
      for (const sessionId of sessionIds) {
        this.cleanupSession(sessionId);
      }

      logger.info("[StreamingAI] All sessions cleaned up");
    };

    process.on("SIGTERM", gracefulShutdown);
    process.on("SIGINT", gracefulShutdown);
    process.on("uncaughtException", (error) => {
      logger.error("[StreamingAI] Uncaught exception:", error);
      gracefulShutdown();
    });
  }

  /**
   * Get enhanced session statistics for monitoring
   */
  getSessionStats() {
    const now = Date.now();
    const stats = {
      totalSessions: this.sessionRegistry.size,
      activeSessions: this.sessionRegistry.size,
      sessionsByStatus: {},
      sessionsByAge: {
        under1min: 0,
        under5min: 0,
        under10min: 0,
        over10min: 0,
      },
      averageAge: 0,
      oldestSession: 0,
      newestSession: 0,
      sessionsWithSSE: 0,
      healthyConnections: 0,
      protectedSessions: 0,
      ...this.sessionStats,
      lastUpdated: now,
    };

    let totalAge = 0;
    let maxAge = 0;
    let minAge = Infinity;
    let sessionCount = 0;

    // Iterate through registry and get actual sessions
    for (const [sessionId] of this.sessionRegistry.entries()) {
      const session = this.streamingSessions.get(sessionId);
      if (session) {
        const age = now - session.startTime;
        const idleTime = now - (session.lastActivity || session.startTime);

        totalAge += age;
        maxAge = Math.max(maxAge, age);
        minAge = Math.min(minAge, age);
        sessionCount++;

        // Count by status
        stats.sessionsByStatus[session.status] =
          (stats.sessionsByStatus[session.status] || 0) + 1;

        // Count by age
        if (age < 60000) stats.sessionsByAge.under1min++;
        else if (age < 5 * 60000) stats.sessionsByAge.under5min++;
        else if (age < 10 * 60000) stats.sessionsByAge.under10min++;
        else stats.sessionsByAge.over10min++;

        // Count connections
        if (session.sseResponse) stats.sessionsWithSSE++;
        if (session.sseResponse && !session.sseResponse.destroyed)
          stats.healthyConnections++;
        if (session.protectedUntil && now < session.protectedUntil)
          stats.protectedSessions++;
      }
    }

    if (sessionCount > 0) {
      stats.averageAge = Math.round(totalAge / sessionCount);
      stats.oldestSession = maxAge;
      stats.newestSession = minAge === Infinity ? 0 : minAge;
    }

    return stats;
  }

  /**
   * Get session health metrics for monitoring
   */
  getSessionHealthMetrics() {
    const stats = this.getSessionStats();
    const now = Date.now();

    // Calculate health score (0-100)
    let healthScore = 100;

    // Deduct points for high capacity
    const capacityRatio =
      stats.activeSessions / this.sessionConfig.maxConcurrentSessions;
    if (capacityRatio > 0.8) healthScore -= (capacityRatio - 0.8) * 100;

    // Deduct points for old sessions
    if (stats.sessionsByAge.over10min > 0) {
      healthScore -=
        (stats.sessionsByAge.over10min / stats.activeSessions) * 20;
    }

    // Deduct points for recent errors
    const recentCleanupTime = now - this.sessionStats.lastCleanupTime;
    if (recentCleanupTime < 60000 && this.sessionStats.sessionsExpired > 0) {
      healthScore -= 10;
    }

    // Bonus points for healthy connections
    const connectionHealthRatio =
      stats.healthyConnections / Math.max(stats.sessionsWithSSE, 1);
    if (connectionHealthRatio > 0.9) healthScore += 5;

    return {
      healthScore: Math.max(0, Math.round(healthScore)),
      status:
        healthScore >= 80
          ? "healthy"
          : healthScore >= 60
          ? "degraded"
          : "unhealthy",
      metrics: {
        capacityUtilization: Math.round(capacityRatio * 100),
        averageSessionAge: Math.round(stats.averageAge / 1000),
        connectionSuccess: Math.round(connectionHealthRatio * 100),
        recentExpiredSessions: this.sessionStats.sessionsExpired,
        timeSinceLastCleanup: Math.round(recentCleanupTime / 1000),
      },
      recommendations: this.generateHealthRecommendations(stats, healthScore),
    };
  }

  /**
   * Generate health recommendations based on metrics
   */
  generateHealthRecommendations(stats, healthScore) {
    const recommendations = [];

    if (
      stats.activeSessions >=
      this.sessionConfig.maxConcurrentSessions * 0.9
    ) {
      recommendations.push({
        priority: "high",
        message: "Session capacity near limit - consider scaling or cleanup",
        action: "monitor_capacity",
      });
    }

    if (stats.sessionsByAge.over10min > 0) {
      recommendations.push({
        priority: "medium",
        message: `${stats.sessionsByAge.over10min} sessions are over 10 minutes old`,
        action: "review_session_timeouts",
      });
    }

    if (stats.healthyConnections < stats.sessionsWithSSE * 0.8) {
      recommendations.push({
        priority: "high",
        message: "Many SSE connections appear unhealthy",
        action: "check_network_connectivity",
      });
    }

    if (healthScore < 60) {
      recommendations.push({
        priority: "critical",
        message: "Service health is poor - immediate attention required",
        action: "investigate_service_health",
      });
    }

    return recommendations;
  }

  /**
   * 🆕 PHASE 4: Build clarification detection prompt
   *
   * Creates a specialized prompt to determine if additional user input is needed
   * based on the initial quick estimate and original form data.
   */
  buildClarificationDetectionPrompt(formData, quickEstimate) {
    const getFormValue = (key, defaultValue = "") => {
      if (formData && typeof formData.get === "function") {
        return formData.get(key) || defaultValue;
      }
      return formData?.[key] || defaultValue;
    };

    const inputType = getFormValue("inputType", "overview");
    const inputData = getFormValue("inputData", "");
    const files = getFormValue("files");
    const hasImages = files && files.length > 0;

    let imagePromptAddition = "";
    if (hasImages) {
      imagePromptAddition = `\n\nIMAGES PROVIDED: ${files.length} image(s) uploaded showing electrical equipment and conditions. These images should be analyzed for additional context that may require clarification. Consider if the images reveal:
- Equipment details that need specification confirmation
- Installation conditions that affect scope
- Safety concerns requiring special attention
- Code compliance issues needing clarification`;
    }

    const prompt = `You are an expert electrical estimator reviewing a project for completeness.

**Your Task:** Determine if the provided information is sufficient to generate a detailed, accurate electrical quote, or if clarification questions are needed.

**Original Input:**
- Type: ${inputType}
- Description: ${inputData}${imagePromptAddition}

**Initial Analysis Results:**
${JSON.stringify(quickEstimate, null, 2)}

**Guidelines for Clarification:**
Ask clarification questions ONLY if:
1. Safety/code requirements are unclear (voltage, amperage, installation method)
2. Location details are vague (indoor/outdoor, accessibility, existing infrastructure)
3. Scope boundaries are ambiguous (what's included vs excluded)
4. Technical specifications are missing (wire gauge, conduit type, panel capacity)
5. Timeline or permit requirements are unclear
6. Images reveal equipment or conditions that need specific clarification

DO NOT ask clarification for:
- General cost estimates or budget ranges
- Standard electrical practices that can be assumed
- Minor details that don't affect quote accuracy
- Information that can be determined during site visit

**Response Format (JSON only):**
{
  "needsClarification": boolean,
  "reason": "Brief explanation of why clarification is needed",
  "questions": [
    "Specific question 1?",
    "Specific question 2?"
  ]
}

**Examples:**
- Input: "Install outlet" → needsClarification: true (where? voltage? GFCI needed?)
- Input: "Replace 200A main panel in basement" → needsClarification: false (sufficient detail)
- Input: "Some electrical work" → needsClarification: true (completely vague)

Provide ONLY the JSON response, no other text.`;

    return prompt;
  }

  /**
   * 🆕 PHASE 4: Parse clarification response from AI
   *
   * Extracts clarification decision and questions from AI response
   */
  parseClarificationResponse(response) {
    try {
      // Clean the response and extract JSON
      let cleanResponse = response.trim();

      // Remove markdown code blocks if present
      if (cleanResponse.includes("```")) {
        const jsonMatch = cleanResponse.match(/```(?:json)?\s*([\s\S]*?)```/);
        if (jsonMatch) {
          cleanResponse = jsonMatch[1].trim();
        }
      }

      // Find JSON object
      const jsonMatch = cleanResponse.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        const parsed = JSON.parse(jsonMatch[0]);

        // Validate required fields
        const result = {
          needsClarification: Boolean(parsed.needsClarification),
          reason: parsed.reason || "Additional information needed",
          questions: Array.isArray(parsed.questions) ? parsed.questions : [],
        };

        // Filter out empty questions
        result.questions = result.questions
          .filter((q) => typeof q === "string" && q.trim().length > 0)
          .map((q) => q.trim());

        // If needsClarification is true but no questions, create a generic one
        if (result.needsClarification && result.questions.length === 0) {
          result.questions = [
            "Could you provide more specific details about your electrical project requirements?",
          ];
        }

        logger.debug(`[StreamingAI] Parsed clarification response:`, {
          needsClarification: result.needsClarification,
          questionsCount: result.questions.length,
          reason: result.reason,
        });

        return result;
      }
    } catch (error) {
      logger.warn(
        `[StreamingAI] Failed to parse clarification response:`,
        error
      );
      logger.debug(`[StreamingAI] Raw response:`, response.substring(0, 500));
    }

    // Fallback: assume no clarification needed if parsing fails
    return {
      needsClarification: false,
      reason:
        "Clarification parsing failed, proceeding with available information",
      questions: [],
    };
  }

  /**
   * 🆕 PHASE 4: Process clarification answers and resume streaming
   *
   * This method handles user answers to clarification questions and resumes
   * the streaming workflow from the appropriate phase.
   */
  async processClarificationAnswers(sessionId, answers) {
    logger.info(
      `[StreamingAI] Processing clarification answers for session ${sessionId}`
    );

    const session = this.streamingSessions.get(sessionId);
    if (!session) {
      throw new Error(`Session ${sessionId} not found`);
    }

    if (!session.awaitingClarification) {
      throw new Error(`Session ${sessionId} is not awaiting clarification`);
    }

    try {
      // Store the answers in session
      session.clarificationAnswers = answers;
      session.awaitingClarification = false;
      session.status = "processing_clarification";
      session.lastActivity = Date.now();

      // Send processing event
      this.sendSSEEvent(sessionId, "chunk", {
        type: "clarification_processing",
        phase: "clarification_processing",
        progress: 40,
        message: "Processing your answers and enhancing analysis...",
        answersReceived: Object.keys(answers).length,
        sessionId: sessionId,
        timestamp: new Date().toISOString(),
      });

      // Update form data with clarification context while preserving original data including images
      const enhancedFormData = new Map(session.formData);
      enhancedFormData.set("clarificationAnswers", JSON.stringify(answers));
      enhancedFormData.set(
        "originalQuestions",
        JSON.stringify(session.clarificationQuestions)
      );
      enhancedFormData.set(
        "clarificationContext",
        "User provided additional details for accurate quote generation"
      );

      // Log information about preserved image data
      const files = enhancedFormData.get("files");
      if (files && files.length > 0) {
        logger.info(
          `[StreamingAI] Preserving ${files.length} images for clarification processing`
        );
      }

      session.formData = enhancedFormData;

      // Send clarification complete event
      this.sendSSEEvent(sessionId, "chunk", {
        type: "clarification_complete",
        phase: "clarification_processing",
        progress: 45,
        message: "Clarification complete! Continuing with detailed analysis...",
        sessionId: sessionId,
        timestamp: new Date().toISOString(),
      });

      // Resume streaming from where it left off (after Phase 1)
      if (session.pausedAtPhase === "after_phase_1") {
        logger.info(
          `[StreamingAI] Resuming session ${sessionId} at Phase 2 with clarification context`
        );

        // Continue with Phase 2: Detailed analysis (with clarification context and preserved images)
        await this.streamPhase2DetailedAnalysis(sessionId, enhancedFormData);

        // Continue with Phase 3: Price lookup
        await this.streamPhase3PriceLookup(sessionId, enhancedFormData);

        // Complete the session
        session.status = "completed";
        session.endTime = Date.now();
        session.totalTime = session.endTime - session.startTime;

        logger.info(
          `[StreamingAI] Session ${sessionId} completed with clarification in ${session.totalTime}ms`
        );

        // Send completion event
        this.sendSSEEvent(sessionId, "chunk", {
          type: "completed",
          sessionId,
          totalTime: session.totalTime,
          status: "completed",
          clarificationUsed: true,
          data: session.finalResults,
          timestamp: new Date().toISOString(),
        });

        // Signal client to close connection gracefully
        this.sendSSEEvent(sessionId, "close", {
          message: "Stream finished with clarification. Closing connection.",
          clarificationCompleted: true,
        });

        // Schedule cleanup
        if (session.sseResponse) {
          setTimeout(() => {
            if (session.sseResponse) {
              session.sseResponse.end();
            }
            this.cleanupSession(sessionId);
          }, 10000);
        }
      } else {
        logger.warn(
          `[StreamingAI] Unknown pause phase for session ${sessionId}: ${session.pausedAtPhase}`
        );
        throw new Error(`Unknown pause phase: ${session.pausedAtPhase}`);
      }
    } catch (error) {
      logger.error(
        `[StreamingAI] Error processing clarification answers for session ${sessionId}:`,
        error
      );

      // Send error event
      this.sendSSEEvent(sessionId, "chunk", {
        type: "error",
        phase: "clarification_processing",
        error: `Failed to process clarification: ${error.message}`,
        sessionId: sessionId,
        timestamp: new Date().toISOString(),
      });

      // Clean up the session
      this.cleanupSession(sessionId);
      throw error;
    }
  }
}

const streamingAiServiceInstance = new StreamingAiService();
module.exports = streamingAiServiceInstance;
