{"editor.tabSize": 2, "editor.insertSpaces": true, "editor.detectIndentation": false, "files.eol": "\r\n", "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit"}, "eslint.workingDirectories": ["backend", "frontend"], "javascript.preferences.quoteStyle": "single", "typescript.preferences.quoteStyle": "single", "emmet.includeLanguages": {"javascript": "javascriptreact"}, "files.associations": {"*.js": "javascript"}, "search.exclude": {"**/node_modules": true, "**/build": true, "**/dist": true, "**/.git": true, "**/logs": true, "**/uploads": true}, "files.exclude": {"**/node_modules": true, "**/.git": true, "**/logs": true}, "terminal.integrated.defaultProfile.windows": "PowerShell", "git.enableCommitSigning": true, "git.requireGitSuffix": false}