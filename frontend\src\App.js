import React, { useEffect } from "react";
import { Routes, Route, Navigate } from "react-router-dom";
import { ThemeProvider, createTheme } from "@mui/material/styles";
import CssBaseline from "@mui/material/CssBaseline";
import { useSelector, useDispatch } from "react-redux";
import { setUserFromLocalStorage } from "./slices/authSlice";
import { SnackbarProvider } from "notistack";

// Layout components
import Layout from "./components/layout/Layout";
import ProtectedRoute from "./components/common/ProtectedRoute";
import AdminRoute from "./components/common/AdminRoute";
import ErrorBoundary from "./components/ErrorBoundary";
import SnackbarManager from "./components/SnackbarManager";
import { GoogleMapsProvider } from "./components/GoogleMapsProvider";

// Pages
import Dashboard from "./pages/Dashboard";
import Login from "./pages/Login";
import Register from "./pages/Register";
import Customers from "./pages/Customers";
import CustomerDetail from "./pages/CustomerDetail";
import CreateCustomer from "./pages/CreateCustomer";
import Jobs from "./pages/Jobs";
import JobDetail from "./pages/JobDetail";
import CreateJob from "./pages/CreateJob";
import Invoices from "./pages/Invoices";
import InvoiceDetail from "./pages/InvoiceDetail";
import CreateInvoice from "./pages/CreateInvoice";
import Calendar from "./pages/Calendar";
import Materials from "./pages/Materials";
import MaterialsDetail from "./pages/MaterialDetail";
import CreateMaterial from "./pages/CreateMaterial"; // Assuming this component exists
// import CreateMaterialQuote from './pages/CreateMaterialQuote'; // Removed unused import after rename
import Quotes from "./pages/Quotes";
// import CreateQuote from './pages/CreateQuote'; // Temporarily commented out
import CreateQuoteFormik from "./pages/CreateQuoteFormik"; // Updated path to renamed file
import QuoteDetail from "./pages/QuoteDetail";
import QuotePreview from "./pages/QuotePreview";
import Technicians from "./pages/Technicians";
import TechnicianDetail from "./pages/TechnicianDetail";
import Profile from "./pages/Profile";
import CompanyConfig from "./pages/CompanyConfig";
import NotFound from "./pages/NotFound";

// Create theme
const theme = createTheme({
  palette: {
    primary: {
      main: "#1976d2",
    },
    secondary: {
      main: "#f50057",
    },
  },
  typography: {
    fontFamily: ["Roboto", '"Helvetica Neue"', "Arial", "sans-serif"].join(","),
  },
});

function App() {
  const dispatch = useDispatch();
  const { userInfo } = useSelector((state) => state.auth);

  useEffect(() => {
    // Initialize user data from localStorage on app start
    dispatch(setUserFromLocalStorage());

    // Suppress ResizeObserver errors that don't affect functionality
    const resizeObserverErrorHandler = (e) => {
      if (
        e.message ===
        "ResizeObserver loop completed with undelivered notifications."
      ) {
        console.debug("Suppressed ResizeObserver error (non-critical)");
        e.stopImmediatePropagation();
        return;
      }
      if (e.message.includes("ResizeObserver loop limit exceeded")) {
        console.debug(
          "Suppressed ResizeObserver loop limit error (non-critical)"
        );
        e.stopImmediatePropagation();
        return;
      }
    };

    window.addEventListener("error", resizeObserverErrorHandler);

    return () => {
      window.removeEventListener("error", resizeObserverErrorHandler);
    };
  }, [dispatch]);

  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <ErrorBoundary>
        <SnackbarProvider maxSnack={3} autoHideDuration={3000}>
          <GoogleMapsProvider>
            <Routes>
              {/* Public routes */}
              <Route
                path="/login"
                element={userInfo ? <Navigate to="/" /> : <Login />}
              />
              <Route
                path="/register"
                element={userInfo ? <Navigate to="/" /> : <Register />}
              />

              {/* Protected routes */}
              <Route
                path="/"
                element={
                  <ProtectedRoute>
                    <Layout />
                  </ProtectedRoute>
                }
              >
                <Route index element={<Dashboard />} />
                <Route path="customers" element={<Customers />} />
                <Route path="customers/create" element={<CreateCustomer />} />
                <Route path="customers/:id" element={<CustomerDetail />} />
                <Route path="customers/:id/edit" element={<CreateCustomer />} />
                <Route path="jobs" element={<Jobs />} />
                <Route path="jobs/create" element={<CreateJob />} />
                <Route path="jobs/:id" element={<JobDetail />} />
                <Route path="invoices" element={<Invoices />} />
                <Route path="invoices/create" element={<CreateInvoice />} />
                <Route path="invoices/:id" element={<InvoiceDetail />} />
                <Route path="invoices/:id/edit" element={<CreateInvoice />} />
                <Route path="calendar" element={<Calendar />} />
                <Route path="materials" element={<Materials />} />
                {/* Define '/new' before '/:id' to ensure correct matching */}
                <Route path="materials/new" element={<CreateMaterial />} />
                <Route path="materials/:id" element={<MaterialsDetail />} />
                {/* Add edit route if needed */}
                {/* <Route path="materials/:id/edit" element={<CreateMaterial />} />  */}
                {/* <Route path="materials/quotes/create" element={<CreateMaterialQuote />} /> // Removed duplicate route */}
                <Route path="quotes" element={<Quotes />} />{" "}
                {/* Updated path */}
                <Route
                  path="quotes/create"
                  element={<CreateQuoteFormik />}
                />{" "}
                {/* Updated path, TEMPORARILY POINTING TO NEW */}
                <Route path="quotes/:id" element={<QuoteDetail />} />{" "}
                {/* Updated path */}
                {/* Add edit route */}
                <Route
                  path="quotes/edit/:id"
                  element={<Navigate to="/quotes/:id/edit" replace />}
                />{" "}
                {/* Redirect for backward compatibility */}
                <Route
                  path="quotes/:id/edit"
                  element={<CreateQuoteFormik />}
                />{" "}
                {/* Added edit route, TEMPORARILY POINTING TO NEW */}
                <Route path="quotes/preview" element={<QuotePreview />} />{" "}
                {/* Added preview route */}
                <Route path="technicians" element={<Technicians />} />
                <Route path="technicians/:id" element={<TechnicianDetail />} />
                <Route path="profile" element={<Profile />} />
                <Route
                  path="company-config"
                  element={
                    <AdminRoute>
                      <CompanyConfig />
                    </AdminRoute>
                  }
                />
              </Route>

              {/* 404 route */}
              <Route path="*" element={<NotFound />} />
            </Routes>

            {/* Global Notification System */}
            <SnackbarManager />
          </GoogleMapsProvider>
        </SnackbarProvider>
      </ErrorBoundary>
    </ThemeProvider>
  );
}

export default App;
