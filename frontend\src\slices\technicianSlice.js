import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import axios from "axios";

// Async thunks for API calls
export const getTechnicians = createAsyncThunk(
  "technicians/getTechnicians",
  async (_, { rejectWithValue, getState }) => {
    try {
      // Get token from state
      const { auth } = getState();

      // Configure headers with token
      const config = {
        headers: {
          Authorization: `Bearer ${auth.userInfo?.token}`,
        },
      };

      const response = await axios.get("/api/technicians", config);
      return response.data;
    } catch (error) {
      return rejectWithValue(
        error.response?.data?.message || "Failed to fetch technicians"
      );
    }
  }
);

export const getTechnicianById = createAsyncThunk(
  "technicians/getTechnicianById",
  async (id, { rejectWithValue, getState }) => {
    try {
      // Get token from state
      const { auth } = getState();

      // Configure headers with token
      const config = {
        headers: {
          Authorization: `Bearer ${auth.userInfo?.token}`,
        },
      };

      const response = await axios.get(`/api/technicians/${id}`, config);
      return response.data;
    } catch (error) {
      return rejectWithValue(
        error.response?.data?.message || "Failed to fetch technician details"
      );
    }
  }
);

export const createTechnician = createAsyncThunk(
  "technicians/createTechnician",
  async (technicianData, { rejectWithValue, getState }) => {
    try {
      // Get token from state
      const { auth } = getState();

      // Configure headers with token
      const config = {
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${auth.userInfo?.token}`,
        },
      };

      const response = await axios.post(
        "/api/technicians",
        technicianData,
        config
      );
      return response.data;
    } catch (error) {
      return rejectWithValue(
        error.response?.data?.message || "Failed to create technician"
      );
    }
  }
);

export const updateTechnician = createAsyncThunk(
  "technicians/updateTechnician",
  async ({ id, ...technicianData }, { rejectWithValue, getState }) => {
    try {
      // Get token from state
      const { auth } = getState();

      // Configure headers with token
      const config = {
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${auth.userInfo?.token}`,
        },
      };

      const response = await axios.put(
        `/api/technicians/${id}`,
        technicianData,
        config
      );
      return response.data;
    } catch (error) {
      return rejectWithValue(
        error.response?.data?.message || "Failed to update technician"
      );
    }
  }
);

export const deleteTechnician = createAsyncThunk(
  "technicians/deleteTechnician",
  async (id, { rejectWithValue, getState }) => {
    try {
      // Get token from state
      const { auth } = getState();

      // Configure headers with token
      const config = {
        headers: {
          Authorization: `Bearer ${auth.userInfo?.token}`,
        },
      };

      await axios.delete(`/api/technicians/${id}`, config);
      return id;
    } catch (error) {
      return rejectWithValue(
        error.response?.data?.message || "Failed to delete technician"
      );
    }
  }
);

// Initial state
const initialState = {
  technicians: [],
  technician: null,
  loading: false,
  success: false,
  error: null,
};

// Slice
const technicianSlice = createSlice({
  name: "technicians",
  initialState,
  reducers: {
    clearTechnicianError: (state) => {
      state.error = null;
    },
    resetTechnicianSuccess: (state) => {
      state.success = false;
    },
  },
  extraReducers: (builder) => {
    builder
      // Get all technicians
      .addCase(getTechnicians.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getTechnicians.fulfilled, (state, action) => {
        state.loading = false;
        state.technicians = action.payload;
      })
      .addCase(getTechnicians.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Get technician by ID
      .addCase(getTechnicianById.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getTechnicianById.fulfilled, (state, action) => {
        state.loading = false;
        state.technician = action.payload;
      })
      .addCase(getTechnicianById.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Create technician
      .addCase(createTechnician.pending, (state) => {
        state.loading = true;
        state.error = null;
        state.success = false;
      })
      .addCase(createTechnician.fulfilled, (state, action) => {
        state.loading = false;
        state.success = true;
        state.technicians.push(action.payload);
      })
      .addCase(createTechnician.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Update technician
      .addCase(updateTechnician.pending, (state) => {
        state.loading = true;
        state.error = null;
        state.success = false;
      })
      .addCase(updateTechnician.fulfilled, (state, action) => {
        state.loading = false;
        state.success = true;

        // Update technician in the list
        const index = state.technicians.findIndex(
          (tech) => tech._id === action.payload._id
        );
        if (index !== -1) {
          state.technicians[index] = action.payload;
        }

        // Update current technician if it's the same
        if (state.technician && state.technician._id === action.payload._id) {
          state.technician = action.payload;
        }
      })
      .addCase(updateTechnician.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Delete technician
      .addCase(deleteTechnician.pending, (state) => {
        state.loading = true;
        state.error = null;
        state.success = false;
      })
      .addCase(deleteTechnician.fulfilled, (state, action) => {
        state.loading = false;
        state.success = true;
        state.technicians = state.technicians.filter(
          (tech) => tech._id !== action.payload
        );

        // Clear current technician if it's the deleted one
        if (state.technician && state.technician._id === action.payload) {
          state.technician = null;
        }
      })
      .addCase(deleteTechnician.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      });
  },
});

export const { clearTechnicianError, resetTechnicianSuccess } =
  technicianSlice.actions;

export default technicianSlice.reducer;
