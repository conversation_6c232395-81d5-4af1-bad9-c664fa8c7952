/**
 * Web Search Service for Price Lookup
 * Uses web search to find pricing information for non-electrical items
 */

const logger = require("./logger");

/**
 * Web Search Service Class
 */
class WebSearchService {
  constructor() {
    this.name = "WebSearchService";
  }

  /**
   * Search for pricing information using web search
   * @param {string} query - Search query
   * @param {Object} options - Search options
   * @returns {Promise<Array>} - Array of pricing results
   */
  async searchForPricing(query, options = {}) {
    try {
      logger.info(`[WebSearchService] Searching for pricing: ${query}`);
      
      // Use the web-search tool available in the environment
      const webSearchTool = require('../tools/webSearchTool');
      
      const searchResults = await webSearchTool.search({
        query: query,
        num_results: options.numResults || 5
      });

      if (!searchResults || !searchResults.results) {
        logger.warn(`[WebSearchService] No search results returned for: ${query}`);
        return [];
      }

      // Extract pricing information from search results
      const pricingResults = this.extractPricingFromResults(searchResults.results, query);
      
      logger.info(`[WebSearchService] Extracted ${pricingResults.length} pricing results from ${searchResults.results.length} search results`);
      
      return pricingResults;
      
    } catch (error) {
      logger.error(`[WebSearchService] Search failed for "${query}":`, error);
      return [];
    }
  }

  /**
   * Extract pricing information from web search results
   * @param {Array} results - Web search results
   * @param {string} originalQuery - Original search query
   * @returns {Array} - Extracted pricing information
   */
  extractPricingFromResults(results, originalQuery) {
    const pricingResults = [];

    for (const result of results) {
      try {
        const pricing = this.extractPriceFromResult(result, originalQuery);
        if (pricing) {
          pricingResults.push(pricing);
        }
      } catch (error) {
        logger.warn(`[WebSearchService] Error extracting price from result:`, error);
      }
    }

    return pricingResults;
  }

  /**
   * Extract price information from a single search result
   * @param {Object} result - Single search result
   * @param {string} originalQuery - Original search query
   * @returns {Object|null} - Extracted pricing information
   */
  extractPriceFromResult(result, originalQuery) {
    const { title, snippet, url } = result;
    const text = `${title} ${snippet}`.toLowerCase();

    // Look for price patterns in the text
    const pricePatterns = [
      /\$(\d{1,3}(?:,\d{3})*(?:\.\d{2})?)/g,  // $123.45, $1,234.56
      /(\d{1,3}(?:,\d{3})*(?:\.\d{2})?)\s*dollars?/gi,  // 123 dollars
      /price[:\s]*\$?(\d{1,3}(?:,\d{3})*(?:\.\d{2})?)/gi,  // price: $123
      /cost[:\s]*\$?(\d{1,3}(?:,\d{3})*(?:\.\d{2})?)/gi,   // cost: $123
      /fee[:\s]*\$?(\d{1,3}(?:,\d{3})*(?:\.\d{2})?)/gi,    // fee: $123
    ];

    const foundPrices = [];
    
    for (const pattern of pricePatterns) {
      let match;
      while ((match = pattern.exec(text)) !== null) {
        const priceStr = match[1].replace(/,/g, '');
        const price = parseFloat(priceStr);
        
        // Filter reasonable prices (between $1 and $10,000 for most items)
        if (price >= 1 && price <= 10000) {
          foundPrices.push(price);
        }
      }
    }

    if (foundPrices.length === 0) {
      return null;
    }

    // Use the most common price or median if multiple prices found
    const price = foundPrices.length === 1 
      ? foundPrices[0] 
      : this.calculateMedianPrice(foundPrices);

    // Determine confidence based on source and context
    const confidence = this.calculatePriceConfidence(result, originalQuery, foundPrices);

    return {
      price: price,
      source: 'web_search',
      sourceUrl: url,
      sourceTitle: title,
      confidence: confidence,
      method: 'web_search_extraction',
      foundPrices: foundPrices,
      extractedFrom: snippet.substring(0, 200)
    };
  }

  /**
   * Calculate median price from array of prices
   * @param {Array} prices - Array of price numbers
   * @returns {number} - Median price
   */
  calculateMedianPrice(prices) {
    const sorted = prices.sort((a, b) => a - b);
    const mid = Math.floor(sorted.length / 2);
    
    return sorted.length % 2 !== 0 
      ? sorted[mid] 
      : (sorted[mid - 1] + sorted[mid]) / 2;
  }

  /**
   * Calculate confidence score for extracted price
   * @param {Object} result - Search result
   * @param {string} originalQuery - Original query
   * @param {Array} foundPrices - Array of found prices
   * @returns {string} - Confidence level (high, medium, low)
   */
  calculatePriceConfidence(result, originalQuery, foundPrices) {
    let score = 0;
    const { title, snippet, url } = result;
    const text = `${title} ${snippet}`.toLowerCase();
    const query = originalQuery.toLowerCase();

    // Higher confidence for government/official sites
    if (url.includes('.gov') || url.includes('.edu')) {
      score += 0.3;
    }

    // Higher confidence for contractor/industry sites
    if (url.includes('contractor') || url.includes('electric') || url.includes('permit')) {
      score += 0.2;
    }

    // Higher confidence if query terms appear in result
    const queryWords = query.split(' ').filter(word => word.length > 3);
    const matchingWords = queryWords.filter(word => text.includes(word));
    score += (matchingWords.length / queryWords.length) * 0.3;

    // Higher confidence for single consistent price vs multiple varying prices
    if (foundPrices.length === 1) {
      score += 0.2;
    } else {
      const priceVariation = Math.max(...foundPrices) - Math.min(...foundPrices);
      const avgPrice = foundPrices.reduce((a, b) => a + b, 0) / foundPrices.length;
      const variationPercent = priceVariation / avgPrice;
      
      if (variationPercent < 0.2) { // Less than 20% variation
        score += 0.1;
      }
    }

    // Convert score to confidence level
    if (score >= 0.7) return 'high';
    if (score >= 0.4) return 'medium';
    return 'low';
  }

  /**
   * Get service diagnostics
   * @returns {Object} - Service status and capabilities
   */
  getDiagnostics() {
    return {
      status: 'OK',
      name: this.name,
      capabilities: [
        'web_price_search',
        'price_extraction',
        'confidence_scoring'
      ],
      supportedPriceRange: '$1 - $10,000',
      lastUpdated: new Date().toISOString()
    };
  }
}

// Create singleton instance
const webSearchService = new WebSearchService();

module.exports = webSearchService;
