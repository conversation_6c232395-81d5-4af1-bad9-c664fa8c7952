/**
 * quoteRoutes.js
 * Routes for quote API
 */

const express = require("express");
const router = express.Router();
const quoteController = require("../controllers/quoteController"); // Renamed controller import
const auth = require("../middleware/auth").protect;
const { checkPermissions } = require("../middleware/permissions");
const upload = require("../middleware/uploadMiddleware"); // Import upload middleware

// --- Base Quote Routes ---
router.get("/", auth, quoteController.getQuotes); // Path relative to /api/quotes
router.post("/", auth, quoteController.createQuote); // Path relative to /api/quotes

// --- AI Quote Generation (Base Level) ---
router.post("/generate-ai", auth, quoteController.generateQuoteWithAI); // Path relative to /api/quotes

// --- Price Lookup without saving quote ---
router.post("/lookup-prices", auth, quoteController.lookupPricesWithoutSaving); // Get prices for items without saving quote
router.post("/lookup-prices-sse", auth, quoteController.lookupPricesWithSSE); // Real-time price lookup with Server-Sent Events

// --- Material Search & Details (Specific paths first) ---
router.get("/materials/search", auth, quoteController.searchMaterials); // General material search
router.get("/sku/:sku", auth, quoteController.getMaterialBySku); // Search by SKU
router.post("/url", auth, quoteController.getMaterialByUrl); // Search by URL
router.post("/details-by-url", auth, quoteController.getMaterialDetailsByUrl); // Get details by URL
router.get(
  "/history/:sku/:sourceId",
  auth,
  quoteController.getMaterialPriceHistory
); // Get price history

// --- Material Sources Management (Specific paths first) ---
router.get("/sources", auth, quoteController.getMaterialSources); // Get all sources
router.post(
  "/sources",
  auth,
  checkPermissions("admin"),
  quoteController.createMaterialSource
); // Create source
router.get("/sources/:id", auth, quoteController.getMaterialSourceById); // Get source by ID (parameterized, but under /sources)
router.put(
  "/sources/:id",
  auth,
  checkPermissions("admin"),
  quoteController.updateMaterialSource
); // Update source
router.delete(
  "/sources/:id",
  auth,
  checkPermissions("admin"),
  quoteController.deleteMaterialSource
); // Delete source
router.post(
  "/sources/:id/clear-cache",
  auth,
  checkPermissions("admin"),
  quoteController.clearSourceCache
); // Clear source cache

// --- Scraping Logs ---
router.get(
  "/logs",
  auth,
  checkPermissions("admin"),
  quoteController.getScrapingLogs
); // Get logs

// --- Parameterized Quote Routes (Define LAST) ---
// Image upload route for a specific quote
router.post(
  "/:id/images",
  auth,
  upload.array("quoteImages", 10),
  quoteController.uploadQuoteImages
);

router.get("/:id", auth, quoteController.getQuoteById); // Path relative to /api/quotes
router.put("/:id", auth, quoteController.updateQuote); // Path relative to /api/quotes
router.delete("/:id", auth, quoteController.deleteQuote); // Path relative to /api/quotes
router.get("/:id/pdf", auth, quoteController.generateQuotePDF); // Path relative to /api/quotes
router.post("/:id/send", auth, quoteController.emailQuote); // Path relative to /api/quotes
router.post("/:id/answer-ai", auth, quoteController.answerAIQuestions); // Path relative to /api/quotes
router.post("/:id/resolve-action", auth, quoteController.resolveQuoteAction); // Path relative to /api/quotes

// Temporary endpoint to manually start price lookup poller
router.post("/start-poller", auth, (req, res) => {
  try {
    const priceLookupPoller = require("../services/priceLookupPoller");
    if (!priceLookupPoller.isRunning) {
      priceLookupPoller.start();
      res.json({
        success: true,
        message: "Price lookup poller started successfully",
      });
    } else {
      res.json({
        success: true,
        message: "Price lookup poller is already running",
      });
    }
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
});

module.exports = router;
