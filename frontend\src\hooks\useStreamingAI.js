import { useState, useCallback, useRef, useEffect } from "react";
import { useSnackbar } from "notistack";
import { fetchEventSource } from "@microsoft/fetch-event-source";
import axios from "axios";

// Streaming phases
export const STREAMING_PHASES = {
  IDLE: "idle",
  CONNECTING: "connecting",
  PHASE_1: "phase_1", // Quick estimate
  PHASE_2: "phase_2", // Detailed analysis
  PHASE_3: "phase_3", // Price lookup
  COMPLETED: "completed",
  ERROR: "error",
};

// Custom error classes for better error handling
class RetriableError extends Error {
  constructor(message) {
    super(message);
    this.name = "RetriableError";
  }
}

class FatalError extends Error {
  constructor(message) {
    super(message);
    this.name = "FatalError";
  }
}

const useStreamingAI = () => {
  const [isStreaming, setIsStreaming] = useState(false);
  const [currentPhase, setCurrentPhase] = useState(STREAMING_PHASES.IDLE);
  const [progress, setProgress] = useState(0);
  const [statusMessage, setStatusMessage] = useState("");
  const [streamingData, setStreamingData] = useState(null);
  const [error, setError] = useState(null);
  const [sessionId, setSessionId] = useState(null);
  const [retryCount, setRetryCount] = useState(0);
  const [connectionHealth, setConnectionHealth] = useState({
    isHealthy: true,
    lastHeartbeat: null,
    reconnectAttempts: 0,
  });

  // 🆕 PHASE 4: Clarification state management for streaming AI
  const [clarificationRequired, setClarificationRequired] = useState(false);
  const [clarificationQuestions, setClarificationQuestions] = useState([]);
  const [clarificationReason, setClarificationReason] = useState(null);

  const { enqueueSnackbar } = useSnackbar();
  const abortControllerRef = useRef(null);
  const heartbeatIntervalRef = useRef(null);
  const maxRetries = 3;

  // Session persistence keys
  const SESSION_STORAGE_KEY = "workiz_streaming_session";
  const SESSION_DATA_KEY = "workiz_streaming_data";

  // Enhanced session restoration with validation and migration
  useEffect(() => {
    try {
      const persistedSession = sessionStorage.getItem(SESSION_STORAGE_KEY);
      const persistedData = sessionStorage.getItem(SESSION_DATA_KEY);

      if (persistedSession) {
        const sessionInfo = JSON.parse(persistedSession);
        const now = Date.now();

        // Validate session structure and migrate old versions
        if (!sessionInfo.sessionId || !sessionInfo.timestamp) {
          console.warn(
            "[Streaming] Invalid persisted session structure, clearing"
          );
          sessionStorage.removeItem(SESSION_STORAGE_KEY);
          sessionStorage.removeItem(SESSION_DATA_KEY);
          return;
        }

        // Check if session is still valid (extended to 10 minutes for better UX)
        const sessionAge = now - sessionInfo.timestamp;
        const maxSessionAge = 10 * 60 * 1000; // 10 minutes

        if (sessionAge < maxSessionAge) {
          console.log("[Streaming] Restoring persisted session:", {
            sessionId: sessionInfo.sessionId,
            age: Math.round(sessionAge / 1000) + "s",
            phase: sessionInfo.phase,
            version: sessionInfo.version || "legacy",
          });

          // Restore session state
          setSessionId(sessionInfo.sessionId);
          setCurrentPhase(sessionInfo.phase || STREAMING_PHASES.IDLE);
          setProgress(sessionInfo.progress || 0);
          setStatusMessage(sessionInfo.statusMessage || "");
          setRetryCount(sessionInfo.retryCount || 0);

          // Restore connection health if available
          if (sessionInfo.connectionHealth) {
            setConnectionHealth((prev) => ({
              ...prev,
              ...sessionInfo.connectionHealth,
              // Reset heartbeat as it's stale
              lastHeartbeat: null,
            }));
          }

          // Restore streaming data if available
          if (persistedData) {
            try {
              const streamingDataInfo = JSON.parse(persistedData);
              setStreamingData(streamingDataInfo);
            } catch (dataError) {
              console.warn(
                "[Streaming] Failed to restore streaming data:",
                dataError
              );
              sessionStorage.removeItem(SESSION_DATA_KEY);
            }
          }

          // Don't automatically reconnect, but keep session info for manual retry
          if (
            sessionInfo.phase !== STREAMING_PHASES.COMPLETED &&
            sessionInfo.phase !== STREAMING_PHASES.ERROR
          ) {
            const timeAgo = Math.round(sessionAge / 1000);
            setError(
              `Connection lost ${timeAgo}s ago. Click "Retry Connection" to resume.`
            );
            setCurrentPhase(STREAMING_PHASES.ERROR);
          }
        } else {
          console.log("[Streaming] Persisted session expired, clearing", {
            age: Math.round(sessionAge / 1000) + "s",
            maxAge: Math.round(maxSessionAge / 1000) + "s",
          });
          sessionStorage.removeItem(SESSION_STORAGE_KEY);
          sessionStorage.removeItem(SESSION_DATA_KEY);
        }
      }
    } catch (error) {
      console.warn("[Streaming] Failed to restore persisted session:", error);
      // Clear corrupted session data
      try {
        sessionStorage.removeItem(SESSION_STORAGE_KEY);
        sessionStorage.removeItem(SESSION_DATA_KEY);
      } catch (clearError) {
        console.error(
          "[Streaming] Failed to clear corrupted session data:",
          clearError
        );
      }
    }
  }, []);

  // Enhanced session persistence with recovery metadata
  useEffect(() => {
    if (sessionId && currentPhase !== STREAMING_PHASES.IDLE) {
      const sessionInfo = {
        sessionId,
        phase: currentPhase,
        progress,
        statusMessage,
        timestamp: Date.now(),
        isStreaming,
        connectionHealth,
        retryCount,
        // Add recovery metadata
        lastActiveTime: Date.now(),
        userAgent: navigator.userAgent,
        url: window.location.href,
        version: "1.1", // Version for future compatibility
      };

      try {
        sessionStorage.setItem(
          SESSION_STORAGE_KEY,
          JSON.stringify(sessionInfo)
        );
        console.debug("[Streaming] Session state persisted:", {
          sessionId,
          phase: currentPhase,
          timestamp: sessionInfo.timestamp,
        });
      } catch (error) {
        console.warn("[Streaming] Failed to persist session:", error);
        // Clear corrupted data
        try {
          sessionStorage.removeItem(SESSION_STORAGE_KEY);
        } catch (clearError) {
          console.error(
            "[Streaming] Failed to clear corrupted session data:",
            clearError
          );
        }
      }
    } else if (
      currentPhase === STREAMING_PHASES.IDLE ||
      currentPhase === STREAMING_PHASES.COMPLETED
    ) {
      // Clear persistence when session is idle or completed
      try {
        sessionStorage.removeItem(SESSION_STORAGE_KEY);
        sessionStorage.removeItem(SESSION_DATA_KEY);
        console.debug(
          "[Streaming] Session persistence cleared - phase:",
          currentPhase
        );
      } catch (error) {
        console.warn("[Streaming] Failed to clear session persistence:", error);
      }
    }
  }, [
    sessionId,
    currentPhase,
    progress,
    statusMessage,
    isStreaming,
    connectionHealth,
    retryCount,
  ]);

  // Persist streaming data changes
  useEffect(() => {
    if (streamingData && sessionId) {
      try {
        sessionStorage.setItem(SESSION_DATA_KEY, JSON.stringify(streamingData));
      } catch (error) {
        console.warn("[Streaming] Failed to persist streaming data:", error);
      }
    }
  }, [streamingData, sessionId]);

  // Retry connection for existing session
  const retryConnection = useCallback(
    async (options = {}) => {
      if (!sessionId) {
        console.error("[Streaming] No session ID available for retry");
        enqueueSnackbar(
          "No session available to retry. Please start a new generation.",
          { variant: "error" }
        );
        return false;
      }

      console.log(
        `[Streaming] Attempting to retry connection for session ${sessionId}`
      );

      try {
        // Reset error state
        setError(null);
        setIsStreaming(true);
        setCurrentPhase(STREAMING_PHASES.CONNECTING);
        setStatusMessage("Reconnecting to session...");

        // Check if session still exists on server
        const userInfo = localStorage.getItem("userInfo");
        const token = userInfo ? JSON.parse(userInfo).token : "";

        if (!token) {
          throw new Error(
            "Authentication token not found. Please log in again."
          );
        }

        // Check session status
        const statusResponse = await axios.get(
          `/api/streaming/session/${sessionId}/status`,
          {
            headers: {
              Authorization: `Bearer ${token}`,
              Accept: "application/json",
            },
            timeout: 10000,
          }
        );

        if (statusResponse.data.success) {
          console.log(
            "[Streaming] Session is still active, reconnecting to SSE..."
          );

          // Reconnect to SSE stream
          const protocol = window.location.protocol;
          const hostname = window.location.hostname;
          const port = process.env.NODE_ENV === "production" ? "" : ":5000";
          const backendUrl =
            process.env.REACT_APP_API_BASE_URL ||
            `${protocol}//${hostname}${port}`;
          const sseUrl = `${backendUrl}/api/streaming/quote-generation/${sessionId}`;

          // Create new abort controller
          if (
            abortControllerRef.current &&
            !abortControllerRef.current.signal.aborted
          ) {
            abortControllerRef.current.abort();
          }
          abortControllerRef.current = new AbortController();

          // Attempt to reconnect to existing session
          // Start fetch-event-source connection with advanced error handling
          await fetchEventSource(sseUrl, {
            method: "GET",
            headers: {
              Authorization: `Bearer ${token}`,
              Accept: "text/event-stream",
              "Cache-Control": "no-cache",
            },
            signal: abortControllerRef.current.signal,

            async onopen(response) {
              console.log(
                "[Streaming] SSE reconnection opened, status:",
                response.status
              );

              if (
                response.ok &&
                response.headers
                  .get("content-type")
                  ?.includes("text/event-stream")
              ) {
                console.log(
                  "[Streaming] Reconnection established successfully"
                );
                setError(null);
                setRetryCount(0);
                setConnectionHealth((prev) => ({
                  ...prev,
                  isHealthy: true,
                  lastHeartbeat: Date.now(),
                  reconnectAttempts: 0,
                }));
                return; // Connection is good
              } else if (
                response.status >= 400 &&
                response.status < 500 &&
                response.status !== 429
              ) {
                // Client-side errors (except rate limiting) are usually non-retriable
                const errorText = await response.text();
                console.error(
                  "[Streaming] Client error on reconnect:",
                  response.status,
                  errorText
                );
                throw new FatalError(
                  `Client error: ${response.status} - ${errorText}`
                );
              } else {
                // Server errors or rate limiting - retriable
                const errorText = await response.text();
                console.error(
                  "[Streaming] Server error on reconnect:",
                  response.status,
                  errorText
                );
                throw new RetriableError(
                  `Server error: ${response.status} - ${errorText}`
                );
              }
            },

            onmessage(event) {
              try {
                console.debug(
                  "[Streaming] Reconnect message:",
                  event.event,
                  event.data?.substring(0, 100)
                );

                // Update connection health
                setConnectionHealth((prev) => ({
                  ...prev,
                  isHealthy: true,
                  lastHeartbeat: Date.now(),
                  reconnectAttempts: 0,
                }));

                // Handle heartbeat messages
                if (event.event === "heartbeat") {
                  console.debug("[Streaming] Heartbeat received on reconnect");
                  return;
                }

                // Handle different event types
                if (event.event === "connected") {
                  console.log(
                    "[Streaming] Connected event received on reconnect"
                  );
                  setStatusMessage("Reconnected to AI streaming service");
                  return;
                }

                // Process regular data messages
                if (event.data) {
                  const data = JSON.parse(event.data);
                  // Basic message processing for reconnection
                  console.log(
                    "[Streaming] Reconnected - processing message:",
                    data.type
                  );
                }
              } catch (err) {
                console.error(
                  "[Streaming] Error processing reconnect message:",
                  err
                );
              }
            },

            onclose() {
              console.log("[Streaming] Reconnection closed");
              throw new RetriableError("Reconnection closed unexpectedly");
            },

            onerror(err) {
              console.error("[Streaming] Reconnection error:", err);
              throw new FatalError(`Reconnection failed: ${err.message}`);
            },
          });

          enqueueSnackbar("Successfully reconnected to streaming session!", {
            variant: "success",
          });
          return true;
        } else {
          throw new Error(
            statusResponse.data.message || "Session no longer available"
          );
        }
      } catch (error) {
        console.error("[Streaming] Retry connection failed:", error);

        let userFriendlyMessage =
          "Failed to reconnect. Please try starting a new quote generation.";

        if (error.response?.status === 404 || error.response?.status === 410) {
          // Enhanced handling for session not found with recovery details
          try {
            const errorData = error.response?.data;
            if (errorData?.recoveryAttempted) {
              console.log(
                "[Streaming] Backend attempted session recovery:",
                errorData.recoveryDetails
              );

              // Provide specific guidance based on recovery details
              const possibleCauses =
                errorData.supportInfo?.possibleCauses || [];
              if (possibleCauses.includes("high_capacity")) {
                userFriendlyMessage =
                  "AI service is currently busy. Please wait a moment and try again.";
              } else if (possibleCauses.includes("recent_cleanup")) {
                userFriendlyMessage =
                  "Your session was cleaned up. Please start a new quote generation.";
              } else if (possibleCauses.includes("registry_mismatch")) {
                userFriendlyMessage =
                  "Service restarted. Please start a new quote generation.";
              }
            }
          } catch (parseError) {
            console.warn(
              "[Streaming] Failed to parse retry error details:",
              parseError
            );
          }

          // Session expired or not found - clear persistence
          setSessionId(null);
          sessionStorage.removeItem(SESSION_STORAGE_KEY);
          sessionStorage.removeItem(SESSION_DATA_KEY);
        } else if (error.response?.status === 503) {
          userFriendlyMessage =
            "AI service is temporarily unavailable. Please try again in a few minutes.";
        } else if (
          error.code === "ECONNABORTED" ||
          error.message?.includes("timeout")
        ) {
          userFriendlyMessage =
            "Connection timed out. Please check your internet connection and try again.";
        } else if (error.response?.status >= 500) {
          userFriendlyMessage =
            "Server error. Please try again or contact support if the issue persists.";
        }

        setError(userFriendlyMessage);
        setIsStreaming(false);
        setCurrentPhase(STREAMING_PHASES.ERROR);
        enqueueSnackbar(userFriendlyMessage, {
          variant: "error",
          autoHideDuration: error.response?.status === 503 ? 8000 : 6000, // Longer for service unavailable
        });
        return false;
      }
    },
    [sessionId, enqueueSnackbar]
  );

  // Cleanup effect
  useEffect(() => {
    return () => {
      // Clear heartbeat interval on unmount
      if (heartbeatIntervalRef.current) {
        clearInterval(heartbeatIntervalRef.current);
        heartbeatIntervalRef.current = null;
      }

      // Abort any ongoing connections
      if (
        abortControllerRef.current &&
        !abortControllerRef.current.signal.aborted
      ) {
        abortControllerRef.current.abort();
      }
    };
  }, []);

  // Start streaming AI generation
  const startStreaming = useCallback(
    async (formData, options = {}) => {
      // Prevent multiple concurrent streaming sessions
      if (isStreaming && currentPhase !== STREAMING_PHASES.ERROR) {
        console.warn("[Streaming] Already streaming, ignoring new request");
        return;
      }

      try {
        // Reset state with enhanced cleanup
        setIsStreaming(true);
        setCurrentPhase(STREAMING_PHASES.CONNECTING);
        setProgress(0);
        setStatusMessage("Initializing AI streaming...");
        setError(null);
        setStreamingData(null);
        setRetryCount(0);
        setConnectionHealth({
          isHealthy: true,
          lastHeartbeat: Date.now(),
          reconnectAttempts: 0,
        });

        // Clear any existing intervals/timeouts
        if (heartbeatIntervalRef.current) {
          clearInterval(heartbeatIntervalRef.current);
          heartbeatIntervalRef.current = null;
        }

        // Create new abort controller for this session
        if (
          abortControllerRef.current &&
          !abortControllerRef.current.signal.aborted
        ) {
          abortControllerRef.current.abort();
        }
        abortControllerRef.current = new AbortController();

        // Get authentication token with validation
        const userInfo = localStorage.getItem("userInfo");
        const token = userInfo ? JSON.parse(userInfo).token : "";

        if (!token) {
          throw new Error(
            "Authentication token not found. Please log in again."
          );
        }

        // Enhanced form data with validated metadata matching backend expectations
        // Use the same FormData structure as traditional AI generation
        const enhancedFormData = new FormData();

        // Copy existing form data with validation (same as traditional AI)
        if (formData instanceof FormData) {
          for (const [key, value] of formData.entries()) {
            // Validate field types and sizes
            if (value instanceof File) {
              // Check file size limit (10MB)
              if (value.size > 10 * 1024 * 1024) {
                throw new Error(
                  `File "${value.name}" is too large. Maximum size is 10MB.`
                );
              }
              enhancedFormData.append(key, value);
            } else if (typeof value === "string") {
              // Validate string length (max 10000 chars per field)
              if (value.length > 10000) {
                console.warn(
                  `[Streaming] Field "${key}" truncated from ${value.length} to 10000 characters`
                );
                enhancedFormData.append(key, value.substring(0, 10000));
              } else {
                enhancedFormData.append(key, value);
              }
            } else {
              enhancedFormData.append(key, value);
            }
          }
        } else {
          // Handle object-style form data with validation (same as traditional AI)
          for (const [key, value] of Object.entries(formData)) {
            if (value !== null && value !== undefined) {
              if (typeof value === "object" && !(value instanceof File)) {
                const jsonString = JSON.stringify(value);
                if (jsonString.length > 5000) {
                  console.warn(
                    `[Streaming] Object field "${key}" truncated from ${jsonString.length} to 5000 characters`
                  );
                  enhancedFormData.append(key, jsonString.substring(0, 5000));
                } else {
                  enhancedFormData.append(key, jsonString);
                }
              } else if (typeof value === "string") {
                if (value.length > 10000) {
                  console.warn(
                    `[Streaming] Field "${key}" truncated from ${value.length} to 10000 characters`
                  );
                  enhancedFormData.append(key, value.substring(0, 10000));
                } else {
                  enhancedFormData.append(key, value);
                }
              } else {
                enhancedFormData.append(key, value);
              }
            }
          }
        }

        console.log(
          "[Streaming] Starting session initialization with enhanced metadata"
        );

        // Start streaming session with timeout and retry logic
        let startResponse;
        try {
          // Log payload for debugging (without sensitive data)
          console.log(
            "[Streaming] Starting session with backend-compatible FormData:",
            {
              fieldCount: Array.from(enhancedFormData.keys()).length,
              fields: Array.from(enhancedFormData.keys()),
              hasFiles: Array.from(enhancedFormData.values()).some(
                (v) => v instanceof File
              ),
              timestamp: new Date().toISOString(),
            }
          );

          // Use the same endpoint as traditional AI but with streaming parameter
          startResponse = await axios.post(
            "/api/ai/generate-quote-content",
            enhancedFormData,
            {
              headers: {
                Authorization: `Bearer ${token}`,
                Accept: "application/json",
                "X-Streaming-Mode": "true", // Add header to indicate streaming mode
                // Remove Content-Type to let Axios set it automatically for FormData
              },
              signal: abortControllerRef.current.signal,
              timeout: 60000, // 60 second timeout for session start
              validateStatus: function (status) {
                // Accept status codes 200-299 as success, reject 400+ for detailed error handling
                return status >= 200 && status < 300;
              },
              maxContentLength: 50 * 1024 * 1024, // 50MB max request size
              maxBodyLength: 50 * 1024 * 1024, // 50MB max body size
            }
          );
        } catch (startError) {
          if (startError.name === "AbortError") {
            console.debug("[Streaming] Session start aborted by user");
            return;
          }

          // Enhanced error handling based on latest best practices
          let errorMsg = "Failed to start streaming session";
          let userFriendlyMsg =
            "Unable to start AI generation. Please try again.";

          if (startError.response) {
            // Server responded with error status
            const status = startError.response.status;
            const responseData = startError.response.data;

            console.error("[Streaming] Session start failed with response:", {
              status,
              statusText: startError.response.statusText,
              data: responseData,
              headers: startError.response.headers,
              config: {
                url: startError.config?.url,
                method: startError.config?.method,
                headers: startError.config?.headers,
              },
            });

            switch (status) {
              case 400:
                errorMsg =
                  responseData?.message ||
                  responseData?.error ||
                  "Invalid request format";
                userFriendlyMsg =
                  "The request format is invalid. Please check your input and try again.";
                if (responseData?.details) {
                  console.error(
                    "[Streaming] 400 Error details:",
                    responseData.details
                  );
                  // Check for specific validation errors
                  if (
                    responseData.details.includes("FormData") ||
                    responseData.details.includes("multipart")
                  ) {
                    userFriendlyMsg =
                      "File upload format error. Please try uploading your files again.";
                  } else if (responseData.details.includes("required")) {
                    userFriendlyMsg =
                      "Some required information is missing. Please check your form and try again.";
                  }
                }
                break;
              case 401:
                errorMsg = "Authentication failed";
                userFriendlyMsg =
                  "Your session has expired. Please log in again.";
                break;
              case 403:
                errorMsg = "Access denied";
                userFriendlyMsg =
                  "You do not have permission to use AI generation.";
                break;
              case 422:
                errorMsg = responseData?.message || "Validation failed";
                userFriendlyMsg = "Please check your input data and try again.";
                break;
              case 429:
                errorMsg = "Rate limit exceeded";
                userFriendlyMsg =
                  "Too many requests. Please wait a moment before trying again.";
                break;
              case 503:
                errorMsg = "Service unavailable";
                userFriendlyMsg =
                  "Streaming service is currently busy. Please try again in a few moments.";
                break;
              case 504:
                errorMsg = "Request timeout";
                userFriendlyMsg =
                  "The request took too long. Please try again.";
                break;
              default:
                errorMsg =
                  responseData?.message ||
                  startError.message ||
                  `Server error (${status})`;
                userFriendlyMsg =
                  "A server error occurred. Please try again later.";
            }
          } else if (startError.request) {
            // Request was made but no response received (backend not available)
            console.log(
              "[Streaming] No response received - backend may not be available:",
              startError.request?.status || "No status"
            );
            errorMsg = "Network error - no response from server";
            userFriendlyMsg =
              "Unable to connect to the server. Please check your internet connection.";
          } else {
            // Error in request setup
            console.log(
              "[Streaming] Request setup error (expected when backend unavailable):",
              startError.message
            );
            errorMsg = startError.message;
            userFriendlyMsg =
              "Failed to prepare the request. Please try again.";
          }

          console.log(
            "[Streaming] Session start failed (falling back to traditional mode):",
            errorMsg
          );
          throw new Error(userFriendlyMsg);
        }

        // For backward compatibility, check if response has streaming sessionId
        // If not, fall back to traditional mode
        let newSessionId = startResponse.data?.sessionId;

        if (!newSessionId && startResponse.data?.success) {
          // Backend doesn't support streaming but returned success
          // Fall back to traditional mode processing
          console.log(
            "[Streaming] Backend doesn't support streaming, processing as traditional response"
          );

          if (options.onComplete && startResponse.data) {
            options.onComplete(startResponse.data);
          }

          setIsStreaming(false);
          setCurrentPhase(STREAMING_PHASES.COMPLETED);
          setProgress(100);
          setStatusMessage("AI generation completed successfully!");
          return;
        }

        if (!newSessionId) {
          throw new Error("Invalid session response: missing sessionId");
        }

        setSessionId(newSessionId);
        console.log(`[Streaming] Session ${newSessionId} created successfully`);

        // Allow brief time for session initialization on server
        await new Promise((resolve) => setTimeout(resolve, 300));

        // Setup SSE connection using fetch-event-source for better error handling
        const protocol = window.location.protocol;
        const hostname = window.location.hostname;
        const port = process.env.NODE_ENV === "production" ? "" : ":5000";
        const backendUrl =
          process.env.REACT_APP_API_BASE_URL ||
          `${protocol}//${hostname}${port}`;

        // Use the correct SSE endpoint that matches the backend streamingRoutes
        const sseUrl = `${backendUrl}/api/streaming/quote-generation/${newSessionId}`;

        console.log("[Streaming] Connecting to SSE:", sseUrl);

        // Reset retry count for new connection
        setRetryCount(0);

        // Verify session was created successfully before attempting SSE connection with retry logic
        const verifySessionWithRetry = async (
          maxRetries = 5,
          baseDelay = 200
        ) => {
          for (let attempt = 0; attempt < maxRetries; attempt++) {
            try {
              console.log(
                `[Streaming] Verifying session is ready for SSE connection (attempt ${
                  attempt + 1
                }/${maxRetries})...`
              );
              const sessionCheckResponse = await axios.get(
                `/api/streaming/session/${newSessionId}/status`,
                {
                  headers: {
                    Authorization: `Bearer ${token}`,
                    Accept: "application/json",
                  },
                  timeout: 8000,
                }
              );

              if (sessionCheckResponse.data.success) {
                console.log(
                  "[Streaming] Session verified successfully, proceeding with SSE connection"
                );
                return true;
              } else {
                console.warn(
                  `[Streaming] Session verification failed (attempt ${
                    attempt + 1
                  }):`,
                  sessionCheckResponse.data
                );
                if (attempt === maxRetries - 1) {
                  throw new Error(
                    `Session verification failed after ${maxRetries} attempts: ${
                      sessionCheckResponse.data.error || "session not ready"
                    }`
                  );
                }
              }
            } catch (sessionCheckError) {
              console.warn(
                `[Streaming] Session verification error (attempt ${
                  attempt + 1
                }):`,
                {
                  error: sessionCheckError.message,
                  response: sessionCheckError.response?.data,
                }
              );

              if (attempt === maxRetries - 1) {
                console.error(
                  "[Streaming] Session verification failed after all retries, proceeding with SSE connection as fallback"
                );
                return false;
              }
            }

            // Exponential backoff with jitter
            const delay =
              baseDelay * Math.pow(2, attempt) + Math.random() * 100;
            console.log(`[Streaming] Waiting ${delay}ms before retry...`);
            await new Promise((resolve) => setTimeout(resolve, delay));
          }
          return false;
        };

        await verifySessionWithRetry();

        // Helper function to process streaming messages with enhanced validation
        const processMessage = (data) => {
          if (!data || typeof data !== "object") {
            console.warn("[Streaming] Invalid message data:", data);
            return;
          }

          // Validate message has required fields
          if (!data.type) {
            console.warn("[Streaming] Message missing type field:", data);
            return;
          }

          console.debug(`[Streaming] Processing message: ${data.type}`);

          // Update state based on event type
          switch (data.type) {
            case "phase_update":
              if (data.phase) setCurrentPhase(data.phase);
              if (typeof data.progress === "number") setProgress(data.progress);
              if (data.message) setStatusMessage(data.message);
              break;

            // 🆕 PHASE 4: Clarification events for streaming AI
            case "clarification_check_progress":
              setProgress(data.progress || 32);
              setStatusMessage(
                data.message || "Checking if clarification is needed..."
              );
              break;

            case "clarification_required":
              console.log(
                "🆕 [PHASE 4] Clarification required event received:",
                data
              );
              setCurrentPhase("AWAITING_CLARIFICATION");
              setProgress(data.progress || 35);
              setStatusMessage(data.message || "Additional information needed");

              // Update clarification state variables
              setClarificationRequired(true);
              setClarificationQuestions(data.questions || []);
              setClarificationReason(
                data.reason || "Additional information needed"
              );

              // Store clarification data in streaming state
              setStreamingData((prev) => ({
                ...prev,
                clarificationRequired: true,
                clarificationQuestions: data.questions || [],
                clarificationReason:
                  data.reason || "Additional information needed",
                sessionId: data.sessionId,
              }));

              // Notify parent component via callback
              if (options.onClarificationRequired) {
                options.onClarificationRequired({
                  questions: data.questions || [],
                  reason: data.reason || "Additional information needed",
                  sessionId: data.sessionId,
                });
              }
              break;

            case "clarification_not_needed":
              console.log(
                "🆕 [PHASE 4] Clarification not needed, continuing with streaming"
              );
              setProgress(data.progress || 35);
              setStatusMessage(
                data.message || "Continuing with detailed analysis..."
              );
              break;

            case "clarification_processing":
              console.log("🆕 [PHASE 4] Processing clarification answers");
              setCurrentPhase("PROCESSING_CLARIFICATION");
              setProgress(data.progress || 40);
              setStatusMessage(data.message || "Processing your answers...");
              break;

            case "clarification_complete":
              console.log(
                "🆕 [PHASE 4] Clarification complete, resuming streaming"
              );
              setCurrentPhase(STREAMING_PHASES.PHASE_2);
              setProgress(data.progress || 45);
              setStatusMessage(
                data.message ||
                  "Clarification complete! Continuing with analysis..."
              );

              // Clear clarification state variables
              setClarificationRequired(false);
              setClarificationQuestions([]);
              setClarificationReason(null);

              // Clear clarification data
              setStreamingData((prev) => ({
                ...prev,
                clarificationRequired: false,
                clarificationQuestions: [],
                clarificationReason: null,
              }));

              if (options.onClarificationComplete) {
                options.onClarificationComplete();
              }
              break;

            case "clarification_check_error":
              console.warn(
                "🆕 [PHASE 4] Clarification check error:",
                data.error
              );
              setProgress(data.progress || 35);
              setStatusMessage(
                data.message || "Proceeding without clarification check..."
              );
              // Continue with normal flow - this is not a fatal error
              break;

            case "phase_1_progress":
              if (typeof data.progress === "number") setProgress(data.progress);
              if (data.message) setStatusMessage(data.message);
              if (data.phase) setCurrentPhase(data.phase);
              break;

            case "phase_1_complete":
              if (data.data) {
                setStreamingData((prev) => ({
                  ...prev,
                  quickEstimate: data.data,
                }));
                if (options.onPhase1Complete) {
                  options.onPhase1Complete(data.data);
                }
              }
              break;

            case "phase_2_progress":
            case "phase_2_complete":
              if (data.data) {
                setStreamingData((prev) => ({
                  ...prev,
                  detailedAnalysis: data.data,
                }));
                if (
                  data.type === "phase_2_progress" &&
                  options.onPhase2Progress
                ) {
                  options.onPhase2Progress(data.data);
                } else if (
                  data.type === "phase_2_complete" &&
                  options.onPhase2Complete
                ) {
                  options.onPhase2Complete(data.data);
                }
              }
              break;

            case "phase_3_complete":
              if (data.data) {
                setStreamingData((prev) => ({
                  ...prev,
                  priceLookupResults: data.data,
                }));
                if (options.onPhase3Complete) {
                  options.onPhase3Complete(data.data);
                }
              }
              break;

            case "price_lookup_step":
              // Handle detailed price lookup progress events
              if (data.itemIndex !== undefined || data.step) {
                setStreamingData((prev) => ({
                  ...prev,
                  priceLookupProgress: {
                    ...(prev?.priceLookupProgress || {}),
                    [data.itemIndex || "current"]: {
                      step: data.step,
                      message: data.message,
                      timestamp: data.timestamp,
                      confidence: data.confidence,
                      productsFound: data.productsFound,
                      contentLength: data.contentLength,
                      searchQuery: data.searchQuery,
                      error: data.error,
                    },
                  },
                }));

                // Update status message for current step
                if (data.message) {
                  setStatusMessage(data.message);
                }

                if (options.onPriceLookupStep) {
                  options.onPriceLookupStep(data);
                }
              }
              break;

            case "phase_3_item_complete":
              if (data.itemId && data.data) {
                setStreamingData((prev) => ({
                  ...prev,
                  priceLookupResults: {
                    ...(prev?.priceLookupResults || {}),
                    [data.itemId]: data.data,
                  },
                }));
                if (options.onItemPriceFound) {
                  options.onItemPriceFound(data.itemId, data.data);
                }
              }
              break;

            case "completed":
              console.log("[Streaming] Stream completed successfully");
              setCurrentPhase(STREAMING_PHASES.COMPLETED);
              setIsStreaming(false);
              setProgress(100);
              setStatusMessage("AI generation completed successfully!");

              if (data.data && options.onComplete) {
                options.onComplete(data.data);
              }

              enqueueSnackbar("AI generation completed successfully!", {
                variant: "success",
              });
              break;

            case "error":
              throw new FatalError(data.error || "Streaming error occurred");

            default:
              console.debug("[Streaming] Unknown message type:", data.type);
          }
        };

        // Start fetch-event-source connection with advanced error handling
        await fetchEventSource(sseUrl, {
          method: "GET",
          headers: {
            Authorization: `Bearer ${token}`,
            Accept: "text/event-stream",
            "Cache-Control": "no-cache",
          },
          signal: abortControllerRef.current.signal,

          async onopen(response) {
            console.log(
              "[Streaming] SSE connection opened, status:",
              response.status
            );

            if (
              response.ok &&
              response.headers
                .get("content-type")
                ?.includes("text/event-stream")
            ) {
              console.log("[Streaming] Connection established successfully");
              setError(null);
              setRetryCount(0);
              setConnectionHealth((prev) => ({
                ...prev,
                isHealthy: true,
                lastHeartbeat: Date.now(),
                reconnectAttempts: 0,
              }));
              return; // Connection is good
            } else if (response.status === 404) {
              // Enhanced session not found handling with recovery info
              let errorDetails = {};
              try {
                const errorText = await response.text();
                errorDetails = JSON.parse(errorText);

                console.warn("[Streaming] Session not found with details:", {
                  sessionId: errorDetails.sessionId,
                  recoveryAttempted: errorDetails.recoveryAttempted,
                  recoveryDetails: errorDetails.recoveryDetails,
                  possibleCauses:
                    errorDetails.supportInfo?.possibleCauses || [],
                  activeSessions: errorDetails.supportInfo?.activeSessions || 0,
                });

                // Check if this was a recent session that might be recoverable
                if (
                  errorDetails.recoveryAttempted &&
                  errorDetails.recoveryDetails?.possibleCauses?.includes(
                    "recent_cleanup"
                  )
                ) {
                  console.log(
                    "[Streaming] Session may have been recently cleaned up - suggesting retry"
                  );
                  const userFriendlyMessage =
                    "Session was recently cleaned up. Please try again.";
                  setStatusMessage(userFriendlyMessage);
                  setError(userFriendlyMessage);
                  return;
                }

                // For high capacity situations, provide better user guidance
                if (
                  errorDetails.supportInfo?.possibleCauses?.includes(
                    "high_capacity"
                  )
                ) {
                  const userFriendlyMessage =
                    "AI service is currently busy. Please try again in a moment.";
                  setStatusMessage(userFriendlyMessage);
                  throw new FatalError(userFriendlyMessage);
                }
              } catch (parseError) {
                console.warn(
                  "[Streaming] Failed to parse 404 error details:",
                  parseError
                );
              }

              // Generic session not found error - likely a race condition during initialization
              const userFriendlyMessage =
                errorDetails.suggestion ||
                "Session initialization failed. Starting in traditional mode...";
              setStatusMessage(userFriendlyMessage);
              throw new FatalError(userFriendlyMessage);
            } else if (
              response.status >= 400 &&
              response.status < 500 &&
              response.status !== 429
            ) {
              // Other client-side errors (except rate limiting) are usually non-retriable
              const errorText = await response.text();
              console.log(
                "[Streaming] Client error, falling back to traditional mode:",
                response.status,
                errorText
              );
              throw new FatalError(
                `Client error: ${response.status} - Falling back to traditional mode`
              );
            } else {
              // Server errors or rate limiting - retriable
              const errorText = await response.text();
              console.log(
                "[Streaming] Server error, will retry:",
                response.status,
                errorText
              );
              throw new RetriableError(
                `Server error: ${response.status} - ${errorText}`
              );
            }
          },

          onmessage(event) {
            try {
              console.debug(
                "[Streaming] Received message:",
                event.event,
                event.data?.substring(0, 100)
              );

              // Update connection health and reset heartbeat timer
              setConnectionHealth((prev) => ({
                ...prev,
                isHealthy: true,
                lastHeartbeat: Date.now(),
                reconnectAttempts: 0,
              }));

              // Handle heartbeat messages
              if (event.event === "heartbeat") {
                console.debug("[Streaming] Heartbeat received");
                return;
              }

              // Handle different event types
              if (event.event === "connected") {
                console.log("[Streaming] Connected event received");
                setStatusMessage("Connected to AI streaming service");
                return;
              }

              if (event.event === "error") {
                console.log(
                  "[Streaming] Server error event received:",
                  event.data
                );
                const errorData = JSON.parse(event.data);

                // Check for session expiration and not found errors
                if (
                  errorData.sessionExpired ||
                  errorData.error?.includes("Session not found") ||
                  errorData.code === "session_not_found" ||
                  errorData.code === "session_expired"
                ) {
                  console.warn("[Streaming] Session error detected:", {
                    error: errorData.error,
                    code: errorData.code,
                    sessionId: errorData.sessionId || sessionId,
                    reason: errorData.reason,
                    suggestion: errorData.suggestion,
                    timestamp: errorData.timestamp,
                  });

                  // Set user-friendly error message based on error type
                  const errorMessage =
                    errorData.code === "session_expired"
                      ? "Your session has expired due to inactivity. Please start a new quote generation."
                      : errorData.suggestion ||
                        "Your session has expired. Please start a new quote generation.";

                  setError(errorMessage);
                  setIsStreaming(false);
                  setCurrentPhase(STREAMING_PHASES.ERROR);

                  // Clear session data
                  setSessionId(null);
                  setStreamingData({});
                  setProgress(0);
                  setStatusMessage("");

                  throw new FatalError("Session expired - please restart");
                }

                // Handle other server errors
                if (errorData.error) {
                  console.error("[Streaming] Server error:", errorData.error);
                  setError(errorData.error);
                  setIsStreaming(false);
                  setCurrentPhase(STREAMING_PHASES.ERROR);
                  throw new FatalError(errorData.error);
                }

                throw new RetriableError(
                  errorData.error || "Server error occurred"
                );
              }

              if (event.event === "complete") {
                console.log("[Streaming] Complete event received");
                setCurrentPhase(STREAMING_PHASES.COMPLETED);
                setIsStreaming(false);
                return;
              }

              // Process regular data messages
              if (event.data) {
                const data = JSON.parse(event.data);
                processMessage(data);
              }
            } catch (err) {
              console.error("[Streaming] Error processing message:", err);
              if (err instanceof FatalError) {
                throw err; // Re-throw fatal errors to stop retrying
              }
              // Non-fatal errors don't stop the stream
            }
          },

          onclose() {
            // Check if connection was intentionally closed
            if (abortControllerRef.current?.signal.aborted) {
              console.debug(
                "[Streaming] Connection closed intentionally (aborted)"
              );
              return; // Don't attempt reconnection for intentional aborts
            }

            console.debug("[Streaming] Connection closed unexpectedly");
            setConnectionHealth((prev) => ({
              ...prev,
              isHealthy: false,
              reconnectAttempts: prev.reconnectAttempts + 1,
            }));

            // Only retry if we haven't exceeded max retries and we're not completed
            if (
              retryCount < maxRetries &&
              currentPhase !== STREAMING_PHASES.COMPLETED &&
              currentPhase !== STREAMING_PHASES.ERROR
            ) {
              setRetryCount((prev) => prev + 1);
              setStatusMessage(
                `Connection interrupted. Retrying... (${
                  retryCount + 1
                }/${maxRetries})`
              );
              throw new RetriableError("Connection closed unexpectedly");
            } else {
              // Clear heartbeat interval on fatal error
              if (heartbeatIntervalRef.current) {
                clearInterval(heartbeatIntervalRef.current);
                heartbeatIntervalRef.current = null;
              }
              console.info(
                "[Streaming] Switching to traditional mode after connection issues"
              );
              setStatusMessage(
                "Switching to traditional AI generation mode..."
              );
              throw new FatalError(
                "Connection failed - switching to traditional mode"
              );
            }
          },

          onerror(err) {
            // Check if error is due to intentional abort
            if (
              abortControllerRef.current?.signal.aborted ||
              err.name === "AbortError"
            ) {
              console.debug("[Streaming] Connection aborted intentionally");
              return; // Don't treat abort as an error
            }

            console.log("[Streaming] Connection error:", err.message || err);

            if (err instanceof FatalError) {
              // Fatal errors should stop the connection and trigger fallback
              setError("Streaming failed - switching to traditional mode");
              setCurrentPhase(STREAMING_PHASES.ERROR);
              setIsStreaming(false);

              // Use info level snackbar for expected fallbacks
              enqueueSnackbar(
                "Streaming unavailable - using traditional mode",
                { variant: "info" }
              );

              if (options.onError) {
                options.onError(
                  new Error("Streaming failed - switching to traditional mode")
                );
              }

              throw err; // Stop retrying
            } else if (err instanceof RetriableError) {
              // Retriable errors - let the library handle retrying
              console.log(
                "[Streaming] Retriable error, will retry:",
                err.message
              );
              setStatusMessage(
                `Connection error. Retrying... (${
                  retryCount + 1
                }/${maxRetries})`
              );

              // Increment retry count
              setRetryCount((prev) => {
                const newCount = prev + 1;
                if (newCount >= maxRetries) {
                  // Convert to fatal error if max retries exceeded
                  const fatalErr = new FatalError(
                    "Max retries exceeded - switching to traditional mode"
                  );
                  setError("Streaming failed - switching to traditional mode");
                  setCurrentPhase(STREAMING_PHASES.ERROR);
                  setIsStreaming(false);
                  enqueueSnackbar(
                    "Streaming unavailable - using traditional mode",
                    { variant: "info" }
                  );

                  if (options.onError) {
                    options.onError(
                      new Error(
                        "Streaming failed - switching to traditional mode"
                      )
                    );
                  }

                  throw fatalErr;
                }
                return newCount;
              });

              // Return retry delay (exponential backoff)
              return Math.min(1000 * Math.pow(2, retryCount), 10000);
            } else {
              // For unknown errors, check if it's a network abort
              if (
                err.code === "NETWORK_ERROR" ||
                err.message?.includes("aborted")
              ) {
                console.debug(
                  "[Streaming] Network error or abort, not retrying:",
                  err.message
                );
                return;
              }

              // Unknown errors - treat as retriable initially
              console.log(
                "[Streaming] Unknown error, treating as retriable:",
                err.message || err
              );
              throw new RetriableError(
                err.message || "Unknown connection error"
              );
            }
          },
        });

        console.log(
          "[Streaming] Fetch-event-source connection completed successfully"
        );
      } catch (err) {
        console.log(
          "[Streaming] Start error (graceful fallback expected):",
          err.message || err
        );

        // Provide more specific error handling
        let userMessage = "Streaming unavailable - using traditional mode";
        let shouldTriggerFallback = true;

        if (err.message) {
          const errorMsg = err.message.toLowerCase();
          if (
            errorMsg.includes("session verification failed") ||
            errorMsg.includes("session initialization failed")
          ) {
            userMessage = "Session setup failed - using traditional mode";
          } else if (
            errorMsg.includes("authentication") ||
            errorMsg.includes("token")
          ) {
            userMessage = "Authentication issue - please refresh and try again";
            shouldTriggerFallback = false;
          } else if (
            errorMsg.includes("network") ||
            errorMsg.includes("timeout")
          ) {
            userMessage = "Network issue - using traditional mode";
          }
        }

        setError(userMessage);
        setCurrentPhase(STREAMING_PHASES.ERROR);
        setIsStreaming(false);

        // Use info level for expected fallbacks
        enqueueSnackbar(userMessage, {
          variant: shouldTriggerFallback ? "info" : "warning",
        });

        if (options.onError && shouldTriggerFallback) {
          options.onError(
            new Error("Streaming failed - switching to traditional mode")
          );
        }
      }
    },
    [currentPhase, enqueueSnackbar, retryCount, sessionId, isStreaming]
  );

  // Stop streaming function
  const stopStreaming = useCallback(() => {
    console.log("[Streaming] Stopping stream");

    // Clear heartbeat interval
    if (heartbeatIntervalRef.current) {
      clearInterval(heartbeatIntervalRef.current);
      heartbeatIntervalRef.current = null;
    }

    // Gracefully abort the fetch-event-source connection
    if (
      abortControllerRef.current &&
      !abortControllerRef.current.signal.aborted
    ) {
      try {
        abortControllerRef.current.abort();
      } catch (error) {
        // Ignore abort errors as they're expected
        console.debug(
          "[Streaming] AbortController abort completed:",
          error.name
        );
      }
      abortControllerRef.current = null;
    }

    // Notify server to clean up session
    if (sessionId) {
      // Get token for authenticated request
      const userInfo = localStorage.getItem("userInfo");
      const token = userInfo ? JSON.parse(userInfo).token : "";

      // Use a separate AbortController for cleanup request to avoid conflicts
      const cleanupController = new AbortController();
      const cleanupTimeout = setTimeout(() => cleanupController.abort(), 5000); // 5 second timeout

      axios
        .post(
          `/api/streaming/stop-quote-generation/${sessionId}`,
          {},
          {
            headers: {
              Authorization: `Bearer ${token}`,
            },
            signal: cleanupController.signal,
            timeout: 5000,
          }
        )
        .then(() => {
          clearTimeout(cleanupTimeout);
        })
        .catch((err) => {
          clearTimeout(cleanupTimeout);
          // Only log if it's not a session not found error or abort error
          if (
            !err.response?.data?.message?.includes("not found") &&
            err.name !== "AbortError" &&
            err.code !== "ECONNABORTED"
          ) {
            console.error("[Streaming] Error stopping session:", err);
          }
        });
    }

    // Reset state
    setIsStreaming(false);
    setCurrentPhase(STREAMING_PHASES.IDLE);
    setSessionId(null);
    setError(null);
    setProgress(0);
    setStatusMessage("");
    setStreamingData({});
  }, [sessionId]);

  // Cleanup effect to prevent memory leaks and orphaned connections
  useEffect(() => {
    return () => {
      console.log("[Streaming] Cleaning up useStreamingAI hook");

      // Abort fetch-event-source connection
      if (
        abortControllerRef.current &&
        !abortControllerRef.current.signal.aborted
      ) {
        try {
          console.debug("[Streaming] Cleanup: Aborting connection on unmount");
          abortControllerRef.current.abort();
        } catch (error) {
          // Ignore abort errors during cleanup
          console.debug("[Streaming] Cleanup abort completed:", error.name);
        }
        abortControllerRef.current = null;
      }
    };
  }, []);

  // Connection health monitoring
  useEffect(() => {
    if (
      currentPhase === STREAMING_PHASES.IDLE ||
      currentPhase === STREAMING_PHASES.COMPLETED
    ) {
      return;
    }

    const healthCheckInterval = setInterval(() => {
      setConnectionHealth((prev) => {
        const now = Date.now();
        const timeSinceLastHeartbeat = now - (prev.lastHeartbeat || now);

        // Consider connection unhealthy if no heartbeat for 30 seconds
        if (timeSinceLastHeartbeat > 30000 && prev.isHealthy) {
          return {
            ...prev,
            isHealthy: false,
          };
        }
        return prev;
      });
    }, 10000); // Check every 10 seconds

    return () => clearInterval(healthCheckInterval);
  }, [currentPhase]);

  // 🆕 PHASE 4: Submit clarification answers to resume streaming
  const submitClarificationAnswers = useCallback(
    async (answers) => {
      if (!sessionId) {
        throw new Error("No active session for clarification answers");
      }

      if (!clarificationRequired) {
        throw new Error("Session is not awaiting clarification");
      }

      console.log("🆕 [PHASE 4] Submitting clarification answers:", {
        sessionId,
        answersCount: Object.keys(answers).length,
        answers,
      });

      try {
        // Get authentication token
        const userInfo = localStorage.getItem("userInfo");
        const token = userInfo ? JSON.parse(userInfo).token : "";

        if (!token) {
          throw new Error(
            "Authentication token not found. Please log in again."
          );
        }

        // Submit answers to backend
        const response = await axios.post(
          `/api/streaming/session/${sessionId}/answer-clarification`,
          { answers },
          {
            headers: {
              Authorization: `Bearer ${token}`,
              "Content-Type": "application/json",
            },
            timeout: 60000, // Increased timeout to 60 seconds to match backend processing time
          }
        );

        if (response.data.success) {
          console.log(
            "🆕 [PHASE 4] Clarification answers submitted successfully"
          );

          // Update local state - the streaming will automatically resume
          setClarificationRequired(false);
          setClarificationQuestions([]);
          setClarificationReason(null);

          enqueueSnackbar(
            "Answers submitted! Continuing with AI generation...",
            { variant: "success" }
          );

          return response.data;
        } else {
          throw new Error(response.data.error || "Failed to submit answers");
        }
      } catch (error) {
        console.error(
          "🆕 [PHASE 4] Failed to submit clarification answers:",
          error
        );

        let errorMessage = "Failed to submit answers. Please try again.";

        if (
          error.code === "ECONNABORTED" ||
          error.message?.includes("timeout")
        ) {
          errorMessage =
            "Request timed out. The server may be processing your answers. Please wait a moment and check if streaming continues.";
        } else if (error.response?.status === 401) {
          errorMessage =
            "Authentication failed. Please refresh the page and try again.";
        } else if (error.response?.status === 404) {
          errorMessage =
            "Session not found. Please restart the AI generation process.";
        } else if (error.response?.data?.error) {
          errorMessage = error.response.data.error;
        } else if (error.message) {
          errorMessage = error.message;
        }

        enqueueSnackbar(errorMessage, { variant: "error" });
        throw error;
      }
    },
    [sessionId, clarificationRequired, enqueueSnackbar]
  );

  return {
    isStreaming,
    currentPhase,
    progress,
    statusMessage,
    streamingData,
    error,
    connectionHealth,
    startStreaming,
    stopStreaming,
    retryConnection,
    STREAMING_PHASES,
    isConnectionHealthy: connectionHealth.isHealthy,
    canRetry:
      sessionId && !isStreaming && currentPhase === STREAMING_PHASES.ERROR,

    // 🆕 PHASE 4: Clarification support for streaming AI
    clarificationRequired,
    clarificationQuestions,
    clarificationReason,
    submitClarificationAnswers,

    // Helper flags for UI state management
    isAwaitingClarification: clarificationRequired,
    hasClarificationQuestions: clarificationQuestions.length > 0,
  };
};

export default useStreamingAI;
