import React, { useEffect, useState } from "react";
import { use<PERSON>ara<PERSON>, useNavigate } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import {
  Container,
  Paper,
  Box,
  Typography,
  Grid,
  Chip,
  Avatar,
  Button,
  CircularProgress,
  Alert,
  Divider,
  Card,
  CardContent,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Snackbar,
} from "@mui/material";
import ArrowBackIcon from "@mui/icons-material/ArrowBack";
import EmailIcon from "@mui/icons-material/Email";
import PhoneIcon from "@mui/icons-material/Phone";
import BadgeIcon from "@mui/icons-material/Badge";
import EventIcon from "@mui/icons-material/Event";
import ConstructionIcon from "@mui/icons-material/Construction";
import HomeRepairServiceIcon from "@mui/icons-material/HomeRepairService";
import EditIcon from "@mui/icons-material/Edit";
import DeleteIcon from "@mui/icons-material/Delete";
import SmartToyIcon from "@mui/icons-material/SmartToy";
import PersonIcon from "@mui/icons-material/Person";

// Import redux actions and components
import { getTechnicianById } from "../slices/technicianSlice";
import { getStatusColor } from "../components/technicians/utils/technicianHelpers";
import DeleteDialog from "../components/technicians/dialogs/DeleteDialog";
import CreateEditDialog from "../components/technicians/dialogs/CreateEditDialog";
import AISuggestionsDialog from "../components/technicians/dialogs/AISuggestionsDialog";

const TechnicianDetail = () => {
  const { id } = useParams();
  const dispatch = useDispatch();
  const navigate = useNavigate();

  // Redux state
  const { technician, loading, error } = useSelector(
    (state) => state.technicians
  );
  const { userInfo } = useSelector((state) => state.auth);

  // Local state
  const [confirmDeleteOpen, setConfirmDeleteOpen] = useState(false);
  const [editTechnicianOpen, setEditTechnicianOpen] = useState(false);
  const [aiSuggestionsOpen, setAiSuggestionsOpen] = useState(false);
  const [validationError, setValidationError] = useState(false);
  const [aiLoading, setAiLoading] = useState(false);
  const [aiPrompt, setAiPrompt] = useState("");
  const [aiSuggestions, setAiSuggestions] = useState(null);

  // Snackbar state
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState("");
  const [snackbarSeverity, setSnackbarSeverity] = useState("success");

  // Form data for edit technician
  const [technicianFormData, setTechnicianFormData] = useState({
    firstName: "",
    lastName: "",
    email: "",
    phone: "",
    contractorType: "",
    hourlyRate: "",
    skills: [],
    services: [],
  });

  // Fetch technician data on component mount
  useEffect(() => {
    if (id) {
      dispatch(getTechnicianById(id));
    }
  }, [dispatch, id]);

  // Update form data when technician data is loaded
  useEffect(() => {
    if (technician) {
      setTechnicianFormData({
        firstName: technician.firstName || "",
        lastName: technician.lastName || "",
        email: technician.email || "",
        phone: technician.phone || "",
        contractorType: technician.contractorType || "",
        hourlyRate: technician.hourlyRate || "",
        skills: technician.skills || [],
        services: technician.services || [],
      });
    }
  }, [technician]);

  // Event handlers
  const handleGoBack = () => {
    navigate("/technicians");
  };

  // Delete dialog handlers
  const handleOpenDeleteDialog = () => {
    setConfirmDeleteOpen(true);
  };

  const handleCloseDeleteDialog = () => {
    setConfirmDeleteOpen(false);
  };

  const handleConfirmDelete = () => {
    // Would dispatch delete action here
    // dispatch(deleteTechnician(id));
    showSnackbar("Technician deleted successfully", "success");
    setConfirmDeleteOpen(false);
    navigate("/technicians");
  };

  // Edit dialog handlers
  const handleOpenEditDialog = () => {
    setEditTechnicianOpen(true);
    setValidationError(false);
  };

  const handleCloseEditDialog = () => {
    setEditTechnicianOpen(false);
  };

  const handleFormChange = (e) => {
    const { name, value } = e.target;
    setTechnicianFormData({
      ...technicianFormData,
      [name]: value,
    });
  };

  const handleUpdateTechnician = () => {
    // Check required fields
    if (
      !technicianFormData.firstName ||
      !technicianFormData.lastName ||
      !technicianFormData.email
    ) {
      setValidationError(true);
      return;
    }

    // Would dispatch update action here
    // dispatch(updateTechnician({ id, ...technicianFormData }));

    showSnackbar("Technician updated successfully", "success");
    setEditTechnicianOpen(false);
  };

  // AI suggestions dialog handlers
  const handleOpenAiSuggestions = () => {
    setAiPrompt(
      `Suggest jobs and skills for ${technician.firstName} ${
        technician.lastName
      } who specializes in ${
        technician.contractorType || "general contracting"
      }`
    );
    setAiSuggestions(null);
    setAiSuggestionsOpen(true);
  };

  const handleCloseAiSuggestions = () => {
    setAiSuggestionsOpen(false);
  };

  const handleAiPromptChange = (e) => {
    setAiPrompt(e.target.value);
  };

  const handleGenerateInsights = () => {
    setAiLoading(true);
    // Simulate AI response
    setTimeout(() => {
      setAiSuggestions({
        recommendedSkills: [
          "Electrical Wiring",
          "Circuit Diagnostics",
          "Panel Installation",
          "Lighting Systems",
          "Safety Compliance",
        ],
        recommendedJobs: [
          {
            id: "job1",
            title: "Home Rewiring",
            customer: "John Smith",
            urgency: "High",
          },
          {
            id: "job2",
            title: "Office Lighting Installation",
            customer: "Acme Corp",
            urgency: "Medium",
          },
        ],
      });
      setAiLoading(false);
    }, 1500);
  };

  const handleAssignAiSuggestedJob = (jobId) => {
    showSnackbar("Job assigned successfully", "success");
    setAiSuggestionsOpen(false);
  };

  // Function to handle assigning a job directly
  const handleAssignJob = () => {
    navigate("/jobs/create", { state: { technicianId: id } });
  };

  // Snackbar handlers
  const showSnackbar = (message, severity) => {
    setSnackbarMessage(message);
    setSnackbarSeverity(severity);
    setSnackbarOpen(true);
  };

  const handleCloseSnackbar = () => {
    setSnackbarOpen(false);
  };

  return (
    <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
      {loading ? (
        <Box display="flex" justifyContent="center" p={3}>
          <CircularProgress />
        </Box>
      ) : error ? (
        <Alert severity="error">{error}</Alert>
      ) : technician ? (
        <>
          {/* Header with actions */}
          <Box
            display="flex"
            justifyContent="space-between"
            alignItems="center"
            mb={3}
          >
            <Button
              startIcon={<ArrowBackIcon />}
              onClick={handleGoBack}
              sx={{ mb: 2 }}
            >
              Back to Technicians
            </Button>

            <Box>
              <Button
                variant="outlined"
                color="secondary"
                startIcon={<SmartToyIcon />}
                onClick={handleOpenAiSuggestions}
                sx={{ mr: 1 }}
              >
                AI Insights
              </Button>

              <Button
                variant="contained"
                color="success"
                onClick={handleAssignJob}
                sx={{ mr: 1 }}
              >
                Assign Job
              </Button>

              {userInfo?.role === "admin" && (
                <>
                  <Button
                    variant="outlined"
                    color="primary"
                    startIcon={<EditIcon />}
                    onClick={handleOpenEditDialog}
                    sx={{ mr: 1 }}
                  >
                    Edit
                  </Button>

                  <Button
                    variant="outlined"
                    color="error"
                    startIcon={<DeleteIcon />}
                    onClick={handleOpenDeleteDialog}
                  >
                    Delete
                  </Button>
                </>
              )}
            </Box>
          </Box>

          {/* Technician profile */}
          <Grid container spacing={3}>
            {/* Main profile information */}
            <Grid item xs={12} md={4}>
              <Paper sx={{ p: 3, height: "100%" }}>
                <Box
                  display="flex"
                  flexDirection="column"
                  alignItems="center"
                  mb={3}
                >
                  <Avatar
                    src={technician.profileImage}
                    alt={`${technician.firstName} ${technician.lastName}`}
                    sx={{ width: 120, height: 120, mb: 2 }}
                  >
                    {!technician.profileImage && (
                      <PersonIcon fontSize="large" />
                    )}
                  </Avatar>

                  <Typography variant="h4" gutterBottom>
                    {technician.firstName} {technician.lastName}
                  </Typography>

                  <Chip
                    label={technician.contractorType || "General Contractor"}
                    color="primary"
                    sx={{ mb: 1 }}
                  />

                  <Chip
                    label={technician.availabilityStatus || "Available"}
                    color={getStatusColor(technician.availabilityStatus)}
                  />
                </Box>

                <Divider sx={{ mb: 2 }} />

                <List>
                  <ListItem>
                    <ListItemIcon>
                      <EmailIcon />
                    </ListItemIcon>
                    <ListItemText
                      primary="Email"
                      secondary={technician.email}
                    />
                  </ListItem>

                  <ListItem>
                    <ListItemIcon>
                      <PhoneIcon />
                    </ListItemIcon>
                    <ListItemText
                      primary="Phone"
                      secondary={technician.phone || "Not provided"}
                    />
                  </ListItem>

                  <ListItem>
                    <ListItemIcon>
                      <BadgeIcon />
                    </ListItemIcon>
                    <ListItemText
                      primary="Hourly Rate"
                      secondary={
                        technician.hourlyRate
                          ? `$${technician.hourlyRate}`
                          : "Not specified"
                      }
                    />
                  </ListItem>

                  <ListItem>
                    <ListItemIcon>
                      <EventIcon />
                    </ListItemIcon>
                    <ListItemText
                      primary="Join Date"
                      secondary={
                        technician.joinDate
                          ? new Date(technician.joinDate).toLocaleDateString()
                          : "Not specified"
                      }
                    />
                  </ListItem>
                </List>
              </Paper>
            </Grid>

            {/* Skills and services */}
            <Grid item xs={12} md={8}>
              <Grid container spacing={3}>
                <Grid item xs={12}>
                  <Paper sx={{ p: 3 }}>
                    <Typography variant="h6" gutterBottom>
                      <ConstructionIcon
                        sx={{ verticalAlign: "middle", mr: 1 }}
                      />
                      Skills
                    </Typography>

                    <Box display="flex" flexWrap="wrap" gap={1} mb={3}>
                      {technician.skills && technician.skills.length > 0 ? (
                        technician.skills.map((skill, index) => (
                          <Chip
                            key={index}
                            label={skill}
                            color="primary"
                            variant="outlined"
                          />
                        ))
                      ) : (
                        <Typography color="text.secondary">
                          No skills listed
                        </Typography>
                      )}
                    </Box>

                    <Typography variant="h6" gutterBottom>
                      <HomeRepairServiceIcon
                        sx={{ verticalAlign: "middle", mr: 1 }}
                      />
                      Services Offered
                    </Typography>

                    <Box display="flex" flexWrap="wrap" gap={1}>
                      {technician.services && technician.services.length > 0 ? (
                        technician.services.map((service, index) => (
                          <Chip
                            key={index}
                            label={service}
                            color="secondary"
                            variant="outlined"
                          />
                        ))
                      ) : (
                        <Typography color="text.secondary">
                          No services listed
                        </Typography>
                      )}
                    </Box>
                  </Paper>
                </Grid>

                {/* Recent jobs */}
                <Grid item xs={12}>
                  <Paper sx={{ p: 3 }}>
                    <Typography variant="h6" gutterBottom>
                      Recent Jobs
                    </Typography>

                    {technician.recentJobs &&
                    technician.recentJobs.length > 0 ? (
                      technician.recentJobs.map((job) => (
                        <Card key={job.id} sx={{ mb: 2 }}>
                          <CardContent>
                            <Typography variant="h6">{job.title}</Typography>
                            <Typography color="text.secondary" gutterBottom>
                              {job.date
                                ? new Date(job.date).toLocaleDateString()
                                : "No date"}
                            </Typography>
                            <Typography variant="body2">
                              {job.description || "No description available"}
                            </Typography>
                            <Box mt={1}>
                              <Chip
                                label={job.status || "Pending"}
                                color={
                                  job.status === "Completed"
                                    ? "success"
                                    : job.status === "In Progress"
                                    ? "info"
                                    : job.status === "Cancelled"
                                    ? "error"
                                    : "default"
                                }
                                size="small"
                              />
                            </Box>
                          </CardContent>
                        </Card>
                      ))
                    ) : (
                      <Alert severity="info">No recent jobs</Alert>
                    )}
                  </Paper>
                </Grid>
              </Grid>
            </Grid>
          </Grid>

          {/* Dialogs */}
          <DeleteDialog
            open={confirmDeleteOpen}
            onClose={handleCloseDeleteDialog}
            onConfirm={handleConfirmDelete}
            technician={technician}
          />

          <CreateEditDialog
            open={editTechnicianOpen}
            onClose={handleCloseEditDialog}
            isCreateMode={false}
            formData={technicianFormData}
            onFormChange={handleFormChange}
            onSubmit={handleUpdateTechnician}
            validationError={validationError}
          />

          <AISuggestionsDialog
            open={aiSuggestionsOpen}
            onClose={handleCloseAiSuggestions}
            technician={technician}
            aiPrompt={aiPrompt}
            onAiPromptChange={handleAiPromptChange}
            onGenerateInsights={handleGenerateInsights}
            aiLoading={aiLoading}
            aiSuggestions={aiSuggestions}
            onAssignJob={handleAssignAiSuggestedJob}
          />
        </>
      ) : (
        <Alert severity="info">Technician not found</Alert>
      )}

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbarOpen}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: "bottom", horizontal: "right" }}
      >
        <Alert
          onClose={handleCloseSnackbar}
          severity={snackbarSeverity}
          sx={{ width: "100%" }}
        >
          {snackbarMessage}
        </Alert>
      </Snackbar>
    </Container>
  );
};

export default TechnicianDetail;
