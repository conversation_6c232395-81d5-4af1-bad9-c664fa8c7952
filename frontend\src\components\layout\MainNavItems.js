import React from "react";
import { useLocation, useNavigate } from "react-router-dom";
import { useSelector } from "react-redux";
import {
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  ListItemButton,
} from "@mui/material";
import {
  Dashboard as DashboardIcon,
  People as PeopleIcon,
  Assignment as JobIcon,
  Receipt as InvoiceIcon,
  CalendarToday as CalendarIcon,
  Inventory as InventoryIcon, // Changed from MaterialsIcon
  RequestQuote as RequestQuoteIcon,
  Engineering as EngineeringIcon,
  Business as BusinessIcon,
} from "@mui/icons-material";

const MainNavItems = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const { userInfo } = useSelector((state) => state.auth);

  // Check if user is an administrator
  const isAdmin = userInfo?.role === "Administrators";

  const menuItems = [
    {
      text: "Dashboard",
      icon: <DashboardIcon />,
      path: "/",
    },
    {
      text: "Customers",
      icon: <PeopleIcon />,
      path: "/customers",
    },
    {
      text: "Jobs",
      icon: <JobIcon />,
      path: "/jobs",
    },
    {
      text: "Invoices",
      icon: <InvoiceIcon />,
      path: "/invoices",
    },
    {
      text: "Calendar",
      icon: <CalendarIcon />,
      path: "/calendar",
    },
    {
      text: "Materials",
      icon: <InventoryIcon />,
      path: "/materials",
    },
    {
      text: "Quotes",
      icon: <RequestQuoteIcon />,
      path: "/quotes", // Updated path
    },
    {
      text: "Technicians",
      icon: <EngineeringIcon />,
      path: "/technicians",
    },
  ];

  // Add Company Configuration menu item for administrators only
  if (isAdmin) {
    menuItems.push({
      text: "Company Settings",
      icon: <BusinessIcon />,
      path: "/company-config",
    });
  }

  return (
    <List>
      {menuItems.map((item) => (
        <ListItem key={item.text} disablePadding>
          <ListItemButton
            selected={location.pathname === item.path}
            onClick={() => navigate(item.path)}
          >
            <ListItemIcon>{item.icon}</ListItemIcon>
            <ListItemText primary={item.text} />
          </ListItemButton>
        </ListItem>
      ))}
    </List>
  );
};

export default MainNavItems;
