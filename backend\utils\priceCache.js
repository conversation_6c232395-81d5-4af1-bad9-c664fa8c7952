/**
 * priceCache.js
 * Redis cache utility for material price data
 */

const logger = require("./logger");

// Only import Redis if not disabled
let redis = null;
try {
  if (process.env.REDIS_DISABLED !== "true") {
    const cacheModule = require("../middleware/cache");
    redis = cacheModule.redis;
  }
} catch (error) {
  logger.warn("Redis cache not available, using mock client:", error.message);
}

// Create mock Redis client if <PERSON><PERSON> is disabled or unavailable
if (!redis) {
  redis = {
    get: async () => null,
    set: async () => "OK",
    del: async () => 1,
    keys: async () => [],
    flushall: async () => "OK",
    pipeline: () => ({
      get: () => {},
      exec: async () => [],
    }),
  };
}

// Helper methods that wrap Redis commands
const getAsync = async (key) => {
  try {
    return await redis.get(key);
  } catch (error) {
    logger.error(`Error getting Redis key ${key}:`, error);
    return null;
  }
};

const setAsync = async (key, value, expiryType, expiry) => {
  try {
    if (expiryType && expiry) {
      return await redis.set(key, value, expiryType, expiry);
    } else {
      return await redis.set(key, value);
    }
  } catch (error) {
    logger.error(`Error setting Redis key ${key}:`, error);
    return null;
  }
};

const delAsync = async (key) => {
  try {
    return await redis.del(key);
  } catch (error) {
    logger.error(`Error deleting Redis key ${key}:`, error);
    return 0;
  }
};

const keysAsync = async (pattern) => {
  try {
    return await redis.keys(pattern);
  } catch (error) {
    logger.error(`Error getting Redis keys with pattern ${pattern}:`, error);
    return [];
  }
};

const flushallAsync = async () => {
  try {
    return await redis.flushall();
  } catch (error) {
    logger.error("Error flushing Redis database:", error);
    return null;
  }
};

// Helper method to get with JSON parsing
const getJson = async (key) => {
  try {
    const data = await getAsync(key);
    return data ? JSON.parse(data) : null;
  } catch (error) {
    logger.error(`Error retrieving JSON from Redis for key ${key}:`, error);
    return null;
  }
};

// Redis client for price cache operations
const client = {
  // Basic Redis methods
  get: getAsync,
  set: setAsync,
  del: delAsync,
  keys: keysAsync,
  flushall: flushallAsync,

  // Enhanced methods
  getJson,

  // Get multiple items from cache
  async getMulti(keys) {
    if (!keys || !Array.isArray(keys) || keys.length === 0) {
      return [];
    }

    try {
      const pipeline = redis.pipeline();
      keys.forEach((key) => pipeline.get(key));
      const results = await pipeline.exec();

      return results.map((result, index) => {
        const [err, data] = result;
        if (err) {
          logger.error(`Error retrieving ${keys[index]} from Redis:`, err);
          return null;
        }

        try {
          return data ? JSON.parse(data) : null;
        } catch (parseError) {
          logger.error(`Error parsing JSON for ${keys[index]}:`, parseError);
          return null;
        }
      });
    } catch (error) {
      logger.error(`Error retrieving multiple keys from Redis:`, error);
      return keys.map(() => null);
    }
  },

  // Set with expiration
  async setEx(key, ttl, value) {
    try {
      return await setAsync(key, value, "EX", ttl);
    } catch (error) {
      logger.error(`Error setting Redis key ${key} with expiration:`, error);
      return null;
    }
  },

  // Set JSON data
  async setJson(key, value, ttl = null) {
    try {
      const jsonValue = JSON.stringify(value);

      if (ttl) {
        return await setAsync(key, jsonValue, "EX", ttl);
      } else {
        return await setAsync(key, jsonValue);
      }
    } catch (error) {
      logger.error(`Error setting JSON in Redis for key ${key}:`, error);
      return null;
    }
  },

  // Get keys matching a pattern
  async scanKeys(pattern) {
    try {
      return await keysAsync(pattern);
    } catch (error) {
      logger.error(`Error scanning Redis keys with pattern ${pattern}:`, error);
      return [];
    }
  },

  // Clear keys matching a pattern
  async clearPattern(pattern) {
    try {
      const keys = await keysAsync(pattern);
      if (keys.length === 0) {
        return 0;
      }

      const pipeline = redis.pipeline();
      keys.forEach((key) => pipeline.del(key));
      const results = await pipeline.exec();

      return results.length;
    } catch (error) {
      logger.error(`Error clearing Redis keys with pattern ${pattern}:`, error);
      return 0;
    }
  },

  // Raw Redis client for advanced operations
  redis,

  // Pipeline for batch operations
  pipeline: () => redis.pipeline(),

  // Generate cache key for SKU and source
  getCacheKey(sku, sourceId) {
    return `price:${sourceId}:${sku}`;
  }
};

// Export the enhanced Redis client
module.exports = client;
