/**
 * Custom error classes for AI service operations
 * Provides more specific error types for better error handling and fallback mechanisms
 */

const logger = require("./logger");
const ApiError = require("./ApiError");

/**
 * Base class for AI service errors
 */
class AiServiceError extends ApiError {
  constructor(statusCode, message, details = null) {
    super(statusCode, message, details);
    this.name = "AiServiceError";
    this.category = "ai-service";
  }
}

/**
 * Error thrown when AI service is unavailable
 */
class AiServiceUnavailableError extends AiServiceError {
  constructor(
    message = "AI service is temporarily unavailable",
    details = null
  ) {
    super(503, message, details);
    this.name = "AiServiceUnavailableError";
    this.retryable = true;
  }
}

/**
 * Error thrown when AI service rate limit is exceeded
 */
class AiRateLimitError extends AiServiceError {
  constructor(message = "AI service rate limit exceeded", details = null) {
    super(429, message, details);
    this.name = "AiRateLimitError";
    this.retryable = true;
    this.retryAfter = details?.retryAfter || 60; // Default retry after 60 seconds
  }
}

/**
 * Error thrown when AI service returns invalid or unexpected response
 */
class AiInvalidResponseError extends AiServiceError {
  constructor(message = "Invalid or unexpected AI response", details = null) {
    super(502, message, details); // Use 502 Bad Gateway for upstream invalid response
    this.name = "AiInvalidResponseError";
    this.retryable = true; // Allow retry for potentially transient stream/JSON parsing issues
  }
}

/**
 * Error thrown when AI service request is invalid
 */
class AiInvalidRequestError extends AiServiceError {
  constructor(message = "Invalid AI request parameters", details = null) {
    super(400, message, details);
    this.name = "AiInvalidRequestError";
    this.retryable = false;
  }
}

/**
 * Error thrown when AI service context length is exceeded
 */
class AiContextLengthError extends AiServiceError {
  constructor(message = "AI context length exceeded", details = null) {
    super(413, message, details);
    this.name = "AiContextLengthError";
    this.retryable = false;
  }
}

/**
 * Error thrown when AI service times out
 */
class AiTimeoutError extends AiServiceError {
  constructor(message = "AI service request timed out", details = null) {
    super(504, message, details);
    this.name = "AiTimeoutError";
    this.retryable = true;
  }
}

/**
 * Error thrown when AI service returns content that violates content policy
 */
class AiContentPolicyError extends AiServiceError {
  constructor(message = "AI content policy violation", details = null) {
    super(422, message, details);
    this.name = "AiContentPolicyError";
    this.retryable = false;
  }
}

/**
 * Helper function to determine if an error is retryable
 * @param {Error} error - The error to check
 * @returns {boolean} - Whether the error is retryable
 */
const isRetryableError = (error) => {
  if (error instanceof AiServiceError) {
    return error.retryable;
  }

  // Check for network errors or other transient errors
  if (
    error.code === "ECONNRESET" ||
    error.code === "ETIMEDOUT" ||
    error.code === "ECONNREFUSED" ||
    error.message.includes("network") ||
    error.message.toLowerCase().includes("timeout")
  ) {
    return true;
  }

  return false;
};

/**
 * Helper function to get retry delay based on error type
 * @param {Error} error - The error to check
 * @param {number} attempt - The current retry attempt
 * @returns {number} - Delay in milliseconds before next retry
 */
const getRetryDelay = (error, attempt) => {
  logger.debug(
    `[AI Errors] getRetryDelay called. Attempt: ${attempt}. Error object: ${JSON.stringify(
      error,
      Object.getOwnPropertyNames(error)
    )}`
  );
  logger.debug(`[AI Errors] getRetryDelay: error.message: ${error?.message}`);
  logger.debug(
    `[AI Errors] getRetryDelay: error.cause: ${JSON.stringify(error?.cause)}`
  );
  logger.debug(
    `[AI Errors] getRetryDelay: error.details: ${JSON.stringify(
      error?.details
    )}`
  );
  const baseDelay = 300;
  const maxDelay = 30000; // 30 seconds

  if (error instanceof AiRateLimitError && error.retryAfter) {
    // This handles cases where mapGeminiError has already processed and set retryAfter
    return error.retryAfter * 1000; // Convert to milliseconds
  }

  // Check the raw error for Google's retryDelay suggestion
  // This is for withRetry's initial attempts on the same model before mapGeminiError fully processes it.
  try {
    // Google's error message often contains the details as a string, or it might be in error.cause or error.details
    let searchStringForDelay = "";
    if (typeof error?.message === "string") {
      searchStringForDelay = error.message;
    }
    let match = searchStringForDelay.match(/"retryDelay":"(\d+)s"/);

    if (!match && error?.cause) {
      try {
        searchStringForDelay =
          typeof error.cause === "string"
            ? error.cause
            : JSON.stringify(error.cause);
        match = searchStringForDelay.match(/"retryDelay":"(\d+)s"/);
      } catch (e) {
        /* ignore stringify errors for cause */
      }
    }

    if (!match) {
      // As a last resort, stringify the whole error object
      try {
        searchStringForDelay = JSON.stringify(
          error,
          Object.getOwnPropertyNames(error)
        );
        match = searchStringForDelay.match(/"retryDelay":"(\d+)s"/);
      } catch (e) {
        /* ignore stringify errors for the whole object */
      }
    }

    // const originalErrorDetailsString = error.message || JSON.stringify(error.cause || error.details || {}); // Old line
    // const match = originalErrorDetailsString.match(/"retryDelay":"(\d+)s"/); // Old line, replaced by the logic above that sets 'match'
    if (match && match[1]) {
      const googleRetryDelaySeconds = parseInt(match[1], 10);
      // Add a small jitter to avoid thundering herd, but primarily respect Google's suggestion
      const delayWithJitter =
        googleRetryDelaySeconds * 1000 + Math.floor(Math.random() * 1000);
      logger.debug(
        `[AI Errors] getRetryDelay: Using Google's suggested retryDelay from raw error: ${googleRetryDelaySeconds}s (parsed), with jitter: ${delayWithJitter}ms`
      );
      return Math.min(maxDelay, delayWithJitter);
    }
  } catch (e) {
    logger.warn(
      `[AI Errors] getRetryDelay: Error parsing Google's retryDelay from raw error: ${e.message}`
    );
    // Fall through to exponential backoff if parsing fails
  }

  // Exponential backoff with jitter
  return Math.min(
    maxDelay,
    baseDelay * Math.pow(2, attempt) * (0.5 + Math.random() * 0.5)
  );
};

module.exports = {
  AiServiceError,
  AiServiceUnavailableError,
  AiRateLimitError,
  AiInvalidResponseError,
  AiInvalidRequestError,
  AiContextLengthError,
  AiTimeoutError,
  AiContentPolicyError,
  isRetryableError,
  getRetryDelay,
};
