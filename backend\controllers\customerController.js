const Customer = require("../models/Customer");

// @desc    Create a new customer
// @route   POST /api/customers
// @access  Private
const createCustomer = async (req, res) => {
  try {
    const {
      businessName,
      contactPerson,
      email,
      phone,
      alternatePhone,
      address,
      notes,
      tags,
      source,
    } = req.body;

    const customer = await Customer.create({
      businessName,
      contactPerson,
      email,
      phone,
      alternatePhone,
      address,
      notes,
      tags,
      source,
    });

    if (customer) {
      res.status(201).json(customer);
    } else {
      res.status(400).json({ message: "Invalid customer data" });
    }
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: "Server error" });
  }
};

// @desc    Get all customers
// @route   GET /api/customers
// @access  Private
const getCustomers = async (req, res) => {
  try {
    // Support for pagination
    // Prioritize 'limit' for dropdowns, fallback to 'pageSize' or default
    const pageSize =
      Number(req.query.limit) || Number(req.query.pageSize) || 10;
    const page = Number(req.query.page) || 1;

    // Support for search
    const keyword = req.query.keyword
      ? {
          $or: [
            {
              "contactPerson.firstName": {
                $regex: req.query.keyword,
                $options: "i",
              },
            },
            {
              "contactPerson.lastName": {
                $regex: req.query.keyword,
                $options: "i",
              },
            },
            { businessName: { $regex: req.query.keyword, $options: "i" } },
            { email: { $regex: req.query.keyword, $options: "i" } },
            { phone: { $regex: req.query.keyword, $options: "i" } },
          ],
        }
      : {};

    // Support for tag filtering
    let tagFilter = {};
    if (req.query.tags) {
      const tagArray = req.query.tags.split(",").map((tag) => tag.trim());
      tagFilter = { tags: { $in: tagArray } };
    }

    const count = await Customer.countDocuments({ ...keyword, ...tagFilter });
    const customers = await Customer.find({ ...keyword, ...tagFilter })
      .limit(pageSize) // Now uses the correct limit from query
      .skip(pageSize * (page - 1))
      .sort({ createdAt: -1 });

    // Return customers under the 'customers' key to match frontend expectation
    res.json({
      customers: customers, // Changed 'data' key to 'customers'
      page,
      totalPages: Math.ceil(count / pageSize), // Changed 'pages' key to 'totalPages' to match slice
      total: count,
      success: true, // Explicitly add success flag often expected by frontend handlers
    });
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: "Server error" });
  }
};

// @desc    Get customer by ID
// @route   GET /api/customers/:id
// @access  Private
const getCustomerById = async (req, res) => {
  try {
    const customer = await Customer.findById(req.params.id);

    if (customer) {
      res.json(customer);
    } else {
      res.status(404).json({ message: "Customer not found" });
    }
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: "Server error" });
  }
};

// @desc    Get customer with job history
// @route   GET /api/customers/:id/jobs
// @access  Private
const getCustomerWithJobs = async (req, res) => {
  try {
    const customer = await Customer.findById(req.params.id);

    if (customer) {
      const customerWithJobs = await customer.getWithJobHistory();
      res.json(customerWithJobs);
    } else {
      res.status(404).json({ message: "Customer not found" });
    }
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: "Server error" });
  }
};

// @desc    Get customer with invoice history
// @route   GET /api/customers/:id/invoices
// @access  Private
const getCustomerWithInvoices = async (req, res) => {
  try {
    const customer = await Customer.findById(req.params.id);

    if (customer) {
      const customerWithInvoices = await customer.getWithInvoiceHistory();
      res.json(customerWithInvoices);
    } else {
      res.status(404).json({ message: "Customer not found" });
    }
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: "Server error" });
  }
};

// @desc    Update customer
// @route   PUT /api/customers/:id
// @access  Private
const updateCustomer = async (req, res) => {
  try {
    const customer = await Customer.findById(req.params.id);

    if (customer) {
      customer.businessName = req.body.businessName || customer.businessName;
      customer.contactPerson = req.body.contactPerson || customer.contactPerson;
      customer.email = req.body.email || customer.email;
      customer.phone = req.body.phone || customer.phone;
      customer.alternatePhone =
        req.body.alternatePhone || customer.alternatePhone;
      customer.address = req.body.address || customer.address;
      customer.notes = req.body.notes || customer.notes;
      customer.tags = req.body.tags || customer.tags;
      customer.source = req.body.source || customer.source;

      const updatedCustomer = await customer.save();
      res.json(updatedCustomer);
    } else {
      res.status(404).json({ message: "Customer not found" });
    }
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: "Server error" });
  }
};

// @desc    Delete customer
// @route   DELETE /api/customers/:id
// @access  Private
const deleteCustomer = async (req, res) => {
  try {
    const customer = await Customer.findById(req.params.id);

    if (customer) {
      await customer.remove();
      res.json({ message: "Customer removed" });
    } else {
      res.status(404).json({ message: "Customer not found" });
    }
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: "Server error" });
  }
};

module.exports = {
  createCustomer,
  getCustomers,
  getCustomerById,
  getCustomerWithJobs,
  getCustomerWithInvoices,
  updateCustomer,
  deleteCustomer,
};
