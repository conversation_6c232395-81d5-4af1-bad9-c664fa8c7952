{"mcpServers": {"github.com/executeautomation/mcp-playwright": {"command": "node", "args": ["C:/Users/<USER>/Documents/Cline/MCP/playwright-mcp-server/start-playwright-mcp.js"], "disabled": false, "autoApprove": ["playwright_navigate", "playwright_screenshot", "playwright_click", "playwright_iframe_click", "playwright_fill", "playwright_select", "playwright_hover", "playwright_evaluate", "playwright_get", "playwright_post", "playwright_put", "playwright_patch", "playwright_delete"], "alwaysAllow": ["playwright_navigate", "playwright_screenshot", "playwright_click", "playwright_iframe_click", "playwright_fill", "playwright_select", "playwright_hover", "playwright_evaluate", "playwright_get", "playwright_post", "playwright_put", "playwright_patch", "playwright_delete"]}, "github.com/mendableai/firecrawl-mcp-server": {"command": "cmd", "args": ["/c", "set FIRECRAWL_API_KEY=fc-5cbd6d31c6b44b3eabe3cbc28823ee2d && npx -y firecrawl-mcp"], "disabled": false, "autoApprove": ["firecrawl_search", "firecrawl_scrape", "firecrawl_map", "firecrawl_crawl", "firecrawl_extract", "firecrawl_batch_scrape", "firecrawl_deep_research"], "alwaysAllow": ["firecrawl_scrape", "firecrawl_map", "firecrawl_crawl", "firecrawl_batch_scrape", "firecrawl_check_batch_status", "firecrawl_check_crawl_status", "firecrawl_search", "firecrawl_extract", "firecrawl_deep_research"]}, "github.com/firebase/genkit/tree/HEAD/js/plugins/mcp": {"command": "node", "args": ["C:/Users/<USER>/Documents/Cline/MCP/genkit-mcp/index.js"], "disabled": false, "autoApprove": ["add", "transform_text"], "alwaysAllow": ["add", "transform_text"]}, "github.com/modelcontextprotocol/servers/tree/main/src/memory": {"command": "node", "args": ["C:/nvm4w/nodejs/node_modules/@modelcontextprotocol/server-memory/dist/index.js"], "env": {"MEMORY_FILE_PATH": "C:/Users/<USER>/Documents/Cline/MCP/memory-server/memory.json"}, "disabled": false, "autoApprove": ["create_entities", "create_relations", "add_observations", "delete_entities", "delete_observations", "delete_relations", "read_graph", "search_nodes", "open_nodes"], "alwaysAllow": ["create_entities", "create_relations", "add_observations", "delete_entities", "delete_observations", "delete_relations", "read_graph", "search_nodes", "open_nodes"]}, "github.com/modelcontextprotocol/servers/tree/main/src/filesystem": {"command": "node", "args": ["C:/nvm4w/nodejs/node_modules/@modelcontextprotocol/server-filesystem/dist/index.js", "C:/Users/<USER>/Desktop", "C:/Users/<USER>/Documents/Cline/MCP/filesystem-server"], "disabled": false, "autoApprove": ["read_file", "read_multiple_files", "write_file", "edit_file", "create_directory", "list_directory", "directory_tree", "move_file", "search_files", "get_file_info", "list_allowed_directories", "list_code_definition_names"], "alwaysAllow": ["read_file", "read_multiple_files", "write_file", "edit_file", "create_directory", "list_directory", "directory_tree", "move_file", "search_files", "get_file_info", "list_allowed_directories"]}, "github.com/modelcontextprotocol/servers/tree/main/src/sequentialthinking": {"command": "node", "args": ["C:/Users/<USER>/Documents/Cline/MCP/sequential-thinking/node_modules/@modelcontextprotocol/server-sequential-thinking/dist/index.js"], "disabled": false, "autoApprove": ["sequentialthinking"], "alwaysAllow": ["sequentialthinking", "sequentialthinking</tool_analysis>\n<arguments>\n{\n  \"thought\": \"I have analyzed `frontend/src/pages/CreateQuote.js` and confirmed that the frontend collects text input and selected images, preparing them in a `FormData` object to send to the backend for AI generation. The frontend populates the form based on the `draftQuote` state managed by the Redux slice. The next step is to examine `frontend/src/slices/quoteSlice.js` to understand how the AI response from the backend is processed and updates the `draftQuote` state. This will help determine if the issue with missing fields is due to incorrect handling of the AI response structure in the frontend Redux reducer.\",\n  \"nextThoughtNeeded\": true,\n  \"thoughtNumber\": 3,\n  \"totalThoughts\": 7\n}\n</arguments>\n</use_mcp_tool>"]}, "github.com/Garoth/wolframalpha-llm-mcp": {"command": "node", "args": ["E:/Business/OneDrive/Documents/Cline/MCP/wolframalpha-llm-mcp/build/index.js"], "env": {"WOLFRAM_LLM_APP_ID": "VU7RYR-K882K6HHJG"}, "disabled": false, "autoApprove": ["ask_llm", "get_simple_answer", "validate_key"], "alwaysAllow": ["ask_llm", "get_simple_answer", "validate_key"]}, "debug-assistant": {"command": "node", "args": ["C:/Users/<USER>/Documents/Cline/MCP/debug-assistant/build/index.js"], "disabled": false, "autoApprove": ["check_code_quality", "get_performance_metrics", "detect_memory_leaks", "profile_runtime_performance", "check_dependency_compatibility", "validate_configuration", "scan_security_vulnerabilities"], "alwaysAllow": ["check_code_quality", "get_performance_metrics", "detect_memory_leaks", "profile_runtime_performance", "check_dependency_compatibility", "validate_configuration", "scan_security_vulnerabilities"]}, "axios-diagnostic": {"command": "node", "args": ["C:/Users/<USER>/Documents/Cline/MCP/axios-diagnostic-server/build/index.js"], "disabled": false, "autoApprove": ["check_axios_installation", "verify_types", "get_documentation"], "alwaysAllow": ["check_axios_installation", "verify_types", "get_documentation"]}, "package-research": {"command": "node", "args": ["C:/Users/<USER>/Documents/Cline/MCP/package-research/build/index.js"], "disabled": false, "autoApprove": ["check_react_compatibility", "analyze_peer_dependencies", "find_compatible_version"], "alwaysAllow": ["check_react_compatibility", "analyze_peer_dependencies", "find_compatible_version"]}, "github.com/modelcontextprotocol/servers/tree/main/src/puppeteer": {"command": "docker", "args": ["run", "-i", "--rm", "--init", "-e", "DOCKER_CONTAINER=true", "mcp/puppeteer"], "disabled": false, "autoApprove": ["puppeteer_navigate", "puppeteer_screenshot", "puppeteer_click", "puppeteer_fill", "puppeteer_select", "puppeteer_hover", "puppeteer_evaluate"], "alwaysAllow": ["puppeteer_navigate", "puppeteer_screenshot", "puppeteer_click", "puppeteer_fill", "puppeteer_select", "puppeteer_hover", "puppeteer_evaluate"]}, "github.com/zcaceres/markdownify-mcp": {"command": "node", "args": ["E:\\Business\\OneDrive\\Documents\\Cline\\MCP\\markdownify-mcp\\dist\\index.js"], "env": {"PYTHONPATH": "E:\\Business\\OneDrive\\Documents\\Cline\\MCP\\markdownify-mcp\\.venv\\Lib\\site-packages"}, "disabled": false, "autoApprove": ["webpage-to-markdown", "audio-to-markdown", "bing-search-to-markdown", "docx-to-markdown", "get-markdown-file", "image-to-markdown", "pdf-to-markdown", "pptx-to-markdown", "xlsx-to-markdown", "youtube-to-markdown"], "alwaysAllow": ["audio-to-markdown", "bing-search-to-markdown", "docx-to-markdown", "get-markdown-file", "image-to-markdown", "pdf-to-markdown", "pptx-to-markdown", "webpage-to-markdown", "xlsx-to-markdown", "youtube-to-markdown"]}}}