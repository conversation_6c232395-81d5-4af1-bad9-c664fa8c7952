// Configuration for NEC Lookup Service
// NOTE: These URLs require verification and might change. Maintaining them here
// allows for easier updates without modifying the service code directly.

const NEC_LOOKUP_URLS = {
  NEC_conductor_size:
    "https://www.cerrowire.com/resources/tables-calculators/ampacity-charts/", // Target: Table 310.16 data
  NEC_conduit_size: "https://www.southwire.com/calculator-conduit", // Target: Interactive Calculator
  NEC_grounding_electrode_conductor_size:
    "https://www.ecmweb.com/national-electrical-code/article/20898189/grounding-electrode-conductor-sizing", // Target: Article with Table 250.66
  NEC_bonding_jumper_size:
    "https://www.ecmweb.com/national-electrical-code/article/20897979/bonding-requirements-for-electrical-systems", // Target: Article with relevant tables (e.g., 250.102(C)(1))
  NEC_overcurrent_protection_size:
    "https://www.ecmweb.com/national-electrical-code/article/20898009/branch-circuit-feeder-and-service-calculations-part-1", // Target: General guidelines/examples (complex lookup)
};

// Add other lookup-related configurations here if needed in the future.

module.exports = {
  NEC_LOOKUP_URLS,
};
