/**
 * React Error Boundary component to catch and handle JavaScript errors
 * Prevents the entire application from crashing due to unhandled errors
 */
import React from "react";
import {
  Box,
  Typography,
  Button,
  Alert,
  Container,
  Paper,
} from "@mui/material";
import {
  Refresh as RefreshIcon,
  BugReport as BugReportIcon,
} from "@mui/icons-material";

class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
    };
  }

  static getDerivedStateFromError(error) {
    // Update state so the next render will show the fallback UI
    return { hasError: true };
  }

  componentDidCatch(error, errorInfo) {
    // Log error details
    console.error("ErrorBoundary caught an error:", error, errorInfo);

    this.setState({
      error: error,
      errorInfo: errorInfo,
    });

    // Report error to monitoring service if available
    if (window.reportError) {
      window.reportError(error, errorInfo);
    }
  }

  handleReload = () => {
    // Clear error state and reload the page
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
    });
    window.location.reload();
  };

  handleRetry = () => {
    // Clear error state to retry rendering
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
    });
  };

  render() {
    if (this.state.hasError) {
      // Fallback UI
      return (
        <Container maxWidth="md" sx={{ mt: 4 }}>
          <Paper elevation={3} sx={{ p: 4 }}>
            <Box
              display="flex"
              flexDirection="column"
              alignItems="center"
              textAlign="center"
            >
              <BugReportIcon color="error" sx={{ fontSize: 64, mb: 2 }} />

              <Typography variant="h4" gutterBottom color="error">
                Something went wrong
              </Typography>

              <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
                We're sorry, but something unexpected happened. Please try
                refreshing the page.
              </Typography>

              <Alert severity="error" sx={{ mb: 3, width: "100%" }}>
                <Typography variant="body2">
                  <strong>Error:</strong>{" "}
                  {this.state.error?.message || "Unknown error"}
                </Typography>
              </Alert>

              <Box display="flex" gap={2}>
                <Button
                  variant="contained"
                  color="primary"
                  startIcon={<RefreshIcon />}
                  onClick={this.handleReload}
                >
                  Reload Page
                </Button>

                <Button
                  variant="outlined"
                  color="primary"
                  onClick={this.handleRetry}
                >
                  Try Again
                </Button>
              </Box>

              {process.env.NODE_ENV === "development" &&
                this.state.errorInfo && (
                  <Box sx={{ mt: 3, width: "100%" }}>
                    <Typography variant="h6" gutterBottom>
                      Error Details (Development Mode)
                    </Typography>
                    <Alert severity="warning" sx={{ textAlign: "left" }}>
                      <pre style={{ fontSize: "12px", overflow: "auto" }}>
                        {this.state.error?.stack}
                        {this.state.errorInfo.componentStack}
                      </pre>
                    </Alert>
                  </Box>
                )}
            </Box>
          </Paper>
        </Container>
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary;
