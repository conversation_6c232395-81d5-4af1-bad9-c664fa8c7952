const mongoose = require("mongoose");
const MaterialSource = require("../models/MaterialSource");
const MaterialPriceHistory = require("../models/MaterialPriceHistory"); // Import the missing model
const connectDB = require("../config/database");
require("dotenv").config({ path: "../.env" }); // Adjust path as needed

const addPlattSource = async () => {
  // Renamed function
  await connectDB();

  try {
    // Find existing or create new Platt source
    let source = await MaterialSource.findOne({ type: "PLATT" });

    if (source) {
      console.log("Platt source already exists. Updating...");
      source.name = "Platt Electric Supply"; // Ensure name is correct
      source.baseUrl = "https://www.platt.com"; // Ensure URL is correct
      source.enabled = true; // Ensure it's enabled
      // Update other fields if necessary, e.g., rate limits
      source.rateLimit = {
        requestsPerMinute: 20, // Example: Adjust based on <PERSON><PERSON>'s limits/tolerance
        cooldownPeriod: 500, // Example: 0.5 second cooldown
      };
      // Clear potentially outdated scrapeConfig from Grainger
      source.scrapeConfig = {
        selectors: new Map(), // Reset selectors
        allowedDomains: ["www.platt.com"],
        excludePatterns: [],
        sessionDuration: 3600,
        userAgents: [],
      };
      await source.save();
      console.log("Platt source updated.");
    } else {
      source = new MaterialSource({
        name: "Platt Electric Supply", // Use Platt name
        type: "PLATT", // Use PLATT type
        baseUrl: "https://www.platt.com", // Platt URL
        enabled: true, // Enable by default
        // Add credentials if needed (ensure they are securely managed, e.g., via env vars)
        // credentials: {
        //   username: process.env.PLATT_USERNAME, // Use PLATT env vars if needed
        //   password: process.env.PLATT_PASSWORD,
        // },
        rateLimit: {
          requestsPerMinute: 20, // Example: Adjust based on Platt's limits/tolerance
          cooldownPeriod: 500, // Example: 0.5 second cooldown
        },
        // Configure proxy settings if required
        // proxySettings: {
        //   useProxy: true,
        //   proxyUrl: process.env.PROXY_URL,
        // },
        // Configure specific scrape settings if needed
        scrapeConfig: {
          selectors: new Map(), // Start with empty selectors, to be filled manually
          allowedDomains: ["www.platt.com"], // Set allowed domain
          // userAgents: [ /* Specific user agents if needed */ ]
        },
      });

      await source.save();
      console.log("Platt source added successfully.");
    }

    // Optionally, remove the old Grainger source if it exists
    const oldGraingerSource = await MaterialSource.findOne({
      type: "GRAINGER",
    });
    if (oldGraingerSource) {
      console.log("Found old Grainger source, attempting to remove...");
      // Check if it has price history before removing
      const priceHistoryCount = await MaterialPriceHistory.countDocuments({
        source: oldGraingerSource._id,
      }); // Use the imported model directly
      if (priceHistoryCount > 0) {
        console.warn(
          `Cannot remove Grainger source (${oldGraingerSource._id}) as it has ${priceHistoryCount} price history records. Please migrate or delete history manually.`
        );
      } else {
        await oldGraingerSource.remove();
        console.log("Old Grainger source removed successfully.");
      }
    }
  } catch (error) {
    console.error(
      "Error adding/updating Platt source or removing Grainger source:",
      error
    );
  } finally {
    mongoose.connection.close();
  }
};

addPlattSource();
