import React from "react";
import {
  <PERSON>pography,
  Grid,
  Card,
  CardMedia,
  CardContent,
  CardActions,
  Button,
  Chip,
  Box,
  Paper,
  Divider,
  CircularProgress,
} from "@mui/material";
import {
  Check as SelectIcon,
  OpenInNew as LinkIcon,
} from "@mui/icons-material";
import { formatCurrency } from "../../utils/formatters";

/**
 * Component to display material options from AI/MCP scraping for user selection
 * Used when an item has lookup_status "pending_user_selection"
 */
const MaterialOptionsDisplay = ({
  options = [],
  onSelect,
  isLoading = false,
  itemId,
  itemName,
}) => {
  if (isLoading) {
    return (
      <Paper elevation={2} sx={{ p: 3, mt: 2, mb: 2, textAlign: "center" }}>
        <CircularProgress size={30} sx={{ mb: 2 }} />
        <Typography>Searching for material options...</Typography>
      </Paper>
    );
  }

  if (!options || options.length === 0) {
    return (
      <Paper elevation={2} sx={{ p: 3, mt: 2, mb: 2, textAlign: "center" }}>
        <Typography color="text.secondary">
          No material options found.
        </Typography>
      </Paper>
    );
  }

  return (
    <Paper elevation={2} sx={{ p: 3, mt: 2, mb: 2 }}>
      <Typography variant="h6" gutterBottom>
        {itemName
          ? `Select material option for "${itemName}"`
          : "Select a material option"}
      </Typography>
      <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
        Please select the best match from the options below.
      </Typography>

      <Divider sx={{ mb: 3 }} />

      <Grid container spacing={2}>
        {options.map((option, index) => {
          // Parse the price if it's a string with currency symbol
          let numericPrice = option.numerical_price;
          if (!numericPrice && typeof option.price === "string") {
            const priceMatch = option.price.match(/[\d,.]+/);
            if (priceMatch) {
              numericPrice = parseFloat(priceMatch[0].replace(/,/g, ""));
            }
          }

          return (
            <Grid item xs={12} sm={6} md={4} key={`${itemId}-option-${index}`}>
              <Card
                sx={{
                  height: "100%",
                  display: "flex",
                  flexDirection: "column",
                  transition: "transform 0.2s",
                  "&:hover": {
                    transform: "scale(1.02)",
                    boxShadow: 3,
                  },
                }}
              >
                {option.imageUrl ? (
                  <CardMedia
                    component="img"
                    height="140"
                    image={option.imageUrl}
                    alt={option.name}
                    sx={{ objectFit: "contain", bgcolor: "#f5f5f5", p: 1 }}
                  />
                ) : (
                  <Box
                    sx={{
                      height: 140,
                      bgcolor: "#f5f5f5",
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "center",
                    }}
                  >
                    <Typography color="text.secondary">No image</Typography>
                  </Box>
                )}
                <CardContent sx={{ flexGrow: 1 }}>
                  <Typography variant="subtitle1" component="div" gutterBottom>
                    {option.name}
                  </Typography>

                  {option.sku && (
                    <Typography
                      variant="caption"
                      color="text.secondary"
                      display="block"
                    >
                      SKU: {option.sku}
                    </Typography>
                  )}

                  {option.source && (
                    <Chip
                      label={option.source}
                      size="small"
                      sx={{ mt: 1, mb: 1 }}
                      color={
                        option.source.toLowerCase().includes("home depot")
                          ? "warning"
                          : "default"
                      }
                    />
                  )}

                  <Typography
                    variant="body2"
                    color="text.secondary"
                    sx={{
                      mt: 1,
                      height: "4.5em",
                      overflow: "hidden",
                      textOverflow: "ellipsis",
                      display: "-webkit-box",
                      WebkitLineClamp: 3,
                      WebkitBoxOrient: "vertical",
                    }}
                  >
                    {option.description || "No description available"}
                  </Typography>

                  <Typography variant="h6" color="primary" sx={{ mt: 2 }}>
                    {numericPrice
                      ? formatCurrency(numericPrice)
                      : option.price || "Price unavailable"}
                  </Typography>

                  {option.available === false && (
                    <Chip
                      label="Out of stock"
                      color="error"
                      size="small"
                      sx={{ mt: 1 }}
                    />
                  )}
                </CardContent>
                <CardActions sx={{ justifyContent: "space-between", p: 2 }}>
                  <Button
                    variant="contained"
                    startIcon={<SelectIcon />}
                    onClick={() => onSelect(index)}
                    color="primary"
                    fullWidth
                  >
                    Select
                  </Button>

                  {option.productUrl && (
                    <Button
                      size="small"
                      startIcon={<LinkIcon />}
                      onClick={() => window.open(option.productUrl, "_blank")}
                      sx={{ ml: 1 }}
                    >
                      View
                    </Button>
                  )}
                </CardActions>
              </Card>
            </Grid>
          );
        })}
      </Grid>
    </Paper>
  );
};

export default MaterialOptionsDisplay;
