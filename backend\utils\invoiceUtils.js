const Invoice = require("../models/Invoice");

/**
 * Generate a unique invoice number with prefix
 * Format: INV-YYYYMMDD-XXXX
 * Example: INV-20250313-0001
 */
async function generateInvoiceNumber() {
  const today = new Date();
  const datePart = today.toISOString().slice(0, 10).replace(/-/g, "");

  // Find the last invoice number for today
  const latestInvoice = await Invoice.findOne({
    number: new RegExp(`^INV-${datePart}`),
  }).sort({ number: -1 });

  let sequence = "0001";
  if (latestInvoice) {
    const lastSequence = parseInt(latestInvoice.number.slice(-4));
    sequence = (lastSequence + 1).toString().padStart(4, "0");
  }

  return `INV-${datePart}-${sequence}`;
}

/**
 * Calculate due date based on payment terms and customer history
 */
function calculateDueDate(customer, amount) {
  let baseTerms = 30; // Default 30 days

  // Adjust based on customer's payment history
  if (customer.paymentHistory) {
    const avgDelay = calculateAveragePaymentDelay(customer.paymentHistory);
    if (avgDelay < 0) {
      // Pays early
      baseTerms = Math.max(15, baseTerms + avgDelay);
    } else if (avgDelay > 0) {
      // Pays late
      baseTerms = Math.min(45, baseTerms - avgDelay);
    }
  }

  // Adjust for large amounts
  if (amount > 10000) {
    baseTerms += 15; // Give more time for large invoices
  }

  const dueDate = new Date();
  dueDate.setDate(dueDate.getDate() + baseTerms);
  return dueDate;
}

/**
 * Calculate tax for line items based on rules
 */
function calculateTax(items, customerTaxExempt = false) {
  if (customerTaxExempt) return 0;

  return items.reduce((total, item) => {
    const taxRate = getTaxRate(item.type);
    return total + item.total * taxRate;
  }, 0);
}

/**
 * Calculate discounts based on rules and customer status
 */
function calculateDiscount(total, customer) {
  let discountRate = 0;

  // Volume discount
  if (total >= 10000) {
    discountRate += 0.05;
  }

  // Loyalty discount
  if (customer.relationshipDuration >= 365) {
    // 1 year
    discountRate += 0.02;
  }

  // Early payment discount
  if (customer.averagePaymentDays < 15) {
    discountRate += 0.02;
  }

  return total * discountRate;
}

/**
 * Format currency amounts
 */
function formatCurrency(amount, currency = "USD") {
  return new Intl.NumberFormat("en-US", {
    style: "currency",
    currency: currency,
  }).format(amount);
}

/**
 * Calculate late fees if applicable
 */
function calculateLateFees(invoice) {
  if (!invoice.isOverdue) return 0;

  const daysLate = invoice.daysOverdue;
  const lateFeeRate = 0.1; // 10% annual rate
  const dailyRate = lateFeeRate / 365;

  return invoice.balance * dailyRate * daysLate;
}

/**
 * Validate payment amount
 */
function validatePayment(invoice, amount) {
  if (amount <= 0) {
    throw new Error("Payment amount must be greater than 0");
  }

  if (amount > invoice.balance) {
    throw new Error("Payment amount cannot exceed invoice balance");
  }

  return true;
}

/**
 * Generate payment receipt text
 */
function generateReceiptText(payment, invoice) {
  return `Receipt for Payment
Date: ${payment.date.toLocaleDateString()}
Invoice: ${invoice.number}
Amount: ${formatCurrency(payment.amount)}
Method: ${payment.method}
Transaction ID: ${payment.transactionId || "N/A"}
Balance Remaining: ${formatCurrency(invoice.balance)}

Thank you for your payment!
`;
}

/**
 * Get display status text
 */
function getStatusDisplay(status) {
  const statusMap = {
    DRAFT: "Draft",
    SENT: "Sent",
    VIEWED: "Viewed by Customer",
    PARTIAL: "Partially Paid",
    PAID: "Paid in Full",
    OVERDUE: "Overdue",
    VOID: "Void",
  };
  return statusMap[status] || status;
}

/**
 * Get tax rate based on item type
 */
function getTaxRate(itemType) {
  const taxRates = {
    SERVICE: 0.08,
    PARTS: 0.06,
    LABOR: 0.08,
    MATERIALS: 0.06,
  };
  return taxRates[itemType] || 0;
}

/**
 * Calculate average payment delay
 */
function calculateAveragePaymentDelay(paymentHistory) {
  if (!paymentHistory || paymentHistory.length === 0) return 0;

  const delays = paymentHistory.map((payment) => {
    const dueDate = new Date(payment.dueDate);
    const paidDate = new Date(payment.paidDate);
    return Math.floor((paidDate - dueDate) / (1000 * 60 * 60 * 24));
  });

  return delays.reduce((sum, delay) => sum + delay, 0) / delays.length;
}

module.exports = {
  generateInvoiceNumber,
  calculateDueDate,
  calculateTax,
  calculateDiscount,
  formatCurrency,
  calculateLateFees,
  validatePayment,
  generateReceiptText,
  getStatusDisplay,
  getTaxRate,
};
