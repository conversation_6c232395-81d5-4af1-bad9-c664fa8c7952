const mongoose = require("mongoose");
const bcrypt = require("bcryptjs");

const userSchema = new mongoose.Schema(
  {
    firstName: {
      type: String,
      required: true,
      trim: true,
    },
    lastName: {
      type: String,
      required: true,
      trim: true,
    },
    email: {
      type: String,
      required: true,
      unique: true,
      trim: true,
      lowercase: true,
    },
    password: {
      type: String,
      required: true,
      minlength: 6,
    },
    role: {
      type: String,
      enum: ["Administrators", "Managers", "Supervisors", "Technicians"],
      default: "Administrators",
    },
    permissions: {
      type: [String],
      default: [],
    },
    phone: {
      type: String,
      trim: true,
    },
    address: {
      street: String,
      city: String,
      state: String,
      zipCode: String,
      country: String,
    },
    profileImage: String,
    skills: [String],
    hourlyRate: Number,
    lastLogin: Date,
    isActive: {
      type: Boolean,
      default: true,
    },
    contractorType: {
      type: String,
      enum: [
        "HVAC",
        "Plumbing",
        "Electrical",
        "Landscaping",
        "Cleaning",
        "General Contractor",
        "Other",
      ],
      default: "Other",
    },
    company: {
      type: String,
      trim: true,
    },
    position: {
      type: String,
      trim: true,
    },
    services: [String],
  },
  {
    timestamps: true,
  }
);

// Hash password before saving
userSchema.pre("save", async function (next) {
  const user = this;
  if (user.isModified("password")) {
    user.password = await bcrypt.hash(user.password, 8);
  }
  next();
});

// Method to compare password for login
userSchema.methods.comparePassword = async function (candidatePassword) {
  return await bcrypt.compare(candidatePassword, this.password);
};

// Method to generate user profile (without sensitive info)
userSchema.methods.getProfile = function () {
  const userObject = this.toObject();
  delete userObject.password;
  return userObject;
};

// Database indexes for performance optimization
userSchema.index({ email: 1 }); // Email lookup (unique already defined)
userSchema.index({ role: 1, isActive: 1 }); // Role-based queries
userSchema.index({ "address.city": 1, "address.state": 1 }); // Location-based queries
userSchema.index({ contractorType: 1 }); // Contractor type filtering
userSchema.index({ skills: 1 }); // Skills-based search
userSchema.index({ company: 1 }); // Company-based queries
userSchema.index({ isActive: 1, lastLogin: -1 }); // Active users by last login
userSchema.index({ createdAt: -1 }); // Recent users
userSchema.index({ hourlyRate: 1 }); // Rate-based queries
userSchema.index({ services: 1 }); // Service-based filtering

const User = mongoose.model("User", userSchema);

module.exports = User;
