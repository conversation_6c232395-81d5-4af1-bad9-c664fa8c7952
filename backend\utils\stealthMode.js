/**
 * stealthMode.js
 * Utility for hiding browser automation indicators to reduce CAPTCHA challenges
 * Applies various JavaScript modifications to make automated browsers appear more like regular user browsers
 * Enhanced with additional detection evasion techniques
 */

const logger = require("./logger");
const enhancedFingerprint = require("./enhancedFingerprint");

/**
 * JavaScript to be injected into the page to hide automation indicators
 * Modifies browser properties commonly used for fingerprinting
 * @private
 */
const stealthModeScript = `
// Overwrite the navigator properties that are commonly used for fingerprinting
() => {
  try {
    // Overwrite navigator properties
    const originalNavigator = window.navigator;
    const navigatorProxy = new Proxy(originalNavigator, {
      get: function(target, prop) {
        // Make plugins and mimeTypes appear to have length 0
        if (prop === 'plugins' || prop === 'mimeTypes') {
          // Instead of empty, return a few fake plugins
          if (prop === 'plugins') {
            return {
              length: 3,
              item: (i) => ({
                name: ['Widevine Content Decryption Module', 'Chrome PDF Plugin', 'Chrome PDF Viewer'][i] || null,
                description: 'Plugin for Chrome',
                filename: ['widevinecdm.dll', 'pdf.dll', 'pdf.dll'][i] || null
              }),
              refresh: () => {},
              // Add array-like access to plugins
              0: { name: 'Widevine Content Decryption Module', description: 'Plugin for Chrome', filename: 'widevinecdm.dll' },
              1: { name: 'Chrome PDF Plugin', description: 'Plugin for Chrome', filename: 'pdf.dll' },
              2: { name: 'Chrome PDF Viewer', description: 'Plugin for Chrome', filename: 'pdf.dll' }
            };
          }
          
          if (prop === 'mimeTypes') {
            return {
              length: 3,
              item: (i) => ({
                type: ['application/pdf', 'application/x-google-chrome-pdf', 'application/x-nacl'][i] || null,
                description: 'PDF document',
                suffixes: 'pdf'
              }),
              // Add array-like access
              0: { type: 'application/pdf', description: 'PDF document', suffixes: 'pdf' },
              1: { type: 'application/x-google-chrome-pdf', description: 'PDF document', suffixes: 'pdf' },
              2: { type: 'application/x-nacl', description: 'Native Client Executable', suffixes: 'nexe' }
            };
          }
        }
        
        // Hide webdriver property
        if (prop === 'webdriver') {
          return false;
        }
        
        // Randomize hardware concurrency but keep it consistent for the page
        if (prop === 'hardwareConcurrency') {
          // Use a value between 4-8 cores that appears realistic
          return 4 + Math.floor(Math.random() * 4);  // 4-8 cores
        }
        
        // Override other commonly checked properties
        if (prop === 'userAgent' && target[prop] && target[prop].includes('HeadlessChrome')) {
          return target[prop].replace('HeadlessChrome', 'Chrome');
        }
        
        if (prop === 'platform') {
          return 'Win32';
        }
        
        if (prop === 'languages') {
          return ['en-US', 'en'];
        }
        
        if (prop === 'deviceMemory') {
          return 8; // 8GB is common for modern systems
        }
        
        // Default to the original property
        return target[prop];
      }
    });
    
    // Define the navigator descriptor
    try {
      Object.defineProperty(window, 'navigator', {
        value: navigatorProxy,
        writable: false,
        configurable: false
      });
    } catch (e) {
      // Ignore if read-only
    }
    
    // Hide automation indicators more comprehensively
    if (window.chrome) {
      // Remove automation flags
      try {
        delete window.chrome.runtime;
        
        // Replace chrome with a clean object if it has suspicious properties
        if ('app' in window.chrome || 'automation' in window.chrome) {
          const chromeProps = {};
          for (let prop in window.chrome) {
            if (prop !== 'app' && prop !== 'automation' && prop !== 'runtime') {
              chromeProps[prop] = window.chrome[prop];
            }
          }
          Object.defineProperty(window, 'chrome', {
            value: chromeProps,
            writable: true,
            configurable: true
          });
        }
      } catch (e) {
        // Ignore if can't delete
      }
    }
    
    // Modify Permissions API if available
    if (navigator.permissions) {
      const originalQuery = navigator.permissions.query;
      navigator.permissions.query = function(parameters) {
        // Report allowed for Automation control
        if (parameters.name === 'notifications' || 
            parameters.name === 'clipboard-read' || 
            parameters.name === 'clipboard-write') {
          return Promise.resolve({ state: 'granted', onchange: null });
        }
        return originalQuery.call(this, parameters);
      };
    }
    
    // Override functions commonly used to detect headless browsers
    const originalGetImageData = CanvasRenderingContext2D.prototype.getImageData;
    CanvasRenderingContext2D.prototype.getImageData = function() {
      const imageData = originalGetImageData.apply(this, arguments);
      // Add small random variations to avoid fingerprinting
      for (let i = 0; i < 20; i++) {
        const index = Math.floor(Math.random() * imageData.data.length);
        const value = imageData.data[index];
        imageData.data[index] = Math.max(0, Math.min(255, value + Math.floor(Math.random() * 3) - 1));
      }
      return imageData;
    };
    
    // Modify Canvas ToDataURL function
    const originalToDataURL = HTMLCanvasElement.prototype.toDataURL;
    HTMLCanvasElement.prototype.toDataURL = function() {
      if (this.width > 16 && this.height > 16) {
        // Add noise to canvas for larger elements (likely fingerprinting)
        const context = this.getContext('2d');
        if (context) {
          try {
            const imageData = context.getImageData(0, 0, this.width, this.height);
            const pixels = imageData.data;
            for (let i = 0; i < pixels.length; i += 4) {
              if (Math.random() < 0.005) {
                const offset = Math.floor(Math.random() * 5) - 2;
                pixels[i] = Math.max(0, Math.min(255, pixels[i] + offset));     // R
                pixels[i+1] = Math.max(0, Math.min(255, pixels[i+1] + offset)); // G
                pixels[i+2] = Math.max(0, Math.min(255, pixels[i+2] + offset)); // B
              }
            }
            context.putImageData(imageData, 0, 0);
          } catch (e) { /* Ignore errors on tainted canvas */ }
        }
      }
      return originalToDataURL.apply(this, arguments);
    };
    
    // Add subtle modifications to Date and performance timing functions
    const originalDateNow = Date.now;
    Date.now = function() {
      // Add small random offset (1-10ms) to timing to mask automation
      return originalDateNow() + (Math.random() * 10);
    };
    
    const originalPerformanceNow = window.performance.now;
    window.performance.now = function() {
      // Add small random offset to performance timing
      return originalPerformanceNow.apply(this, arguments) + (Math.random() * 5);
    };
    
    // Modify language and platform properties
    Object.defineProperties(originalNavigator, {
      language: { value: 'en-US' },
      languages: { value: ['en-US', 'en'] },
      platform: { value: 'Win32' }
    });
    
    // Prevent iframe detection
    if (window !== top) {
      try {
        Object.defineProperty(window, 'parent', {
          get: function() { return window; }
        });
      } catch (e) { /* Ignore if can't redefine */ }
    }
    
    // Modify screen dimensions to common values
    if (window.screen) {
      try {
        Object.defineProperties(window.screen, {
          width: { value: 1920 },
          height: { value: 1080 },
          availWidth: { value: 1920 },
          availHeight: { value: 1040 }, // Account for taskbar
          colorDepth: { value: 24 },
          pixelDepth: { value: 24 }
        });
      } catch (e) { /* Ignore if can't redefine */ }
    }
    
    // Return success
    return { success: true, message: 'Enhanced stealth mode applied successfully' };
  } catch (error) {
    return { success: false, message: error.toString() };
  }
}
`;

/**
 * Apply stealth mode to the browser page
 * @param {Object} mcpClient - MCP client instance
 * @param {string} pageId - MCP page ID
 * @param {Object} options - Additional options
 * @param {boolean} options.useEnhancedFingerprint - Whether to also apply enhanced fingerprint protection
 * @returns {Promise<boolean>} - Success status
 */
async function applyStealthMode(mcpClient, pageId, options = {}) {
  if (!mcpClient || !pageId) {
    logger.warn(
      `[StealthMode] Missing parameters: mcpClient=${!!mcpClient}, pageId=${pageId}`
    );
    return false;
  }

  const { useEnhancedFingerprint = true } = options;

  try {
    logger.debug(`[StealthMode] Applying stealth mode to page ${pageId}`);

    // Apply basic stealth mode script
    const result = await mcpClient.callMcpTool("evaluate", {
      script: stealthModeScript,
      pageId: pageId,
    });

    // If basic stealth mode succeeded and enhanced fingerprint is requested
    if (result && result.success && useEnhancedFingerprint) {
      logger.debug(
        `[StealthMode] Basic stealth mode applied, now applying enhanced fingerprint protection...`
      );

      // Apply enhanced fingerprint protection
      const enhancedResult = await enhancedFingerprint.applyEnhancedFingerprint(
        mcpClient,
        pageId
      );

      if (enhancedResult) {
        logger.debug(
          `[StealthMode] Successfully applied both basic stealth mode and enhanced fingerprint protection to page ${pageId}`
        );
      } else {
        logger.warn(
          `[StealthMode] Basic stealth mode succeeded but enhanced fingerprint protection failed for page ${pageId}`
        );
      }

      // Consider the operation successful if basic stealth mode worked
      return true;
    } else if (result && result.success) {
      logger.debug(
        `[StealthMode] Applied basic stealth mode to page ${pageId}: ${
          result.message || "Success"
        }`
      );
      return true;
    } else {
      logger.warn(
        `[StealthMode] Failed to apply stealth mode: ${
          result ? result.message : "Unknown error"
        }`
      );
      return false;
    }
  } catch (error) {
    logger.error(
      `[StealthMode] Error applying stealth mode: ${error.message}`,
      { stack: error.stack }
    );
    return false;
  }
}

/**
 * Apply targeted stealth script to bypass a specific anti-bot system
 * @param {Object} mcpClient - MCP client instance
 * @param {string} pageId - MCP page ID
 * @param {string} targetSystem - The anti-bot system to target ('cloudflare', 'datadome', 'imperva', etc.)
 * @returns {Promise<boolean>} - Success status
 */
async function applyTargetedStealth(mcpClient, pageId, targetSystem) {
  if (!mcpClient || !pageId || !targetSystem) {
    logger.warn(`[StealthMode] Missing parameters for targeted stealth`);
    return false;
  }

  try {
    logger.debug(
      `[StealthMode] Applying targeted stealth for ${targetSystem} on page ${pageId}`
    );

    let targetScript;

    // Specific scripts for different anti-bot systems
    if (targetSystem.toLowerCase() === "cloudflare") {
      targetScript = `
        () => {
          // Cloudflare specific bypass
          const originalSetTimeout = window.setTimeout;
          window.setTimeout = function(callback, delay) {
            // Speed up Cloudflare challenge timeouts
            if (delay > 1000 && document.querySelector('#cf-please-wait, .cf-browser-verification')) {
              delay = Math.min(delay, 1000);
            }
            return originalSetTimeout(callback, delay);
          };
          
          // Auto-click on "Verify you are human" button if present
          const verifyBtn = document.querySelector('.cf-button');
          if (verifyBtn) {
            verifyBtn.click();
            return { success: true, message: 'Clicked on Cloudflare verify button' };
          }
          
          return { success: true, message: 'Applied Cloudflare-specific stealth' };
        }
      `;
    } else if (targetSystem.toLowerCase() === "datadome") {
      targetScript = `
        () => {
          // DataDome specific bypass
          if (window.DataDome) {
            try {
              // Attempt to override DataDome properties
              window.DataDome.params = window.DataDome.params || {};
              window.DataDome.params.isHuman = true;
            } catch(e) {}
          }
          
          return { success: true, message: 'Applied DataDome-specific stealth' };
        }
      `;
    } else {
      // Generic anti-bot handling
      targetScript = `
        () => {
          // Generic anti-bot measures
          try {
            // Attempt to auto-solve common CAPTCHA widgets
            const captchaCheckbox = document.querySelector('.recaptcha-checkbox-checkmark');
            if (captchaCheckbox) {
              captchaCheckbox.click();
              return { success: true, message: 'Clicked reCAPTCHA checkbox' };
            }
            
            // Look for "I am human" buttons
            const humanButtons = Array.from(document.querySelectorAll('button, input[type="button"]')).filter(el => {
              const text = el.textContent.toLowerCase();
              return text.includes('human') || text.includes('verify') || text.includes('continue');
            });
            
            if (humanButtons.length > 0) {
              humanButtons[0].click();
              return { success: true, message: 'Clicked human verification button' };
            }
          } catch(e) {}
          
          return { success: true, message: 'Applied generic anti-bot stealth' };
        }
      `;
    }

    // Apply the targeted script
    const result = await mcpClient.callMcpTool("evaluate", {
      script: targetScript,
      pageId: pageId,
    });

    if (result && result.success) {
      logger.debug(
        `[StealthMode] Applied targeted stealth for ${targetSystem}: ${
          result.message || "Success"
        }`
      );
      return true;
    } else {
      logger.warn(
        `[StealthMode] Failed to apply targeted stealth for ${targetSystem}: ${
          result ? result.message : "Unknown error"
        }`
      );
      return false;
    }
  } catch (error) {
    logger.error(
      `[StealthMode] Error applying targeted stealth for ${targetSystem}: ${error.message}`,
      { stack: error.stack }
    );
    return false;
  }
}

module.exports = {
  applyStealthMode,
  applyTargetedStealth,
};
