/**
 * Comprehensive Streaming AI Service End-to-End Tests
 * Tests streaming quote generation, session management, persistence, and error handling
 * 
 * Based on recent fixes:
 * - Session management improvements
 * - Connection error handling
 * - Session persistence and recovery
 * - Enhanced error messaging
 */

const { test, expect } = require('@playwright/test');

test.describe('Streaming AI Service - Comprehensive Tests', () => {
  let authToken;
  let sessionId;
  
  const testUser = {
    email: '<EMAIL>',
    password: 'password123'
  };

  const testQuoteData = {
    jobType: 'electrical',
    description: 'Install new electrical panel and upgrade wiring for modern home',
    location: 'Residential home - 2 story, 2500 sq ft',
    projectOverview: 'Complete electrical upgrade including panel replacement',
    materials: [
      {
        name: '200A Electrical Panel',
        quantity: 1,
        category: 'electrical',
        specifications: {
          amperage: '200A',
          brand: 'Square D',
          type: 'main panel'
        }
      },
      {
        name: '12 AWG Copper Wire',
        quantity: 500,
        unit: 'ft',
        category: 'electrical'
      }
    ],
    customerInfo: {
      name: 'Test Customer',
      address: '123 Test St, Test City, TS 12345',
      phone: '************'
    }
  };

  test.beforeAll(async ({ request }) => {
    // Login to get auth token
    console.log('Authenticating test user...');
    try {
      const response = await request.post('http://localhost:5000/api/users/login', {
        data: testUser
      });
      
      expect(response.status()).toBe(200);
      const responseData = await response.json();
      authToken = responseData.token;
      console.log('Authentication successful');
    } catch (error) {
      console.error('Authentication failed:', error);
      throw error;
    }
  });

  test.describe('Session Management', () => {
    test('should start streaming session successfully with metadata', async ({ request }) => {
      const formData = new FormData();
      formData.append('jobType', testQuoteData.jobType);
      formData.append('description', testQuoteData.description);
      formData.append('location', testQuoteData.location);
      formData.append('projectOverview', testQuoteData.projectOverview);
      formData.append('materials', JSON.stringify(testQuoteData.materials));
      formData.append('customerInfo', JSON.stringify(testQuoteData.customerInfo));
      formData.append('userAgent', 'Playwright-Test-Agent');
      formData.append('timestamp', new Date().toISOString());
      formData.append('sessionStartTime', Date.now().toString());

      const response = await request.post('http://localhost:5000/api/streaming/start-quote-generation', {
        headers: {
          'Authorization': `Bearer ${authToken}`
        },
        multipart: formData
      });

      expect(response.status()).toBe(200);
      const responseData = await response.json();
      expect(responseData.success).toBe(true);
      expect(responseData.sessionId).toBeDefined();
      expect(responseData.sessionId).toMatch(/^ses_\d+_\w+_\w+$/);
      
      sessionId = responseData.sessionId;
      console.log(`Session created: ${sessionId}`);
    });

    test('should get session status', async ({ request }) => {
      if (!sessionId) {
        test.skip('No active session available');
      }

      const response = await request.get(`http://localhost:5000/api/streaming/session/${sessionId}/status`, {
        headers: {
          'Authorization': `Bearer ${authToken}`
        }
      });

      expect(response.status()).toBe(200);
      const responseData = await response.json();
      expect(responseData.success).toBe(true);
      expect(responseData.sessionId).toBe(sessionId);
      expect(responseData.status).toBeDefined();
    });

    test('should handle session not found gracefully', async ({ request }) => {
      const fakeSessionId = 'ses_1234567890_fake_session';
      
      const response = await request.get(`http://localhost:5000/api/streaming/session/${fakeSessionId}/status`, {
        headers: {
          'Authorization': `Bearer ${authToken}`
        }
      });

      expect(response.status()).toBe(404);
      const responseData = await response.json();
      expect(responseData.success).toBe(false);
      expect(responseData.message).toContain('not found');
    });

    test('should enforce session limits', async ({ request }) => {
      const sessions = [];
      
      // Try to create many sessions (more than the limit of 50)
      for (let i = 0; i < 55; i++) {
        try {
          const formData = new FormData();
          formData.append('jobType', 'test');
          formData.append('description', `Test session ${i}`);
          formData.append('testIndex', i.toString());

          const response = await request.post('http://localhost:5000/api/streaming/start-quote-generation', {
            headers: {
              'Authorization': `Bearer ${authToken}`
            },
            multipart: formData
          });

          if (response.status() === 200) {
            const data = await response.json();
            sessions.push(data.sessionId);
          } else if (response.status() === 503) {
            // Session limit reached - this is expected
            const errorData = await response.json();
            expect(errorData.error).toContain('Session limit reached');
            break;
          }
        } catch (error) {
          console.log(`Session creation failed at index ${i}:`, error.message);
          break;
        }
      }

      console.log(`Created ${sessions.length} sessions before hitting limit`);
      expect(sessions.length).toBeLessThanOrEqual(50);

      // Clean up created sessions
      for (const sessionId of sessions) {
        try {
          await request.post(`http://localhost:5000/api/streaming/stop-quote-generation/${sessionId}`, {
            headers: {
              'Authorization': `Bearer ${authToken}`
            }
          });
        } catch (error) {
          console.log(`Failed to clean up session ${sessionId}:`, error.message);
        }
      }
    });
  });

  test.describe('Error Handling', () => {
    test('should reject requests without authentication', async ({ request }) => {
      const response = await request.post('http://localhost:5000/api/streaming/start-quote-generation', {
        headers: {
          'Content-Type': 'application/json'
        },
        data: testQuoteData
      });

      expect(response.status()).toBe(401);
    });

    test('should handle malformed data gracefully', async ({ request }) => {
      const response = await request.post('http://localhost:5000/api/streaming/start-quote-generation', {
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json'
        },
        data: 'invalid json data'
      });

      expect([400, 422]).toContain(response.status());
    });

    test('should return detailed error information', async ({ request }) => {
      const response = await request.get('http://localhost:5000/api/streaming/quote-generation/invalid-session-id', {
        headers: {
          'Authorization': `Bearer ${authToken}`
        }
      });

      expect(response.status()).toBe(404);
      const responseData = await response.json();
      
      // Verify enhanced error structure
      expect(responseData.error).toBeDefined();
      expect(responseData.code).toBe('session_not_found');
      expect(responseData.suggestion).toBeDefined();
      expect(responseData.timestamp).toBeDefined();
      expect(responseData.supportInfo).toBeDefined();
      expect(responseData.supportInfo.canRetry).toBe(true);
    });
  });

  test.describe('Connection and Recovery', () => {
    test('should handle SSE connection properly', async ({ request }) => {
      // First create a session
      const formData = new FormData();
      formData.append('jobType', 'test-sse');
      formData.append('description', 'Test SSE connection');

      const sessionResponse = await request.post('http://localhost:5000/api/streaming/start-quote-generation', {
        headers: {
          'Authorization': `Bearer ${authToken}`
        },
        multipart: formData
      });

      expect(sessionResponse.status()).toBe(200);
      const sessionData = await sessionResponse.json();
      const testSessionId = sessionData.sessionId;

      // Try to connect to SSE endpoint
      const sseResponse = await request.get(`http://localhost:5000/api/streaming/quote-generation/${testSessionId}`, {
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Accept': 'text/event-stream',
          'Cache-Control': 'no-cache'
        }
      });

      // Should get either successful connection or proper error
      expect([200, 404, 410]).toContain(sseResponse.status());

      if (sseResponse.status() === 200) {
        expect(sseResponse.headers()['content-type']).toContain('text/event-stream');
      }

      // Clean up
      await request.post(`http://localhost:5000/api/streaming/stop-quote-generation/${testSessionId}`, {
        headers: {
          'Authorization': `Bearer ${authToken}`
        }
      });
    });

    test('should handle session expiration properly', async ({ request }) => {
      // Try to access an expired session
      const expiredSessionId = 'ses_1000000000_expired_session';
      
      const response = await request.get(`http://localhost:5000/api/streaming/quote-generation/${expiredSessionId}`, {
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Accept': 'text/event-stream'
        }
      });

      expect([404, 410]).toContain(response.status());
      
      if (response.status() === 410) {
        const errorData = await response.json();
        expect(errorData.code).toBe('session_expired');
        expect(errorData.details).toBeDefined();
        expect(errorData.canRetry).toBe(true);
      }
    });
  });

  test.describe('Data Validation and Processing', () => {
    test('should handle complex project data', async ({ request }) => {
      const complexQuoteData = {
        jobType: 'electrical',
        description: 'Complete electrical system upgrade for 3-story commercial building',
        location: 'Commercial building - Downtown office complex',
        projectOverview: 'Full electrical modernization including smart systems integration',
        materials: [
          {
            name: '400A Commercial Panel',
            quantity: 2,
            category: 'electrical',
            specifications: {
              amperage: '400A',
              voltage: '480V',
              phases: 3,
              brand: 'Square D',
              type: 'commercial main panel',
              enclosure: 'NEMA 3R'
            },
            unitPrice: 2500.00,
            totalPrice: 5000.00
          },
          {
            name: 'THWN Copper Wire 4/0 AWG',
            quantity: 1000,
            unit: 'ft',
            category: 'electrical',
            specifications: {
              gauge: '4/0',
              material: 'copper',
              insulation: 'THWN',
              voltage_rating: '600V'
            },
            unitPrice: 8.50,
            totalPrice: 8500.00
          },
          {
            name: 'Smart Lighting Control System',
            quantity: 1,
            category: 'smart_systems',
            specifications: {
              zones: 50,
              wireless: true,
              app_control: true,
              energy_monitoring: true
            },
            unitPrice: 15000.00,
            totalPrice: 15000.00
          }
        ],
        customerInfo: {
          name: 'Corporate Client LLC',
          address: '789 Business Ave, Metro City, MC 54321',
          phone: '************',
          email: '<EMAIL>',
          contactPerson: 'John Smith',
          title: 'Facilities Manager'
        },
        additionalRequirements: {
          permitRequired: true,
          inspectionNeeded: true,
          timeline: '4-6 weeks',
          workingHours: 'After business hours only',
          specialInstructions: 'Coordinate with building security and existing tenants. Minimize disruption to business operations.',
          certifications: ['OSHA compliance', 'City electrical permit'],
          warranty: '10 years on installation, 25 years on materials'
        },
        estimatedCosts: {
          materials: 28500.00,
          labor: 15000.00,
          permits: 1500.00,
          total: 45000.00
        }
      };

      const formData = new FormData();
      for (const [key, value] of Object.entries(complexQuoteData)) {
        if (typeof value === 'object') {
          formData.append(key, JSON.stringify(value));
        } else {
          formData.append(key, value.toString());
        }
      }
      formData.append('complexity', 'high');
      formData.append('testType', 'complex-data');

      const response = await request.post('http://localhost:5000/api/streaming/start-quote-generation', {
        headers: {
          'Authorization': `Bearer ${authToken}`
        },
        multipart: formData
      });

      expect(response.status()).toBe(200);
      const responseData = await response.json();
      expect(responseData.success).toBe(true);
      expect(responseData.sessionId).toBeDefined();
      
      // Verify the session was created with complex data
      const statusResponse = await request.get(`http://localhost:5000/api/streaming/session/${responseData.sessionId}/status`, {
        headers: {
          'Authorization': `Bearer ${authToken}`
        }
      });
      
      expect(statusResponse.status()).toBe(200);
      
      // Clean up
      await request.post(`http://localhost:5000/api/streaming/stop-quote-generation/${responseData.sessionId}`, {
        headers: {
          'Authorization': `Bearer ${authToken}`
        }
      });
    });

    test('should validate required fields appropriately', async ({ request }) => {
      const incompleteData = {
        jobType: 'electrical'
        // Missing required fields like description
      };

      const formData = new FormData();
      formData.append('jobType', incompleteData.jobType);
      // Intentionally omit other required fields

      const response = await request.post('http://localhost:5000/api/streaming/start-quote-generation', {
        headers: {
          'Authorization': `Bearer ${authToken}`
        },
        multipart: formData
      });

      // Should either succeed with defaults or return validation error
      expect([200, 400, 422]).toContain(response.status());
      
      if (response.status() !== 200) {
        const errorData = await response.json();
        expect(errorData.error || errorData.message).toBeDefined();
      }
    });
  });

  test.describe('Performance and Load', () => {
    test('should handle multiple concurrent sessions', async ({ request }) => {
      const concurrentSessions = 5;
      const sessionPromises = [];

      for (let i = 0; i < concurrentSessions; i++) {
        const formData = new FormData();
        formData.append('jobType', 'concurrent-test');
        formData.append('description', `Concurrent session test ${i}`);
        formData.append('sessionIndex', i.toString());

        const promise = request.post('http://localhost:5000/api/streaming/start-quote-generation', {
          headers: {
            'Authorization': `Bearer ${authToken}`
          },
          multipart: formData
        });
        
        sessionPromises.push(promise);
      }

      const responses = await Promise.all(sessionPromises);
      const successfulSessions = [];

      for (const response of responses) {
        if (response.status() === 200) {
          const data = await response.json();
          successfulSessions.push(data.sessionId);
        }
      }

      expect(successfulSessions.length).toBeGreaterThan(0);
      console.log(`Successfully created ${successfulSessions.length}/${concurrentSessions} concurrent sessions`);

      // Clean up all successful sessions
      const cleanupPromises = successfulSessions.map(sessionId => 
        request.post(`http://localhost:5000/api/streaming/stop-quote-generation/${sessionId}`, {
          headers: {
            'Authorization': `Bearer ${authToken}`
          }
        }).catch(error => console.log(`Cleanup failed for ${sessionId}:`, error.message))
      );

      await Promise.allSettled(cleanupPromises);
    });

    test('should respond within reasonable time limits', async ({ request }) => {
      const startTime = Date.now();
      
      const formData = new FormData();
      formData.append('jobType', 'performance-test');
      formData.append('description', 'Performance timing test');

      const response = await request.post('http://localhost:5000/api/streaming/start-quote-generation', {
        headers: {
          'Authorization': `Bearer ${authToken}`
        },
        multipart: formData
      });

      const endTime = Date.now();
      const responseTime = endTime - startTime;

      expect(response.status()).toBe(200);
      expect(responseTime).toBeLessThan(5000); // Should respond within 5 seconds
      
      console.log(`Session creation took ${responseTime}ms`);

      const responseData = await response.json();
      
      // Clean up
      await request.post(`http://localhost:5000/api/streaming/stop-quote-generation/${responseData.sessionId}`, {
        headers: {
          'Authorization': `Bearer ${authToken}`
        }
      });
    });
  });

  test.afterAll(async ({ request }) => {
    // Clean up any remaining test sessions
    if (sessionId) {
      try {
        await request.post(`http://localhost:5000/api/streaming/stop-quote-generation/${sessionId}`, {
          headers: {
            'Authorization': `Bearer ${authToken}`
          }
        });
        console.log('Cleaned up main test session');
      } catch (error) {
        console.log('Failed to clean up main session:', error.message);
      }
    }
  });
});