const mongoose = require("mongoose");
const dotenv = require("dotenv");
const connectDB = require("../config/database");
const { execSync } = require("child_process");
const path = require("path");

// Load environment variables
dotenv.config();

// Connect to database
connectDB();

// Run the seeders in sequence
console.log("Running database seeders...");
console.log("\n=============================================");
console.log("SEEDING USERS...");
console.log("=============================================");

try {
  execSync("node " + path.join(__dirname, "userSeeder.js"), {
    stdio: "inherit",
  });

  console.log("\n=============================================");
  console.log("SEEDING TECHNICIANS...");
  console.log("=============================================");

  execSync("node " + path.join(__dirname, "technicianSeeder.js"), {
    stdio: "inherit",
  });

  console.log("\n=============================================");
  console.log("ALL SEEDERS COMPLETED SUCCESSFULLY!");
  console.log("=============================================");
  console.log("\nLogin with:");
  console.log("Email: <EMAIL>");
  console.log("Password: password123");
  console.log(
    "\nYou should now be able to see technicians in the application."
  );

  process.exit(0);
} catch (error) {
  console.error("\nError running seeders:", error.message);
  process.exit(1);
}
