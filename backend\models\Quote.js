/**
 * Quote.js
 * Model for quotes and proposals
 */

const mongoose = require("mongoose");

/**
 * Schema for quotes
 */
const quoteSchema = new mongoose.Schema(
  {
    name: {
      type: String,
      required: true,
      trim: true,
    },
    projectOverview: {
      // Replaces description
      type: String,
      trim: true,
    },
    scopeOfWork: {
      // New field for AI generation
      type: String,
      trim: true,
    },
    materialsIncluded: {
      // New field for AI generation
      type: String,
      trim: true,
    },
    validUntil: {
      type: Date,
    },
    items: [
      {
        name: {
          type: String,
          required: true,
        },
        description: String,
        description_raw_ai: String, // Store the original AI-generated description for reference and linking
        sku: String,
        currency: { type: String, trim: true, default: "USD" },
        price: {
          type: Number,
          required: true,
        },
        category: {
          type: String,
          enum: [
            "electrical_material",
            "labor",
            "non_electrical_material",
            "administrative",
            "unknown",
          ],
          default: "unknown",
        },
        quantity: {
          type: Number,
          required: true,
          default: 1,
        },
        unit: {
          type: String,
          default: "each",
        },
        source: String,
        sourceId: {
          type: mongoose.Schema.Types.ObjectId,
          ref: "MaterialSource",
        },
        imageUrl: String,
        url: String,
        notes: String,
        // AI Material Lookup Fields
        attributes: { type: mongoose.Schema.Types.Mixed, default: {} }, // Store material attributes
        lookup_query_suggestion: String, // Original query suggestion from AI
        lookup_status: {
          type: String,
          enum: [
            "pending_ai_search",
            "pending_user_selection",
            "price_found_ai",
            "price_confirmed_user",
            "not_found_ai",
            "error_ai_search",
          ],
          default: "pending_ai_search",
        },
        lookup_results: [
          {
            type: { type: String }, // e.g., 'PRICE_MATERIAL_COMPONENT', 'PRICE_GENERAL', 'PRODUCT_CODE_GENERAL_INFO'
            status: { type: String }, // e.g., 'pending_internal_price_lookup', 'success', 'error'
            data: { type: mongoose.Schema.Types.Mixed }, // Store any returned data
            messages: [
              {
                message: { type: String },
                timestamp: { type: Date },
              },
            ],
            timestamp: { type: Date, default: Date.now },
            source: { type: String }, // e.g., 'ai_suggestion_for_lookup', 'crawl4ai_result', 'manual_selection'
          },
        ],
        material_options: [
          {
            name: String,
            price: String,
            numerical_price: Number,
            imageUrl: String,
            productUrl: String,
            sku: String,
            source: String,
            description: String,
            available: Boolean,
            metadata: mongoose.Schema.Types.Mixed,
          },
        ],
        selected_option: {
          name: String,
          price: String,
          numerical_price: Number,
          imageUrl: String,
          productUrl: String,
          sku: String,
          source: String,
          description: String,
          selectedAt: Date,
          selectedBy: {
            type: mongoose.Schema.Types.ObjectId,
            ref: "User",
          },
        },
        last_lookup_attempt: Date,
        lookup_attempts: { type: Number, default: 0 },
      },
    ],
    labor: {
      hours: {
        type: Number,
        default: 0,
      },
      rate: {
        type: Number,
        default: 0,
      },
      total: {
        type: Number,
        default: 0,
      },
      notes: String,
    },
    summary: {
      materialsTotal: {
        type: Number,
        default: 0,
      },
      laborTotal: {
        type: Number,
        default: 0,
      },
      tax: {
        type: Number,
        default: 0,
      },
      taxRate: {
        type: Number,
        default: 0,
      },
      grandTotal: {
        type: Number,
        default: 0,
      },
    },
    // AI Generation Fields
    aiClarificationQuestions: {
      aiOriginalInputData: {
        type: mongoose.Schema.Types.Mixed,
        default: null,
      },
      type: [String],
      default: [],
    },
    aiUserInputAnswers: {
      type: mongoose.Schema.Types.Mixed,
      default: {},
    },
    aiGenerationInputType: {
      type: String,
      enum: ["overview", "materials", null],
      default: null,
    },
    aiGenerationStatus: {
      type: String,
      enum: ["idle", "pending_questions", "generating", "complete", "error"],
      default: "idle",
    },
    // --- End AI Fields ---
    status: {
      type: String,
      enum: ["DRAFT", "SENT", "APPROVED", "REJECTED", "EXPIRED"],
      default: "DRAFT",
    },
    job: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Job",
    },
    invoice: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Invoice",
    },
    customer: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Customer",
    },
    createdBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
      required: true,
    },
    company: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Company",
      required: true,
    },
    history: [
      {
        updatedBy: {
          type: mongoose.Schema.Types.ObjectId,
          ref: "User",
        },
        updatedAt: {
          type: Date,
          default: Date.now,
        },
        changes: [String],
        oldValues: mongoose.Schema.Types.Mixed,
        newValues: mongoose.Schema.Types.Mixed,
      },
    ],
    meta: {
      sentAt: Date,
      sentBy: {
        type: mongoose.Schema.Types.ObjectId,
        ref: "User",
      },
      approvedAt: Date,
      approvedBy: {
        type: mongoose.Schema.Types.ObjectId,
        ref: "User",
      },
      rejectedAt: Date,
      rejectedBy: {
        type: mongoose.Schema.Types.ObjectId,
        ref: "User",
      },
      rejectionReason: String,
    },
  },
  {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true },
  }
);

// Database indexes for performance optimization
quoteSchema.index({ company: 1, createdAt: -1 }); // Company quotes by date
quoteSchema.index({ job: 1 }); // Job-related quotes
quoteSchema.index({ customer: 1 }); // Customer quotes
quoteSchema.index({ "items.sku": 1 }); // SKU lookup
quoteSchema.index({ status: 1, createdAt: -1 }); // Status-based queries
quoteSchema.index({ createdBy: 1, createdAt: -1 }); // User-created quotes
quoteSchema.index({ validUntil: 1, status: 1 }); // Expiration tracking
quoteSchema.index({ "summary.grandTotal": 1 }); // Value-based queries
quoteSchema.index({ aiGenerationStatus: 1 }); // AI generation tracking
quoteSchema.index({ "items.category": 1 }); // Item category filtering
quoteSchema.index({ "items.sourceId": 1 }); // Material source tracking
quoteSchema.index({ "items.lookup_status": 1 }); // AI lookup status
quoteSchema.index({ "meta.sentAt": 1 }); // Sent quotes tracking
quoteSchema.index({ "meta.approvedAt": 1 }); // Approved quotes tracking

// Calculate totals before saving
quoteSchema.pre("save", function (next) {
  // Calculate materials total
  const materialsTotal = this.items.reduce((total, item) => {
    return total + item.price * item.quantity;
  }, 0);

  // Calculate labor total
  const laborTotal = this.labor.hours * this.labor.rate;

  // Calculate tax
  const taxableAmount =
    materialsTotal + (this.summary.taxLabor ? laborTotal : 0);
  const tax = (taxableAmount * (this.summary.taxRate || 0)) / 100;

  // Set summary values
  this.summary.materialsTotal = materialsTotal;
  this.summary.laborTotal = laborTotal;
  this.summary.tax = tax;
  this.summary.grandTotal = materialsTotal + laborTotal + tax;

  // Save labor total
  this.labor.total = laborTotal;

  next();
});

// Virtual for item count
quoteSchema.virtual("itemCount").get(function () {
  return this.items.length;
});

// Method to calculate total quantity of materials
quoteSchema.methods.getTotalQuantity = function () {
  return this.items.reduce((total, item) => total + item.quantity, 0);
};

// Method to add a material item
quoteSchema.methods.addItem = function (item) {
  // Check if item with same SKU already exists
  const existingIndex = this.items.findIndex((i) => i.sku === item.sku);

  if (existingIndex >= 0) {
    // Update existing item quantity
    this.items[existingIndex].quantity += item.quantity || 1;
  } else {
    // Add new item
    this.items.push(item);
  }

  return this;
};

// Method to remove a material item
quoteSchema.methods.removeItem = function (itemId) {
  this.items = this.items.filter(
    (item) => item._id.toString() !== itemId.toString()
  );
  return this;
};

// Method to approve quote
quoteSchema.methods.approve = function (userId) {
  this.status = "APPROVED";
  this.meta.approvedAt = new Date();
  this.meta.approvedBy = userId;

  return this;
};

// Method to reject quote
quoteSchema.methods.reject = function (userId, reason) {
  this.status = "REJECTED";
  this.meta.rejectedAt = new Date();
  this.meta.rejectedBy = userId;
  this.meta.rejectionReason = reason;

  return this;
};

// Method to mark as sent
quoteSchema.methods.markAsSent = function (userId) {
  this.status = "SENT";
  this.meta.sentAt = new Date();
  this.meta.sentBy = userId;

  return this;
};

// Method to convert to invoice
quoteSchema.methods.toInvoiceData = function () {
  return {
    customer: this.customer,
    job: this.job,
    // Use projectOverview or name if overview is empty for invoice description
    description: this.projectOverview
      ? `Invoice for: ${this.projectOverview.substring(0, 50)}...`
      : `Material Invoice: ${this.name}`,
    dueDate: this.validUntil,
    items: this.items.map((item) => ({
      name: item.name,
      description: item.description,
      quantity: item.quantity,
      unitPrice: item.price,
      amount: item.price * item.quantity,
      type: "material",
    })),
    labor:
      this.labor.hours > 0
        ? [
            {
              description: "Labor",
              hours: this.labor.hours,
              rate: this.labor.rate,
              amount: this.labor.total,
            },
          ]
        : [],
    subtotal: this.summary.materialsTotal + this.summary.laborTotal,
    tax: this.summary.tax,
    total: this.summary.grandTotal,
    notes: `Quote Reference: ${this._id}`,
  };
};

const Quote = mongoose.model("Quote", quoteSchema);

module.exports = Quote;
