import React, { useState, useEffect } from "react";
import { useParams, Link as RouterLink, useNavigate } from "react-router-dom";
import {
  Container,
  Typography,
  Box,
  Paper,
  Grid,
  Button,
  CircularProgress,
  Alert,
  List,
  ListItem,
  ListItemText,
  Divider,
  Chip,
  Stack,
  ListItemAvatar,
  Tooltip, // ADDED Tooltip
} from "@mui/material";
import {
  ArrowBack as ArrowBackIcon,
  FileCopy as InvoiceIcon,
  Send as SendIcon,
  CheckCircle as CheckCircleIcon,
  Cancel as CancelIcon,
  PictureAsPdf as PictureAsPdfIcon,
  Edit as EditIcon,
} from "@mui/icons-material";
import { useSelector, useDispatch } from "react-redux";
import {
  getQuoteById,
  updateQuoteStatus,
  convertQuoteToInvoice,
  clearQuoteError,
  resolveQuoteItemAction, // Correctly placed import
  clearItemResolveError, // Correctly placed import
} from "../slices/quoteSlice";
import ReactMarkdown from "react-markdown";

const QuoteDetail = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const dispatch = useDispatch();

  const {
    quote,
    loading,
    error,
    conversionResult,
    itemResolving = {}, // ADDED: Default to empty object
    itemError = {}, // ADDED: Default to empty object
  } = useSelector((state) => state.quotes);

  const [actionLoading, setActionLoading] = useState(false);
  const [actionError, setActionError] = useState(null);

  useEffect(() => {
    if (id) {
      dispatch(getQuoteById(id));
    }
    return () => {
      dispatch(clearQuoteError());
    };
  }, [dispatch, id]);

  useEffect(() => {
    if (conversionResult?.success && conversionResult?.invoiceId) {
      navigate(`/invoices/${conversionResult.invoiceId}`);
    }
    // Removed complex error handling for conversion, relying on global error state
  }, [conversionResult, navigate]);

  // MOVED handleResolveItem into component scope
  const handleResolveItem = (lookupQuery, itemIdentifier) => {
    if (!quote || !lookupQuery) return;
    dispatch(clearItemResolveError({ itemIdentifier }));
    dispatch(
      resolveQuoteItemAction({
        quoteId: quote._id,
        actionDetails: {
          type: "electrical_code_lookup",
          params: { query: lookupQuery },
        },
        itemIdentifier,
      })
    );
  };

  // MOVED renderResolvableContent into component scope
  const renderResolvableContent = (content, contentIdentifierPrefix) => {
    if (typeof content !== "string")
      return <ReactMarkdown>{String(content)}</ReactMarkdown>; // Handle non-string content gracefully

    const placeholderRegex = new RegExp(
      "\\[NEEDS_LOOKUP:\\s*([^\\]]+)\\s*\\]",
      "g"
    );
    let lastIndex = 0;
    const parts = [];
    let match;

    while ((match = placeholderRegex.exec(content)) !== null) {
      if (match.index > lastIndex) {
        parts.push(
          <span key={`${contentIdentifierPrefix}-text-${lastIndex}`}>
            {content.substring(lastIndex, match.index)}
          </span>
        );
      }
      const placeholder = match[0];
      const query = match[1];
      const itemIdentifier = `${contentIdentifierPrefix}-${placeholder}`;

      const isLoading = itemResolving[itemIdentifier];
      const currentError = itemError[itemIdentifier];

      parts.push(
        <Box
          component="span"
          key={itemIdentifier}
          sx={{
            display: "inline-block",
            mx: 0.5,
            p: 0.5,
            border: "1px dashed grey",
            borderRadius: 1,
            verticalAlign: "middle",
          }}
        >
          {isLoading ? (
            <CircularProgress size={20} sx={{ verticalAlign: "middle" }} />
          ) : currentError ? (
            <Tooltip title={currentError}>
              <Button
                onClick={() => handleResolveItem(query, itemIdentifier)}
                size="small"
                color="error"
                variant="outlined"
                sx={{ textTransform: "none" }}
              >
                Retry:{" "}
                {query.length > 15 ? query.substring(0, 12) + "..." : query}
              </Button>
            </Tooltip>
          ) : (
            <Button
              onClick={() => handleResolveItem(query, itemIdentifier)}
              size="small"
              variant="contained"
              color="secondary"
              sx={{ textTransform: "none" }}
            >
              Resolve:{" "}
              {query.length > 15 ? query.substring(0, 12) + "..." : query}
            </Button>
          )}
        </Box>
      );
      lastIndex = placeholderRegex.lastIndex;
    }

    if (lastIndex < content.length) {
      parts.push(
        <span key={`${contentIdentifierPrefix}-text-${lastIndex}`}>
          {content.substring(lastIndex)}
        </span>
      );
    }
    // If no placeholders were found, parts will be empty. Render original content with ReactMarkdown.
    // If placeholders were found, 'parts' contains the rendered output.
    return parts.length > 0 ? (
      <>{parts}</>
    ) : (
      <ReactMarkdown>{content}</ReactMarkdown>
    );
  };

  const formatDate = (dateString) => {
    if (!dateString) return "N/A";
    try {
      const date = new Date(dateString);
      if (isNaN(date.getTime())) return "Invalid Date";
      return date.toLocaleDateString("en-US", {
        year: "numeric",
        month: "short",
        day: "numeric",
        hour: "2-digit",
        minute: "2-digit",
      });
    } catch (e) {
      console.error("Error formatting date:", dateString, e);
      return "Invalid Date";
    }
  };

  const handleUpdateStatus = async (newStatus) => {
    if (!quote) return;
    setActionLoading(true); // Use local state for button disabling during status update
    setActionError(null); // Clear local action error
    dispatch(clearQuoteError()); // Clear global error
    try {
      await dispatch(
        updateQuoteStatus({ id: quote._id, status: newStatus })
      ).unwrap();
      // State updates automatically via Redux slice, no need to set local state
    } catch (err) {
      setActionError(err || `Failed to update status to ${newStatus}.`); // Set local action error
      console.error(`Status update error to ${newStatus}:`, err);
    } finally {
      setActionLoading(false);
    }
  };

  const handleConvertToInvoice = async () => {
    if (!quote || quote.status !== "APPROVED") {
      setActionError("Quote must be approved to convert to invoice."); // Use local action error
      return;
    }
    // Loading state is handled by the main 'loading' from the slice
    setActionError(null);
    dispatch(clearQuoteError());
    console.log(`Converting quote ${id} to invoice`);
    try {
      // Dispatch the convert action
      await dispatch(convertQuoteToInvoice(id)).unwrap();
      // Navigation is handled by the useEffect watching conversionResult
    } catch (err) {
      // Error is handled by the slice and stored in 'error' state
      console.error("Conversion error:", err);
      // No need to set local conversionError, rely on global 'error' state
    }
    // Loading state resets automatically via the slice
  };

  const getStatusColor = (status) => {
    switch (status) {
      case "DRAFT":
        return "default";
      case "SENT":
        return "info";
      case "APPROVED":
        return "success";
      case "REJECTED":
        return "error";
      case "EXPIRED":
        return "warning";
      case "CONVERTED":
        return "secondary"; // Add color for converted
      default:
        return "default";
    }
  };

  if (loading && !quote) {
    return (
      <Container sx={{ mt: 4 }}>
        <Box display="flex" justifyContent="center">
          <CircularProgress />
        </Box>
      </Container>
    );
  }

  if (error && !quote) {
    return (
      <Container sx={{ mt: 4 }}>
        <Alert severity="error">{error}</Alert>
      </Container>
    );
  }

  if (!quote) {
    return (
      <Container sx={{ mt: 4 }}>
        <Alert severity="warning">Quote not found or still loading...</Alert>
      </Container>
    );
  }

  const customerName =
    quote.customer?.name || quote.customer?.businessName || "N/A";
  const jobTitle = quote.job?.title || "N/A";
  const createdByName = quote.createdBy?.name || "N/A";
  const isActionInProgress = loading || actionLoading;

  return (
    <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
      <Button
        startIcon={<ArrowBackIcon />}
        component={RouterLink}
        to="/quotes"
        sx={{ mb: 2 }}
      >
        Back to Quotes
      </Button>
      <Paper sx={{ p: 3 }}>
        {/* Display global errors from Redux state (e.g., conversion errors) */}
        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}
        {/* Display local action errors */}
        {actionError && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {actionError}
          </Alert>
        )}

        <Box
          display="flex"
          justifyContent="space-between"
          alignItems="flex-start"
          mb={2}
          flexWrap="wrap"
          gap={2}
        >
          <Box>
            <Typography variant="h4" component="h1" gutterBottom>
              {quote.name}
            </Typography>
            <Chip
              label={quote.status}
              color={getStatusColor(quote.status)}
              size="small"
              sx={{ mb: 1 }}
            />
            <Typography variant="body2" color="text.secondary">
              Created: {formatDate(quote.createdAt)} by {createdByName}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Valid Until: {formatDate(quote.validUntil)}
            </Typography>
            <Typography variant="body1">Customer: {customerName}</Typography>
            <Typography variant="body1">Job: {jobTitle}</Typography>
          </Box>
          {/* Action Buttons */}
          <Stack
            direction="row"
            spacing={1}
            flexWrap="wrap"
            alignItems="center"
          >
            {/* Download PDF Button */}
            <Button
              variant="outlined"
              startIcon={<PictureAsPdfIcon />}
              href={`/api/quotes/${id}/pdf`} // Updated path
              target="_blank" // Open in new tab
              rel="noopener noreferrer" // Security measure
              disabled={isActionInProgress}
            >
              Download PDF
            </Button>

            <Button
              startIcon={<EditIcon />}
              component={RouterLink}
              to={`/quotes/${id}/edit`}
              disabled={isActionInProgress}
              variant="outlined"
            >
              Edit Quote
            </Button>

            {quote.status === "DRAFT" && (
              <Button
                variant="contained"
                startIcon={<SendIcon />}
                onClick={() => handleUpdateStatus("SENT")}
                disabled={isActionInProgress}
              >
                {actionLoading ? (
                  <CircularProgress size={20} color="inherit" />
                ) : (
                  "Send Quote"
                )}
              </Button>
            )}
            {quote.status === "SENT" && (
              <>
                <Button
                  variant="contained"
                  color="success"
                  startIcon={<CheckCircleIcon />}
                  onClick={() => handleUpdateStatus("APPROVED")}
                  disabled={isActionInProgress}
                >
                  {actionLoading ? (
                    <CircularProgress size={20} color="inherit" />
                  ) : (
                    "Approve"
                  )}
                </Button>
                <Button
                  variant="outlined"
                  color="error"
                  startIcon={<CancelIcon />}
                  onClick={() => handleUpdateStatus("REJECTED")}
                  disabled={isActionInProgress}
                >
                  {actionLoading ? (
                    <CircularProgress size={20} color="inherit" />
                  ) : (
                    "Reject"
                  )}
                </Button>
              </>
            )}
            {quote.status === "APPROVED" && (
              <Button
                variant="contained"
                startIcon={
                  loading ? (
                    <CircularProgress size={20} color="inherit" />
                  ) : (
                    <InvoiceIcon />
                  )
                }
                onClick={handleConvertToInvoice}
                disabled={isActionInProgress}
              >
                {loading ? "Converting..." : "Convert to Invoice"}
              </Button>
            )}
            {/* No primary actions for REJECTED, EXPIRED, CONVERTED */}
          </Stack>
        </Box>

        {/* Remove old description display */}
        {/* {quote.description && <Typography variant="body1" sx={{ my: 2 }}>{quote.description}</Typography>} */}

        {/* Display new AI-generated fields */}
        {quote.projectOverview && (
          <Box sx={{ my: 3 }}>
            <Typography variant="h6" gutterBottom>
              Project Overview
            </Typography>
            <Paper variant="outlined" sx={{ p: 2, "& p": { margin: 0 } }}>
              {/* MODIFIED: Use renderResolvableContent */}
              <div>
                {renderResolvableContent(
                  quote.projectOverview,
                  "projectOverview"
                )}
              </div>
            </Paper>
          </Box>
        )}

        {quote.scopeOfWork && (
          <Box sx={{ my: 3 }}>
            <Typography variant="h6" gutterBottom>
              Scope of Work
            </Typography>
            <Paper
              variant="outlined"
              sx={{
                p: 2,
                "& ul": { pl: 2, my: 0 },
                "& li": { mb: 0.5 },
                "& p": { my: 0.5 },
              }}
            >
              {/* MODIFIED: Use renderResolvableContent */}
              <div>
                {renderResolvableContent(quote.scopeOfWork, "scopeOfWork")}
              </div>
            </Paper>
          </Box>
        )}

        {quote.materialsIncluded && (
          <Box sx={{ my: 3 }}>
            <Typography variant="h6" gutterBottom>
              Materials Included (Summary)
            </Typography>
            <Paper
              variant="outlined"
              sx={{
                p: 2,
                "& ul": { pl: 2, my: 0 },
                "& li": { mb: 0.5 },
                "& p": { my: 0.5 },
              }}
            >
              {/* MODIFIED: Use renderResolvableContent */}
              <div>
                {renderResolvableContent(
                  quote.materialsIncluded,
                  "materialsIncluded"
                )}
              </div>
            </Paper>
          </Box>
        )}

        <Divider sx={{ my: 2 }} />

        <Typography variant="h6" gutterBottom>
          Items
        </Typography>
        <List disablePadding>
          {quote.items?.map((item, index) => (
            <React.Fragment key={item._id || index}>
              <ListItem alignItems="flex-start">
                <ListItemAvatar sx={{ mr: 1 }}>
                  <img
                    src={item.imageUrl || "/placeholder.jpg"} // Use placeholder if no image
                    alt={item.name || "Item"}
                    style={{
                      width: "50px",
                      height: "50px",
                      objectFit: "contain",
                      border: "1px solid #eee",
                      borderRadius: "4px",
                    }}
                  />
                </ListItemAvatar>
                <ListItemText
                  primary={`${item.name} (${item.sku || "N/A"})`}
                  secondary={
                    <>
                      <Typography
                        component="span"
                        variant="body2"
                        color="text.primary"
                      >
                        Qty: {item.quantity} {item.unit} @ $
                        {item.price?.toFixed(2) || "0.00"} each = $
                        {(item.quantity * (item.price || 0)).toFixed(2)}
                      </Typography>
                      {item.notes && ` — ${item.notes}`}
                      {item.source && ` (Source: ${item.source})`}
                    </>
                  }
                />
              </ListItem>
              {index < (quote.items?.length || 0) - 1 && (
                <Divider variant="inset" component="li" />
              )}
            </React.Fragment>
          ))}
        </List>

        <Divider sx={{ my: 2 }} />

        <Typography variant="h6" gutterBottom>
          Labor
        </Typography>
        <Typography>Hours: {quote.labor?.hours || 0}</Typography>
        <Typography>
          Rate: ${quote.labor?.rate?.toFixed(2) || "0.00"} / hr
        </Typography>
        <Typography>
          Total Labor: ${quote.labor?.total?.toFixed(2) || "0.00"}
        </Typography>
        {quote.labor?.notes && (
          <Typography variant="body2" color="text.secondary">
            Notes: {quote.labor.notes}
          </Typography>
        )}

        <Divider sx={{ my: 2 }} />

        <Grid container justifyContent="flex-end">
          <Grid item xs={12} md={4}>
            <Typography>
              Materials Total: $
              {quote.summary?.materialsTotal?.toFixed(2) || "0.00"}
            </Typography>
            <Typography>
              Labor Total: ${quote.summary?.laborTotal?.toFixed(2) || "0.00"}
            </Typography>
            <Typography>
              Subtotal: $
              {(
                quote.summary?.materialsTotal + quote.summary?.laborTotal || 0
              ).toFixed(2)}
            </Typography>
            <Typography>
              Tax ({quote.summary?.taxRate || 0}%): $
              {quote.summary?.tax?.toFixed(2) || "0.00"}
            </Typography>
            <Typography variant="h6" sx={{ mt: 1, fontWeight: "bold" }}>
              Grand Total: ${quote.summary?.grandTotal?.toFixed(2) || "0.00"}
            </Typography>
          </Grid>
        </Grid>
      </Paper>
    </Container>
  );
};

export default QuoteDetail;
