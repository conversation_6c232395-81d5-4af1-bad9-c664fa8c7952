const express = require("express");
const router = express.Router();
const {
  createCalendarEvent,
  getCalendarEvents,
  getCalendarEventById,
  updateCalendarEvent,
  deleteCalendarEvent,
  getUserCalendarEvents,
  checkEventOverlap,
} = require("../controllers/calendarController");
const { protect } = require("../middleware/authMiddleware");

// All routes are protected
router.use(protect);

router.route("/").post(createCalendarEvent).get(getCalendarEvents);

router
  .route("/:id")
  .get(getCalendarEventById)
  .put(updateCalendarEvent)
  .delete(deleteCalendarEvent);

router.get("/user/:userId", getUserCalendarEvents);
router.post("/check-overlap", checkEventOverlap);

module.exports = router;
