import React, { useState, useEffect, useCallback } from "react";
import { useDispatch, useSelector } from "react-redux";
import logger from "../../utils/logger"; // Import logger
import {
  Box,
  Typography,
  Button,
  IconButton,
  Card,
  CardMedia,
  CardContent,
  Dialog,
  Grid,
  Chip,
  Menu,
  MenuItem,
  FormControl,
  InputLabel,
  Select,
  CircularProgress,
  Paper,
  TextField,
  Divider,
  Tooltip,
} from "@mui/material";
import {
  CloudUpload as UploadIcon,
  PhotoLibrary as GalleryIcon,
  Close as CloseIcon,
  Download as DownloadIcon,
  Delete as DeleteIcon,
  MoreVert as MoreIcon,
  FilterList as FilterIcon,
  Psychology as AiIcon,
  Fullscreen as FullscreenIcon,
  FullscreenExit as FullscreenExitIcon,
  Info as InfoIcon,
  ZoomIn as ZoomInIcon,
  ZoomOut as ZoomOutIcon,
  RestartAlt as ResetIcon,
} from "@mui/icons-material";
import {
  uploadCustomerImages,
  getCustomerImages,
  deleteCustomerImage,
  updateCustomerImage,
  updateSingleCustomerImage,
} from "../../slices/customerSlice"; // Added updateSingleCustomerImage
import { useSnackbar } from "notistack";
import "./ImageGallery.css";

const ImageGallery = ({ customerId }) => {
  const dispatch = useDispatch();
  const { enqueueSnackbar } = useSnackbar();

  // Create a ref to track if component is mounted
  const isMounted = React.useRef(true);

  // Get customer data and image loading state from Redux
  const { customer, imageUploading, imageError, imageSuccess } = useSelector(
    (state) => state.customers
  );

  // Local state
  const [selectedImage, setSelectedImage] = useState(null);
  const [filter, setFilter] = useState("all");
  const [anchorEl, setAnchorEl] = useState(null);
  const [selectedImageForMenu, setSelectedImageForMenu] = useState(null);
  const [uploadCategory, setUploadCategory] = useState("other");
  const [uploadTitle, setUploadTitle] = useState("");
  const [showUploadForm, setShowUploadForm] = useState(false);
  const [aiAnalyzing, setAiAnalyzing] = useState(false);
  const [fullscreenImage, setFullscreenImage] = useState(false);
  const [fullscreenDetails, setFullscreenDetails] = useState(false);
  const [zoomLevel, setZoomLevel] = useState(1);
  const [isDragging, setIsDragging] = useState(false);
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 });
  const [imagePosition, setImagePosition] = useState({ x: 0, y: 0 });

  // Get images from customer data using useMemo to avoid unnecessary re-renders
  const images = React.useMemo(
    () => customer?.customerImages || [],
    [customer]
  );

  // Debug log for images data
  useEffect(() => {
    if (images.length > 0) {
      console.log("Customer images loaded:", images.length);
      console.log("First image sample:", images[0]);
    }
  }, [images]);

  // Set isMounted to false when component unmounts
  useEffect(() => {
    return () => {
      isMounted.current = false;
    };
  }, []);

  // Load customer images when component mounts
  useEffect(() => {
    if (customerId) {
      dispatch(getCustomerImages(customerId));
    }
  }, [dispatch, customerId]);

  // Show notification on success or error
  useEffect(() => {
    if (isMounted.current) {
      if (imageSuccess) {
        enqueueSnackbar("Image operation completed successfully", {
          variant: "success",
        });
      }
      if (imageError) {
        enqueueSnackbar(`Error: ${imageError}`, { variant: "error" });
      }
    }
  }, [imageSuccess, imageError, enqueueSnackbar]);

  // Track last uploaded images for auto-analysis
  const [lastUploadedImages, setLastUploadedImages] = useState([]);
  const [autoAnalyzeEnabled, setAutoAnalyzeEnabled] = useState(true);

  // Handle menu close
  const handleMenuClose = useCallback(() => {
    setAnchorEl(null);
    setSelectedImageForMenu(null);
  }, []);

  // Handle AI analysis
  const handleAnalyzeImage = useCallback(async () => {
    if (selectedImageForMenu && customerId) {
      setAiAnalyzing(true);
      try {
        // Get auth token from localStorage
        const token = JSON.parse(localStorage.getItem("userInfo"))?.token;

        if (!token) {
          enqueueSnackbar("Authentication required", { variant: "error" });
          return;
        }

        // Call the AI analysis endpoint
        const response = await fetch("/api/ai/analyze-image", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
          body: JSON.stringify({
            imageUrl: selectedImageForMenu.url,
            imageId: selectedImageForMenu._id,
            customerId,
          }),
        });

        if (!response.ok) {
          throw new Error(`Error: ${response.statusText}`);
        }

        const result = await response.json();
        console.log("AI analysis result:", result);

        if (result.success && result.updatedImage) {
          // Log the updated image data for debugging
          console.log(
            "Received updated image from AI analysis:",
            result.updatedImage
          );

          // Update Redux store directly with the single updated image
          dispatch(updateSingleCustomerImage(result.updatedImage));

          // If the modal is open for this image, update it immediately
          if (selectedImage && selectedImage._id === result.updatedImage._id) {
            setSelectedImage(result.updatedImage);
          }
          enqueueSnackbar("Image analysis completed and updated successfully", {
            variant: "success",
          });
        } else if (result.analysis) {
          // We have analysis but no updatedImage - create a synthetic update
          console.log(
            "AI analysis results received but no updatedImage:",
            result.analysis
          );

          // Find the image in the current state
          const currentImage = customer?.customerImages?.find(
            (img) => img._id === selectedImageForMenu._id
          );

          if (currentImage) {
            // Create an updated version of the image with the AI analysis results
            const syntheticUpdatedImage = {
              ...currentImage,
              title: result.analysis.title || currentImage.title,
              description:
                result.analysis.description || currentImage.description,
              aiCategory: result.analysis.category,
              aiComponents: result.analysis.components,
              aiConcerns: result.analysis.concerns,
              aiConfidence: result.analysis.confidence,
              aiLocation: result.analysis.location,
              aiMetadata: result.analysis.metadata,
              aiAnalyzedAt: new Date().toISOString(),
            };

            console.log(
              "Created synthetic updated image:",
              syntheticUpdatedImage
            );

            // Update Redux store with our synthetic image
            dispatch(updateSingleCustomerImage(syntheticUpdatedImage));

            // If the modal is open for this image, update it immediately
            if (
              selectedImage &&
              selectedImage._id === syntheticUpdatedImage._id
            ) {
              setSelectedImage(syntheticUpdatedImage);
            }

            enqueueSnackbar("Image analysis completed with synthetic update", {
              variant: "success",
            });
          } else {
            // Fallback: Refresh all images
            logger.warn(
              "Could not find current image to create synthetic update",
              { imageId: selectedImageForMenu._id }
            );
            dispatch(getCustomerImages(customerId));
            enqueueSnackbar(
              "Image analysis completed. Refreshing all images...",
              { variant: "info" }
            );
          }
        } else {
          // No analysis or updatedImage - full refresh
          logger.warn(
            "AI analysis response did not contain updatedImage or analysis, falling back to full refresh.",
            { result }
          );

          // Force a full refresh of customer images
          dispatch(getCustomerImages(customerId));
          console.log("Refreshed customer images after AI analysis");

          // Still update modal after a delay as a fallback
          setTimeout(() => {
            if (
              selectedImage &&
              selectedImage._id === selectedImageForMenu._id
            ) {
              const refreshedImage = customer?.customerImages?.find(
                (img) => img._id === selectedImage._id
              );
              if (refreshedImage) {
                console.log(
                  "Updated selected image from refreshed data:",
                  refreshedImage
                );
                setSelectedImage(refreshedImage);
              }
            }
          }, 500);

          if (result.error) {
            enqueueSnackbar(
              `Analysis complete, but error occurred: ${result.error}`,
              { variant: "warning" }
            );
          } else {
            enqueueSnackbar("Image analysis completed (fallback refresh)", {
              variant: "success",
            });
          }
        }
      } catch (error) {
        console.error("Error analyzing image:", error);
        enqueueSnackbar(`Error analyzing image: ${error.message}`, {
          variant: "error",
        });
      } finally {
        setAiAnalyzing(false);
        handleMenuClose();
      }
    }
  }, [
    customerId,
    selectedImageForMenu,
    customer,
    selectedImage,
    dispatch,
    enqueueSnackbar,
    handleMenuClose,
  ]);

  // Handle image upload
  const handleUpload = (event) => {
    const files = event.target.files;
    if (files.length > 0 && customerId) {
      // Create FormData object
      const formData = new FormData();

      // Append each file
      Array.from(files).forEach((file) => {
        formData.append("customerImages", file);
      });

      // Add metadata
      formData.append("category", uploadCategory);
      formData.append("title", uploadTitle || files[0].name);
      formData.append("autoAnalyze", autoAnalyzeEnabled ? "true" : "false");

      // Log what we're uploading for debugging
      console.log(
        `Uploading ${files.length} files to customer ${customerId} with category ${uploadCategory}`
      );

      // Dispatch upload action
      dispatch(
        uploadCustomerImages({
          id: customerId,
          formData,
        })
      );

      // Reset form
      setUploadTitle("");
      setShowUploadForm(false);
    } else {
      if (!customerId) {
        console.error("No customer ID provided for image upload");
        enqueueSnackbar("Error: No customer ID provided for image upload", {
          variant: "error",
        });
      } else if (files.length === 0) {
        console.error("No files selected for upload");
        enqueueSnackbar("Please select at least one file to upload", {
          variant: "warning",
        });
      }
    }
  };

  // Auto-analyze newly uploaded images
  useEffect(() => {
    if (imageSuccess && customer?.customerImages && !imageUploading) {
      // Find newly uploaded images (those without aiAnalyzedAt)
      const newImages = customer.customerImages.filter(
        (img) => !img.aiAnalyzedAt
      );

      if (newImages.length > 0 && autoAnalyzeEnabled) {
        console.log(`Auto-analyzing ${newImages.length} new images`);
        // Analyze the first image and set it for auto-analysis
        setSelectedImageForMenu(newImages[0]);
        setLastUploadedImages(newImages.slice(1)); // Store the rest for sequential analysis
        handleAnalyzeImage();
      }
    }
  }, [
    imageSuccess,
    customer?.customerImages,
    imageUploading,
    autoAnalyzeEnabled,
    handleAnalyzeImage,
  ]);

  // Continue analyzing remaining images after each analysis completes
  useEffect(() => {
    if (!aiAnalyzing && lastUploadedImages.length > 0 && autoAnalyzeEnabled) {
      // Small delay to prevent rate limiting
      const timer = setTimeout(() => {
        const nextImage = lastUploadedImages[0];
        setSelectedImageForMenu(nextImage);
        setLastUploadedImages(lastUploadedImages.slice(1));
        handleAnalyzeImage();
      }, 1000);

      return () => clearTimeout(timer);
    }
  }, [aiAnalyzing, lastUploadedImages, autoAnalyzeEnabled, handleAnalyzeImage]);

  // Open image in modal
  const handleImageClick = (image) => {
    setSelectedImage(image);
  };

  // Close modal
  const handleCloseModal = () => {
    setSelectedImage(null);
    setFullscreenImage(false);
    setFullscreenDetails(false);
    resetZoom();
  };

  const toggleFullscreenImage = () => {
    setFullscreenImage(!fullscreenImage);
    setFullscreenDetails(false);
    resetZoom();
  };

  const toggleFullscreenDetails = () => {
    setFullscreenDetails(!fullscreenDetails);
    setFullscreenImage(false);
    resetZoom();
  };

  // Zoom functions
  const zoomIn = () => {
    setZoomLevel((prevZoom) => Math.min(prevZoom + 0.25, 5));
    setImagePosition({ x: 0, y: 0 }); // Reset position when zooming
  };

  const zoomOut = () => {
    setZoomLevel((prevZoom) => Math.max(prevZoom - 0.25, 0.5));
    setImagePosition({ x: 0, y: 0 }); // Reset position when zooming
  };

  const resetZoom = () => {
    setZoomLevel(1);
    setImagePosition({ x: 0, y: 0 });
    setIsDragging(false);
  };

  // Handle mouse wheel zoom
  const handleWheel = (e) => {
    if (e.deltaY < 0) {
      // Scroll up - zoom in
      setZoomLevel((prevZoom) => Math.min(prevZoom + 0.1, 5));
    } else {
      // Scroll down - zoom out
      setZoomLevel((prevZoom) => Math.max(prevZoom - 0.1, 0.5));
    }
    e.preventDefault();
  };

  // Handle image dragging when zoomed
  const handleMouseDown = (e) => {
    if (zoomLevel > 1) {
      setIsDragging(true);
      setDragStart({ x: e.clientX, y: e.clientY });
    }
  };

  const handleMouseMove = (e) => {
    if (isDragging && zoomLevel > 1) {
      const deltaX = e.clientX - dragStart.x;
      const deltaY = e.clientY - dragStart.y;

      setImagePosition((prev) => ({
        x: prev.x + deltaX,
        y: prev.y + deltaY,
      }));

      setDragStart({ x: e.clientX, y: e.clientY });
    }
  };

  const handleMouseUp = () => {
    setIsDragging(false);
  };

  const handleMouseLeave = () => {
    setIsDragging(false);
  };

  // Handle menu open
  const handleMenuOpen = (event, image) => {
    // Make sure to stop propagation to prevent the card click
    event.stopPropagation();
    event.preventDefault();
    console.log(
      "Menu opened for image:",
      image.title || "Untitled",
      "ID:",
      image._id
    );
    setAnchorEl(event.currentTarget);
    setSelectedImageForMenu(image);
  };

  // Handle image download
  const handleDownload = () => {
    if (selectedImageForMenu && selectedImageForMenu.url) {
      // Create a temporary link and trigger download
      const link = document.createElement("a");
      link.href = selectedImageForMenu.url;
      link.download = selectedImageForMenu.title || "image";
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
    handleMenuClose();
  };

  // Handle image delete
  const handleDelete = () => {
    if (selectedImageForMenu && customerId) {
      dispatch(
        deleteCustomerImage({
          customerId,
          imageId: selectedImageForMenu._id,
        })
      );
    }
    handleMenuClose();
  };

  // Handle image category update
  const handleUpdateCategory = (category) => {
    if (selectedImageForMenu && customerId) {
      dispatch(
        updateCustomerImage({
          customerId,
          imageId: selectedImageForMenu._id,
          imageData: { category },
        })
      );
    }
    handleMenuClose();
  };

  // AI Analysis toggle
  const handleToggleAutoAnalyze = () => {
    setAutoAnalyzeEnabled((prev) => !prev);
    enqueueSnackbar(
      `Auto AI analysis ${!autoAnalyzeEnabled ? "enabled" : "disabled"}`,
      {
        variant: "info",
      }
    );
  };

  // Filter images based on category or AI category
  const filteredImages =
    filter === "all"
      ? images
      : filter === "ai_inspection"
      ? images.filter((img) => img.aiCategory === "inspection")
      : filter === "ai_diagram"
      ? images.filter((img) => img.aiCategory === "diagram")
      : filter === "ai_equipment"
      ? images.filter((img) => img.aiCategory === "equipment")
      : filter === "ai_damage"
      ? images.filter((img) => img.aiCategory === "damage")
      : filter === "ai_hazard"
      ? images.filter((img) => img.aiCategory === "hazard")
      : filter === "ai_invoice"
      ? images.filter((img) => img.aiCategory === "invoice")
      : images.filter((img) => img.category === filter);

  // Get category style
  const getCategoryStyle = (category) => {
    switch (category) {
      case "before":
        return "category-before";
      case "after":
        return "category-after";
      case "inspection":
        return "ai-inspection";
      case "diagram":
        return "ai-diagram";
      case "equipment":
        return "ai-equipment";
      case "damage":
        return "ai-damage";
      case "hazard":
        return "ai-hazard";
      case "invoice":
        return "ai-invoice";
      default:
        return "category-other";
    }
  };

  // Get category label
  const getCategoryLabel = (category) => {
    switch (category) {
      case "before":
        return "Before";
      case "after":
        return "After";
      default:
        return category || "Other";
    }
  };

  return (
    <div className="image-gallery">
      <div className="image-gallery-header">
        <Box sx={{ display: "flex", alignItems: "center" }}>
          <GalleryIcon sx={{ mr: 1 }} />
          <Typography variant="h6">Customer Images</Typography>
        </Box>

        <Box sx={{ display: "flex", gap: 2 }}>
          <Box
            sx={{
              display: "flex",
              alignItems: "center",
              border: "1px solid #e0e0e0",
              borderRadius: "4px",
              pl: 1,
            }}
          >
            <Typography variant="body2" sx={{ mr: 1, color: "#666" }}>
              Filter
            </Typography>
            <Select
              value={filter}
              onChange={(e) => setFilter(e.target.value)}
              variant="standard"
              disableUnderline
              sx={{ minWidth: 120, "& .MuiSelect-select": { py: 1 } }}
              IconComponent={(props) => (
                <FilterIcon {...props} style={{ marginRight: "8px" }} />
              )}
            >
              <MenuItem value="all">All Images</MenuItem>
              <Divider />
              <MenuItem
                disabled
                sx={{ opacity: 0.7, fontWeight: "bold", fontSize: "0.8rem" }}
              >
                STANDARD CATEGORIES
              </MenuItem>
              <MenuItem value="before">Before</MenuItem>
              <MenuItem value="after">After</MenuItem>
              <MenuItem value="other">Other</MenuItem>
              <Divider />
              <MenuItem
                disabled
                sx={{ opacity: 0.7, fontWeight: "bold", fontSize: "0.8rem" }}
              >
                AI CATEGORIES
              </MenuItem>
              <MenuItem value="ai_inspection">Inspection</MenuItem>
              <MenuItem value="ai_diagram">Diagram</MenuItem>
              <MenuItem value="ai_equipment">Equipment</MenuItem>
              <MenuItem value="ai_damage">Damage</MenuItem>
              <MenuItem value="ai_hazard">Hazard</MenuItem>
              <MenuItem value="ai_invoice">Invoice</MenuItem>
            </Select>
          </Box>

          <Button
            variant="contained"
            onClick={() => setShowUploadForm(!showUploadForm)}
            startIcon={<UploadIcon />}
            disabled={imageUploading}
            sx={{
              bgcolor: "#1976d2",
              color: "white",
              textTransform: "uppercase",
            }}
          >
            {imageUploading ? "Uploading..." : "Upload Image"}
          </Button>
        </Box>
      </div>

      {/* Upload Form */}
      {showUploadForm && (
        <Paper sx={{ p: 2, mb: 2 }}>
          <Typography variant="subtitle1" gutterBottom>
            Upload New Image
          </Typography>
          <Box sx={{ display: "flex", flexDirection: "column", gap: 2 }}>
            <TextField
              label="Image Title (Optional)"
              value={uploadTitle}
              onChange={(e) => setUploadTitle(e.target.value)}
              size="small"
              fullWidth
            />
            <FormControl size="small" fullWidth>
              <InputLabel>Category</InputLabel>
              <Select
                value={uploadCategory}
                label="Category"
                onChange={(e) => setUploadCategory(e.target.value)}
              >
                <MenuItem value="before">Before</MenuItem>
                <MenuItem value="after">After</MenuItem>
                <MenuItem value="other">Other</MenuItem>
              </Select>
            </FormControl>

            <Box sx={{ display: "flex", alignItems: "center", mb: 1 }}>
              <FormControl component="fieldset">
                <Box sx={{ display: "flex", alignItems: "center" }}>
                  <Chip
                    icon={<AiIcon style={{ fontSize: "14px" }} />}
                    label={
                      autoAnalyzeEnabled
                        ? "Auto-analyze with AI (ON)"
                        : "Auto-analyze with AI (OFF)"
                    }
                    color={autoAnalyzeEnabled ? "primary" : "default"}
                    variant={autoAnalyzeEnabled ? "filled" : "outlined"}
                    onClick={handleToggleAutoAnalyze}
                    sx={{ cursor: "pointer" }}
                  />
                </Box>
              </FormControl>
            </Box>

            <Button
              variant="contained"
              component="label"
              startIcon={
                imageUploading ? <CircularProgress size={20} /> : <UploadIcon />
              }
              disabled={imageUploading}
              fullWidth
            >
              Select File(s)
              <input
                type="file"
                hidden
                accept="image/*"
                multiple
                onChange={handleUpload}
              />
            </Button>
          </Box>
        </Paper>
      )}

      {filteredImages.length === 0 ? (
        <div className="empty-gallery">
          <Typography variant="body1" color="textSecondary" gutterBottom>
            No images found in this category.
          </Typography>
          {filter !== "all" && (
            <Button
              variant="outlined"
              onClick={() => setFilter("all")}
              startIcon={<FilterIcon />}
            >
              Show All Images
            </Button>
          )}
        </div>
      ) : (
        <Grid container spacing={3} className="image-gallery-grid">
          {filteredImages.map((image, index) => {
            // Log the image object being rendered, especially its ID
            console.log(
              `Rendering image index ${index}:`,
              image,
              "with key:",
              image?._id || image?.id || "undefined"
            );
            return (
              <Grid
                item
                xs={12}
                sm={6}
                md={4}
                lg={3}
                key={image._id || image.id || `image-${index}`}
              >
                {" "}
                {/* Use index as fallback key */}
                <Card
                  className="image-card"
                  onClick={() => handleImageClick(image)}
                  sx={{
                    cursor: "pointer",
                    position: "relative", // Establish stacking context
                    overflow: "visible", // Allow menu to overflow
                  }}
                >
                  <CardMedia
                    component="img"
                    className="image-thumbnail"
                    image={
                      image.thumbnailUrl ||
                      image.url ||
                      "https://via.placeholder.com/200x150?text=No+Image"
                    } // Prioritize thumbnail
                    alt={image.title || "Customer image"}
                  />
                  <CardContent sx={{ p: 1.5, position: "relative", pb: 2 }}>
                    <Box sx={{ position: "relative" }}>
                      <Box>
                        <Typography
                          variant="body2"
                          className="image-title"
                          sx={{ fontWeight: "normal", fontSize: "0.875rem" }}
                        >
                          {image.title ||
                            (image.originalName
                              ? image.originalName.split(".")[0]
                              : "Untitled Image")}
                        </Typography>
                        <Typography
                          variant="caption"
                          color="text.secondary"
                          sx={{
                            display: "block",
                            mt: 0.5,
                            mb: 1,
                            fontSize: "0.75rem",
                          }}
                        >
                          {new Date(
                            image.date ||
                              image.uploadedAt ||
                              image.createdAt ||
                              Date.now()
                          ).toLocaleDateString()}
                        </Typography>
                        <div>
                          <Box
                            sx={{
                              display: "flex",
                              alignItems: "center",
                              gap: 0.5,
                              mb: 0.5,
                            }}
                          >
                            <Chip
                              label={getCategoryLabel(
                                image.category
                              ).toLowerCase()}
                              size="small"
                              className={`image-category ${getCategoryStyle(
                                image.category
                              )}`}
                              sx={{
                                height: "20px",
                                "& .MuiChip-label": { px: 1, py: 0 },
                              }}
                            />
                            {/* AI Badge */}
                            {image.aiAnalyzedAt ? (
                              <Tooltip
                                title={`AI analyzed on ${new Date(
                                  image.aiAnalyzedAt
                                ).toLocaleString()}`}
                              >
                                <Chip
                                  icon={<AiIcon style={{ fontSize: "14px" }} />}
                                  label="AI"
                                  size="small"
                                  color="primary"
                                  variant="outlined"
                                  sx={{
                                    height: "20px",
                                    "& .MuiChip-label": { px: 0.5, py: 0 },
                                  }}
                                />
                              </Tooltip>
                            ) : (
                              <Tooltip title="Analyze with AI">
                                <Chip
                                  icon={<AiIcon style={{ fontSize: "14px" }} />}
                                  label="Analyze"
                                  size="small"
                                  variant="outlined"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    setSelectedImageForMenu(image);
                                    handleAnalyzeImage();
                                  }}
                                  sx={{
                                    height: "20px",
                                    "& .MuiChip-label": { px: 0.5, py: 0 },
                                    cursor: "pointer",
                                  }}
                                />
                              </Tooltip>
                            )}
                          </Box>
                          {/* Display AI Category if available */}
                          {image.aiCategory && image.aiCategory !== "TBD" && (
                            <Typography
                              variant="caption"
                              color="text.secondary"
                              sx={{
                                display: "block",
                                mt: 0.5,
                                fontStyle: "italic",
                              }}
                            >
                              AI: {image.aiCategory}{" "}
                              {image.aiConfidence
                                ? `(${Math.round(
                                    image.aiConfidence * 100
                                  )}% confidence)`
                                : ""}
                            </Typography>
                          )}

                          {/* Display location if available */}
                          {image.aiLocation && (
                            <Typography
                              variant="caption"
                              color="text.secondary"
                              sx={{
                                display: "block",
                                mt: 0.5,
                                fontSize: "0.75rem",
                              }}
                            >
                              Location: {image.aiLocation}
                            </Typography>
                          )}

                          {/* Display short description if available */}
                          {image.description && (
                            <Typography
                              variant="caption"
                              color="text.secondary"
                              sx={{
                                display: "block",
                                mt: 0.5,
                                fontSize: "0.75rem",
                                overflow: "hidden",
                                textOverflow: "ellipsis",
                                whiteSpace: "nowrap",
                              }}
                            >
                              {image.description.length > 60
                                ? `${image.description.substring(0, 60)}...`
                                : image.description}
                            </Typography>
                          )}
                        </div>
                      </Box>
                      <IconButton
                        size="small"
                        onClick={(e) => handleMenuOpen(e, image)}
                        onMouseDown={(e) => e.stopPropagation()}
                        sx={{
                          padding: "4px",
                          zIndex: 10, // Ensure higher z-index
                          position: "absolute", // Position absolutely
                          top: "8px",
                          right: "8px",
                        }}
                        className="image-menu-button"
                      >
                        <MoreIcon />
                      </IconButton>
                    </Box>
                  </CardContent>
                </Card>
              </Grid>
            );
          })}
        </Grid>
      )}

      {/* Image Menu */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
        sx={{ zIndex: 1500 }} // Ensure menu is above other elements
        slotProps={{ paper: { elevation: 4 } }} // Increase shadow for better visibility
        MenuListProps={{ onClick: (e) => e.stopPropagation() }} // Prevent click propagation
      >
        <MenuItem onClick={handleDownload}>
          <DownloadIcon fontSize="small" sx={{ mr: 1 }} />
          Download
        </MenuItem>
        <MenuItem onClick={() => handleUpdateCategory("before")}>
          <Chip
            size="small"
            label="Before"
            className="category-before"
            sx={{ mr: 1 }}
          />
          Mark as Before
        </MenuItem>
        <MenuItem onClick={() => handleUpdateCategory("after")}>
          <Chip
            size="small"
            label="After"
            className="category-after"
            sx={{ mr: 1 }}
          />
          Mark as After
        </MenuItem>
        <MenuItem onClick={() => handleUpdateCategory("other")}>
          <Chip
            size="small"
            label="Other"
            className="category-other"
            sx={{ mr: 1 }}
          />
          Mark as Other
        </MenuItem>
        <Divider />
        <MenuItem onClick={handleAnalyzeImage} disabled={aiAnalyzing}>
          <AiIcon fontSize="small" sx={{ mr: 1 }} color="primary" />
          {aiAnalyzing ? "Analyzing..." : "Analyze with AI"}
        </MenuItem>
        <Divider />
        <MenuItem onClick={handleDelete}>
          <DeleteIcon fontSize="small" sx={{ mr: 1 }} color="error" />
          Delete
        </MenuItem>
      </Menu>

      {/* Image Modal */}
      <Dialog
        open={Boolean(selectedImage)}
        onClose={handleCloseModal}
        maxWidth="lg"
        fullWidth
        PaperProps={{
          sx: {
            display: "flex",
            flexDirection: "column",
            maxHeight: "90vh",
            height: "90vh",
          },
        }}
      >
        {selectedImage && (
          <>
            <Box
              sx={{
                display: "flex",
                justifyContent: "space-between",
                alignItems: "center",
                p: 1,
                borderBottom: "1px solid rgba(0,0,0,0.12)",
              }}
            >
              <Box sx={{ display: "flex", alignItems: "center" }}>
                <Typography variant="h6" sx={{ mr: 1 }}>
                  {selectedImage.title || "Untitled Image"}
                </Typography>
                <Chip
                  label={getCategoryLabel(selectedImage.category)}
                  size="small"
                  className={`image-category ${getCategoryStyle(
                    selectedImage.category
                  )}`}
                />
              </Box>
              <Box>
                <Tooltip title="View full image">
                  <IconButton onClick={toggleFullscreenImage} sx={{ mr: 0.5 }}>
                    {fullscreenImage ? (
                      <FullscreenExitIcon />
                    ) : (
                      <FullscreenIcon />
                    )}
                  </IconButton>
                </Tooltip>
                <Tooltip title="View full details">
                  <IconButton
                    onClick={toggleFullscreenDetails}
                    sx={{ mr: 0.5 }}
                  >
                    <InfoIcon />
                  </IconButton>
                </Tooltip>
                <Button
                  startIcon={<DownloadIcon />}
                  onClick={() => {
                    const link = document.createElement("a");
                    link.href =
                      selectedImage.url ||
                      selectedImage.fullSizeUrl ||
                      selectedImage.thumbnailUrl;
                    link.download = selectedImage.title || "image";
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                  }}
                  sx={{ mr: 1 }}
                >
                  DOWNLOAD
                </Button>
                <IconButton onClick={handleCloseModal}>
                  <CloseIcon />
                </IconButton>
              </Box>
            </Box>

            {/* Main content with resizable panes */}
            <Box
              sx={{
                flex: 1,
                display: "flex",
                flexDirection: "column",
                overflow: "hidden",
              }}
            >
              {fullscreenImage ? (
                // Full screen image view
                <Box
                  sx={{
                    flex: 1,
                    overflow: "hidden",
                    display: "flex",
                    justifyContent: "center",
                    alignItems: "center",
                    bgcolor: "#000",
                    position: "relative",
                  }}
                >
                  {/* Zoom controls */}
                  <Box className="zoom-controls">
                    <Tooltip title="Zoom out">
                      <IconButton
                        size="small"
                        onClick={zoomOut}
                        sx={{ color: "white" }}
                      >
                        <ZoomOutIcon />
                      </IconButton>
                    </Tooltip>
                    <Typography
                      variant="body2"
                      sx={{
                        color: "white",
                        display: "flex",
                        alignItems: "center",
                        mx: 1,
                      }}
                    >
                      {Math.round(zoomLevel * 100)}%
                    </Typography>
                    <Tooltip title="Zoom in">
                      <IconButton
                        size="small"
                        onClick={zoomIn}
                        sx={{ color: "white" }}
                      >
                        <ZoomInIcon />
                      </IconButton>
                    </Tooltip>
                    <Tooltip title="Reset zoom">
                      <IconButton
                        size="small"
                        onClick={resetZoom}
                        sx={{ color: "white", ml: 1 }}
                      >
                        <ResetIcon />
                      </IconButton>
                    </Tooltip>
                  </Box>

                  <Box
                    className={`zoom-image-container ${
                      zoomLevel > 1 ? "draggable" : ""
                    } ${isDragging ? "dragging" : ""}`}
                    onMouseDown={handleMouseDown}
                    onMouseMove={handleMouseMove}
                    onMouseUp={handleMouseUp}
                    onMouseLeave={handleMouseLeave}
                    onWheel={handleWheel}
                  >
                    <img
                      className={`zoom-image ${isDragging ? "dragging" : ""}`}
                      src={
                        selectedImage.url ||
                        selectedImage.fullSizeUrl ||
                        selectedImage.thumbnailUrl
                      }
                      alt={selectedImage.title || "Customer image"}
                      style={{
                        maxWidth: zoomLevel === 1 ? "100%" : "none",
                        maxHeight: zoomLevel === 1 ? "100%" : "none",
                        width: zoomLevel === 1 ? "auto" : `${100 * zoomLevel}%`,
                        height: zoomLevel === 1 ? "auto" : "auto",
                        objectFit: "contain",
                        display: "block",
                        transform: `translate(${imagePosition.x}px, ${imagePosition.y}px)`,
                      }}
                    />
                  </Box>
                </Box>
              ) : fullscreenDetails ? (
                // Full screen details view
                <Box sx={{ flex: 1, overflow: "auto", p: 2 }}>
                  <Typography variant="h6" sx={{ mb: 2 }}>
                    Image Details
                  </Typography>

                  {/* Date */}
                  <Typography
                    variant="body2"
                    color="text.secondary"
                    sx={{ mb: 2 }}
                  >
                    {new Date(
                      selectedImage.date ||
                        selectedImage.createdAt ||
                        Date.now()
                    ).toLocaleDateString()}
                  </Typography>

                  {/* Description section */}
                  <Box sx={{ mb: 3 }}>
                    <Typography
                      variant="body1"
                      sx={{ fontWeight: "medium", mb: 1 }}
                    >
                      Description
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {selectedImage.description || "No description available"}
                    </Typography>
                  </Box>

                  {/* AI Analysis section */}
                  {(selectedImage.aiCategory ||
                    selectedImage.aiComponents ||
                    selectedImage.aiConcerns ||
                    selectedImage.aiLocation ||
                    selectedImage.aiMetadata) && (
                    <Box className="ai-analysis-section" sx={{ mb: 2 }}>
                      <Typography variant="h6" sx={{ mb: 2 }}>
                        AI Analysis{" "}
                        {selectedImage.aiConfidence
                          ? `(${Math.round(
                              selectedImage.aiConfidence * 100
                            )}% confidence)`
                          : ""}
                      </Typography>

                      <Grid container spacing={3}>
                        {/* Left column */}
                        <Grid item xs={12} md={6}>
                          {selectedImage.aiCategory && (
                            <Box sx={{ mb: 2 }}>
                              <Typography
                                variant="body1"
                                sx={{ fontWeight: "medium" }}
                              >
                                Category:
                              </Typography>
                              <Typography
                                variant="body2"
                                color="text.secondary"
                              >
                                {selectedImage.aiCategory}
                              </Typography>
                            </Box>
                          )}

                          {selectedImage.aiLocation && (
                            <Box sx={{ mb: 2 }}>
                              <Typography
                                variant="body1"
                                sx={{ fontWeight: "medium" }}
                              >
                                Location:
                              </Typography>
                              <Typography
                                variant="body2"
                                color="text.secondary"
                              >
                                {selectedImage.aiLocation}
                              </Typography>
                            </Box>
                          )}

                          {/* Display GPS location if available */}
                          {selectedImage.aiMetadata &&
                            selectedImage.aiMetadata.gpsLocation && (
                              <Box sx={{ mb: 2 }}>
                                <Typography
                                  variant="body1"
                                  sx={{ fontWeight: "medium" }}
                                >
                                  GPS Location:
                                </Typography>
                                <Typography
                                  variant="body2"
                                  color="text.secondary"
                                >
                                  {
                                    selectedImage.aiMetadata.gpsLocation
                                      .formatted
                                  }
                                </Typography>
                              </Box>
                            )}
                        </Grid>

                        {/* Right column */}
                        <Grid item xs={12} md={6}>
                          {selectedImage.aiComponents &&
                            selectedImage.aiComponents.length > 0 && (
                              <Box sx={{ mb: 2 }}>
                                <Typography
                                  variant="body1"
                                  sx={{ fontWeight: "medium" }}
                                >
                                  Components Identified:
                                </Typography>
                                <Box
                                  sx={{
                                    display: "flex",
                                    flexWrap: "wrap",
                                    gap: 0.5,
                                    mt: 1,
                                  }}
                                >
                                  {selectedImage.aiComponents.map(
                                    (component, index) => (
                                      <Chip
                                        key={index}
                                        label={component}
                                        size="small"
                                        variant="outlined"
                                        className="ai-component-chip"
                                      />
                                    )
                                  )}
                                </Box>
                              </Box>
                            )}

                          {selectedImage.aiConcerns &&
                            selectedImage.aiConcerns.length > 0 && (
                              <Box sx={{ mb: 2 }}>
                                <Typography
                                  variant="body1"
                                  sx={{
                                    fontWeight: "medium",
                                    color: "warning.main",
                                  }}
                                >
                                  Potential Concerns:
                                </Typography>
                                <Box
                                  sx={{
                                    display: "flex",
                                    flexDirection: "column",
                                    gap: 0.5,
                                    mt: 1,
                                  }}
                                >
                                  {selectedImage.aiConcerns.map(
                                    (concern, index) => (
                                      <Typography
                                        key={index}
                                        variant="body2"
                                        className="ai-concern-item"
                                        sx={{
                                          display: "flex",
                                          alignItems: "center",
                                        }}
                                      >
                                        • {concern}
                                      </Typography>
                                    )
                                  )}
                                </Box>
                              </Box>
                            )}
                        </Grid>
                      </Grid>

                      {/* Display other metadata if available */}
                      {selectedImage.aiMetadata &&
                        Object.keys(selectedImage.aiMetadata).filter(
                          (key) => key !== "gpsLocation"
                        ).length > 0 && (
                          <Box sx={{ mb: 2 }}>
                            <Typography
                              variant="body1"
                              sx={{ fontWeight: "medium" }}
                            >
                              Additional Metadata:
                            </Typography>
                            <Box sx={{ mt: 1 }}>
                              {Object.entries(selectedImage.aiMetadata)
                                .filter(([key]) => key !== "gpsLocation")
                                .map(([key, value], index) => (
                                  <Typography
                                    key={index}
                                    variant="body2"
                                    color="text.secondary"
                                  >
                                    <strong>
                                      {key.charAt(0).toUpperCase() +
                                        key.slice(1)}
                                      :
                                    </strong>{" "}
                                    {typeof value === "object"
                                      ? JSON.stringify(value)
                                      : value}
                                  </Typography>
                                ))}
                            </Box>
                          </Box>
                        )}

                      {/* Display analysis date if available */}
                      {selectedImage.aiAnalyzedAt && (
                        <Typography
                          variant="caption"
                          color="text.secondary"
                          sx={{ display: "block", mt: 2, fontStyle: "italic" }}
                        >
                          Analyzed on{" "}
                          {new Date(
                            selectedImage.aiAnalyzedAt
                          ).toLocaleString()}
                        </Typography>
                      )}
                    </Box>
                  )}
                </Box>
              ) : (
                // Default split view with custom resizable layout
                <Box
                  sx={{
                    display: "flex",
                    flexDirection: "column",
                    height: "100%",
                  }}
                >
                  {/* Image Container */}
                  <Box
                    sx={{
                      flex: fullscreenImage ? 1 : "70%",
                      overflow: "hidden",
                      display: "flex",
                      justifyContent: "center",
                      alignItems: "center",
                      bgcolor: "#000",
                      position: "relative",
                      minHeight: "200px",
                      transition: "flex 0.3s ease",
                    }}
                  >
                    {/* Zoom controls */}
                    <Box className="zoom-controls">
                      <Tooltip title="Zoom out">
                        <IconButton
                          size="small"
                          onClick={zoomOut}
                          sx={{ color: "white" }}
                        >
                          <ZoomOutIcon />
                        </IconButton>
                      </Tooltip>
                      <Typography
                        variant="body2"
                        sx={{
                          color: "white",
                          display: "flex",
                          alignItems: "center",
                          mx: 1,
                        }}
                      >
                        {Math.round(zoomLevel * 100)}%
                      </Typography>
                      <Tooltip title="Zoom in">
                        <IconButton
                          size="small"
                          onClick={zoomIn}
                          sx={{ color: "white" }}
                        >
                          <ZoomInIcon />
                        </IconButton>
                      </Tooltip>
                      <Tooltip title="Reset zoom">
                        <IconButton
                          size="small"
                          onClick={resetZoom}
                          sx={{ color: "white", ml: 1 }}
                        >
                          <ResetIcon />
                        </IconButton>
                      </Tooltip>
                    </Box>

                    <Box
                      className={`zoom-image-container ${
                        zoomLevel > 1 ? "draggable" : ""
                      } ${isDragging ? "dragging" : ""}`}
                      onMouseDown={handleMouseDown}
                      onMouseMove={handleMouseMove}
                      onMouseUp={handleMouseUp}
                      onMouseLeave={handleMouseLeave}
                      onWheel={handleWheel}
                    >
                      <img
                        className={`zoom-image ${isDragging ? "dragging" : ""}`}
                        src={
                          selectedImage.url ||
                          selectedImage.fullSizeUrl ||
                          selectedImage.thumbnailUrl
                        }
                        alt={selectedImage.title || "Customer image"}
                        style={{
                          maxWidth: zoomLevel === 1 ? "100%" : "none",
                          maxHeight: zoomLevel === 1 ? "100%" : "none",
                          width:
                            zoomLevel === 1 ? "auto" : `${100 * zoomLevel}%`,
                          height: zoomLevel === 1 ? "auto" : "auto",
                          objectFit: "contain",
                          display: "block",
                          transform: `translate(${imagePosition.x}px, ${imagePosition.y}px)`,
                        }}
                      />
                    </Box>
                  </Box>

                  {/* Resizable divider */}
                  <Box
                    sx={{
                      height: "10px",
                      backgroundColor: "rgba(0,0,0,0.05)",
                      cursor: "ns-resize",
                      borderTop: "1px solid rgba(0,0,0,0.1)",
                      borderBottom: "1px solid rgba(0,0,0,0.1)",
                      "&:hover": {
                        backgroundColor: "rgba(25, 118, 210, 0.2)",
                      },
                    }}
                    onMouseDown={(e) => {
                      const startY = e.clientY;
                      const container = e.currentTarget.parentElement;
                      const imageContainer = container.firstChild;
                      const detailsContainer = container.lastChild;
                      const startHeight = imageContainer.offsetHeight;
                      const totalHeight = container.offsetHeight - 10; // Subtract divider height

                      const handleMouseMove = (moveEvent) => {
                        const deltaY = moveEvent.clientY - startY;
                        const newImageHeight = Math.max(
                          200,
                          Math.min(totalHeight - 100, startHeight + deltaY)
                        );
                        const imagePercent =
                          (newImageHeight / totalHeight) * 100;
                        const detailsPercent = 100 - imagePercent;

                        imageContainer.style.flex = `${imagePercent}%`;
                        detailsContainer.style.flex = `${detailsPercent}%`;
                      };

                      const handleMouseUp = () => {
                        document.removeEventListener(
                          "mousemove",
                          handleMouseMove
                        );
                        document.removeEventListener("mouseup", handleMouseUp);
                      };

                      document.addEventListener("mousemove", handleMouseMove);
                      document.addEventListener("mouseup", handleMouseUp);
                    }}
                  />

                  {/* Metadata Panel */}
                  <Box
                    sx={{
                      flex: "30%",
                      overflow: "auto",
                      borderTop: "1px solid rgba(0,0,0,0.12)",
                      minHeight: "100px",
                      transition: "flex 0.3s ease",
                    }}
                  >
                    <Box sx={{ p: 2 }}>
                      {/* Date */}
                      <Typography
                        variant="body2"
                        color="text.secondary"
                        sx={{ mb: 1 }}
                      >
                        {new Date(
                          selectedImage.date ||
                            selectedImage.createdAt ||
                            Date.now()
                        ).toLocaleDateString()}
                      </Typography>

                      {/* Description section */}
                      <Box sx={{ mb: 2 }}>
                        <Typography
                          variant="body1"
                          sx={{ fontWeight: "medium", mb: 0.5 }}
                        >
                          Description
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          {selectedImage.description ||
                            "No description available"}
                        </Typography>
                      </Box>

                      {/* AI Analysis section */}
                      {(selectedImage.aiCategory ||
                        selectedImage.aiComponents ||
                        selectedImage.aiConcerns ||
                        selectedImage.aiLocation ||
                        selectedImage.aiMetadata) && (
                        <Box className="ai-analysis-section" sx={{ mb: 1 }}>
                          <Typography
                            variant="body1"
                            sx={{ fontWeight: "medium", mb: 0.5 }}
                          >
                            AI Analysis{" "}
                            {selectedImage.aiConfidence
                              ? `(${Math.round(
                                  selectedImage.aiConfidence * 100
                                )}% confidence)`
                              : ""}
                          </Typography>

                          <Grid container spacing={2}>
                            {/* Left column */}
                            <Grid item xs={12} md={6}>
                              {selectedImage.aiCategory && (
                                <Box sx={{ mb: 1 }}>
                                  <Typography
                                    variant="body2"
                                    sx={{ fontWeight: "medium" }}
                                  >
                                    Category:
                                  </Typography>
                                  <Typography
                                    variant="body2"
                                    color="text.secondary"
                                  >
                                    {selectedImage.aiCategory}
                                  </Typography>
                                </Box>
                              )}

                              {selectedImage.aiLocation && (
                                <Box sx={{ mb: 1 }}>
                                  <Typography
                                    variant="body2"
                                    sx={{ fontWeight: "medium" }}
                                  >
                                    Location:
                                  </Typography>
                                  <Typography
                                    variant="body2"
                                    color="text.secondary"
                                  >
                                    {selectedImage.aiLocation}
                                  </Typography>
                                </Box>
                              )}

                              {/* Display GPS location if available */}
                              {selectedImage.aiMetadata &&
                                selectedImage.aiMetadata.gpsLocation && (
                                  <Box sx={{ mb: 1 }}>
                                    <Typography
                                      variant="body2"
                                      sx={{ fontWeight: "medium" }}
                                    >
                                      GPS Location:
                                    </Typography>
                                    <Typography
                                      variant="body2"
                                      color="text.secondary"
                                    >
                                      {
                                        selectedImage.aiMetadata.gpsLocation
                                          .formatted
                                      }
                                    </Typography>
                                  </Box>
                                )}
                            </Grid>

                            {/* Right column */}
                            <Grid item xs={12} md={6}>
                              {selectedImage.aiComponents &&
                                selectedImage.aiComponents.length > 0 && (
                                  <Box sx={{ mb: 1 }}>
                                    <Typography
                                      variant="body2"
                                      sx={{ fontWeight: "medium" }}
                                    >
                                      Components Identified:
                                    </Typography>
                                    <Box
                                      sx={{
                                        display: "flex",
                                        flexWrap: "wrap",
                                        gap: 0.5,
                                        mt: 0.5,
                                      }}
                                    >
                                      {selectedImage.aiComponents.map(
                                        (component, index) => (
                                          <Chip
                                            key={index}
                                            label={component}
                                            size="small"
                                            variant="outlined"
                                            className="ai-component-chip"
                                          />
                                        )
                                      )}
                                    </Box>
                                  </Box>
                                )}

                              {selectedImage.aiConcerns &&
                                selectedImage.aiConcerns.length > 0 && (
                                  <Box sx={{ mb: 1 }}>
                                    <Typography
                                      variant="body2"
                                      sx={{
                                        fontWeight: "medium",
                                        color: "warning.main",
                                      }}
                                    >
                                      Potential Concerns:
                                    </Typography>
                                    <Box
                                      sx={{
                                        display: "flex",
                                        flexDirection: "column",
                                        gap: 0.5,
                                        mt: 0.5,
                                      }}
                                    >
                                      {selectedImage.aiConcerns.map(
                                        (concern, index) => (
                                          <Typography
                                            key={index}
                                            variant="body2"
                                            className="ai-concern-item"
                                            sx={{
                                              display: "flex",
                                              alignItems: "center",
                                            }}
                                          >
                                            • {concern}
                                          </Typography>
                                        )
                                      )}
                                    </Box>
                                  </Box>
                                )}
                            </Grid>
                          </Grid>

                          {/* Display other metadata if available */}
                          {selectedImage.aiMetadata &&
                            Object.keys(selectedImage.aiMetadata).filter(
                              (key) => key !== "gpsLocation"
                            ).length > 0 && (
                              <Box sx={{ mb: 1 }}>
                                <Typography
                                  variant="body2"
                                  sx={{ fontWeight: "medium" }}
                                >
                                  Additional Metadata:
                                </Typography>
                                <Box sx={{ mt: 0.5 }}>
                                  {Object.entries(selectedImage.aiMetadata)
                                    .filter(([key]) => key !== "gpsLocation")
                                    .map(([key, value], index) => (
                                      <Typography
                                        key={index}
                                        variant="body2"
                                        color="text.secondary"
                                      >
                                        <strong>
                                          {key.charAt(0).toUpperCase() +
                                            key.slice(1)}
                                          :
                                        </strong>{" "}
                                        {typeof value === "object"
                                          ? JSON.stringify(value)
                                          : value}
                                      </Typography>
                                    ))}
                                </Box>
                              </Box>
                            )}

                          {/* Display analysis date if available */}
                          {selectedImage.aiAnalyzedAt && (
                            <Typography
                              variant="caption"
                              color="text.secondary"
                              sx={{
                                display: "block",
                                mt: 1,
                                fontStyle: "italic",
                              }}
                            >
                              Analyzed on{" "}
                              {new Date(
                                selectedImage.aiAnalyzedAt
                              ).toLocaleString()}
                            </Typography>
                          )}
                        </Box>
                      )}
                    </Box>
                  </Box>
                </Box>
              )}
            </Box>
          </>
        )}
      </Dialog>
    </div>
  );
};

export default ImageGallery;
