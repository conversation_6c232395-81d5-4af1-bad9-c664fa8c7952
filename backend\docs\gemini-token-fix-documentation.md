# Gemini Model Token Limits Fix Documentation

## Issue Summary

The Gemini 2.5 Pro model was returning "Empty AI response" errors due to overly restrictive token limits (100-500 tokens), which prevented the model from generating complete JSON responses.

## Root Cause

- Token limits were set too low across multiple service files
- Gemini 2.5 Pro supports up to 65,536 output tokens but was restricted to 100-500 tokens
- Insufficient token budget caused responses to be truncated or empty

## Changes Implemented

### 1. Token Configuration Guidelines (`backend/config/tokenConfig.js`)

Created a centralized configuration file with:

- Model-specific token limits
- Use case recommendations
- Helper functions for safe token limits

### 2. Updated Token Limits

#### jobController.js

- `calculatePriorityScore`: 100 → 500 tokens
- `suggestNextSteps`: 300 → 1000 tokens

#### aiController.js

- Customer descriptions: 300 → 1000 tokens
- Customer insights: 500 → 1500 tokens
- Service recommendations: 400 → 1200 tokens

#### aiService.js

- Material descriptions: 500 → 1000 tokens
- JSON responses: 2000 → 4000 tokens

#### templateGenerator.js

- Email templates: 1000 → 3000 tokens

## Token Recommendations by Use Case

| Use Case                | Recommended Tokens | Rationale                              |
| ----------------------- | ------------------ | -------------------------------------- |
| Priority scoring        | 500                | Simple numeric/category output         |
| Material descriptions   | 1000               | 1-2 sentence descriptions              |
| Customer insights       | 3000               | Detailed analysis with multiple points |
| Service recommendations | 2500               | List of services with descriptions     |
| Next steps              | 2000               | Action items with details              |
| Email templates         | 3000               | Full email content with formatting     |
| JSON responses          | 4000               | Structured data with multiple fields   |
| Complex reports         | 8000               | Multi-section detailed reports         |

## Testing Recommendations

1. **Verify JSON Response Completeness**

   ```bash
   # Monitor logs for successful JSON parsing
   tail -f backend/logs/backend.log | grep "AI response"
   ```

2. **Test Each Updated Endpoint**

   - Priority calculation endpoint
   - Customer insights generation
   - Service recommendations
   - Material descriptions
   - Email template generation

3. **Monitor Token Usage**
   - Check `usageMetadata` in responses
   - Ensure actual usage doesn't exceed limits
   - Adjust if patterns show consistent under/over usage

## Best Practices

1. **Never set tokens below 500 for JSON responses** - JSON structure overhead requires minimum space
2. **Use configuration file** - Reference `tokenConfig.js` for consistency
3. **Consider response complexity** - More complex structures need more tokens
4. **Monitor and adjust** - Track actual usage patterns and refine limits

## Rollback Plan

If issues persist:

1. Check logs for specific error patterns
2. Increase token limits incrementally (2x current values)
3. Verify model name matches configuration
4. Check API quota/rate limits

## References

- [Google Gemini API Documentation](https://ai.google.dev/api/rest/v1/models)
- Gemini 2.5 Pro: 65,536 max output tokens
- Property name: `maxOutputTokens` (mapped from `maxTokens` in our code)

## Change Log

- **Date**: Current session
- **Author**: System update
- **Files Modified**: 5 backend files
- **New Files**: 2 (tokenConfig.js, this documentation)
- **Impact**: Resolves empty AI response errors, improves response completeness
