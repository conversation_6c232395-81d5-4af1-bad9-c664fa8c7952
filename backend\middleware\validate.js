const ApiError = require("../utils/ApiError");

/**
 * Request validation middleware
 * @param {Function|Object} schema - Validation schema or validation function
 * @param {Object} options - Validation options
 */
function validate(schema, options = {}) {
  return async (req, res, next) => {
    try {
      let validationFn;

      // If schema is a function, create a wrapper that intelligently handles both
      // direct data validation functions and those expecting wrapped objects
      if (typeof schema === "function") {
        validationFn = async (data) => {
          try {
            // First try with the wrapped data (in case the function expects { body, query, params })
            const result = await schema(data);
            return result;
          } catch (error) {
            // If validation failed, it might be because the function expects unwrapped data
            // Try with body directly if it exists (most common case)
            if (data.body) {
              try {
                const unwrappedResult = await schema(data.body);
                // Structure the result to match the expected format with body property
                return {
                  value: {
                    body: unwrappedResult,
                  },
                };
              } catch (innerError) {
                // If both attempts fail, return the original error
                throw error;
              }
            } else {
              // If no body property, just throw the original error
              throw error;
            }
          }
        };
      }
      // If schema is an object with validate method (e.g., Jo<PERSON> schema)
      else if (schema && typeof schema.validate === "function") {
        validationFn = (data) => schema.validate(data, options);
      }
      // If schema is a validation config object
      else if (typeof schema === "object") {
        validationFn = (data) => validateWithConfig(data, schema);
      } else {
        throw new Error("Invalid validation schema");
      }

      // Determine what to validate based on the request method
      const toValidate = {};

      if (req.body && Object.keys(req.body).length > 0) {
        toValidate.body = req.body;
      }

      if (req.query && Object.keys(req.query).length > 0) {
        toValidate.query = req.query;
      }

      if (req.params && Object.keys(req.params).length > 0) {
        toValidate.params = req.params;
      }

      // Run validation
      const { error, value } = await validationFn(toValidate);

      if (error) {
        throw new ApiError(
          400,
          "Validation Error",
          formatValidationError(error)
        );
      }

      // Replace request data with validated data
      if (value.body) req.body = value.body;
      if (value.query) req.query = value.query;
      if (value.params) req.params = value.params;

      next();
    } catch (error) {
      next(error);
    }
  };
}

/**
 * Format validation errors into a consistent structure
 * @param {Object} error - Validation error object
 * @returns {Object} - Formatted error object
 */
function formatValidationError(error) {
  // Handle different error formats based on validation library
  if (error.details && Array.isArray(error.details)) {
    // This is for Joi-like errors
    return error.details.reduce((acc, detail) => {
      const path = detail.path.join(".");
      acc[path] = detail.message;
      return acc;
    }, {});
  }

  // For custom validators or other formats
  return error.message || "Validation failed";
}

/**
 * Validate using a config object
 * @param {Object} data - Data to validate
 * @param {Object} config - Validation config
 * @returns {Object} - Validation result
 */
function validateWithConfig(data, config) {
  const errors = {};

  Object.keys(config).forEach((key) => {
    if (data[key]) {
      const fieldConfig = config[key];
      if (fieldConfig.validate && typeof fieldConfig.validate === "function") {
        const result = fieldConfig.validate(data[key]);
        if (result !== true) {
          errors[key] = result || `${key} is invalid`;
        }
      }
    } else if (config[key].required) {
      errors[key] = `${key} is required`;
    }
  });

  if (Object.keys(errors).length > 0) {
    return { error: { details: errors } };
  }

  return { value: data };
}

/**
 * Common validation rules
 */
const rules = {
  objectId: {
    pattern: /^[0-9a-fA-F]{24}$/,
    message: "Invalid ID format",
  },
  email: {
    pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
    message: "Invalid email format",
  },
  phone: {
    pattern: /^\+?[\d\s-()]+$/,
    message: "Invalid phone number format",
  },
  password: {
    minLength: 8,
    pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/,
    message:
      "Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character",
  },
  url: {
    pattern: /^https?:\/\/[^\s$.?#].[^\s]*$/,
    message: "Invalid URL format",
  },
};

/**
 * Validation helper functions
 */
const helpers = {
  isValidDate: (value) => !isNaN(new Date(value).getTime()),
  isInFuture: (value) => new Date(value) > new Date(),
  isInPast: (value) => new Date(value) < new Date(),
  isCurrency: (value) => /^\d+(\.\d{1,2})?$/.test(value),
  isPositive: (value) => Number(value) > 0,
  isArray: (value) => Array.isArray(value),
  isNonEmptyArray: (value) => Array.isArray(value) && value.length > 0,
};

module.exports = {
  validate,
  formatValidationError,
  validateWithConfig,
  rules,
  helpers,
};
