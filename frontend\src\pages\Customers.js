import React, { useEffect, useState, useCallback } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import {
  Box,
  Typography,
  Button,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  TextField,
  InputAdornment,
  IconButton,
  Chip,
  CircularProgress,
  Alert,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
} from "@mui/material";
import AddIcon from "@mui/icons-material/Add";
import SearchIcon from "@mui/icons-material/Search";
import EditIcon from "@mui/icons-material/Edit";
import DeleteIcon from "@mui/icons-material/Delete";
import VisibilityIcon from "@mui/icons-material/Visibility";
import {
  getCustomers,
  deleteCustomer,
  resetCustomerState,
} from "../slices/customerSlice";
import AiCustomerFilter from "../components/AiCustomerFilter";

const Customers = () => {
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [searchTerm, setSearchTerm] = useState("");
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [customerToDelete, setCustomerToDelete] = useState(null);
  const [selectedTags, setSelectedTags] = useState([]);
  // eslint-disable-next-line no-unused-vars
  const [availableTags, setAvailableTags] = useState([
    "Commercial",
    "Residential",
    "VIP",
    "Regular",
    "New",
  ]);
  const [aiFilteredCustomers, setAiFilteredCustomers] = useState(null);

  const dispatch = useDispatch();
  const navigate = useNavigate();

  const { customers, loading, error, success, totalPages } = useSelector(
    (state) => state.customers
  );

  const loadCustomers = useCallback(() => {
    dispatch(
      getCustomers({
        page: page + 1,
        limit: rowsPerPage,
        search: searchTerm,
        tags: selectedTags.join(","),
      })
    );
  }, [dispatch, page, rowsPerPage, searchTerm, selectedTags]);

  useEffect(() => {
    dispatch(resetCustomerState());
    loadCustomers();
  }, [dispatch, loadCustomers]);

  useEffect(() => {
    if (success) {
      setDeleteDialogOpen(false);
      setCustomerToDelete(null);
    }
  }, [success]);

  // Handle customers filtered by AI component
  const handleAiFilteredCustomers = (filteredResults) => {
    setAiFilteredCustomers(filteredResults);
  };

  const handleTagSelect = (tag) => {
    setSelectedTags((prevTags) => {
      if (prevTags.includes(tag)) {
        return prevTags.filter((t) => t !== tag);
      } else {
        return [...prevTags, tag];
      }
    });
    setPage(0);
  };

  const handleChangePage = (event, newPage) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const handleSearch = (e) => {
    e.preventDefault();
    setPage(0);
    loadCustomers();
  };

  const handleViewCustomer = (id) => {
    navigate(`/customers/${id}`);
  };

  const handleEditCustomer = (id) => {
    navigate(`/customers/${id}/edit`);
  };

  const handleDeleteClick = (customer) => {
    setCustomerToDelete(customer);
    setDeleteDialogOpen(true);
  };

  const confirmDelete = () => {
    if (customerToDelete) {
      dispatch(deleteCustomer(customerToDelete._id));
    }
  };

  const handleAddCustomer = () => {
    navigate("/customers/create");
  };

  // Determine which customers to display: AI filtered or regular list
  const displayedCustomers = aiFilteredCustomers || customers;

  return (
    <Box>
      <Box sx={{ display: "flex", justifyContent: "space-between", mb: 3 }}>
        <Typography variant="h4">Customers</Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={handleAddCustomer}
        >
          Add Customer
        </Button>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {/* AI Customer Filter Component */}
      <AiCustomerFilter
        customers={customers}
        onFilteredResultsChange={handleAiFilteredCustomers}
      />

      {/* Traditional search/filter (could be hidden when AI filter is active) */}
      {!aiFilteredCustomers && (
        <Paper sx={{ mb: 3 }}>
          <Box component="form" onSubmit={handleSearch} sx={{ p: 2 }}>
            <TextField
              fullWidth
              placeholder="Search customers by name, email, or phone..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon />
                  </InputAdornment>
                ),
                endAdornment: (
                  <InputAdornment position="end">
                    <Button type="submit" variant="contained" size="small">
                      Search
                    </Button>
                  </InputAdornment>
                ),
              }}
            />

            {/* Tag filtering */}
            <Box sx={{ mt: 2 }}>
              <Typography variant="subtitle2" sx={{ mb: 1 }}>
                Filter by tags:
              </Typography>
              <Box sx={{ display: "flex", flexWrap: "wrap", gap: 1 }}>
                {availableTags.map((tag) => (
                  <Chip
                    key={tag}
                    label={tag}
                    onClick={() => handleTagSelect(tag)}
                    color={selectedTags.includes(tag) ? "primary" : "default"}
                    variant={selectedTags.includes(tag) ? "filled" : "outlined"}
                    size="small"
                  />
                ))}
                {selectedTags.length > 0 && (
                  <Chip
                    label="Clear Filters"
                    onClick={() => setSelectedTags([])}
                    color="secondary"
                    size="small"
                  />
                )}
              </Box>
            </Box>
          </Box>
        </Paper>
      )}

      <TableContainer component={Paper}>
        {loading ? (
          <Box sx={{ display: "flex", justifyContent: "center", p: 3 }}>
            <CircularProgress />
          </Box>
        ) : (displayedCustomers || []).length === 0 ? (
          <Box sx={{ p: 3, textAlign: "center" }}>
            <Typography variant="body1">No customers found.</Typography>
            <Button
              variant="contained"
              startIcon={<AddIcon />}
              onClick={handleAddCustomer}
              sx={{ mt: 2 }}
            >
              Add Your First Customer
            </Button>
          </Box>
        ) : (
          <>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Name</TableCell>
                  <TableCell>Contact Person</TableCell>
                  <TableCell>Email</TableCell>
                  <TableCell>Phone</TableCell>
                  <TableCell>Tags</TableCell>
                  <TableCell align="right">Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {(displayedCustomers || []).map((customer) => (
                  <TableRow key={customer._id}>
                    <TableCell>
                      {customer.businessName ||
                        `${customer.contactPerson.firstName} ${customer.contactPerson.lastName}`}
                    </TableCell>
                    <TableCell>
                      {`${customer.contactPerson.firstName} ${customer.contactPerson.lastName}`}
                      {customer.contactPerson.position &&
                        ` (${customer.contactPerson.position})`}
                    </TableCell>
                    <TableCell>{customer.email}</TableCell>
                    <TableCell>{customer.phone}</TableCell>
                    <TableCell>
                      <Box sx={{ display: "flex", flexWrap: "wrap", gap: 0.5 }}>
                        {customer.tags &&
                          customer.tags.map((tag) => (
                            <Chip
                              key={tag}
                              label={tag}
                              size="small"
                              color="primary"
                              variant="outlined"
                            />
                          ))}
                      </Box>
                    </TableCell>
                    <TableCell align="right">
                      <IconButton
                        color="primary"
                        onClick={() => handleViewCustomer(customer._id)}
                      >
                        <VisibilityIcon />
                      </IconButton>
                      <IconButton
                        color="secondary"
                        onClick={() => handleEditCustomer(customer._id)}
                      >
                        <EditIcon />
                      </IconButton>
                      <IconButton
                        color="error"
                        onClick={() => handleDeleteClick(customer)}
                      >
                        <DeleteIcon />
                      </IconButton>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>

            {/* Only show pagination for regular customers (not AI filtered) */}
            {!aiFilteredCustomers && (
              <TablePagination
                rowsPerPageOptions={[5, 10, 25]}
                component="div"
                count={
                  totalPages && totalPages > 0 ? totalPages * rowsPerPage : 0
                }
                rowsPerPage={rowsPerPage}
                page={page}
                onPageChange={handleChangePage}
                onRowsPerPageChange={handleChangeRowsPerPage}
              />
            )}
          </>
        )}
      </TableContainer>

      {/* Delete Confirmation Dialog */}
      <Dialog
        open={deleteDialogOpen}
        onClose={() => setDeleteDialogOpen(false)}
      >
        <DialogTitle>Confirm Delete</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Are you sure you want to delete{" "}
            {customerToDelete && (
              <strong>
                {customerToDelete.businessName ||
                  `${customerToDelete.contactPerson.firstName} ${customerToDelete.contactPerson.lastName}`}
              </strong>
            )}
            ? This action cannot be undone.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialogOpen(false)}>Cancel</Button>
          <Button onClick={confirmDelete} color="error" autoFocus>
            Delete
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default Customers;
