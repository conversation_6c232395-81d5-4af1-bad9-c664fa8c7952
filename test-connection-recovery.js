#!/usr/bin/env node

/**
 * Test script to verify MongoDB connection recovery functionality
 */

require('dotenv').config();
const mongoose = require('mongoose');
const { setTimeout } = require('timers/promises');

async function testConnectionRecovery() {
  console.log('Testing MongoDB connection recovery...');
  
  try {
    // Connect to MongoDB
    console.log('Connecting to MongoDB...');
    await mongoose.connect(process.env.MONGODB_URI, {
      serverSelectionTimeoutMS: 5000,
      connectTimeoutMS: 5000
    });
    
    console.log('Connected successfully');
    
    // Test a simple query
    const adminDb = mongoose.connection.db.admin();
    const pingResult = await adminDb.ping();
    console.log('Ping result:', pingResult);
    
    // Simulate connection failure by stopping MongoDB
    console.log('\n=== Simulating connection failure ===');
    console.log('Please stop the MongoDB container manually to test recovery...');
    console.log('Run: docker stop workiz-mongo-rs');
    console.log('Waiting 10 seconds for you to stop the container...');
    
    await setTimeout(10000);
    
    // Try to query after connection failure
    console.log('\nTesting query after simulated failure...');
    try {
      const postFailurePing = await adminDb.ping();
      console.log('Query succeeded after failure:', postFailurePing);
    } catch (error) {
      console.log('Query failed as expected:', error.message);
      
      // Test reconnection
      console.log('\nTesting reconnection...');
      await mongoose.connection.close();
      
      console.log('Please start the MongoDB container manually to test recovery...');
      console.log('Run: docker start workiz-mongo-rs');
      console.log('Waiting 10 seconds for you to start the container...');
      
      await setTimeout(10000);
      
      try {
        await mongoose.connect(process.env.MONGODB_URI);
        console.log('Reconnection successful!');
        
        const reconnectedPing = await mongoose.connection.db.admin().ping();
        console.log('Ping after reconnection:', reconnectedPing);
        
      } catch (reconnectError) {
        console.log('Reconnection failed:', reconnectError.message);
      }
    }
    
    await mongoose.connection.close();
    console.log('\nConnection recovery test completed');
    
  } catch (error) {
    console.error('Initial connection failed:', error.message);
  }
}

if (require.main === module) {
  testConnectionRecovery()
    .then(() => process.exit(0))
    .catch(error => {
      console.error('Test failed:', error);
      process.exit(1);
    });
}