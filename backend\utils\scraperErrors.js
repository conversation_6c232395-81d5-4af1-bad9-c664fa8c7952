/**
 * scraperErrors.js
 * Custom error classes for scraping operations.
 */
const ApiError = require("./ApiError");

class ScraperError extends Error {
  constructor(message, details = {}) {
    super(message);
    this.name = this.constructor.name;
    this.details = details; // To store context like URL, SKU, selector
    this.isScraperError = true; // Flag to identify scraper-specific errors
    Error.captureStackTrace(this, this.constructor);
  }
}

class CaptchaError extends ScraperError {
  constructor(message = "CAPTCHA detected", details = {}) {
    super(message, details);
    this.errorCode = "CAPTCHA_DETECTED";
  }
}

class SelectorError extends ScraperError {
  constructor(message = "CSS selector not found or failed", details = {}) {
    super(message, details);
    this.errorCode = "SELECTOR_ERROR";
    // details might include: { selector: '...', pageUrl: '...' }
  }
}

class ScrapingNetworkError extends ScraperError {
  constructor(message = "Network error during scraping", details = {}) {
    super(message, details);
    this.errorCode = "SCRAPING_NETWORK_ERROR";
    // details might include: { url: '...', httpStatus: '...' }
  }
}

class ProductNotFoundError extends ScraperError {
  constructor(message = "Product not found", details = {}) {
    super(message, details);
    this.errorCode = "PRODUCT_NOT_FOUND";
    // details might include: { query: '...', sku: '...', url: '...' }
  }
}

class PriceExtractionError extends ScraperError {
  constructor(
    message = "Failed to extract price from product page",
    details = {}
  ) {
    super(message, details);
    this.errorCode = "PRICE_EXTRACTION_ERROR";
  }
}

class PageStructureChangedError extends ScraperError {
  constructor(
    message = "Page structure appears to have changed, impacting selectors",
    details = {}
  ) {
    super(message, details);
    this.errorCode = "PAGE_STRUCTURE_CHANGED";
  }
}

class ScrapingTimeoutError extends ScraperError {
  constructor(message = "Scraping operation timed out", details = {}) {
    super(message, details);
    this.errorCode = "SCRAPING_TIMEOUT";
  }
}

// Could be used if a scraper needs login but it's not implemented or fails
class LoginRequiredError extends ScraperError {
  constructor(message = "Login required to access content", details = {}) {
    super(message, details);
    this.errorCode = "LOGIN_REQUIRED";
  }
}

module.exports = {
  ScraperError,
  CaptchaError,
  SelectorError,
  ScrapingNetworkError,
  ProductNotFoundError,
  PriceExtractionError,
  PageStructureChangedError,
  ScrapingTimeoutError,
  LoginRequiredError,
};
