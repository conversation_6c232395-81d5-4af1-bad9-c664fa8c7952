/**
 * AI Health Service
 * Centralized service for monitoring health of all AI providers
 */

const logger = require("../utils/logger");

// Import all AI service modules
const geminiService = require("../utils/geminiService");
const deepseekService = require("../utils/deepseekService");
const visionService = require("./visionService");

/**
 * Get health metrics for all AI services
 * @returns {Object} Aggregated health metrics
 */
function getAllHealthMetrics() {
  try {
    const services = {};
    const errors = [];

    // Get Gemini health metrics
    try {
      services.gemini = geminiService.getHealthMetrics();
    } catch (error) {
      logger.error("Error getting Gemini health metrics:", error);
      errors.push({ service: "gemini", error: error.message });
      services.gemini = { status: "ERROR", error: error.message };
    }

    // Get Deepseek health metrics
    try {
      services.deepseek = deepseekService.getHealthMetrics();
    } catch (error) {
      logger.error("Error getting Deepseek health metrics:", error);
      errors.push({ service: "deepseek", error: error.message });
      services.deepseek = { status: "ERROR", error: error.message };
    }

    // Get Vision service health metrics
    try {
      services.vision = visionService.getHealthMetrics();
    } catch (error) {
      logger.error("Error getting Vision service health metrics:", error);
      errors.push({ service: "vision", error: error.message });
      services.vision = { status: "ERROR", error: error.message };
    }

    // Calculate overall health status
    const overallStatus = calculateOverallStatus(services);

    // Calculate aggregated metrics
    const aggregatedMetrics = calculateAggregatedMetrics(services);

    return {
      status: overallStatus,
      services,
      aggregatedMetrics,
      errors: errors.length > 0 ? errors : undefined,
      timestamp: new Date().toISOString(),
    };
  } catch (error) {
    logger.error("Error in getAllHealthMetrics:", error);
    return {
      status: "ERROR",
      error: error.message,
      timestamp: new Date().toISOString(),
    };
  }
}

/**
 * Calculate overall health status based on individual service statuses
 * @param {Object} services - Object containing health metrics for each service
 * @returns {string} Overall status (HEALTHY, DEGRADED, CRITICAL, ERROR)
 */
function calculateOverallStatus(services) {
  const serviceStatuses = Object.values(services).map((service) => {
    // Handle different status formats
    if (service.status) return service.status;
    if (service.circuitBreakerState?.state === "OPEN") return "DEGRADED";
    if (service.error) return "ERROR";
    return "HEALTHY";
  });

  // Determine overall status
  if (serviceStatuses.every((status) => status === "HEALTHY")) {
    return "HEALTHY";
  } else if (
    serviceStatuses.some(
      (status) => status === "ERROR" || status === "CRITICAL"
    )
  ) {
    return "CRITICAL";
  } else if (serviceStatuses.some((status) => status === "DEGRADED")) {
    return "DEGRADED";
  }

  return "UNKNOWN";
}

/**
 * Calculate aggregated metrics across all services
 * @param {Object} services - Service health metrics
 * @returns {Object} Aggregated metrics
 */
function calculateAggregatedMetrics(services) {
  const metrics = {
    totalRequests: 0,
    successfulRequests: 0,
    failedRequests: 0,
    retryAttempts: 0,
    averageSuccessRate: 0,
    circuitBreakersOpen: 0,
    modelsAvailable: 0,
    totalModels: 0,
  };

  let serviceCount = 0;
  let totalSuccessRate = 0;

  Object.entries(services).forEach(([serviceName, serviceMetrics]) => {
    if (serviceMetrics.error) return;

    // Aggregate request counts
    metrics.totalRequests += serviceMetrics.totalRequests || 0;
    metrics.successfulRequests += serviceMetrics.successfulRequests || 0;
    metrics.failedRequests += serviceMetrics.failedRequests || 0;
    metrics.retryAttempts += serviceMetrics.retryAttempts || 0;

    // Check circuit breakers
    if (serviceMetrics.circuitBreakerState?.state === "OPEN") {
      metrics.circuitBreakersOpen++;
    }

    // Count models
    if (serviceMetrics.models) {
      const models = Array.isArray(serviceMetrics.models)
        ? serviceMetrics.models
        : Object.values(serviceMetrics.models);

      metrics.totalModels += models.length;
      metrics.modelsAvailable += models.filter(
        (m) => !m.circuitBreaker || m.circuitBreaker.state !== "OPEN"
      ).length;
    }

    // Calculate success rate for this service
    if (serviceMetrics.totalRequests > 0) {
      const successRate =
        (serviceMetrics.successfulRequests / serviceMetrics.totalRequests) *
        100;
      totalSuccessRate += successRate;
      serviceCount++;
    }
  });

  // Calculate average success rate
  if (serviceCount > 0) {
    metrics.averageSuccessRate = Math.round(totalSuccessRate / serviceCount);
  }

  return metrics;
}

/**
 * Get health status for a specific AI service
 * @param {string} serviceName - Name of the service (gemini, deepseek, vision)
 * @returns {Object} Health metrics for the specified service
 */
function getServiceHealth(serviceName) {
  try {
    switch (serviceName.toLowerCase()) {
      case "gemini":
        return geminiService.getHealthMetrics();
      case "deepseek":
        return deepseekService.getHealthMetrics();
      case "vision":
        return visionService.getHealthMetrics();
      default:
        throw new Error(`Unknown AI service: ${serviceName}`);
    }
  } catch (error) {
    logger.error(`Error getting health for service ${serviceName}:`, error);
    return {
      status: "ERROR",
      error: error.message,
      timestamp: new Date().toISOString(),
    };
  }
}

/**
 * Reset metrics for all AI services
 * @returns {Object} Reset confirmation
 */
function resetAllMetrics() {
  const results = {};

  try {
    // Reset Gemini metrics
    if (typeof geminiService.resetHealthMetrics === "function") {
      geminiService.resetHealthMetrics();
      results.gemini = "reset";
    }

    // Reset Deepseek metrics
    if (typeof deepseekService.resetHealthMetrics === "function") {
      deepseekService.resetHealthMetrics();
      results.deepseek = "reset";
    }

    // Reset Vision service metrics
    if (typeof visionService.resetHealthMetrics === "function") {
      visionService.resetHealthMetrics();
      results.vision = "reset";
    }

    logger.info("AI health metrics reset for all services");

    return {
      success: true,
      results,
      timestamp: new Date().toISOString(),
    };
  } catch (error) {
    logger.error("Error resetting AI health metrics:", error);
    return {
      success: false,
      error: error.message,
      timestamp: new Date().toISOString(),
    };
  }
}

/**
 * Get a summary of AI service availability
 * @returns {Object} Service availability summary
 */
function getServiceAvailability() {
  const health = getAllHealthMetrics();

  return {
    overall: health.status,
    services: Object.entries(health.services).reduce((acc, [name, metrics]) => {
      acc[name] = {
        available:
          !metrics.error && metrics.circuitBreakerState?.state !== "OPEN",
        status:
          metrics.status ||
          (metrics.circuitBreakerState?.state === "OPEN"
            ? "DEGRADED"
            : "HEALTHY"),
        successRate:
          metrics.totalRequests > 0
            ? Math.round(
                (metrics.successfulRequests / metrics.totalRequests) * 100
              )
            : null,
      };
      return acc;
    }, {}),
    timestamp: health.timestamp,
  };
}

module.exports = {
  getAllHealthMetrics,
  getServiceHealth,
  resetAllMetrics,
  getServiceAvailability,
  calculateOverallStatus,
  calculateAggregatedMetrics,
};
