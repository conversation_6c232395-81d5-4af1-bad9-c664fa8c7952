/**
 * ScraperService.js
 * Service to manage material price scrapers
 * Enhanced with Crawl4AI integration for improved reliability and CAPTCHA handling
 */

const logger = require("../utils/logger");
const ApiError = require("../utils/ApiError");
const MaterialSource = require("../models/MaterialSource");
const PriceScrapingLog = require("../models/PriceScrapingLog");
const MaterialPriceHistory = require("../models/MaterialPriceHistory");
const priceCache = require("../utils/priceCache");
const { FuseJS } = require("../utils/fuseSearch");
const materialFetchLogger = require("../utils/materialFetchLogger");
const queryTransformer = require("../utils/queryTransformer");
const { circuitBreakers } = require("../utils/circuitBreaker");

// Import Crawl4AI scraper system only (no more legacy MCP-dependent scrapers)
const crawl4ai = require("./crawl4ai");
const { PlattCrawl4AIScraper } = require("./crawl4ai");

/**
 * Scraper service to manage material price scrapers
 */
class ScraperService {
  constructor() {
    this.scrapers = new Map();
    this.initialized = false;
    this.fuzzySimilarityThreshold = 0.3; // Threshold for fuzzy matching (0-1)
    this.sourceTypeMap = new Map(); // Map source type (e.g., 'HOME_DEPOT') to sourceId
  }

  /**
   * Initialize all enabled scrapers
   * @returns {Promise<void>}
   */
  async initialize() {
    if (this.initialized) {
      return;
    }

    try {
      // Clear any existing scrapers and map
      this.scrapers.clear();
      this.sourceTypeMap.clear();

      // Get all enabled material sources from the database
      const sources = await MaterialSource.find({ enabled: true });
      logger.info(`Found ${sources.length} enabled material sources in DB.`);
      // Log source details for debugging
      sources.forEach((s) =>
        logger.debug(
          `Enabled Source Found: ID=${s._id}, Name=${s.name}, Type=${s.type}`
        )
      );

      logger.info(`Initializing scrapers for ${sources.length} sources...`);

      // Initialize scrapers for each source
      for (const source of sources) {
        logger.debug(
          `Attempting to initialize scraper for source: ${source.name} (ID: ${source._id}, Type: ${source.type})`
        );
        try {
          const scraperInstance = await this.initializeScraper(source);
          if (scraperInstance) {
            logger.info(
              `Successfully initialized and added scraper for ${source.name}.`
            );
            this.sourceTypeMap.set(source.type, source._id.toString()); // Map type to ID
            logger.debug(
              `Source type map updated: ${
                source.type
              } -> ${source._id.toString()}`
            );
          } else {
            logger.warn(
              `initializeScraper returned null/falsy for ${source.name}, possibly disabled or unsupported type.`
            );
          }
        } catch (error) {
          // Log the specific error for this source but continue with others
          logger.error(
            `Error during initializeScraper call for ${source.name} (ID: ${source._id}):`,
            error.message
          );
          // Optionally log stack trace if needed: logger.error(error.stack);
        }
      }

      this.initialized = true; // Mark as initialized even if some scrapers failed
      logger.info(`ScraperService initialization loop finished.`);
      logger.info(`Final Scraper Count: ${this.scrapers.size}`);
      logger.info(
        `Final Scrapers Map Keys: ${JSON.stringify(
          Array.from(this.scrapers.keys())
        )}`
      );
      logger.info(
        `Final Source Type Map: ${JSON.stringify(
          Object.fromEntries(this.sourceTypeMap)
        )}`
      );
    } catch (error) {
      logger.error("Failed to initialize ScraperService:", error);
      throw error;
    }
  }

  /**
   * Initialize a scraper for a specific source
   * @param {Object} source - Material source document
   * @returns {Promise<Object>} - Scraper instance
   */
  async initializeScraper(source) {
    try {
      // Skip if the source is not enabled
      if (!source.enabled) {
        logger.warn(
          `Skipping initialization for disabled source: ${source.name} (ID: ${source._id})`
        );
        return null;
      }

      // Check if scraper is already initialized
      if (this.scrapers.has(source._id.toString())) {
        logger.debug(
          `Scraper for ${source.name} (ID: ${source._id}) already initialized. Returning existing instance.`
        );
        return this.scrapers.get(source._id.toString());
      }

      logger.debug(
        `[InitScraper:${source.name}] Attempting to instantiate scraper class for type: ${source.type}`
      );

      // Check if this source type is supported by Crawl4AI
      if (!crawl4ai.isSupported(source.type)) {
        logger.warn(
          `[InitScraper:${source.name}] Source type ${source.type} is not supported by Crawl4AI. Skipping.`
        );
        return null;
      }

      // Create scraper instance using Crawl4AI (only option now)
      let scraperInstance;
      try {
        logger.info(
          `[InitScraper:${source.name}] Creating Crawl4AI scraper for type: ${source.type}`
        );
        scraperInstance = crawl4ai.createScraper(source);
      } catch (crawl4aiError) {
        logger.error(
          `[InitScraper:${source.name}] Failed to create Crawl4AI scraper: ${crawl4aiError.message}`
        );
        return null;
      }

      logger.debug(
        `[InitScraper:${source.name}] Scraper class instantiated successfully.`
      );

      // Initialize the scraper
      logger.debug(
        `[InitScraper:${source.name}] Calling scraper.initialize()...`
      );
      await scraperInstance.initialize();
      logger.debug(
        `[InitScraper:${source.name}] scraper.initialize() completed.`
      );

      // Store the instance in the map
      this.scrapers.set(source._id.toString(), scraperInstance);
      logger.debug(
        `Added ${
          source.name
        } scraper instance to map with key ${source._id.toString()}.`
      );

      return scraperInstance;
    } catch (error) {
      // Log the full error object here as well for clarity
      logger.error(
        `[InitScraper:${source.name}] Failed during initializeScraper process:`,
        error
      );
      // Re-throw the original error or a new ApiError wrapping it
      if (error instanceof ApiError) {
        throw error;
      }
      throw new ApiError(
        500,
        `Failed to initialize scraper for ${source.name}: ${
          error.message || error
        }`
      );
    }
  }

  /**
   * Search for materials with enhanced query transformation, trying Home Depot first, then falling back to Platt.
   * @param {string} query - Search query
   * @param {Object} options - Search options (sourceId, skipCache, limit, etc.)
   * @returns {Promise<Array>} - Array of material objects
   */
  async searchMaterials(query, options = {}) {
    await this.ensureInitialized();
    logger.debug(
      `[ScraperService] searchMaterials (Enhanced with Query Transformation) called with query: "${query}", options: ${JSON.stringify(
        options
      )}`
    );

    // 🚀 CRITICAL FIX: Transform query for better electrical material matching
    const queryTransformations = queryTransformer.transformQuery(query, options);
    logger.info(`[ScraperService] Query transformation generated ${queryTransformations.variations.length} variations`, {
      originalQuery: query,
      detectedBrand: queryTransformations.metadata.detectedBrand,
      detectedType: queryTransformations.metadata.detectedType,
      confidence: queryTransformations.metadata.confidence
    });

    const homeDepotSourceId = this.sourceTypeMap.get("HOME_DEPOT");
    const plattSourceId = this.sourceTypeMap.get("PLATT");

    // If a specific source is requested, use only that
    if (options.sourceId) {
      logger.debug(
        `[ScraperService] Searching specific source: ${options.sourceId}`
      );
      return this.searchBySourceWithVariations(options.sourceId, queryTransformations, options);
    }

    let results = [];
    let primaryError = null;

    // 1. Try Home Depot (Primary)
    if (homeDepotSourceId && this.scrapers.has(homeDepotSourceId)) {
      logger.info(
        `[ScraperService Fallback] Attempting primary search: Home Depot (ID: ${homeDepotSourceId})`
      );
      try {
        results = await this.searchBySourceWithVariations(homeDepotSourceId, queryTransformations, options);
        // Define "Success" - has results
        if (results && results.length > 0) {
          logger.info(
            `[ScraperService Fallback] Home Depot search successful with ${results.length} results.`
          );
          // Apply post-processing (like fuzzy matching) only to successful primary results
          return this.processSearchResults(query, results, options);
        } else {
          logger.warn(
            `[ScraperService Fallback] Home Depot search returned no results for query: "${query}".`
          );
          primaryError = new Error(
            "Primary source (Home Depot) returned no results."
          ); // Store non-critical error
        }
      } catch (error) {
        logger.error(
          `[ScraperService Fallback] Error searching primary source (Home Depot ID: ${homeDepotSourceId}):`,
          error
        );
        primaryError = error; // Store critical error
      }
    } else {
      const errorMessage =
        "[ScraperService Fallback] Primary source (Home Depot) is not configured, enabled, or failed to initialize.";
      logger.error(errorMessage);
      // Throw an error immediately instead of just setting primaryError
      throw new ApiError(
        503,
        "Primary data source (Home Depot) is currently unavailable. Please try again later or contact support."
      );
    }

    // 2. Try Platt (Fallback) - RE-ENABLED with circuit breaker protection
    if (plattSourceId && this.scrapers.has(plattSourceId)) {
      logger.warn(`[ScraperService Fallback] Primary search failed or yielded no results. Attempting fallback: Platt (ID: ${plattSourceId})`);
      try {
        results = await this.searchBySourceWithVariations(plattSourceId, queryTransformations, options);
        if (results && results.length > 0) {
          logger.info(`[ScraperService Fallback] Platt search successful with ${results.length} results.`);
          // Apply post-processing to fallback results
          return this.processSearchResults(query, results, options);
        } else {
          logger.warn(`[ScraperService Fallback] Platt search also returned no results for query: "${query}".`);
        }
      } catch (fallbackError) {
        logger.error(`[ScraperService Fallback] Error searching fallback source (Platt ID: ${plattSourceId}):`, fallbackError);

        // Handle circuit breaker errors gracefully
        if (fallbackError.statusCode === 503 && fallbackError.message.includes('circuit breaker')) {
          logger.warn(`[ScraperService Fallback] Platt circuit breaker is open, skipping fallback`);
        } else {
          // If primary also had a critical error, rethrow that one, otherwise log fallback error
          if (primaryError && !(primaryError instanceof Error && primaryError.message.includes('returned no results'))) {
            throw primaryError;
          }
          // Don't rethrow fallback errors - just log them and continue
          logger.warn(`[ScraperService Fallback] Platt fallback failed but continuing: ${fallbackError.message}`);
        }
      }
    } else {
      logger.warn('[ScraperService Fallback] Platt source not configured or enabled for fallback.');
    }

    // If both failed or returned no results, return empty array (or rethrow primary error if it was critical)
    logger.warn(
      `[ScraperService Fallback] Both primary (Home Depot) and fallback (Platt) failed or returned no results for query: "${query}".`
    );
    if (
      primaryError &&
      !(
        primaryError instanceof Error &&
        primaryError.message.includes("returned no results")
      )
    ) {
      throw primaryError; // Rethrow if HD had a critical failure
    }
    return []; // Return empty if both yielded no results or Platt wasn't available
  }

  /**
   * Search by a specific source ID with query variations for better results
   * @param {string} sourceId - Source ID to search
   * @param {Object} queryTransformations - Query transformations from queryTransformer
   * @param {Object} options - Search options
   * @returns {Promise<Array>} - Array of material objects
   */
  async searchBySourceWithVariations(sourceId, queryTransformations, options = {}) {
    const { primary, variations } = queryTransformations;

    // Try primary query first
    logger.info(`[ScraperService] Trying primary query: "${primary}"`);
    let results = await this.searchBySource(sourceId, primary, options);

    if (results && results.length > 0) {
      logger.info(`[ScraperService] Primary query successful with ${results.length} results`);
      return results;
    }

    // If primary query failed, try variations in order of confidence
    for (const variation of variations) {
      logger.info(`[ScraperService] Trying variation (${variation.type}, confidence: ${variation.confidence}): "${variation.query}"`);

      try {
        results = await this.searchBySource(sourceId, variation.query, options);

        if (results && results.length > 0) {
          logger.info(`[ScraperService] Query variation "${variation.query}" successful with ${results.length} results`);
          return results;
        }
      } catch (error) {
        logger.warn(`[ScraperService] Query variation "${variation.query}" failed:`, error.message);
        // Continue to next variation
      }
    }

    logger.warn(`[ScraperService] All query variations failed for source ${sourceId}`);
    return [];
  }

  /**
   * Search for materials in a specific source
   * @param {string} sourceId - Source ID
   * @param {string} query - Search query
   * @param {Object} options - Search options
   * @returns {Promise<Array>} - Array of material objects
   */
  async searchBySource(sourceId, query, options = {}) {
    await this.ensureInitialized();

    const scraper = this.scrapers.get(sourceId);
    if (!scraper) {
      logger.warn(
        `[ScraperService] No scraper found or initialized for source ${sourceId}`
      );
      return []; // Or perhaps { results: [], sourceStatus: { status: 'NOT_FOUND', reason: 'Scraper not available' } } ? Returning array for now.
    }

    // --- Platt Specific Blocking Check ---
    if (
      scraper instanceof PlattCrawl4AIScraper &&
      scraper.isLikelyBlocked &&
      scraper.isLikelyBlocked()
    ) {
      logger.warn(
        `[ScraperService] Platt scraper (ID: ${sourceId}) is likely blocked. Skipping live search for query: "${query}".`
      );
      const diagnostics = scraper.getDiagnostics
        ? scraper.getDiagnostics()
        : { status: "BLOCKED", reason: "Platt scraper blocked" };
      // Search results are typically not cached, return empty results with status
      return { results: [], sourceStatus: diagnostics };
    }
    // --- End Platt Specific Check ---

    try {
      // Log to AI generation file if AI process is active, otherwise normal debug log
      if (logger.isAiGenerationActive) {
        logger.aiGenerationDebug(
          `[ScraperService] Calling searchByDescription on ${scraper.name} for query: "${query}"`
        );
      } else {
        logger.debug(
          `[ScraperService] Calling searchByDescription on ${scraper.name} for query: "${query}"`
        );
      }

      // Apply circuit breaker protection based on scraper type
      let sourceResults;
      const circuitBreakerKey = `${scraper.name.toLowerCase()}-${query}`;

      if (scraper.name.toLowerCase().includes('homedepot')) {
        sourceResults = await circuitBreakers.homeDepot.execute(
          () => scraper.searchByDescription(query, options),
          circuitBreakerKey
        );
      } else if (scraper.name.toLowerCase().includes('platt')) {
        sourceResults = await circuitBreakers.platt.execute(
          () => scraper.searchByDescription(query, options),
          circuitBreakerKey
        );
      } else {
        // Default circuit breaker for other scrapers
        sourceResults = await circuitBreakers.crawl4ai.execute(
          () => scraper.searchByDescription(query, options),
          circuitBreakerKey
        );
      }

      // Log to AI generation file if AI process is active, otherwise normal debug log
      if (logger.isAiGenerationActive) {
        logger.aiGenerationDebug(
          `[ScraperService] Received ${sourceResults.length} results from ${scraper.name}`
        );
      } else {
        logger.debug(
          `[ScraperService] Received ${sourceResults.length} results from ${scraper.name}`
        );
      }
      // Return standard results array if successful
      return sourceResults; // Assuming successful scrape doesn't need sourceStatus
    } catch (error) {
      logger.error(
        `[ScraperService] Error searching in ${scraper.name} for query "${query}":`,
        error
      );

      // --- Platt Specific Error Handling ---
      if (scraper instanceof PlattCrawl4AIScraper) {
        // Check if the error indicates blocking *after* the attempt
        if (scraper.isLikelyBlocked && scraper.isLikelyBlocked()) {
          logger.warn(
            `[ScraperService] Platt scraper (ID: ${sourceId}) reported blocking after search attempt for query: "${query}".`
          );
          const diagnostics = scraper.getDiagnostics
            ? scraper.getDiagnostics()
            : {
                status: "BLOCKED",
                reason: "Platt scraper blocked after attempt",
              };
          // Return empty results with status on blocking error
          return { results: [], sourceStatus: diagnostics };
        }
      }
      // --- End Platt Specific Error Handling ---

      // Re-throw other errors for the calling function (searchMaterials) to handle fallback
      throw error;
    }
  }

  /**
   * Process search results with post-processing like fuzzy matching
   * @param {string} query - Original search query
   * @param {Array} results - Search results
   * @param {Object} options - Processing options
   * @returns {Array} - Processed results
   */
  processSearchResults(query, results, options = {}) {
    if (!results || results.length === 0) {
      return [];
    }

    // Apply fuzzy searching if enabled and we have enough results
    if (options.fuzzyMatch !== false && results.length > 0) {
      // Create a FuseJS instance for fuzzy matching
      const fuse = new FuseJS(results, {
        keys: ["name", "sku", "description"],
        includeScore: true,
        threshold: this.fuzzySimilarityThreshold,
      });

      // Search with original query
      const fuzzyResults = fuse.search(query);

      // Return ranked results
      if (fuzzyResults.length > 0) {
        // Convert Fuse results back to our format and add fuzzy match score
        return fuzzyResults.map((result) => ({
          ...result.item,
          fuzzyMatchScore: 1 - result.score, // Convert to similarity (0-1)
          fuzzyRank: result.refIndex,
        }));
      }
    }

    // If fuzzy search didn't return anything or was disabled, return original results
    return results;
  }

  /**
   * Analyze search results to determine if user selection is needed
   * @param {string} query - Original search query
   * @param {Array} results - Search results
   * @param {Object} options - Analysis options
   * @returns {Object} - Analysis result with selection recommendation
   */
  analyzeResultsForUserSelection(query, results, options = {}) {
    if (!results || results.length === 0) {
      return { needsUserSelection: false, bestMatch: null, relevantResults: [] };
    }

    if (results.length === 1) {
      return {
        needsUserSelection: false,
        bestMatch: results[0],
        relevantResults: [results[0]],
        reason: "single_result"
      };
    }

    // Score each result for relevance to the query
    const scoredResults = results.map((result, index) => {
      const score = this.calculateRelevanceScore(query, result);
      return {
        ...result,
        relevanceScore: score,
        originalIndex: index
      };
    });

    // Sort by relevance score (highest first)
    scoredResults.sort((a, b) => b.relevanceScore - a.relevanceScore);

    const topScore = scoredResults[0].relevanceScore;
    const secondScore = scoredResults.length > 1 ? scoredResults[1].relevanceScore : 0;

    // Define thresholds for user selection
    const HIGH_RELEVANCE_THRESHOLD = 0.8;
    const SCORE_DIFFERENCE_THRESHOLD = 0.15;
    const MIN_RELEVANT_RESULTS = 2;
    const MAX_RELEVANT_RESULTS = 8;

    // Find all results with high relevance scores
    const highRelevanceResults = scoredResults.filter(
      result => result.relevanceScore >= HIGH_RELEVANCE_THRESHOLD
    );

    // Check if we have multiple highly relevant results with similar scores
    const similarScoreResults = scoredResults.filter(
      result => (topScore - result.relevanceScore) <= SCORE_DIFFERENCE_THRESHOLD
    );

    logger.info(`[ScraperService] Material selection analysis for "${query}":`, {
      totalResults: results.length,
      topScore: topScore.toFixed(3),
      secondScore: secondScore.toFixed(3),
      scoreDifference: (topScore - secondScore).toFixed(3),
      highRelevanceCount: highRelevanceResults.length,
      similarScoreCount: similarScoreResults.length
    });

    // Determine if user selection is needed
    const needsUserSelection = (
      highRelevanceResults.length >= MIN_RELEVANT_RESULTS &&
      highRelevanceResults.length <= MAX_RELEVANT_RESULTS &&
      similarScoreResults.length >= MIN_RELEVANT_RESULTS
    ) || (
      // Alternative condition: multiple results with decent scores and small difference
      scoredResults.length >= MIN_RELEVANT_RESULTS &&
      topScore >= 0.6 &&
      (topScore - secondScore) <= SCORE_DIFFERENCE_THRESHOLD
    );

    const relevantResults = needsUserSelection
      ? (highRelevanceResults.length >= MIN_RELEVANT_RESULTS ? highRelevanceResults : similarScoreResults)
      : [scoredResults[0]];

    return {
      needsUserSelection,
      bestMatch: scoredResults[0],
      relevantResults: relevantResults.slice(0, MAX_RELEVANT_RESULTS),
      reason: needsUserSelection
        ? `multiple_relevant_results (${relevantResults.length} options)`
        : "clear_best_match",
      analysisDetails: {
        totalResults: results.length,
        topScore,
        secondScore,
        scoreDifference: topScore - secondScore,
        highRelevanceCount: highRelevanceResults.length,
        similarScoreCount: similarScoreResults.length
      }
    };
  }

  /**
   * Calculate relevance score for a search result against the query
   * @param {string} query - Original search query
   * @param {Object} result - Search result to score
   * @returns {number} - Relevance score (0-1)
   */
  calculateRelevanceScore(query, result) {
    const queryLower = query.toLowerCase();
    const resultName = (result.name || '').toLowerCase();
    const resultDescription = (result.description || '').toLowerCase();
    const resultSku = (result.sku || '').toLowerCase();

    let score = 0;

    // Extract key terms from query
    const queryWords = queryLower.split(/\s+/).filter(word => word.length > 2);
    const resultWords = (resultName + ' ' + resultDescription).split(/\s+/);

    // Exact phrase match (highest weight)
    if (resultName.includes(queryLower) || resultDescription.includes(queryLower)) {
      score += 0.4;
    }

    // Word matching with different weights
    let wordMatchScore = 0;
    let exactWordMatches = 0;

    for (const queryWord of queryWords) {
      let wordFound = false;

      // Exact word match
      if (resultWords.some(word => word.toLowerCase() === queryWord)) {
        exactWordMatches++;
        wordMatchScore += 0.1;
        wordFound = true;
      }
      // Partial word match
      else if (resultWords.some(word => word.toLowerCase().includes(queryWord) || queryWord.includes(word.toLowerCase()))) {
        wordMatchScore += 0.05;
        wordFound = true;
      }

      // Bonus for important electrical terms
      if (wordFound && this.isImportantElectricalTerm(queryWord)) {
        wordMatchScore += 0.05;
      }
    }

    score += Math.min(wordMatchScore, 0.4); // Cap word matching contribution

    // Word coverage bonus
    const wordCoverage = queryWords.length > 0 ? exactWordMatches / queryWords.length : 0;
    score += wordCoverage * 0.15;

    // Price availability bonus
    if (result.price && result.price > 0) {
      score += 0.05;
    }

    // SKU availability bonus
    if (result.sku) {
      score += 0.03;
    }

    // URL/source reliability bonus
    if (result.url) {
      score += 0.02;
    }

    return Math.min(score, 1.0); // Ensure score doesn't exceed 1.0
  }

  /**
   * Check if a term is an important electrical specification
   * @param {string} term - Term to check
   * @returns {boolean} - True if it's an important electrical term
   */
  isImportantElectricalTerm(term) {
    const importantTerms = [
      'amp', 'amps', 'ampere', 'amperes',
      'volt', 'volts', 'voltage',
      'watt', 'watts', 'wattage',
      'awg', 'gauge',
      'inch', 'inches', 'ft', 'feet',
      'emt', 'conduit', 'wire', 'cable',
      'breaker', 'switch', 'outlet', 'receptacle',
      'panel', 'box', 'enclosure',
      'copper', 'aluminum', 'steel',
      'thhn', 'thwn', 'romex', 'mc',
      'pvc', 'metallic', 'non-metallic'
    ];

    return importantTerms.some(important =>
      term.includes(important) || important.includes(term)
    );
  }

  /**
   * Get material price by SKU, trying Home Depot first, then falling back to Platt.
   * @param {string} sku - Material SKU
   * @param {Object} options - Options (sourceId, skipCache, etc.)
   * @returns {Promise<Object>} - Material object
   */
  async getMaterialBySku(sku, options = {}) {
    await this.ensureInitialized();
    logger.debug(
      `[ScraperService] getMaterialBySku (Fallback Logic) called for SKU: "${sku}", options: ${JSON.stringify(
        options
      )}`
    );

    const homeDepotSourceId = this.sourceTypeMap.get("HOME_DEPOT");
    const plattSourceId = this.sourceTypeMap.get("PLATT");

    // If a specific source is requested, use only that
    if (options.sourceId) {
      logger.debug(
        `[ScraperService] Getting SKU from specific source: ${options.sourceId}`
      );
      return this.getMaterialBySkuFromSource(options.sourceId, sku, options);
    }

    let material = null;
    let primaryError = null;

    // 1. Try Home Depot (Primary)
    if (homeDepotSourceId && this.scrapers.has(homeDepotSourceId)) {
      logger.info(
        `[ScraperService Fallback] Attempting primary SKU lookup: Home Depot (ID: ${homeDepotSourceId}) for SKU: ${sku}`
      );
      try {
        material = await this.getMaterialBySkuFromSource(
          homeDepotSourceId,
          sku,
          options
        );
        // Define "Success" - returns a non-null object (scraper.getBySku should return null/throw on not found)
        if (material) {
          logger.info(
            `[ScraperService Fallback] Home Depot SKU lookup successful for SKU: ${sku}.`
          );
          return material; // Return immediately on success
        } else {
          // This case might not happen if getBySku throws 404, but handle defensively
          logger.warn(
            `[ScraperService Fallback] Home Depot SKU lookup returned null/undefined for SKU: ${sku}.`
          );
          primaryError = new ApiError(
            404,
            `Material with SKU ${sku} not found in primary source (Home Depot).`
          ); // Treat as not found
        }
      } catch (error) {
        logger.error(
          `[ScraperService Fallback] Error looking up SKU ${sku} from primary source (Home Depot ID: ${homeDepotSourceId}):`,
          error
        );
        primaryError = error; // Store error (could be 404 or 500)
      }
    } else {
      logger.warn(
        "[ScraperService Fallback] Home Depot source not configured or enabled for SKU lookup."
      );
      primaryError = new ApiError(
        404,
        "Primary source (Home Depot) not available."
      );
    }

    // 2. Try Platt (Fallback) if Home Depot failed (threw error or returned null)
    if (plattSourceId && this.scrapers.has(plattSourceId)) {
      logger.warn(
        `[ScraperService Fallback] Primary SKU lookup failed for ${sku}. Attempting fallback: Platt (ID: ${plattSourceId})`
      );
      try {
        material = await this.getMaterialBySkuFromSource(
          plattSourceId,
          sku,
          options
        );
        if (material) {
          logger.info(
            `[ScraperService Fallback] Platt SKU lookup successful for SKU: ${sku}.`
          );
          return material; // Return immediately on success
        } else {
          logger.warn(
            `[ScraperService Fallback] Platt SKU lookup also returned null/undefined for SKU: ${sku}.`
          );
          // If primary also failed with 404 or null, rethrow the primary 404 error for consistency.
          if (primaryError && primaryError.statusCode === 404) {
            throw primaryError;
          }
          // Otherwise, throw a generic Platt 404.
          throw new ApiError(
            404,
            `Material with SKU ${sku} not found in fallback source (Platt).`
          );
        }
      } catch (fallbackError) {
        logger.error(
          `[ScraperService Fallback] Error looking up SKU ${sku} from fallback source (Platt ID: ${plattSourceId}):`,
          fallbackError
        );
        // If primary also had a critical error (non-404), rethrow that one, otherwise throw fallback error
        if (primaryError && primaryError.statusCode !== 404) {
          throw primaryError;
        }
        throw fallbackError;
      }
    } else {
      logger.warn(
        "[ScraperService Fallback] Platt source not configured or enabled for fallback SKU lookup."
      );
    }

    // If both failed or Platt wasn't available, rethrow the primary error (which could be 404 or 500)
    logger.error(
      `[ScraperService Fallback] Could not find SKU ${sku} in primary (Home Depot) or fallback (Platt). Rethrowing primary error.`
    );
    throw (
      primaryError ||
      new ApiError(
        404,
        `Material with SKU ${sku} not found in any configured source.`
      )
    );
  }

  /**
   * Get material price by SKU from a specific source
   * @param {string} sourceId - Source ID
   * @param {string} sku - Material SKU
   * @param {Object} options - Options
   * @returns {Promise<Object>} - Material object
   */
  async getMaterialBySkuFromSource(sourceId, sku, options = {}) {
    await this.ensureInitialized();

    const scraper = this.scrapers.get(sourceId);
    if (!scraper) {
      logger.warn(`No scraper found for source ${sourceId}`);
      throw new ApiError(404, `Source ${sourceId} not found or disabled`);
    }

    const cacheKey = priceCache.getCacheKey(sku, sourceId); // Assuming a helper exists

    // --- Platt Specific Blocking Check ---
    if (
      scraper instanceof PlattCrawl4AIScraper &&
      scraper.isLikelyBlocked &&
      scraper.isLikelyBlocked()
    ) {
      logger.warn(
        `[ScraperService] Platt scraper (ID: ${sourceId}) is likely blocked. Skipping live SKU lookup for: "${sku}". Attempting cache.`
      );
      const diagnostics = scraper.getDiagnostics
        ? scraper.getDiagnostics()
        : { status: "BLOCKED", reason: "Platt scraper blocked" };
      const cachedData = await priceCache.get(cacheKey);
      if (cachedData) {
        logger.info(
          `[ScraperService] Returning cached data for blocked Platt SKU: ${sku}`
        );
      } else {
        logger.warn(
          `[ScraperService] No cached data found for blocked Platt SKU: ${sku}`
        );
      }
      // Return cached data (or null) along with diagnostics
      return { data: cachedData, sourceStatus: diagnostics };
    }
    // --- End Platt Specific Check ---

    try {
      logger.debug(
        `[ScraperService] Calling getBySku on ${scraper.name} for SKU: "${sku}"`
      );
      const materialData = await scraper.getBySku(sku, options);
      // Return standard material object if successful
      return materialData; // Assuming successful scrape doesn't need sourceStatus
    } catch (error) {
      logger.error(
        `[ScraperService] Error getting SKU ${sku} from ${scraper.name}:`,
        error
      );

      // --- Platt Specific Error Handling ---
      if (scraper instanceof PlattCrawl4AIScraper) {
        // Check if the error indicates blocking *after* the attempt
        if (scraper.isLikelyBlocked && scraper.isLikelyBlocked()) {
          logger.warn(
            `[ScraperService] Platt scraper (ID: ${sourceId}) reported blocking after SKU lookup attempt for: "${sku}". Attempting cache.`
          );
          const diagnostics = scraper.getDiagnostics
            ? scraper.getDiagnostics()
            : {
                status: "BLOCKED",
                reason: "Platt scraper blocked after SKU attempt",
              };
          const cachedData = await priceCache.get(cacheKey);
          if (cachedData) {
            logger.info(
              `[ScraperService] Returning cached data for blocked Platt SKU: ${sku} after failed attempt.`
            );
          } else {
            logger.warn(
              `[ScraperService] No cached data found for blocked Platt SKU: ${sku} after failed attempt.`
            );
          }
          // Return cached data (or null) with status on blocking error
          return { data: cachedData, sourceStatus: diagnostics };
        }
      }
      // --- End Platt Specific Error Handling ---

      // Rethrow other errors (like 404 Not Found) to preserve status code
      throw error;
    }
  }

  /**
   * Get material price by URL from appropriate source
   * @param {string} url - Material URL
   * @param {Object} options - Options
   * @returns {Promise<Object>} - Material object
   */
  async getMaterialByUrl(url, options = {}) {
    await this.ensureInitialized();

    // Determine which source to use based on URL
    const sourceId = this.getSourceIdFromUrl(url);
    if (!sourceId) {
      throw new ApiError(400, `Could not determine source for URL: ${url}`);
    }

    // Get the scraper for the determined source
    const scraper = this.scrapers.get(sourceId);
    if (!scraper) {
      logger.warn(`No scraper found for source ${sourceId}`);
      throw new ApiError(404, `Source for URL ${url} not found or disabled`);
    }

    // --- Platt Specific Blocking Check ---
    // Note: Caching by URL is less common than by SKU. We might need a way to extract SKU from URL if possible for caching.
    // For now, we'll just return diagnostics without cached data if blocked before attempt.
    if (
      scraper instanceof PlattCrawl4AIScraper &&
      scraper.isLikelyBlocked &&
      scraper.isLikelyBlocked()
    ) {
      logger.warn(
        `[ScraperService] Platt scraper (ID: ${sourceId}) is likely blocked. Skipping live URL lookup for: "${url}".`
      );
      const diagnostics = scraper.getDiagnostics
        ? scraper.getDiagnostics()
        : { status: "BLOCKED", reason: "Platt scraper blocked" };
      // Cannot reliably get cached data just from URL, return null data + diagnostics
      return { data: null, sourceStatus: diagnostics };
    }
    // --- End Platt Specific Check ---

    try {
      logger.debug(
        `[ScraperService] Calling getByUrl on ${scraper.name} for URL: "${url}"`
      );
      const materialData = await scraper.getByUrl(url, options);
      // Return standard material object if successful
      return materialData; // Assuming successful scrape doesn't need sourceStatus
    } catch (error) {
      logger.error(
        `[ScraperService] Error getting URL ${url} from ${scraper.name}:`,
        error
      );

      // --- Platt Specific Error Handling ---
      if (scraper instanceof PlattCrawl4AIScraper) {
        // Check if the error indicates blocking *after* the attempt
        if (scraper.isLikelyBlocked && scraper.isLikelyBlocked()) {
          logger.warn(
            `[ScraperService] Platt scraper (ID: ${sourceId}) reported blocking after URL lookup attempt for: "${url}".`
          );
          const diagnostics = scraper.getDiagnostics
            ? scraper.getDiagnostics()
            : {
                status: "BLOCKED",
                reason: "Platt scraper blocked after URL attempt",
              };
          // Cannot reliably get cached data just from URL, return null data + diagnostics
          return { data: null, sourceStatus: diagnostics };
        }
      }
      // --- End Platt Specific Error Handling ---

      // Rethrow other errors to preserve status code
      throw error;
    }
  }

  /**
   * Determine source ID from URL
   * @param {string} url - Material URL
   * @returns {string|null} - Source ID or null if not found
   */
  getSourceIdFromUrl(url) {
    try {
      const urlObj = new URL(url);
      const hostname = urlObj.hostname.toLowerCase();

      // Find source by hostname
      for (const [sourceId, scraper] of this.scrapers.entries()) {
        const sourceUrl = new URL(scraper.baseUrl);
        if (hostname.includes(sourceUrl.hostname)) {
          return sourceId;
        }
      }

      return null;
    } catch (error) {
      logger.error(`Error parsing URL ${url}:`, error);
      return null;
    }
  }

  /**
   * Check if a material price is anomalous
   * @param {string} sku - Material SKU
   * @param {number} price - Material price
   * @param {string} sourceId - Source ID
   * @returns {Promise<Object>} - Anomaly check result
   */
  async checkPriceAnomaly(sku, price, sourceId) {
    try {
      // Find price history for the material
      const priceHistory = await MaterialPriceHistory.findOne({
        material: sku, // Assuming history uses the normalized SKU (e.g., HD-12345)
        source: sourceId,
      });

      if (!priceHistory || priceHistory.prices.length < 3) {
        return { isAnomaly: false, reason: "Not enough price history" };
      }

      // Check for anomaly
      const anomalyResult = await priceHistory.detectPriceAnomaly(price);

      return anomalyResult;
    } catch (error) {
      logger.error(`Error checking price anomaly for SKU ${sku}:`, error);
      return { isAnomaly: false, reason: "Error checking anomaly" };
    }
  }

  /**
   * Determine the correct methodUsed value based on scraper type
   * @param {Object} scraper - Scraper instance
   * @returns {string} - Valid methodUsed enum value
   */
  getScraperMethod(scraper) {
    if (!scraper) {
      return "unknown";
    }

    // All scrapers now use Crawl4AI
    if (
      scraper.constructor &&
      scraper.constructor.name &&
      (scraper.constructor.name.includes("Crawl4AI") ||
        scraper.constructor.name.includes("crawl4ai"))
    ) {
      return "crawl4ai";
    }

    // Check if scraper has a methodType property
    if (
      scraper.methodType &&
      ["crawl4ai", "firecrawl", "playwright", "cache", "unknown"].includes(
        scraper.methodType
      )
    ) {
      return scraper.methodType;
    }

    // Default to crawl4ai since that's our only method now
    return "crawl4ai";
  }

  /**
   * Ensure the service is initialized
   * @returns {Promise<void>}
   */
  async ensureInitialized() {
    if (!this.initialized) {
      await this.initialize();
    }
  }

  /**
   * Fetch price from a specific source
   * @param {string} sourceId - Source ID
   * @param {string} query - Material description or search query
   * @param {Object} options - Additional options
   * @returns {Promise<Object>} - Object with price information
   */
  async fetchPriceFromSource(sourceId, query, options = {}) {
    logger.info(
      `[ScraperService] fetchPriceFromSource called for source ${sourceId} with query: "${query}"`
    );

    if (!this.scrapers.has(sourceId)) {
      throw new ApiError(
        404,
        `Source ${sourceId} not found or not initialized`
      );
    }

    const scraper = this.scrapers.get(sourceId);
    const source = await MaterialSource.findById(sourceId);

    if (!source) {
      throw new ApiError(404, `Source ${sourceId} not found in database`);
    }

    // Start scraping log entry
    const log = await PriceScrapingLog.startScrape(sourceId, {
      query,
      initiatedBy: options.userId || "system",
      company: options.companyId,
      methodUsed: this.getScraperMethod(scraper),
    });

    try {
      // Use the scraper's searchByDescription method to find matches
      const results = await scraper.searchByDescription(query);

      if (!results || results.length === 0) {
        await log.fail(new Error(`No results found for query: "${query}"`));
        return {
          success: false,
          message: `No results found for query: "${query}"`,
        };
      }

      // Use the best match (first result)
      const bestMatch = results[0];

      // Update the log with success
      await log.success({
        foundProductName: bestMatch.name,
        price: bestMatch.price,
        sku: bestMatch.sku,
        url: bestMatch.url,
      });

      // Update price history if necessary
      if (bestMatch.sku && bestMatch.price) {
        await MaterialPriceHistory.recordPrice({
          material: bestMatch.sku,
          source: sourceId,
          price: bestMatch.price,
          currency: "USD", // Default to USD
          recordedBy: options.userId || "system",
        });
      }

      return {
        success: true,
        price: bestMatch.price,
        currency: "USD", // Default to USD
        sku: bestMatch.sku,
        url: bestMatch.url,
        name: bestMatch.name,
        description: bestMatch.description || bestMatch.name,
        sourceName: source.name,
        sourceId: sourceId,
        matchDetails: {
          query: query,
          matchedProduct: bestMatch.name,
          confidence: "exact", // Default confidence level
          matchMethod: "search_by_description",
        },
      };
    } catch (error) {
      await log.fail(error);
      logger.error(
        `[ScraperService] Error fetching price for "${query}" from source ${sourceId}: ${error.message}`
      );
      throw error;
    }
  }

  /**
   * Refresh a scraper
   * @param {string} sourceId - Source ID
   * @returns {Promise<void>}
   */
  async refreshScraper(sourceId) {
    // Get the source from the database
    const source = await MaterialSource.findById(sourceId);
    if (!source) {
      throw new ApiError(404, `Source ${sourceId} not found`);
    }

    // Remove the old scraper if exists
    if (this.scrapers.has(sourceId)) {
      const scraper = this.scrapers.get(sourceId);
      // Clean up any resources used by the scraper
      this.scrapers.delete(sourceId);
      this.sourceTypeMap.delete(source.type); // Remove from type map
    }

    // Initialize a new scraper
    if (source.enabled) {
      const newScraper = await this.initializeScraper(source);
      if (newScraper) {
        this.sourceTypeMap.set(source.type, source._id.toString()); // Add back to type map
      }
    }
  }

  /**
   * Clear all cached prices for a source
   * @param {string} sourceId - Source ID
   * @returns {Promise<void>}
   */
  async clearSourceCache(sourceId) {
    // Get cached keys for the source
    const keys = await priceCache.scanKeys(`*:${sourceId}:*`);

    // Delete each key
    for (const key of keys) {
      await priceCache.del(key);
    }

    logger.info(`Cleared ${keys.length} cached items for source ${sourceId}`);
  }

  /**
   * Get price history for a material
   * @param {string} sku - Material SKU
   * @param {string} sourceId - Source ID
   * @returns {Promise<Object>} - Price history
   */
  async getMaterialPriceHistory(sku, sourceId) {
    // Get price history from database
    const priceHistory = await MaterialPriceHistory.findOne({
      material: sku, // Assuming history uses the normalized SKU (e.g., HD-12345)
      source: sourceId,
    });

    if (!priceHistory) {
      return { material: sku, source: sourceId, prices: [] };
    }

    return priceHistory;
  }

  /**
   * Fetches price for a material by its description, trying preferred source then fallbacks.
   * @param {string} itemDescription - Description of the material to search for.
   * @param {string} preferredSourceType - The preferred source type (e.g., 'HOME_DEPOT', 'PLATT'). Defaults to 'HOME_DEPOT'.
   * @param {object} options - Additional options like skipCache, userId, companyId.
   * @returns {Promise<Object|null>} - Material object with price information, or null if not found/error.
   */
  async fetchPriceForMaterial(
    itemDescription,
    preferredSourceType = "HOME_DEPOT",
    options = {}
  ) {
    await this.ensureInitialized();

    logger.info(
      `[ScraperService] fetchPriceForMaterial called for: "${itemDescription}", preferred: ${preferredSourceType}`
    );

    const sourceTypesInOrder = [preferredSourceType];
    const allConfiguredTypes = Array.from(this.sourceTypeMap.keys());
    allConfiguredTypes.forEach((type) => {
      if (type !== preferredSourceType && !sourceTypesInOrder.includes(type)) {
        sourceTypesInOrder.push(type);
      }
    });

    const availableSourceIdsToTry = sourceTypesInOrder
      .map((type) => this.sourceTypeMap.get(type))
      .filter((id) => id && this.scrapers.has(id));

    if (availableSourceIdsToTry.length === 0) {
      logger.warn(
        `[ScraperService] No available scrapers for types: ${sourceTypesInOrder.join(
          ", "
        )} for query "${itemDescription}"`
      );
      return null;
    }

    return await this._performPriceLookup(
      availableSourceIdsToTry,
      itemDescription,
      options
    );
  }

  async _performPriceLookup(availableSourceIdsToTry, itemDescription, options) {
    // let overallLogEntry; // For a high-level log if needed, or manage per-source logs

    for (const sourceId of availableSourceIdsToTry) {
      const scraper = this.scrapers.get(sourceId);
      if (!scraper) continue;

      const perSourceLog = await PriceScrapingLog.startScrape(sourceId, {
        query: itemDescription,
        initiatedBy: options.userId,
        company: options.companyId,
        methodUsed: this.getScraperMethod(scraper),
      });

      try {
        logger.info(
          `[ScraperService] Attempting to fetch price from ${scraper.name} for "${itemDescription}"`
        );

        // Log to material fetch logger (assuming we can extract quote/item IDs from options)
        if (options.quoteId && options.itemId) {
          materialFetchLogger.logStep(
            options.quoteId,
            options.itemId,
            "SCRAPER_SEARCH_START",
            {
              source: scraper.name,
              sourceId: sourceId,
            }
          );
        }

        // Perform search
        const searchOutcome = await this.searchBySource(
          sourceId,
          itemDescription,
          options
        );

        let actualSearchResults = [];
        if (Array.isArray(searchOutcome)) {
          actualSearchResults = searchOutcome;
        } else if (searchOutcome && searchOutcome.results) {
          actualSearchResults = searchOutcome.results;
          if (
            searchOutcome.sourceStatus &&
            searchOutcome.sourceStatus.status !== "OK"
          ) {
            logger.warn(
              `[ScraperService] Source ${scraper.name} reported status: ${searchOutcome.sourceStatus.status} - ${searchOutcome.sourceStatus.reason}`
            );
            if (
              searchOutcome.sourceStatus.status === "BLOCKED" ||
              searchOutcome.sourceStatus.status === "RATE_LIMITED"
            ) {
              await perSourceLog.fail(
                new Error(searchOutcome.sourceStatus.reason),
                {
                  status: searchOutcome.sourceStatus.status,
                  errorCode: searchOutcome.sourceStatus.status,
                  methodUsed: this.getScraperMethod(scraper),
                }
              );
              continue;
            }
          }
        }

        if (actualSearchResults && actualSearchResults.length > 0) {
          // 🚀 NEW: Analyze results for intelligent material selection
          const selectionAnalysis = this.analyzeResultsForUserSelection(
            itemDescription,
            actualSearchResults,
            options
          );

          logger.info(`[ScraperService] Material selection analysis result:`, {
            needsUserSelection: selectionAnalysis.needsUserSelection,
            reason: selectionAnalysis.reason,
            relevantResultsCount: selectionAnalysis.relevantResults.length,
            query: itemDescription
          });

          // If multiple relevant results found, return them for user selection
          if (selectionAnalysis.needsUserSelection && options.enableUserSelection !== false) {
            logger.info(`[ScraperService] Multiple relevant results found for "${itemDescription}". Returning ${selectionAnalysis.relevantResults.length} options for user selection.`);

            await perSourceLog.complete(
              {
                found: actualSearchResults.length,
                processed: selectionAnalysis.relevantResults.length,
                updated: 0
              },
              {
                status: "PENDING_USER_SELECTION",
                methodUsed: this.getScraperMethod(scraper),
                reason: selectionAnalysis.reason
              }
            );

            return {
              success: true,
              status: "pending_user_selection",
              material_options: selectionAnalysis.relevantResults.map((result, index) => ({
                name: result.name,
                price: result.price,
                sku: result.sku,
                description: result.description || result.name,
                url: result.url,
                imageUrl: result.imageUrl,
                source: scraper.name,
                sourceId: sourceId,
                relevanceScore: result.relevanceScore,
                optionIndex: index,
                originalIndex: result.originalIndex
              })),
              sourceName: scraper.name,
              sourceId: sourceId,
              selectionReason: selectionAnalysis.reason,
              analysisDetails: selectionAnalysis.analysisDetails
            };
          }

          // Use the best match for automatic selection
          const firstResult = selectionAnalysis.bestMatch;
          let materialDetails = null;
          let lookupError = null;

          try {
            if (firstResult.url) {
              logger.info(
                `[ScraperService] Found URL ${firstResult.url} from ${scraper.name}. Attempting to get details by URL.`
              );

              const urlLookupOutcome = await this.getMaterialByUrl(
                firstResult.url,
                options
              );
              if (
                urlLookupOutcome &&
                urlLookupOutcome.sourceStatus &&
                urlLookupOutcome.sourceStatus.status !== "OK"
              ) {
                logger.warn(
                  `[ScraperService] URL lookup from ${scraper.name} for ${firstResult.url} reported status: ${urlLookupOutcome.sourceStatus.status} - ${urlLookupOutcome.sourceStatus.reason}`
                );
                materialDetails = urlLookupOutcome.data;
                if (
                  urlLookupOutcome.sourceStatus.status === "BLOCKED" ||
                  urlLookupOutcome.sourceStatus.status === "RATE_LIMITED"
                ) {
                  throw new ApiError(
                    429,
                    `Source ${scraper.name} blocked or rate limited during URL lookup. Reason: ${urlLookupOutcome.sourceStatus.reason}`
                  );
                }
              } else {
                materialDetails = urlLookupOutcome;
              }
            } else if (firstResult.sku) {
              logger.info(
                `[ScraperService] Found SKU ${firstResult.sku} from ${scraper.name}. Attempting to get details by SKU.`
              );

              const skuLookupOutcome = await this.getMaterialBySkuFromSource(
                sourceId,
                firstResult.sku,
                options
              );
              if (
                skuLookupOutcome &&
                skuLookupOutcome.sourceStatus &&
                skuLookupOutcome.sourceStatus.status !== "OK"
              ) {
                logger.warn(
                  `[ScraperService] SKU lookup from ${scraper.name} for ${firstResult.sku} reported status: ${skuLookupOutcome.sourceStatus.status} - ${skuLookupOutcome.sourceStatus.reason}`
                );
                materialDetails = skuLookupOutcome.data;
                if (
                  skuLookupOutcome.sourceStatus.status === "BLOCKED" ||
                  skuLookupOutcome.sourceStatus.status === "RATE_LIMITED"
                ) {
                  throw new ApiError(
                    429,
                    `Source ${scraper.name} blocked or rate limited during SKU lookup. Reason: ${skuLookupOutcome.sourceStatus.reason}`
                  );
                }
              } else {
                materialDetails = skuLookupOutcome;
              }
            }
          } catch (err) {
            lookupError = err;
            logger.error(
              `[ScraperService] Error during detail lookup from ${
                scraper.name
              } for item "${firstResult.name || itemDescription}": ${
                err.message
              }`
            );
          }

          if (
            materialDetails &&
            materialDetails.price !== null &&
            materialDetails.price !== undefined
          ) {
            logger.info(
              `[ScraperService] Successfully fetched price from ${scraper.name}: ${materialDetails.price}`
            );
            await perSourceLog.complete(
              { found: actualSearchResults.length, processed: 1, updated: 1 },
              { status: "SUCCESS", methodUsed: this.getScraperMethod(scraper) }
            );
            return {
              ...materialDetails,
              sourceName: scraper.name,
              sourceId: sourceId,
              success: true,
            };
          } else {
            const reason = lookupError
              ? `Detail lookup failed: ${lookupError.message}`
              : "Could not extract price or details were null.";
            logger.warn(
              `[ScraperService] ${reason} from ${scraper.name} for item: ${
                firstResult.name || itemDescription
              }`
            );
            await perSourceLog.fail(lookupError || new Error(reason), {
              status: "PARTIAL",
              errorCode: "NO_PRICE_EXTRACTED",
              methodUsed: this.getScraperMethod(scraper),
            });
          }
        } else {
          logger.warn(
            `[ScraperService] No search results from ${scraper.name} for "${itemDescription}"`
          );
          await perSourceLog.complete(
            { found: 0, processed: 0 },
            {
              status: "PARTIAL",
              errorCode: "NO_SEARCH_RESULTS",
              methodUsed: this.getScraperMethod(scraper),
            }
          );
        }
      } catch (error) {
        logger.error(
          `[ScraperService] Error fetching price from ${scraper.name} for "${itemDescription}": ${error.message}`
        );
        const errorCode =
          error.statusCode === 404
            ? "NOT_FOUND"
            : error.statusCode === 429
            ? "RATE_LIMITED"
            : "SCRAPE_ERROR";
        await perSourceLog.fail(error, {
          errorCode,
          methodUsed: this.getScraperMethod(scraper),
        });
      }
    }

    logger.warn(
      `[ScraperService] Failed to fetch price for "${itemDescription}" from all attempted sources.`
    );
    return null;
  }
  /**
   * Validate material sources configuration
   * @returns {Promise<object>} Validation results
   */
  async validateMaterialSources() {
    logger.info("[SCRAPER_HEALTH] Validating material sources...");

    const sources = await MaterialSource.find({ enabled: true });
    const validation = {
      totalSources: sources.length,
      homeDepotFound: false,
      plattFound: false,
      validSources: [],
      issues: [],
    };

    sources.forEach((source) => {
      if (source.type === "HOME_DEPOT") {
        validation.homeDepotFound = true;
        validation.validSources.push(source);
      } else if (source.type === "PLATT") {
        validation.plattFound = true;
        validation.validSources.push(source);
      }
    });

    if (!validation.homeDepotFound) {
      validation.issues.push("HOME_DEPOT source not found in database");
    }

    if (!validation.plattFound) {
      validation.issues.push("PLATT source not found in database");
    }

    // Log material source validation results in a structured way to prevent JSON serialization issues
    logger.info(
      `[SCRAPER_HEALTH] Material source validation completed: ${validation.totalSources} total sources`
    );
    logger.info(
      `[SCRAPER_HEALTH] Home Depot found: ${validation.homeDepotFound}, Platt found: ${validation.plattFound}`
    );
    logger.info(
      `[SCRAPER_HEALTH] Valid sources count: ${validation.validSources.length}`
    );
    if (validation.issues.length > 0) {
      logger.warn(
        `[SCRAPER_HEALTH] Validation issues found: ${validation.issues.join(
          ", "
        )}`
      );
    } else {
      logger.info("[SCRAPER_HEALTH] No validation issues found");
    }
    return validation;
  }

  /**
   * Get comprehensive scraper service health status
   * @returns {Promise<object>} Health status
   */
  async getHealthStatus() {
    const health = {
      scraperService: {
        initialized: this.initialized,
        scraperCount: this.scrapers.size,
        sourceTypes: Array.from(this.sourceTypeMap.keys()),
      },
      materialSources: null,
      crawl4ai: null,
      timestamp: new Date().toISOString(),
    };

    try {
      // Check material sources
      health.materialSources = await this.validateMaterialSources();

      // Check Crawl4AI health
      const crawl4aiService = require("./crawl4ai/crawl4ai-service");
      health.crawl4ai = await crawl4aiService.healthCheck();
    } catch (error) {
      logger.error("[SCRAPER_HEALTH] Failed to get health status:", error);
      health.error = error.message;
    }

    return health;
  }
}

// Export singleton instance
module.exports = new ScraperService();
