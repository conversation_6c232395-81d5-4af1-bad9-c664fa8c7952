const mongoose = require("mongoose");
const Schema = mongoose.Schema;

const materialTransactionSchema = new Schema(
  {
    material: {
      type: Schema.Types.ObjectId,
      ref: "Material",
      required: true,
      index: true,
    },
    change: {
      type: Number,
      required: true, // Positive for increase, negative for decrease
    },
    quantityBefore: {
      type: Number,
      required: true,
    },
    quantityAfter: {
      type: Number,
      required: true,
    },
    type: {
      type: String,
      required: true,
      enum: ["INITIAL", "PURCHASE", "JOB_USAGE", "ADJUSTMENT", "RETURN"],
      default: "ADJUSTMENT",
    },
    user: {
      type: Schema.Types.ObjectId,
      ref: "User",
      required: true, // User performing the action
    },
    relatedDocId: {
      // Optional reference to Job, Purchase Order, etc.
      type: Schema.Types.ObjectId,
      index: true,
    },
    relatedDocModel: {
      // Model name for relatedDocId population
      type: String,
    },
    notes: {
      type: String,
      trim: true,
    },
    timestamp: {
      type: Date,
      default: Date.now,
      index: true,
    },
  },
  {
    timestamps: { createdAt: "timestamp", updatedAt: false }, // Use 'timestamp' as the creation field
  }
);

// Optional: Add methods or statics if needed later

// Database indexes for performance optimization
materialTransactionSchema.index({ material: 1, timestamp: -1 }); // Material transaction history
materialTransactionSchema.index({ type: 1, timestamp: -1 }); // Transaction type queries
materialTransactionSchema.index({ user: 1, timestamp: -1 }); // User activity tracking
materialTransactionSchema.index({ relatedDocId: 1, relatedDocModel: 1 }); // Related document lookup
materialTransactionSchema.index({ timestamp: -1 }); // Recent transactions
materialTransactionSchema.index({ material: 1, type: 1 }); // Material-specific transaction types
materialTransactionSchema.index({ change: 1 }); // Quantity change analysis

const MaterialTransaction = mongoose.model(
  "MaterialTransaction",
  materialTransactionSchema
);

module.exports = MaterialTransaction;
