<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <link rel="icon" href="%PUBLIC_URL%/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <!-- Theme color meta tag - only supported by Chrome/Edge, not Firefox/Opera -->
    <!-- Uncomment if Chrome/Edge mobile toolbar color is needed -->
    <!-- <meta name="theme-color" content="#000000" /> -->
    <meta
      name="description"
      content="Workiz Clone - Field Service Management Software"
    />
    <link rel="apple-touch-icon" href="%PUBLIC_URL%/logo192.png" />
    <link rel="manifest" href="%PUBLIC_URL%/manifest.json" />
    <title>Workiz Clone</title>
    <!-- Inject environment variables as global variables -->
    <script>
      // Make environment variables available globally to avoid "process is not defined" errors
      window.REACT_APP_GOOGLE_MAPS_API_KEY = "AIzaSyBjul622_fwkUK8MmlAyOLRFx6S1aHobVc";
      window.REACT_APP_API_BASE_URL = "http://localhost:5000/api";
    </script>
  </head>
  <body>
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <div id="root"></div>
  </body>
</html>