/**
 * quoteService.js
 * Service layer for quote-related API calls
 */
import axios from "axios";
import logger from "../utils/logger"; // Assuming logger is available

const API_BASE_URL = process.env.REACT_APP_API_BASE_URL || "/api";

/**
 * Resolve a specific action or placeholder within a quote.
 * @param {string} quoteId - The ID of the quote.
 * @param {object} actionDetails - Details of the action to resolve (e.g., { type: 'lookup', params: 'query text' }).
 * @param {function} getToken - Function to get the auth token.
 * @returns {Promise<object>} - The updated quote data or resolved item details.
 */
export const resolveQuoteActionAPI = async (
  quoteId,
  actionDetails,
  getToken
) => {
  if (!quoteId) {
    logger.error("resolveQuoteActionAPI: quoteId is required");
    throw new Error("Quote ID is required for resolving action.");
  }
  if (!actionDetails) {
    logger.error("resolveQuoteActionAPI: actionDetails are required");
    throw new Error("Action details are required for resolving action.");
  }

  try {
    const token = getToken();
    if (!token) {
      logger.error("resolveQuoteActionAPI: No auth token available");
      throw new Error("Authentication token not found.");
    }

    const config = {
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
    };

    const { data } = await axios.post(
      `${API_BASE_URL}/quotes/${quoteId}/resolve-action`,
      actionDetails,
      config
    );

    // Assuming the backend returns { success: true, data: updatedQuoteOrItem }
    if (data && data.success) {
      return data.data;
    } else {
      logger.error(
        "resolveQuoteActionAPI: API call did not return success or data",
        data
      );
      throw new Error(
        data?.message ||
          "Failed to resolve quote action due to unexpected API response."
      );
    }
  } catch (error) {
    const errorMessage = error.response?.data?.message || error.message;
    logger.error(
      `resolveQuoteActionAPI: Error resolving action for quote ${quoteId}:`,
      errorMessage,
      error.response?.data
    );
    throw new Error(errorMessage);
  }
};

/**
 * Lookup prices for items without saving the quote.
 * @param {Array} items - Array of items to lookup prices for.
 * @param {function} getToken - Function to get the auth token.
 * @param {object} options - Additional options for the lookup.
 * @returns {Promise<object>} - The price lookup results.
 */
export const lookupPricesWithoutSaving = async (items, getToken, options = {}) => {
  if (!items || !Array.isArray(items) || items.length === 0) {
    logger.error("lookupPricesWithoutSaving: items array is required");
    throw new Error("Items array is required for price lookup.");
  }

  try {
    const token = getToken();
    if (!token) {
      logger.error("lookupPricesWithoutSaving: No auth token available");
      throw new Error("Authentication token not found.");
    }

    const config = {
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      timeout: options.timeout || 60000, // 60 second timeout
    };

    // Prepare items payload
    const itemsPayload = items.map(item => ({
      name: item.name || '',
      description: item.description || '',
      sku: item.sku || '',
      price: item.price || 0,
      quantity: item.quantity || 1,
      unit: item.unit || 'each',
      lookup_query_suggestion: item.lookup_query_suggestion || item._aiData?.lookup_query_suggestion || '',
      source: item.source || '',
      sourceId: item.sourceId || '',
      url: item.url || '',
      imageUrl: item.imageUrl || '',
    }));

    logger.info(`lookupPricesWithoutSaving: Starting price lookup for ${items.length} items`);

    const { data } = await axios.post(
      `${API_BASE_URL}/quotes/lookup-prices`,
      { items: itemsPayload },
      config
    );

    if (data && data.success) {
      logger.info(`lookupPricesWithoutSaving: Price lookup completed`, {
        total: data.summary.total,
        found: data.summary.pricesFound,
        failed: data.summary.pricesFailed,
        averageTime: data.summary.averageResponseTime,
      });
      return data;
    } else {
      logger.error(
        "lookupPricesWithoutSaving: API call did not return success",
        data
      );
      throw new Error(
        data?.message ||
          "Failed to lookup prices due to unexpected API response."
      );
    }
  } catch (error) {
    const errorMessage = error.response?.data?.message || error.message;
    logger.error(
      `lookupPricesWithoutSaving: Error looking up prices:`,
      errorMessage,
      error.response?.data
    );
    throw new Error(errorMessage);
  }
};

/**
 * Show AI price lookup progress in the frontend.
 * This function provides visual feedback while AI is retrieving prices.
 * @param {function} onProgressUpdate - Callback to update progress in UI.
 * @param {Array} items - Items being processed.
 * @returns {object} - Progress tracking utilities.
 */
export const createPriceLookupProgressTracker = (onProgressUpdate, items = []) => {
  let isActive = false;
  let progress = {
    total: items.length,
    processed: 0,
    found: 0,
    failed: 0,
    percentage: 0,
  };

  const updateProgress = (updates) => {
    progress = { ...progress, ...updates };
    progress.percentage = progress.total > 0 ? Math.round((progress.processed / progress.total) * 100) : 0;
    
    if (onProgressUpdate) {
      onProgressUpdate(progress);
    }
  };

  const start = () => {
    isActive = true;
    updateProgress({ processed: 0, found: 0, failed: 0 });
    logger.info("Price lookup progress tracker started");
  };

  const complete = () => {
    isActive = false;
    updateProgress({ percentage: 100 });
    logger.info("Price lookup progress tracker completed");
  };

  const updateItem = (index, status, price = null) => {
    if (status === 'found') {
      updateProgress({ 
        processed: progress.processed + 1, 
        found: progress.found + 1 
      });
    } else if (status === 'failed' || status === 'not_found') {
      updateProgress({ 
        processed: progress.processed + 1, 
        failed: progress.failed + 1 
      });
    }
  };

  const reset = () => {
    isActive = false;
    progress = {
      total: items.length,
      processed: 0,
      found: 0,
      failed: 0,
      percentage: 0,
    };
    updateProgress({});
  };

  return {
    start,
    complete,
    updateItem,
    reset,
    isActive: () => isActive,
    getProgress: () => progress,
  };
};
