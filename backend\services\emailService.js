const nodemailer = require("nodemailer");
const { formatCurrency } = require("../utils/invoiceUtils");
const ApiError = require("../utils/ApiError");
const { cacheGet, cacheSet } = require("../utils/cache");
const { generateTemplate } = require("../utils/templateGenerator");

// Initialize email transport
const transporter = nodemailer.createTransport({
  host: process.env.SMTP_HOST,
  port: process.env.SMTP_PORT,
  secure: process.env.SMTP_SECURE === "true",
  auth: {
    user: process.env.SMTP_USER,
    pass: process.env.SMTP_PASS,
  },
});

/**
 * Send invoice email to customer
 */
async function sendInvoiceEmail(invoice, customer) {
  try {
    // Get cached template if available
    const cacheKey = `invoice_email_template:${invoice.status}`;
    let template = await cacheGet(cacheKey);

    if (!template) {
      // Generate new template based on invoice status
      template = await generateTemplate("invoice", {
        status: invoice.status,
        type: "email",
      });
      await cacheSet(cacheKey, template, 3600); // Cache for 1 hour
    }

    // Replace template variables
    const emailContent = template
      .replace("{{customerName}}", customer.businessName)
      .replace("{{invoiceNumber}}", invoice.number)
      .replace("{{amount}}", formatCurrency(invoice.total))
      .replace("{{dueDate}}", new Date(invoice.dueDate).toLocaleDateString())
      .replace("{{items}}", generateItemsTable(invoice.items))
      .replace("{{paymentLink}}", generatePaymentLink(invoice));

    const mail = {
      from: `${process.env.EMAIL_FROM_NAME} <${process.env.EMAIL_FROM_ADDRESS}>`,
      to: customer.email,
      subject: `Invoice ${invoice.number} from ${process.env.COMPANY_NAME}`,
      html: emailContent,
      attachments: [
        {
          filename: `invoice-${invoice.number}.pdf`,
          content: await generateInvoicePDF(invoice),
        },
      ],
    };

    // Send email
    const info = await transporter.sendMail(mail);

    // Log email send
    await logEmailActivity(invoice._id, "INVOICE_SENT", info.messageId);

    return {
      success: true,
      messageId: info.messageId,
    };
  } catch (error) {
    console.error("Email send error:", error);
    throw new ApiError(500, "Failed to send invoice email", error.message);
  }
}

/**
 * Send payment reminder email
 */
async function sendReminderEmail(invoice, customer, reminderType = "gentle") {
  try {
    const cacheKey = `reminder_template:${reminderType}`;
    let template = await cacheGet(cacheKey);

    if (!template) {
      template = await generateTemplate("reminder", {
        type: reminderType,
      });
      await cacheSet(cacheKey, template, 3600);
    }

    const emailContent = template
      .replace("{{customerName}}", customer.businessName)
      .replace("{{invoiceNumber}}", invoice.number)
      .replace("{{amount}}", formatCurrency(invoice.balance))
      .replace("{{daysOverdue}}", invoice.daysOverdue)
      .replace("{{paymentLink}}", generatePaymentLink(invoice));

    const mail = {
      from: `${process.env.EMAIL_FROM_NAME} <${process.env.EMAIL_FROM_ADDRESS}>`,
      to: customer.email,
      subject: `Payment Reminder: Invoice ${invoice.number}`,
      html: emailContent,
    };

    const info = await transporter.sendMail(mail);
    await logEmailActivity(invoice._id, "REMINDER_SENT", info.messageId);

    return {
      success: true,
      messageId: info.messageId,
    };
  } catch (error) {
    console.error("Reminder email error:", error);
    throw new ApiError(500, "Failed to send reminder email", error.message);
  }
}

/**
 * Send payment receipt email
 */
async function sendReceiptEmail(invoice, payment, customer) {
  try {
    const template = await generateTemplate("receipt");

    const emailContent = template
      .replace("{{customerName}}", customer.businessName)
      .replace("{{invoiceNumber}}", invoice.number)
      .replace("{{amount}}", formatCurrency(payment.amount))
      .replace("{{paymentMethod}}", payment.method)
      .replace("{{paymentDate}}", new Date(payment.date).toLocaleDateString())
      .replace("{{remainingBalance}}", formatCurrency(invoice.balance));

    const mail = {
      from: `${process.env.EMAIL_FROM_NAME} <${process.env.EMAIL_FROM_ADDRESS}>`,
      to: customer.email,
      subject: `Payment Receipt for Invoice ${invoice.number}`,
      html: emailContent,
      attachments: [
        {
          filename: `receipt-${payment._id}.pdf`,
          content: await generateReceiptPDF(payment, invoice),
        },
      ],
    };

    const info = await transporter.sendMail(mail);
    await logEmailActivity(invoice._id, "RECEIPT_SENT", info.messageId);

    return {
      success: true,
      messageId: info.messageId,
    };
  } catch (error) {
    console.error("Receipt email error:", error);
    throw new ApiError(500, "Failed to send receipt email", error.message);
  }
}

/**
 * Generate items table HTML for email
 */
function generateItemsTable(items) {
  return `
    <table style="width:100%; border-collapse: collapse; margin: 20px 0;">
      <tr style="background-color: #f8f9fa;">
        <th style="padding: 10px; text-align: left; border: 1px solid #dee2e6;">Description</th>
        <th style="padding: 10px; text-align: right; border: 1px solid #dee2e6;">Quantity</th>
        <th style="padding: 10px; text-align: right; border: 1px solid #dee2e6;">Unit Price</th>
        <th style="padding: 10px; text-align: right; border: 1px solid #dee2e6;">Total</th>
      </tr>
      ${items
        .map(
          (item) => `
        <tr>
          <td style="padding: 10px; border: 1px solid #dee2e6;">${
            item.description
          }</td>
          <td style="padding: 10px; text-align: right; border: 1px solid #dee2e6;">${
            item.quantity
          }</td>
          <td style="padding: 10px; text-align: right; border: 1px solid #dee2e6;">${formatCurrency(
            item.unitPrice
          )}</td>
          <td style="padding: 10px; text-align: right; border: 1px solid #dee2e6;">${formatCurrency(
            item.total
          )}</td>
        </tr>
      `
        )
        .join("")}
    </table>
  `;
}

/**
 * Generate secure payment link
 */
function generatePaymentLink(invoice) {
  const baseUrl = process.env.FRONTEND_URL;
  const token = generatePaymentToken(invoice);
  return `${baseUrl}/pay/${invoice._id}?token=${token}`;
}

/**
 * Generate secure payment token
 */
function generatePaymentToken(invoice) {
  const jwt = require("jsonwebtoken");
  return jwt.sign(
    {
      invoiceId: invoice._id,
      amount: invoice.balance,
      exp: Math.floor(Date.now() / 1000) + 60 * 60 * 24, // 24 hours
    },
    process.env.JWT_SECRET
  );
}

/**
 * Send quote email to customer
 */
async function sendQuoteEmail(quote, emailData = {}) {
  try {
    // Get cached template if available
    const cacheKey = `quote_email_template:${quote.status}`;
    let template = await cacheGet(cacheKey);

    if (!template) {
      // Generate new template based on quote status
      template = await generateTemplate("quote", {
        status: quote.status,
        type: "email",
      });
      await cacheSet(cacheKey, template, 3600); // Cache for 1 hour
    }

    // Populate quote customer if not already populated
    const customer = quote.customer._id
      ? quote.customer
      : await require("../models/Customer").findById(quote.customer);

    // Replace template variables
    const emailContent = template
      .replace(
        "{{customerName}}",
        customer.businessName || customer.name || "Valued Customer"
      )
      .replace("{{quoteName}}", quote.name || `Quote #${quote._id}`)
      .replace("{{amount}}", formatCurrency(quote.total || 0))
      .replace(
        "{{validUntil}}",
        quote.validUntil
          ? new Date(quote.validUntil).toLocaleDateString()
          : "N/A"
      )
      .replace("{{items}}", generateQuoteItemsTable(quote.items))
      .replace("{{companyName}}", process.env.COMPANY_NAME || "Your Company")
      .replace(
        "{{message}}",
        emailData.message || `Please find attached your quote ${quote.name}.`
      )
      .replace("{{viewLink}}", generateQuoteLink(quote));

    const mail = {
      from: `${process.env.EMAIL_FROM_NAME} <${process.env.EMAIL_FROM_ADDRESS}>`,
      to: emailData.to || customer.email,
      subject:
        emailData.subject ||
        `Quote ${quote.name} from ${process.env.COMPANY_NAME}`,
      html: emailContent,
      attachments: [],
    };

    // Add PDF attachment if available
    if (quote.pdfUrl || typeof generateQuotePDF === "function") {
      try {
        const pdfContent = quote.pdfUrl
          ? await fetchPDFFromUrl(quote.pdfUrl)
          : await generateQuotePDF(quote);
        mail.attachments.push({
          filename: `quote-${quote.name || quote._id}.pdf`,
          content: pdfContent,
        });
      } catch (pdfError) {
        console.error("Failed to attach PDF:", pdfError);
        // Continue without PDF attachment
      }
    }

    // Send email
    const info = await transporter.sendMail(mail);

    // Log email send
    await logEmailActivity(quote._id, "QUOTE_SENT", info.messageId, "quote");

    return {
      success: true,
      messageId: info.messageId,
    };
  } catch (error) {
    console.error("Quote email send error:", error);
    throw new ApiError(500, "Failed to send quote email", error.message);
  }
}

/**
 * Generate quote items table HTML for email
 */
function generateQuoteItemsTable(items) {
  if (!items || items.length === 0) {
    return "<p>No items in this quote.</p>";
  }

  return `
    <table style="width:100%; border-collapse: collapse; margin: 20px 0;">
      <tr style="background-color: #f8f9fa;">
        <th style="padding: 10px; text-align: left; border: 1px solid #dee2e6;">Description</th>
        <th style="padding: 10px; text-align: right; border: 1px solid #dee2e6;">Quantity</th>
        <th style="padding: 10px; text-align: right; border: 1px solid #dee2e6;">Unit Price</th>
        <th style="padding: 10px; text-align: right; border: 1px solid #dee2e6;">Total</th>
      </tr>
      ${items
        .map(
          (item) => `
        <tr>
          <td style="padding: 10px; border: 1px solid #dee2e6;">${
            item.description || item.name || "Item"
          }</td>
          <td style="padding: 10px; text-align: right; border: 1px solid #dee2e6;">${
            item.quantity || 1
          }</td>
          <td style="padding: 10px; text-align: right; border: 1px solid #dee2e6;">${formatCurrency(
            item.price || 0
          )}</td>
          <td style="padding: 10px; text-align: right; border: 1px solid #dee2e6;">${formatCurrency(
            (item.price || 0) * (item.quantity || 1)
          )}</td>
        </tr>
      `
        )
        .join("")}
    </table>
  `;
}

/**
 * Generate secure quote view link
 */
function generateQuoteLink(quote) {
  const baseUrl = process.env.FRONTEND_URL;
  const token = generateQuoteToken(quote);
  return `${baseUrl}/quotes/${quote._id}?token=${token}`;
}

/**
 * Generate secure quote token
 */
function generateQuoteToken(quote) {
  const jwt = require("jsonwebtoken");
  return jwt.sign(
    {
      quoteId: quote._id,
      customerId: quote.customer._id || quote.customer,
      exp: Math.floor(Date.now() / 1000) + 60 * 60 * 24 * 7, // 7 days
    },
    process.env.JWT_SECRET
  );
}

/**
 * Fetch PDF from URL (placeholder function)
 */
async function fetchPDFFromUrl(url) {
  // This would fetch the PDF from a URL if stored externally
  // For now, return null to skip attachment
  return null;
}

/**
 * Generate quote PDF (placeholder function)
 */
async function generateQuotePDF(quote) {
  // This would generate a PDF for the quote
  // For now, return null to skip attachment
  // You can implement this using libraries like puppeteer, pdfkit, etc.
  return null;
}

/**
 * Log email activity
 */
async function logEmailActivity(
  entityId,
  type,
  messageId,
  entityType = "invoice"
) {
  const EmailLog = require("../models/EmailLog");
  await EmailLog.create({
    [entityType]: entityId,
    type,
    messageId,
    timestamp: new Date(),
  });
}

module.exports = {
  sendInvoiceEmail,
  sendReminderEmail,
  sendReceiptEmail,
  sendQuoteEmail,
};
