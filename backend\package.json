{"name": "workiz-clone-backend", "version": "1.0.0", "description": "Backend API for Workiz Clone - Field Service Management Software", "main": "server.js", "scripts": {"start": "docker-compose -f ../docker-compose.yml up -d && node scripts/startup.js", "dev": "docker-compose -f ../docker-compose.yml up -d mongodb && cross-env LOG_LEVEL=debug nodemon server.js", "dev:no-db": "cross-env LOG_LEVEL=debug nodemon server.js", "dev:clean": "node scripts/clear-logs.js && cross-env LOG_LEVEL=debug nodemon server.js", "start:clean": "node scripts/clear-logs.js && docker-compose -f ../docker-compose.yml up -d && node scripts/startup.js", "clear-logs": "node scripts/clear-logs.js", "init:replica-set": "node scripts/initReplicaSet.js", "check:replica-set": "node scripts/checkReplicaSet.js", "startup": "node scripts/startup.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:unit": "jest tests/unit", "test:integration": "jest tests/integration"}, "dependencies": {"@google-cloud/vision": "^5.1.0", "@google/generative-ai": "^0.24.0", "@mendable/firecrawl-js": "^1.21.1", "axios": "^1.8.4", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^10.0.0", "express": "^4.17.1", "express-async-handler": "^1.2.0", "express-rate-limit": "^7.5.0", "fuse.js": "^7.1.0", "handlebars": "^4.7.8", "ioredis": "^5.6.0", "joi": "^17.13.3", "jpeg-exif": "^1.1.4", "jsonwebtoken": "^9.0.2", "mime-types": "^3.0.1", "mongoose": "^6.0.12", "morgan": "^1.10.0", "multer": "^1.4.5-lts.2", "nodemailer": "^6.10.0", "openai": "^4.87.3", "p-limit": "^6.2.0", "pdfkit": "^0.16.0", "playwright": "^1.51.1", "playwright-extra": "^4.3.6", "playwright-extra-plugin-stealth": "^0.0.1", "puppeteer": "^22.0.0", "rate-limit-redis": "^4.2.0", "redis": "^4.7.0", "sharp": "^0.34.2", "winston": "^3.17.0", "zod": "^3.24.2"}, "devDependencies": {"cross-env": "^7.0.3", "jest": "^29.7.0", "mongodb-memory-server": "^10.1.4", "nodemon": "^2.0.14", "supertest": "^7.0.0"}, "engines": {"node": ">=14.0.0"}}