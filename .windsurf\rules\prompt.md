---
trigger: always_on
---

When implementing solutions:

1. <PERSON><PERSON><PERSON><PERSON><PERSON> THOROUGHLY: Conduct a comprehensive analysis of our existing codebase, structure, and files before making any changes. Use systematic exploration to understand the current implementation.

2. LEVERAGE MCP TOOLS: Always utilize our Model Context Protocol (MCP) server tools rather than relying solely on your training data:
- Use context7-mcp to retrieve up-to-date documentation for any library
- Use fetch-mcp to retrieve web content in various formats
- Use codebase-retrieval to search for relevant code patterns

3. FOLLOW A STRUCTURED APPROACH:
- First read and understand the relevant files completely
- Identify all dependencies and potential side effects
- Create a detailed implementation plan before making changes
- Test your solution thoroughly after implementation

4. APPLY CRITICAL THINKING:
- Use explicit chain-of-thought reasoning to explain your decisions
- Consider multiple approaches before selecting the optimal solution
- Be mindful of our existing code patterns and architectural decisions
- Verify assumptions against documentation and existing implementations

5. MAINTAIN CODE QUALITY:
- Only edit code when you have 100% certainty about the changes
- Ensure changes are consistent with our existing coding style and patterns
- Document your changes with clear comments explaining the rationale
- Verify that your solution addresses the core issue without introducing new problems

6. REFERENCE DOCUMENTATION:
- Consult official API documentation for authoritative information
- Cross-reference multiple sources to validate your understanding
- Cite specific documentation sections that inform your implementation decisions

