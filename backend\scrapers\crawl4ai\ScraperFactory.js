/**
 * ScraperFactory.js
 * Factory class to create the appropriate scraper instance based on source type.
 * This provides a clean integration point between the new Crawl4AI scrapers
 * and the existing scraper system.
 */

const logger = require("../../utils/logger");
const HomeDepotCrawl4AIScraper = require("./HomeDepotCrawl4AIScraper");
const PlattCrawl4AIScraper = require("./PlattCrawl4AIScraper");

/**
 * Factory class for creating Crawl4AI-based scrapers
 */
class ScraperFactory {
  /**
   * Create a scraper instance based on source type
   * @param {Object} source - Source document from database
   * @returns {Object} - Scraper instance
   */
  static createScraper(source) {
    if (!source || !source.type) {
      throw new Error("Invalid source: missing required type");
    }

    logger.debug(
      `[Crawl4AI ScraperFactory] Creating scraper for source type: ${source.type}`
    );

    switch (source.type.toUpperCase()) {
      case "HOME_DEPOT":
        return new HomeDepotCrawl4<PERSON>IScraper(source);

      case "PLATT":
        return new PlattCrawl4<PERSON>IScraper(source);

      default:
        throw new Error(
          `Unsupported source type for Crawl4AI scraper: ${source.type}`
        );
    }
  }

  /**
   * Check if a source type is supported by Crawl4AI scrapers
   * @param {string} sourceType - Source type to check
   * @returns {boolean} - True if supported
   */
  static isSupported(sourceType) {
    if (!sourceType) return false;

    const type = sourceType.toUpperCase();

    // Add source types as they are implemented
    return ["HOME_DEPOT", "PLATT"].includes(type);
  }
}

module.exports = ScraperFactory;
