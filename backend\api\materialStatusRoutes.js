/**
 * Material Status Routes
 * API endpoints for monitoring material price lookup status
 */

const express = require("express");
const router = express.Router();
const { protect } = require("../middleware/auth");
const Quote = require("../models/Quote");
const materialFetchLogger = require("../utils/materialFetchLogger");
const logger = require("../utils/logger");

/**
 * @route   GET /api/material-status/active-lookups
 * @desc    Get all active material lookups
 * @access  Private
 */
router.get("/active-lookups", protect, async (req, res, next) => {
  try {
    const activeLookups = materialFetchLogger.getActiveLookups();

    res.json({
      success: true,
      count: activeLookups.length,
      lookups: activeLookups,
    });
  } catch (error) {
    logger.error("Error getting active lookups:", error);
    next(error);
  }
});

/**
 * @route   GET /api/material-status/quote/:quoteId
 * @desc    Get material lookup status for a specific quote
 * @access  Private
 */
router.get("/quote/:quoteId", protect, async (req, res, next) => {
  try {
    const { quoteId } = req.params;

    // Get quote with items
    const quote = await Quote.findById(quoteId).select(
      "items status createdAt"
    );

    if (!quote) {
      return res.status(404).json({
        success: false,
        message: "Quote not found",
      });
    }

    // Check authorization
    if (
      quote.createdBy &&
      quote.createdBy.toString() !== req.user._id.toString()
    ) {
      return res.status(403).json({
        success: false,
        message: "Not authorized to view this quote",
      });
    }

    // Extract material lookup information
    const itemStatuses = quote.items.map((item) => {
      const pendingLookups =
        item.lookup_results?.filter(
          (lr) =>
            lr.status === "pending_internal_price_lookup" ||
            lr.status === "processing_direct_lookup"
        ) || [];

      const completedLookups =
        item.lookup_results?.filter(
          (lr) =>
            lr.status === "price_lookup_success" ||
            lr.status === "success_manual_selection_pending"
        ) || [];

      const failedLookups =
        item.lookup_results?.filter(
          (lr) =>
            lr.status === "price_lookup_failed" ||
            lr.status === "price_lookup_orchestration_error"
        ) || [];

      return {
        itemId: item._id,
        name: item.name || item.description,
        price: item.price,
        hasPrice: item.price > 0,
        lookupStatus: item.lookup_status || "none",
        pendingCount: pendingLookups.length,
        completedCount: completedLookups.length,
        failedCount: failedLookups.length,
        totalLookups: item.lookup_results?.length || 0,
        lastLookup:
          item.lookup_results?.[item.lookup_results.length - 1] || null,
      };
    });

    // Check for any active lookups for this quote
    const activeLookups = materialFetchLogger.getActiveLookups();
    const quoteActiveLookups = activeLookups.filter(
      (lookup) => lookup.quoteId === quoteId
    );

    res.json({
      success: true,
      quoteId,
      quoteStatus: quote.status,
      itemCount: quote.items.length,
      itemStatuses,
      activeLookups: quoteActiveLookups,
      summary: {
        totalItems: itemStatuses.length,
        itemsWithPrice: itemStatuses.filter((i) => i.hasPrice).length,
        itemsPending: itemStatuses.filter((i) => i.pendingCount > 0).length,
        itemsCompleted: itemStatuses.filter(
          (i) => i.completedCount > 0 && i.pendingCount === 0
        ).length,
        itemsFailed: itemStatuses.filter(
          (i) =>
            i.failedCount > 0 && i.pendingCount === 0 && i.completedCount === 0
        ).length,
      },
    });
  } catch (error) {
    logger.error("Error getting quote material status:", error);
    next(error);
  }
});

/**
 * @route   POST /api/material-status/cleanup-stale
 * @desc    Clean up stale lookups from memory
 * @access  Private (admin only)
 */
router.post("/cleanup-stale", protect, async (req, res, next) => {
  try {
    // Check if user is admin
    if (req.user.role !== "admin") {
      return res.status(403).json({
        success: false,
        message: "Admin access required",
      });
    }

    materialFetchLogger.cleanupStaleLookups();

    res.json({
      success: true,
      message: "Stale lookups cleaned",
    });
  } catch (error) {
    logger.error("Error cleaning up stale lookups:", error);
    next(error);
  }
});

/**
 * @route   POST /api/material-status/trigger-lookup/:quoteId
 * @desc    Manually trigger price lookup for a quote (for debugging)
 * @access  Private
 */
router.post("/trigger-lookup/:quoteId", protect, async (req, res, next) => {
  try {
    const { quoteId } = req.params;
    const debugPriceLookup = require("../utils/debugPriceLookup");

    logger.info(
      `[MaterialStatusRoutes] Manual price lookup triggered for quote ${quoteId} by user ${req.user._id}`
    );

    const result = await debugPriceLookup.triggerPriceLookupForQuote(quoteId);

    res.json({
      success: true,
      ...result,
    });
  } catch (error) {
    logger.error("Error manually triggering price lookup:", error);
    next(error);
  }
});

/**
 * @route   GET /api/material-status/pending-summary
 * @desc    Get summary of all pending price lookups
 * @access  Private
 */
router.get("/pending-summary", protect, async (req, res, next) => {
  try {
    const debugPriceLookup = require("../utils/debugPriceLookup");
    const summary = await debugPriceLookup.checkPendingLookups();

    res.json({
      success: true,
      ...summary,
    });
  } catch (error) {
    logger.error("Error getting pending lookups summary:", error);
    next(error);
  }
});

module.exports = router;
