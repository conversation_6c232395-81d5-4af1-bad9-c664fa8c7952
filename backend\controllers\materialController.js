const Material = require("../models/Material");
const MaterialTransaction = require("../models/MaterialTransaction"); // Import transaction model
const ApiError = require("../utils/ApiError"); // Import ApiError for consistency
const mongoose = require("mongoose");

// @desc    Create a new inventory item
// @route   POST /api/inventory
// @access  Private
const createMaterial = async (req, res, next) => {
  // Added next
  try {
    const userId = req.user._id; // Get user ID from auth middleware
    const {
      name,
      description,
      sku,
      category,
      unitPrice,
      costPrice,
      quantity,
      reorderLevel,
      supplier,
      location,
      images,
    } = req.body;

    // Check if SKU already exists
    if (sku) {
      const existingItem = await Material.findOne({ sku });
      if (existingItem) {
        // --- Item exists, update it ---
        const quantityBefore = existingItem.quantity;
        const newQuantity =
          req.body.quantity !== undefined
            ? req.body.quantity
            : existingItem.quantity;
        const quantityChange = newQuantity - quantityBefore;

        existingItem.name = name || existingItem.name;
        existingItem.description = description || existingItem.description;
        existingItem.category = category || existingItem.category;
        existingItem.unitPrice =
          unitPrice !== undefined ? unitPrice : existingItem.unitPrice;
        existingItem.costPrice =
          costPrice !== undefined ? costPrice : existingItem.costPrice;
        existingItem.quantity = newQuantity;
        existingItem.reorderLevel =
          reorderLevel !== undefined ? reorderLevel : existingItem.reorderLevel;
        existingItem.supplier = supplier || existingItem.supplier;
        existingItem.location = location || existingItem.location;
        existingItem.images = images || existingItem.images;
        // Ensure isActive is handled if needed, assuming default true or managed elsewhere
        // existingItem.isActive = req.body.isActive !== undefined ? req.body.isActive : existingItem.isActive;

        const updatedMaterial = await existingItem.save();

        // Log transaction if quantity changed
        if (quantityChange !== 0) {
          try {
            await MaterialTransaction.create({
              material: updatedMaterial._id,
              change: quantityChange,
              quantityBefore: quantityBefore,
              quantityAfter: updatedMaterial.quantity,
              type: "UPDATE", // Use 'UPDATE' or a more specific type
              user: userId,
              notes: `Material updated via create endpoint (SKU: ${sku})`,
            });
          } catch (logError) {
            console.error(
              `Failed to log update transaction for material ${updatedMaterial._id}:`,
              logError
            );
            // Log error but don't fail the main operation
          }
        }

        return res.status(200).json(updatedMaterial); // Return 200 OK for update
        // --- End update logic ---
      }
    }
    // If SKU doesn't exist or no SKU provided, proceed to create new

    const material = await Material.create({
      name,
      description,
      sku,
      category,
      unitPrice,
      costPrice,
      quantity,
      reorderLevel,
      supplier,
      location,
      images,
    });

    if (material) {
      // Log the initial transaction if quantity > 0
      if (material.quantity > 0) {
        try {
          await MaterialTransaction.create({
            material: material._id,
            change: material.quantity,
            quantityBefore: 0,
            quantityAfter: material.quantity,
            type: "INITIAL",
            user: userId,
            notes: "Initial stock on creation",
          });
        } catch (logError) {
          console.error(
            `Failed to log initial transaction for material ${material._id}:`,
            logError
          );
          // Log error but don't fail the main operation
        }
      }
      res.status(201).json(material);
    } else {
      // This case might not be reachable if Material.create throws on failure
      return next(new ApiError(400, "Invalid material data"));
    }
  } catch (error) {
    console.error("Error creating material:", error);
    next(error); // Pass error to centralized handler
  }
};

// @desc    Get all inventory items
// @route   GET /api/inventory
// @access  Private
const getMaterials = async (req, res) => {
  try {
    // Support for pagination
    const pageSize = Number(req.query.pageSize) || 10;
    const page = Number(req.query.page) || 1;

    // Support for filtering by category
    const categoryFilter = req.query.category
      ? { category: req.query.category }
      : {};

    // Support for filtering by low stock
    const lowStockFilter =
      req.query.lowStock === "true"
        ? { quantity: { $lte: "$reorderLevel" } }
        : {};

    // Support for search
    const keyword = req.query.keyword
      ? {
          $or: [
            { name: { $regex: req.query.keyword, $options: "i" } },
            { description: { $regex: req.query.keyword, $options: "i" } },
            { sku: { $regex: req.query.keyword, $options: "i" } },
            { supplier: { $regex: req.query.keyword, $options: "i" } },
          ],
        }
      : {};

    // Combine all filters
    const filter = {
      ...categoryFilter,
      ...lowStockFilter,
      ...keyword,
    };

    const count = await Material.countDocuments(filter);
    const materials = await Material.find(filter)
      .limit(pageSize)
      .skip(pageSize * (page - 1))
      .sort({ name: 1 });

    res.json({
      materials,
      page,
      pages: Math.ceil(count / pageSize),
      total: count,
    });
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: "Server error" });
  }
};

// @desc    Get inventory item by ID
// @route   GET /api/inventory/:id
// @access  Private
const getMaterialById = async (req, res) => {
  try {
    const material = await Material.findById(req.params.id);

    if (material) {
      res.json(material);
    } else {
      res.status(404).json({ message: "Inventory item not found" });
    }
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: "Server error" });
  }
};

// @desc    Update inventory item
// @route   PUT /api/inventory/:id
// @access  Private
const updateMaterial = async (req, res) => {
  try {
    const material = await Material.findById(req.params.id);

    if (material) {
      // Check if updating SKU and if it already exists
      if (req.body.sku && req.body.sku !== material.sku) {
        const existingItem = await Material.findOne({ sku: req.body.sku });
        if (existingItem) {
          return res
            .status(400)
            .json({ message: "Item with this SKU already exists" });
        }
      }

      material.name = req.body.name || material.name;
      material.description = req.body.description || material.description;
      material.sku = req.body.sku || material.sku;
      material.category = req.body.category || material.category;
      material.unitPrice =
        req.body.unitPrice !== undefined
          ? req.body.unitPrice
          : material.unitPrice;
      material.costPrice =
        req.body.costPrice !== undefined
          ? req.body.costPrice
          : material.costPrice;
      material.quantity =
        req.body.quantity !== undefined ? req.body.quantity : material.quantity;
      material.reorderLevel =
        req.body.reorderLevel !== undefined
          ? req.body.reorderLevel
          : material.reorderLevel;
      material.supplier = req.body.supplier || material.supplier;
      material.location = req.body.location || material.location;
      material.images = req.body.images || material.images;
      material.isActive =
        req.body.isActive !== undefined ? req.body.isActive : material.isActive;

      const updatedMaterial = await material.save();
      res.json(updatedMaterial);
    } else {
      res.status(404).json({ message: "Inventory item not found" });
    }
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: "Server error" });
  }
};

// @desc    Delete inventory item
// @route   DELETE /api/inventory/:id
// @access  Private
const deleteMaterial = async (req, res) => {
  try {
    const material = await Material.findById(req.params.id);

    if (material) {
      await material.remove();
      res.json({ message: "Inventory item removed" });
    } else {
      res.status(404).json({ message: "Inventory item not found" });
    }
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: "Server error" });
  }
};

// @desc    Update inventory quantity
// @route   PATCH /api/inventory/:id/quantity
// @access  Private
const updateInventoryQuantity = async (req, res, next) => {
  // Added next
  try {
    // Extract change and transaction details from request body
    const {
      change,
      type = "ADJUSTMENT",
      notes,
      relatedDocId,
      relatedDocModel,
    } = req.body;
    const userId = req.user._id; // Get user ID from auth middleware

    if (change === undefined || typeof change !== "number") {
      return next(new ApiError(400, "Numeric quantity change is required"));
    }
    if (!userId) {
      return next(
        new ApiError(401, "User ID not found, authorization required")
      );
    }

    const material = await Material.findById(req.params.id);

    if (!material) {
      return next(new ApiError(404, "Material not found"));
    }

    // Call the updated model method with transaction details
    const updatedItem = await material.updateQuantity(change, {
      type,
      user: userId,
      notes,
      relatedDocId,
      relatedDocModel,
    });

    res.json(updatedItem);
  } catch (error) {
    console.error(
      `Error updating quantity for material ${req.params.id}:`,
      error
    );
    next(error); // Pass error to centralized handler
  }
};

// @desc    Get low stock items
// @route   GET /api/inventory/low-stock
// @access  Private
const getLowStockItems = async (req, res) => {
  try {
    const lowStockItems = await Material.getLowStockItems();
    res.json(lowStockItems);
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: "Server error" });
  }
};

// @desc    Get inventory categories
// @route   GET /api/inventory/categories
// @access  Private
const getInventoryCategories = async (req, res) => {
  try {
    const categories = await Material.distinct("category");
    res.json(categories.filter((category) => category)); // Filter out null/empty categories
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: "Server error" });
  }
};

// @desc    Get material statistics
// @route   GET /api/materials/stats
// @access  Private
const getMaterialStats = async (req, res, next) => {
  try {
    const totalItems = await Material.countDocuments();
    const lowStockCount = await Material.countDocuments({
      $expr: { $lte: ["$quantity", "$reorderLevel"] },
      isActive: true,
    });
    const categories = await Material.distinct("category");
    const totalValue = await Material.aggregate([
      { $match: { isActive: true } },
      { $project: { itemValue: { $multiply: ["$quantity", "$costPrice"] } } },
      { $group: { _id: null, totalValue: { $sum: "$itemValue" } } },
    ]);

    res.json({
      totalItems,
      lowStock: lowStockCount,
      categories: categories.filter((c) => c).length,
      value: totalValue[0]?.totalValue || 0,
    });
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: "Server error fetching stats" });
    next(error); // Pass error to centralized handler
  }
};

// @desc    Get transaction history for a material item
// @route   GET /api/materials/:id/transactions
// @access  Private
const getMaterialTransactions = async (req, res, next) => {
  try {
    const { id } = req.params;
    const page = parseInt(req.query.page, 10) || 1;
    const limit = parseInt(req.query.limit, 10) || 10;
    const skip = (page - 1) * limit;

    // Validate Material ID
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return next(new ApiError(400, "Invalid Material ID format"));
    }

    // Check if material exists (optional, but good practice)
    const materialExists = await Material.findById(id).select("_id");
    if (!materialExists) {
      return next(new ApiError(404, "Material not found"));
    }

    // Query transactions for the specific material
    const query = { material: id };
    const totalTransactions = await MaterialTransaction.countDocuments(query);
    const transactions = await MaterialTransaction.find(query)
      .populate("user", "firstName lastName email") // Populate user details
      // .populate('relatedDocId') // Generic population - might need specific model population based on relatedDocModel
      .sort({ timestamp: -1 }) // Sort by most recent first
      .skip(skip)
      .limit(limit);

    res.json({
      transactions,
      page: page,
      limit: limit,
      totalPages: Math.ceil(totalTransactions / limit),
      total: totalTransactions,
    });
  } catch (error) {
    console.error(
      `Error fetching transactions for material ${req.params.id}:`,
      error
    );
    next(error); // Pass error to centralized handler
  }
};

module.exports = {
  createMaterial,
  getMaterials,
  getMaterialById,
  updateMaterial,
  deleteMaterial,
  updateInventoryQuantity,
  getLowStockItems,
  getInventoryCategories,
  getMaterialStats, // Added export
  getMaterialTransactions, // Added export
};
