const {
  GoogleGenerativeAI,
  GoogleGenerativeAIResponseError,
  HarmCategory,
  HarmBlockThreshold,
} = require("@google/generative-ai");
const {
  // AiBaseError, // Removed as it's not exported by aiErrors.js; AiServiceError is the base.
  AiRateLimitError,
  AiContentPolicyError,
  // AiBadResponseError, // Removed as it's not exported by aiErrors.js and not used
  AiTimeoutError,
  // AiServerError,      // Removed as it's not exported by aiErrors.js and not used
  AiServiceUnavailableError,
  AiInvalidResponseError,
  AiInvalidRequestError,
  AiContextLengthError,
  isRetryableError, // Utility function
  getRetryDelay, // Utility function
  AiServiceError,
} = require("./aiErrors"); // Corrected path to be local to utils directory
const logger = require("./logger"); // MOVED UP
const fs = require("fs"); // Import fs for file reading
const path = require("path"); // Import path for constructing file paths
const { withRetry, circuitBreakers } = require("./retryUtils");

// Diagnostic log is now safe to use logger
logger.debug(
  `[GeminiService Module Load] typeof AiServiceError: ${typeof AiServiceError}, AiServiceError: ${JSON.stringify(
    AiServiceError
  )}, typeof GoogleGenerativeAIResponseError: ${typeof GoogleGenerativeAIResponseError}, GoogleGenerativeAIResponseError: ${JSON.stringify(
    GoogleGenerativeAIResponseError
  )}`
);

// Initialize the Gemini API
const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY);

// Track API health metrics
const healthMetrics = {
  totalRequests: 0,
  successfulRequests: 0,
  failedRequests: 0,
  retryAttempts: 0,
  lastRequestTime: null,
  lastErrorTime: null,
  lastErrorMessage: null,
};

// Model configurations from environment variables
// Define models with JSON mode support flag
// Token limit configuration for different Gemini models
const MODEL_CONFIGS = {
  // Gemini 2.5 Pro models
  "models/gemini-2.5-pro-preview-03-25": {
    inputTokenLimit: 1048576, // 1M tokens
    outputTokenLimit: 65536, // 64K tokens
    supportsJsonMode: true,
  },
  "models/gemini-2.5-pro-preview-05-06": {
    inputTokenLimit: 1048576, // 1M tokens
    outputTokenLimit: 65536, // 64K tokens
    supportsJsonMode: true,
  },

  // Gemini 2.5 Flash models
  "models/gemini-2.5-flash-preview-05-20": {
    inputTokenLimit: 1048576, // 1M tokens
    outputTokenLimit: 65536, // 64K tokens
    supportsJsonMode: true,
  },

  // Gemini 2.0 models
  "models/gemini-2.0-flash": {
    inputTokenLimit: 1048576, // 1M tokens
    outputTokenLimit: 8192, // 8K tokens
    supportsJsonMode: true,
  },
  "models/gemini-2.0-flash-lite": {
    inputTokenLimit: 1048576, // 1M tokens
    outputTokenLimit: 8192, // 8K tokens
    supportsJsonMode: true,
  },

  // Gemini 1.5 models
  "models/gemini-1.5-pro": {
    inputTokenLimit: 1048576, // 1M tokens
    outputTokenLimit: 8192, // 8K tokens
    supportsJsonMode: true,
  },
  "models/gemini-1.5-flash": {
    inputTokenLimit: 1048576, // 1M tokens
    outputTokenLimit: 8192, // 8K tokens
    supportsJsonMode: true,
  },

  // Default fallback configuration for any unspecified models
  default: {
    inputTokenLimit: 32000, // Conservative default
    outputTokenLimit: 8192, // Conservative default
    supportsJsonMode: true,
  },
};

// Function to get token limits for a model
function getModelTokenLimits(modelName) {
  // Extract just the model name from potential formats like "models/gemini-2.5-pro-preview-05-06"
  const normalizedModelName = modelName.includes("/")
    ? modelName
    : `models/${modelName}`;

  // Check if we have specific config for this model
  if (MODEL_CONFIGS[normalizedModelName]) {
    return MODEL_CONFIGS[normalizedModelName];
  }

  // If not found by exact name, try to match by pattern
  const modelPattern = normalizedModelName.split("-").slice(0, 3).join("-"); // Get "models/gemini-2.5-pro" from "models/gemini-2.5-pro-preview-05-06"

  for (const configModel in MODEL_CONFIGS) {
    if (configModel !== "default" && configModel.startsWith(modelPattern)) {
      logger.info(
        `[GeminiService] Using token limits from ${configModel} for model ${normalizedModelName}`
      );
      return MODEL_CONFIGS[configModel];
    }
  }

  // Fallback to default if no match found
  logger.warn(
    `[GeminiService] No specific token limits found for model ${normalizedModelName}, using defaults`
  );
  return MODEL_CONFIGS.default;
}

const MODELS = {
  PRIMARY: {
    name: process.env.AI_MODEL_PRIMARY || "gemini-1.5-pro-latest", // Default to Gemini 1.5 Pro
    requestsPerMinute: parseInt(process.env.AI_PRIMARY_RATE_LIMIT || "10"), // Gemini 1.5 Pro: 10 RPM (Pay-as-you-go)
    supportsJsonMode: true, // Gemini 1.5 Pro supports JSON mode
    lastRequest: 0,
    requestCount: 0,
    type: "gemini",
  },
  FALLBACK_1: {
    name: process.env.AI_MODEL_FALLBACK_1 || "gemini-1.5-flash-latest", // Default to Gemini 1.5 Flash
    requestsPerMinute: parseInt(process.env.AI_FALLBACK_1_RATE_LIMIT || "120"), // Gemini 1.5 Flash: 120 RPM (Pay-as-you-go)
    supportsJsonMode: true, // Gemini 1.5 Flash supports JSON mode
    lastRequest: 0,
    requestCount: 0,
    type: "gemini",
  },
  FALLBACK_2: {
    name: process.env.AI_MODEL_FALLBACK_2 || "models/gemini-1.5-flash", // Assuming this is also gemini-1.5-flash-latest
    requestsPerMinute: parseInt(process.env.AI_FALLBACK_2_RATE_LIMIT || "120"), // Gemini 1.5 Flash: 120 RPM
    supportsJsonMode: true, // 1.5 Flash supports JSON mode
    lastRequest: 0,
    requestCount: 0,
    type: "gemini",
  },
  FALLBACK_3: {
    name: process.env.AI_MODEL_FALLBACK_3 || "models/gemini-2.0-flash-lite", // Or a more specific newer flash model if applicable
    requestsPerMinute: parseInt(process.env.AI_FALLBACK_3_RATE_LIMIT || "30"), // RPM for this specific model might need verification if it's not a standard one
    supportsJsonMode: false, // Assuming based on 'lite' and previous setting
    lastRequest: 0,
    requestCount: 0,
    type: "gemini",
  },
};

// Log model configuration on startup using logger with structured logging to prevent truncation
logger.info("AI Model Configuration initialized with the following models:");
logger.info(
  `Primary Model: ${MODELS.PRIMARY.name} (${MODELS.PRIMARY.requestsPerMinute} RPM, JSON: ${MODELS.PRIMARY.supportsJsonMode})`
);
logger.info(
  `Fallback 1: ${MODELS.FALLBACK_1.name} (${MODELS.FALLBACK_1.requestsPerMinute} RPM, JSON: ${MODELS.FALLBACK_1.supportsJsonMode})`
);
logger.info(
  `Fallback 2: ${MODELS.FALLBACK_2.name} (${MODELS.FALLBACK_2.requestsPerMinute} RPM, JSON: ${MODELS.FALLBACK_2.supportsJsonMode})`
);
logger.info(
  `Fallback 3: ${MODELS.FALLBACK_3.name} (${MODELS.FALLBACK_3.requestsPerMinute} RPM, JSON: ${MODELS.FALLBACK_3.supportsJsonMode})`
);

// Queue for API requests
const requestQueue = [];
let isProcessingQueue = false;

// Process queue with delay between requests
async function processQueue() {
  if (isProcessingQueue) return;
  isProcessingQueue = true;

  while (requestQueue.length > 0) {
    const request = requestQueue.shift();
    try {
      const result = await executeRequestWithFallback(request);
      request.resolve(result);
    } catch (error) {
      request.reject(error);
    }
    // Removed fixed delay. Throttling/backoff is handled by withRetry and circuit breaker.
  }

  isProcessingQueue = false;
}

/**
 * Helper function to handle prompt continuation for very long outputs
 * This implements a chaining approach where we detect if response is likely truncated
 * and continue generating from where it left off
 * @param {string} modelName - The model name to use
 * @param {string} initialSystemPrompt - The original system prompt
 * @param {string} userPrompt - The user prompt
 * @param {string} currentResponse - The response generated so far
 * @param {Object} options - Additional options for the request
 * @param {number} attemptCount - Current continuation attempt (to prevent infinite loops)
 * @returns {Promise<string>} - The complete response after chaining
 */
async function handlePromptContinuation(
  modelName,
  initialSystemPrompt,
  userPrompt,
  currentResponse,
  options = {},
  attemptCount = 0
) {
  // Reduce max attempts and add max total time constraint
  const MAX_CONTINUATION_ATTEMPTS = options.maxContinuationAttempts || 2; // Default to just 2 attempts
  const MAX_CONTINUATION_TIME_MS = options.maxContinuationTimeMs || 60000; // Default to 1 minute total time
  const startTime = Date.now();

  const TRUNCATION_MARKERS = [
    "[CONTINUE]",
    "[CONTINUED]",
    "...", // Be careful with this one as it might occur naturally
    "To be continued",
    "I need to continue",
  ];

  // Check if we've hit the max attempts, time limit, or response is already very long
  const elapsedTime = Date.now() - startTime;
  if (
    attemptCount >= MAX_CONTINUATION_ATTEMPTS ||
    elapsedTime > MAX_CONTINUATION_TIME_MS
  ) {
    const reason =
      attemptCount >= MAX_CONTINUATION_ATTEMPTS ? "max attempts" : "time limit";
    logger.warn(
      `[GeminiService] Stopping continuation due to ${reason} (${elapsedTime}ms elapsed). Returning current response.`
    );
    return currentResponse;
  }

  // If the response is already extremely long, stop continuation
  const MAX_REASONABLE_RESPONSE_LENGTH = 50000; // About 12,500 tokens
  if (currentResponse.length > MAX_REASONABLE_RESPONSE_LENGTH) {
    logger.warn(
      `[GeminiService] Response already very long (${currentResponse.length} chars). Stopping continuation.`
    );
    return currentResponse;
  }

  // Detect potential truncation based on markers or apparent incompleteness
  const hasTruncationMarker = TRUNCATION_MARKERS.some((marker) =>
    currentResponse.trim().endsWith(marker)
  );

  const looksIncomplete = isPotentiallyIncomplete(currentResponse);

  if (!hasTruncationMarker && !looksIncomplete) {
    return currentResponse; // Response appears complete
  }

  logger.info(
    `[GeminiService] Response appears incomplete. Attempting continuation (attempt ${
      attemptCount + 1
    }/${MAX_CONTINUATION_ATTEMPTS}).`
  );

  // Remove continuation markers if present
  let cleanedResponse = currentResponse;
  for (const marker of TRUNCATION_MARKERS) {
    if (cleanedResponse.trim().endsWith(marker)) {
      cleanedResponse = cleanedResponse.substring(
        0,
        cleanedResponse.lastIndexOf(marker)
      );
      break;
    }
  }

  // Create continuation prompt - with optimized instructions
  const continuationSystemPrompt = `${initialSystemPrompt}\n\nThis is a continuation request. You previously provided a partial response that needs to be completed. IMPORTANT INSTRUCTIONS:\n1. Start EXACTLY where the previous response ended without any repetition\n2. DO NOT summarize or recap the previous content\n3. DO NOT use phrases like "Continuing from where I left off"\n4. Keep the same format, style, and tone as the original response\n5. Maintain the same JSON structure if applicable`;

  const continuationUserPrompt = `${userPrompt}\n\nYour previous response (DO NOT REPEAT THIS):\n${cleanedResponse.trim()}`;

  // Execute the continuation request
  const modelConfig = selectModelForRequest(modelName, options);
  try {
    const continuationResponse = await executeGeminiModelInternal(
      modelConfig,
      continuationSystemPrompt,
      continuationUserPrompt,
      options
    );

    // Combine the original and continuation responses
    const combinedResponse = `${cleanedResponse}\n${continuationResponse}`;

    // Check if we need to continue further (recursive call)
    return handlePromptContinuation(
      modelName,
      initialSystemPrompt,
      userPrompt,
      combinedResponse,
      options,
      attemptCount + 1
    );
  } catch (error) {
    logger.error(
      `[GeminiService] Error during continuation attempt ${attemptCount + 1}: ${
        error.message
      }`
    );
    // Return what we have so far rather than failing completely
    return cleanedResponse;
  }
}

/**
 * Helper function to detect if a response appears incomplete based on heuristics
 * @param {string} text - The response text to analyze
 * @returns {boolean} - True if the response appears incomplete
 */
// Helper function to check if a string is valid JSON
function isValidJsonResponse(text) {
  if (!text || typeof text !== "string") return false;

  try {
    // Try to parse as JSON
    JSON.parse(text);
    return true;
  } catch (e) {
    // Look for incomplete JSON structure
    return false;
  }
}

function isPotentiallyIncomplete(text) {
  if (!text || typeof text !== "string") return false;

  const trimmedText = text.trim();

  // First check for complete sentence with terminal punctuation
  const hasCompleteEnding = /[.!?;\n]\s*$/.test(trimmedText);
  if (hasCompleteEnding && trimmedText.length > 500) {
    // Long response with proper ending punctuation is likely complete
    return false;
  }

  // Check for common indicators of an incomplete response
  const incompletenessIndicators = [
    // Ends with an unclosed parenthesis, bracket, or brace
    /[\(\[\{][^\)\]\}]*$/,
    // Ends with a comma or colon suggesting more content should follow
    /[,:]$/,
    // Ends with an unclosed quote
    /"[^"]*$/,
    // Ends with an incomplete sentence (no terminal punctuation)
    /[a-zA-Z0-9]$/,
    // Ends with "and", "or", "but", suggesting a list or sentence continuation
    /\b(and|or|but|as|if|then|because|since|while|when|therefore|thus|for|to)$/i,
    // JSON specific - ends with a property name without a value
    /"\s*:\s*$/,
    // JSON specific - ends with array or object that's not closed
    /\{[^\}]*$|\[[^\]]*$/,
    // Ends with a list number or bullet that's not followed by content
    /\d+\.\s*$|\*\s*$/,
  ];

  return incompletenessIndicators.some((pattern) => pattern.test(trimmedText));
}

/**
 * Find the appropriate model for a given request
 * This is a helper function for handlePromptContinuation
 */
function selectModelForRequest(modelName, options = {}) {
  // If the model name is one of our predefined models, return that model config
  for (const key in MODELS) {
    if (MODELS[key].name === modelName) {
      return MODELS[key];
    }
  }

  // Otherwise, create a temporary model config
  return {
    name: modelName,
    requestsPerMinute: 5, // Conservative default
    supportsJsonMode: true, // Assume modern models support JSON mode
    type: "gemini",
  };
}

// Refactored: Now a wrapper around executeRequestWithFallback
async function getGeminiResponse(systemPrompt, userPrompt, options = {}) {
  const requestDetails = { systemPrompt, userPrompt, options };
  // Pass the function that gets raw text as the task
  return executeRequestWithFallback(requestDetails, executeGeminiModelInternal);
}

// Removed helper function: canUseModel
// Rate limiting is now handled by the withRetry logic using AiRateLimitError and getRetryDelay

// Refactored: Accepts taskFn to execute for each model attempt
async function executeRequestWithFallback(requestDetails, taskFn) {
  const { systemPrompt, userPrompt, options } = requestDetails;

  // Update health metrics
  healthMetrics.totalRequests++;
  healthMetrics.lastRequestTime = Date.now();

  // Use circuit breaker to prevent overwhelming the service during outages
  return await circuitBreakers.gemini.execute(async () => {
    const modelsToTry = [
      MODELS.PRIMARY,
      MODELS.FALLBACK_1,
      MODELS.FALLBACK_2,
      MODELS.FALLBACK_3,
    ];
    let lastError = null;

    for (const model of modelsToTry) {
      if (!model) continue; // Skip if a fallback model is not defined in env

      const modelNameForLog = Object.keys(MODELS).find(
        (key) => MODELS[key] === model
      ); // Get key like 'PRIMARY', 'FALLBACK_1'

      // Removed check: if (await canUseModel(model)) {
      // Rely on the API call within withRetry to throw AiRateLimitError if needed
      logger.info(`Attempting model: ${modelNameForLog} (${model.name})`); // Use logger
      try {
        // Use retry with exponential backoff for transient errors within the taskFn execution
        const result = await withRetry(
          () => taskFn(model, systemPrompt, userPrompt, options), // Call the passed taskFn
          {
            maxRetries: model === MODELS.PRIMARY ? 2 : 1, // More retries for primary
            baseDelay: 300, // Base delay for exponential backoff if getRetryDelay doesn't apply
            maxDelay: 30000, // Increase max delay to accommodate potential longer retryAfter values
            shouldRetry: isRetryableError, // Use the imported helper
            getRetryDelay: getRetryDelay, // Use the imported helper for delay calculation
            onRetry: (error, attempt, delay) => {
              // Added delay parameter to log
              logger.warn(
                `Retrying ${modelNameForLog} (Attempt ${attempt}) after ${delay}ms due to error: ${error.message}`
              ); // Use logger
              healthMetrics.retryAttempts++;
            },
          }
        );

        // Update success metrics
        healthMetrics.successfulRequests++;
        logger.info(`Success with model: ${modelNameForLog}`); // Use logger
        return result; // Return successful result immediately
      } catch (error) {
        const mappedError = error; // Error should already be mapped by executeGeminiModelInternal or _executeAndParseJsonInternal
        lastError = mappedError; // Store the mapped error
        logger.error(
          `[AI Request] Model ${modelNameForLog} (${model.name}) failure. Initial error: [${error.name}] ${error.message}. Mapped error: [${mappedError.name}] ${mappedError.message}`,
          {}
        );

        // Check if the error is definitively non-retryable and indicates a request issue
        if (
          !mappedError.retryable &&
          (mappedError instanceof AiContentPolicyError ||
            mappedError instanceof AiInvalidRequestError ||
            mappedError instanceof AiContextLengthError)
        ) {
          logger.warn(
            `[AI Request] Non-retryable error (${mappedError.name}) encountered with ${modelNameForLog}. Stopping fallback chain.`
          );
          throw mappedError; // Throw immediately, don't try other fallbacks
        }

        // Log specific messages for common retryable/fallback issues
        if (mappedError instanceof AiRateLimitError) {
          logger.warn(
            `[AI Request] ${modelNameForLog} rate limited, trying next fallback...`
          );
        } else if (
          mappedError instanceof AiServiceUnavailableError ||
          mappedError instanceof AiTimeoutError
        ) {
          logger.warn(
            `[AI Request] ${modelNameForLog} unavailable or timed out, trying next fallback...`
          );
        } else if (mappedError instanceof AiInvalidResponseError) {
          // <-- Check for invalid format/empty
          logger.warn(
            `[AI Request] ${modelNameForLog} failed due to invalid format/empty response, trying next fallback...`
          );
        } else {
          // Log other retryable errors before proceeding
          const currentModelIndex = modelsToTry.findIndex(
            (m) => m && m.name === model.name
          );
          const attemptNumber =
            currentModelIndex !== -1 ? currentModelIndex + 1 : "N/A";
          const totalConfiguredModels = modelsToTry.filter((m) => m).length;
          logger.warn(
            `[AI Request] Encountered retryable error (${
              mappedError.name || mappedError.constructor.name
            }) with ${modelNameForLog} (attempt ${attemptNumber}/${totalConfiguredModels}): "${
              mappedError.message
            }". Trying next fallback if available.`,
            {}
          );
        }
        // Continue loop for retryable errors AND AiInvalidResponseError
      }
      // Removed the corresponding 'else' block for the canUseModel check
    } // End of loop through models

    // If loop completes without returning, all models failed or were rate-limited
    healthMetrics.failedRequests++;
    healthMetrics.lastErrorTime = Date.now();
    healthMetrics.lastErrorMessage = lastError
      ? lastError.message
      : "All models exhausted or failed";

    logger.error(
      `All models failed. Last error recorded: ${healthMetrics.lastErrorMessage}`
    ); // Existing log

    // Add detailed final error log before throwing
    if (lastError) {
      const mappedLastError = mapGeminiError(lastError); // Ensure it's mapped
      logger.error(
        `[AI Fallback Exhausted] Throwing final error after trying all models. Last Error Type: ${mappedLastError.name}, Message: ${mappedLastError.message}`,
        {
          originalError: mappedLastError.originalError, // Include original error details if available
          // Consider adding request context if easily available (e.g., prompt type)
        }
      );
      throw mappedLastError; // Throw the mapped error
    } else {
      // This case should ideally not happen if the loop finishes, but handle defensively
      const genericError = new AiServiceUnavailableError(
        "All AI models failed, but no specific last error was recorded."
      );
      logger.error(
        "[AI Fallback Exhausted] Throwing generic error as no specific last error was found."
      );
      throw genericError;
    }

    // Fallback response logic is now handled by the caller catching the error
    // logger.info('All models exhausted, using fallback response'); // Use logger
    // return options.fallbackResponse ||
    //   "Service temporarily unavailable due to high demand. Please try again later.";
  });
}

// Execute request with specific model and update its stats
async function executeWithModel(model, systemPrompt, userPrompt, options = {}) {
  try {
    // Update model stats
    model.lastRequest = Date.now();
    model.requestCount++;

    // Get model instance
    const modelInstance = genAI.getGenerativeModel({
      model: model.name,
      generationConfig: {
        temperature: options.temperature || 0.7,
        topP: options.topP || 0.95,
        topK: options.topK || 40,
        maxOutputTokens: options.maxTokens || 1024,
        stopSequences: options.stopSequences || [],
      },
      safetySettings: options.safetySettings || [
        {
          category: "HARM_CATEGORY_HARASSMENT",
          threshold: "BLOCK_MEDIUM_AND_ABOVE",
        },
        {
          category: "HARM_CATEGORY_HATE_SPEECH",
          threshold: "BLOCK_MEDIUM_AND_ABOVE",
        },
        {
          category: "HARM_CATEGORY_SEXUALLY_EXPLICIT",
          threshold: "BLOCK_MEDIUM_AND_ABOVE",
        },
        {
          category: "HARM_CATEGORY_DANGEROUS_CONTENT",
          threshold: "BLOCK_MEDIUM_AND_ABOVE",
        },
      ],
    });

    // Create chat session
    const chat = modelInstance.startChat({
      history: [
        {
          role: "user",
          parts: [{ text: systemPrompt }],
        },
        {
          role: "model",
          parts: [{ text: "I understand. I will follow these instructions." }],
        },
      ],
      generationConfig: {
        temperature: options.temperature || 0.7,
        maxOutputTokens: options.maxTokens || 1024,
      },
    });

    // Send message and get response
    const result = await chat.sendMessage(userPrompt);
    const response = await result.response;
    const text = response.text();

    // Validate response
    validateAiResponse(text, options);

    return text;
  } catch (error) {
    // Map error to specific AI error types
    const mappedError = mapGeminiError(error, model.name, {
      modelRpm: model.requestsPerMinute,
    });
    throw mappedError;
  }
}

/**
 * Maps Gemini API errors to our custom error types.
 * Assumes AiBaseError is the base class for all custom AI errors.
 * Assumes GoogleGenerativeAIResponseError is from the Gemini SDK.
 * @param {Error} error - The original error from Gemini API.
 * @param {string} [modelName='unknown_model'] - The name of the model for which the error occurred.
 * @returns {AiBaseError} - Mapped custom error.
 */
function mapGeminiError(error, modelName = "unknown_model", context = {}) {
  logger.debug(
    `[mapGeminiError] Received error for ${modelName}. Name: ${error.name}, Message: ${error.message}`,
    { originalError: error, modelName, context }
  );

  // Check if already one of our custom AiServiceError types
  logger.debug(
    `[mapGeminiError] Checking AiServiceError: typeof AiServiceError is ${typeof AiServiceError}`
  );
  if (typeof AiServiceError === "function" && error instanceof AiServiceError) {
    // Use AiServiceError as the root custom error type
    logger.debug(
      `[Gemini Service] Error is already an instance of AiServiceError for model '${modelName}'. Returning as is.`
    );
    return error;
  }

  // Handle structured GoogleGenerativeAIResponseError, especially for content policy
  // Ensure GoogleGenerativeAIResponseError is imported or available in scope if needed.
  logger.debug(
    `[mapGeminiError] Checking GoogleGenerativeAIResponseError: typeof GoogleGenerativeAIResponseError is ${typeof GoogleGenerativeAIResponseError}`
  );
  if (
    typeof GoogleGenerativeAIResponseError === "function" &&
    error instanceof GoogleGenerativeAIResponseError
  ) {
    if (error.response?.promptFeedback?.blockReason) {
      const { blockReason, safetyRatings } = error.response.promptFeedback;
      logger.warn(
        `[Gemini Service] Content policy violation for model '${modelName}'. Reason: ${blockReason}`,
        { safetyRatings }
      );
      return new AiContentPolicyError(
        `Response blocked by content policy: ${blockReason}`,
        {
          originalError: error,
          blockReason,
          safetyRatings,
          modelName,
        }
      );
    }
    logger.debug(
      `[Gemini Service] Encountered GoogleGenerativeAIResponseError for model '${modelName}' without specific blockReason. Proceeding with status/message checks.`,
      { response: error.response }
    );
  }

  // Check for rate limit errors (status 429 is a strong indicator)
  if (
    error.status === 429 ||
    error.message?.includes("rate limit") ||
    error.message?.includes("quota exceeded")
  ) {
    let googleRetryAfterSeconds = null; // Initialize to null to distinguish from parsed '0s'
    try {
      const errorMessageString =
        error.message ||
        JSON.stringify(error.cause) ||
        JSON.stringify(error.details) ||
        "";
      const match = errorMessageString.match(/"retryDelay":"(\d+)s"/);
      if (match && match[1]) {
        googleRetryAfterSeconds = parseInt(match[1], 10);
        logger.debug(
          `[mapGeminiError] Parsed googleRetryAfterSeconds: ${googleRetryAfterSeconds}s for model ${modelName}`
        );
      }
    } catch (e) {
      logger.warn(
        `[Gemini Service] Error parsing Google's retryDelay for model '${modelName}', will use RPM-based or default: ${e.message}`
      );
    }

    let finalRetryAfterSeconds;
    if (googleRetryAfterSeconds !== null) {
      finalRetryAfterSeconds = googleRetryAfterSeconds;
      logger.info(
        `[Gemini Service] Using Google's suggested retry_after for ${modelName}: ${finalRetryAfterSeconds}s`
      );
    } else if (
      context?.modelRpm &&
      Number.isFinite(context.modelRpm) &&
      context.modelRpm > 0
    ) {
      // Calculate based on model's RPM if Google didn't specify and RPM is valid
      // Add 1 to ensure it's at least 1s, plus jitter up to 5s.
      finalRetryAfterSeconds =
        Math.ceil(60 / context.modelRpm) + Math.floor(Math.random() * 5) + 1;
      logger.info(
        `[Gemini Service] Using RPM-based retry_after for ${modelName}: ${finalRetryAfterSeconds}s (RPM: ${context.modelRpm})`
      );
    } else {
      finalRetryAfterSeconds = 60; // Default if no Google delay and no/invalid RPM
      logger.warn(
        `[Gemini Service] No Google retryDelay or valid modelRpm for ${modelName} (RPM: ${context?.modelRpm}). Defaulting retry_after to ${finalRetryAfterSeconds}s.`
      );
    }

    return new AiRateLimitError("AI service rate limit exceeded", {
      originalError: error,
      retryAfter: finalRetryAfterSeconds, // Use the determined value
      modelName,
      detailsFromGoogle: error.message, // Preserve original message for debugging
    });
  }

  // Check for context length errors
  if (
    error.message?.includes("context length") ||
    error.message?.includes("token limit") ||
    error.message?.includes("input was too long")
  ) {
    return new AiContextLengthError("AI context length exceeded", {
      originalError: error,
      modelName,
    });
  }

  // Check for content policy violations (message-based fallback)
  if (
    error.message?.includes("content policy") ||
    error.message?.includes("safety") ||
    (!(
      typeof GoogleGenerativeAIResponseError !== "undefined" &&
      error instanceof GoogleGenerativeAIResponseError
    ) &&
      error.message?.includes("blocked"))
  ) {
    return new AiContentPolicyError(
      `AI content policy violation: ${error.message}`,
      {
        originalError: error,
        modelName,
      }
    );
  }

  // Check for timeout errors
  if (
    error.message?.includes("timeout") ||
    error.message?.includes("deadline exceeded") ||
    error.code === "ETIMEDOUT" ||
    error.name === "AbortError"
  ) {
    return new AiTimeoutError("AI service request timed out", {
      originalError: error,
      modelName,
    });
  }

  // Check for service unavailable errors (5xx)
  if (
    error.status >= 500 ||
    error.message?.includes("unavailable") ||
    error.message?.includes("server error")
  ) {
    return new AiServiceUnavailableError(
      "AI service is temporarily unavailable",
      {
        originalError: error,
        modelName,
      }
    );
  }

  // Check for invalid request errors (4xx, excluding 429)
  if (error.status >= 400 && error.status < 500 && error.status !== 429) {
    return new AiInvalidRequestError(
      `Invalid AI request parameters: ${error.message}`,
      {
        originalError: error,
        modelName,
        statusCode: error.status,
      }
    );
  }

  // Default to generic AI service error
  logger.warn(
    `[Gemini Service] Unhandled AI error for model '${modelName}', mapping to generic AiServiceError. Original: ${error.message}`
  );
  return new AiServiceError(
    error.status || 500,
    `AI service error with model ${modelName}: ${error.message}`,
    {
      originalError: error,
      modelName,
    }
  );
}

/**
 * Validates AI response to ensure it meets requirements
 * @param {string} response - The AI response text
 * @param {Object} options - Validation options
 * @throws {AiInvalidResponseError} - If response is invalid
 */
function validateAiResponse(response, options = {}) {
  // Check if response is empty or too short
  if (!response || response.trim().length === 0) {
    throw new AiInvalidResponseError("Empty AI response");
  }

  // Check if response is too short (if minimum length is specified)
  if (options.minLength && response.length < options.minLength) {
    throw new AiInvalidResponseError(
      `AI response too short (${response.length} chars)`,
      {
        response,
        minLength: options.minLength,
      }
    );
  }

  // Check if response is too long (if maximum length is specified)
  if (options.maxLength && response.length > options.maxLength) {
    throw new AiInvalidResponseError(
      `AI response too long (${response.length} chars)`,
      {
        response: response.substring(0, 100) + "...",
        maxLength: options.maxLength,
      }
    );
  }

  // Check if response format is valid (if expected format is specified)
  if (options.expectedFormat === "json") {
    try {
      JSON.parse(response);
    } catch (error) {
      throw new AiInvalidResponseError("Invalid JSON response format", {
        response: response.substring(0, 100) + "...",
        parseError: error.message,
      });
    }
  }

  // Check if response contains required fields (if specified)
  if (options.requiredFields && options.expectedFormat === "json") {
    try {
      const parsed = JSON.parse(response);
      for (const field of options.requiredFields) {
        if (!(field in parsed)) {
          throw new AiInvalidResponseError(
            `Missing required field '${field}' in response`,
            {
              response: response.substring(0, 100) + "...",
              missingField: field,
            }
          );
        }
      }
    } catch (error) {
      if (error instanceof AiInvalidResponseError) {
        throw error;
      }
      throw new AiInvalidResponseError("Error validating response fields", {
        response: response.substring(0, 100) + "...",
        error: error.message,
      });
    }
  }
}

async function executeGeminiModelInternal(
  modelConfig,
  systemPrompt,
  userPrompt,
  options = {}
) {
  // options can now include imageDetails: [{ path: string, mimeType: string }]
  // Get model-specific token limits
  const modelTokenLimits = getModelTokenLimits(modelConfig.name);
  logger.debug(
    `[GeminiService] Using token limits for ${modelConfig.name}: inputLimit=${modelTokenLimits.inputTokenLimit}, outputLimit=${modelTokenLimits.outputTokenLimit}`
  );

  // Define base generation config with model-aware token limits
  const genConfig = {
    temperature: options.temperature || 0.4,
    // Use passed maxTokens, model-specific limit, or fallback to default
    maxOutputTokens:
      options.maxTokens || modelTokenLimits.outputTokenLimit || 8192,
    topP: options.topP || 0.95,
    topK: options.topK || 40,
  };

  // Conditionally add JSON mode if supported by the model AND grounding tools are NOT being used
  if (
    modelConfig.supportsJsonMode &&
    !options.tools?.some((tool) => tool.googleSearchRetrieval)
  ) {
    genConfig.responseMimeType = "application/json";
    logger.info(`Using JSON mode for model: ${modelConfig.name}`);
  }

  const modelParams = {
    model: modelConfig.name,
    generationConfig: genConfig, // Use the constructed config
    // Assuming safety settings are handled elsewhere or using defaults if not passed in options
  };
  // Add tools if provided in options
  if (options.tools) {
    modelParams.tools = options.tools;
    logger.info(
      `[Gemini Service] Using tools configuration for model ${
        modelConfig.name
      }: ${JSON.stringify(options.tools)}`
    );
  }
  const model = genAI.getGenerativeModel(modelParams);

  logger.info(
    `[Gemini Service] Executing model ${modelConfig.name}. JSON Mode: ${
      genConfig.responseMimeType === "application/json"
    }. Image Parts: ${
      options.imageDetails?.length || 0
    }. Tools: ${!!options.tools}`
  );
  logger.debug(`[Gemini Service] Model Params for ${modelConfig.name}:`, {
    modelParams,
  }); // Includes generationConfig

  try {
    // --- Construct content parts (text + optional images) ---
    const contentParts = [];

    // 1. Add the text part
    contentParts.push({ text: `${systemPrompt}\n\n${userPrompt}` });

    // 2. Add image parts if provided with enhanced analysis
    if (
      options.imageDetails &&
      Array.isArray(options.imageDetails) &&
      options.imageDetails.length > 0
    ) {
      logger.info(
        `[Gemini Service] Processing ${options.imageDetails.length} image(s) for multimodal request with enhanced analysis.`
      );
      for (const imageDetail of options.imageDetails) {
        try {
          // Construct the full path, handling both relative and absolute paths
          let imagePath;
          if (path.isAbsolute(imageDetail.path)) {
            // If the path is already absolute, use it directly
            imagePath = imageDetail.path;
            logger.debug(
              `[Gemini Service] Using absolute image path: ${imagePath}`
            );
          } else {
            // If the path is relative, join it with the backend directory
            imagePath = path.join(__dirname, "..", imageDetail.path);
            logger.debug(
              `[Gemini Service] Constructed relative image path: ${imagePath}`
            );
          }
          logger.debug(
            `[Gemini Service] Reading image file from: ${imagePath}`
          );

          if (!fs.existsSync(imagePath)) {
            logger.warn(
              `[Gemini Service] Image file not found at path: ${imagePath}. Skipping this image.`
            );
            continue; // Skip this image if not found
          }

          const imageBuffer = fs.readFileSync(imagePath);
          const base64Data = imageBuffer.toString("base64");

          // Add image metadata to help with analysis
          let imageMetadata = "";
          if (imageDetail.originalName) {
            imageMetadata += `Original filename: ${imageDetail.originalName}\n`;
          }
          if (imageDetail.size) {
            imageMetadata += `File size: ${(imageDetail.size / 1024).toFixed(
              2
            )} KB\n`;
          }

          // Add the image metadata as a text part before the image
          if (imageMetadata) {
            contentParts.push({
              text: `IMAGE METADATA:\n${imageMetadata}\nPlease analyze this image in extreme detail, looking for all visible electrical components, wiring, hazards, and code violations.`,
            });
          }

          contentParts.push({
            inlineData: {
              data: base64Data,
              mimeType: imageDetail.mimeType,
            },
          });
          logger.debug(
            `[Gemini Service] Added image ${imageDetail.path} (${imageDetail.mimeType}) to request parts with enhanced metadata.`
          );
        } catch (fileError) {
          logger.error(
            `[Gemini Service] Error reading or processing image file ${imageDetail.path}: ${fileError.message}`,
            { stack: fileError.stack }
          );
          // Optionally decide whether to continue without the image or throw an error
          // For now, we'll log the error and continue without this specific image
        }
      }
    }
    // --- End Construct content parts ---

    // Add timeout to prevent hanging requests - adjusted for better performance
    const timeoutPromise = new Promise((_, reject) => {
      // Reduced base timeout for better user experience
      const baseTimeoutMs = parseInt(process.env.GEMINI_TIMEOUT || "60000"); // 1 minute base (reduced from 3 minutes)
      const tokenScalingFactor = 0.01; // Reduced scaling factor (was 0.02)
      const adjustedTimeoutMs =
        baseTimeoutMs + modelTokenLimits.outputTokenLimit * tokenScalingFactor;

      const timeoutMs =
        parseInt(process.env.GEMINI_TIMEOUT_OVERRIDE) || adjustedTimeoutMs; // Allow override
      logger.debug(
        `[Gemini Service] Setting timeout to ${timeoutMs}ms for model ${modelConfig.name}`
      );
      setTimeout(
        () =>
          reject(
            new AiTimeoutError(`AI request timed out after ${timeoutMs}ms`)
          ),
        timeoutMs
      );
    });

    // Race between the actual request and the timeout
    const streamResult = await Promise.race([
      model.generateContentStream({
        // Use the constructed contentParts array
        contents: [{ role: "user", parts: contentParts }],
        ...(options.tools && { tools: options.tools }), // Conditionally add tools
      }),
      timeoutPromise,
    ]);

    logger.debug(
      `[Gemini Service Stream - ${modelConfig.name}] Stream processing started.`
    );
    let aggregatedResponseText = "";
    for await (const chunk of streamResult.stream) {
      const chunkText = chunk.text(); // Get text once
      logger.debug(
        `[Gemini Service Stream - ${
          modelConfig.name
        }] Received chunk. Text length: ${chunkText?.length ?? "undefined"}`
      );
      if (options.onChunk) {
        options.onChunk(chunkText);
      }
      aggregatedResponseText += chunkText;
    }

    logger.debug(
      `[Gemini Service Stream - ${modelConfig.name}] Stream processing finished. Total aggregated length: ${aggregatedResponseText.length}`
    );

    // After loop, check if response is empty (which can happen with safety filters)
    if (!aggregatedResponseText && stream?.response) {
      const blockReason = stream.response.promptFeedback?.blockReason;
      if (blockReason) {
        logger.error(
          `[Gemini Service] Response from ${modelConfig.name} blocked due to safety settings: ${blockReason}`,
          { feedback: stream.response.promptFeedback }
        );
        throw new AiContentPolicyError(
          `Response blocked by content policy: ${blockReason}`,
          {
            originalError: `Block Reason: ${blockReason}`,
            modelName: modelConfig.name,
          }
        );
      } else {
        logger.warn(
          `[Gemini Service] Empty text response from ${modelConfig.name} without explicit block reason.`,
          { fullResponseObject: response }
        );
        throw new AiInvalidResponseError(
          `Empty AI response received from ${modelConfig.name}`,
          { modelName: modelConfig.name }
        );
      }
    }

    validateAiResponse(aggregatedResponseText, {
      modelName: modelConfig.name,
      isEmptyAllowed: false,
    }); // Ensure response is valid

    // Check if the response might need continuation due to token limit restrictions
    if (
      aggregatedResponseText.length > 0 &&
      options.enableContinuation !== false
    ) {
      const modelTokenLimits = getModelTokenLimits(modelConfig.name);
      // If the response is close to the output token limit (80%+), it might be truncated
      // This is a rough estimate as we don't have exact token count
      const estimatedTokens = aggregatedResponseText.length / 4; // Very rough estimate: ~4 chars per token
      const isNearTokenLimit =
        estimatedTokens > modelTokenLimits.outputTokenLimit * 0.8;

      // Check for JSON validity if response is supposed to be JSON
      const isValidJson =
        options.responseFormat === "json"
          ? isValidJsonResponse(aggregatedResponseText)
          : true;

      // Only continue if the response appears incomplete and is not already valid JSON
      if (
        (isNearTokenLimit || isPotentiallyIncomplete(aggregatedResponseText)) &&
        !isValidJson
      ) {
        logger.info(
          `[GeminiService] Response may be incomplete (${estimatedTokens} est. tokens). Attempting continuation.`
        );
        try {
          // Send a progress event if this is a streaming request
          if (options.onProgress && typeof options.onProgress === "function") {
            options.onProgress({
              type: "continuation",
              message: "Response is incomplete, continuing generation...",
              progress: 0,
            });
          }

          // Configure optimized continuation parameters
          const continuationOptions = {
            ...options,
            maxContinuationAttempts: 2, // Limit to 2 attempts max
            maxContinuationTimeMs: 60000, // Limit to 1 minute total
            // Use higher temperature for continuation to encourage completion
            temperature: Math.min((options.temperature || 0.4) + 0.1, 0.9),
          };

          // Try to continue the generation with a timeout
          const continuationPromise = handlePromptContinuation(
            modelConfig.name,
            systemPrompt,
            userPrompt,
            aggregatedResponseText,
            continuationOptions
          );

          // Add an overall timeout for the continuation process
          const timeoutPromise = new Promise((_, reject) => {
            setTimeout(
              () => reject(new Error("Continuation timed out")),
              90000
            ); // 1.5 minute max
          });

          const completeResponse = await Promise.race([
            continuationPromise,
            timeoutPromise,
          ]);

          if (completeResponse !== aggregatedResponseText) {
            logger.info(
              `[GeminiService] Successfully continued response. Original length: ${aggregatedResponseText.length}, New length: ${completeResponse.length}`
            );
            aggregatedResponseText = completeResponse;

            // Send completion progress event
            if (
              options.onProgress &&
              typeof options.onProgress === "function"
            ) {
              options.onProgress({
                type: "continuation_complete",
                message: "Response continuation completed successfully",
                progress: 100,
              });
            }
          }
        } catch (continuationError) {
          logger.warn(
            `[GeminiService] Failed to continue response: ${continuationError.message}. Using original response.`
          );
          // Fall back to the original response rather than failing completely
        }
      }
    }

    // Update model stats on success for this specific model
    // Assuming modelConfig directly holds mutable stats or is a key to a stats object
    // If MODELS_CONFIG is the central mutable store for stats indexed by modelConfig.name:
    if (global.MODELS_CONFIG && global.MODELS_CONFIG[modelConfig.name]) {
      global.MODELS_CONFIG[modelConfig.name].lastRequest = Date.now();
      global.MODELS_CONFIG[modelConfig.name].requestCount =
        (global.MODELS_CONFIG[modelConfig.name].requestCount || 0) + 1;
    } else if (
      modelConfig.hasOwnProperty("lastRequest") &&
      modelConfig.hasOwnProperty("requestCount")
    ) {
      // Fallback if modelConfig itself is supposed to be mutable for these stats
      modelConfig.lastRequest = Date.now();
      modelConfig.requestCount = (modelConfig.requestCount || 0) + 1;
    } else {
      logger.warn(
        `[Gemini Service] Could not find where to update stats for model ${modelConfig.name}.`
      );
    }

    return aggregatedResponseText; // Successful return for the non-tools path
  } catch (error) {
    // Catch block for the main try (API call, response processing)
    // Log the original error with context
    logger.error(
      `[Gemini Service] Error during executeGeminiModelInternal for ${modelConfig.name}: ${error.message}`,
      {
        originalErrorName: error.name,
        originalErrorMessage: error.message,
        modelName: modelConfig.name,
        isTimeout: error.message?.startsWith("Request timed out after"),
        requestOptions: {
          supportsJsonMode: modelConfig.supportsJsonMode,
          toolsRequested: !!options.tools,
          imageCount: options.imageDetails?.length || 0,
        },
        // stack: error.stack // Stack trace can be logged selectively if needed
      }
    );

    if (error instanceof AiServiceError) {
      // If already one of our custom errors, re-throw
      throw error;
    }

    // Handle specific timeout error string from Promise.race
    if (error.message?.startsWith("Request timed out after")) {
      throw new AiTimeoutError(error.message, { modelName: modelConfig.name });
    }

    // For all other errors, pass them through mapGeminiError
    logger.debug(
      `[executeGeminiModelInternal catch] AiServiceError available: ${typeof AiServiceError === 'function'}, GoogleGenerativeAIResponseError available: ${typeof GoogleGenerativeAIResponseError === 'function'}`
    );
    logger.debug(
      `[executeGeminiModelInternal catch] Original error ('error' var): Name: ${
        error?.name
      }, Message: ${error?.message}, Instanceof GoogleError: ${
        typeof GoogleGenerativeAIResponseError === "function" &&
        error instanceof GoogleGenerativeAIResponseError
      }`
    );
    throw mapGeminiError(error, modelConfig.name, {
      modelRpm: modelConfig.requestsPerMinute,
    }); // Pass modelName and modelRpm for context
  }
} // End of executeGeminiModelInternal function

/**
 * Get a structured JSON response from the Gemini API
 */
// Refactored: Wrapper around executeRequestWithFallback using _executeAndParseJsonInternal
async function getGeminiJsonResponse(systemPrompt, userInput, options = {}) {
  const requestDetails = { systemPrompt, userPrompt: userInput, options }; // Corrected variable name
  // Pass the internal function that gets text AND parses JSON as the task
  return executeRequestWithFallback(
    requestDetails,
    _executeAndParseJsonInternal
  );
}

// NEW Internal helper function to combine execution and JSON parsing
async function _executeAndParseJsonInternal(
  modelConfig,
  systemPrompt,
  userPrompt,
  options = {}
) {
  // Determine if the selected model supports JSON mode
  const useJsonMode = modelConfig.supportsJsonMode;

  // Construct the prompt (simplified if using JSON mode)
  let finalSystemPrompt = systemPrompt;
  if (!useJsonMode) {
    const expectsArray =
      systemPrompt.includes("JSON ARRAY") || systemPrompt.includes("array of");
    finalSystemPrompt = `${systemPrompt}
IMPORTANT: Respond ONLY with a valid, parseable JSON ${
      expectsArray ? "array" : "object"
    }. Your entire response must start with ${
      expectsArray ? "'['" : "'{'"
    } and end with ${
      expectsArray ? "']'" : "'}'"
    }. Do not include any other text or markdown formatting.`;
  }

  // Get response using the core execution function. It might be text or the full result object.
  const responseResult = await executeGeminiModelInternal(
    modelConfig,
    finalSystemPrompt,
    userPrompt,
    options
  );

  // If using JSON mode, the SDK might return a parsed object (though Gemini API currently returns text)
  // Extract the text part for parsing. If it was a full result, get text from response.
  let responseText;
  if (
    typeof responseResult === "object" &&
    responseResult !== null &&
    responseResult.response
  ) {
    // Handle the case where the full result object was returned (tools were likely used)
    responseText = String(responseResult.response.text()).trim();
    logger.debug(
      "[Gemini Service] Extracted text from full response object for JSON parsing."
    );
  } else if (typeof responseResult === "string") {
    // Handle the case where only text was returned (no tools used)
    responseText = String(responseResult).trim();
  } else {
    logger.error(
      "[Gemini Service] Unexpected response type received in _executeAndParseJsonInternal.",
      { responseType: typeof responseResult }
    );
    throw new AiInvalidResponseError(
      `Unexpected response type received from AI model ${modelConfig.name} execution`,
      {
        modelName: modelConfig.name,
        responseType: typeof responseResult,
      }
    );
  }
  logger.debug(
    `Raw AI response text received for JSON parsing (first 200 chars): ${responseText.substring(
      0,
      200
    )}`
  );

  if (!responseText || responseText.length < 2) {
    // This check might be redundant if executeGeminiModelInternal throws, but keep for safety
    logger.error(
      "Empty or too short response text received before JSON parsing."
    );
    throw new AiInvalidResponseError(
      `Empty or invalid response text received from model ${modelConfig.name} before JSON parsing`,
      {
        modelName: modelConfig.name,
      }
    );
  }

  // Clean markdown fences
  let jsonStr = responseText;
  // More robust regex to remove ```json ... ``` or ``` ... ```, handling potential whitespace and variations
  const codeBlockRegex = /^```(?:json)?\s*([\s\S]*?)\s*```$/;
  const match = jsonStr.match(codeBlockRegex);
  if (match && match[1]) {
    jsonStr = match[1].trim();
    logger.debug("Extracted content from markdown code block.");
  } else {
    // Fallback: Simple replacement if regex doesn't match expected structure
    jsonStr = jsonStr
      .replace(/^```(?:json)?\s*/, "")
      .replace(/\s*```$/, "")
      .trim();
  }
  logger.debug(
    `Cleaned response text for parsing (first 200 chars): ${jsonStr.substring(
      0,
      200
    )}`
  );

  // Attempt to parse the cleaned JSON string
  try {
    logger.debug(`[${modelConfig.name}] Attempting to parse JSON response`);
    const jsonResponse = JSON.parse(jsonStr);
    logger.info(
      `[${
        modelConfig.name
      }] Successfully parsed JSON response. Keys: ${Object.keys(
        jsonResponse
      ).join(", ")}`
    );

    // Enhanced logging for material items
    if (jsonResponse.items && Array.isArray(jsonResponse.items)) {
      const itemCount = jsonResponse.items.length;
      logger.info(
        `[${modelConfig.name}] Response contains ${itemCount} material items`
      );

      if (itemCount > 0) {
        // Log the first item as a sample
        logger.debug(
          `[${modelConfig.name}] First item sample:`,
          JSON.stringify(jsonResponse.items[0], null, 2).substring(0, 500)
        );

        // Count items with various properties to help diagnose issues
        const itemsWithName = jsonResponse.items.filter((i) => i.name).length;
        const itemsWithPrice = jsonResponse.items.filter(
          (i) => i.price !== undefined
        ).length;
        const itemsWithQuantity = jsonResponse.items.filter(
          (i) => i.quantity !== undefined
        ).length;
        const itemsWithLookupQuery = jsonResponse.items.filter(
          (i) => i.lookup_query_suggestion
        ).length;

        logger.debug(
          `[${modelConfig.name}] Item property stats: withName=${itemsWithName}, withPrice=${itemsWithPrice}, withQuantity=${itemsWithQuantity}, withLookupQuery=${itemsWithLookupQuery}`
        );
      }
    } else {
      logger.warn(
        `[${modelConfig.name}] No 'items' array found in response or 'items' is not an array`
      );
    }

    return jsonResponse;
  } catch (parseError) {
    const isLikelyTruncationError =
      parseError.message.includes("Unexpected end of JSON input") ||
      parseError.name === "AiInvalidResponseError";
    logger.error(
      `[${modelConfig.name}] Initial JSON parse error: ${parseError.message}${
        isLikelyTruncationError
          ? " (Likely due to truncation or invalid format)"
          : ""
      }`
    );
    logger.error(
      `[${
        modelConfig.name
      }] Failed JSON content (first 500 chars): ${jsonStr.substring(0, 500)}`
    );

    // Initial diagnostics about JSON structure
    const initialDiagnostics = {
      responseLength: jsonStr.length,
      hasStartBracket: jsonStr.includes("{"),
      hasEndBracket: jsonStr.includes("}"),
      hasStartArray: jsonStr.includes("["),
      hasEndArray: jsonStr.includes("]"),
      hasSingleQuotes: jsonStr.includes("'"),
      hasUnescapedQuotes: /[^\\]"/.test(jsonStr),
      containsMarkdown: jsonStr.includes("```") || jsonStr.includes("##"),
      firstChars: jsonStr.substring(0, 100),
      lastChars: jsonStr.substring(jsonStr.length - 100),
    };

    logger.debug(
      `[${modelConfig.name}] Initial JSON diagnostics:`,
      initialDiagnostics
    );

    // Try to fix common JSON formatting issues and retry parsing
    try {
      logger.info(
        `[${modelConfig.name}] Attempting to fix and reparse malformed JSON`
      );

      // Replace single quotes with double quotes (common LLM error)
      let fixedJson = jsonStr.replace(/'/g, '"');

      // Attempt to fix unquoted property names
      fixedJson = fixedJson.replace(/([{,])\s*(\w+)\s*:/g, '$1"$2":');

      // Attempt to fix trailing commas in arrays/objects
      fixedJson = fixedJson.replace(/,\s*([\]}])/g, "$1");

      // Attempt to fix missing quotes around string values (uncommented and improved regex)
      fixedJson = fixedJson.replace(
        /:\s*([^\d"'\{\[]\S+)\s*([,}])/g,
        ':"$1"$2'
      );

      // Try to fix multiline JSON issues (LLM might add newlines in strings)
      fixedJson = fixedJson.replace(/("[^"\\]*)(\n)([^"\\]*")/g, "$1\\n$3");

      // Fix missing commas between array items or object properties
      fixedJson = fixedJson.replace(/(["}\]])\s*(\{)/g, "$1,$2");
      fixedJson = fixedJson.replace(/(["}\]])\s*(\[)/g, "$1,$2");

      logger.debug(
        `[${
          modelConfig.name
        }] Fixed JSON attempt (first 200 chars): ${fixedJson.substring(0, 200)}`
      );

      const repairedJson = JSON.parse(fixedJson);
      logger.info(
        `[${
          modelConfig.name
        }] Successfully repaired and parsed JSON after initial parse failure!${
          isLikelyTruncationError
            ? " (Initial error was likely truncation/invalid format)"
            : ""
        }`
      );

      // Log the repaired JSON structure and top-level keys
      const repairedStructure = {
        topLevelKeys: Object.keys(repairedJson),
        hasItems: Boolean(repairedJson.items),
        itemsIsArray: Array.isArray(repairedJson.items),
        itemCount: Array.isArray(repairedJson.items)
          ? repairedJson.items.length
          : "N/A",
        hasContent: Boolean(repairedJson.content),
        hasGeneratedData: Boolean(repairedJson.generatedData),
      };

      logger.debug(
        `[${modelConfig.name}] Repaired JSON structure:`,
        repairedStructure
      );
      return repairedJson;
    } catch (repairError) {
      logger.error(
        `[${modelConfig.name}] JSON repair attempt also failed: ${repairError.message}`
      );

      // Second repair attempt - more aggressive, try to extract valid JSON from within the response
      try {
        logger.info(
          `[${modelConfig.name}] Attempting second JSON repair technique - JSON extraction`
        );

        // Try to find a JSON object or array within the larger text
        const objectMatch = jsonStr.match(/\{[\s\S]*\}/);
        const arrayMatch = jsonStr.match(/\[[\s\S]*\]/);

        let extractedJson = null;
        if (objectMatch) {
          extractedJson = objectMatch[0];
          logger.debug(
            `[${modelConfig.name}] Extracted JSON object from response`
          );
        } else if (arrayMatch) {
          extractedJson = arrayMatch[0];
          logger.debug(
            `[${modelConfig.name}] Extracted JSON array from response`
          );
        }

        if (extractedJson) {
          // Apply the same fixes to the extracted portion
          extractedJson = extractedJson
            .replace(/'/g, '"')
            .replace(/([{,])\s*(\w+)\s*:/g, '$1"$2":')
            .replace(/,\s*([\]}])/g, "$1")
            .replace(/:\s*([^\d"'\{\[]\S+)\s*([,}])/g, ':"$1"$2');

          const secondRepairJson = JSON.parse(extractedJson);
          logger.info(
            `[${
              modelConfig.name
            }] Second repair attempt (extraction) succeeded after initial parse failure!${
              isLikelyTruncationError
                ? " (Initial error was likely truncation/invalid format)"
                : ""
            }`
          );
          return secondRepairJson;
        } else {
          throw new Error(
            "No valid JSON object or array found in the response"
          );
        }
      } catch (secondRepairError) {
        logger.error(
          `[${modelConfig.name}] Second JSON repair attempt also failed: ${secondRepairError.message}`
        );

        // Add comprehensive diagnostics to help with debugging
        const diagnostics = {
          originalErrorMessage: parseError.message,
          firstRepairErrorMessage: repairError.message,
          secondRepairErrorMessage: secondRepairError.message,
          responseLength: jsonStr.length,
          firstChars: jsonStr.substring(0, 100),
          lastChars: jsonStr.substring(jsonStr.length - 100),
          initialDiagnostics,
          containsMaterialKeywords:
            jsonStr.includes('"items"') || jsonStr.includes('"material_items"'),
          modelName: modelConfig.name,
          detectedFormat:
            initialDiagnostics.hasStartBracket &&
            initialDiagnostics.hasEndBracket
              ? "Likely JSON object"
              : initialDiagnostics.hasStartArray &&
                initialDiagnostics.hasEndArray
              ? "Likely JSON array"
              : initialDiagnostics.containsMarkdown
              ? "Likely Markdown with code blocks"
              : "Unknown format",
        };

        logger.error(
          `[${modelConfig.name}] Comprehensive JSON parsing diagnostics:`,
          diagnostics
        );

        throw new AiInvalidResponseError(
          `Failed to parse JSON response from model ${modelConfig.name}: ${parseError.message}`,
          {
            diagnostics: diagnostics,
          }
        );
      }
    }
  }
}

/**
 * Get health metrics for the Gemini service
 * @returns {Object} Current health metrics
 */
function getHealthMetrics() {
  const modelMetrics = {};
  Object.keys(MODELS).forEach((key) => {
    // Exclude non-Gemini models if any were added (like DEEPSEEK in aiService)
    if (MODELS[key].type === "gemini") {
      modelMetrics[key.toLowerCase()] = {
        name: MODELS[key].name,
        rateLimit: MODELS[key].requestsPerMinute,
        currentCount: MODELS[key].requestCount,
        lastRequest: MODELS[key].lastRequest,
      };
    }
  });

  return {
    ...healthMetrics,
    successRate:
      healthMetrics.totalRequests > 0
        ? (
            (healthMetrics.successfulRequests / healthMetrics.totalRequests) *
            100
          ).toFixed(2) + "%"
        : "N/A",
    models: modelMetrics, // Include all configured Gemini models
    circuitBreakerState: circuitBreakers.gemini.getState(),
  };
}

/**
 * Reset rate limiting counters (useful for testing)
 */
function resetRateLimits() {
  Object.keys(MODELS).forEach((key) => {
    if (MODELS[key].type === "gemini") {
      MODELS[key].requestCount = 0;
    }
  });
  logger.info("Rate limits reset for all configured Gemini models"); // Use logger
}

/**
 * Get a Gemini response using a specific model configured for Google Search grounding.
 * Uses the 'geminiGrounding' circuit breaker.
 */
async function getGroundedGeminiResponse(
  systemPrompt,
  userPrompt,
  options = {}
) {
  const groundingModelConfig = MODELS.FALLBACK_2; // Use gemini-1.5-flash as it supports grounding
  if (!groundingModelConfig) {
    throw new AiServiceError(
      500,
      "Grounding model (FALLBACK_3) is not configured."
    );
  }

  const groundingTool = { tools: [{ googleSearchRetrieval: {} }] }; // Use correct syntax for 1.5 models
  const combinedOptions = { ...options, ...groundingTool };

  const requestDetails = {
    systemPrompt,
    userPrompt,
    options: combinedOptions,
    modelIdentifier: "GROUNDING (FALLBACK_3)", // For logging/debugging
  };

  logger.info(
    `[Gemini Service] Attempting grounding call with model: ${groundingModelConfig.name}`
  );

  // Use the dedicated grounding circuit breaker
  return await circuitBreakers.geminiGrounding.execute(async () => {
    // Use retry logic similar to executeRequestWithFallback, but simplified as we target one model
    return await withRetry(
      () =>
        executeGeminiModelInternal(
          groundingModelConfig,
          systemPrompt,
          userPrompt,
          combinedOptions
        ),
      {
        maxRetries: 2, // Allow a couple of retries for transient issues
        baseDelay: 400,
        maxDelay: 10000,
        shouldRetry: isRetryableError,
        getRetryDelay: getRetryDelay,
        onRetry: (error, attempt, delay) => {
          logger.warn(
            `Retrying Grounding Call (Attempt ${attempt}) after ${delay}ms due to error: ${error.message}`
          );
          healthMetrics.retryAttempts++; // Still track overall retries
        },
      }
    );
  });
}

module.exports = {
  getGeminiResponse,
  getGeminiJsonResponse,
  getGroundedGeminiResponse, // Export the new function
  // getFallbackJsonResponse, // Removed as fallback is handled by caller
  getHealthMetrics,
  resetRateLimits,
};
