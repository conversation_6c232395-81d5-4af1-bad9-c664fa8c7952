/**
 * cookieManager.js
 * Utility for managing and persisting browser cookies to maintain sessions
 * and reduce CAPTCHA challenges by reusing successful sessions
 */

const fs = require("fs");
const path = require("path");
const logger = require("./logger");

class CookieManager {
  constructor() {
    this.cookiesDir = path.join(__dirname, "../data/cookies");
    this.ensureCookieDirectory();
  }

  /**
   * Ensure cookie storage directory exists
   * @private
   */
  ensureCookieDirectory() {
    if (!fs.existsSync(this.cookiesDir)) {
      try {
        fs.mkdirSync(this.cookiesDir, { recursive: true });
        logger.info(
          `[CookieManager] Created cookies directory at ${this.cookiesDir}`
        );
      } catch (error) {
        logger.error(
          `[CookieManager] Failed to create cookies directory: ${error.message}`
        );
      }
    }
  }

  /**
   * Get the file path for a source's cookies
   * @param {string} sourceName - Name of the material source
   * @returns {string} - File path for cookies
   * @private
   */
  getCookieFilePath(sourceName) {
    return path.join(
      this.cookiesDir,
      `${sourceName.replace(/[^a-z0-9]/gi, "_").toLowerCase()}.json`
    );
  }

  /**
   * Save cookies for a material source
   * @param {string} sourceName - Name of the material source
   * @param {Array<Object>} cookies - Array of cookie objects
   * @returns {boolean} - Success status
   */
  saveCookies(sourceName, cookies) {
    if (!sourceName || !cookies || !Array.isArray(cookies)) {
      logger.warn(
        `[CookieManager] Invalid parameters for saveCookies: sourceName=${sourceName}, cookies=${typeof cookies}`
      );
      return false;
    }

    try {
      const filePath = this.getCookieFilePath(sourceName);
      const data = {
        cookies,
        timestamp: new Date().toISOString(),
      };
      fs.writeFileSync(filePath, JSON.stringify(data, null, 2));
      logger.debug(
        `[CookieManager] Saved ${cookies.length} cookies for ${sourceName}`
      );
      return true;
    } catch (error) {
      logger.error(
        `[CookieManager] Failed to save cookies for ${sourceName}: ${error.message}`
      );
      return false;
    }
  }

  /**
   * Load cookies for a material source
   * @param {string} sourceName - Name of the material source
   * @param {number} maxAgeHours - Maximum age of cookies in hours (default: 24)
   * @returns {Array<Object>|null} - Array of cookie objects or null if not found/expired
   */
  loadCookies(sourceName, maxAgeHours = 24) {
    try {
      const filePath = this.getCookieFilePath(sourceName);
      if (!fs.existsSync(filePath)) {
        logger.debug(`[CookieManager] No cookie file found for ${sourceName}`);
        return null;
      }

      const data = JSON.parse(fs.readFileSync(filePath, "utf8"));

      // Check if cookies are too old
      const cookieTime = new Date(data.timestamp);
      const now = new Date();
      const hoursDiff = (now - cookieTime) / (1000 * 60 * 60);

      if (hoursDiff > maxAgeHours) {
        logger.debug(
          `[CookieManager] Cookies for ${sourceName} are too old (${hoursDiff.toFixed(
            2
          )} hours)`
        );
        return null;
      }

      logger.debug(
        `[CookieManager] Loaded ${
          data.cookies.length
        } cookies for ${sourceName} (age: ${hoursDiff.toFixed(2)} hours)`
      );
      return data.cookies;
    } catch (error) {
      logger.error(
        `[CookieManager] Failed to load cookies for ${sourceName}: ${error.message}`
      );
      return null;
    }
  }

  /**
   * Delete cookies for a material source
   * @param {string} sourceName - Name of the material source
   * @returns {boolean} - Success status
   */
  deleteCookies(sourceName) {
    try {
      const filePath = this.getCookieFilePath(sourceName);
      if (fs.existsSync(filePath)) {
        fs.unlinkSync(filePath);
        logger.debug(`[CookieManager] Deleted cookies for ${sourceName}`);
        return true;
      }
      return false;
    } catch (error) {
      logger.error(
        `[CookieManager] Failed to delete cookies for ${sourceName}: ${error.message}`
      );
      return false;
    }
  }

  /**
   * List all available cookie files
   * @returns {Array<Object>} - Array of { sourceName, age } objects
   */
  listCookies() {
    try {
      const files = fs.readdirSync(this.cookiesDir);
      const now = new Date();

      return files
        .filter((file) => file.endsWith(".json"))
        .map((file) => {
          try {
            const filePath = path.join(this.cookiesDir, file);
            const data = JSON.parse(fs.readFileSync(filePath, "utf8"));
            const cookieTime = new Date(data.timestamp);
            const hoursDiff = (now - cookieTime) / (1000 * 60 * 60);

            return {
              sourceName: file.replace(/\.json$/, "").replace(/_/g, " "),
              age: hoursDiff.toFixed(2),
              count: data.cookies.length,
            };
          } catch (e) {
            return null;
          }
        })
        .filter((item) => item !== null);
    } catch (error) {
      logger.error(`[CookieManager] Failed to list cookies: ${error.message}`);
      return [];
    }
  }
}

// Export a singleton instance
module.exports = new CookieManager();
