require("dotenv").config();
const mongoose = require("mongoose");
const Quote = require("./models/Quote");

async function checkLookupResults() {
  try {
    await mongoose.connect(process.env.MONGODB_URI);
    console.log("Connected to database");

    const quote = await Quote.findOne({
      "items.lookup_results": { $exists: true, $ne: [] },
    });

    if (quote) {
      console.log("Found quote with lookup_results:");
      console.log("Quote ID:", quote._id);

      const itemWithResults = quote.items.find(
        (item) => item.lookup_results && item.lookup_results.length > 0
      );

      if (itemWithResults) {
        console.log("Item ID:", itemWithResults._id);
        console.log(
          "lookup_results type:",
          typeof itemWithResults.lookup_results
        );
        console.log(
          "lookup_results length:",
          itemWithResults.lookup_results.length
        );
        console.log(
          "First lookup_result:",
          JSON.stringify(itemWithResults.lookup_results[0], null, 2)
        );
      }
    } else {
      console.log("No quotes found with lookup_results");
    }
  } catch (error) {
    console.error("Error:", error.message);
  } finally {
    await mongoose.disconnect();
    process.exit(0);
  }
}

checkLookupResults();
