/**
 * Material Status Monitor
 * Utility to track and display real-time material price lookup status
 */

import axios from "axios";
import logger from "./logger";

class MaterialMonitor {
  constructor() {
    this.pollInterval = null;
    this.isPolling = false;
    this.callbacks = new Map(); // Map of quoteId to callback functions
  }

  /**
   * Start monitoring material status for a specific quote
   * @param {string} quoteId - Quote ID to monitor
   * @param {function} onUpdate - Callback function with status updates
   * @param {number} intervalMs - Polling interval in milliseconds (default: 3000)
   */
  startMonitoring(quoteId, onUpdate, intervalMs = 3000) {
    if (!quoteId || !onUpdate) {
      logger.error("[MaterialMonitor] Invalid parameters for startMonitoring");
      return;
    }

    logger.info(`[MaterialMonitor] Starting monitoring for quote ${quoteId}`);

    // Store callback
    this.callbacks.set(quoteId, onUpdate);

    // Start polling if not already started
    if (!this.isPolling) {
      this.isPolling = true;
      this.poll(intervalMs);
    }

    // Immediately fetch status
    this.fetchQuoteStatus(quoteId);
  }

  /**
   * Stop monitoring a specific quote
   * @param {string} quoteId - Quote ID to stop monitoring
   */
  stopMonitoring(quoteId) {
    logger.info(`[MaterialMonitor] Stopping monitoring for quote ${quoteId}`);
    this.callbacks.delete(quoteId);

    // Stop polling if no more callbacks
    if (this.callbacks.size === 0 && this.pollInterval) {
      clearInterval(this.pollInterval);
      this.pollInterval = null;
      this.isPolling = false;
    }
  }

  /**
   * Fetch status for all active lookups
   */
  async fetchActiveLookupsStatus() {
    try {
      const token = localStorage.getItem("token");
      const response = await axios.get("/api/material-status/active-lookups", {
        headers: { Authorization: `Bearer ${token}` },
      });

      return response.data;
    } catch (error) {
      logger.error("[MaterialMonitor] Error fetching active lookups:", error);
      return { lookups: [] };
    }
  }

  /**
   * Fetch status for a specific quote
   * @param {string} quoteId - Quote ID
   */
  async fetchQuoteStatus(quoteId) {
    try {
      const token = localStorage.getItem("token");
      const response = await axios.get(
        `/api/material-status/quote/${quoteId}`,
        {
          headers: { Authorization: `Bearer ${token}` },
        }
      );

      const data = response.data;

      // Call the callback if registered
      const callback = this.callbacks.get(quoteId);
      if (callback) {
        callback(data);
      }

      return data;
    } catch (error) {
      logger.error(
        `[MaterialMonitor] Error fetching status for quote ${quoteId}:`,
        error
      );

      // Still call callback with error state
      const callback = this.callbacks.get(quoteId);
      if (callback) {
        callback({
          success: false,
          error: error.message,
          itemStatuses: [],
        });
      }

      return null;
    }
  }

  /**
   * Poll for status updates
   * @param {number} intervalMs - Polling interval
   */
  poll(intervalMs) {
    // Clear any existing interval
    if (this.pollInterval) {
      clearInterval(this.pollInterval);
    }

    this.pollInterval = setInterval(async () => {
      // Fetch status for all monitored quotes
      for (const quoteId of this.callbacks.keys()) {
        await this.fetchQuoteStatus(quoteId);
      }
    }, intervalMs);
  }

  /**
   * Get a formatted status message for an item
   * @param {object} itemStatus - Item status object
   */
  getStatusMessage(itemStatus) {
    if (!itemStatus) return "Unknown status";

    if (itemStatus.hasPrice) {
      return `Price found: $${itemStatus.price.toFixed(2)}`;
    }

    if (itemStatus.pendingCount > 0) {
      return `Price lookup in progress (${itemStatus.pendingCount} attempts)`;
    }

    if (itemStatus.failedCount > 0) {
      return `Price lookup failed (${itemStatus.failedCount} attempts)`;
    }

    if (itemStatus.completedCount > 0) {
      return `Price lookup completed (${itemStatus.completedCount} attempts)`;
    }

    return "No price lookup attempted";
  }

  /**
   * Get color/severity for status display
   * @param {object} itemStatus - Item status object
   */
  getStatusSeverity(itemStatus) {
    if (!itemStatus) return "default";

    if (itemStatus.hasPrice) return "success";
    if (itemStatus.pendingCount > 0) return "info";
    if (itemStatus.failedCount > 0) return "error";
    if (itemStatus.completedCount > 0) return "warning";

    return "default";
  }

  /**
   * Calculate overall progress for a quote
   * @param {object} quoteStatus - Quote status object
   */
  calculateProgress(quoteStatus) {
    if (!quoteStatus || !quoteStatus.summary) {
      return { percentage: 0, message: "No data" };
    }

    const {
      totalItems,
      itemsWithPrice,
      itemsPending,
      itemsCompleted,
      itemsFailed,
    } = quoteStatus.summary;

    if (totalItems === 0) {
      return { percentage: 0, message: "No items" };
    }

    const processed = itemsWithPrice + itemsCompleted + itemsFailed;
    const percentage = Math.round((processed / totalItems) * 100);

    let message = "";
    if (itemsPending > 0) {
      message = `Processing ${itemsPending} of ${totalItems} items...`;
    } else if (itemsFailed > 0) {
      message = `Completed with ${itemsFailed} failed lookups`;
    } else if (itemsWithPrice === totalItems) {
      message = "All prices found!";
    } else {
      message = `${processed} of ${totalItems} items processed`;
    }

    return { percentage, message };
  }
}

// Export singleton instance
const materialMonitor = new MaterialMonitor();
export default materialMonitor;
