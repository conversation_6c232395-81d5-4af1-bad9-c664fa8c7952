/**
 * userAgentManager.js
 * Utility for managing and rotating User-Agent strings to reduce CAPTCHA challenges
 */

const logger = require("./logger");

class UserAgentManager {
  constructor() {
    this.userAgents = [
      "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
      "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.107 Safari/537.36",
      "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:90.0) Gecko/20100101 Firefox/90.0",
      "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.106 Safari/537.36 Edg/91.0.864.53",
      "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15",
      "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.114 Safari/537.36",
      "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.131 Safari/537.36",
      "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.164 Safari/537.36",
      "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/93.0.4577.63 Safari/537.36",
    ];
    this.currentIndex = 0;
    logger.debug(
      `[UserAgentManager] Initialized with ${this.userAgents.length} user agents`
    );
  }

  /**
   * Get a random user agent from the collection
   * @returns {string} A random user agent string
   */
  getRandomUserAgent() {
    const randomIndex = Math.floor(Math.random() * this.userAgents.length);
    return this.userAgents[randomIndex];
  }

  /**
   * Get the next user agent in sequence (for deterministic rotation)
   * @returns {string} The next user agent in sequence
   */
  getNextUserAgent() {
    const userAgent = this.userAgents[this.currentIndex];
    this.currentIndex = (this.currentIndex + 1) % this.userAgents.length;
    return userAgent;
  }

  /**
   * Add a new user agent to the collection
   * @param {string} userAgent - User agent string to add
   * @returns {boolean} - Success status
   */
  addUserAgent(userAgent) {
    if (!userAgent || typeof userAgent !== "string") {
      return false;
    }

    if (!this.userAgents.includes(userAgent)) {
      this.userAgents.push(userAgent);
      logger.debug(`[UserAgentManager] Added new user agent: ${userAgent}`);
      return true;
    }

    return false;
  }
}

// Export a singleton instance
module.exports = new UserAgentManager();
