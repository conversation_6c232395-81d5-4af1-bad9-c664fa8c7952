import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import { useSnackbar } from "notistack";
import aiService from "../utils/aiService";
import {
  Container,
  Paper,
  Typography,
  TextField,
  Button,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Box,
  Chip,
  Alert,
  CircularProgress,
  Divider,
  Card,
  CardContent,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  IconButton,
} from "@mui/material";
import { LoadingButton } from "@mui/lab";
import {
  Send as SendIcon,
  Add as AddIcon,
  Warning as WarningIcon,
  AutoFixHigh as AutoFixHighIcon,
} from "@mui/icons-material";
import { createJob } from "../slices/jobSlice";
import { getCustomers } from "../slices/customerSlice";

const CreateJob = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { enqueueSnackbar } = useSnackbar();

  // State for storing user form inputs
  const [formData, setFormData] = useState({
    title: "",
    description: "",
    originalDescription: "",
    customer: "",
    priority: "Medium",
    status: "Scheduled",
    assignedTo: "",
    location: {
      street: "",
      city: "",
      state: "",
      zipCode: "",
    },
    estimatedDuration: 60,
    scheduledDate: new Date(),
    jobType: "Standard",
    notes: "",
    tasks: [], // Each task should be an object with {description, completed}
  });

  const [newTask, setNewTask] = useState("");
  const [equipment, setEquipment] = useState("");
  // AI analytics state
  const [aiSuggestions, setAiSuggestions] = useState({
    suggestedTitle: "",
    enhancedDescription: "",
    estimatedDuration: 60,
    complexity: "Medium",
    skills: [],
    additionalTasks: [],
    technicalTerms: [],
    warnings: [],
    chainOfThought: {},
    wasEnhanced: false,
  });
  const [loadingAI, setLoadingAI] = useState(false);

  const { loading, error, success } = useSelector((state) => state.jobs);
  const { customers } = useSelector((state) => state.customers);
  // eslint-disable-next-line no-unused-vars
  const { users } = useSelector((state) => state.users); // Assuming you have user state for technicians

  // Load customers when component mounts
  useEffect(() => {
    dispatch(getCustomers({ limit: 100 })); // Load up to 100 customers for the dropdown
  }, [dispatch]);

  // Get AI suggestions based on the job description
  const getAISuggestions = async (description) => {
    if (!description || description.trim().length < 10) return;

    setLoadingAI(true);
    try {
      const data = await aiService.analyzeJob(description, formData.title);

      setAiSuggestions({
        complexity: data.complexity || "Medium",
        skills: data.skills || [],
        additionalTasks: data.additionalTasks || [],
        enhancedDescription: data.enhancedDescription || description,
        technicalTerms: data.technicalTerms || [],
        chainOfThought: data.chainOfThought || {},
        wasEnhanced: false,
      });
    } catch (error) {
      console.error("Error getting AI suggestions:", error);
    } finally {
      setLoadingAI(false);
    }
  };

  // Get complete AI job analysis including title and duration estimates
  const getCompleteJobAnalysis = async (description) => {
    if (!description || description.trim().length < 10) return;

    setLoadingAI(true);
    try {
      const data = await aiService.analyzeCompleteJob(
        description,
        formData.title
      );

      // Update AI suggestions with comprehensive analysis
      setAiSuggestions((prev) => ({
        ...prev,
        suggestedTitle: data.suggestedTitle || prev.suggestedTitle,
        enhancedDescription: data.enhancedDescription || description,
        estimatedDuration: data.estimatedDuration || 60,
        technicalTerms: data.technicalTerms || [],
        chainOfThought: data.chainOfThought || {},
        wasEnhanced: true,
      }));

      // Update form data with AI-suggested values
      setFormData((prev) => ({
        ...prev,
        title: data.suggestedTitle || prev.title,
        description: data.enhancedDescription || prev.description,
        estimatedDuration: data.estimatedDuration || prev.estimatedDuration,
      }));

      // Show success notification
      enqueueSnackbar("Job analysis completed successfully!", {
        variant: "success",
        autoHideDuration: 3000,
      });
    } catch (error) {
      console.error("Error analyzing job:", error);
      enqueueSnackbar(
        "Could not complete job analysis. Using original values.",
        {
          variant: "error",
          autoHideDuration: 3000,
        }
      );
    } finally {
      setLoadingAI(false);
    }
  };

  const enhanceJobDescription = async () => {
    setLoadingAI(true);
    try {
      const data = await aiService.enhanceJobDescription(
        formData.description,
        formData.title,
        formData.jobType || "Standard"
      );

      // Store both the original and enhanced descriptions
      if (
        data.enhancedDescription &&
        data.enhancedDescription !== formData.description
      ) {
        setFormData((prev) => ({
          ...prev,
          originalDescription: prev.description,
          description: data.enhancedDescription,
        }));

        // Update AI suggestions with the enhancement data
        setAiSuggestions((prev) => ({
          ...prev,
          enhancedDescription: data.enhancedDescription,
          technicalTerms: data.technicalTerms || [],
          chainOfThought: data.chainOfThought || {},
          wasEnhanced: true,
        }));

        // After enhancing the description, get title and duration estimates
        await getCompleteJobAnalysis(data.enhancedDescription);

        // Show success message
        enqueueSnackbar("Job description professionally enhanced with AI!", {
          variant: "success",
          autoHideDuration: 3000,
        });
      } else {
        // Enhancement returned same text or failed
        enqueueSnackbar(
          "No significant enhancements were made to the description",
          {
            variant: "info",
            autoHideDuration: 3000,
          }
        );
      }
    } catch (error) {
      console.error("Error enhancing job description:", error);
      enqueueSnackbar("Failed to enhance job description. Please try again.", {
        variant: "error",
        autoHideDuration: 3000,
      });
    } finally {
      setLoadingAI(false);
    }
  };

  const handleChange = (e) => {
    const { name, value } = e.target;
    if (name.includes(".")) {
      const [parent, child] = name.split(".");
      setFormData((prev) => ({
        ...prev,
        [parent]: {
          ...prev[parent],
          [child]: value,
        },
      }));
    } else {
      setFormData((prev) => ({
        ...prev,
        [name]: value,
      }));

      // Auto-fill customer address when customer is selected
      if (name === "customer" && value) {
        const selectedCustomer = customers.find(
          (customer) => customer._id === value
        );
        if (selectedCustomer && selectedCustomer.address) {
          setFormData((prev) => ({
            ...prev,
            location: {
              street: selectedCustomer.address.street || "",
              city: selectedCustomer.address.city || "",
              state: selectedCustomer.address.state || "",
              zipCode: selectedCustomer.address.zipCode || "",
            },
          }));
        }
      }
    }
  };

  const handleAddTask = (taskDescription) => {
    if (taskDescription && taskDescription.trim()) {
      setFormData((prev) => ({
        ...prev,
        tasks: [
          ...prev.tasks,
          {
            description: taskDescription.trim(),
            completed: false,
          },
        ],
      }));
      setNewTask("");
    }
  };

  const handleRemoveTask = (index) => {
    setFormData((prev) => ({
      ...prev,
      tasks: prev.tasks.filter((_, i) => i !== index),
    }));
  };
  // eslint-disable-next-line no-unused-vars
  const handleAddEquipment = () => {
    if (equipment.trim()) {
      setFormData((prev) => ({
        ...prev,
        equipmentNeeded: [...prev.equipmentNeeded, equipment.trim()],
      }));
      setEquipment("");
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    // Get AI suggestions first if not already done
    if (!aiSuggestions && formData.description) {
      await getAISuggestions(formData.description);
    }

    // Validate required fields
    if (!formData.title) {
      enqueueSnackbar("Please enter a job title", { variant: "error" });
      return;
    }

    if (!formData.customer) {
      enqueueSnackbar("Please select a customer", { variant: "error" });
      return;
    }

    // Check if customer ID is in valid MongoDB ObjectId format (24 character hex string)
    const isValidObjectId = /^[0-9a-fA-F]{24}$/.test(formData.customer);
    if (!isValidObjectId) {
      enqueueSnackbar("Invalid customer ID format", { variant: "error" });
      console.error("Invalid customer ID format:", formData.customer);
      return;
    }

    if (!formData.location.street) {
      enqueueSnackbar("Please enter a street address", { variant: "error" });
      return;
    }

    // Ensure estimatedDuration is a number
    if (
      typeof formData.estimatedDuration !== "number" ||
      isNaN(formData.estimatedDuration)
    ) {
      enqueueSnackbar("Please enter a valid duration", { variant: "error" });
      return;
    }

    // Transform location format to match backend expectations
    const transformedLocation = {
      address: formData.location.street,
      city: formData.location.city || "",
      state: formData.location.state || "",
      zipCode: formData.location.zipCode || "",
      country: "USA", // Default country if needed
    };

    // Format notes if needed
    const formattedNotes = formData.notes
      ? [{ content: formData.notes, createdAt: new Date() }]
      : [];

    // Merge AI suggestions with form data and ensure data structure matches backend expectations
    const enhancedJobData = {
      title: formData.title,
      customer: formData.customer,
      description: formData.description || "",
      status: formData.status,
      priority: formData.priority,
      // Send both fields for compatibility
      schedule: {
        startDate:
          formData.scheduledDate instanceof Date
            ? formData.scheduledDate
            : new Date(formData.scheduledDate),
      },
      scheduledDate:
        formData.scheduledDate instanceof Date
          ? formData.scheduledDate
          : new Date(formData.scheduledDate),
      estimatedDuration: Number(formData.estimatedDuration), // Ensure it's a number
      location: transformedLocation,
      tasks:
        formData.tasks.length > 0
          ? formData.tasks
          : [{ description: "Initial assessment", completed: false }], // Ensure there's at least one task
      notes: formattedNotes,
      assignedTo: formData.assignedTo || null,
      // Don't send empty metadata - let the backend set it from the authenticated user
      aiInsights: aiSuggestions || null,
    };

    console.log("Submitting job data:", JSON.stringify(enhancedJobData));
    try {
      dispatch(createJob(enhancedJobData));
    } catch (error) {
      console.error("Error dispatching job creation:", error);
      enqueueSnackbar(`Error: ${error.message}`, { variant: "error" });
    }
  };

  useEffect(() => {
    if (success) {
      enqueueSnackbar("Job created successfully!", { variant: "success" });
      navigate("/jobs");
    }
    if (error) {
      enqueueSnackbar(`Error: ${error}`, { variant: "error" });
      console.error("Job creation error:", error);
    }
  }, [success, error, enqueueSnackbar, navigate]);

  return (
    <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
      <Typography variant="h4" gutterBottom>
        Create New Job
      </Typography>

      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      <Grid container spacing={3}>
        <Grid item xs={12} md={8}>
          <Paper sx={{ p: 3 }}>
            <form onSubmit={handleSubmit}>
              <Grid container spacing={3}>
                {/* Basic Information */}
                <Grid item xs={12}>
                  <Typography variant="h6" gutterBottom>
                    Basic Information
                  </Typography>
                </Grid>

                <Grid item xs={12}>
                  <TextField
                    required
                    fullWidth
                    label="Job Title"
                    name="title"
                    value={formData.title}
                    onChange={handleChange}
                  />
                </Grid>

                <Grid item xs={12}>
                  <TextField
                    required
                    fullWidth
                    multiline
                    rows={4}
                    label="Job Description"
                    name="description"
                    value={formData.description}
                    onChange={handleChange}
                    onBlur={() => {
                      if (formData.description) {
                        getAISuggestions(formData.description);
                      }
                    }}
                  />
                  <Box
                    sx={{ display: "flex", justifyContent: "flex-end", mt: 1 }}
                  >
                    <Button
                      variant="outlined"
                      color="primary"
                      startIcon={<AutoFixHighIcon />}
                      size="small"
                      onClick={() => enhanceJobDescription()}
                      disabled={
                        !formData.description ||
                        formData.description.trim().length < 10 ||
                        loadingAI
                      }
                    >
                      {loadingAI ? "Enhancing..." : "Enhance with AI"}
                    </Button>
                  </Box>
                </Grid>

                <Grid item xs={12} sm={6}>
                  <FormControl fullWidth>
                    <InputLabel>Customer</InputLabel>
                    <Select
                      required
                      name="customer"
                      value={formData.customer}
                      onChange={handleChange}
                    >
                      {customers?.map((customer) => (
                        <MenuItem key={customer._id} value={customer._id}>
                          {customer.businessName}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Grid>

                <Grid item xs={12} sm={6}>
                  <FormControl fullWidth>
                    <InputLabel>Priority</InputLabel>
                    <Select
                      name="priority"
                      value={formData.priority}
                      onChange={handleChange}
                    >
                      <MenuItem value="Low">Low</MenuItem>
                      <MenuItem value="Medium">Medium</MenuItem>
                      <MenuItem value="High">High</MenuItem>
                      <MenuItem value="Urgent">Urgent</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>

                {/* Scheduling */}
                <Grid item xs={12}>
                  <Divider sx={{ my: 2 }} />
                  <Typography variant="h6" gutterBottom>
                    Scheduling
                  </Typography>
                </Grid>

                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    label="Scheduled Date & Time"
                    type="datetime-local"
                    InputLabelProps={{
                      shrink: true,
                    }}
                    value={
                      formData.scheduledDate instanceof Date
                        ? formData.scheduledDate.toISOString().slice(0, 16)
                        : ""
                    }
                    onChange={(e) => {
                      setFormData((prev) => ({
                        ...prev,
                        scheduledDate: new Date(e.target.value),
                      }));
                    }}
                  />
                </Grid>

                <Grid item xs={12} sm={6}>
                  <Grid container spacing={2}>
                    <Grid item xs={6}>
                      <TextField
                        fullWidth
                        type="number"
                        label="Hours"
                        InputProps={{ inputProps: { min: 0 } }}
                        value={Math.floor(formData.estimatedDuration / 60)}
                        onChange={(e) => {
                          const hours = parseInt(e.target.value) || 0;
                          const minutes = formData.estimatedDuration % 60;
                          setFormData((prev) => ({
                            ...prev,
                            estimatedDuration: hours * 60 + minutes,
                          }));
                        }}
                      />
                    </Grid>
                    <Grid item xs={6}>
                      <TextField
                        fullWidth
                        type="number"
                        label="Minutes"
                        InputProps={{ inputProps: { min: 0, max: 59 } }}
                        value={formData.estimatedDuration % 60}
                        onChange={(e) => {
                          const minutes = parseInt(e.target.value) || 0;
                          const hours = Math.floor(
                            formData.estimatedDuration / 60
                          );
                          setFormData((prev) => ({
                            ...prev,
                            estimatedDuration:
                              hours * 60 + (minutes > 59 ? 59 : minutes),
                          }));
                        }}
                      />
                    </Grid>
                  </Grid>
                </Grid>

                {/* Location */}
                <Grid item xs={12}>
                  <Divider sx={{ my: 2 }} />
                  <Typography variant="h6" gutterBottom>
                    Location
                  </Typography>
                </Grid>

                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Street Address"
                    name="location.street"
                    value={formData.location.street}
                    onChange={handleChange}
                  />
                </Grid>

                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    label="City"
                    name="location.city"
                    value={formData.location.city}
                    onChange={handleChange}
                  />
                </Grid>

                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    label="State"
                    name="location.state"
                    value={formData.location.state}
                    onChange={handleChange}
                  />
                </Grid>

                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    label="ZIP Code"
                    name="location.zipCode"
                    value={formData.location.zipCode}
                    onChange={handleChange}
                  />
                </Grid>

                {/* Tasks */}
                <Grid item xs={12}>
                  <Divider sx={{ my: 2 }} />
                  <Typography variant="h6" gutterBottom>
                    Tasks
                  </Typography>
                </Grid>

                <Grid item xs={12}>
                  <Box sx={{ display: "flex", gap: 1, mb: 2 }}>
                    <TextField
                      fullWidth
                      label="Add Task"
                      value={newTask}
                      onChange={(e) => setNewTask(e.target.value)}
                      onKeyPress={(e) => {
                        if (e.key === "Enter") {
                          e.preventDefault();
                          handleAddTask(newTask);
                        }
                      }}
                    />
                    <Button
                      variant="contained"
                      onClick={() => handleAddTask(newTask)}
                    >
                      Add
                    </Button>
                  </Box>
                  <Box sx={{ display: "flex", flexWrap: "wrap", gap: 1 }}>
                    {formData.tasks.map((task, index) => (
                      <Chip
                        key={index}
                        label={task.description}
                        onDelete={() => handleRemoveTask(index)}
                      />
                    ))}
                  </Box>
                </Grid>

                <Grid item xs={12}>
                  <Box sx={{ display: "flex", gap: 2, mt: 3 }}>
                    <Button
                      variant="outlined"
                      onClick={() => navigate("/jobs")}
                    >
                      Cancel
                    </Button>
                    <LoadingButton
                      loading={loading}
                      variant="contained"
                      type="submit"
                      startIcon={<SendIcon />}
                    >
                      Create Job
                    </LoadingButton>
                  </Box>
                </Grid>
              </Grid>
            </form>
          </Paper>
        </Grid>

        {/* AI Suggestions Panel */}
        <Grid item xs={12} md={4}>
          <Paper sx={{ p: 3, position: "sticky", top: 20 }}>
            <Box sx={{ display: "flex", alignItems: "center", mb: 2 }}>
              <Typography variant="h6" sx={{ flexGrow: 1 }}>
                AI Insights
              </Typography>
              <Button
                size="small"
                startIcon={<AutoFixHighIcon />}
                onClick={() =>
                  formData.description
                    ? getCompleteJobAnalysis(formData.description)
                    : null
                }
                disabled={!formData.description || loadingAI}
                color="primary"
                variant="outlined"
              >
                Analyze Job
              </Button>
            </Box>

            {loadingAI ? (
              <Box sx={{ display: "flex", justifyContent: "center", p: 3 }}>
                <CircularProgress />
              </Box>
            ) : aiSuggestions ? (
              <Box>
                <Grid container spacing={2} sx={{ mb: 2 }}>
                  <Grid item xs={6}>
                    <Card sx={{ height: "100%" }}>
                      <CardContent>
                        <Typography
                          color="textSecondary"
                          gutterBottom
                          fontSize="0.875rem"
                        >
                          Complexity
                        </Typography>
                        <Typography variant="h6">
                          {aiSuggestions.complexity || "Medium"}
                        </Typography>
                      </CardContent>
                    </Card>
                  </Grid>
                  <Grid item xs={6}>
                    <Card sx={{ height: "100%" }}>
                      <CardContent>
                        <Typography
                          color="textSecondary"
                          gutterBottom
                          fontSize="0.875rem"
                        >
                          Est. Duration
                        </Typography>
                        <Typography variant="h6">
                          {Math.floor(aiSuggestions.estimatedDuration / 60)}h{" "}
                          {aiSuggestions.estimatedDuration % 60}m
                        </Typography>
                      </CardContent>
                    </Card>
                  </Grid>
                </Grid>

                <Typography variant="subtitle2" gutterBottom>
                  Required Skills:
                </Typography>
                <Box sx={{ mb: 2 }}>
                  {aiSuggestions.skills && aiSuggestions.skills.length > 0 ? (
                    aiSuggestions.skills.map((skill, index) => (
                      <Chip
                        key={index}
                        label={skill}
                        size="small"
                        sx={{ mr: 1, mb: 1 }}
                      />
                    ))
                  ) : (
                    <Typography variant="body2" color="textSecondary">
                      Skills will appear after job description analysis
                    </Typography>
                  )}
                </Box>

                <Typography variant="subtitle2" gutterBottom>
                  Recommended Tasks:
                </Typography>
                <List dense>
                  {aiSuggestions.additionalTasks &&
                  aiSuggestions.additionalTasks.length > 0 ? (
                    aiSuggestions.additionalTasks.map((task, index) => (
                      <ListItem
                        key={index}
                        button
                        onClick={() =>
                          handleAddTask({ description: task, completed: false })
                        }
                        secondaryAction={
                          <IconButton
                            edge="end"
                            size="small"
                            onClick={() =>
                              handleAddTask({
                                description: task,
                                completed: false,
                              })
                            }
                          >
                            <AddIcon fontSize="small" />
                          </IconButton>
                        }
                      >
                        <ListItemText
                          primary={task}
                          primaryTypographyProps={{ variant: "body2" }}
                        />
                      </ListItem>
                    ))
                  ) : (
                    <ListItem>
                      <ListItemText
                        primary="Tasks will appear after analysis"
                        primaryTypographyProps={{
                          variant: "body2",
                          color: "textSecondary",
                        }}
                      />
                    </ListItem>
                  )}
                </List>

                {aiSuggestions.warnings?.length > 0 && (
                  <>
                    <Typography variant="subtitle2" color="error" gutterBottom>
                      Potential Issues:
                    </Typography>
                    <List dense>
                      {aiSuggestions.warnings.map((warning, index) => (
                        <ListItem key={index}>
                          <ListItemIcon sx={{ minWidth: 36 }}>
                            <WarningIcon color="error" fontSize="small" />
                          </ListItemIcon>
                          <ListItemText
                            primary={warning}
                            primaryTypographyProps={{ variant: "body2" }}
                          />
                        </ListItem>
                      ))}
                    </List>
                  </>
                )}

                {aiSuggestions?.technicalTerms &&
                  aiSuggestions.technicalTerms.length > 0 && (
                    <Box sx={{ mt: 2, mb: 2 }}>
                      <Typography variant="subtitle2" gutterBottom>
                        Key Technical Terms:
                      </Typography>
                      <Card variant="outlined" sx={{ bgcolor: "#f6f8ff" }}>
                        <CardContent sx={{ p: 2, "&:last-child": { pb: 2 } }}>
                          <Grid container spacing={1}>
                            {aiSuggestions.technicalTerms.map((term, idx) => (
                              <Grid item key={idx}>
                                <Chip
                                  label={term}
                                  size="small"
                                  color="primary"
                                  variant="outlined"
                                  sx={{
                                    borderRadius: "4px",
                                    bgcolor: "rgba(25, 118, 210, 0.08)",
                                  }}
                                />
                              </Grid>
                            ))}
                          </Grid>
                        </CardContent>
                      </Card>
                    </Box>
                  )}

                <Box
                  sx={{
                    mt: 3,
                    border: "1px dashed #ddd",
                    borderRadius: 1,
                    p: 1.5,
                  }}
                >
                  <Typography variant="subtitle2" gutterBottom>
                    Job Enhancement Summary:
                  </Typography>
                  <Typography variant="body2" color="textSecondary">
                    {aiSuggestions.wasEnhanced
                      ? "This job description has been professionally enhanced to include industry-standard terminology and important service details."
                      : 'Click "Enhance with AI" to professionally improve the job description with industry-standard terms and details.'}
                  </Typography>
                  {!aiSuggestions.wasEnhanced && formData.description && (
                    <Button
                      variant="text"
                      color="primary"
                      size="small"
                      startIcon={<AutoFixHighIcon />}
                      onClick={() => enhanceJobDescription()}
                      disabled={loadingAI}
                      sx={{ mt: 1 }}
                    >
                      Enhance Description
                    </Button>
                  )}
                </Box>

                {formData.originalDescription && (
                  <Button
                    variant="text"
                    color="primary"
                    size="small"
                    sx={{ mt: 2 }}
                    onClick={() => {
                      setFormData((prev) => ({
                        ...prev,
                        description: prev.originalDescription,
                        originalDescription: "",
                      }));
                      setAiSuggestions((prev) => ({
                        ...prev,
                        wasEnhanced: false,
                      }));
                    }}
                  >
                    Revert to Original Description
                  </Button>
                )}
              </Box>
            ) : (
              <Box sx={{ p: 2, textAlign: "center" }}>
                <Typography variant="body2" color="textSecondary">
                  Enter a job description to get AI insights
                </Typography>
              </Box>
            )}
          </Paper>
        </Grid>
      </Grid>
    </Container>
  );
};

export default CreateJob;
