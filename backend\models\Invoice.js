const mongoose = require("mongoose");
const Schema = mongoose.Schema;

const lineItemSchema = new Schema({
  description: {
    type: String,
    required: true,
  },
  quantity: {
    type: Number,
    required: true,
    min: 0,
  },
  unitPrice: {
    type: Number,
    required: true,
    min: 0,
  },
  unitCost: {
    type: Number,
    min: 0,
  },
  taxRate: {
    type: Number,
    default: 0,
    min: 0,
    max: 100,
  },
  discountRate: {
    type: Number,
    default: 0,
    min: 0,
    max: 100,
  },
  total: {
    type: Number,
    required: true,
    min: 0,
  },
  // Material pricing fields
  materialSource: {
    type: Schema.Types.ObjectId,
    ref: "MaterialSource",
  },
  inventoryItem: {
    type: Schema.Types.ObjectId,
    ref: "Material",
  },
  sku: String,
  externalSku: String,
  lastPriceCheck: Date,
  priceHistory: {
    type: Schema.Types.ObjectId,
    ref: "MaterialPriceHistory",
  },
  priceConfidence: {
    score: {
      type: Number,
      min: 0,
      max: 1,
    },
    factors: [String],
    anomalyDetected: Boolean,
    anomalyScore: Number,
  },
  originalQuoteItem: {
    type: Schema.Types.ObjectId,
    ref: "Quote.items", // Updated model reference
  },
});

const paymentSchema = new Schema(
  {
    amount: {
      type: Number,
      required: true,
      min: 0,
    },
    method: {
      type: String,
      required: true,
      enum: ["CREDIT_CARD", "BANK_TRANSFER", "CHECK", "CASH", "OTHER"],
    },
    status: {
      type: String,
      required: true,
      enum: ["PENDING", "COMPLETED", "FAILED", "REFUNDED"],
    },
    transactionId: String,
    date: {
      type: Date,
      required: true,
      default: Date.now,
    },
    notes: String,
    metadata: {
      gatewayResponse: Schema.Types.Mixed,
      refundReason: String,
      processingFees: Number,
    },
  },
  {
    timestamps: true,
  }
);

const invoiceSchema = new Schema(
  {
    number: {
      type: String,
      required: true,
      unique: true,
    },
    job: {
      type: Schema.Types.ObjectId,
      ref: "Job",
      required: true,
    },
    customer: {
      type: Schema.Types.ObjectId,
      ref: "Customer",
      required: true,
    },
    status: {
      type: String,
      required: true,
      enum: ["DRAFT", "SENT", "VIEWED", "PARTIAL", "PAID", "OVERDUE", "VOID"],
      default: "DRAFT",
    },
    issueDate: {
      type: Date,
      required: true,
      default: Date.now,
    },
    dueDate: {
      type: Date,
      required: true,
    },
    items: [lineItemSchema],
    subtotal: {
      type: Number,
      required: true,
      min: 0,
    },
    taxTotal: {
      type: Number,
      required: true,
      default: 0,
      min: 0,
    },
    discountTotal: {
      type: Number,
      required: true,
      default: 0,
      min: 0,
    },
    total: {
      type: Number,
      required: true,
      min: 0,
    },
    amountPaid: {
      type: Number,
      default: 0,
      min: 0,
    },
    balance: {
      type: Number,
      required: true,
      min: 0,
    },
    payments: [paymentSchema],
    notes: {
      internal: String,
      customer: String,
    },
    terms: String,
    metadata: {
      createdBy: {
        type: Schema.Types.ObjectId,
        ref: "User",
        required: true,
      },
      lastModifiedBy: {
        type: Schema.Types.ObjectId,
        ref: "User",
        required: true,
      },
      source: {
        type: String,
        enum: ["MANUAL", "JOB_AUTO", "RECURRING"],
        default: "MANUAL",
      },
    },
    aiInsights: {
      paymentProbability: {
        score: Number,
        factors: [String],
      },
      suggestedFollowUp: {
        date: Date,
        reason: String,
      },
      customerPatterns: {
        averagePaymentDelay: Number,
        preferredPaymentMethod: String,
        riskLevel: {
          type: String,
          enum: ["LOW", "MEDIUM", "HIGH"],
        },
      },
    },
    attachments: [
      {
        name: String,
        type: String,
        url: String,
        uploadedAt: Date,
        uploadedBy: {
          type: Schema.Types.ObjectId,
          ref: "User",
        },
      },
    ],
    history: [
      {
        action: {
          type: String,
          required: true,
        },
        date: {
          type: Date,
          required: true,
          default: Date.now,
        },
        user: {
          type: Schema.Types.ObjectId,
          ref: "User",
          required: true,
        },
        details: Schema.Types.Mixed,
      },
    ],
  },
  {
    timestamps: true,
  }
);

// Indexes
invoiceSchema.index({ number: 1 }, { unique: true });
invoiceSchema.index({ job: 1 });
invoiceSchema.index({ customer: 1 });
invoiceSchema.index({ status: 1 });
invoiceSchema.index({ dueDate: 1 });
invoiceSchema.index({ "payments.date": 1 });

// Virtual fields
invoiceSchema.virtual("isOverdue").get(function () {
  return this.status !== "PAID" && this.dueDate < new Date();
});

invoiceSchema.virtual("daysOverdue").get(function () {
  if (!this.isOverdue) return 0;
  return Math.floor((new Date() - this.dueDate) / (1000 * 60 * 60 * 24));
});

// Methods
invoiceSchema.methods.recordPayment = async function (paymentData) {
  this.payments.push(paymentData);
  this.amountPaid += paymentData.amount;
  this.balance = this.total - this.amountPaid;

  if (this.balance === 0) {
    this.status = "PAID";
  } else if (this.amountPaid > 0) {
    this.status = "PARTIAL";
  }

  await this.save();
};

invoiceSchema.methods.void = async function (reason, userId) {
  this.status = "VOID";
  this.history.push({
    action: "VOIDED",
    user: userId,
    details: { reason },
  });
  await this.save();
};

// Material pricing methods
invoiceSchema.methods.updateMaterialPrices = async function (userId) {
  const updatedItems = [];

  // Import models
  const MaterialSource = mongoose.model("MaterialSource");
  const MaterialPriceHistory = mongoose.model("MaterialPriceHistory");
  const Material = mongoose.model("Material");

  // For each item with material source info, attempt to update price
  for (let i = 0; i < this.items.length; i++) {
    const item = this.items[i];

    if (!item.materialSource || !item.sku) continue;

    try {
      // Get source for scraping
      const source = await MaterialSource.findById(item.materialSource);
      if (!source || !source.isActive) continue;

      // Get price history
      let priceHistory = item.priceHistory
        ? await MaterialPriceHistory.findById(item.priceHistory)
        : null;

      if (!priceHistory) {
        // Create new price history if not exists
        priceHistory = new MaterialPriceHistory({
          material: item.sku,
          source: source._id,
          materialName: item.description,
        });

        await priceHistory.save();
        item.priceHistory = priceHistory._id;
      }

      // In a real implementation, this would call the scraping service
      // For now, we'll only check existing price history
      if (priceHistory.prices.length > 0) {
        const latestPrice = priceHistory.prices.sort(
          (a, b) => new Date(b.date) - new Date(a.date)
        )[0];

        // Check if price has changed
        if (latestPrice.price !== item.unitPrice) {
          // Record old price
          const oldPrice = item.unitPrice;

          // Update price
          item.unitPrice = latestPrice.price;
          item.total = latestPrice.price * item.quantity;
          item.lastPriceCheck = new Date();

          // Check for anomaly
          const anomalyResult = await priceHistory.checkAnomaly(
            latestPrice.price
          );
          item.priceConfidence = {
            score: anomalyResult.isAnomaly ? 0.5 : 0.9,
            factors: anomalyResult.isAnomaly
              ? ["Significant price change detected"]
              : [],
            anomalyDetected: anomalyResult.isAnomaly,
            anomalyScore: anomalyResult.score,
          };

          // Add to history
          this.history.push({
            action: "MATERIAL_PRICE_UPDATED",
            user: userId,
            date: new Date(),
            details: {
              itemIndex: i,
              oldPrice,
              newPrice: latestPrice.price,
              isAnomaly: anomalyResult.isAnomaly,
            },
          });

          updatedItems.push({
            index: i,
            oldPrice,
            newPrice: latestPrice.price,
          });
        }
      }
    } catch (error) {
      console.error(`Error updating material price for item ${i}:`, error);
    }
  }

  // Recalculate totals if items were updated
  if (updatedItems.length > 0) {
    await this.save();
  }

  return updatedItems;
};

invoiceSchema.methods.getItemPriceHistory = async function (itemIndex) {
  if (!this.items[itemIndex] || !this.items[itemIndex].priceHistory) {
    return null;
  }

  const MaterialPriceHistory = mongoose.model("MaterialPriceHistory");
  const priceHistory = await MaterialPriceHistory.findById(
    this.items[itemIndex].priceHistory
  ).populate("source");

  return priceHistory;
};

invoiceSchema.methods.refreshMaterialFromInventory = async function (
  itemIndex,
  userId
) {
  if (!this.items[itemIndex] || !this.items[itemIndex].inventoryItem) {
    return {
      success: false,
      message: "Item not found or not linked to inventory",
    };
  }

  const Material = mongoose.model("Material");
  const inventoryItem = await Material.findById(
    this.items[itemIndex].inventoryItem
  );

  if (!inventoryItem) {
    return { success: false, message: "Inventory item not found" };
  }

  // Get current values for comparison
  const oldPrice = this.items[itemIndex].unitPrice;

  // Update from inventory
  this.items[itemIndex].description = inventoryItem.name;
  this.items[itemIndex].unitPrice = inventoryItem.unitPrice;
  this.items[itemIndex].unitCost = inventoryItem.costPrice;
  this.items[itemIndex].total =
    inventoryItem.unitPrice * this.items[itemIndex].quantity;

  // Add to history
  this.history.push({
    action: "MATERIAL_UPDATED_FROM_INVENTORY",
    user: userId,
    date: new Date(),
    details: {
      itemIndex,
      inventoryItemId: inventoryItem._id,
      oldPrice,
      newPrice: inventoryItem.unitPrice,
    },
  });

  await this.save();

  return {
    success: true,
    message: "Item updated from inventory",
    priceChanged: oldPrice !== inventoryItem.unitPrice,
    oldPrice,
    newPrice: inventoryItem.unitPrice,
  };
};

// Static method to create invoice from quote
invoiceSchema.statics.createFromQuote = async function (quoteId, userId) {
  const Quote = mongoose.model("Quote"); // Use renamed model
  const quote = await Quote.findById(quoteId);

  if (!quote) {
    throw new Error("Quote not found");
  }

  if (quote.conversionToInvoice.converted) {
    throw new Error("Quote has already been converted to an invoice");
  }

  const invoice = new this({
    number: await this.generateNextInvoiceNumber(),
    job: quote.job,
    customer: quote.customer,
    status: "DRAFT",
    issueDate: new Date(),
    dueDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
    items: quote.items.map((item) => ({
      description: item.description,
      quantity: item.quantity,
      unitPrice: item.unitPrice,
      unitCost: item.unitCost,
      taxRate: item.taxRate,
      discountRate: item.discountRate,
      total: item.total,
      materialSource: item.materialSource,
      sku: item.sku,
      externalSku: item.originalSku,
      lastPriceCheck: item.lastPriceCheck,
      priceHistory: item.priceHistory,
      priceConfidence: item.priceConfidence,
      originalQuoteItem: item._id,
    })),
    subtotal: quote.subtotal,
    taxTotal: quote.taxTotal,
    discountTotal: quote.discountTotal,
    total: quote.total,
    amountPaid: 0,
    balance: quote.total,
    notes: quote.notes,
    terms: quote.terms,
    metadata: {
      createdBy: userId,
      lastModifiedBy: userId,
      source: "QUOTE_CONVERSION",
    },
    history: [
      {
        action: "CREATED_FROM_QUOTE",
        user: userId,
        date: new Date(),
        details: { quoteId: quote._id, quoteNumber: quote.number },
      },
    ],
  });

  await invoice.save();

  // Update quote with conversion info
  quote.status = "CONVERTED_TO_INVOICE";
  quote.conversionToInvoice = {
    converted: true,
    invoice: invoice._id,
    convertedAt: new Date(),
    convertedBy: userId,
  };

  quote.history.push({
    action: "CONVERTED_TO_INVOICE",
    date: new Date(),
    user: userId,
    details: { invoiceId: invoice._id, invoiceNumber: invoice.number },
  });

  await quote.save();

  return invoice;
};

// Static method to generate next invoice number
invoiceSchema.statics.generateNextInvoiceNumber = async function () {
  const lastInvoice = await this.findOne().sort({ number: -1 });

  if (!lastInvoice) {
    return "INV-10001";
  }

  const lastNumber = parseInt(lastInvoice.number.split("-")[1], 10);
  return `INV-${lastNumber + 1}`;
};

// Pre-save middleware
invoiceSchema.pre("save", function (next) {
  // Calculate totals
  if (this.items && this.items.length > 0) {
    this.subtotal = this.items.reduce((sum, item) => sum + item.total, 0);
    this.taxTotal = this.items.reduce(
      (sum, item) => sum + (item.total * item.taxRate) / 100,
      0
    );
    this.discountTotal = this.items.reduce(
      (sum, item) => sum + (item.total * item.discountRate) / 100,
      0
    );
    this.total = this.subtotal + this.taxTotal - this.discountTotal;
    this.balance = this.total - this.amountPaid;
  }

  // Check overdue status
  if (this.status !== "PAID" && this.dueDate < new Date()) {
    this.status = "OVERDUE";
  }

  next();
});

// Database indexes for performance optimization
invoiceSchema.index({ number: 1 }); // Invoice number lookup
invoiceSchema.index({ customer: 1, status: 1 }); // Customer invoices by status
invoiceSchema.index({ job: 1 }); // Job-related invoices
invoiceSchema.index({ status: 1, dueDate: 1 }); // Payment tracking
invoiceSchema.index({ issueDate: -1 }); // Recent invoices
invoiceSchema.index({ dueDate: 1, status: 1 }); // Overdue tracking
invoiceSchema.index({ total: -1, status: 1 }); // High-value invoices
invoiceSchema.index({ "metadata.createdBy": 1, issueDate: -1 }); // User's invoices
invoiceSchema.index({ "aiInsights.paymentRiskScore": -1 }); // Risk-based queries
invoiceSchema.index({ "items.materialSource": 1 }); // Material source tracking
invoiceSchema.index({ "items.inventoryItem": 1 }); // Inventory usage
invoiceSchema.index({ "payments.status": 1, "payments.date": -1 }); // Payment history

const Invoice = mongoose.model("Invoice", invoiceSchema);
module.exports = Invoice;
