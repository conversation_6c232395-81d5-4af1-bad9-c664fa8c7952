/**
 * fuseSearch.js
 * Wrapper for Fuse.js fuzzy search library
 */

const Fuse = require("fuse.js");
const logger = require("./logger");

/**
 * FuseJS wrapper for fuzzy searching
 */
class FuseJS {
  /**
   * Create a new FuseJS instance
   * @param {Array} items - Array of items to search
   * @param {Object} options - Fuse.js options
   */
  constructor(items, options = {}) {
    // Default options for Fuse.js
    const defaultOptions = {
      // isCaseSensitive: false,
      includeScore: true,
      shouldSort: true,
      // includeMatches: false,
      // findAllMatches: false,
      // minMatchCharLength: 1,
      // location: 0,
      threshold: 0.6,
      // distance: 100,
      // useExtendedSearch: false,
      // ignoreLocation: false,
      // ignoreFieldNorm: false,
      keys: [],
    };

    // Merge default options with provided options
    const searchOptions = { ...defaultOptions, ...options };

    try {
      this.fuse = new Fuse(items, searchOptions);
    } catch (error) {
      logger.error("Error initializing FuseJS:", error);
      throw error;
    }
  }

  /**
   * Search for items using fuzzy matching
   * @param {string} pattern - Search pattern
   * @returns {Array} - Search results
   */
  search(pattern) {
    try {
      return this.fuse.search(pattern);
    } catch (error) {
      logger.error(`Error searching with FuseJS: ${error.message}`);
      return [];
    }
  }

  /**
   * Add an item to the Fuse index
   * @param {Object} item - Item to add
   */
  add(item) {
    try {
      this.fuse.add(item);
    } catch (error) {
      logger.error(`Error adding item to FuseJS: ${error.message}`);
    }
  }

  /**
   * Remove an item from the Fuse index
   * @param {function} predicate - Function to find the item to remove
   */
  remove(predicate) {
    try {
      this.fuse.remove(predicate);
    } catch (error) {
      logger.error(`Error removing item from FuseJS: ${error.message}`);
    }
  }
}

module.exports = {
  FuseJS,
};
