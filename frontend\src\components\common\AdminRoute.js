import React from "react";
import ProtectedRoute from "./ProtectedRoute";

/**
 * Administrator-only route protection
 *
 * This is a specialized version of ProtectedRoute that requires the user
 * to have the Administrator role.
 *
 * @param {Object} props Component props
 * @param {React.ReactNode} props.children Child components to render if authorized
 * @returns {React.ReactNode} Child components or redirect
 */
const AdminRoute = ({ children }) => {
  return (
    <ProtectedRoute requiredRoles={["Administrators"]}>
      {children}
    </ProtectedRoute>
  );
};

export default AdminRoute;
