/**
 * MaterialPriceHistory.js
 * Model for tracking price history of materials
 */

const mongoose = require("mongoose");

/**
 * Schema for material price history
 */
const materialPriceHistorySchema = new mongoose.Schema(
  {
    material: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Material",
      required: true,
      index: true,
    },
    source: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "MaterialSource",
      required: true,
      index: true,
    },
    prices: [
      {
        price: {
          type: Number,
          required: true,
        },
        currency: {
          type: String,
          default: "USD",
        },
        timestamp: {
          type: Date,
          default: Date.now,
        },
        metadata: {
          availability: String,
          url: String,
          notes: String,
        },
        scraperVersion: String,
      },
    ],
    lastUpdated: {
      type: Date,
      default: Date.now,
    },
    company: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Company",
    },
  },
  { timestamps: true }
);

// Database indexes for performance optimization
materialPriceHistorySchema.index({ material: 1, source: 1 }, { unique: true }); // Unique material-source combination
materialPriceHistorySchema.index({ material: 1, lastUpdated: -1 }); // Material price history by date
materialPriceHistorySchema.index({ source: 1, lastUpdated: -1 }); // Source price updates
materialPriceHistorySchema.index({ company: 1 }); // Company-specific price history
materialPriceHistorySchema.index({ "prices.timestamp": -1 }); // Price chronology
materialPriceHistorySchema.index({ "prices.price": 1 }); // Price value queries
materialPriceHistorySchema.index({ lastUpdated: -1 }); // Recent price updates
materialPriceHistorySchema.index({ createdAt: -1 }); // Recent price history records

/**
 * Add a new price point to the history
 * @param {Number} price - Price value
 * @param {Object} options - Price options
 * @returns {Promise<Object>} - Updated price history document
 */
materialPriceHistorySchema.methods.addPrice = async function (
  price,
  options = {}
) {
  const { currency = "USD", metadata = {}, scraperVersion } = options;

  // Create new price entry
  const priceEntry = {
    price,
    currency,
    timestamp: new Date(),
    metadata,
    scraperVersion,
  };

  // Add to prices array
  this.prices.push(priceEntry);

  // Update lastUpdated timestamp
  this.lastUpdated = new Date();

  // Save and return
  return this.save();
};

/**
 * Get price history for a specific time period
 * @param {Date} startDate - Start date for price history
 * @param {Date} endDate - End date for price history
 * @returns {Array} - Filtered price history
 */
materialPriceHistorySchema.methods.getPriceHistory = function (
  startDate,
  endDate = new Date()
) {
  return this.prices
    .filter((entry) => {
      return entry.timestamp >= startDate && entry.timestamp <= endDate;
    })
    .sort((a, b) => a.timestamp - b.timestamp);
};

/**
 * Get the latest price
 * @returns {Object|null} - Latest price entry or null if no prices
 */
materialPriceHistorySchema.methods.getLatestPrice = function () {
  if (this.prices.length === 0) {
    return null;
  }

  return this.prices.sort((a, b) => b.timestamp - a.timestamp)[0];
};

/**
 * Get price statistics
 * @param {Number} days - Number of days to consider
 * @returns {Object} - Price statistics
 */
materialPriceHistorySchema.methods.getPriceStats = function (days = 30) {
  const cutoffDate = new Date();
  cutoffDate.setDate(cutoffDate.getDate() - days);

  const recentPrices = this.prices
    .filter((entry) => entry.timestamp >= cutoffDate)
    .map((entry) => entry.price);

  if (recentPrices.length === 0) {
    return {
      min: null,
      max: null,
      avg: null,
      median: null,
      count: 0,
    };
  }

  // Calculate stats
  const min = Math.min(...recentPrices);
  const max = Math.max(...recentPrices);
  const avg =
    recentPrices.reduce((sum, price) => sum + price, 0) / recentPrices.length;

  // Calculate median
  const sortedPrices = [...recentPrices].sort((a, b) => a - b);
  const mid = Math.floor(sortedPrices.length / 2);
  const median =
    sortedPrices.length % 2 === 0
      ? (sortedPrices[mid - 1] + sortedPrices[mid]) / 2
      : sortedPrices[mid];

  return {
    min,
    max,
    avg,
    median,
    count: recentPrices.length,
  };
};

/**
 * Detect if a price is anomalous based on historical data
 * @param {Number} price - Price to check
 * @returns {Object} - Anomaly check result
 */
materialPriceHistorySchema.methods.detectPriceAnomaly = async function (price) {
  // Get price statistics for the past 90 days
  const stats = this.getPriceStats(90);

  // If we don't have enough price points, we can't detect anomalies
  if (stats.count < 3) {
    return {
      isAnomaly: false,
      reason: "Not enough price history",
      confidence: 0,
    };
  }

  // Calculate standard deviation
  const pricesSample = this.prices
    .slice(-Math.min(30, this.prices.length))
    .map((entry) => entry.price);

  const mean = stats.avg;
  const squareDiffs = pricesSample.map((price) => Math.pow(price - mean, 2));
  const avgSquareDiff =
    squareDiffs.reduce((sum, diff) => sum + diff, 0) / squareDiffs.length;
  const stdDev = Math.sqrt(avgSquareDiff);

  // Calculate z-score (how many standard deviations from the mean)
  const zScore = Math.abs((price - mean) / stdDev);

  // Calculate % change from the latest price
  const latestPrice = this.getLatestPrice();
  const percentChange = latestPrice
    ? ((price - latestPrice.price) / latestPrice.price) * 100
    : 0;

  // Check if this price is an anomaly
  // We consider it an anomaly if:
  // 1. It's more than 3 standard deviations from the mean, or
  // 2. It's more than 50% different from the previous price
  let isAnomaly = false;
  let reason = "";
  let confidence = 0;

  if (zScore > 3) {
    isAnomaly = true;
    reason = `Price deviates significantly from average (z-score: ${zScore.toFixed(
      2
    )})`;
    confidence = Math.min(0.9, (zScore - 3) / 7 + 0.5); // Scales from 0.5 to 0.9 as z-score goes from 3 to 10
  } else if (Math.abs(percentChange) > 50) {
    isAnomaly = true;
    reason = `Price changed by ${percentChange.toFixed(
      2
    )}% from previous price`;
    confidence = Math.min(0.9, Math.abs(percentChange) / 100); // Scales from 0.5 to 0.9 as % change goes from 50% to 100%
  }

  return {
    isAnomaly,
    reason,
    confidence: confidence.toFixed(2),
    statistics: {
      mean: mean.toFixed(2),
      stdDev: stdDev.toFixed(2),
      zScore: zScore.toFixed(2),
      percentChange: percentChange.toFixed(2),
      sampleSize: stats.count,
    },
  };
};

/**
 * Get price statistics using MongoDB aggregation
 * @param {Object} filters - Filtering options
 * @param {String} filters.materialId - Material ID
 * @param {String} filters.sourceId - Source ID
 * @param {String} filters.companyId - Company ID
 * @param {Date|String} filters.startDate - Start date for price history
 * @param {Date|String} filters.endDate - End date for price history
 * @returns {Promise<Object>} - Price statistics { min, max, avg, median, count }
 */
materialPriceHistorySchema.statics.getStatistics = async function ({
  materialId,
  sourceId,
  companyId,
  startDate,
  endDate,
}) {
  const matchConditions = {};
  if (materialId)
    matchConditions.material = new mongoose.Types.ObjectId(materialId);
  if (sourceId) matchConditions.source = new mongoose.Types.ObjectId(sourceId);
  if (companyId)
    matchConditions.company = new mongoose.Types.ObjectId(companyId);

  const pipeline = [];
  if (Object.keys(matchConditions).length > 0) {
    pipeline.push({ $match: matchConditions });
  }

  pipeline.push({ $unwind: "$prices" });

  const postUnwindMatchConditions = {};
  if (startDate)
    postUnwindMatchConditions["prices.timestamp"] = {
      ...(postUnwindMatchConditions["prices.timestamp"] || {}),
      $gte: new Date(startDate),
    };
  if (endDate)
    postUnwindMatchConditions["prices.timestamp"] = {
      ...(postUnwindMatchConditions["prices.timestamp"] || {}),
      $lte: new Date(endDate),
    };

  if (Object.keys(postUnwindMatchConditions).length > 0) {
    pipeline.push({ $match: postUnwindMatchConditions });
  }

  pipeline.push({
    $group: {
      _id: null,
      minPrice: { $min: "$prices.price" },
      maxPrice: { $max: "$prices.price" },
      avgPrice: { $avg: "$prices.price" },
      count: { $sum: 1 },
      allPrices: { $push: "$prices.price" },
    },
  });

  pipeline.push({
    $project: {
      _id: 0,
      min: "$minPrice",
      max: "$maxPrice",
      avg: "$avgPrice",
      count: "$count",
      allPrices: 1,
    },
  });

  const result = await this.aggregate(pipeline);

  if (result.length > 0) {
    const stats = result[0];
    if (stats.allPrices && stats.allPrices.length > 0) {
      const sortedPrices = [...stats.allPrices].sort((a, b) => a - b);
      const mid = Math.floor(sortedPrices.length / 2);
      stats.median =
        sortedPrices.length % 2 === 0
          ? (sortedPrices[mid - 1] + sortedPrices[mid]) / 2
          : sortedPrices[mid];
    } else {
      stats.median = null;
    }
    delete stats.allPrices; // Clean up
    return stats;
  }

  return { min: null, max: null, avg: null, median: null, count: 0 };
};

/**
 * Get the latest price entry using MongoDB aggregation
 * @param {Object} filters - Filtering options
 * @param {String} filters.materialId - Material ID
 * @param {String} filters.sourceId - Source ID
 * @param {String} filters.companyId - Company ID
 * @returns {Promise<Object|null>} - Latest price entry or null
 */
materialPriceHistorySchema.statics.getLatestPriceEntry = async function ({
  materialId,
  sourceId,
  companyId,
}) {
  const matchConditions = {};
  if (materialId)
    matchConditions.material = new mongoose.Types.ObjectId(materialId);
  if (sourceId) matchConditions.source = new mongoose.Types.ObjectId(sourceId);
  if (companyId)
    matchConditions.company = new mongoose.Types.ObjectId(companyId);

  const pipeline = [];
  if (Object.keys(matchConditions).length > 0) {
    pipeline.push({ $match: matchConditions });
  }

  pipeline.push(
    { $unwind: "$prices" },
    { $sort: { "prices.timestamp": -1 } },
    { $limit: 1 },
    {
      $project: {
        _id: 0,
        price: "$prices.price",
        currency: "$prices.currency",
        timestamp: "$prices.timestamp",
        metadata: "$prices.metadata",
        scraperVersion: "$prices.scraperVersion",
      },
    }
  );

  const result = await this.aggregate(pipeline);
  return result.length > 0 ? result[0] : null;
};

/**
 * Get price history for a specific time period using MongoDB aggregation
 * @param {Object} filters - Filtering options
 * @param {String} filters.materialId - Material ID
 * @param {String} filters.sourceId - Source ID
 * @param {String} filters.companyId - Company ID
 * @param {Date|String} filters.startDate - Start date for price history
 * @param {Date|String} filters.endDate - End date for price history
 * @returns {Promise<Array>} - Filtered price history entries
 */
materialPriceHistorySchema.statics.getPriceHistoryRange = async function ({
  materialId,
  sourceId,
  companyId,
  startDate,
  endDate,
}) {
  const matchConditions = {};
  if (materialId)
    matchConditions.material = new mongoose.Types.ObjectId(materialId);
  if (sourceId) matchConditions.source = new mongoose.Types.ObjectId(sourceId);
  if (companyId)
    matchConditions.company = new mongoose.Types.ObjectId(companyId);

  const pipeline = [];
  if (Object.keys(matchConditions).length > 0) {
    pipeline.push({ $match: matchConditions });
  }

  pipeline.push({ $unwind: "$prices" });

  const postUnwindMatchConditions = {};
  if (startDate)
    postUnwindMatchConditions["prices.timestamp"] = {
      ...(postUnwindMatchConditions["prices.timestamp"] || {}),
      $gte: new Date(startDate),
    };
  if (endDate)
    postUnwindMatchConditions["prices.timestamp"] = {
      ...(postUnwindMatchConditions["prices.timestamp"] || {}),
      $lte: new Date(endDate),
    };

  if (Object.keys(postUnwindMatchConditions).length > 0) {
    pipeline.push({ $match: postUnwindMatchConditions });
  }

  pipeline.push(
    { $sort: { "prices.timestamp": 1 } },
    {
      $project: {
        _id: 0,
        price: "$prices.price",
        currency: "$prices.currency",
        timestamp: "$prices.timestamp",
        metadata: "$prices.metadata",
        scraperVersion: "$prices.scraperVersion",
      },
    }
  );

  return this.aggregate(pipeline);
};
const MaterialPriceHistory = mongoose.model(
  "MaterialPriceHistory",
  materialPriceHistorySchema
);

module.exports = MaterialPriceHistory;
