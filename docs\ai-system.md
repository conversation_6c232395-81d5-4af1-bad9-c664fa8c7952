# AI System Architecture Documentation

## Overview

The Workiz AI system implements a multi-phase streaming architecture designed to reduce perceived latency from 140 seconds to 1-3 seconds for initial responses. This system leverages Google Gemini models with progressive enhancement and confidence-based pricing lookups.

## Performance Optimization

### Latency Reduction Strategy
- **Before**: Single-phase processing with 140s wait time
- **After**: Multi-phase streaming with 1-3s first response
- **Implementation**: WebSocket/SSE token-by-token streaming
- **User Experience**: Progressive disclosure with real-time updates

## Architecture Components

### 1. Streaming AI Service (`streamingAiService.js`)

Core service implementing enhanced four-phase quote generation with interactive clarification:

#### Phase 1: Quick Initial Estimate (1-3 seconds)
- **Model**: Gemini 2.0 Flash
- **Purpose**: Fast initial project assessment
- **Configuration**:
  - Temperature: 0.2 (consistent pricing)
  - Max Output Tokens: 1500 (faster generation)
  - Top P: 0.8, Top K: 40

#### Phase 1.5: Interactive Clarification (NEW - PHASE 4 ENHANCEMENT)
- **Model**: Gemini 2.0 Flash (for quick clarification detection)
- **Purpose**: Identify when user input is needed for accurate quote generation
- **Features**:
  - Real-time clarification detection based on initial estimate
  - Interactive Q&A without breaking streaming connection
  - Session persistence during clarification interaction
  - Automatic streaming resumption after answers received
- **User Experience**: "No way to respond" issue resolved - users can now interact with AI during streaming

#### Phase 2: Detailed Analysis (Progressive)
- **Model**: Gemini 2.5 Pro Preview
- **Purpose**: Comprehensive project breakdown (enhanced with clarification context)
- **Configuration**:
  - Temperature: 0.1 (pricing accuracy)
  - Max Output Tokens: 2000
  - Top P: 0.9, Top K: 50

#### Phase 3: Price Lookup with Confidence Scoring
- **Integration**: External pricing services
- **Confidence Levels**:
  - 90-100%: Exact product match
  - 70-89%: Similar product with adjustment
  - 50-69%: Category-based pricing
  - <50%: Manual review required

### 2. Frontend Streaming Hook (`useStreamingAI.js`)

React hook managing streaming phases and real-time updates:

```javascript
const STREAMING_PHASES = {
  IDLE: 'idle',
  CONNECTING: 'connecting',
  PHASE_1: 'phase_1',    // Quick estimate
  PHASE_2: 'phase_2',    // Detailed analysis
  PHASE_3: 'phase_3',    // Price lookup
  COMPLETED: 'completed',
  ERROR: 'error'
};
```

## Model Configuration

### Gemini 2.0 Flash (Quick Estimates)
- **Use Case**: Initial project assessment
- **Strengths**: Fast response, good for estimates
- **Token Limit**: 1500 (optimized for speed)
- **Temperature**: 0.2 (balanced creativity/consistency)

### Gemini 2.5 Pro Preview (Detailed Analysis)
- **Use Case**: Comprehensive project breakdown
- **Strengths**: Detailed analysis, accurate calculations
- **Token Limit**: 2000 (comprehensive responses)
- **Temperature**: 0.1 (maximum accuracy for pricing)

## Confidence-Based Price Lookup

### Confidence Scoring System

The system implements a confidence-based approach to price lookups:

1. **Exact Match (90-100% confidence)**
   - Direct product identification
   - Verified pricing from primary sources
   - High reliability for quotes

2. **Similar Product (70-89% confidence)**
   - Comparable products with adjustments
   - Cross-referenced pricing
   - Good reliability with notes

3. **Category Estimate (50-69% confidence)**
   - Industry average pricing
   - Category-based calculations
   - Requires validation

4. **Manual Review (<50% confidence)**
   - Complex or unique items
   - Insufficient data for automation
   - Human intervention required

## Session Management

### Session Lifecycle
1. **Initialization**: Create session with unique ID
2. **Phase 1**: Quick estimate generation
3. **Phase 2**: Detailed analysis streaming
4. **Phase 3**: Price lookup with progress updates
5. **Completion**: Final results compilation
6. **Cleanup**: Session data removal

### Session Data Structure
```javascript
{
  startTime: Date.now(),
  status: 'initialized|starting|quick_estimate|detailed_analysis|price_lookup|completed',
  progress: 0-100,
  formData: FormData,
  sseResponse: SSEConnection,
  quickEstimate: Object,
  detailedAnalysis: Object,
  finalResults: Object
}
```

## Error Handling

### Graceful Degradation
- **Connection Issues**: Fallback to polling
- **Model Failures**: Switch to backup models
- **Parsing Errors**: Structured fallback responses
- **Timeout Handling**: Progressive timeout increases

### Error Recovery
- **Automatic Retry**: Exponential backoff
- **Session Persistence**: Resume from last phase
- **User Notification**: Clear error messages
- **Fallback Options**: Manual input alternatives

## Performance Metrics

### Target Performance
- **First Response**: 1-3 seconds
- **Complete Analysis**: 15-30 seconds
- **Price Lookup**: 5-10 seconds per item
- **Total Time**: 30-60 seconds (vs. 140s baseline)

### Monitoring Points
- Phase completion times
- Model response latencies
- Price lookup success rates
- User engagement metrics

## Integration Points

### Backend Services
- **Streaming Routes**: `/api/streaming/*`
- **AI Controllers**: Quote generation endpoints
- **Price Services**: External pricing APIs
- **Database**: Session and result storage

### Frontend Components
- **Quote Creation**: Real-time form updates
- **Progress Indicators**: Phase-based progress bars
- **Result Display**: Streaming content updates
- **Error Handling**: User-friendly error states

## Security Considerations

### API Security
- **Authentication**: Bearer token validation
- **Rate Limiting**: Per-session request limits
- **Input Validation**: Sanitized form data
- **Output Filtering**: Safe content rendering

### Data Privacy
- **Session Isolation**: User-specific sessions
- **Data Cleanup**: Automatic session expiration
- **Logging**: Sanitized log entries
- **Compliance**: GDPR/privacy considerations

## Future Enhancements

### Planned Improvements
1. **Model Fine-tuning**: Domain-specific training
2. **Caching Layer**: Repeated query optimization
3. **Batch Processing**: Multiple quote generation
4. **Analytics Integration**: Performance monitoring
5. **A/B Testing**: Model comparison framework

### Scalability Considerations
- **Horizontal Scaling**: Multi-instance deployment
- **Load Balancing**: Session distribution
- **Resource Management**: Memory and CPU optimization
- **Database Optimization**: Query performance tuning

## Troubleshooting

### Common Issues
1. **Slow Initial Response**: Check model availability
2. **Parsing Failures**: Validate JSON structure
3. **Price Lookup Errors**: Verify external API status
4. **Session Timeouts**: Adjust timeout configurations

### Debug Tools
- **Session Monitoring**: Real-time session status
- **Performance Profiling**: Phase timing analysis
- **Error Logging**: Structured error tracking
- **Health Checks**: System component status

## References

- [Google Gemini API Documentation](https://ai.google.dev/docs)
- [Server-Sent Events Specification](https://html.spec.whatwg.org/multipage/server-sent-events.html)
- [Streaming Best Practices](https://developer.mozilla.org/en-US/docs/Web/API/Server-sent_events)
- [Performance Optimization Guidelines](https://web.dev/performance/)