import React, { useState, useEffect, useCallback } from 'react';
import {
  Box,
  Typography,
  LinearProgress,
  Alert,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  IconButton,
  Chip,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Tooltip,
  CircularProgress,
} from '@mui/material';
import {
  ExpandMore as ExpandMoreIcon,
  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon,
  HourglassEmpty as HourglassEmptyIcon,
  Search as SearchIcon,
  Refresh as RefreshIcon,
  AttachMoney as AttachMoneyIcon,
} from '@mui/icons-material';
import { formatCurrency } from '../../utils/formatters';

const PriceLookupProgressIndicator = ({
  isActive = false,
  items = [],
  onStatusUpdate,
  onRetryLookup,
  expanded = false,
  onToggleExpanded,
  title = "AI Price Lookup in Progress",
  showRetryOption = true,
  autoHide = true,
  autoHideDuration = 5000,
}) => {
  const [internalExpanded, setInternalExpanded] = useState(expanded);
  const [lookupStatus, setLookupStatus] = useState({
    total: 0,
    pending: 0,
    found: 0,
    failed: 0,
    progress: 0,
    averageResponseTime: 0,
    totalValue: 0,
  });

  // Calculate lookup statistics
  const calculateStatus = useCallback(() => {
    if (!items || items.length === 0) {
      return {
        total: 0,
        pending: 0,
        found: 0,
        failed: 0,
        progress: 0,
        averageResponseTime: 0,
        totalValue: 0,
      };
    }

    const total = items.length;
    let pending = 0;
    let found = 0;
    let failed = 0;
    let totalValue = 0;
    let totalResponseTime = 0;
    let responseTimes = 0;

    items.forEach((item) => {
      const status = item.priceInfo?.status || 'pending';
      
      switch (status) {
        case 'pending':
        case 'searching':
          pending++;
          break;
        case 'found':
        case 'existing_price':
        case 'estimated': // 🚀 NEW: Handle estimated prices from generic lookup
          found++;
          if (item.price && item.quantity) {
            totalValue += item.price * item.quantity;
          }
          break;
        case 'not_found':
        case 'error':
        case 'failed':
        case 'timeout': // 🚀 NEW: Handle timeout errors
        case 'blocked': // 🚀 NEW: Handle blocked scrapers
          failed++;
          break;
        case 'skipped': // 🚀 NEW: Handle skipped items
          // Don't count as failed, just processed
          break;
        default:
          pending++;
      }

      // Add response time if available
      if (item.priceInfo?.responseTime) {
        totalResponseTime += item.priceInfo.responseTime;
        responseTimes++;
      }
    });

    const progress = total > 0 ? ((found + failed) / total) * 100 : 0;
    const averageResponseTime = responseTimes > 0 ? Math.round(totalResponseTime / responseTimes) : 0;

    return {
      total,
      pending,
      found,
      failed,
      progress,
      averageResponseTime,
      totalValue,
    };
  }, [items]);

  // Update status when items change
  useEffect(() => {
    const newStatus = calculateStatus();
    setLookupStatus(newStatus);

    // Notify parent component of status update
    if (onStatusUpdate) {
      onStatusUpdate(newStatus);
    }
  }, [items, calculateStatus, onStatusUpdate]);

  // Auto-hide when complete and autoHide is enabled
  useEffect(() => {
    if (autoHide && lookupStatus.progress === 100 && lookupStatus.pending === 0) {
      const timer = setTimeout(() => {
        setInternalExpanded(false);
      }, autoHideDuration);

      return () => clearTimeout(timer);
    }
  }, [autoHide, autoHideDuration, lookupStatus.progress, lookupStatus.pending]);

  // Handle expand/collapse
  const handleToggleExpanded = () => {
    const newExpanded = !internalExpanded;
    setInternalExpanded(newExpanded);
    if (onToggleExpanded) {
      onToggleExpanded(newExpanded);
    }
  };

  // Don't render if not active and no items
  if (!isActive && (!items || items.length === 0)) {
    return null;
  }

  const getStatusColor = () => {
    if (lookupStatus.pending > 0) return 'primary';
    if (lookupStatus.failed > 0 && lookupStatus.found === 0) return 'error';
    if (lookupStatus.failed > 0) return 'warning';
    return 'success';
  };

  const getStatusIcon = (item) => {
    const status = item.priceInfo?.status || 'pending';
    
    switch (status) {
      case 'found':
      case 'existing_price':
        return <CheckCircleIcon color="success" />;
      case 'estimated': // 🚀 NEW: Show different icon for estimated prices
        return <CheckCircleIcon color="warning" />;
      case 'pending':
      case 'searching':
        return <CircularProgress size={20} />;
      case 'skipped': // 🚀 NEW: Show info icon for skipped items
        return <HourglassEmptyIcon color="info" />;
      case 'timeout': // 🚀 NEW: Show specific icon for timeouts
        return <ErrorIcon color="warning" />;
      case 'blocked': // 🚀 NEW: Show specific icon for blocked scrapers
        return <ErrorIcon color="error" />;
      case 'not_found':
      case 'error':
      case 'failed':
        return <ErrorIcon color="error" />;
      default:
        return <HourglassEmptyIcon color="action" />;
    }
  };

  const getStatusText = (item) => {
    const status = item.priceInfo?.status || 'pending';
    
    switch (status) {
      case 'found':
        return `Price found: ${formatCurrency(item.price)}`;
      case 'existing_price':
        return `Existing price: ${formatCurrency(item.price)}`;
      case 'estimated': // 🚀 NEW: Show estimated price status
        return `Estimated: ${formatCurrency(item.price)} (${item.priceInfo?.confidence || 'medium'} confidence)`;
      case 'pending':
        return 'Waiting for lookup...';
      case 'searching':
        return 'Searching for price...';
      case 'skipped': // 🚀 NEW: Show skipped status
        return `Skipped: ${item.priceInfo?.lookupError || 'Not suitable for price lookup'}`;
      case 'timeout': // 🚀 NEW: Show timeout status
        return 'Timeout: Price lookup took too long';
      case 'blocked': // 🚀 NEW: Show blocked status
        return 'Blocked: Scraper was blocked by website';
      case 'not_found':
        return 'No price found';
      case 'error':
      case 'failed':
        return `Error: ${item.priceInfo?.lookupError || 'Lookup failed'}`;
      default:
        return 'Unknown status';
    }
  };

  return (
    <Box sx={{ mb: 2 }}>
      <Accordion 
        expanded={internalExpanded} 
        onChange={handleToggleExpanded}
        sx={{
          border: `2px solid`,
          borderColor: `${getStatusColor()}.main`,
          borderRadius: 1,
          '&:before': { display: 'none' },
        }}
      >
        <AccordionSummary 
          expandIcon={<ExpandMoreIcon />}
          sx={{
            bgcolor: `${getStatusColor()}.main`,
            color: 'white',
            '& .MuiAccordionSummary-expandIconWrapper': {
              color: 'white',
            },
          }}
        >
          <Box sx={{ display: 'flex', alignItems: 'center', width: '100%', gap: 2 }}>
            <SearchIcon />
            <Box sx={{ flexGrow: 1 }}>
              <Typography variant="h6" component="span" sx={{ lineHeight: 1.6, display: 'block' }}>
                {title}
              </Typography>
              <Typography variant="body2" component="span" sx={{ opacity: 0.9, lineHeight: 1.43, display: 'block' }}>
                {lookupStatus.total > 0 && (
                  <span>
                    {lookupStatus.found} found, {lookupStatus.pending} pending, {lookupStatus.failed} failed
                    {lookupStatus.totalValue > 0 && (
                      <span> • Total value: {formatCurrency(lookupStatus.totalValue)}</span>
                    )}
                  </span>
                )}
              </Typography>
            </Box>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              {lookupStatus.averageResponseTime > 0 && (
                <Chip
                  label={`${lookupStatus.averageResponseTime}ms avg`}
                  size="small"
                  sx={{ bgcolor: 'rgba(255,255,255,0.2)', color: 'white' }}
                />
              )}
              <Box component="span" sx={{ fontSize: '1.25rem', fontWeight: 500, lineHeight: 1.6 }}>
                {Math.round(lookupStatus.progress)}%
              </Box>
            </Box>
          </Box>
        </AccordionSummary>
        
        <AccordionDetails sx={{ p: 0 }}>
          {/* Progress Bar */}
          <Box sx={{ p: 2, pb: 1 }}>
            <LinearProgress
              variant="determinate"
              value={lookupStatus.progress}
              color={getStatusColor()}
              sx={{ height: 8, borderRadius: 4 }}
            />
          </Box>

          {/* Summary Stats */}
          <Box sx={{ p: 2, pt: 0 }}>
            <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
              <Chip
                icon={<CheckCircleIcon />}
                label={`${lookupStatus.found} Found`}
                color="success"
                variant="outlined"
                size="small"
              />
              <Chip
                icon={<HourglassEmptyIcon />}
                label={`${lookupStatus.pending} Pending`}
                color="primary"
                variant="outlined"
                size="small"
              />
              <Chip
                icon={<ErrorIcon />}
                label={`${lookupStatus.failed} Failed`}
                color="error"
                variant="outlined"
                size="small"
              />
              {lookupStatus.totalValue > 0 && (
                <Chip
                  icon={<AttachMoneyIcon />}
                  label={`${formatCurrency(lookupStatus.totalValue)} Total`}
                  color="secondary"
                  variant="outlined"
                  size="small"
                />
              )}
            </Box>
          </Box>

          {/* Item Details */}
          {items && items.length > 0 && (
            <List dense sx={{ maxHeight: 300, overflow: 'auto' }}>
              {items.map((item, index) => (
                <ListItem
                  key={index}
                  secondaryAction={
                    showRetryOption && 
                    item.priceInfo?.status === 'failed' && 
                    onRetryLookup && (
                      <Tooltip title="Retry price lookup">
                        <IconButton
                          edge="end"
                          size="small"
                          onClick={() => onRetryLookup(item, index)}
                        >
                          <RefreshIcon />
                        </IconButton>
                      </Tooltip>
                    )
                  }
                >
                  <ListItemIcon sx={{ minWidth: 40 }}>
                    {getStatusIcon(item)}
                  </ListItemIcon>
                  <ListItemText
                    primary={item.name || `Item ${index + 1}`}
                    secondary={
                      <span>
                        <span style={{ color: 'rgba(0, 0, 0, 0.6)', fontSize: '0.875rem' }}>
                          {getStatusText(item)}
                        </span>
                        {item.priceInfo?.searchQuery && (
                          <span style={{ display: 'block', color: 'rgba(0, 0, 0, 0.6)', fontSize: '0.75rem' }}>
                            Search: {item.priceInfo.searchQuery} ({item.priceInfo.searchType})
                          </span>
                        )}
                        {item.priceInfo?.responseTime && (
                          <span style={{ display: 'block', color: 'rgba(0, 0, 0, 0.6)', fontSize: '0.75rem' }}>
                            • {item.priceInfo.responseTime}ms
                          </span>
                        )}
                      </span>
                    }
                  />
                </ListItem>
              ))}
            </List>
          )}

          {/* No items message */}
          {(!items || items.length === 0) && (
            <Box sx={{ p: 2 }}>
              <Alert severity="info">
                No items to lookup prices for.
              </Alert>
            </Box>
          )}
        </AccordionDetails>
      </Accordion>
    </Box>
  );
};

export default PriceLookupProgressIndicator;