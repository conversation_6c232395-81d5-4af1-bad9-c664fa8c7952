const mongoose = require("mongoose");
const dotenv = require("dotenv");
const connectDB = require("../config/database");
const MaterialSource = require("../models/MaterialSource");
const logger = require("../utils/logger");

// Load environment variables
dotenv.config({ path: require("path").resolve(__dirname, "../.env") }); // Ensure correct .env path

const checkSource = async () => {
  try {
    logger.info("Connecting to database...");
    await connectDB();
    logger.info("Database connected.");

    logger.info("Searching for 'HOME_DEPOT' MaterialSource...");
    const homeDepotSource = await MaterialSource.findOne({
      type: "HOME_DEPOT",
    });

    if (homeDepotSource) {
      logger.info("Found Home Depot source:", homeDepotSource.toObject()); // Log the full object
      logger.info(`Home Depot 'enabled' status: ${homeDepotSource.enabled}`);
    } else {
      logger.warn(
        "Could not find a MaterialSource entry with type: 'HOME_DEPOT'"
      );
    }
  } catch (error) {
    logger.error("Error checking Home Depot source:", error);
  } finally {
    logger.info("Disconnecting from database...");
    await mongoose.disconnect();
    logger.info("Database disconnected.");
  }
};

checkSource();
