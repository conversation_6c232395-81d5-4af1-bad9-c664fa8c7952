import React, { useState, useEffect } from "react";
import {
  Box,
  TextField,
  Button,
  CircularProgress,
  Typography,
  Paper,
  Grid,
  Divider,
  Card,
  CardContent,
  CardMedia,
  CardActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  InputAdornment,
  Tooltip,
  Alert,
} from "@mui/material";
import {
  Search as SearchIcon,
  Link as LinkIcon,
  Warning as WarningIcon,
  Cached as CachedIcon,
  Add as AddIcon,
} from "@mui/icons-material";
import axios from "axios";
import { formatCurrency } from "../../utils/formatters";
import { useSnackbar } from "notistack";
import { useSelector } from "react-redux";

/**
 * Component for searching and selecting materials from various sources
 */
const MaterialSearch = ({ onSelectMaterial, buttonLabel = "Add Material" }) => {
  console.log("🔧 MATERIALSEARCH COMPONENT LOADED - UPDATED VERSION");
  const [searchQuery, setSearchQuery] = useState("");
  const [materialUrl, setMaterialUrl] = useState("");
  const [searchMode, setSearchMode] = useState("query"); // 'query' or 'url'
  const [selectedSourceId, setSelectedSourceId] = useState("");
  const [sources, setSources] = useState([]);
  const [searchResults, setSearchResults] = useState([]);
  const [selectedMaterial, setSelectedMaterial] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);

  const { enqueueSnackbar } = useSnackbar();
  const { userInfo } = useSelector((state) => state.auth);

  // Fetch available material sources
  useEffect(() => {
    const fetchSources = async () => {
      try {
        if (!userInfo || !userInfo.token) {
          console.error("No authentication token available");
          return;
        }

        const response = await axios.get("/api/quotes/sources", {
          headers: { Authorization: `Bearer ${userInfo.token}` },
        });

        if (response.data.success) {
          const enabledSources = response.data.data.filter(
            (source) => source.enabled
          );
          setSources(enabledSources);

          if (enabledSources.length > 0 && !selectedSourceId) {
            setSelectedSourceId(enabledSources[0]._id);
          }
        }
      } catch (error) {
        console.error("Error fetching material sources:", error);
        enqueueSnackbar("Failed to load material sources", {
          variant: "error",
        });
      }
    };

    // Only fetch if we have a valid userInfo
    if (userInfo) {
      fetchSources();
    }
  }, [userInfo, enqueueSnackbar, selectedSourceId]);

  // Handle search submission
  const handleSearch = async (e) => {
    e.preventDefault();

    if (
      searchMode === "query" &&
      (!searchQuery.trim() || searchQuery.length < 3)
    ) {
      enqueueSnackbar("Please enter at least 3 characters to search", {
        variant: "warning",
      });
      return;
    }

    if (searchMode === "url" && !materialUrl.trim()) {
      enqueueSnackbar("Please enter a URL to search", { variant: "warning" });
      return;
    }

    setIsLoading(true);
    setError(null);
    setSearchResults([]);
    setSelectedMaterial(null);

    try {
      // let response; // Removed unused variable

      if (!userInfo || !userInfo.token) {
        enqueueSnackbar(
          "Authentication token is missing. Please log in again.",
          { variant: "error" }
        );
        return;
      }

      if (searchMode === "query") {
        // --- Search Internal and External ---
        const internalSearchPromise = axios.get("/api/materials", {
          params: { keyword: searchQuery, pageSize: 20 }, // Search internal DB
          headers: { Authorization: `Bearer ${userInfo.token}` },
        });
        const externalSearchPromise = axios.get(
          "/api/quotes/materials/search",
          {
            params: {
              query: searchQuery,
              sourceId: selectedSourceId || undefined,
              limit: 10,
            }, // Search external scrapers
            headers: { Authorization: `Bearer ${userInfo.token}` },
          }
        );

        const [internalResult, externalResult] = await Promise.allSettled([
          internalSearchPromise,
          externalSearchPromise,
        ]);

        let combinedResults = [];
        let internalError = null;
        let externalError = null;

        // Process internal results
        if (
          internalResult.status === "fulfilled" &&
          internalResult.value.data
        ) {
          // Assuming internal API returns { materials: [...] }
          const internalMaterials = Array.isArray(
            internalResult.value.data.materials
          )
            ? internalResult.value.data.materials
            : [];
          combinedResults = combinedResults.concat(
            internalMaterials.map((m) => ({ ...m, isInternal: true }))
          );
        } else if (internalResult.status === "rejected") {
          internalError = internalResult.reason;
          console.error("Error fetching internal materials:", internalError);
        }

        // Process external results
        if (
          externalResult.status === "fulfilled" &&
          externalResult.value.data?.success
        ) {
          const externalMaterials = Array.isArray(
            externalResult.value.data.data
          )
            ? externalResult.value.data.data
            : [];
          console.log("External materials received:", externalMaterials.length);
          if (externalMaterials.length > 0) {
            console.log(
              "First external material fields:",
              Object.keys(externalMaterials[0])
            );
            console.log(
              "First external material sample:",
              externalMaterials[0]
            );
          }
          // Add external results, potentially filtering duplicates if needed later
          combinedResults = combinedResults.concat(
            externalMaterials.map((m) => ({ ...m, isInternal: false }))
          );
        } else if (externalResult.status === "rejected") {
          externalError = externalResult.reason;
          console.error("Error fetching external materials:", externalError);
        } else if (
          externalResult.status === "fulfilled" &&
          !externalResult.value.data?.success
        ) {
          // Handle cases where the API call succeeded but the operation failed (e.g., scraper blocked)
          console.warn(
            "External material search API call succeeded but operation failed:",
            externalResult.value.data
          );
          // Optionally set an error message based on externalResult.value.data.message or sourceStatus
        }

        console.log("Final combined results:", combinedResults.length);
        if (combinedResults.length > 0) {
          console.log("Combined results sample:", combinedResults[0]);
        }
        setSearchResults(combinedResults); // Always set an array

        if (combinedResults.length === 0) {
          const errorMsg =
            internalError && externalError
              ? "Failed to fetch materials from internal and external sources."
              : internalError
              ? "Failed to fetch internal materials. External search yielded no results."
              : externalError
              ? "Failed to fetch external materials. Internal search yielded no results."
              : "No materials found matching your search query.";
          setError(errorMsg);
        }
        // --- End Combined Search ---
      } else {
        // searchMode === 'url'
        // Search by URL (remains largely the same)
        const response = await axios.post(
          "/api/quotes/url",
          { url: materialUrl },
          { headers: { Authorization: `Bearer ${userInfo.token}` } }
        );

        if (response.data.success) {
          const material = response.data.data;
          // Ensure searchResults is always an array
          setSearchResults(
            material ? [{ ...material, isInternal: false }] : []
          ); // Add isInternal flag

          if (!material) {
            setError("No material found at the provided URL");
          }
        } else {
          // Handle API call success but operational failure (e.g., URL not found by scraper)
          setError(response.data.message || "Failed to fetch material by URL");
          setSearchResults([]); // Ensure array
        }
      }
    } catch (error) {
      console.error("Error searching for materials:", error);
      setError(
        error.response?.data?.message || "Failed to search for materials"
      );
      enqueueSnackbar("Error searching for materials", { variant: "error" });
    } finally {
      setIsLoading(false);
    }
  };

  // Handle selecting a material
  const handleSelectMaterial = (material) => {
    setSelectedMaterial(material);
  };

  // Handle add button click
  const handleAddClick = () => {
    if (selectedMaterial && onSelectMaterial) {
      onSelectMaterial(selectedMaterial);

      // Reset selection after adding
      setSelectedMaterial(null);

      // Optionally clear results
      // setSearchResults([]);
    }
  };

  // Format material source for display
  const formatMaterialSource = (source) => {
    switch (source) {
      case "HOME_DEPOT":
        return "Home Depot";
      case "Platt Electric Supply":
        return "Platt Electric Supply";
      case "GRAYBAR":
        return "Graybar";
      default:
        return source;
    }
  };

  return (
    <Box>
      {/* Search Form */}
      <Paper sx={{ p: 2, mb: 3 }}>
        <Box component="div">
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} sm={4}>
              <FormControl fullWidth variant="outlined">
                <InputLabel id="search-mode-label">Search By</InputLabel>
                <Select
                  labelId="search-mode-label"
                  value={searchMode}
                  onChange={(e) => setSearchMode(e.target.value)}
                  label="Search By"
                >
                  <MenuItem value="query">Description/SKU</MenuItem>
                  <MenuItem value="url">Material URL</MenuItem>
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12} sm={searchMode === "query" ? 4 : 8}>
              {searchMode === "query" ? (
                <TextField
                  fullWidth
                  label="Search materials..."
                  variant="outlined"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  InputProps={{
                    endAdornment: (
                      <InputAdornment position="end">
                        <SearchIcon />
                      </InputAdornment>
                    ),
                  }}
                />
              ) : (
                <TextField
                  fullWidth
                  label="Material URL"
                  variant="outlined"
                  value={materialUrl}
                  onChange={(e) => setMaterialUrl(e.target.value)}
                  placeholder="https://www.homedepot.com/p/..."
                  InputProps={{
                    endAdornment: (
                      <InputAdornment position="end">
                        <LinkIcon />
                      </InputAdornment>
                    ),
                  }}
                />
              )}
            </Grid>

            {searchMode === "query" && (
              <Grid item xs={12} sm={4}>
                <FormControl fullWidth variant="outlined">
                  <InputLabel id="source-select-label">
                    Material Source
                  </InputLabel>
                  <Select
                    labelId="source-select-label"
                    value={selectedSourceId}
                    onChange={(e) => setSelectedSourceId(e.target.value)}
                    label="Material Source"
                  >
                    {sources.map((source) => (
                      <MenuItem key={source._id} value={source._id}>
                        {source.name}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
            )}

            <Grid item xs={12}>
              <Button
                onClick={(e) => handleSearch(e)}
                variant="contained"
                color="primary"
                disabled={isLoading}
                startIcon={
                  isLoading ? <CircularProgress size={20} /> : <SearchIcon />
                }
              >
                {isLoading ? "Searching..." : "Search Materials"}
              </Button>
            </Grid>
          </Grid>
        </Box>
      </Paper>

      {/* Error Messages */}
      {error && (
        <Alert severity="info" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {/* Search Results */}
      {searchResults.length > 0 && (
        <Box sx={{ mb: 3 }}>
          <Typography variant="h6" gutterBottom>
            Search Results ({searchResults.length})
          </Typography>

          <Grid container spacing={2}>
            {Array.isArray(searchResults) &&
              searchResults.map(
                (
                  material,
                  index // Add Array.isArray check for safety
                ) => (
                  <Grid item xs={12} sm={6} md={4} key={index}>
                    <Card
                      variant="outlined"
                      sx={{
                        height: "100%",
                        display: "flex",
                        flexDirection: "column",
                        border:
                          selectedMaterial === material
                            ? "2px solid #1976d2"
                            : undefined,
                      }}
                    >
                      {(material.imageUrl || material.image) && (
                        <CardMedia
                          component="img"
                          height="140"
                          image={material.imageUrl || material.image}
                          alt={material.name || material.title}
                          sx={{ objectFit: "contain", p: 1 }}
                        />
                      )}

                      <CardContent sx={{ flexGrow: 1 }}>
                        <Typography
                          variant="subtitle1"
                          component="div"
                          gutterBottom
                          noWrap
                        >
                          {material.name || material.title}
                        </Typography>

                        {material.sku && (
                          <Typography
                            variant="body2"
                            color="text.secondary"
                            gutterBottom
                          >
                            SKU: {material.sku}
                          </Typography>
                        )}

                        <Typography variant="h6" color="primary">
                          {formatCurrency(
                            material.price ||
                              material.perUnitPrice ||
                              material.unitPrice ||
                              material.finalPrice
                          )}
                        </Typography>

                        <Box
                          sx={{ display: "flex", alignItems: "center", mt: 1 }}
                        >
                          <Typography variant="body2" color="text.secondary">
                            Source:{" "}
                            {material.isInternal
                              ? "Internal DB"
                              : formatMaterialSource(material.source)}
                          </Typography>

                          {material.cached && (
                            <Tooltip title="Price from cache">
                              <CachedIcon
                                color="action"
                                fontSize="small"
                                sx={{ ml: 1 }}
                              />
                            </Tooltip>
                          )}

                          {material.priceAnomaly?.isAnomaly && (
                            <Tooltip title="Price anomaly detected">
                              <WarningIcon
                                color="warning"
                                fontSize="small"
                                sx={{ ml: 1 }}
                              />
                            </Tooltip>
                          )}
                        </Box>
                      </CardContent>

                      <CardActions>
                        <Button
                          size="small"
                          onClick={() => handleSelectMaterial(material)}
                          color={
                            selectedMaterial === material
                              ? "primary"
                              : "inherit"
                          }
                        >
                          Select
                        </Button>
                        {(material.url || material.productUrl) && (
                          <Button
                            size="small"
                            href={material.url || material.productUrl}
                            target="_blank"
                            rel="noopener noreferrer"
                            startIcon={<LinkIcon fontSize="small" />}
                          >
                            View
                          </Button>
                        )}
                      </CardActions>
                    </Card>
                  </Grid>
                )
              )}
          </Grid>
        </Box>
      )}

      {/* Selected Material */}
      {selectedMaterial && (
        <Box sx={{ mb: 3 }}>
          <Divider sx={{ my: 2 }} />

          <Typography variant="h6" gutterBottom>
            Selected Material
          </Typography>

          <Paper variant="outlined" sx={{ p: 2 }}>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={2}>
                {(selectedMaterial.imageUrl || selectedMaterial.image) && (
                  <Box
                    component="img"
                    src={selectedMaterial.imageUrl || selectedMaterial.image}
                    alt={selectedMaterial.name || selectedMaterial.title}
                    sx={{
                      width: "100%",
                      maxHeight: 120,
                      objectFit: "contain",
                    }}
                  />
                )}
              </Grid>

              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle1">
                  {selectedMaterial.name || selectedMaterial.title}
                </Typography>
                {selectedMaterial.sku && (
                  <Typography variant="body2" color="text.secondary">
                    SKU: {selectedMaterial.sku}
                  </Typography>
                )}
                <Typography variant="body2" color="text.secondary">
                  Source:{" "}
                  {selectedMaterial.isInternal
                    ? "Internal DB"
                    : formatMaterialSource(selectedMaterial.source)}
                </Typography>
              </Grid>

              <Grid item xs={12} sm={2}>
                <Typography variant="h6" color="primary">
                  {formatCurrency(
                    selectedMaterial.price ||
                      selectedMaterial.perUnitPrice ||
                      selectedMaterial.unitPrice ||
                      selectedMaterial.finalPrice
                  )}
                </Typography>
              </Grid>

              <Grid
                item
                xs={12}
                sm={2}
                sx={{
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "flex-end",
                }}
              >
                <Button
                  variant="contained"
                  color="primary"
                  onClick={handleAddClick}
                  startIcon={<AddIcon />}
                >
                  {buttonLabel}
                </Button>
              </Grid>
            </Grid>
          </Paper>
        </Box>
      )}

      {/* No Selected Material */}
      {!selectedMaterial && searchResults.length > 0 && (
        <Box sx={{ mt: 2, textAlign: "center" }}>
          <Typography variant="body1" color="text.secondary">
            Select a material from the results above to add it to your quote
          </Typography>
        </Box>
      )}
    </Box>
  );
};

export default MaterialSearch;
