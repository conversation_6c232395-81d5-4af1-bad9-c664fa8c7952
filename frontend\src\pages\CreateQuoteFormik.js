import React, {
  useState,
  useEffect,
  useMemo,
  useCallback,
  useRef,
} from "react";
import { unstable_batchedUpdates } from "react-dom";
import { Modal, Backdrop, Fade } from "@mui/material";
import {
  Container,
  Typography,
  Box,
  Paper,
  Grid,
  TextField,
  Button,
  CircularProgress,
  Alert,
  Divider,
  IconButton,
  Autocomplete,
  InputAdornment,
  Chip,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  FormControlLabel,
  Switch,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Card,
  CardMedia,
  Tooltip,
  LinearProgress,
} from "@mui/material";
import { LoadingButton } from "@mui/lab";
import {
  Add as AddIcon,
  Delete as DeleteIcon,
  Refresh as RefreshIcon,
  AutoAwesome as AutoAwesomeIcon,
  Save as SaveIcon,
  ExpandMore as ExpandMoreIcon,
  Visibility as VisibilityIcon,
  Image as ImageIcon,
  Close as CloseIcon,
  AccessTime as AccessTimeIcon,
  Search as SearchIcon,
  HelpOutline as HelpOutlineIcon,
  CheckCircleOutline as CheckCircleOutlineIcon,
  ErrorOutline as ErrorOutlineIcon,
  QueryBuilder as QueryBuilderIcon,
  InfoOutlined as InfoIcon,
  CloudUpload as CloudUploadIcon,
} from "@mui/icons-material";
import { useNavigate, useParams } from "react-router-dom";
import { useSelector, useDispatch } from "react-redux";
import { Formik, Form, FieldArray } from "formik";
import * as Yup from "yup";
import axios from "axios"; // Added for direct API calls (e.g., image upload, quote update)
import { DatePicker } from "@mui/x-date-pickers/DatePicker";
import { LocalizationProvider } from "@mui/x-date-pickers/LocalizationProvider";
import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFns";

import {
  getCustomers as fetchCustomersAction,
  deleteCustomerImage,
} from "../slices/customerSlice";
import { getJobs } from "../slices/jobSlice";
import { refreshMaterialPrice } from "../slices/materialSlice";
import {
  createQuote,
  clearDraftQuote,
  generateQuoteAI,
  getQuoteById,
  updateWorkingQuoteData,
  answerAIQuestions as answerAIQuestionsAction, // Renamed for clarity
  clearAIState as clearGlobalAIState, // Added to clear global AI state on unmount
  setDraftQuote, // Added to preserve form state during AI generation
} from "../slices/quoteSlice";
import { useSnackbar } from "notistack";
import CreateCustomerModal from "../components/customers/CreateCustomerModal";
import { formatCurrency } from "../utils/formatters";
import logger from "../utils/logger";
import ReactMarkdown from "react-markdown";
import remarkGfm from "remark-gfm";
import MaterialSearch from "../components/materials/MaterialSearch"; // For adding materials
import MaterialOptionSelectionDialog from "../components/materials/MaterialOptionSelectionDialog"; // For material option selection
import MaterialPriceStatusDisplay from "../components/materials/MaterialPriceStatusDisplay"; // For material price status monitoring
import QuotePreview from "./QuotePreview"; // For modal preview
import AiClarificationDialog from "../components/ai/AiClarificationDialog"; // For AI clarification prompts
import StreamingProgressIndicator from "../components/ai/StreamingProgressIndicator"; // For streaming progress display
import PriceLookupProgressIndicator from "../components/ai/PriceLookupProgressIndicator"; // For immediate price lookup progress
import useStreamingAI from "../hooks/useStreamingAI"; // For real-time streaming AI generation
import usePriceLookup from "../hooks/usePriceLookup"; // For immediate price lookup during quote generation

// Mutex/Semaphore for critical form operations to prevent race conditions
class FormOperationMutex {
  constructor() {
    this.locked = false;
    this.queue = [];
  }

  async lock() {
    return new Promise((resolve) => {
      if (!this.locked) {
        this.locked = true;
        resolve();
      } else {
        this.queue.push(resolve);
      }
    });
  }

  unlock() {
    if (this.queue.length > 0) {
      const next = this.queue.shift();
      next();
    } else {
      this.locked = false;
    }
  }

  isLocked() {
    return this.locked;
  }
}

// 🔧 ENHANCED DEBOUNCE: Memory leak prevention with proper cleanup
const useDebounce = (callback, delay) => {
  const timeoutRef = useRef(null);
  const isMountedRef = useRef(true);

  const debouncedCallback = useCallback(
    (...args) => {
      if (!isMountedRef.current) return; // Prevent execution after unmount

      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
      timeoutRef.current = setTimeout(() => {
        if (isMountedRef.current && callback) {
          // Double-check mount status
          try {
            callback(...args);
          } catch (error) {
            console.error("[Debounce] Error in callback:", error);
          }
        }
      }, delay);
    },
    [callback, delay]
  );

  // Enhanced cleanup function with mount tracking
  const cancel = useCallback(() => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
    }
  }, []);

  // Cleanup effect to prevent memory leaks
  useEffect(() => {
    isMountedRef.current = true;
    return () => {
      isMountedRef.current = false;
      cancel();
    };
  }, [cancel]);

  return { debouncedCallback, cancel };
};

const CreateQuoteFormik = () => {
  // Mutex instances for critical operations to prevent race conditions
  const formUpdateMutex = useRef(new FormOperationMutex()).current;
  const aiGenerationMutex = useRef(new FormOperationMutex()).current;

  // Debounced streaming updates to prevent excessive re-renders
  const streamingDebounce = useDebounce((data) => {
    if (!formikRef.current || !data) return;

    // Batch updates to prevent multiple re-renders
    unstable_batchedUpdates(() => {
      // Update form progressively as streaming data arrives
      if (data?.detailedAnalysis) {
        const analysis = data?.detailedAnalysis;

        // Update text fields progressively
        if (analysis.projectOverview) {
          formikRef.current.setFieldValue(
            "projectOverview",
            analysis.projectOverview
          );
          formikRef.current.setFieldTouched("projectOverview", true);
        }

        if (analysis.scopeOfWork) {
          formikRef.current.setFieldValue("scopeOfWork", analysis.scopeOfWork);
          formikRef.current.setFieldTouched("scopeOfWork", true);
        }

        if (analysis.materialsIncluded) {
          formikRef.current.setFieldValue(
            "materialsIncluded",
            analysis.materialsIncluded
          );
          formikRef.current.setFieldTouched("materialsIncluded", true);
        }

        // Update items progressively
        if (
          analysis.items &&
          Array.isArray(analysis.items) &&
          analysis.items.length > 0
        ) {
          const transformedItems = ensureCompleteItems(analysis.items);
          formikRef.current.setFieldValue("items", transformedItems);
          formikRef.current.setFieldTouched("items", true);
        }
      }

      // Update individual items with pricing as they come in
      if (data?.items && data?.items.length > 0) {
        const transformedItems = ensureCompleteItems(data?.items);
        formikRef.current.setFieldValue("items", transformedItems);
        formikRef.current.setFieldTouched("items", true);
      }
    });
  }, 300); // 300ms debounce delay

  const { id: quoteIdFromParams } = useParams(); // quoteIdFromParams can be undefined
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { enqueueSnackbar } = useSnackbar();
  const formikRef = useRef(null);

  const { userInfo } = useSelector((state) => state.auth);
  const { customers } = useSelector((state) => state.customers);
  const { jobs } = useSelector((state) => state.jobs);
  const {
    draftQuote, // For initialValues if no id

    aiLoading: globalAiLoading, // Global AI loading state from quoteSlice
    aiError: globalAiError, // Global AI error from quoteSlice
    aiStatus: globalAiStatus, // Global AI status
    aiQuestions: globalAiQuestions, // Global AI questions
  } = useSelector((state) => state.quotes);

  const [formInitialValues, setFormInitialValues] = useState(null);
  const [isFetchingInitialData, setIsFetchingInitialData] = useState(
    !!quoteIdFromParams
  );
  const [localError, setLocalError] = useState(null); // For form-specific errors not covered by Formik
  const [isAiGenerating, setIsAiGenerating] = useState(false); // Track AI generation state to prevent form resets
  const [initialValuesSet, setInitialValuesSet] = useState(false); // Track if initial values have been set

  // States migrated from CreateQuote.js or specific to new functionalities
  const [associatedQuoteImages, setAssociatedQuoteImages] = useState([]); // Images linked to an existing quote
  const [quoteImagesForUpload, setQuoteImagesForUpload] = useState([]); // New File objects selected for upload
  const [imagePreviews, setImagePreviews] = useState([]); // Previews for quoteImagesForUpload
  const [imageUploadLoading, setImageUploadLoading] = useState(false);
  const [isCreateCustomerModalOpen, setIsCreateCustomerModalOpen] =
    useState(false);
  const [isAiClarificationDialogOpen, setIsAiClarificationDialogOpen] =
    useState(false);
  const [aiAnswers, setAiAnswers] = useState({}); // For AI clarification panel
  const [isPreviewModalOpen, setIsPreviewModalOpen] = useState(false);
  const [modalPreviewData, setModalPreviewData] = useState(null);

  // 🚀 STREAMING AI INTEGRATION
  const {
    isStreaming,
    currentPhase,
    progress,
    statusMessage,
    streamingData,
    error: streamingError,
    startStreaming,
    stopStreaming, // eslint-disable-line no-unused-vars
    STREAMING_PHASES, // eslint-disable-line no-unused-vars
    // 🆕 PHASE 4: Streaming clarification support
    submitClarificationAnswers,
    isAwaitingClarification,
    clarificationQuestions,
  } = useStreamingAI();

  // Streaming preferences and controls
  const [useStreamingMode, setUseStreamingMode] = useState(true); // Default to streaming for better UX
  const [streamingProgressExpanded, setStreamingProgressExpanded] =
    useState(true);

  // 🚀 IMMEDIATE PRICE LOOKUP INTEGRATION
  const {
    isLookingUp: isPriceLookupActive,
    lookupProgress,
    lookupResults,
    lookupError,
    lookupPrices,
    cancelLookup,
    resetLookup,
    hasResults: hasPriceLookupResults,
    isComplete: isPriceLookupComplete,
    hasFailed: hasPriceLookupFailed,
    hasSucceeded: hasPriceLookupSucceeded,
  } = usePriceLookup();

  // Default structures
  const defaultItem = useMemo(
    () => ({
      name: "",
      description: "",
      sku: "",
      price: 0,
      quantity: 1,
      unit: "each",
      source: "",
      imageUrl: "",
      notes: "",
      taxRate: 0,
      _aiData: {},
      category: "unknown", // Add default category
      priceInfo: {
        source: "",
        lastUpdated: null,
        status: "pending_ai_search",
        lookupError: null,
        priceLookupTimestamp: null,
      },
      // Backend-expected fields
      lookup_results: [],
      material_options: [],
      selected_option: {},
      lookup_attempts: 0,
    }),
    []
  );
  const defaultLabor = useMemo(() => ({ hours: 0, rate: 50, notes: "" }), []);

  const ensureCompleteItems = useCallback(
    (items) => {
      if (!items || !Array.isArray(items) || items.length === 0)
        return [{ ...defaultItem, priceInfo: { ...defaultItem.priceInfo } }];
      return items.map((item) => ({
        ...defaultItem,
        ...item,
        // Preserve category field from backend
        category: item.category || "unknown",
        // Map backend fields to frontend structure
        _aiData: {
          raw_description:
            item.description_raw_ai || item._aiData?.raw_description || "",
          attributes: item.attributes || item._aiData?.attributes || {},
          lookup_query_suggestion:
            item.lookup_query_suggestion ||
            item._aiData?.lookup_query_suggestion ||
            "",
          category: item.category || "unknown", // Also store in _aiData for consistency
          ...(item._aiData || {}),
        },
        priceInfo: {
          source: item.source || item.priceInfo?.source || "",
          sourceId: item.sourceId || item.priceInfo?.sourceId || "",
          lastUpdated:
            item.last_lookup_attempt || item.priceInfo?.lastUpdated || null,
          status: item.lookup_status || item.priceInfo?.status || "idle",
          lookupError: item.priceInfo?.lookupError || null,
          priceLookupTimestamp: item.priceInfo?.priceLookupTimestamp || null,
          perUnitPrice: item.priceInfo?.perUnitPrice || item.price || 0,
        },
        // Ensure all backend fields are mapped
        lookup_results: item.lookup_results || [],
        material_options: item.material_options || [],
        selected_option: item.selected_option || {},
        lookup_attempts: item.lookup_attempts || 0,
      }));
    },
    [defaultItem]
  );

  // Effect to fetch existing quote data when editing (quoteIdFromParams exists)
  useEffect(() => {
    // CRITICAL FIX: Prevent useEffect from running during AI generation to avoid form resets
    if (isAiGenerating) {
      console.log(
        "[useEffect:fetchQuote] Skipping form reinitialization during AI generation"
      );
      return;
    }

    // 🔒 MUTEX PROTECTION: Prevent concurrent form initialization
    if (formUpdateMutex.isLocked()) {
      console.log("[useEffect:fetchQuote] Form update mutex locked, skipping");
      return;
    }

    if (quoteIdFromParams) {
      setIsFetchingInitialData(true);
      dispatch(getQuoteById(quoteIdFromParams))
        .unwrap()
        .then((fetchedQuote) => {
          // Batch the state updates to prevent multiple re-renders
          unstable_batchedUpdates(() => {
            setFormInitialValues({
              id: fetchedQuote._id,
              name: fetchedQuote.name || "",
              description: fetchedQuote.description || "",
              projectOverview: fetchedQuote.projectOverview || "",
              scopeOfWork: fetchedQuote.scopeOfWork || "",
              materialsIncluded: fetchedQuote.materialsIncluded || "", // from CreateQuote
              customer: fetchedQuote.customer || null,
              job: fetchedQuote.job || null,
              validUntil: fetchedQuote.validUntil
                ? new Date(fetchedQuote.validUntil).toISOString().split("T")[0]
                : new Date(Date.now() + 14 * 24 * 60 * 60 * 1000)
                    .toISOString()
                    .split("T")[0],
              items: ensureCompleteItems(fetchedQuote.items),
              labor: { ...defaultLabor, ...(fetchedQuote.labor || {}) },
              includeLaborCosts: !!fetchedQuote.labor, // from CreateQuote
              taxRate: fetchedQuote.summary?.taxRate ?? 8.5,
              status: fetchedQuote.status || "DRAFT",
              // AI related fields - map backend to frontend structure
              ai_confidence_score: fetchedQuote.ai_confidence_score || null,
              aiGenerationStatus: fetchedQuote.aiGenerationStatus || "idle",
              aiClarificationQuestions:
                fetchedQuote.aiClarificationQuestions || [],
              aiUserInputAnswers: fetchedQuote.aiUserInputAnswers || {},
              aiGenerationInputType: fetchedQuote.aiGenerationInputType || null,
              _aiData: fetchedQuote.aiOriginalInputData || null,
            });
            setAssociatedQuoteImages(fetchedQuote.associatedImages || []);
            // If customers/jobs lists don't contain the selected one, add it
            if (
              fetchedQuote.customer &&
              !customers.find((c) => c._id === fetchedQuote.customer._id)
            ) {
              dispatch({
                type: "customers/addCustomerToList",
                payload: fetchedQuote.customer,
              });
            }
            if (
              fetchedQuote.job &&
              !jobs.find((j) => j._id === fetchedQuote.job._id)
            ) {
              dispatch({
                type: "jobs/addJobToList",
                payload: fetchedQuote.job,
              });
            }
            setInitialValuesSet(true); // Mark that initial values have been set
          });
        })
        .catch((err) => {
          console.error("Failed to fetch quote:", err);
          enqueueSnackbar("Failed to load quote data.", { variant: "error" });
          setLocalError("Failed to load quote data.");
          navigate("/quotes"); // Navigate away if quote loading fails
        })
        .finally(() => setIsFetchingInitialData(false));
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    quoteIdFromParams,
    dispatch,
    enqueueSnackbar,
    navigate,
    customers,
    jobs,
    defaultLabor,
    isAiGenerating,
    formUpdateMutex,
  ]); // 🚨 REMOVED ensureCompleteItems to prevent circular dependency

  // Separate effect to initialize form with draftQuote data when creating new quotes
  useEffect(() => {
    // CRITICAL FIX: Only set initial values once to prevent resetting form dirty state
    if (initialValuesSet) {
      console.log(
        "[useEffect:initializeDraft] Initial values already set, skipping to preserve form dirty state"
      );
      return;
    }

    // CRITICAL FIX: Prevent useEffect from running during AI generation to avoid form resets
    if (isAiGenerating) {
      console.log(
        "[useEffect:initializeDraft] Skipping form reinitialization during AI generation"
      );
      return;
    }

    // 🔒 MUTEX PROTECTION: Prevent concurrent form initialization
    if (formUpdateMutex.isLocked()) {
      console.log(
        "[useEffect:initializeDraft] Form update mutex locked, skipping"
      );
      return;
    }

    // Only initialize from draftQuote when NOT editing an existing quote
    if (!quoteIdFromParams) {
      const base = draftQuote || {};
      // Batch the state updates to prevent multiple re-renders
      unstable_batchedUpdates(() => {
        setFormInitialValues({
          id: undefined,
          name: base.name || "",
          description: base.description || "",
          projectOverview: base.projectOverview || "",
          scopeOfWork: base.scopeOfWork || "",
          materialsIncluded: base.materialsIncluded || "",
          customer: base.customer || null,
          job: base.job || null,
          validUntil:
            base.validUntil ||
            new Date(Date.now() + 14 * 24 * 60 * 60 * 1000)
              .toISOString()
              .split("T")[0],
          items: ensureCompleteItems(base.items),
          labor: { ...defaultLabor, ...(base.labor || {}) },
          includeLaborCosts: !!base.labor,
          taxRate: base.taxRate ?? 8.5,
          status: base.status || "DRAFT",
          // AI related fields - ensure they exist for new quotes
          ai_confidence_score: base.ai_confidence_score || null,
          aiGenerationStatus: base.aiGenerationStatus || "idle",
          aiClarificationQuestions: base.aiClarificationQuestions || [],
          aiUserInputAnswers: base.aiUserInputAnswers || {},
          aiGenerationInputType: base.aiGenerationInputType || null,
          _aiData: base.aiOriginalInputData || null,
        });
        setAssociatedQuoteImages([]); // No associated images for a new quote
        setIsFetchingInitialData(false);
        setInitialValuesSet(true); // Mark that initial values have been set
      });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    quoteIdFromParams,
    defaultLabor,
    isAiGenerating,
    initialValuesSet,
    formUpdateMutex,
  ]); // 🚨 REMOVED ensureCompleteItems - INTENTIONALLY excluding draftQuote to prevent re-initialization

  // 🔄 COMPONENT INITIALIZATION: Fetch required data and setup cleanup
  useEffect(() => {
    const initializeComponent = async () => {
      try {
        // Fetch required data in parallel for better performance
        await Promise.all([
          dispatch(fetchCustomersAction({ page: 1, limit: 100 })),
          dispatch(getJobs({ page: 1, limit: 100 })),
        ]);
        console.log("[CreateQuoteFormik] Component initialization completed");
      } catch (error) {
        console.error("[CreateQuoteFormik] Initialization error:", error);
        enqueueSnackbar(
          "Failed to load required data. Some features may not work correctly.",
          {
            variant: "warning",
          }
        );
      }
    };

    initializeComponent();
  }, [dispatch, enqueueSnackbar]);
  useEffect(() => {
    // Cleanup function to prevent memory leaks on unmount
    return () => {
      console.log("[CreateQuoteFormik] Component cleanup initiated");

      // Cancel any pending debounced operations
      if (streamingDebounce?.cancel) {
        streamingDebounce.cancel();
      }

      // Revoke any remaining image preview URLs to prevent memory leaks
      imagePreviews.forEach((url) => {
        if (url && url.startsWith("blob:")) {
          try {
            URL.revokeObjectURL(url);
          } catch (error) {
            console.debug("[Cleanup] URL already revoked:", url);
          }
        }
      });

      // Clear global AI state to prevent state persistence issues
      dispatch(clearGlobalAIState());

      // Cancel any ongoing price lookup operations
      if (cancelLookup) {
        cancelLookup();
      }
      if (resetLookup) {
        resetLookup();
      }

      console.log("[CreateQuoteFormik] Component cleanup completed");
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [imagePreviews, dispatch, cancelLookup, resetLookup]); // 🚨 REMOVED streamingDebounce to break circular dependency, added cleanup dependencies

  // 🔧 CRITICAL FIX: Sync AI-generated content from Redux to form
  // This effect specifically handles AI completion and updates form fields with generated content
  useEffect(() => {
    // Only sync when we have a formik reference and draftQuote with AI-generated content
    if (!formikRef.current || !draftQuote || isAiGenerating) {
      return;
    }

    // 🔒 MUTEX PROTECTION: Prevent concurrent AI sync operations
    if (aiGenerationMutex.isLocked()) {
      console.log("[AI SYNC] AI generation mutex locked, skipping sync");
      return;
    }

    // Check if draftQuote contains AI-generated content that needs to be synced
    const hasAiGeneratedItems =
      draftQuote.items &&
      Array.isArray(draftQuote.items) &&
      draftQuote.items.length > 0;
    const hasAiGeneratedContent =
      draftQuote.projectOverview ||
      draftQuote.scopeOfWork ||
      draftQuote.materialsIncluded;
    const isAiComplete =
      draftQuote.aiGenerationStatus === "complete" ||
      draftQuote.aiGenerationStatus === "complete_needs_review";

    if (hasAiGeneratedItems || hasAiGeneratedContent || isAiComplete) {
      console.log("[AI SYNC] Syncing AI-generated content to form:", {
        hasItems: hasAiGeneratedItems,
        itemCount: draftQuote.items?.length || 0,
        hasContent: hasAiGeneratedContent,
        aiStatus: draftQuote.aiGenerationStatus,
      });

      // Batch all AI sync updates to prevent multiple re-renders
      unstable_batchedUpdates(() => {
        // Sync AI-generated fields while preserving user input
        const currentValues = formikRef.current.values;

        // Update AI-generated content fields
        if (
          draftQuote.projectOverview &&
          draftQuote.projectOverview !== currentValues.projectOverview
        ) {
          console.log("[AI SYNC] Updating projectOverview");
          formikRef.current.setFieldValue(
            "projectOverview",
            draftQuote.projectOverview
          );
        }

        if (
          draftQuote.scopeOfWork &&
          draftQuote.scopeOfWork !== currentValues.scopeOfWork
        ) {
          console.log("[AI SYNC] Updating scopeOfWork");
          formikRef.current.setFieldValue(
            "scopeOfWork",
            draftQuote.scopeOfWork
          );
        }

        if (
          draftQuote.materialsIncluded &&
          draftQuote.materialsIncluded !== currentValues.materialsIncluded
        ) {
          console.log("[AI SYNC] Updating materialsIncluded");
          formikRef.current.setFieldValue(
            "materialsIncluded",
            draftQuote.materialsIncluded
          );
        }

        // 🔥 CRITICAL: Update items with AI-generated content
        if (hasAiGeneratedItems) {
          const transformedItems = ensureCompleteItems(draftQuote.items);
          console.log("[AI SYNC] Updating items:", {
            originalCount: draftQuote.items.length,
            transformedCount: transformedItems.length,
            firstItem: transformedItems[0]
              ? {
                  name: transformedItems[0].name,
                  description:
                    transformedItems[0].description?.substring(0, 50) + "...",
                  price: transformedItems[0].price,
                  quantity: transformedItems[0].quantity,
                }
              : null,
          });

          formikRef.current.setFieldValue("items", transformedItems);

          // Mark form as touched to enable save button
          formikRef.current.setFieldTouched("items", true);
        }

        // Update AI confidence score if present
        if (draftQuote.ai_confidence_score !== undefined) {
          formikRef.current.setFieldValue(
            "ai_confidence_score",
            draftQuote.ai_confidence_score
          );
        }

        console.log("[AI SYNC] ✅ Successfully synced AI content to form");
      });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [draftQuote, isAiGenerating, aiGenerationMutex]); // 🚨 REMOVED ensureCompleteItems to prevent circular dependency

  // 🆕 PHASE 4: Monitor streaming clarification state
  useEffect(() => {
    if (isAwaitingClarification && clarificationQuestions?.length > 0) {
      console.log(
        "🆕 [STREAMING CLARIFICATION] Opening dialog for streaming AI clarification:",
        {
          isStreaming,
          isAwaitingClarification,
          questionsCount: clarificationQuestions?.length,
        }
      );
      setIsAiClarificationDialogOpen(true);
    } else if (
      isAwaitingClarification &&
      (!clarificationQuestions || clarificationQuestions.length === 0)
    ) {
      console.warn(
        "🆕 [STREAMING CLARIFICATION] Clarification required but no questions available:",
        {
          isAwaitingClarification,
          clarificationQuestions,
        }
      );
      // Don't open dialog if there are no questions
    }
  }, [isAwaitingClarification, isStreaming, clarificationQuestions]);

  // 📡 TRADITIONAL AI: Monitor traditional AI clarification (aiQuestions from Redux)
  useEffect(() => {
    if (globalAiQuestions?.length > 0 && !isStreaming) {
      console.log(
        "📡 [TRADITIONAL CLARIFICATION] Opening dialog for traditional AI clarification:",
        {
          questionsCount: globalAiQuestions.length,
          isStreaming,
        }
      );
      setIsAiClarificationDialogOpen(true);
    }
  }, [globalAiQuestions, isStreaming]);

  // 🔧 ENHANCED FORM VALIDATION with better error messages
  const validationSchema = Yup.object({
    name: Yup.string()
      .trim()
      .required("Quote name is required")
      .min(3, "Quote name must be at least 3 characters")
      .max(200, "Quote name cannot exceed 200 characters"),
    customer: Yup.object()
      .nullable()
      .required("Customer is required for quote generation"),
    job: Yup.object().nullable(), // Job is optional
    validUntil: Yup.date()
      .required("Valid until date is required")
      .min(new Date(), "Valid until date must be in the future"),
    items: Yup.array()
      .of(
        Yup.object().shape({
          name: Yup.string()
            .trim()
            .required("Item name is required")
            .min(2, "Item name must be at least 2 characters"),
          quantity: Yup.number()
            .required("Quantity is required")
            .positive("Quantity must be positive")
            .integer("Quantity must be a whole number")
            .max(10000, "Quantity cannot exceed 10,000"),
          price: Yup.number()
            .required("Price is required")
            .min(0, "Price cannot be negative")
            .max(1000000, "Price cannot exceed $1,000,000"),
          unit: Yup.string()
            .trim()
            .required("Unit is required")
            .min(1, "Unit cannot be empty"),
        })
      )
      .min(1, "At least one item is required for the quote"),
    labor: Yup.object().shape({
      hours: Yup.number()
        .min(0, "Hours cannot be negative")
        .max(10000, "Hours cannot exceed 10,000"),
      rate: Yup.number()
        .min(0, "Rate cannot be negative")
        .max(1000, "Rate cannot exceed $1,000/hour"),
    }),
    taxRate: Yup.number()
      .min(0, "Tax rate cannot be negative")
      .max(100, "Tax rate cannot exceed 100%"),
    projectOverview: Yup.string().max(
      5000,
      "Project overview cannot exceed 5,000 characters"
    ),
    scopeOfWork: Yup.string().max(
      10000,
      "Scope of work cannot exceed 10,000 characters"
    ),
    materialsIncluded: Yup.string().max(
      5000,
      "Materials summary cannot exceed 5,000 characters"
    ),
  });

  // Image Handling Functions (adapted from CreateQuote.js)
  const handleQuoteImageUpload = useCallback(async (filesToUpload, targetQuoteId) => {
    if (!targetQuoteId) {
      enqueueSnackbar("Quote ID is missing. Cannot upload images.", {
        variant: "error",
      });
      return;
    }
    if (!filesToUpload || filesToUpload.length === 0) return;

    setImageUploadLoading(true);
    const formData = new FormData();
    filesToUpload.forEach((fileObj) => {
      // Assuming filesToUpload can be array of File or {file: File}
      const file = fileObj.file || fileObj; // Handle both structures
      formData.append("quoteImages", file);
    });

    try {
      const config = {
        headers: {
          "Content-Type": "multipart/form-data",
          Authorization: `Bearer ${userInfo.token}`,
        },
      };
      const { data } = await axios.post(
        `/api/quotes/${targetQuoteId}/images`,
        formData,
        config
      );

      if (data.success && data.images) {
        setAssociatedQuoteImages((prev) => [...prev, ...data.images]);
        enqueueSnackbar(
          `${data.images.length} image(s) uploaded successfully!`,
          { variant: "success" }
        );
        if (targetQuoteId !== quoteIdFromParams) {
          // If uploading to a newly created quote
          setQuoteImagesForUpload([]); // Clear the pending uploads
          imagePreviews.forEach(URL.revokeObjectURL); // Revoke old previews
          setImagePreviews([]);
        }
      } else {
        enqueueSnackbar(
          data.message || "Image upload failed. Please try again.",
          { variant: "error" }
        );
      }
    } catch (err) {
      console.error("Error uploading quote images:", err);
      const errorMsg = err.response?.data?.message || "Image upload failed.";
      enqueueSnackbar(errorMsg, { variant: "error" });
    } finally {
      setImageUploadLoading(false);
    }
  }, [enqueueSnackbar, setAssociatedQuoteImages, setImageUploadLoading, userInfo.token, quoteIdFromParams, setQuoteImagesForUpload, imagePreviews, setImagePreviews]);

  const handleImageSelection = useCallback(
    (event) => {
      try {
        const files = Array.from(event.target.files || []);
        if (files.length === 0) {
          console.log("[Image Upload] No files selected");
          return;
        }

        console.log(`[Image Upload] Processing ${files.length} selected files`);

        const validFiles = files.filter((file) => {
          if (!file.type.startsWith("image/")) {
            enqueueSnackbar(`File ${file.name} is not a valid image type.`, {
              variant: "warning",
            });
            return false;
          }
          if (file.size > 10 * 1024 * 1024) {
            // 10MB limit
            enqueueSnackbar(`File ${file.name} exceeds the 10MB size limit.`, {
              variant: "warning",
            });
            return false;
          }
          return true;
        });

        if (validFiles.length > 0) {
          if (quoteIdFromParams) {
            // Editing existing quote
            console.log(
              `[Image Upload] Uploading ${validFiles.length} files to existing quote ${quoteIdFromParams}`
            );
            handleQuoteImageUpload(validFiles, quoteIdFromParams);
          } else {
            // Creating new quote
            console.log(
              `[Image Upload] Preparing ${validFiles.length} files for upload on save`
            );
            const newUploads = validFiles.map((file) => ({
              file,
              name: file.name,
              size: file.size,
              preview: URL.createObjectURL(file),
            }));
            setQuoteImagesForUpload((prev) => [...prev, ...newUploads]);
            setImagePreviews((prev) => [
              ...prev,
              ...newUploads.map((f) => f.preview),
            ]);
            enqueueSnackbar(
              `${validFiles.length} image(s) selected. They will be uploaded when you save the quote.`,
              { variant: "info" }
            );
          }
        }
      } catch (error) {
        console.error("[Image Upload] Error processing file selection:", error);
        enqueueSnackbar("Error processing selected files. Please try again.", {
          variant: "error",
        });
      } finally {
        // Always reset the input value to ensure the same file can be selected again
        if (event.target) {
          event.target.value = "";
        }
      }
    },
    [
      quoteIdFromParams,
      enqueueSnackbar,
      setQuoteImagesForUpload,
      setImagePreviews,
      handleQuoteImageUpload,
    ]
  );

  const handleDeleteAssociatedQuoteImage = async (imageIdToDelete) => {
    // This uses deleteCustomerImage as per CreateQuote.js.
    // A dedicated quote image deletion endpoint might be better.
    if (!quoteIdFromParams || !imageIdToDelete) {
      enqueueSnackbar("Cannot delete image: Missing quote or image ID.", {
        variant: "error",
      });
      return;
    }
    if (
      !window.confirm(
        "Are you sure you want to delete this image? This cannot be undone."
      )
    )
      return;

    try {
      // Assuming a similar structure to deleteCustomerImage or a new action
      // For now, using a placeholder for the actual dispatch if it differs.
      // This needs to be wired to a backend endpoint that disassociates/deletes the image from the quote.
      // Example: await dispatch(deleteQuoteImage({ quoteId: quoteIdFromParams, imageId: imageIdToDelete })).unwrap();
      // Using deleteCustomerImage as a placeholder based on CreateQuote.js, this is likely incorrect for quote-specific images.
      // This part needs review based on backend capabilities for quote-specific image deletion.
      // For now, let's simulate removal from frontend:
      await dispatch(
        deleteCustomerImage({
          customerId: formInitialValues?.customer?._id,
          imageId: imageIdToDelete,
        })
      ).unwrap(); // This is likely wrong for quote images
      setAssociatedQuoteImages((prev) =>
        prev.filter((img) => img._id !== imageIdToDelete)
      );
      enqueueSnackbar(
        "Image disassociated (simulated). Backend integration needed for actual deletion.",
        { variant: "info" }
      );
    } catch (err) {
      console.error("Error deleting associated image:", err);
      enqueueSnackbar(err.message || "Failed to delete image.", {
        variant: "error",
      });
    }
  };

  // 🔧 ENHANCED IMAGE CLEANUP: Prevent memory leaks with proper URL management
  const removeQuoteImageForUpload = useCallback(
    (indexToRemove) => {
      try {
        const itemToRemove = quoteImagesForUpload[indexToRemove];
        if (!itemToRemove) {
          console.warn(
            "[Image Cleanup] Item not found at index:",
            indexToRemove
          );
          return;
        }

        // Properly revoke the preview URL to prevent memory leaks
        if (itemToRemove.preview) {
          URL.revokeObjectURL(itemToRemove.preview);
          console.log("[Image Cleanup] Revoked URL for:", itemToRemove.name);
        }

        // Update state with remaining items
        const newUploads = quoteImagesForUpload.filter(
          (_, index) => index !== indexToRemove
        );
        setQuoteImagesForUpload(newUploads);

        // Update previews array accordingly
        const newPreviews = newUploads.map((f) => f.preview);
        setImagePreviews(newPreviews);

        enqueueSnackbar(`Removed ${itemToRemove.name}`, { variant: "info" });
      } catch (error) {
        console.error("[Image Cleanup] Error removing image:", error);
        enqueueSnackbar("Error removing image. Please try again.", {
          variant: "error",
        });
      }
    },
    [quoteImagesForUpload, enqueueSnackbar]
  );

  // Main form submission
  const handleSubmit = async (values, { resetForm, setSubmitting }) => {
    // 🔒 MUTEX PROTECTION: Prevent concurrent form submissions
    if (formUpdateMutex.isLocked()) {
      console.log(
        "[Form Submit] Form update mutex locked, skipping submission"
      );
      enqueueSnackbar("Form is currently being saved. Please wait.", {
        variant: "warning",
      });
      setSubmitting(false);
      return;
    }

    await formUpdateMutex.lock();
    console.log("[Form Submit] Starting form submission");

    setLocalError(null);
    const isEditing = !!quoteIdFromParams;

    // Construct quotePayload from form values
    const quotePayload = {
      ...values,
      customer: values.customer?._id || values.customer, // Send only ID if object
      job: values.job?._id || values.job, // Send only ID if object
      // Map items to match backend schema
      items: values.items.map((item) => ({
        name: item.name,
        description: item.description || "",
        description_raw_ai: item._aiData?.raw_description || "",
        sku: item.sku || "",
        currency: item.currency || "USD",
        price: item.price || 0,
        quantity: item.quantity || 1,
        unit: item.unit || "each",
        source: item.priceInfo?.source || item.source || "",
        sourceId: item.priceInfo?.sourceId || item.sourceId || null,
        imageUrl: item.imageUrl || "",
        url: item.url || "",
        notes: item.notes || "",
        attributes: item._aiData?.attributes || {},
        lookup_query_suggestion: item._aiData?.lookup_query_suggestion || "",
        lookup_status: item.priceInfo?.status || "pending_ai_search",
        lookup_results: item.lookup_results || [],
        material_options: item.material_options || [],
        selected_option: item.selected_option || {},
        last_lookup_attempt: item.priceInfo?.lastUpdated
          ? new Date(item.priceInfo.lastUpdated)
          : null,
        lookup_attempts: item.lookup_attempts || 0,
      })),
      // AI Generation Fields - map frontend to backend structure
      aiClarificationQuestions: values.aiClarificationQuestions || [],
      aiUserInputAnswers: values.aiUserInputAnswers || {},
      aiGenerationInputType: values.aiGenerationInputType || null,
      aiGenerationStatus: values.aiGenerationStatus || "idle",
      aiOriginalInputData: values._aiData || null,
    };

    let savedQuoteData = null; // Declare savedQuoteData
    try {
      if (isEditing) {
        const response = await axios.put(
          `/api/quotes/${quoteIdFromParams}`,
          quotePayload,
          {
            headers: { Authorization: `Bearer ${userInfo.token}` },
          }
        );
        savedQuoteData = response.data.data;
        enqueueSnackbar("Quote updated successfully!", { variant: "success" });
      } else {
        const resultAction = await dispatch(createQuote(quotePayload)).unwrap();
        savedQuoteData = resultAction; // Assuming createQuote thunk returns the created quote
        enqueueSnackbar("Quote created successfully!", { variant: "success" });
        // Upload images for new quote
        if (savedQuoteData?._id && quoteImagesForUpload.length > 0) {
          await handleQuoteImageUpload(
            quoteImagesForUpload.map((qifu) => qifu.file),
            savedQuoteData._id
          );
        }
      }
      dispatch(clearDraftQuote());
      if (!isEditing) resetForm({ values: formInitialValues }); // Reset only for new quotes
      navigate("/quotes");
    } catch (err) {
      console.error(`Error ${isEditing ? "updating" : "creating"} quote:`, err);
      setLocalError(
        err.response?.data?.message ||
          err.message ||
          `Failed to ${isEditing ? "update" : "create"} quote.`
      );
      enqueueSnackbar(localError, { variant: "error" });
    } finally {
      // 🔒 MUTEX UNLOCK: Always release the mutex
      formUpdateMutex.unlock();
      setSubmitting(false);
    }
  };

  // 🚀 ENHANCED: Function to fetch prices immediately using proper price lookup service
  const fetchPricesForItems = async (items, formikSetFieldValue, options = {}) => {
    if (!items || items.length === 0) return;

    // Filter items that need price lookup based on options
    const itemsNeedingLookup = items.filter(item => {
      // If force refresh is requested, lookup all electrical items
      if (options.forceRefresh) {
        return item.source !== 'Labor' && item.source !== 'Administrative' && item.source !== 'Non-Electrical';
      }

      // Otherwise, only lookup items without valid prices
      const hasPrice = item.price && item.price > 0;
      const hasValidStatus = item.priceInfo?.status === "found" || item.priceInfo?.status === "existing_price";
      return !hasPrice || !hasValidStatus;
    });

    if (itemsNeedingLookup.length === 0) {
      logger.info("[Price Lookup] All items already have prices, skipping lookup");
      enqueueSnackbar("All items already have current pricing", { variant: "info" });
      return;
    }

    logger.info(
      `[Price Lookup] Starting immediate price lookup for ${itemsNeedingLookup.length} items using enhanced lookup service`
    );

    try {
      // Use the proper usePriceLookup hook for immediate price fetching
      const result = await lookupPrices(itemsNeedingLookup, { timeout: 60000 });

      if (result.success && result.items) {
        logger.info(`[Price Lookup] Processing ${result.items.length} updated items from API`);

        // Debug: Log the structure of items we're trying to match
        logger.debug(`[Price Lookup] Original items:`, items.map((item, idx) => ({
          index: idx,
          name: item.name,
          sku: item.sku,
          price: item.price,
          hasPrice: !!(item.price && item.price > 0)
        })));

        logger.debug(`[Price Lookup] Updated items from API:`, result.items.map((item, idx) => ({
          index: idx,
          name: item.name,
          sku: item.sku,
          price: item.price,
          priceInfoStatus: item.priceInfo?.status
        })));

        // Create a map of updated items by their index/name for efficient lookup
        const updatedItemsMap = new Map();
        result.items.forEach((updatedItem, apiIndex) => {
          // Try multiple matching strategies
          let originalIndex = -1;

          // Strategy 1: Match by name (most reliable for AI-generated items)
          if (updatedItem.name) {
            originalIndex = items.findIndex(item =>
              item.name && item.name.trim().toLowerCase() === updatedItem.name.trim().toLowerCase()
            );
          }

          // Strategy 2: Match by SKU if name match failed
          if (originalIndex === -1 && updatedItem.sku) {
            originalIndex = items.findIndex(item =>
              item.sku && item.sku === updatedItem.sku
            );
          }

          // Strategy 3: Match by index position (fallback for items sent in same order)
          if (originalIndex === -1 && apiIndex < items.length) {
            const potentialMatch = items[apiIndex];
            if (potentialMatch && (!potentialMatch.price || potentialMatch.price <= 0)) {
              originalIndex = apiIndex;
              logger.debug(`[Price Lookup] Using index-based matching for item ${apiIndex}: "${updatedItem.name}"`);
            }
          }

          if (originalIndex !== -1) {
            updatedItemsMap.set(originalIndex, updatedItem);
            logger.debug(`[Price Lookup] Matched API item "${updatedItem.name}" (price: ${updatedItem.price}) to original item ${originalIndex}`);
          } else {
            logger.warn(`[Price Lookup] Could not match API item "${updatedItem.name}" to any original item`);
          }
        });

        // Update the original items array with the lookup results
        const finalItems = items.map((item, index) => {
          if (updatedItemsMap.has(index)) {
            const updatedItem = updatedItemsMap.get(index);
            const mergedItem = {
              ...item, // Preserve original item structure
              price: updatedItem.price || item.price, // Explicitly set price
              sku: updatedItem.sku || item.sku,
              source: updatedItem.source || item.source,
              sourceId: updatedItem.sourceId || item.sourceId,
              url: updatedItem.url || item.url,
              imageUrl: updatedItem.imageUrl || item.imageUrl,
              priceInfo: {
                ...item.priceInfo, // Preserve existing priceInfo structure
                ...updatedItem.priceInfo, // Apply updated price lookup results
              }
            };
            logger.debug(`[Price Lookup] Updated item ${index} "${item.name}" with price: ${mergedItem.price}`);
            return mergedItem;
          }
          return item; // Return unchanged if no update
        });

        // Update form with the enhanced items
        formikSetFieldValue("items", finalItems);

        // Debug: Log final items to verify prices were set
        logger.info(`[Price Lookup] Final items after update:`, finalItems.map((item, idx) => ({
          index: idx,
          name: item.name,
          price: item.price,
          priceInfoStatus: item.priceInfo?.status
        })));

        logger.info(
          `[Price Lookup] ✅ Price lookup completed successfully:`,
          {
            total: result.summary.total,
            found: result.summary.pricesFound,
            failed: result.summary.pricesFailed,
            averageTime: result.summary.averageResponseTime,
          }
        );
      }
    } catch (error) {
      logger.error("[Price Lookup] ❌ Price lookup failed:", error);
      enqueueSnackbar(
        `Price lookup failed: ${error.message || "Unknown error"}`,
        { variant: "error" }
      );
    }
  };

  // 🚀 NEW: Manual price lookup function for user-triggered lookups
  const handleManualPriceLookup = async (formikValues, formikSetFieldValue, options = {}) => {
    if (!formikValues.items || formikValues.items.length === 0) {
      enqueueSnackbar("No items to lookup prices for", { variant: "info" });
      return;
    }

    try {
      await fetchPricesForItems(formikValues.items, formikSetFieldValue, {
        forceRefresh: options.forceRefresh || false
      });
    } catch (error) {
      // Error handling is already done in fetchPricesForItems
      logger.error("[Manual Price Lookup] Error:", error);
    }
  };

  // 🔄 PROGRESSIVE FORM UPDATES from streaming data (ENHANCED with validation)
  useEffect(() => {
    if (!formikRef.current || !streamingData) return;

    // Validate streaming data structure before processing
    try {
      if (typeof streamingData === "object" && streamingData !== null) {
        // Use debounced updates to prevent excessive re-renders and race conditions
        streamingDebounce.debouncedCallback(streamingData);
      } else {
        console.warn(
          "[Streaming Updates] Invalid streaming data format:",
          typeof streamingData
        );
      }
    } catch (error) {
      console.error(
        "[Streaming Updates] Error processing streaming data:",
        error
      );
    }

    // Cleanup function to cancel pending debounced calls
    return () => {
      streamingDebounce.cancel();
    };
  }, [streamingData, streamingDebounce]); // ✅ All dependencies declared

  // Handle streaming completion
  useEffect(() => {
    if (
      currentPhase === STREAMING_PHASES.COMPLETED &&
      streamingData?.finalResults
    ) {
      console.log(
        "[STREAMING COMPLETE] Processing final results:",
        streamingData?.finalResults
      );

      // Final form update with complete data
      if (formikRef.current && streamingData?.finalResults?.items) {
        const finalItems = ensureCompleteItems(
          streamingData?.finalResults?.items
        );
        formikRef.current.setFieldValue("items", finalItems);
        formikRef.current.setFieldTouched("items", true);
      }

      enqueueSnackbar(
        `🎉 Quote generated successfully! ${
          streamingData?.finalResults?.items?.length || 0
        } items found.`,
        { variant: "success", autoHideDuration: 4000 }
      );
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    currentPhase,
    streamingData?.finalResults,
    STREAMING_PHASES.COMPLETED,
    enqueueSnackbar,
  ]); // 🚨 REMOVED ensureCompleteItems to prevent circular dependency

  // 🚨 ENHANCED STREAMING ERROR HANDLING with user-friendly messages
  useEffect(() => {
    if (streamingError) {
      // Parse and categorize errors for better user experience
      let userMessage = "AI generation encountered an issue. Please try again.";
      let severity = "warning";

      if (typeof streamingError === "string") {
        if (streamingError.includes("session has expired")) {
          userMessage =
            "Your session has expired. Please refresh the page and try again.";
          severity = "error";
        } else if (
          streamingError.includes("network") ||
          streamingError.includes("connection")
        ) {
          userMessage =
            "Network connection issue. Please check your internet and try again.";
          severity = "warning";
        } else if (
          streamingError.includes("permission") ||
          streamingError.includes("unauthorized")
        ) {
          userMessage =
            "Access denied. Please ensure you have permission to use AI generation.";
          severity = "error";
        } else {
          userMessage = `AI generation issue: ${streamingError}. Switching to standard mode.`;
        }
      }

      console.log(
        "[STREAMING INFO] Graceful fallback to traditional mode:",
        streamingError
      );
      enqueueSnackbar(userMessage, {
        variant: severity,
        autoHideDuration: severity === "error" ? 8000 : 6000,
      });
    }
  }, [streamingError, enqueueSnackbar]); // ✅ All dependencies declared

  // AI Interaction Handlers (Enhanced with Streaming Support and Mutex Protection)
  const handleAIGenerateRequest = async (
    inputType,
    formikSetFieldValue,
    currentFormValues
  ) => {
    // 🔒 IMMEDIATE PROTECTION: Prevent duplicate clicks by checking both mutex and generating state
    if (aiGenerationMutex.isLocked() || isAiGenerating || isStreaming) {
      console.log(
        "[AI Generation] Operation already in progress, blocking duplicate request:",
        {
          mutexLocked: aiGenerationMutex.isLocked(),
          isAiGenerating,
          isStreaming,
        }
      );
      enqueueSnackbar(
        "AI generation already in progress. Please wait for completion.",
        { variant: "warning" }
      );
      return;
    }

    // 🚨 IMMEDIATE STATE UPDATE: Set generating state BEFORE async operations to prevent race conditions
    setIsAiGenerating(true);
    console.log(
      `[AI Generation] Starting AI generation request for type: ${inputType}`
    );

    // Lock mutex after immediate state update
    await aiGenerationMutex.lock();

    try {
      // 🔧 ENHANCED VALIDATION: Validate form context before proceeding
      if (!formikSetFieldValue || typeof formikSetFieldValue !== "function") {
        throw new Error(
          "Invalid form context: setFieldValue function not available"
        );
      }

      if (!currentFormValues || typeof currentFormValues !== "object") {
        throw new Error(
          "Invalid form context: current form values not available"
        );
      }
      // CRITICAL FIX: Capture complete current form state before AI generation
      const currentFormState = {
        name: currentFormValues.name || "",
        customer: currentFormValues.customer || null,
        job: currentFormValues.job || null,
        validUntil: currentFormValues.validUntil || null,
        taxRate:
          currentFormValues.taxRate !== undefined
            ? currentFormValues.taxRate
            : 8.5,
        status: currentFormValues.status || "DRAFT",
        labor: currentFormValues.labor || {},
        includeLaborCosts:
          currentFormValues.includeLaborCosts !== undefined
            ? currentFormValues.includeLaborCosts
            : false,
        // Preserve any existing AI-related data
        ai_confidence_score: currentFormValues.ai_confidence_score || null,
        aiGenerationStatus: currentFormValues.aiGenerationStatus || "idle",
        aiClarificationQuestions:
          currentFormValues.aiClarificationQuestions || [],
        aiUserInputAnswers: currentFormValues.aiUserInputAnswers || {},
        aiGenerationInputType: currentFormValues.aiGenerationInputType || null,
        _aiData: currentFormValues._aiData || null,
      };

      console.log(
        "[AI Generation] Captured current form state:",
        currentFormState
      );

      // Update draft quote with current form state BEFORE AI generation
      dispatch(setDraftQuote({ ...draftQuote, ...currentFormState }));

      // 🔧 ENHANCED INPUT VALIDATION with detailed checks
      if (!inputType || typeof inputType !== "string") {
        throw new Error("Input type must be a valid string");
      }

      if (inputType !== "materials" && inputType !== "overview") {
        console.error(`[AI Generation] Invalid input type: ${inputType}`);
        throw new Error(
          `Invalid AI generation type: ${inputType}. Expected 'materials' or 'overview'.`
        );
      }

      // Default text data
      let textInputData = {
        customerDescription: currentFormValues.description || "",
        jobType:
          typeof currentFormValues.serviceType === "object"
            ? currentFormValues.serviceType.name || ""
            : "",
        // other context fields like address, customer details, etc.
      };

      // 🔧 ENHANCED INPUT VALIDATION for materials type
      if (inputType === "materials") {
        console.log(
          `[AI Generation] Processing materials input type. Items count: ${
            currentFormValues.items?.length || 0
          }, Materials included text length: ${
            currentFormValues.materialsIncluded?.length || 0
          }`
        );

        const hasItems =
          currentFormValues.items && currentFormValues.items.length > 0;
        const hasMaterialsText =
          currentFormValues.materialsIncluded &&
          currentFormValues.materialsIncluded.trim();

        if (!hasItems && !hasMaterialsText) {
          console.warn(
            "[AI Generation] Cannot proceed: Material list or summary is empty"
          );
          throw new Error(
            "Please provide either a materials list or a materials summary to generate from."
          );
        }

        // Check if we have valid items first
        let validItems = [];
        if (hasItems) {
          validItems = currentFormValues.items.filter(
            (item) =>
              item &&
              (item.name || item.description) &&
              ((item.name && item.name.trim()) ||
                (item.description && item.description.trim()))
          );
        }

        // Prioritize materials text if no valid items exist
        if (validItems.length > 0) {
          // Use structured items data
          textInputData = validItems.map((item) => ({
            name: (item.name || "").trim(),
            sku: (item.sku || "").trim(),
            quantity: item.quantity || 1,
            description: (item.description || "").trim(),
          }));
        } else if (hasMaterialsText) {
          // Use materials text from textbox
          textInputData = hasMaterialsText.trim();
        } else {
          // No valid data available
          throw new Error(
            "Please ensure your materials list contains items with valid names."
          );
        }

        console.log(
          `[AI Generation] Prepared textInputData for materials. Type: ${typeof textInputData}`,
          Array.isArray(textInputData)
            ? `Array length: ${textInputData.length}`
            : `String length: ${textInputData.length}`
        );
      } else if (inputType === "overview") {
        // 🔧 ENHANCED INPUT VALIDATION for overview type
        const projectOverview = currentFormValues.projectOverview;
        if (
          !projectOverview ||
          typeof projectOverview !== "string" ||
          !projectOverview.trim()
        ) {
          console.warn(
            "[AI Generation] Cannot proceed: Project overview is empty or invalid"
          );
          throw new Error(
            "Please provide a project overview with sufficient detail for AI generation."
          );
        }

        const trimmedOverview = projectOverview.trim();
        if (trimmedOverview.length < 10) {
          throw new Error(
            "Project overview must be at least 10 characters long to generate meaningful results."
          );
        }

        if (trimmedOverview.length > 5000) {
          console.warn(
            "[AI Generation] Project overview is very long, truncating to 5000 characters"
          );
          textInputData = trimmedOverview.substring(0, 5000) + "...";
        } else {
          textInputData = trimmedOverview;
        }

        console.log(
          `[AI Generation] Prepared textInputData for overview. Length: ${textInputData.length}`
        );
      } else {
        throw new Error(`Unsupported input type: ${inputType}`);
      }

      console.log(
        "[AI Generation] Setting up form context and preparing request data"
      );
      // formikRef.current already contains the Formik instance - DO NOT OVERWRITE

      // Create FormData to properly handle file uploads
      const formData = new FormData();

      // Add text data with proper structure
      formData.append("inputType", inputType);
      const serializedInputData =
        typeof textInputData === "object"
          ? JSON.stringify(textInputData)
          : textInputData;
      formData.append("inputData", serializedInputData);
      console.log(
        `[AI Generation] Added inputData to FormData. Type: ${typeof textInputData}, Serialized length: ${
          serializedInputData.length
        }`
      );

      if (quoteIdFromParams) {
        formData.append("quoteId", quoteIdFromParams);
        console.log(
          `[AI Generation] Added quoteId to FormData: ${quoteIdFromParams}`
        );
      }

      // Add quote images for AI analysis
      if (quoteImagesForUpload && quoteImagesForUpload.length > 0) {
        console.log(
          `[AI Generation] Adding ${quoteImagesForUpload.length} new images to FormData`
        );
        quoteImagesForUpload.forEach((imgData, idx) => {
          formData.append("contextImages", imgData.file, imgData.file.name);
          console.log(
            `[AI Generation] Added image ${idx + 1}/${
              quoteImagesForUpload.length
            }: ${imgData.file.name} (${imgData.file.size} bytes)`
          );
        });
      } else {
        console.log("[AI Generation] No new images to upload");
      }

      // Also add any associated images that are already stored
      if (associatedQuoteImages && associatedQuoteImages.length > 0) {
        console.log(
          `[AI Generation] Adding ${associatedQuoteImages.length} existing image references to FormData`
        );
        // Add image references to help backend locate already stored images
        formData.append(
          "associatedImageIds",
          JSON.stringify(associatedQuoteImages.map((img) => img._id))
        );
      } else {
        console.log("[AI Generation] No existing images to reference");
      }

      // 🚀 STREAMING MODE: Use real-time streaming if enabled
      if (useStreamingMode) {
        console.log(
          "[AI Generation] Using STREAMING mode for real-time updates"
        );

        // 🔧 ENHANCED STREAMING PAYLOAD with comprehensive validation
        const streamingPayload = {
          inputType: formData.get("inputType"),
          inputData: formData.get("inputData"),
          projectOverview: (currentFormValues.projectOverview || "").substring(
            0,
            5000
          ), // Limit size
          description: (currentFormValues.description || "").substring(0, 2000), // Limit size
          serviceType: currentFormValues.serviceType || "",
          // Add quote context with validation
          quoteId: formData.get("quoteId") || null,
          // Add image references (files need separate handling)
          associatedImageIds: (() => {
            try {
              const imageIds = formData.get("associatedImageIds");
              return imageIds ? JSON.parse(imageIds) : [];
            } catch (error) {
              console.warn(
                "[AI Generation] Invalid image IDs format, using empty array"
              );
              return [];
            }
          })(),
          // Add user context with validation
          userId: userInfo?.id || null,
          companyId: userInfo?.companyId || null,
          // Add metadata for better processing
          timestamp: new Date().toISOString(),
          clientVersion: process.env.REACT_APP_VERSION || "1.0.0",
        };

        console.log(
          "[AI Generation] Starting streaming with validated payload:",
          {
            fieldCount: Object.keys(streamingPayload).length,
            hasValidInputType: !!streamingPayload.inputType,
            hasValidInputData: !!streamingPayload.inputData,
            hasUserId: !!streamingPayload.userId,
            hasCompanyId: !!streamingPayload.companyId,
            payloadSize: JSON.stringify(streamingPayload).length,
            timestamp: new Date().toISOString(),
          }
        );

        // Start streaming AI generation
        await startStreaming(streamingPayload, {
          onUpdate: (data) => {
            console.log("[STREAMING UPDATE]", data);
            // Updates are handled by useEffect hooks above
          },
          onComplete: (data) => {
            console.log("[STREAMING COMPLETE]", data);
            // Completion is handled by useEffect hooks above
          },
          // 🆕 PHASE 4: Streaming clarification callbacks
          onClarificationRequired: (clarificationData) => {
            console.log(
              "🆕 [STREAMING CLARIFICATION] Clarification required callback triggered:",
              clarificationData
            );
            // The useEffect hooks above will handle opening the dialog
            // This callback is just for logging and additional processing if needed
          },
          onClarificationComplete: () => {
            console.log(
              "🆕 [STREAMING CLARIFICATION] Clarification complete callback triggered"
            );
            // Dialog will be closed by useEffect monitoring
          },
          onError: (error) => {
            console.log(
              "[STREAMING INFO] Graceful fallback to traditional mode due to:",
              error.message || error
            );

            // 🔧 ENHANCED ERROR CATEGORIZATION with specific handling
            let fallbackMessage =
              "Streaming failed, falling back to traditional method...";
            let shouldFallback = true;
            let errorCategory = "generic";

            // Parse error message for better user feedback
            if (error.message) {
              const errorMsg = error.message.toLowerCase();

              if (
                errorMsg.includes("session has expired") ||
                errorMsg.includes("log in again")
              ) {
                fallbackMessage =
                  "Your session has expired. Please refresh the page and log in again.";
                shouldFallback = false;
                errorCategory = "authentication";
              } else if (
                errorMsg.includes("format error") ||
                errorMsg.includes("upload") ||
                errorMsg.includes("formdata")
              ) {
                fallbackMessage =
                  "File upload error. Please try uploading your files again or use traditional mode.";
                shouldFallback = true;
                errorCategory = "upload";
              } else if (
                errorMsg.includes("permission") ||
                errorMsg.includes("access denied") ||
                errorMsg.includes("unauthorized")
              ) {
                fallbackMessage =
                  "You do not have permission to use AI generation. Please contact support.";
                shouldFallback = false;
                errorCategory = "permission";
              } else if (
                errorMsg.includes("rate limit") ||
                errorMsg.includes("too many requests")
              ) {
                fallbackMessage =
                  "Too many requests. Please wait a moment before trying again.";
                shouldFallback = false;
                errorCategory = "rate_limit";
              } else if (
                errorMsg.includes("connect") ||
                errorMsg.includes("network") ||
                errorMsg.includes("timeout")
              ) {
                fallbackMessage =
                  "Network connection error. Please check your internet connection and try again.";
                shouldFallback = true;
                errorCategory = "network";
              } else if (
                errorMsg.includes("service unavailable") ||
                errorMsg.includes("backend")
              ) {
                fallbackMessage =
                  "AI service is temporarily unavailable. Using traditional mode instead.";
                shouldFallback = true;
                errorCategory = "service";
              }
            }

            // Log error for monitoring
            console.log("[Streaming Error]", {
              category: errorCategory,
              shouldFallback,
              originalError: error.message || error,
              userMessage: fallbackMessage,
              timestamp: new Date().toISOString(),
            });

            enqueueSnackbar(fallbackMessage, {
              variant: shouldFallback ? "warning" : "error",
              autoHideDuration: shouldFallback ? 4000 : 6000,
            });

            if (shouldFallback && errorCategory !== "rate_limit") {
              setUseStreamingMode(false);
              // Retry with traditional method after delay
              setTimeout(() => {
                console.log(
                  "[Streaming Fallback] Retrying with traditional method"
                );
                handleTraditionalAIGeneration(
                  formData,
                  formikSetFieldValue,
                  currentFormValues
                );
              }, 1000); // Small delay before fallback
            }
          },
        });

        return; // Exit early for streaming mode
      }

      // 📡 TRADITIONAL MODE: Use existing Redux action
      console.log("[AI Generation] Using TRADITIONAL mode (Redux action)");
      await handleTraditionalAIGeneration(
        formData,
        formikSetFieldValue,
        currentFormValues
      );
    } catch (error) {
      logger.error("[AI Generation] AI generation setup failed:", error);
      enqueueSnackbar(
        error.message || "AI generation failed. Please try again.",
        { variant: "error" }
      );
    } finally {
      // 🔒 MUTEX UNLOCK: Always release the mutex
      aiGenerationMutex.unlock();
      // Clear AI generating state
      setIsAiGenerating(false);
    }
  };

  /**
   * Traditional AI generation using Redux action (fallback method)
   * @param {FormData} formData - The form data to send to AI
   * @param {Function} formikSetFieldValue - Formik's setFieldValue function
   * @param {Object} currentFormValues - Current form values
   */
  const handleTraditionalAIGeneration = async (
    formData,
    formikSetFieldValue,
    currentFormValues
  ) => {
    try {
      console.log(
        "[Traditional AI] Starting traditional AI generation via Redux action"
      );

      // Dispatch the Redux action for AI generation
      const resultAction = await dispatch(generateQuoteAI(formData)).unwrap();

      console.log("[Traditional AI] Redux action completed:", resultAction);

      // Handle the response based on status
      if (
        resultAction.status === "pending_questions" &&
        resultAction.questions
      ) {
        // AI needs clarification - show dialog
        console.log(
          "[Traditional AI] AI requires clarification, showing dialog"
        );
        setIsAiClarificationDialogOpen(true);
        enqueueSnackbar(
          "AI needs clarification. Please answer the questions.",
          { variant: "info" }
        );
      } else if (
        resultAction.status === "complete" &&
        resultAction.generatedData
      ) {
        // AI generation complete - update form with generated data
        console.log("[Traditional AI] AI generation complete, updating form");

        // Update form fields with AI-generated content
        if (resultAction.generatedData.projectOverview) {
          formikSetFieldValue(
            "projectOverview",
            resultAction.generatedData.projectOverview
          );
        }
        if (resultAction.generatedData.scopeOfWork) {
          formikSetFieldValue(
            "scopeOfWork",
            resultAction.generatedData.scopeOfWork
          );
        }
        if (resultAction.generatedData.materialsIncluded) {
          formikSetFieldValue(
            "materialsIncluded",
            resultAction.generatedData.materialsIncluded
          );
        }

        // Update items if provided
        if (
          Array.isArray(resultAction.generatedData.items) &&
          resultAction.generatedData.items.length > 0
        ) {
          const itemsWithPriceInfo = resultAction.generatedData.items.map(
            (item) => ({
              ...item,
              priceInfo: {
                ...defaultItem.priceInfo,
                source: "AI_GENERATED",
                status:
                  item.price !== undefined && item.price !== null
                    ? "found"
                    : "idle",
                lastUpdated:
                  item.price !== undefined && item.price !== null
                    ? new Date().toISOString()
                    : null,
              },
            })
          );

          const finalItems = ensureCompleteItems(itemsWithPriceInfo);
          formikSetFieldValue("items", finalItems);

          // Trigger price lookup for AI-generated items
          setTimeout(() => {
            fetchPricesForItems(finalItems, formikSetFieldValue);
          }, 100);
        }

        enqueueSnackbar("AI generation completed successfully!", {
          variant: "success",
        });
      } else {
        // Unexpected response format
        console.warn(
          "[Traditional AI] Unexpected response format:",
          resultAction
        );
        enqueueSnackbar("AI generation completed but with unexpected format.", {
          variant: "warning",
        });
      }
    } catch (error) {
      console.error(
        "[Traditional AI] Traditional AI generation failed:",
        error
      );
      enqueueSnackbar(
        error.message || "Traditional AI generation failed. Please try again.",
        { variant: "error" }
      );
      throw error; // Re-throw to allow caller to handle
    }
  };

  const handleAISubmitAnswers = async (
    formikSetFieldValue,
    currentFormValues
  ) => {
    console.group(
      "🚀 [PHASE 3] AI Clarification Answer Submission - Enhanced Monitoring"
    );
    console.log("📊 [PHASE 3] handleAISubmitAnswers called with:", {
      hasFormikSetFieldValue: typeof formikSetFieldValue === "function",
      hasCurrentFormValues: !!currentFormValues,
      currentFormValuesKeys: currentFormValues
        ? Object.keys(currentFormValues)
        : [],
      timestamp: new Date().toISOString(),
    });

    // Determine target quote ID
    const targetId = quoteIdFromParams || draftQuote?.quoteId;
    console.log("🎯 [PHASE 3] Target quote ID determination:", {
      quoteIdFromParams,
      draftQuoteId: draftQuote?.quoteId,
      targetId,
      source: quoteIdFromParams
        ? "URL params"
        : draftQuote?.quoteId
        ? "draft quote"
        : "none",
    });

    if (!targetId) {
      console.error(
        "[AI Clarification] ❌ No quote ID available for AI answers submission"
      );
      console.error("[AI Clarification] Context:", {
        quoteIdFromParams,
        draftQuote: draftQuote
          ? { id: draftQuote.quoteId, keys: Object.keys(draftQuote) }
          : null,
      });
      enqueueSnackbar(
        "Please save the quote first or ensure AI initiated from a saved quote.",
        { variant: "warning" }
      );
      console.groupEnd();
      return;
    }

    // Validation: ensure aiAnswers has answers for all questions
    console.log("[AI Clarification] Validating AI answers:", {
      aiAnswers,
      answersCount: aiAnswers ? Object.keys(aiAnswers).length : 0,
      hasAnswers: !!(aiAnswers && Object.keys(aiAnswers).length > 0),
    });

    if (!aiAnswers || Object.keys(aiAnswers).length === 0) {
      console.error("[AI Clarification] ❌ No AI answers provided");
      enqueueSnackbar("Please answer all AI questions.", {
        variant: "warning",
      });
      console.groupEnd();
      return;
    }

    console.log(
      "[AI Clarification] ✅ Validation passed, submitting answers:",
      aiAnswers
    );

    // Set AI generating state to prevent form resets
    console.log("[AI Clarification] Setting AI generating state to true");
    setIsAiGenerating(true);

    try {
      console.log("[AI Clarification] 🚀 Dispatching answerAIQuestionsAction");
      console.log("[AI Clarification] Redux action parameters:", {
        quoteId: targetId,
        answers: aiAnswers,
      });

      const resultAction = await dispatch(
        answerAIQuestionsAction({ quoteId: targetId, answers: aiAnswers })
      ).unwrap();

      console.log(
        "[AI Clarification] ✅ Received result from Redux:",
        resultAction
      );
      console.log("[AI Clarification] Result analysis:", {
        hasResult: !!resultAction,
        status: resultAction?.status,
        hasGeneratedData: !!resultAction?.generatedData,
        generatedDataKeys: resultAction?.generatedData
          ? Object.keys(resultAction.generatedData)
          : [],
      });

      if (resultAction.status === "complete" && resultAction.generatedData) {
        console.group("[AI Clarification] 📝 Processing Generated Content");

        // FIXED: Preserve existing form data while updating AI-generated fields
        // Only update AI-generated fields, preserving customer, name, images, etc.
        const updates = {
          projectOverview:
            resultAction.generatedData.projectOverview ||
            currentFormValues.projectOverview,
          scopeOfWork:
            resultAction.generatedData.scopeOfWork ||
            currentFormValues.scopeOfWork,
          materialsIncluded:
            resultAction.generatedData.materialsIncluded ||
            currentFormValues.materialsIncluded,
        };

        console.log("[AI Clarification] Form field updates:", updates);

        formikSetFieldValue("projectOverview", updates.projectOverview);
        formikSetFieldValue("scopeOfWork", updates.scopeOfWork);
        formikSetFieldValue("materialsIncluded", updates.materialsIncluded);

        if (Array.isArray(resultAction.generatedData.items)) {
          console.log("[AI Clarification] Processing items:", {
            itemCount: resultAction.generatedData.items.length,
            items: resultAction.generatedData.items.map((item) => ({
              name: item.name,
              price: item.price,
            })),
          });

          const itemsWithPriceInfo = resultAction.generatedData.items.map(
            (item) => ({
              ...item,
              priceInfo: {
                ...defaultItem.priceInfo, // Start with defaults
                source: "AI_GENERATED", // Mark source as AI
                status:
                  item.price !== undefined && item.price !== null
                    ? "found"
                    : "idle", // If AI gives a price, mark as found
                lastUpdated:
                  item.price !== undefined && item.price !== null
                    ? new Date().toISOString()
                    : null,
              },
            })
          );

          const finalItems = ensureCompleteItems(itemsWithPriceInfo);
          console.log(
            "[AI Clarification] Final items with price info:",
            finalItems.length
          );
          formikSetFieldValue("items", finalItems);

          // 🚀 IMMEDIATE PRICE LOOKUP 🚀
          // Fetch prices immediately for AI-generated items
          console.log(
            "[AI Clarification] 🚀 Scheduling immediate price lookup"
          );
          setTimeout(() => {
            fetchPricesForItems(finalItems, formikSetFieldValue);
          }, 100); // Small delay to ensure form is updated first
        }

        console.log(
          "[AI Answers] 🔒 PRESERVED: Customer, quote name, and images remain unchanged"
        );
        enqueueSnackbar("AI generation complete after clarification!", {
          variant: "success",
        });
        setAiAnswers({}); // Clear answers

        // Mark form as touched to enable save button after AI clarification
        if (formikRef.current) {
          console.log("[AI Clarification] Marking form fields as touched");
          formikRef.current.setTouched({
            items: true,
            projectOverview: true,
            scopeOfWork: true,
            materialsIncluded: true,
          });

          // Trigger validation to update form validity and wait for it to complete
          formikRef.current.validateForm().then((errors) => {
            console.log("[AI Clarification] Form validation completed:", {
              errors: Object.keys(errors),
              dirty: formikRef.current.dirty,
              isValid: formikRef.current.isValid,
            });

            // If there are no errors, the form should be valid and the save button enabled
            if (Object.keys(errors).length === 0) {
              console.log(
                "[AI Clarification] ✅ Form is valid, save button should be enabled"
              );
            } else {
              console.warn(
                "[AI Clarification] ⚠️ Form has validation errors:",
                errors
              );
            }
          });
        }

        console.groupEnd();
      } else {
        console.warn("[AI Clarification] ⚠️ Unexpected result format:", {
          status: resultAction?.status,
          hasGeneratedData: !!resultAction?.generatedData,
          resultAction,
        });
        enqueueSnackbar(
          "AI generation completed but returned unexpected format. Please check the results.",
          { variant: "warning" }
        );
      }
    } catch (err) {
      console.groupCollapsed(
        "[AI Clarification] ❌ Failed to submit AI answers"
      );
      console.error("[AI Clarification] Error details:", {
        message: err?.message,
        name: err?.name,
        status: err?.response?.status,
        data: err?.response?.data,
        stack: err?.stack?.substring(0, 500),
      });

      let errorMessage = `Failed to submit AI answers: ${err.message || err}`;

      // Provide more specific error messages based on error type
      if (err.message?.includes("Invalid quote ID")) {
        errorMessage =
          "Invalid quote ID. Please save the quote first before answering AI questions.";
        console.error("[AI Clarification] Invalid quote ID detected");
      } else if (err.message?.includes("not found")) {
        errorMessage =
          "Quote not found. Please refresh the page and try again.";
        console.error("[AI Clarification] Quote not found detected");
      } else if (
        err.message?.includes("network") ||
        err.message?.includes("timeout")
      ) {
        errorMessage =
          "Network error occurred while submitting answers. Please try again.";
        console.error("[AI Clarification] Network error detected");
      }

      enqueueSnackbar(errorMessage, { variant: "error" });
      console.groupEnd();
    } finally {
      console.log("[AI Clarification] 🏁 Clearing AI generating state");
      setIsAiGenerating(false); // Always clear AI generating state
      console.groupEnd();
    }
  };

  // Customer Creation Modal handler
  const handleCustomerCreated = (newCustomer) => {
    if (newCustomer && newCustomer._id) {
      dispatch({ type: "customers/addCustomerToList", payload: newCustomer }); // Add to local Redux list
      if (formikRef.current) {
        formikRef.current.setFieldValue("customer", newCustomer);
      } else {
        console.error("Formik ref is not available in handleCustomerCreated");
        enqueueSnackbar("Could not update customer in form.", {
          variant: "error",
        });
      }
      enqueueSnackbar("New customer created and selected.", {
        variant: "success",
      });
    }
    setIsCreateCustomerModalOpen(false);
  };

  // CRITICAL FIX: Function to update draft quote with current form values
  const updateDraftWithCurrentValues = useCallback(
    (currentFormValues) => {
      if (!isAiGenerating) {
        // Only update draft when not generating AI to avoid conflicts
        const updatedDraft = {
          ...draftQuote,
          ...currentFormValues,
          // Ensure critical fields are preserved
          name: currentFormValues.name || "",
          customer: currentFormValues.customer || null,
          job: currentFormValues.job || null,
          validUntil: currentFormValues.validUntil || null,
          taxRate:
            currentFormValues.taxRate !== undefined
              ? currentFormValues.taxRate
              : 8.5,
          status: currentFormValues.status || "DRAFT",
          labor: currentFormValues.labor || {},
          includeLaborCosts:
            currentFormValues.includeLaborCosts !== undefined
              ? currentFormValues.includeLaborCosts
              : false,
        };
        dispatch(setDraftQuote(updatedDraft));
        console.log(
          "[Form Update] Draft quote updated with current form values"
        );
      }
    },
    [draftQuote, dispatch, isAiGenerating]
  );

  // Preview Handler
  // Preview Handler - Updated for modal
  const handlePreview = (currentFormValues) => {
    const previewData = {
      ...currentFormValues,
      // Ensure customer and job are objects with _id for preview if they exist
      customer: currentFormValues.customer
        ? {
            _id: currentFormValues.customer._id,
            name:
              currentFormValues.customer.name ||
              currentFormValues.customer.businessName,
          }
        : null,
      job: currentFormValues.job
        ? {
            _id: currentFormValues.job._id,
            title:
              currentFormValues.job.title || currentFormValues.job.description,
          }
        : null,
      // Calculate totals for preview
      summary: calculateTotals(
        currentFormValues.items,
        currentFormValues.labor,
        currentFormValues.taxRate,
        currentFormValues.includeLaborCosts
      ),
      // FIXED: Include both associated images and pending upload images for complete preview
      associatedImages: associatedQuoteImages, // Pass current associated images
      pendingImages: quoteImagesForUpload, // Pass images waiting to be uploaded
      // Pass any other relevant data needed by QuotePreview
      ...currentFormValues, // Spread all current form values to ensure QuotePreview gets everything it needs
    };
    dispatch(updateWorkingQuoteData(previewData));
    setModalPreviewData(previewData);
    setIsPreviewModalOpen(true);
  };

  const calculateTotals = (
    items = [],
    labor = {},
    taxRate = 0,
    includeLabor = false
  ) => {
    const materialsTotal = items.reduce(
      (sum, item) => sum + (item.price || 0) * (item.quantity || 0),
      0
    );
    const laborTotal = includeLabor
      ? (labor.hours || 0) * (labor.rate || 0)
      : 0;
    const subtotal = materialsTotal + laborTotal;
    const taxAmount = (subtotal * (taxRate || 0)) / 100;
    const grandTotal = subtotal + taxAmount;
    return { materialsTotal, laborTotal, subtotal, taxAmount, grandTotal };
  };

  if (isFetchingInitialData || !formInitialValues) {
    return (
      <Container sx={{ display: "flex", justifyContent: "center", mt: 5 }}>
        <CircularProgress />
      </Container>
    );
  }

  return (
    <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
      <Paper sx={{ p: 3 }}>
        <Typography variant="h5" gutterBottom>
          {quoteIdFromParams ? "Edit Quote" : "Create Quote"}
        </Typography>
        {localError && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {localError}
          </Alert>
        )}
        <Formik
          innerRef={formikRef}
          initialValues={formInitialValues}
          validationSchema={validationSchema}
          onSubmit={handleSubmit}
          enableReinitialize={!initialValuesSet} // Only reinitialize if initial values haven't been set yet
        >
          {(formikProps) => (
            <QuoteFormFields
              formikProps={formikProps}
              quoteId={quoteIdFromParams}
              customers={customers || []} // Ensure customers is always an array
              jobs={jobs || []} // Ensure jobs is always an array
              globalAiLoading={globalAiLoading}
              globalAiError={globalAiError}
              globalAiStatus={globalAiStatus}
              globalAiQuestions={globalAiQuestions}
              aiAnswers={aiAnswers}
              setAiAnswers={setAiAnswers}
              handleAIGenerateRequest={handleAIGenerateRequest}
              handleAISubmitAnswers={handleAISubmitAnswers}
              associatedQuoteImages={associatedQuoteImages}
              quoteImagesForUpload={quoteImagesForUpload}
              imagePreviews={imagePreviews}
              handleImageSelection={handleImageSelection}
              removeQuoteImageForUpload={removeQuoteImageForUpload}
              handleDeleteAssociatedQuoteImage={
                handleDeleteAssociatedQuoteImage
              }
              imageUploadLoading={imageUploadLoading}
              isCreateCustomerModalOpen={isCreateCustomerModalOpen}
              setIsCreateCustomerModalOpen={setIsCreateCustomerModalOpen}
              handleCustomerCreated={handleCustomerCreated}
              handlePreview={handlePreview}
              defaultItem={defaultItem}
              ensureCompleteItems={ensureCompleteItems}
              calculateTotals={calculateTotals}
              enqueueSnackbar={enqueueSnackbar}
              updateDraftWithCurrentValues={updateDraftWithCurrentValues} // ADDED: For preserving form state
              // AI Generation state
              isAiGenerating={isAiGenerating}
              // Streaming AI props
              isStreaming={isStreaming}
              currentPhase={currentPhase}
              progress={progress}
              statusMessage={statusMessage}
              streamingData={streamingData}
              streamingError={streamingError}
              useStreamingMode={useStreamingMode}
              setUseStreamingMode={setUseStreamingMode}
              streamingProgressExpanded={streamingProgressExpanded}
              setStreamingProgressExpanded={setStreamingProgressExpanded}
              // 🚀 Price lookup props
              isPriceLookupActive={isPriceLookupActive}
              lookupProgress={lookupProgress}
              lookupResults={lookupResults}
              lookupError={lookupError}
              hasPriceLookupResults={hasPriceLookupResults}
              isPriceLookupComplete={isPriceLookupComplete}
              hasPriceLookupFailed={hasPriceLookupFailed}
              hasPriceLookupSucceeded={hasPriceLookupSucceeded}
              cancelLookup={cancelLookup}
              resetLookup={resetLookup}
              handleManualPriceLookup={handleManualPriceLookup}
            />
          )}
        </Formik>
        {/* Modal for Quote Preview */}
        <Modal
          open={isPreviewModalOpen}
          onClose={() => setIsPreviewModalOpen(false)}
          aria-labelledby="quote-preview-modal-title"
          aria-describedby="quote-preview-modal-description"
          closeAfterTransition
          BackdropComponent={Backdrop}
          BackdropProps={{
            timeout: 500,
          }}
        >
          <Fade in={isPreviewModalOpen}>
            <Box
              sx={{
                position: "absolute",
                top: "50%",
                left: "50%",
                transform: "translate(-50%, -50%)",
                width: { xs: "95%", md: "80%", lg: "70%" }, // Responsive width
                maxHeight: "90vh",
                bgcolor: "background.paper",
                border: "1px solid #ddd",
                borderRadius: "8px",
                boxShadow: 24,
                p: { xs: 2, md: 3 }, // Responsive padding
                outline: "none", // Remove focus outline on modal
              }}
            >
              <Typography
                variant="h6"
                id="quote-preview-modal-title"
                sx={{ mb: 2, textAlign: "center" }}
              >
                Quote Preview
              </Typography>
              <Box
                sx={{
                  maxHeight: "calc(90vh - 130px)",
                  overflowY: "auto",
                  pr: 1 /* for scrollbar padding */,
                }}
              >
                {modalPreviewData && (
                  <QuotePreview
                    quoteData={modalPreviewData}
                    isModalView={true}
                  />
                )}
              </Box>
              <Box
                sx={{
                  display: "flex",
                  justifyContent: "flex-end",
                  mt: 2,
                  pt: 2,
                  borderTop: "1px solid #eee",
                }}
              >
                <Button
                  onClick={() => setIsPreviewModalOpen(false)}
                  variant="outlined"
                >
                  Close Preview
                </Button>
              </Box>
            </Box>
          </Fade>
        </Modal>
      </Paper>
      <CreateCustomerModal
        open={isCreateCustomerModalOpen}
        onClose={() => setIsCreateCustomerModalOpen(false)}
        onSuccess={(newCustomer) => handleCustomerCreated(newCustomer)}
      />
      <AiClarificationDialog
        open={isAiClarificationDialogOpen}
        onClose={() => {
          console.log("🚪 [CLARIFICATION DIALOG] Closing dialog");
          setIsAiClarificationDialogOpen(false);
        }}
        onSubmitAnswers={async (answers) => {
          console.log("🚀 [CLARIFICATION DIALOG] Submitting answers:", answers);

          if (formikRef.current) {
            if (isAwaitingClarification && submitClarificationAnswers) {
              // 🆕 STREAMING MODE: Use streaming clarification submission
              console.log(
                "🆕 [STREAMING CLARIFICATION] Submitting answers via streaming API"
              );
              try {
                await submitClarificationAnswers(answers);
                console.log(
                  "🆕 [STREAMING CLARIFICATION] ✅ Answers submitted successfully"
                );
                setIsAiClarificationDialogOpen(false);
              } catch (error) {
                console.error(
                  "🆕 [STREAMING CLARIFICATION] ❌ Failed to submit answers:",
                  error
                );
                enqueueSnackbar(
                  "Failed to submit clarification answers. Please try again.",
                  { variant: "error" }
                );
              }
            } else {
              // 📡 TRADITIONAL MODE: Use existing flow
              console.log(
                "📡 [TRADITIONAL CLARIFICATION] Using traditional clarification flow"
              );

              // Check if we're in draft mode (no quoteId) or traditional mode
              const hasSavedQuote = !!(
                quoteIdFromParams || draftQuote?.quoteId
              );

              if (hasSavedQuote) {
                // Traditional mode: use existing flow
                setAiAnswers(answers);
                await handleAISubmitAnswers(
                  formikRef.current.setFieldValue,
                  formikRef.current.values
                );
              } else {
                // Draft mode: directly apply enhanced data from backend
                console.log(
                  "📡 [DRAFT MODE] Applying enhanced data directly to form:",
                  answers
                );

                // Update form fields with enhanced data (if answers contain enhanced data)
                if (answers.projectOverview) {
                  formikRef.current.setFieldValue(
                    "projectOverview",
                    answers.projectOverview
                  );
                }
                if (answers.scopeOfWork) {
                  formikRef.current.setFieldValue(
                    "scopeOfWork",
                    answers.scopeOfWork
                  );
                }
                if (answers.materialsIncluded) {
                  formikRef.current.setFieldValue(
                    "materialsIncluded",
                    answers.materialsIncluded
                  );
                }
                if (answers.items && Array.isArray(answers.items)) {
                  const itemsWithPriceInfo = answers.items.map((item) => ({
                    ...item,
                    priceInfo: {
                      ...defaultItem.priceInfo,
                      source: "AI_GENERATED",
                      status:
                        item.price !== undefined && item.price !== null
                          ? "found"
                          : "idle",
                      lastUpdated:
                        item.price !== undefined && item.price !== null
                          ? new Date().toISOString()
                          : null,
                    },
                  }));

                  const finalItems = ensureCompleteItems(itemsWithPriceInfo);
                  formikRef.current.setFieldValue("items", finalItems);

                  // Schedule price lookup for new items
                  setTimeout(() => {
                    fetchPricesForItems(
                      finalItems,
                      formikRef.current.setFieldValue
                    );
                  }, 100);
                }

                // Mark form as touched to enable save button
                formikRef.current.setTouched({
                  items: true,
                  projectOverview: true,
                  scopeOfWork: true,
                  materialsIncluded: true,
                });

                enqueueSnackbar(
                  "AI clarification complete! Quote updated with enhanced content.",
                  { variant: "success" }
                );
              }

              setIsAiClarificationDialogOpen(false);
            }
          }
        }}
        currentFormValues={formikRef.current?.values || {}}
        quoteId={quoteIdFromParams || draftQuote?.quoteId || null}
        // 🆕 Dynamic mode detection: streaming if awaiting clarification, otherwise detect traditional vs draft
        mode={
          isAwaitingClarification
            ? "streaming"
            : quoteIdFromParams || draftQuote?.quoteId
            ? "traditional"
            : "draft"
        }
        // 🆕 Pass appropriate data based on mode
        draftQuoteData={
          isAwaitingClarification
            ? {
                inputType: "streaming_clarification",
                inputData: formikRef.current?.values || {},
                aiQuestions: clarificationQuestions || [], // Get questions from streaming hook
                formData: formikRef.current?.values || {},
              }
            : !quoteIdFromParams && !draftQuote?.quoteId
            ? {
                inputType: "form_data",
                inputData: formikRef.current?.values || {},
                aiQuestions: globalAiQuestions || [],
                formData: formikRef.current?.values || {},
              }
            : null
        }
      />
    </Container>
  );
};

// Sub-component for Formik fields to easily access context
const QuoteFormFields = ({
  formikProps,
  quoteId,
  customers,
  jobs,
  globalAiLoading,
  globalAiError,
  globalAiStatus,
  globalAiQuestions,
  aiAnswers,
  setAiAnswers,
  handleAIGenerateRequest,
  handleAISubmitAnswers,
  associatedQuoteImages,
  quoteImagesForUpload,
  imagePreviews,
  handleImageSelection,
  removeQuoteImageForUpload,
  handleDeleteAssociatedQuoteImage,
  imageUploadLoading,
  isCreateCustomerModalOpen,
  setIsCreateCustomerModalOpen,
  handleCustomerCreated, // Added modal state and handler
  handlePreview,
  defaultItem,
  ensureCompleteItems,
  calculateTotals,
  enqueueSnackbar,
  updateDraftWithCurrentValues, // ADDED: For preserving form state
  // AI Generation state
  isAiGenerating,
  // Streaming AI props
  isStreaming,
  currentPhase,
  progress,
  statusMessage,
  streamingData,
  streamingError,
  useStreamingMode,
  setUseStreamingMode,
  streamingProgressExpanded,
  setStreamingProgressExpanded,
  // 🚀 Price lookup props
  isPriceLookupActive,
  lookupProgress,
  lookupResults,
  lookupError,
  hasPriceLookupResults,
  isPriceLookupComplete,
  hasPriceLookupFailed,
  hasPriceLookupSucceeded,
  cancelLookup,
  resetLookup,
  handleManualPriceLookup,
}) => {
  const {
    values,
    errors,
    touched,
    handleChange,
    handleBlur,
    setFieldValue,
    isSubmitting,
    dirty,
    isValid,
  } = formikProps;
  const dispatch = useDispatch(); // For preview
  const navigate = useNavigate(); // Initialize navigate

  // Debug form state
  useEffect(() => {
    console.log("[QuoteFormFields] Form state:", {
      dirty,
      isValid,
      isSubmitting,
      errors: Object.keys(errors).length > 0 ? errors : "No errors",
      hasRequiredFields: {
        name: !!values.name,
        customer: !!values.customer,
        validUntil: !!values.validUntil,
        itemsCount: values.items?.length || 0,
      },
    });
  }, [
    dirty,
    isValid,
    isSubmitting,
    errors,
    values.name,
    values.customer,
    values.validUntil,
    values.items,
  ]);

  const [refreshingPriceIndex, setRefreshingPriceIndex] = useState(null);
  const [isBatchRefreshingPrices, setIsBatchRefreshingPrices] = useState(false);
  const [materialOptionDialogOpen, setMaterialOptionDialogOpen] =
    useState(false);
  const [selectedItemIndex, setSelectedItemIndex] = useState(null);
  const [selectedItemId, setSelectedItemId] = useState(null);
  const [showMaterialSearchComponent, setShowMaterialSearchComponent] =
    useState(false);

  //   const debouncedMaterialSearch = useMemo( // This line (861) is already commented
  // START OF BLOCK TO COMMENT OUT
  //   () => debounce(async (query) => {
  //     if (!query || query.length < 2) {

  //       // setIsSearchingMaterials(false); // Removed as isSearchingMaterials is unused
  //       return;
  //     }
  //     // setIsSearchingMaterials(true); // Removed as isSearchingMaterials is unused
  //     try {
  //       /* const results = */ await dispatch(searchMaterialsPricing({ query })).unwrap(); // results is unused
  //       // setMaterialSearchResults(results || []); // Removed as materialSearchResults is unused
  //     } catch (error) {
  //       enqueueSnackbar('Failed to search materials.', { variant: 'error' });
  //     } // This closes the catch block
  //   }, 500), // This brace closes the async (query) => {} function. Then 500, then the ) closes debounce. Then ,
  //   [dispatch, enqueueSnackbar] // This is the dependency array for useMemo. Error reported on this line (875).
  // ); // This ) closes the useMemo call.
  // END OF BLOCK TO COMMENT OUT

  const handleAddMaterialFromSearch = (material) => {
    // Log the complete material object to help with debugging
    console.debug("Adding material from search:", material);

    const description =
      material.description ||
      material.productDescription ||
      material.title ||
      material.name ||
      "";
    const dimensions = material.dimensions || "";
    let unit = material.unit || "each";
    if (material.packSize && parseInt(material.packSize) > 1) {
      unit = "pack";
    }

    const newItem = {
      ...defaultItem,
      name: material.name || material.title || material.productName || "",
      description: description,
      sku: material.sku || "",
      price: material.price || 0,
      unit: unit,
      dimensions: dimensions,
      source: material.source || "",
      imageUrl: material.imageUrl || "",
      brand: material.brand || "",
      modelNumber: material.modelNumber || "",
      packSize: material.packSize || "",
      url: material.url || material.productUrl || "",
      taxRate: values.taxRate || 0,
      priceInfo: {
        ...defaultItem.priceInfo,
        source: material.source || "Search",
        sourceId: material.sourceId || "",
        perUnitPrice: material.perUnitPrice || material.price || 0,
        status: material.price ? "found" : "idle",
        lastUpdated: material.price ? new Date().toISOString() : null,
      },
    };

    setFieldValue("items", [...values.items, newItem]);

    setShowMaterialSearchComponent(false); // Hide search component
  };

  const handleMaterialSelected = useCallback(
    (selectedOption) => {
      if (
        selectedItemIndex === null ||
        !values.items ||
        !values.items[selectedItemIndex]
      )
        return;

      // Safety check: ensure selectedOption is a valid object
      if (!selectedOption || typeof selectedOption !== "object") {
        console.error(
          "handleMaterialSelected: Invalid selectedOption received:",
          selectedOption
        );
        enqueueSnackbar("Invalid material option selected", {
          variant: "error",
        });
        return;
      }

      const updatedItems = [...values.items];
      const item = updatedItems[selectedItemIndex];

      const description =
        selectedOption.description ||
        selectedOption.productDescription ||
        selectedOption.title ||
        selectedOption.name ||
        item.description ||
        "";
      const dimensions = selectedOption.dimensions || item.dimensions || "";
      let unit = selectedOption.unit || item.unit || "each";
      if (selectedOption.packSize && parseInt(selectedOption.packSize) > 1) {
        unit = "pack";
      }

      item.name =
        selectedOption.name ||
        selectedOption.title ||
        selectedOption.productName ||
        item.name;
      item.description = description;

      let newPrice = item.price;
      if (
        selectedOption.numerical_price !== undefined &&
        selectedOption.numerical_price !== null
      ) {
        newPrice = selectedOption.numerical_price;
      } else if (
        selectedOption.price !== undefined &&
        selectedOption.price !== null
      ) {
        // Safe string conversion to handle undefined/null/complex objects
        const priceStr =
          selectedOption.price != null ? String(selectedOption.price) : "";
        const parsed = parseFloat(priceStr.replace(/[^0-9.]/g, ""));
        if (!isNaN(parsed)) {
          newPrice = parsed;
        }
      }
      item.price = newPrice;

      item.sku = selectedOption.sku || selectedOption.id || item.sku;
      item.imageUrl =
        selectedOption.imageUrl || selectedOption.image || item.imageUrl;
      item.url = selectedOption.url || selectedOption.productUrl || item.url;
      item.source =
        selectedOption.source || selectedOption.sourceName || item.source;
      item.unit = unit;
      item.dimensions = dimensions;
      item.brand = selectedOption.brand || item.brand;
      item.modelNumber = selectedOption.modelNumber || item.modelNumber;
      item.packSize = selectedOption.packSize || item.packSize;

      item.priceInfo = {
        ...item.priceInfo,
        source:
          selectedOption.source || selectedOption.sourceName || "Selected",
        sourceId: selectedOption.sourceId || "",
        perUnitPrice: selectedOption.perUnitPrice || newPrice,
        status: "price_confirmed_user",
        lastUpdated: new Date().toISOString(),
        priceLookupTimestamp: new Date().toISOString(),
      };

      setFieldValue("items", updatedItems);
      enqueueSnackbar(`Material option selected for ${item.name}`, {
        variant: "success",
      });

      setMaterialOptionDialogOpen(false);
      setSelectedItemIndex(null);
      setSelectedItemId(null);
    },
    [selectedItemIndex, values, setFieldValue, enqueueSnackbar]
  );

  const formatMaterialSource = (sourceType) => {
    switch (sourceType) {
      case "PLATT":
        return "Platt Electric";
      case "HOME_DEPOT":
        return "Home Depot";
      case "MANUAL":
        return "Manual";
      case "AI_GENERATED":
        return "AI Generated";
      default:
        return sourceType || "Unknown";
    }
  };

  const openMaterialOptionDialog = (index, itemId) => {
    setSelectedItemIndex(index);
    setSelectedItemId(itemId);
    setMaterialOptionDialogOpen(true);
  };

  const handleRefreshPrice = async (index) => {
    setRefreshingPriceIndex(index);
    const item = values.items[index];
    // Define sku based on item properties, ensuring it's available for subsequent logic
    const sku =
      item.sku || item.part_number || item.name || `Item ${index + 1}`;
    enqueueSnackbar(`Refreshing price for ${sku}...`, { variant: "info" });

    try {
      const item = values.items[index];
      if (!item.sku) {
        enqueueSnackbar("SKU is required to refresh price", {
          variant: "error",
        });
        return;
      }

      // Call the refreshMaterialPrice API
      const result = await dispatch(
        refreshMaterialPrice({
          sku: item.sku,
          sourceId: item.sourceId,
        })
      );

      let refreshedData = null;
      if (refreshMaterialPrice.fulfilled.match(result)) {
        refreshedData = {
          price: result.payload.price,
          source: result.payload.source,
          sku: result.payload.sku,
          status: "success",
        };
      } else {
        throw new Error(result.payload || "Failed to refresh price");
      }

      if (refreshedData) {
        // Check if the response includes material options that need user selection
        if (
          refreshedData.status === "pending_user_selection" &&
          refreshedData.material_options?.length > 0
        ) {
          // Set the material options in the item and open the selection dialog
          setFieldValue(
            `items[${index}].material_options`,
            refreshedData.material_options
          );
          setFieldValue(
            `items[${index}].lookup_status`,
            "pending_user_selection"
          );
          setFieldValue(
            `items[${index}].priceInfo.status`,
            "pending_user_selection"
          );

          // Open the material selection dialog using item._id (assuming it's the correct identifier)
          openMaterialOptionDialog(index, item._id);
          enqueueSnackbar(
            "Multiple material options found. Please select the best match.",
            { variant: "info" }
          );
        } else {
          // Standard price update flow
          setFieldValue(
            `items[${index}].price`,
            refreshedData.price ?? item.price
          ); // Use item.price as fallback
          setFieldValue(
            `items[${index}].priceInfo.source`,
            refreshedData.source || item.priceInfo.source
          ); // Use item.priceInfo.source
          setFieldValue(
            `items[${index}].priceInfo.lastUpdated`,
            refreshedData.lastUpdated || new Date().toISOString()
          );
          setFieldValue(
            `items[${index}].priceInfo.status`,
            refreshedData.status || "found"
          );
          setFieldValue(
            `items[${index}].priceInfo.lookupError`,
            refreshedData.lookupError || null
          );
          setFieldValue(
            `items[${index}].priceInfo.priceLookupTimestamp`,
            new Date().toISOString()
          );

          if (
            refreshedData.status === "found" &&
            refreshedData.price !== undefined
          ) {
            enqueueSnackbar(
              `Price for ${sku} refreshed: ${formatCurrency(
                refreshedData.price
              )} from ${refreshedData.source || "source"}.`,
              { variant: "success" }
            );
          } else if (refreshedData.status === "failed") {
            enqueueSnackbar(
              `Failed to refresh price for ${sku}: ${
                refreshedData.lookupError || "Unknown error"
              }`,
              { variant: "error" }
            );
          } else {
            enqueueSnackbar(
              `Price for ${sku} updated. Status: ${
                refreshedData.status || "unknown"
              }`,
              { variant: "info" }
            );
          }
        }
      } else {
        enqueueSnackbar(`No data returned for SKU: ${sku}`, {
          variant: "error",
        });
        // Update status to reflect that no data was found
        setFieldValue(`items[${index}].priceInfo.status`, "not_found");
        setFieldValue(
          `items[${index}].priceInfo.lookupError`,
          `No data returned for SKU: ${sku}`
        );
      }
    } catch (error) {
      console.error("Error refreshing price:", error);
      setFieldValue(`items[${index}].priceInfo.status`, "failed");
      setFieldValue(
        `items[${index}].priceInfo.lookupError`,
        error.message || "Refresh failed"
      );
      enqueueSnackbar(
        `Error refreshing price: ${error.message || "Unknown error"}`,
        { variant: "error" }
      );
    } finally {
      setRefreshingPriceIndex(null);
    }
  };

  const handleBatchRefreshPrices = async () => {
    setIsBatchRefreshingPrices(true);
    enqueueSnackbar("Starting batch price refresh...", { variant: "info" });
    let successCount = 0;
    let failCount = 0;

    for (let i = 0; i < values.items.length; i++) {
      const item = values.items[i];
      if (
        item.sku &&
        item.priceInfo?.source &&
        item.priceInfo.source !== "MANUAL" &&
        item.priceInfo.source !== "Custom"
      ) {
        // Only refresh if SKU and non-manual source
        try {
          // We call the individual handleRefreshPrice to leverage its logic including UI updates per item
          // This will show individual loading spinners if implemented or just update data
          await handleRefreshPrice(i, item.sku);
          // The enqueueSnackbar inside handleRefreshPrice will give individual feedback.
          // We might want to suppress that for batch or change its wording.
          // For now, let's assume it's acceptable.
          successCount++;
        } catch (e) {
          // Error is already handled and snackbarred by handleRefreshPrice
          failCount++;
        }
      }
    }
    setIsBatchRefreshingPrices(false);
    enqueueSnackbar(
      `Batch refresh complete. ${successCount} succeeded, ${failCount} failed.`,
      { variant: failCount > 0 ? "warning" : "success" }
    );
  };

  const totals = calculateTotals(
    values.items,
    values.labor,
    values.taxRate,
    values.includeLaborCosts
  );
  return (
    <Form>
      {/* AI Generation Progress Indicator */}
      {globalAiLoading && (
        <Box
          sx={{
            mb: 3,
            p: 2,
            bgcolor: "primary.main",
            color: "white",
            borderRadius: 1,
          }}
        >
          <Box sx={{ display: "flex", alignItems: "center", mb: 1 }}>
            <CircularProgress size={20} sx={{ color: "white", mr: 2 }} />
            <Typography variant="h6">
              🤖 AI Quote Generation in Progress
            </Typography>
          </Box>
          <Typography variant="body2" sx={{ mb: 2 }}>
            Analyzing your input and searching for material prices...
          </Typography>
          <LinearProgress
            sx={{
              bgcolor: "rgba(255,255,255,0.3)",
              "& .MuiLinearProgress-bar": { bgcolor: "white" },
            }}
          />
          <Typography variant="caption" sx={{ mt: 1, display: "block" }}>
            💡 Immediate pricing results will appear automatically when found!
          </Typography>
        </Box>
      )}
      
      {/* 🚀 IMMEDIATE PRICE LOOKUP STATUS INDICATOR */}
      {isPriceLookupActive && (
        <Box
          sx={{
            mb: 3,
            p: 2,
            bgcolor: "success.main",
            color: "white",
            borderRadius: 1,
          }}
        >
          <Box sx={{ display: "flex", alignItems: "center", mb: 1 }}>
            <CircularProgress size={20} sx={{ color: "white", mr: 2 }} />
            <Typography variant="h6">
              💰 Fetching Material Prices
            </Typography>
          </Box>
          <Typography variant="body2" sx={{ mb: 2 }}>
            Searching for current market prices for {lookupProgress.total} items...
          </Typography>
          <LinearProgress
            variant="determinate"
            value={lookupProgress.percentage}
            sx={{
              bgcolor: "rgba(255,255,255,0.3)",
              "& .MuiLinearProgress-bar": { bgcolor: "white" },
            }}
          />
          <Typography variant="caption" sx={{ mt: 1, display: "block" }}>
            ✅ {lookupProgress.found} found • ⏳ {lookupProgress.processed}/{lookupProgress.total} processed • ❌ {lookupProgress.failed} failed
          </Typography>
        </Box>
      )}

      <Grid container spacing={3}>
        <Grid item xs={12} md={6}>
          <TextField
            fullWidth
            name="name"
            label="Quote Name / Title"
            value={values.name}
            onChange={(e) => {
              handleChange(e);
              // CRITICAL FIX: Update draft quote when quote name changes
              updateDraftWithCurrentValues({ ...values, name: e.target.value });
            }}
            onBlur={handleBlur}
            error={touched.name && Boolean(errors.name)}
            helperText={touched.name && errors.name}
            required
          />
        </Grid>
        <Grid item xs={12} md={6}>
          <LocalizationProvider dateAdapter={AdapterDateFns}>
            <DatePicker
              label="Valid Until"
              name="validUntil"
              value={new Date(values.validUntil)}
              onChange={(newValue) =>
                setFieldValue(
                  "validUntil",
                  newValue
                    ? new Date(newValue).toISOString().split("T")[0]
                    : null
                )
              }
              slots={{
                textField: (params) => (
                  <TextField
                    {...params}
                    fullWidth
                    required
                    error={touched.validUntil && Boolean(errors.validUntil)}
                    helperText={touched.validUntil && errors.validUntil}
                    InputLabelProps={{ shrink: true }}
                  />
                ),
              }}
            />
          </LocalizationProvider>
        </Grid>

        <Grid item xs={12} md={5}>
          <Autocomplete
            options={customers}
            getOptionLabel={(o) =>
              o.businessName ||
              `${o.contactPerson?.firstName || ""} ${
                o.contactPerson?.lastName || ""
              }`.trim() ||
              o.email ||
              "Unnamed Customer"
            }
            value={values.customer}
            onChange={(_, nv) => {
              setFieldValue("customer", nv);
              setFieldValue("job", null);
              // CRITICAL FIX: Update draft quote when customer changes
              updateDraftWithCurrentValues({
                ...values,
                customer: nv,
                job: null,
              });
            }}
            onBlur={handleBlur}
            renderInput={(params) => (
              <TextField
                {...params}
                name="customer"
                label="Customer"
                required
                error={touched.customer && Boolean(errors.customer)}
                helperText={touched.customer && errors.customer}
              />
            )}
            isOptionEqualToValue={(o, v) => o?._id === v?._id}
          />
        </Grid>
        <Grid
          item
          xs={12}
          md={2}
          sx={{ display: "flex", alignItems: "center" }}
        >
          <Button
            variant="outlined"
            startIcon={<AddIcon />}
            onClick={() => setIsCreateCustomerModalOpen(true)}
            sx={{ height: "fit-content", alignSelf: "center" }}
          >
            New
          </Button>
        </Grid>
        <Grid item xs={12} md={5}>
          <Autocomplete
            options={
              values.customer
                ? jobs.filter(
                    (job) => job.customer?._id === values.customer._id
                  )
                : []
            }
            getOptionLabel={(o) =>
              o.name || o.description || `Job #${o.number}` || ""
            }
            value={values.job}
            onChange={(_, nv) => {
              setFieldValue("job", nv);
              // CRITICAL FIX: Update draft quote when job changes
              updateDraftWithCurrentValues({ ...values, job: nv });
            }}
            onBlur={handleBlur}
            disabled={!values.customer}
            renderInput={(params) => (
              <TextField
                {...params}
                name="job"
                label="Related Job"
                error={touched.job && Boolean(errors.job)}
                helperText={touched.job && errors.job}
              />
            )}
            isOptionEqualToValue={(o, v) => o?._id === v?._id}
            noOptionsText={
              values.customer
                ? "No jobs for this customer"
                : "Select customer first"
            }
          />
        </Grid>

        {/* AI Quote Assistant Section from CreateQuote.js */}
        <Grid item xs={12}>
          <Paper
            variant="outlined"
            sx={{ p: 2, mt: 1, mb: 2, borderColor: "primary.main" }}
          >
            <Typography
              variant="h6"
              gutterBottom
              sx={{
                color: "primary.main",
                display: "flex",
                alignItems: "center",
              }}
            >
              <AutoAwesomeIcon sx={{ mr: 1 }} />
              AI Quote Assistant
            </Typography>
            {globalAiLoading && <LinearProgress sx={{ mb: 1 }} />}{" "}
            {/* General AI Loading Indicator */}
            {/* Image Upload for Quote & AI Context */}
            <Box
              sx={{
                mb: 2,
                p: 1.5,
                border: "1px dashed",
                borderColor: "grey.400",
                borderRadius: 1,
              }}
            >
              <Typography variant="subtitle2" gutterBottom>
                Add Quote Images (for AI context & association)
              </Typography>
              <Box
                sx={{
                  position: "relative",
                  display: "inline-block",
                  width: "100%",
                }}
              >
                <Button
                  variant="outlined"
                  component="label"
                  startIcon={
                    imageUploadLoading ? (
                      <CircularProgress size={20} />
                    ) : (
                      <CloudUploadIcon />
                    )
                  }
                  disabled={imageUploadLoading}
                  fullWidth
                  sx={{
                    mb: 1,
                    minHeight: 48,
                    cursor: imageUploadLoading ? "not-allowed" : "pointer",
                    "&:hover": {
                      backgroundColor: imageUploadLoading
                        ? "transparent"
                        : "rgba(25, 118, 210, 0.04)",
                    },
                  }}
                >
                  {imageUploadLoading ? "Uploading..." : "Choose Files"}
                  <input
                    type="file"
                    hidden
                    key={`file-input-${Date.now()}`} // Add unique key to prevent React caching issues
                    accept="image/png,image/jpeg,image/webp,image/heic,image/heif"
                    multiple
                    onChange={handleImageSelection}
                    style={{ display: "none" }}
                    aria-label="Upload image files"
                    ref={(input) => {
                      // Reset input value on mount to ensure clean state
                      if (input) {
                        input.value = "";
                      }
                    }}
                  />
                </Button>
              </Box>
              {imageUploadLoading && <LinearProgress sx={{ mb: 1 }} />}

              {/* Display images selected for upload (new quotes) */}
              {quoteImagesForUpload.length > 0 && (
                <Box sx={{ my: 1 }}>
                  <Typography variant="caption">
                    Files ready for upload on save:
                  </Typography>
                  <List
                    dense
                    sx={{
                      maxHeight: 150,
                      overflow: "auto",
                      bgcolor: "grey.100",
                      borderRadius: 1,
                    }}
                  >
                    {quoteImagesForUpload.map((imgInfo, index) => (
                      <ListItem
                        key={index}
                        dense
                        secondaryAction={
                          <IconButton
                            edge="end"
                            aria-label="delete"
                            onClick={() => removeQuoteImageForUpload(index)}
                            size="small"
                          >
                            <CloseIcon fontSize="small" />
                          </IconButton>
                        }
                      >
                        <ListItemIcon sx={{ minWidth: 32 }}>
                          <ImageIcon fontSize="small" />
                        </ListItemIcon>
                        <ListItemText
                          primary={imgInfo.name}
                          secondary={`${(imgInfo.size / 1024).toFixed(1)} KB`}
                        />
                      </ListItem>
                    ))}
                  </List>
                </Box>
              )}

              {/* Display Associated Images (existing quotes) */}
              {associatedQuoteImages.length > 0 && (
                <Box sx={{ mt: 2 }}>
                  <Typography variant="subtitle2" gutterBottom>
                    Associated Images:
                  </Typography>
                  <Grid container spacing={1}>
                    {associatedQuoteImages.map((img) => (
                      <Grid
                        item
                        key={img._id || img.url}
                        xs={6}
                        sm={4}
                        md={3}
                        lg={2}
                      >
                        <Card sx={{ position: "relative", height: "100%" }}>
                          <CardMedia
                            component="img"
                            height="100"
                            image={img.thumbnailUrl || img.url}
                            alt={img.title || "Quote image"}
                            sx={{ objectFit: "cover" }}
                          />
                          <Tooltip title="Delete Associated Image">
                            <IconButton
                              size="small"
                              onClick={() =>
                                handleDeleteAssociatedQuoteImage(img._id)
                              }
                              sx={{
                                position: "absolute",
                                top: 2,
                                right: 2,
                                backgroundColor: "rgba(0,0,0,0.4)",
                                color: "white",
                                "&:hover": {
                                  backgroundColor: "rgba(0,0,0,0.6)",
                                },
                              }}
                            >
                              <CloseIcon fontSize="small" />
                            </IconButton>
                          </Tooltip>
                        </Card>
                      </Grid>
                    ))}
                  </Grid>
                </Box>
              )}
            </Box>
            <TextField
              fullWidth
              name="projectOverview"
              label="Project Overview (for AI)"
              multiline
              minRows={3}
              value={values.projectOverview}
              onChange={handleChange}
              onBlur={handleBlur}
              helperText="Describe project goals for AI. This will also be used as the quote's project overview if not edited."
              sx={{ mb: 2 }}
              InputProps={{
                endAdornment: (
                  <InputAdornment position="end">
                    <Tooltip title="Expand to Full Preview">
                      <IconButton
                        onClick={() => handlePreview(values)}
                        edge="end"
                        size="small"
                      >
                        <VisibilityIcon />
                      </IconButton>
                    </Tooltip>
                  </InputAdornment>
                ),
              }}
            />
            <TextField
              fullWidth
              name="materialsIncluded"
              label="Materials Summary / List (for AI)"
              multiline
              minRows={3}
              value={values.materialsIncluded}
              onChange={handleChange}
              onBlur={handleBlur}
              helperText="Summarize key materials or provide a list for AI. This will also be used as the quote's 'materials included' section if not edited."
              sx={{ mb: 2 }}
              InputProps={{
                endAdornment: (
                  <InputAdornment position="end">
                    <Tooltip title="Expand to Full Preview">
                      <IconButton
                        onClick={() => handlePreview(values)}
                        edge="end"
                        size="small"
                      >
                        <VisibilityIcon />
                      </IconButton>
                    </Tooltip>
                  </InputAdornment>
                ),
              }}
            />
            {/* AI Generation Controls */}
            <Box
              sx={{
                display: "flex",
                gap: 2,
                mb: 1,
                flexWrap: "wrap",
                alignItems: "center",
              }}
            >
              <LoadingButton
                variant="contained"
                onClick={() =>
                  handleAIGenerateRequest("overview", setFieldValue, values)
                }
                loading={
                  (globalAiLoading &&
                    (globalAiStatus === "generating_overview" ||
                      globalAiStatus === "pending_ai_call")) ||
                  isStreaming ||
                  isAiGenerating
                }
                disabled={
                  globalAiLoading ||
                  isStreaming ||
                  isAiGenerating ||
                  !values.projectOverview?.trim()
                }
                size="small"
                loadingPosition="start"
                startIcon={<AutoAwesomeIcon />}
              >
                Generate from Overview
              </LoadingButton>
              <LoadingButton
                variant="contained"
                onClick={() =>
                  handleAIGenerateRequest("materials", setFieldValue, values)
                }
                loading={
                  (globalAiLoading &&
                    (globalAiStatus === "generating_materials" ||
                      globalAiStatus === "pending_ai_call")) ||
                  isStreaming ||
                  isAiGenerating
                }
                disabled={
                  globalAiLoading ||
                  isStreaming ||
                  isAiGenerating ||
                  (values.items.length === 0 &&
                    !values.materialsIncluded?.trim())
                }
                size="small"
                loadingPosition="start"
                startIcon={<AutoAwesomeIcon />}
              >
                Generate from Materials
              </LoadingButton>

              {/* Streaming Mode Toggle */}
              <FormControlLabel
                control={
                  <Switch
                    checked={useStreamingMode}
                    onChange={(e) => setUseStreamingMode(e.target.checked)}
                    color="primary"
                    size="small"
                  />
                }
                label={
                  <Box sx={{ display: "flex", alignItems: "center", gap: 0.5 }}>
                    <Typography variant="body2">Streaming Mode</Typography>
                    <Tooltip title="Enable real-time progressive updates (1-3s response) instead of waiting for complete generation (140s+)">
                      <HelpOutlineIcon fontSize="small" />
                    </Tooltip>
                  </Box>
                }
                sx={{ ml: 2 }}
              />
            </Box>
            {/* Mode Status Indicator */}
            <Box sx={{ mb: 2 }}>
              <Chip
                label={
                  useStreamingMode
                    ? "🚀 Streaming Mode (1-3s response)"
                    : "📡 Traditional Mode (140s+ response)"
                }
                color={useStreamingMode ? "primary" : "default"}
                size="small"
                variant={useStreamingMode ? "filled" : "outlined"}
              />
            </Box>
            {/* 🚀 STREAMING PROGRESS INDICATOR */}
            <StreamingProgressIndicator
              isStreaming={isStreaming}
              currentPhase={currentPhase}
              progress={progress}
              statusMessage={statusMessage}
              streamingData={streamingData}
              error={streamingError}
              expanded={streamingProgressExpanded}
              onToggleExpanded={() =>
                setStreamingProgressExpanded(!streamingProgressExpanded)
              }
            />
            {/* 🚀 IMMEDIATE PRICE LOOKUP PROGRESS INDICATOR */}
            <PriceLookupProgressIndicator
              isActive={isPriceLookupActive}
              items={lookupResults.length > 0 ? lookupResults : values.items}
              title="💰 Immediate Price Lookup in Progress"
              expanded={isPriceLookupActive}
              autoHide={true}
              autoHideDuration={3000}
              showRetryOption={false}
              onStatusUpdate={(status) => {
                console.log("[Price Lookup Progress]", status);
              }}
            />

            {/* 🚀 PROMINENT PRICE LOOKUP BUTTON */}
            {values.items && values.items.length > 0 && !isPriceLookupActive && (
              <Box sx={{ mb: 2, textAlign: 'center' }}>
                <Button
                  variant="contained"
                  color="primary"
                  size="large"
                  startIcon={<SearchIcon />}
                  onClick={() => handleManualPriceLookup(values, setFieldValue, { forceRefresh: false })}
                  disabled={isPriceLookupActive}
                  sx={{
                    px: 4,
                    py: 1.5,
                    fontSize: '1.1rem',
                    fontWeight: 600,
                    boxShadow: 3,
                    '&:hover': {
                      boxShadow: 6,
                    }
                  }}
                >
                  Get Current Prices for All Materials
                </Button>
                <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                  Get real-time pricing from Home Depot and Platt Electric before saving your quote
                </Typography>
              </Box>
            )}
            {globalAiStatus === "pending_questions" &&
              globalAiQuestions?.length > 0 && (
                <Box
                  sx={{
                    mt: 2,
                    border: "1px solid",
                    borderColor: "warning.main",
                    p: 2,
                    borderRadius: 1,
                  }}
                >
                  <Typography
                    variant="subtitle1"
                    color="warning.main"
                    gutterBottom
                  >
                    AI Needs Clarification:
                  </Typography>
                  {globalAiQuestions.map((q, index) => (
                    <TextField
                      key={index}
                      label={`Question ${index + 1}: ${q}`}
                      fullWidth
                      multiline
                      rows={2}
                      value={aiAnswers[q] || ""}
                      onChange={(e) =>
                        setAiAnswers((prev) => ({
                          ...prev,
                          [q]: e.target.value,
                        }))
                      }
                      sx={{ mb: 1.5 }}
                      variant="outlined"
                      size="small"
                    />
                  ))}
                  <LoadingButton
                    variant="contained"
                    color="warning"
                    onClick={() => handleAISubmitAnswers(setFieldValue, values)}
                    loading={
                      globalAiLoading && globalAiStatus === "submitting_answers"
                    }
                    disabled={
                      globalAiLoading ||
                      Object.keys(aiAnswers).length !==
                        globalAiQuestions.length ||
                      !globalAiQuestions.every((q) => aiAnswers[q]?.trim())
                    }
                    size="small"
                    loadingPosition="start"
                    startIcon={<SaveIcon />}
                  >
                    Submit Answers to AI
                  </LoadingButton>
                </Box>
              )}
            {globalAiError && (
              <Alert severity="error" sx={{ mt: 2 }}>
                AI Error: {globalAiError.message || globalAiError}
              </Alert>
            )}
          </Paper>
        </Grid>

        {/* Material Price Status Display - shows real-time lookup status */}
        {quoteId && (
          <Grid item xs={12}>
            <MaterialPriceStatusDisplay
              quoteId={quoteId}
              autoRefresh={true}
              onStatusUpdate={(status) => {
                // Optional: Handle status updates if needed
                if (
                  status &&
                  status.summary &&
                  status.summary.itemsWithPrice > 0
                ) {
                  logger.debug(
                    "[CreateQuoteFormik] Material price lookup status updated:",
                    status.summary
                  );
                }
              }}
            />
          </Grid>
        )}

        <Grid item xs={12}>
          <TextField
            fullWidth
            name="scopeOfWork"
            label="Scope of Work"
            multiline
            minRows={6}
            value={values.scopeOfWork}
            onChange={handleChange}
            onBlur={handleBlur}
            helperText="Detail the specific tasks (Markdown supported). Edit AI-generated text here."
            InputProps={{
              endAdornment: (
                <InputAdornment position="end">
                  <Tooltip title="Expand to Full Preview">
                    <IconButton
                      onClick={() => handlePreview(values)}
                      edge="end"
                      size="small"
                    >
                      <VisibilityIcon />
                    </IconButton>
                  </Tooltip>
                </InputAdornment>
              ),
            }}
          />
          {values.scopeOfWork && (
            <Paper
              variant="outlined"
              sx={{
                p: 2,
                mt: 1,
                maxHeight: 300,
                overflow: "auto",
                bgcolor: "grey.50",
              }}
            >
              <Typography
                variant="subtitle2"
                gutterBottom
                sx={{ color: "text.secondary" }}
              >
                Preview:
              </Typography>
              <ReactMarkdown remarkPlugins={[remarkGfm]}>
                {values.scopeOfWork}
              </ReactMarkdown>
            </Paper>
          )}
        </Grid>

        {/* Materials Section from CreateQuote.js */}
        <Grid item xs={12}>
          <Box
            sx={{
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
              mb: 2,
            }}
          >
            <Typography variant="h6">Materials</Typography>
            <Box>
              <Button
                variant="contained"
                color="primary"
                startIcon={isPriceLookupActive ? <CircularProgress size={16} color="inherit" /> : <SearchIcon />}
                onClick={() => handleManualPriceLookup(values, setFieldValue, { forceRefresh: false })}
                disabled={isPriceLookupActive || values.items.length === 0}
                sx={{ mr: 1 }}
              >
                {isPriceLookupActive ? "Getting Prices..." : "Get Prices Now"}
              </Button>
              <Button
                variant="outlined"
                startIcon={<RefreshIcon />}
                onClick={handleBatchRefreshPrices}
                disabled={isBatchRefreshingPrices || values.items.length === 0}
                sx={{ mr: 1 }}
              >
                {isBatchRefreshingPrices ? (
                  <CircularProgress size={20} sx={{ mr: 1 }} />
                ) : null}
                Refresh All Prices
              </Button>
              <Button
                variant="contained"
                startIcon={<AddIcon />}
                onClick={() => setShowMaterialSearchComponent(true)}
              >
                Add Material from Search
              </Button>
            </Box>
          </Box>
          {showMaterialSearchComponent && (
            <Box sx={{ mb: 3 }}>
              <MaterialSearch
                onSelectMaterial={handleAddMaterialFromSearch}
                buttonLabel="Add to Quote"
              />
            </Box>
          )}
        </Grid>

        <Grid item xs={12}>
          <FieldArray name="items">
            {({ push, remove }) => (
              <Box>
                {values.items.map((item, index) => (
                  <Paper
                    key={index}
                    sx={{
                      p: 2,
                      mb: 2,
                      border:
                        item.priceInfo?.source === "BACKEND_IMMEDIATE_LOOKUP"
                          ? "2px solid #4caf50"
                          : "1px solid #eee",
                      bgcolor:
                        item.priceInfo?.source === "BACKEND_IMMEDIATE_LOOKUP"
                          ? "rgba(76, 175, 80, 0.05)"
                          : "inherit",
                    }}
                  >
                    {/* Visual indicator for immediate pricing */}
                    {item.priceInfo?.source === "BACKEND_IMMEDIATE_LOOKUP" && (
                      <Box
                        sx={{
                          display: "flex",
                          alignItems: "center",
                          gap: 1,
                          mb: 1,
                          p: 1,
                          bgcolor: "success.main",
                          color: "white",
                          borderRadius: 1,
                          fontSize: "0.875rem",
                        }}
                      >
                        <CheckCircleOutlineIcon fontSize="small" />
                        <Typography variant="body2" sx={{ fontWeight: "bold" }}>
                          ✨ Immediate Pricing Found During AI Generation!
                        </Typography>
                        {item.priceInfo?.lookupDuration && (
                          <Typography variant="caption" sx={{ ml: "auto" }}>
                            ({item.priceInfo.lookupDuration}ms)
                          </Typography>
                        )}
                      </Box>
                    )}

                    <Grid container spacing={2} alignItems="center">
                      {item.imageUrl && (
                        <Grid item xs={12} sm={1}>
                          <img
                            src={item.imageUrl}
                            alt={item.name || "Item"}
                            style={{
                              width: "40px",
                              height: "40px",
                              objectFit: "contain",
                            }}
                          />
                        </Grid>
                      )}
                      <Grid item xs={12} sm={item.imageUrl ? 3 : 4}>
                        <TextField
                          fullWidth
                          label="Item Name"
                          name={`items[${index}].name`}
                          value={item.name}
                          onChange={handleChange}
                          onBlur={handleBlur}
                          required
                          error={
                            touched.items?.[index]?.name &&
                            Boolean(errors.items?.[index]?.name)
                          }
                          helperText={
                            touched.items?.[index]?.name &&
                            errors.items?.[index]?.name
                          }
                        />
                      </Grid>
                      <Grid item xs={6} sm={1}>
                        <TextField
                          fullWidth
                          label="Qty"
                          type="number"
                          name={`items[${index}].quantity`}
                          value={item.quantity}
                          onChange={handleChange}
                          onBlur={handleBlur}
                          required
                          error={
                            touched.items?.[index]?.quantity &&
                            Boolean(errors.items?.[index]?.quantity)
                          }
                          helperText={
                            touched.items?.[index]?.quantity &&
                            errors.items?.[index]?.quantity
                          }
                          InputProps={{ inputProps: { min: 1 } }}
                        />
                      </Grid>
                      <Grid item xs={6} sm={2}>
                        <TextField
                          fullWidth
                          label="Unit"
                          name={`items[${index}].unit`}
                          value={item.unit}
                          onChange={handleChange}
                          onBlur={handleBlur}
                          required
                          error={
                            touched.items?.[index]?.unit &&
                            Boolean(errors.items?.[index]?.unit)
                          }
                          helperText={
                            touched.items?.[index]?.unit &&
                            errors.items?.[index]?.unit
                          }
                        />
                      </Grid>
                      <Grid item xs={6} sm={2}>
                        <TextField
                          fullWidth
                          label="Price"
                          type="number"
                          name={`items[${index}].price`}
                          value={item.price}
                          onChange={handleChange}
                          onBlur={handleBlur}
                          required
                          error={
                            touched.items?.[index]?.price &&
                            Boolean(errors.items?.[index]?.price)
                          }
                          helperText={
                            touched.items?.[index]?.price &&
                            errors.items?.[index]?.price
                          }
                          InputProps={{
                            startAdornment: (
                              <InputAdornment position="start">
                                $
                              </InputAdornment>
                            ),
                            inputProps: { min: 0, step: 0.01 },
                            endAdornment: item.sku && (
                              <InputAdornment position="end">
                                <IconButton
                                  aria-label="refresh price"
                                  onClick={() =>
                                    handleRefreshPrice(index, item.sku)
                                  }
                                  disabled={refreshingPriceIndex === index}
                                  size="small"
                                >
                                  {refreshingPriceIndex === index ? (
                                    <CircularProgress size={20} />
                                  ) : (
                                    <RefreshIcon fontSize="small" />
                                  )}
                                </IconButton>
                              </InputAdornment>
                            ),
                          }}
                        />
                      </Grid>
                      <Grid item xs={6} sm={2}>
                        <TextField
                          fullWidth
                          label="SKU"
                          name={`items[${index}].sku`}
                          value={item.sku}
                          onChange={handleChange}
                          onBlur={handleBlur}
                        />
                      </Grid>
                      <Grid item xs={12} sm={1}>
                        <Typography
                          variant="body2"
                          sx={{ textAlign: "right", fontWeight: "bold" }}
                        >
                          {formatCurrency(
                            (item.price || 0) * (item.quantity || 0)
                          )}
                        </Typography>
                      </Grid>
                      <Grid item xs={12}>
                        <TextField
                          fullWidth
                          label="Notes/Details"
                          name={`items[${index}].notes`}
                          value={item.notes}
                          onChange={handleChange}
                          onBlur={handleBlur}
                          size="small"
                          multiline
                          minRows={1}
                        />
                      </Grid>

                      <Grid
                        item
                        xs={12}
                        sx={{ pt: 1, borderTop: "1px solid #f0f0f0", mt: 1 }}
                      >
                        <Grid
                          container
                          spacing={1}
                          alignItems="center"
                          justifyContent="space-between"
                        >
                          <Grid item xs={12} sm="auto">
                            <Box
                              sx={{
                                display: "flex",
                                alignItems: "center",
                                gap: 1,
                                flexWrap: "wrap",
                              }}
                            >
                              <Chip
                                icon={<InfoIcon />}
                                label={`Source: ${formatMaterialSource(
                                  item.priceInfo?.source || item.source
                                )}`}
                                size="small"
                                variant="outlined"
                              />
                              {item.priceInfo?.lastUpdated && (
                                <Tooltip
                                  title={`Last Price Check: ${new Date(
                                    item.priceInfo.lastUpdated
                                  ).toLocaleString()}`}
                                >
                                  <Chip
                                    icon={<AccessTimeIcon />}
                                    label={`${new Date(
                                      item.priceInfo.lastUpdated
                                    ).toLocaleDateString()}`}
                                    size="small"
                                    variant="outlined"
                                  />
                                </Tooltip>
                              )}
                              <Tooltip
                                title={
                                  item.priceInfo?.status === "failed"
                                    ? `Error: ${
                                        item.priceInfo?.lookupError || "Unknown"
                                      }`
                                    : `Status: ${
                                        item.priceInfo?.status || "Unknown"
                                      }`
                                }
                              >
                                <Chip
                                  icon={
                                    item.priceInfo?.status === "pending" ? (
                                      <QueryBuilderIcon />
                                    ) : item.priceInfo?.status === "found" ? (
                                      <CheckCircleOutlineIcon />
                                    ) : item.priceInfo?.status === "failed" ? (
                                      <ErrorOutlineIcon />
                                    ) : (
                                      <HelpOutlineIcon />
                                    )
                                  }
                                  label={`${
                                    item.priceInfo?.status || "Unknown"
                                  }`}
                                  size="small"
                                  color={
                                    item.priceInfo?.status === "pending"
                                      ? "warning"
                                      : item.priceInfo?.status === "found"
                                      ? "success"
                                      : item.priceInfo?.status === "failed"
                                      ? "error"
                                      : "default"
                                  }
                                  variant="outlined"
                                />
                              </Tooltip>
                            </Box>
                          </Grid>
                          <Grid item>
                            <IconButton
                              onClick={() => remove(index)}
                              color="error"
                              size="small"
                              aria-label="delete item"
                            >
                              <DeleteIcon />
                            </IconButton>
                          </Grid>
                        </Grid>
                      </Grid>
                    </Grid>
                  </Paper>
                ))}
                <Button
                  startIcon={<AddIcon />}
                  onClick={() =>
                    push({
                      ...defaultItem,
                      taxRate: values.taxRate || 0,
                      priceInfo: { ...defaultItem.priceInfo },
                    })
                  }
                >
                  Add Item Manually
                </Button>
              </Box>
            )}
          </FieldArray>
        </Grid>

        {/* Labor Costs Section from CreateQuote.js */}
        <Grid item xs={12}>
          <Accordion
            expanded={values.includeLaborCosts}
            onChange={() =>
              setFieldValue("includeLaborCosts", !values.includeLaborCosts)
            }
          >
            <AccordionSummary expandIcon={<ExpandMoreIcon />}>
              <Box
                sx={{ display: "flex", alignItems: "center", width: "100%" }}
              >
                <Typography variant="h6" sx={{ flexGrow: 1 }}>
                  Labor Costs
                </Typography>
                <FormControlLabel
                  control={
                    <Switch
                      checked={values.includeLaborCosts}
                      onChange={(e) =>
                        setFieldValue("includeLaborCosts", e.target.checked)
                      }
                      onClick={(e) => e.stopPropagation()}
                    />
                  }
                  label="Include labor"
                  sx={{ mr: 2 }}
                />
              </Box>
            </AccordionSummary>
            <AccordionDetails>
              <Grid container spacing={2}>
                <Grid item xs={12} md={4}>
                  <TextField
                    label="Labor Hours"
                    type="number"
                    fullWidth
                    name="labor.hours"
                    value={values.labor.hours}
                    onChange={handleChange}
                    onBlur={handleBlur}
                    InputProps={{
                      endAdornment: (
                        <InputAdornment position="end">hrs</InputAdornment>
                      ),
                    }}
                    disabled={!values.includeLaborCosts}
                  />
                </Grid>
                <Grid item xs={12} md={4}>
                  <TextField
                    label="Hourly Rate"
                    type="number"
                    fullWidth
                    name="labor.rate"
                    value={values.labor.rate}
                    onChange={handleChange}
                    onBlur={handleBlur}
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">$</InputAdornment>
                      ),
                    }}
                    disabled={!values.includeLaborCosts}
                  />
                </Grid>
                <Grid item xs={12} md={4}>
                  <TextField
                    label="Labor Notes"
                    fullWidth
                    name="labor.notes"
                    value={values.labor.notes}
                    onChange={handleChange}
                    onBlur={handleBlur}
                    multiline
                    minRows={1}
                    disabled={!values.includeLaborCosts}
                  />
                </Grid>
                <Grid item xs={12}>
                  <Box sx={{ display: "flex", justifyContent: "flex-end" }}>
                    <Typography variant="h6">
                      Labor Total:{" "}
                      {formatCurrency(
                        (values.labor.hours || 0) * (values.labor.rate || 0)
                      )}
                    </Typography>
                  </Box>
                </Grid>
              </Grid>
            </AccordionDetails>
          </Accordion>
        </Grid>

        {/* Totals & Actions */}
        <Grid item xs={12}>
          <Divider sx={{ my: 2 }} />
          <Grid container spacing={2} justifyContent="flex-end">
            <Grid item xs={12} md={4}>
              <Typography>
                Materials Total: {formatCurrency(totals.materialsTotal)}
              </Typography>
              {values.includeLaborCosts && (
                <Typography>
                  Labor Total: {formatCurrency(totals.laborTotal)}
                </Typography>
              )}
              <Typography>
                Subtotal: {formatCurrency(totals.subtotal)}
              </Typography>
              <TextField
                fullWidth
                label="Tax Rate (%)"
                type="number"
                name="taxRate"
                value={values.taxRate}
                onChange={handleChange}
                onBlur={handleBlur}
                InputProps={{ inputProps: { min: 0, max: 100, step: 0.1 } }}
                sx={{ my: 1 }}
              />
              <Typography>
                Tax Amount: {formatCurrency(totals.taxAmount)}
              </Typography>
              <Typography variant="h6" sx={{ mt: 1, fontWeight: "bold" }}>
                Grand Total: {formatCurrency(totals.grandTotal)}
              </Typography>
            </Grid>
          </Grid>
          <Box display="flex" justifyContent="flex-end" gap={2} mt={2}>
            <Button
              variant="outlined"
              color="info"
              startIcon={<VisibilityIcon />}
              onClick={() => handlePreview(values)}
              disabled={isSubmitting || !dirty}
            >
              Preview Quote
            </Button>
            <Button
              variant="outlined"
              color="secondary"
              onClick={() => {
                dispatch(clearDraftQuote());
                navigate("/quotes");
              }}
            >
              Cancel
            </Button>
            <LoadingButton
              type="submit"
              variant="contained"
              color="primary"
              loading={isSubmitting}
              disabled={isSubmitting || !dirty || !isValid}
              startIcon={<SaveIcon />}
            >
              {quoteId ? "Update Quote" : "Save Quote"}
            </LoadingButton>
          </Box>
        </Grid>
      </Grid>

      {/* Material Option Selection Dialog */}
      <MaterialOptionSelectionDialog
        open={materialOptionDialogOpen}
        onClose={() => setMaterialOptionDialogOpen(false)}
        quoteId={quoteId}
        itemId={selectedItemId}
        itemName={
          selectedItemIndex !== null
            ? values.items[selectedItemIndex]?.name
            : ""
        }
        onMaterialSelected={handleMaterialSelected}
      />
    </Form>
  );
};

export default CreateQuoteFormik;
