const User = require("./User");
const Customer = require("./Customer");
const Job = require("./Job");
const Invoice = require("./Invoice");
const Material = require("./Material");
const CalendarEvent = require("./CalendarEvent");
const Notification = require("./Notification");
const MaterialSource = require("./MaterialSource");
const PriceScrapingLog = require("./PriceScrapingLog");
const MaterialPriceHistory = require("./MaterialPriceHistory");
const Quote = require("./Quote"); // Renamed model import
const MaterialTransaction = require("./MaterialTransaction"); // Added
module.exports = {
  User,
  Customer,
  Job,
  Invoice,
  Material,
  CalendarEvent,
  Notification,
  MaterialSource,
  PriceScrapingLog,
  MaterialPriceHistory,
  Quote, // Renamed model export
  MaterialTransaction, // Added
};
