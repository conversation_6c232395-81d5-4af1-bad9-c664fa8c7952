/**
 * Price Lookup Poller Service
 * Temporary service to poll for quotes needing price lookups
 * This is a fallback if Change Streams are not working
 */

const logger = require("../utils/logger");
const Quote = require("../models/Quote");
const priceLookupService = require("./priceLookupService");
const mongoose = require("mongoose");
const materialFetchLogger = require("../utils/materialFetchLogger");

class PriceLookupPoller {
  constructor() {
    this.isRunning = false;
    this.pollInterval = null;
    this.POLL_INTERVAL_MS = 10000; // Poll every 10 seconds
    this.processedItems = new Set(); // Track processed items to avoid duplicates
    this.connectionHealthy = true;
    this.connectionRetryCount = 0;
    this.MAX_RETRY_COUNT = 5;
    this.RETRY_BACKOFF_MS = 5000; // Start with 5 seconds backoff
  }

  /**
   * Start polling for quotes with pending price lookups
   */
  start() {
    if (this.isRunning) {
      logger.warn("[PriceLookupPoller] Already running");
      return;
    }

    logger.info("[PriceLookupPoller] Starting price lookup polling service");
    this.isRunning = true;

    // Run immediately
    this.pollForPendingLookups();

    // Then run periodically
    this.pollInterval = setInterval(() => {
      this.pollForPendingLookups();
    }, this.POLL_INTERVAL_MS);
  }

  /**
   * Stop polling
   */
  stop() {
    if (!this.isRunning) {
      return;
    }

    logger.info("[PriceLookupPoller] Stopping price lookup polling service");
    this.isRunning = false;

    if (this.pollInterval) {
      clearInterval(this.pollInterval);
      this.pollInterval = null;
    }
  }

  /**
   * Check if MongoDB connection is healthy
   */
  async checkConnectionHealth() {
    try {
      // Simple query to check connection
      await mongoose.connection.db.admin().ping();
      this.connectionHealthy = true;
      this.connectionRetryCount = 0;
      this.RETRY_BACKOFF_MS = 5000; // Reset backoff
      return true;
    } catch (error) {
      this.connectionHealthy = false;
      logger.warn("[PriceLookupPoller] MongoDB connection unhealthy:", error.message);
      return false;
    }
  }

  /**
   * Handle connection failure with exponential backoff
   */
  async handleConnectionFailure(error) {
    this.connectionHealthy = false;
    this.connectionRetryCount++;

    if (this.connectionRetryCount > this.MAX_RETRY_COUNT) {
      logger.error("[PriceLookupPoller] Max connection retries exceeded. Pausing polling.");
      this.stop();
      return;
    }

    // Exponential backoff with jitter
    const backoffTime = this.RETRY_BACKOFF_MS * Math.pow(2, this.connectionRetryCount - 1);
    const jitter = Math.random() * 1000; // Add up to 1 second jitter
    const totalWait = backoffTime + jitter;

    logger.warn(`[PriceLookupPoller] Connection failed (attempt ${this.connectionRetryCount}/${this.MAX_RETRY_COUNT}). Retrying in ${Math.round(totalWait/1000)}s`);
    
    // Wait before next attempt
    await new Promise(resolve => setTimeout(resolve, totalWait));

    // Try to reconnect
    try {
      await mongoose.connection.close();
      await mongoose.connect(process.env.MONGODB_URI);
      this.connectionHealthy = true;
      this.connectionRetryCount = 0;
      logger.info("[PriceLookupPoller] Connection restored successfully");
    } catch (reconnectError) {
      logger.error("[PriceLookupPoller] Reconnection failed:", reconnectError.message);
    }
  }

  /**
   * Poll for quotes with items needing price lookup
   */
  async pollForPendingLookups() {
    // Check connection health before proceeding
    if (!this.connectionHealthy) {
      const isHealthy = await this.checkConnectionHealth();
      if (!isHealthy) {
        logger.warn("[PriceLookupPoller] Skipping poll due to unhealthy connection");
        return;
      }
    }

    try {
      // Find quotes created in the last hour with pending lookups
      const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000);

      const quotes = await Quote.find({
        createdAt: { $gte: oneHourAgo },
        "items.lookup_results": {
          $elemMatch: {
            status: "pending_internal_price_lookup",
          },
        },
      }).limit(10); // Process up to 10 quotes at a time

      if (quotes.length === 0) {
        return;
      }

      logger.info(
        `[PriceLookupPoller] Found ${quotes.length} quotes with pending price lookups`
      );
      materialFetchLogger.logPollerActivity("FOUND_PENDING_QUOTES", {
        quoteCount: quotes.length,
      });

      for (const quote of quotes) {
        for (const item of quote.items) {
          if (!item.lookup_results || !Array.isArray(item.lookup_results)) {
            continue;
          }

          const lookupEntry = item.lookup_results.find(
            (lr) =>
              lr.status === "pending_internal_price_lookup" &&
              lr.request_data &&
              lr.request_data.internal_query_details
          );

          if (lookupEntry) {
            const itemKey = `${quote._id}_${item._id}`;

            // Skip if already processed
            if (this.processedItems.has(itemKey)) {
              continue;
            }

            try {
              logger.info(
                `[PriceLookupPoller] Processing price lookup for item ${item._id} in quote ${quote._id}`
              );
              materialFetchLogger.logPollerActivity("PROCESSING_ITEM", {
                quoteId: quote._id.toString(),
                itemId: item._id.toString(),
                description: lookupEntry.request_data.internal_query_details,
              });

              const originalToolParams = {
                description: lookupEntry.request_data.internal_query_details,
                userId: quote.createdBy,
                companyId: quote.company,
                quantity: item.quantity || 1,
              };

              await priceLookupService.initiatePriceLookup(
                quote._id,
                item._id,
                originalToolParams
              );

              // Mark as processed
              this.processedItems.add(itemKey);

              // Clean up old entries (keep only last 1000)
              if (this.processedItems.size > 1000) {
                const entries = Array.from(this.processedItems);
                entries.slice(0, entries.length - 1000).forEach((key) => {
                  this.processedItems.delete(key);
                });
              }
            } catch (error) {
              logger.error(
                `[PriceLookupPoller] Error processing item ${item._id}:`,
                error
              );
            }
          }
        }
      }
    } catch (error) {
      logger.error(
        "[PriceLookupPoller] Error in pollForPendingLookups:",
        error
      );
      
      // Handle connection failures specifically
      if (error.name === 'MongooseServerSelectionError' || 
          error.message.includes('ECONNREFUSED') ||
          error.message.includes('connection')) {
        await this.handleConnectionFailure(error);
      }
    }
  }
}

// Export singleton instance
module.exports = new PriceLookupPoller();
