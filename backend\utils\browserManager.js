/**
 * browserManager.js
 * Utility for managing a shared Puppeteer browser instance.
 */
const puppeteer = require("puppeteer");
const logger = require("./logger");

let browserInstance = null;

/**
 * Launches and returns a Puppeteer browser instance.
 * If an instance already exists, returns the existing one.
 * @returns {Promise<import('puppeteer').Browser>}
 */
async function launchBrowser() {
  if (browserInstance) {
    logger.debug("[BrowserManager] Returning existing browser instance.");
    return browserInstance;
  }

  logger.info("[BrowserManager] Launching new Puppeteer browser instance...");
  try {
    // Add launch options as needed (e.g., headless mode, args)
    // Consider '--no-sandbox' if running in certain environments, but be aware of security implications.
    const launchOptions = {
      headless: true, // Run headless by default
      args: [
        "--disable-gpu",
        "--no-sandbox",
        "--disable-setuid-sandbox",
        "--disable-dev-shm-usage",
      ],
    };
    browserInstance = await puppeteer.launch(launchOptions);
    logger.info("[BrowserManager] Puppeteer browser launched successfully.");

    browserInstance.on("disconnected", () => {
      logger.warn("[BrowserManager] Puppeteer browser disconnected.");
      browserInstance = null; // Reset instance on disconnect
    });

    return browserInstance;
  } catch (error) {
    logger.error("[BrowserManager] Failed to launch Puppeteer browser:", error);
    browserInstance = null; // Ensure instance is null on failure
    throw error; // Rethrow the error to be handled by the caller
  }
}

/**
 * Returns a new page from the shared browser instance.
 * Launches the browser if it's not already running.
 * @returns {Promise<import('puppeteer').Page>}
 */
async function getPage() {
  const browser = await launchBrowser();
  if (!browser) {
    throw new Error("Browser instance is not available.");
  }
  try {
    logger.debug("[BrowserManager] Opening new page...");
    const page = await browser.newPage();
    // Optional: Set default navigation timeout or viewport
    await page.setDefaultNavigationTimeout(60000); // 60 seconds
    // await page.setViewport({ width: 1280, height: 800 });
    logger.debug("[BrowserManager] New page opened successfully.");
    return page;
  } catch (error) {
    logger.error("[BrowserManager] Failed to open new page:", error);
    throw error;
  }
}

/**
 * Closes the shared browser instance if it exists.
 * @returns {Promise<void>}
 */
async function closeBrowser() {
  if (browserInstance) {
    logger.info("[BrowserManager] Closing Puppeteer browser instance...");
    try {
      await browserInstance.close();
      logger.info("[BrowserManager] Browser instance closed successfully.");
    } catch (error) {
      logger.error("[BrowserManager] Error closing browser instance:", error);
    } finally {
      browserInstance = null; // Ensure instance is reset
    }
  } else {
    logger.debug("[BrowserManager] No active browser instance to close.");
  }
}

// Optional: Add a cleanup handler for process exit
process.on("exit", closeBrowser);
process.on("SIGINT", async () => {
  await closeBrowser();
  process.exit();
});
process.on("SIGTERM", async () => {
  await closeBrowser();
  process.exit();
});

module.exports = {
  launchBrowser,
  getPage,
  closeBrowser,
};
