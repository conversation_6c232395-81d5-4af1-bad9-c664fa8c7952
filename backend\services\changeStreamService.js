const logger = require("../utils/logger");
const priceLookupService = require("./priceLookupService");
const mongoose = require("mongoose"); // Needed for ObjectId
const materialFetchLogger = require("../utils/materialFetchLogger");

// Configuration for batching and debouncing
const CONFIG = {
  DEBOUNCE_MS: 2000, // Wait 2 seconds before processing
  BATCH_SIZE: 10, // Process up to 10 items at once
  MAX_CONCURRENT_LOOKUPS: 5, // Limit concurrent price lookups
  RETRY_DELAY_MS: 5000, // Delay before retrying failed items
  MAX_RETRY_ATTEMPTS: 3,
};

// Store references to active change streams if needed for graceful shutdown
const activeStreams = [];

// Batching and debouncing state
const pendingLookups = new Map(); // quoteId -> { timer, items: Set<itemId> }
let activeLookupCount = 0;
const processingQueue = [];
let isProcessing = false;
const retryQueue = new Map(); // itemKey -> { attempts, lastAttempt }

async function initializeChangeStreams(conn) {
  logger.info(
    `[ChangeStreamService] Initializing. Received 'conn' object type: ${typeof conn}, is Mongoose singleton: ${
      conn === mongoose
    }`
  );
  logger.info(
    `[ChangeStreamService] Mongoose connection readyState: ${mongoose.connection.readyState} (expected: 1 for connected)`
  );
  if (mongoose.connection.readyState !== 1) {
    // 1 means connected
    logger.error(
      `[ChangeStreamService] Mongoose connection is not in 'connected' state (readyState: ${mongoose.connection.readyState}). Aborting initialization.`
    );
    // Optional: Log if the passed 'conn' object also seems problematic, though readyState is primary.
    if (!conn || conn !== mongoose) {
      logger.warn(
        `[ChangeStreamService] Additionally, the passed 'conn' object was either null or not the Mongoose singleton.`
      );
    }
    return;
  }

  try {
    const db = mongoose.connection.db;
    if (!db) {
      logger.error(
        "[ChangeStreamService] mongoose.connection.db is unexpectedly null or undefined even though readyState is 1. Aborting."
      );
      return;
    }
    const quoteCollection = db.collection("quotes");

    // Define the pipeline to watch for both inserts and updates
    // This ensures we catch new quotes and updates to existing ones
    const pipeline = [
      {
        $match: {
          $or: [
            { operationType: "insert" },
            { operationType: "update" },
            { operationType: "replace" },
          ],
        },
      },
    ];

    logger.info(
      '[ChangeStreamService] Setting up change stream for the "quotes" collection...'
    );
    const changeStream = quoteCollection.watch(pipeline, {
      fullDocument: "updateLookup",
    });

    changeStream.on("error", (error) => {
      logger.error("[ChangeStreamService] Error on the change stream:", error);
      // Start fallback poller on Change Stream errors
      const priceLookupPoller = require("./priceLookupPoller");
      if (!priceLookupPoller.isRunning) {
        logger.warn(
          "[ChangeStreamService] Starting fallback poller due to Change Stream error"
        );
        priceLookupPoller.start();
      }
    });

    changeStream.on("close", () => {
      logger.warn("[ChangeStreamService] Change stream closed");
    });

    activeStreams.push(changeStream);
    logger.info(
      "[ChangeStreamService] Change stream successfully created and added to active streams"
    );

    // Test if Change Streams are actually working by checking MongoDB deployment type
    try {
      const adminDb = db.admin();
      const status = await adminDb.replSetGetStatus();
      logger.info(
        "[ChangeStreamService] MongoDB replica set detected, Change Streams should work"
      );
    } catch (replicaError) {
      logger.warn(
        "[ChangeStreamService] MongoDB standalone instance detected, Change Streams may not work"
      );
      logger.warn(
        "[ChangeStreamService] Starting fallback poller as precaution"
      );
      const priceLookupPoller = require("./priceLookupPoller");
      if (!priceLookupPoller.isRunning) {
        priceLookupPoller.start();
      }
    }

    changeStream.on("change", async (change) => {
      logger.info("[ChangeStreamService] Received change event:", {
        operationType: change.operationType,
        documentKey: change.documentKey?._id,
        hasFullDocument: !!change.fullDocument,
      });

      materialFetchLogger.logChangeStreamEvent("CHANGE_RECEIVED", {
        operationType: change.operationType,
        quoteId: change.documentKey?._id,
        hasFullDocument: !!change.fullDocument,
      });

      // Handle both insert and update operations
      if (
        (change.operationType === "insert" ||
          change.operationType === "update" ||
          change.operationType === "replace") &&
        change.fullDocument
      ) {
        const quote = change.fullDocument;
        const quoteId = quote._id.toString();
        const itemsToProcess = [];

        logger.info(
          `[ChangeStreamService] Processing ${
            change.operationType
          } for quote ${quoteId} with ${quote.items?.length || 0} items`
        );
        materialFetchLogger.logChangeStreamActivity("PROCESSING_QUOTE", {
          quoteId,
          operationType: change.operationType,
          itemCount: quote.items?.length || 0,
        });

        // Find all items that need processing
        if (quote.items && Array.isArray(quote.items)) {
          for (let i = 0; i < quote.items.length; i++) {
            const item = quote.items[i];
            const itemId = item._id;

            logger.debug(`[ChangeStreamService] Item ${itemId} details:`, {
              hasLookupResults: !!item.lookup_results,
              lookupResultsCount: item.lookup_results?.length || 0,
              description: item.description || item.name || "No description",
            });

            if (item.lookup_results && Array.isArray(item.lookup_results)) {
              materialFetchLogger.logChangeStreamActivity(
                "ITEM_HAS_LOOKUP_RESULTS",
                {
                  quoteId,
                  itemId: itemId?.toString(),
                  lookupResultCount: item.lookup_results.length,
                  statuses: item.lookup_results.map((lr) => lr.status),
                }
              );

              for (let j = 0; j < item.lookup_results.length; j++) {
                const lookupEntry = item.lookup_results[j];

                // Check if item needs processing
                if (
                  lookupEntry.status === "pending_internal_price_lookup" &&
                  lookupEntry.mcp_request &&
                  lookupEntry.mcp_request.internal_query_details
                ) {
                  // Check if not already in retry cooldown
                  const itemKey = `${quoteId}_${itemId}`;
                  const retryInfo = retryQueue.get(itemKey);
                  if (
                    retryInfo &&
                    Date.now() - retryInfo.lastAttempt < CONFIG.RETRY_DELAY_MS
                  ) {
                    logger.debug(
                      `[ChangeStreamService] Item ${itemId} is in retry cooldown, skipping`
                    );
                    continue;
                  }

                  const originalToolParams = {
                    description: lookupEntry.mcp_request.internal_query_details,
                    userId: quote.createdBy,
                    companyId: quote.company,
                    quantity: item.quantity,
                  };

                  itemsToProcess.push({
                    item,
                    itemId: itemId.toString(),
                    lookupEntry,
                    originalToolParams,
                  });

                  break; // Only process one lookup entry per item at a time
                } else if (
                  lookupEntry.data &&
                  lookupEntry.data.price_lookup_flow_state &&
                  lookupEntry.data.price_lookup_flow_state.next_handler_function
                ) {
                  // Check if this lookup entry was the one just updated by Cascade
                  // This is a heuristic: Cascade updates 'status' and 'data'.
                  // The 'current_stage' should reflect that an MCP action was completed by Cascade
                  // and is now ready for the next handler.

                  // Expected statuses after Cascade completes an MCP tool call and before a handler runs:
                  // 'success' or 'error' (as per memory 060e11c8-cfec-452e-8770-1dc0a5540cb9 for the MCP action itself)
                  // AND a 'next_handler_function' is present.

                  const mcpActionCompleted =
                    (lookupEntry.status === "success" ||
                      lookupEntry.status === "error") &&
                    lookupEntry.mcp_request &&
                    lookupEntry.mcp_request.tool_id; // Confirms it was an MCP action

                  // More specific check: was this *particular* handler meant to be triggered now?
                  // The priceLookupService sets a specific 'current_stage' before Cascade acts,
                  // e.g., 'brave_search_initiated', 'scrape_initiated', 'extraction_initiated'.
                  // After Cascade acts and updates the DB with 'success'/'error', the *next* handler is invoked.
                  // The handler itself will then update current_stage (e.g. 'brave_search_results_processing')
                  // This change stream is primarily concerned with the transition *between* Cascade's action and our handler.

                  if (mcpActionCompleted) {
                    // Check if not already in retry cooldown
                    const itemKey = `${quoteId}_${itemId}`;
                    const retryInfo = retryQueue.get(itemKey);
                    if (
                      retryInfo &&
                      Date.now() - retryInfo.lastAttempt < CONFIG.RETRY_DELAY_MS
                    ) {
                      logger.debug(
                        `[ChangeStreamService] Item ${itemId} is in retry cooldown, skipping`
                      );
                      continue;
                    }

                    itemsToProcess.push({
                      item,
                      itemId: itemId.toString(),
                      lookupEntry,
                      originalToolParams: null, // Not needed for next_handler_function calls
                    });

                    break; // Only process one lookup entry per item at a time
                  }
                }
              }
            }
          }
        }

        // Schedule items for processing with debouncing
        if (itemsToProcess.length > 0) {
          logger.info(
            `[ChangeStreamService] Found ${itemsToProcess.length} items requiring lookup in quote ${quoteId}`
          );
          scheduleLookups(quoteId, itemsToProcess);
        } else {
          logger.info(
            `[ChangeStreamService] No items requiring lookup found in quote ${quoteId}`
          );
          materialFetchLogger.logChangeStreamActivity("NO_ITEMS_TO_PROCESS", {
            quoteId,
          });
        }
      }
    });

    changeStream.on("error", (error) => {
      logger.error(
        '[ChangeStreamService] Error in "quotes" collection change stream:',
        error
      );
      // Consider re-initializing or other recovery mechanisms if critical
    });

    logger.info(
      '[ChangeStreamService] Successfully listening for changes on "quotes" collection.'
    );
    logger.info(
      "[ChangeStreamService] Change stream pipeline:",
      JSON.stringify(pipeline)
    );
    logger.info(
      "[ChangeStreamService] Number of active streams:",
      activeStreams.length
    );
  } catch (error) {
    logger.error(
      "[ChangeStreamService] Failed to initialize change streams:",
      error
    );
    logger.error("[ChangeStreamService] Error stack:", error.stack);
    // Rethrow or handle as appropriate for your application's startup sequence
    throw error; // Re-throw to ensure server.js knows initialization failed
  }
}

/**
 * Schedule lookups with debouncing
 */
function scheduleLookups(quoteId, items) {
  const existingEntry = pendingLookups.get(quoteId);

  if (existingEntry) {
    // Clear existing timer
    clearTimeout(existingEntry.timer);

    // Add new items to the set
    items.forEach((item) => existingEntry.items.add(item));
  } else {
    // Create new entry
    pendingLookups.set(quoteId, {
      items: new Set(items),
      timer: null,
    });
  }

  // Set new timer
  const timer = setTimeout(() => {
    processPendingLookups(quoteId);
  }, CONFIG.DEBOUNCE_MS);

  pendingLookups.get(quoteId).timer = timer;

  logger.debug(
    `[ChangeStreamService] Scheduled ${items.length} items for quote ${quoteId}`
  );
}

/**
 * Process pending lookups for a quote
 */
async function processPendingLookups(quoteId) {
  const entry = pendingLookups.get(quoteId);
  if (!entry || entry.items.size === 0) {
    return;
  }

  // Convert Set to Array and batch
  const items = Array.from(entry.items);
  const batches = createBatches(items, CONFIG.BATCH_SIZE);

  logger.info(
    `[ChangeStreamService] Processing ${items.length} items in ${batches.length} batches for quote ${quoteId}`
  );

  // Process batches with concurrency control
  for (const batch of batches) {
    // Wait if we're at max concurrent lookups
    while (activeLookupCount >= CONFIG.MAX_CONCURRENT_LOOKUPS) {
      await new Promise((resolve) => setTimeout(resolve, 100));
    }

    // Process batch without blocking
    processBatch(quoteId, batch).catch((error) => {
      logger.error("[ChangeStreamService] Error processing batch:", error);
    });
  }

  // Clear from pending
  pendingLookups.delete(quoteId);
}

/**
 * Create batches from items array
 */
function createBatches(items, batchSize) {
  const batches = [];
  for (let i = 0; i < items.length; i += batchSize) {
    batches.push(items.slice(i, i + batchSize));
  }
  return batches;
}

/**
 * Process a batch of items
 */
async function processBatch(quoteId, items) {
  logger.info(
    `[ChangeStreamService] Processing batch of ${items.length} items for quote ${quoteId}`
  );

  // Process items in parallel within the batch
  const promises = items.map(async (itemData) => {
    activeLookupCount++;

    try {
      await processItem(quoteId, itemData);
    } catch (error) {
      logger.error(
        `[ChangeStreamService] Error processing item ${itemData.itemId}:`,
        error
      );
      handleItemError(quoteId, itemData);
    } finally {
      activeLookupCount--;
    }
  });

  await Promise.allSettled(promises);
}

/**
 * Process a single item (extracted from the original change handler logic)
 */
async function processItem(quoteId, itemData) {
  const { item, lookupEntry, originalToolParams } = itemData;
  const itemId = item._id;

  try {
    if (lookupEntry.status === "pending_internal_price_lookup") {
      logger.info(
        `[ChangeStreamService] Invoking initiatePriceLookup for item ${itemId}`
      );
      await priceLookupService.initiatePriceLookup(
        new mongoose.Types.ObjectId(quoteId),
        new mongoose.Types.ObjectId(itemId),
        originalToolParams
      );
    } else if (
      lookupEntry.data?.price_lookup_flow_state?.next_handler_function
    ) {
      const handlerFunction =
        priceLookupService[
          lookupEntry.data.price_lookup_flow_state.next_handler_function
        ];
      if (typeof handlerFunction === "function") {
        logger.info(
          `[ChangeStreamService] Invoking ${lookupEntry.data.price_lookup_flow_state.next_handler_function} for item ${itemId}`
        );
        await handlerFunction(
          new mongoose.Types.ObjectId(quoteId),
          new mongoose.Types.ObjectId(itemId)
        );
      } else {
        logger.error(
          `[ChangeStreamService] Handler function ${lookupEntry.data.price_lookup_flow_state.next_handler_function} not found`
        );
      }
    }
  } catch (error) {
    logger.error(
      `[ChangeStreamService] Error in processItem for ${itemId}:`,
      error
    );
    throw error;
  }
}

/**
 * Handle errors for a specific item with retry logic
 */
function handleItemError(quoteId, itemData) {
  const { item } = itemData;
  const itemKey = `${quoteId}_${item._id}`;

  let retryInfo = retryQueue.get(itemKey) || { attempts: 0, lastAttempt: 0 };
  retryInfo.attempts++;
  retryInfo.lastAttempt = Date.now();

  retryQueue.set(itemKey, retryInfo);

  if (retryInfo.attempts < CONFIG.MAX_RETRY_ATTEMPTS) {
    // Schedule retry
    setTimeout(() => {
      logger.info(
        `[ChangeStreamService] Retrying item ${item._id} (attempt ${
          retryInfo.attempts + 1
        })`
      );
      scheduleLookups(quoteId, [itemData]);
    }, CONFIG.RETRY_DELAY_MS);
  } else {
    logger.error(
      `[ChangeStreamService] Max retries exceeded for item ${item._id}`
    );
  }
}

async function closeChangeStreams() {
  logger.info("[ChangeStreamService] Closing all active change streams...");

  // Clear all pending timers
  for (const [quoteId, entry] of pendingLookups) {
    if (entry.timer) {
      clearTimeout(entry.timer);
    }
  }
  pendingLookups.clear();

  // Close all streams
  for (const stream of activeStreams) {
    await stream.close();
  }
  logger.info("[ChangeStreamService] All active change streams closed.");
}

/**
 * Get service statistics for monitoring
 */
function getStats() {
  return {
    pendingLookups: pendingLookups.size,
    pendingItems: Array.from(pendingLookups.values()).reduce(
      (sum, entry) => sum + entry.items.size,
      0
    ),
    activeLookupCount,
    retryQueueSize: retryQueue.size,
    config: CONFIG,
  };
}

module.exports = {
  initializeChangeStreams,
  closeChangeStreams, // For graceful shutdown
  getStats, // For monitoring
};
