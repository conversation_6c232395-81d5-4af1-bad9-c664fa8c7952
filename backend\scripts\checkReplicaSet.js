const mongoose = require("mongoose");

async function checkReplicaSet() {
  try {
    console.log("Connecting to MongoDB...");
    await mongoose.connect(
      process.env.MONGODB_URI ||
        "mongodb://localhost:27017/workiz_clone?replicaSet=rs0",
      {
        serverSelectionTimeoutMS: 5000,
      }
    );

    console.log("Connected to MongoDB");

    // Check replica set status
    const admin = mongoose.connection.db.admin();
    const replSetStatus = await admin.command({ replSetGetStatus: 1 });

    console.log("Replica Set Status:");
    console.log("- Set:", replSetStatus.set);
    console.log("- Members:", replSetStatus.members.length);
    console.log(
      "- Primary:",
      replSetStatus.members.find((m) => m.stateStr === "PRIMARY")?.name
    );

    // Check if change streams are supported
    const db = mongoose.connection.db;
    const collection = db.collection("test_change_stream");

    console.log("\nTesting change stream support...");
    const changeStream = collection.watch();

    changeStream.on("change", (change) => {
      console.log("Change detected:", change);
    });

    // Insert a test document
    await collection.insertOne({ test: true, timestamp: new Date() });

    // Wait a bit for the change stream to process
    await new Promise((resolve) => setTimeout(resolve, 1000));

    await changeStream.close();
    console.log("Change streams are supported!");

    // Cleanup
    await collection.deleteMany({ test: true });

    process.exit(0);
  } catch (error) {
    console.error("Error:", error.message);
    if (
      error.code === "NoReplicaSetFound" ||
      error.message.includes("replica set")
    ) {
      console.error("\nReplica set not properly initialized!");
      console.error("To initialize, connect to MongoDB and run:");
      console.error("rs.initiate()");
    }
    process.exit(1);
  }
}

checkReplicaSet();
