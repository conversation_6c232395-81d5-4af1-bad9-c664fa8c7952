/**
 * visionService.js
 * Service for interacting with Google Cloud Vision API.
 */

const { ImageAnnotatorClient } = require("@google-cloud/vision");
const logger = require("../utils/logger");
const { withRetry } = require("../utils/retryUtils");
const { isRetryableError, getRetryDelay } = require("../utils/aiErrors"); // Use common retry helpers

// Initialize the Vision AI Client
// Assumes GOOGLE_APPLICATION_CREDENTIALS environment variable is set
// or running in a GCP environment with appropriate service account permissions.
let visionClient;
try {
  visionClient = new ImageAnnotatorClient();
  logger.info("Google Vision AI Client initialized successfully.");
} catch (error) {
  logger.error("Failed to initialize Google Vision AI Client:", error);
  // Depending on requirements, you might want to throw the error
  // or allow the application to continue with Vision features disabled.
  visionClient = null;
}

// Track health metrics
const healthMetrics = {
  totalRequests: 0,
  successfulRequests: 0,
  failedRequests: 0,
  retryAttempts: 0,
  lastRequestTime: null,
  lastErrorTime: null,
  lastErrorMessage: null,
  initialized: !!visionClient,
};

/**
 * Analyzes an image buffer using Google Vision API for text and labels.
 * @param {Buffer} imageBuffer - The buffer containing the image data.
 * @returns {Promise<{text: string|null, labels: string[]}>} - An object containing extracted text and labels.
 */
async function analyzeImageForSearch(imageBuffer) {
  if (!visionClient) {
    logger.warn("Vision AI Client not initialized. Skipping image analysis.");
    return { text: null, labels: [] };
  }

  if (!imageBuffer || !Buffer.isBuffer(imageBuffer)) {
    logger.error("Invalid image buffer provided to analyzeImageForSearch.");
    return { text: null, labels: [] };
  }

  // Define a specific retry condition for Vision API errors
  // Google Cloud errors often have a 'code' property. Common retryable codes include:
  // 13 (INTERNAL), 14 (UNAVAILABLE), 4 (DEADLINE_EXCEEDED), 8 (RESOURCE_EXHAUSTED - sometimes retryable)
  // We'll start with general network/5xx and potentially refine based on observed error codes.
  const isVisionRetryable = (error) => {
    const code = error?.code;
    const status = error?.response?.status; // Check for HTTP status if available
    return (
      isRetryableError(error) || // Check for generic network errors
      (status && status >= 500) || // Check for 5xx HTTP errors
      [4, 8, 13, 14].includes(code)
    ); // Check for specific gRPC codes
  };

  healthMetrics.totalRequests++;
  healthMetrics.lastRequestTime = new Date();

  try {
    const request = {
      image: {
        content: imageBuffer.toString("base64"), // Send image content as base64
      },
      features: [
        { type: "TEXT_DETECTION" }, // OCR
        { type: "LABEL_DETECTION", maxResults: 10 }, // Object/Scene Labels
      ],
    };

    logger.debug("Sending request to Google Vision API...");
    // Wrap the API call with retry logic
    const [result] = await withRetry(
      () => visionClient.annotateImage(request),
      {
        maxRetries: 3,
        baseDelay: 600,
        maxDelay: 6000,
        shouldRetry: isVisionRetryable,
        getRetryDelay: getRetryDelay,
        onRetry: (error, attempt, delay) => {
          healthMetrics.retryAttempts++;
          logger.warn(
            `Retrying Vision API call (Attempt ${attempt}) after ${delay}ms due to error: ${error.message} (Code: ${error.code})`
          );
        },
      }
    );
    logger.debug("Received response from Google Vision API.");

    healthMetrics.successfulRequests++;

    // Extract Text (OCR)
    const textAnnotations = result.textAnnotations;
    const fullText =
      textAnnotations && textAnnotations.length > 0
        ? textAnnotations[0].description // The first annotation usually contains the full detected text block
        : null;

    // Extract Labels
    const labelAnnotations = result.labelAnnotations;
    const labels = labelAnnotations
      ? labelAnnotations.map((label) => label.description)
      : [];

    logger.info(
      `Vision analysis complete. Text found: ${!!fullText}, Labels: ${labels.join(
        ", "
      )}`
    );

    return {
      text: fullText ? fullText.replace(/\n/g, " ").trim() : null, // Clean up text
      labels: labels,
    };
  } catch (error) {
    healthMetrics.failedRequests++;
    healthMetrics.lastErrorTime = new Date();
    healthMetrics.lastErrorMessage = error.message;
    logger.error("Google Vision API request failed:", error);
    // Re-throw the error to allow callers to handle specific failures
    throw error;
  }
}

/**
 * Get health metrics for the Vision service
 * @returns {Object} Health metrics
 */
function getHealthMetrics() {
  return {
    ...healthMetrics,
    status: !visionClient
      ? "UNINITIALIZED"
      : healthMetrics.failedRequests > healthMetrics.successfulRequests
      ? "DEGRADED"
      : "HEALTHY",
    successRate:
      healthMetrics.totalRequests > 0
        ? Math.round(
            (healthMetrics.successfulRequests / healthMetrics.totalRequests) *
              100
          )
        : null,
    circuitBreakerState: {
      state: !visionClient ? "OPEN" : "CLOSED",
      failureCount: healthMetrics.failedRequests,
      lastFailureTime: healthMetrics.lastErrorTime,
    },
  };
}

/**
 * Categorize an image for construction project documentation (before/after/other)
 * @param {string|Buffer} imageInput - Path to image file or image buffer
 * @returns {Promise<{category: string, confidence: number, labels: string[]}>} - Categorization result
 */
async function categorizeConstructionImage(imageInput) {
  if (!visionClient) {
    logger.warn("Vision AI Client not initialized. Cannot categorize image.");
    return { category: "other", confidence: 0, labels: [] };
  }

  healthMetrics.totalRequests++;
  healthMetrics.lastRequestTime = new Date();

  try {
    let imageBuffer;

    // Handle both file path and buffer inputs
    if (typeof imageInput === "string") {
      // It's a file path
      const fs = require("fs").promises;
      imageBuffer = await fs.readFile(imageInput);
    } else if (Buffer.isBuffer(imageInput)) {
      imageBuffer = imageInput;
    } else {
      throw new Error("Invalid image input. Must be file path or Buffer.");
    }

    const request = {
      image: {
        content: imageBuffer.toString("base64"),
      },
      features: [
        { type: "LABEL_DETECTION", maxResults: 20 },
        { type: "OBJECT_LOCALIZATION", maxResults: 10 },
        { type: "TEXT_DETECTION" },
        { type: "IMAGE_PROPERTIES" },
      ],
    };

    logger.debug("Sending categorization request to Google Vision API...");

    const [result] = await withRetry(
      () => visionClient.annotateImage(request),
      {
        maxRetries: 3,
        baseDelay: 600,
        maxDelay: 6000,
        shouldRetry: isRetryableError,
        getRetryDelay: getRetryDelay,
        onRetry: (error, attempt, delay) => {
          healthMetrics.retryAttempts++;
          logger.warn(
            `Retrying Vision API categorization (Attempt ${attempt}) after ${delay}ms`
          );
        },
      }
    );

    healthMetrics.successfulRequests++;

    // Extract all detected features
    const labels = result.labelAnnotations
      ? result.labelAnnotations.map((label) => ({
          description: label.description.toLowerCase(),
          score: label.score,
        }))
      : [];

    const objects = result.localizedObjectAnnotations
      ? result.localizedObjectAnnotations.map((obj) => ({
          name: obj.name.toLowerCase(),
          score: obj.score,
        }))
      : [];

    const hasText = result.textAnnotations && result.textAnnotations.length > 0;
    const dominantColors =
      result.imagePropertiesAnnotation?.dominantColors?.colors || [];

    // Categorization logic for construction images
    let category = "other";
    let confidence = 0;

    // Keywords indicating different stages
    const beforeKeywords = [
      "construction site",
      "demolition",
      "excavation",
      "foundation",
      "bare wall",
      "exposed",
      "old",
      "damaged",
      "repair needed",
      "before renovation",
      "unfinished",
      "raw",
      "framework",
      "scaffolding",
      "debris",
      "rubble",
      "dirt",
      "mud",
    ];

    const afterKeywords = [
      "finished",
      "completed",
      "renovated",
      "new",
      "clean",
      "painted",
      "installed",
      "modern",
      "polished",
      "furnished",
      "interior design",
      "decoration",
      "lighting fixture",
      "appliance",
      "cabinet",
      "flooring",
      "tile",
      "landscaping",
    ];

    const duringKeywords = [
      "working",
      "installing",
      "building",
      "construction worker",
      "tools",
      "equipment",
      "in progress",
      "partially complete",
      "wiring",
      "plumbing",
      "framing",
      "drywall",
    ];

    // Check labels and objects against keywords
    const allDetections = [
      ...labels.map((l) => ({ text: l.description, score: l.score })),
      ...objects.map((o) => ({ text: o.name, score: o.score })),
    ];

    let beforeScore = 0,
      afterScore = 0,
      duringScore = 0;

    for (const detection of allDetections) {
      for (const keyword of beforeKeywords) {
        if (detection.text.includes(keyword)) {
          beforeScore += detection.score;
        }
      }
      for (const keyword of afterKeywords) {
        if (detection.text.includes(keyword)) {
          afterScore += detection.score;
        }
      }
      for (const keyword of duringKeywords) {
        if (detection.text.includes(keyword)) {
          duringScore += detection.score;
        }
      }
    }

    // Determine category based on scores
    if (afterScore > beforeScore && afterScore > duringScore) {
      category = "after";
      confidence = Math.min(afterScore / allDetections.length, 1);
    } else if (beforeScore > afterScore && beforeScore > duringScore) {
      category = "before";
      confidence = Math.min(beforeScore / allDetections.length, 1);
    } else if (duringScore > 0) {
      category = "during";
      confidence = Math.min(duringScore / allDetections.length, 1);
    }

    // If confidence is too low, default to 'other'
    if (confidence < 0.3) {
      category = "other";
    }

    logger.info(
      `Image categorized as '${category}' with confidence ${confidence.toFixed(
        2
      )}`
    );

    return {
      category,
      confidence,
      labels: labels.slice(0, 10).map((l) => l.description), // Return top 10 labels
      objects: objects.slice(0, 5).map((o) => o.name), // Return top 5 objects
      hasText,
      dominantColors: dominantColors.slice(0, 3).map((c) => ({
        rgb: c.color,
        score: c.score,
      })),
    };
  } catch (error) {
    healthMetrics.failedRequests++;
    healthMetrics.lastErrorTime = new Date();
    healthMetrics.lastErrorMessage = error.message;
    logger.error("Image categorization failed:", error);

    // Return default category on error
    return {
      category: "other",
      confidence: 0,
      labels: [],
      error: error.message,
    };
  }
}

/**
 * Reset health metrics
 */
function resetHealthMetrics() {
  healthMetrics.totalRequests = 0;
  healthMetrics.successfulRequests = 0;
  healthMetrics.failedRequests = 0;
  healthMetrics.retryAttempts = 0;
  healthMetrics.lastRequestTime = null;
  healthMetrics.lastErrorTime = null;
  healthMetrics.lastErrorMessage = null;
  // Don't reset 'initialized' as it depends on visionClient state
}

module.exports = {
  analyzeImageForSearch,
  categorizeConstructionImage,
  getHealthMetrics,
  resetHealthMetrics,
};
