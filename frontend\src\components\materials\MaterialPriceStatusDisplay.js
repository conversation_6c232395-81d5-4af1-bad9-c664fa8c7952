/**
 * Material Price Status Display Component
 * Shows real-time status of material price lookups in quotes
 */

import React, { useEffect, useState, useCallback } from "react";
import {
  Box,
  Typography,
  LinearProgress,
  Alert,
  Chip,
  Collapse,
  IconButton,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Paper,
  Tooltip,
  CircularProgress,
} from "@mui/material";
import {
  ExpandMore as ExpandMoreIcon,
  ExpandLess as ExpandLessIcon,
  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon,
  HourglassEmpty as PendingIcon,
  Info as InfoIcon,
  Refresh as RefreshIcon,
  PlayArrow as TriggerIcon,
} from "@mui/icons-material";
import materialMonitor from "../../utils/materialMonitor";
import logger from "../../utils/logger";

const MaterialPriceStatusDisplay = ({
  quoteId,
  onStatusUpdate,
  autoRefresh = true,
}) => {
  const [status, setStatus] = useState(null);
  const [loading, setLoading] = useState(true);
  const [expanded, setExpanded] = useState(false);
  const [progress, setProgress] = useState({ percentage: 0, message: "" });

  // Handle status updates from monitor
  const handleStatusUpdate = useCallback(
    (newStatus) => {
      logger.debug(
        "[MaterialPriceStatusDisplay] Received status update:",
        newStatus
      );
      setStatus(newStatus);
      setLoading(false);

      // Calculate progress
      if (newStatus && newStatus.summary) {
        const prog = materialMonitor.calculateProgress(newStatus);
        setProgress(prog);
      }

      // Notify parent if callback provided
      if (onStatusUpdate) {
        onStatusUpdate(newStatus);
      }
    },
    [onStatusUpdate]
  );

  // Start monitoring when component mounts
  useEffect(() => {
    if (!quoteId) {
      logger.warn("[MaterialPriceStatusDisplay] No quoteId provided");
      setLoading(false);
      return;
    }

    logger.info(
      `[MaterialPriceStatusDisplay] Starting monitoring for quote ${quoteId}`
    );

    // Start monitoring
    materialMonitor.startMonitoring(quoteId, handleStatusUpdate);

    // Cleanup on unmount
    return () => {
      logger.info(
        `[MaterialPriceStatusDisplay] Stopping monitoring for quote ${quoteId}`
      );
      materialMonitor.stopMonitoring(quoteId);
    };
  }, [quoteId, handleStatusUpdate]);

  // Manual refresh
  const handleRefresh = async () => {
    setLoading(true);
    await materialMonitor.fetchQuoteStatus(quoteId);
  };

  // Manual trigger price lookup
  const handleTriggerLookup = async () => {
    try {
      setLoading(true);
      const token = localStorage.getItem("token");

      logger.info(
        `[MaterialPriceStatusDisplay] Manually triggering price lookup for quote ${quoteId}`
      );

      const response = await fetch(
        `/api/material-status/trigger-lookup/${quoteId}`,
        {
          method: "POST",
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json",
          },
        }
      );

      if (response.ok) {
        const result = await response.json();
        logger.info(
          "[MaterialPriceStatusDisplay] Manual trigger result:",
          result
        );

        // Refresh status after triggering
        await materialMonitor.fetchQuoteStatus(quoteId);

        // Show success message
        if (result.itemsProcessed > 0) {
          // Can add a snackbar here if needed
          logger.info(
            `[MaterialPriceStatusDisplay] Triggered lookup for ${result.itemsProcessed} items`
          );
        }
      } else {
        logger.error(
          "[MaterialPriceStatusDisplay] Failed to trigger manual lookup:",
          response.statusText
        );
      }
    } catch (error) {
      logger.error(
        "[MaterialPriceStatusDisplay] Error triggering manual lookup:",
        error
      );
    } finally {
      setLoading(false);
    }
  };

  // Don't render if no quote ID
  if (!quoteId) {
    return (
      <Paper variant="outlined" sx={{ p: 2, mb: 2, borderColor: "info.main" }}>
        <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
          <InfoIcon color="info" />
          <Typography variant="body2" color="text.secondary">
            Material price lookups will appear here after saving the quote
          </Typography>
        </Box>
      </Paper>
    );
  }

  // Loading state
  if (loading && !status) {
    return (
      <Box sx={{ display: "flex", alignItems: "center", gap: 1, mb: 2 }}>
        <CircularProgress size={20} />
        <Typography variant="body2">
          Loading material price status...
        </Typography>
      </Box>
    );
  }

  // Error state
  if (status && !status.success) {
    return (
      <Alert severity="error" sx={{ mb: 2 }}>
        Failed to load material price status: {status.error || "Unknown error"}
      </Alert>
    );
  }

  // No data state
  if (!status || !status.itemStatuses || status.itemStatuses.length === 0) {
    return null;
  }

  // Check if any lookups are active
  const hasActiveLookups = status.itemStatuses.some(
    (item) => item.pendingCount > 0
  );
  const hasFailedLookups = status.itemStatuses.some(
    (item) => item.failedCount > 0
  );

  // Determine alert severity
  let alertSeverity = "info";
  if (hasFailedLookups) alertSeverity = "warning";
  if (hasActiveLookups) alertSeverity = "info";
  if (progress.percentage === 100 && !hasFailedLookups)
    alertSeverity = "success";

  return (
    <Paper variant="outlined" sx={{ p: 2, mb: 2, borderColor: "primary.main" }}>
      <Box
        sx={{
          display: "flex",
          alignItems: "center",
          justifyContent: "space-between",
          mb: 1,
        }}
      >
        <Typography
          variant="subtitle1"
          sx={{ fontWeight: "bold", color: "primary.main" }}
        >
          Material Price Lookup Status
        </Typography>
        <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
          {autoRefresh && hasActiveLookups && (
            <Chip
              size="small"
              label="Auto-refreshing"
              color="info"
              variant="outlined"
              icon={<RefreshIcon sx={{ fontSize: 16 }} />}
            />
          )}
          <Tooltip title="Refresh status">
            <IconButton size="small" onClick={handleRefresh} disabled={loading}>
              <RefreshIcon />
            </IconButton>
          </Tooltip>
          {hasActiveLookups === false && (
            <Tooltip title="Manually trigger price lookups">
              <IconButton
                size="small"
                onClick={handleTriggerLookup}
                disabled={loading}
              >
                <TriggerIcon />
              </IconButton>
            </Tooltip>
          )}
          <IconButton
            size="small"
            onClick={() => setExpanded(!expanded)}
            aria-expanded={expanded}
            aria-label="show more"
          >
            {expanded ? <ExpandLessIcon /> : <ExpandMoreIcon />}
          </IconButton>
        </Box>
      </Box>

      {/* Progress bar */}
      <Box sx={{ mb: 2 }}>
        <Box
          sx={{
            display: "flex",
            alignItems: "center",
            justifyContent: "space-between",
            mb: 0.5,
          }}
        >
          <Typography variant="body2" color="text.secondary">
            {progress.message}
          </Typography>
          <Typography variant="body2" color="text.secondary">
            {progress.percentage}%
          </Typography>
        </Box>
        <LinearProgress
          variant="determinate"
          value={progress.percentage}
          sx={{
            height: 8,
            borderRadius: 1,
            backgroundColor: "grey.300",
            "& .MuiLinearProgress-bar": {
              borderRadius: 1,
              backgroundColor: hasFailedLookups
                ? "warning.main"
                : "primary.main",
            },
          }}
        />
      </Box>

      {/* Summary alert */}
      {(hasActiveLookups || hasFailedLookups) && (
        <Alert severity={alertSeverity} sx={{ mb: 2 }}>
          {hasActiveLookups && (
            <Typography variant="body2">
              Price lookups in progress for {status.summary.itemsPending}{" "}
              item(s). This may take a few moments...
            </Typography>
          )}
          {hasFailedLookups && !hasActiveLookups && (
            <Typography variant="body2">
              {status.summary.itemsFailed} item(s) failed price lookup. You may
              need to set prices manually.
            </Typography>
          )}
        </Alert>
      )}

      {/* Detailed item status (collapsible) */}
      <Collapse in={expanded} timeout="auto" unmountOnExit>
        <Typography variant="subtitle2" sx={{ mb: 1, fontWeight: "bold" }}>
          Item Details:
        </Typography>
        <List dense sx={{ maxHeight: 300, overflow: "auto" }}>
          {status.itemStatuses.map((item, index) => {
            const statusMessage = materialMonitor.getStatusMessage(item);
            const statusSeverity = materialMonitor.getStatusSeverity(item);

            return (
              <ListItem key={item.itemId || index}>
                <ListItemIcon>
                  {statusSeverity === "success" && (
                    <CheckCircleIcon color="success" />
                  )}
                  {statusSeverity === "error" && <ErrorIcon color="error" />}
                  {statusSeverity === "info" && <PendingIcon color="info" />}
                  {statusSeverity === "warning" && <InfoIcon color="warning" />}
                  {statusSeverity === "default" && <InfoIcon />}
                </ListItemIcon>
                <ListItemText
                  primary={item.name || `Item ${index + 1}`}
                  secondary={
                    <Box>
                      <Typography variant="caption" display="block">
                        {statusMessage}
                      </Typography>
                      {item.lastLookup && (
                        <Typography
                          variant="caption"
                          color="text.secondary"
                          display="block"
                        >
                          Last attempt:{" "}
                          {new Date(
                            item.lastLookup.timestamp
                          ).toLocaleTimeString()}
                          {item.lastLookup.error &&
                            ` - Error: ${item.lastLookup.error}`}
                        </Typography>
                      )}
                    </Box>
                  }
                />
              </ListItem>
            );
          })}
        </List>
      </Collapse>

      {/* Active lookups details */}
      {status.activeLookups && status.activeLookups.length > 0 && (
        <Box sx={{ mt: 2, p: 1, bgcolor: "info.light", borderRadius: 1 }}>
          <Typography variant="subtitle2" sx={{ mb: 1 }}>
            Active Lookups:
          </Typography>
          {status.activeLookups.map((lookup, index) => (
            <Box key={index} sx={{ mb: 1 }}>
              <Typography variant="caption" display="block">
                {lookup.description} - {lookup.source}
              </Typography>
              <Typography variant="caption" color="text.secondary">
                Started: {new Date(lookup.startTime).toLocaleTimeString()}
                {lookup.duration && ` (${Math.round(lookup.duration / 1000)}s)`}
              </Typography>
            </Box>
          ))}
        </Box>
      )}
    </Paper>
  );
};

export default MaterialPriceStatusDisplay;
