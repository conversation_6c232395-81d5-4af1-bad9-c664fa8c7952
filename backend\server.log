
> workiz-clone-backend@1.0.0 start
> docker-compose -f ../docker-compose.yml up -d && node server.js

 Container workiz-mongo-rs  Running
 Container workiz-redis  Running
Using MongoDB connection string: mongodb://localhost:27017/workiz_clone?replicaSet=rs0
🔧 [LOGGER DEBUG] Environment Variables:
  NODE_ENV: development
  LOG_LEVEL: debug
  Process argv: []
🔧 [LOGGER DEBUG] Resolved log level: debug
[Server Start] LOG_LEVEL environment variable: debug
[Server Start] Logger configured level: debug
2025-08-28 03:12:21 [[34mdebug[39m] [general]: [GeminiService Module Load] typeof AiServiceError: function, AiServiceError: undefined, typeof GoogleGenerativeAIResponseError: function, GoogleGenerativeAIResponseError: undefined | {"service":"workiz-api"}
2025-08-28 03:12:21 [[32minfo[39m] [general]: AI Model Configuration initialized with the following models: | {"service":"workiz-api"}
2025-08-28 03:12:21 [[32minfo[39m] [general]: Primary Model: models/gemini-2.5-pro-preview-03-25 (5 RPM, JSON: true) | {"service":"workiz-api"}
2025-08-28 03:12:21 [[32minfo[39m] [general]: Fallback 1: models/gemini-2.0-flash (2 RPM, JSON: true) | {"service":"workiz-api"}
2025-08-28 03:12:21 [[32minfo[39m] [general]: Fallback 2: models/gemini-1.5-flash (15 RPM, JSON: true) | {"service":"workiz-api"}
2025-08-28 03:12:21 [[32minfo[39m] [general]: Fallback 3: models/gemini-2.0-flash-lite (30 RPM, JSON: false) | {"service":"workiz-api"}
2025-08-28 03:12:24 [[32minfo[39m] [general]: Starting async server setup... | {"service":"workiz-api"}
2025-08-28 03:12:24 [[32minfo[39m] [general]: AI generation log file cleaned successfully | {"service":"workiz-api"}
2025-08-28 03:12:24 [[32minfo[39m] [general]: Connecting to Database... | {"service":"workiz-api"}
Mongoose connected to MongoDB
MongoDB Connected: localhost
2025-08-28 03:12:24 [[32minfo[39m] [general]: Database connection established. | {"service":"workiz-api"}
2025-08-28 03:12:24 [[32minfo[39m] [general]: Initializing Change Stream Service... | {"service":"workiz-api"}
2025-08-28 03:12:24 [[32minfo[39m] [general]: [ChangeStreamService] Initializing. Received 'conn' object type: object, is Mongoose singleton: true | {"service":"workiz-api"}
2025-08-28 03:12:24 [[32minfo[39m] [general]: [ChangeStreamService] Mongoose connection readyState: 1 (expected: 1 for connected) | {"service":"workiz-api"}
2025-08-28 03:12:24 [[32minfo[39m] [general]: [ChangeStreamService] Setting up change stream for the "quotes" collection... | {"service":"workiz-api"}
2025-08-28 03:12:24 [[32minfo[39m] [general]: [ChangeStreamService] Change stream successfully created and added to active streams | {"service":"workiz-api"}
2025-08-28 03:12:24 [[32minfo[39m] [general]: [ChangeStreamService] MongoDB replica set detected, Change Streams should work | {"service":"workiz-api"}
2025-08-28 03:12:24 [[32minfo[39m] [general]: [ChangeStreamService] Successfully listening for changes on "quotes" collection. | {"service":"workiz-api"}
2025-08-28 03:12:24 [[32minfo[39m] [general]: [ChangeStreamService] Change stream pipeline: | {"service":"workiz-api"}
2025-08-28 03:12:24 [[32minfo[39m] [general]: [ChangeStreamService] Number of active streams: | {"service":"workiz-api"}
2025-08-28 03:12:24 [[32minfo[39m] [general]: Change Stream Service initialized successfully. | {"service":"workiz-api"}
2025-08-28 03:12:24 [[32minfo[39m] [general]: Initializing Scraper Service... | {"service":"workiz-api"}
2025-08-28 03:12:24 [[32minfo[39m] [general]: Found 3 enabled material sources in DB. | {"service":"workiz-api"}
2025-08-28 03:12:24 [[34mdebug[39m] [general]: Enabled Source Found: ID=682d20deccc41cc693d861e0, Name=Home Depot Test, Type=HOME_DEPOT | {"service":"workiz-api"}
2025-08-28 03:12:24 [[34mdebug[39m] [general]: Enabled Source Found: ID=682bdb0e477e692811d861e0, Name=Home Depot, Type=HOME_DEPOT | {"service":"workiz-api"}
2025-08-28 03:12:24 [[34mdebug[39m] [general]: Enabled Source Found: ID=682bdb1305cc8e619ad861e0, Name=Platt Electric Supply, Type=PLATT | {"service":"workiz-api"}
2025-08-28 03:12:24 [[32minfo[39m] [general]: Initializing scrapers for 3 sources... | {"service":"workiz-api"}
2025-08-28 03:12:24 [[34mdebug[39m] [general]: Attempting to initialize scraper for source: Home Depot Test (ID: 682d20deccc41cc693d861e0, Type: HOME_DEPOT) | {"service":"workiz-api"}
2025-08-28 03:12:24 [[34mdebug[39m] [general]: [InitScraper:Home Depot Test] Attempting to instantiate scraper class for type: HOME_DEPOT | {"service":"workiz-api"}
2025-08-28 03:12:24 [[32minfo[39m] [general]: [InitScraper:Home Depot Test] Creating Crawl4AI scraper for type: HOME_DEPOT | {"service":"workiz-api"}
2025-08-28 03:12:24 [[34mdebug[39m] [general]: [Crawl4AI ScraperFactory] Creating scraper for source type: HOME_DEPOT | {"service":"workiz-api"}
2025-08-28 03:12:24 [[34mdebug[39m] [general]: [InitScraper:Home Depot Test] Scraper class instantiated successfully. | {"service":"workiz-api"}
2025-08-28 03:12:24 [[34mdebug[39m] [general]: [InitScraper:Home Depot Test] Calling scraper.initialize()... | {"service":"workiz-api"}
2025-08-28 03:12:24 [[32minfo[39m] [general]: Initializing Home Depot Test scraper with Crawl4AI | {"service":"workiz-api"}
2025-08-28 03:12:24 [[34mdebug[39m] [general]: Testing Python executable: python3 | {"service":"workiz-api"}
2025-08-28 03:12:24 [[32minfo[39m] [general]: Successfully detected Python executable: python3 (Python 3.10.12) | {"service":"workiz-api"}
2025-08-28 03:12:24 [[32minfo[39m] [general]: Using Python executable: python3 | {"service":"workiz-api"}
2025-08-28 03:12:25 [[32minfo[39m] [general]: Crawl4AI service initialized successfully | {"service":"workiz-api"}
2025-08-28 03:12:25 [[32minfo[39m] [general]: Initializing Home Depot Crawl4AI scraper | {"service":"workiz-api"}
2025-08-28 03:12:25 [[32minfo[39m] [general]: Scraper for Home Depot Test initialized successfully | {"service":"workiz-api"}
2025-08-28 03:12:25 [[34mdebug[39m] [general]: [InitScraper:Home Depot Test] scraper.initialize() completed. | {"service":"workiz-api"}
2025-08-28 03:12:25 [[34mdebug[39m] [general]: Added Home Depot Test scraper instance to map with key 682d20deccc41cc693d861e0. | {"service":"workiz-api"}
2025-08-28 03:12:25 [[32minfo[39m] [general]: Successfully initialized and added scraper for Home Depot Test. | {"service":"workiz-api"}
2025-08-28 03:12:25 [[34mdebug[39m] [general]: Source type map updated: HOME_DEPOT -> 682d20deccc41cc693d861e0 | {"service":"workiz-api"}
2025-08-28 03:12:25 [[34mdebug[39m] [general]: Attempting to initialize scraper for source: Home Depot (ID: 682bdb0e477e692811d861e0, Type: HOME_DEPOT) | {"service":"workiz-api"}
2025-08-28 03:12:25 [[34mdebug[39m] [general]: [InitScraper:Home Depot] Attempting to instantiate scraper class for type: HOME_DEPOT | {"service":"workiz-api"}
2025-08-28 03:12:25 [[32minfo[39m] [general]: [InitScraper:Home Depot] Creating Crawl4AI scraper for type: HOME_DEPOT | {"service":"workiz-api"}
2025-08-28 03:12:25 [[34mdebug[39m] [general]: [Crawl4AI ScraperFactory] Creating scraper for source type: HOME_DEPOT | {"service":"workiz-api"}
2025-08-28 03:12:25 [[34mdebug[39m] [general]: [InitScraper:Home Depot] Scraper class instantiated successfully. | {"service":"workiz-api"}
2025-08-28 03:12:25 [[34mdebug[39m] [general]: [InitScraper:Home Depot] Calling scraper.initialize()... | {"service":"workiz-api"}
2025-08-28 03:12:25 [[32minfo[39m] [general]: Initializing Home Depot scraper with Crawl4AI | {"service":"workiz-api"}
2025-08-28 03:12:25 [[32minfo[39m] [general]: Initializing Home Depot Crawl4AI scraper | {"service":"workiz-api"}
2025-08-28 03:12:25 [[32minfo[39m] [general]: Scraper for Home Depot initialized successfully | {"service":"workiz-api"}
2025-08-28 03:12:25 [[34mdebug[39m] [general]: [InitScraper:Home Depot] scraper.initialize() completed. | {"service":"workiz-api"}
2025-08-28 03:12:25 [[34mdebug[39m] [general]: Added Home Depot scraper instance to map with key 682bdb0e477e692811d861e0. | {"service":"workiz-api"}
2025-08-28 03:12:25 [[32minfo[39m] [general]: Successfully initialized and added scraper for Home Depot. | {"service":"workiz-api"}
2025-08-28 03:12:25 [[34mdebug[39m] [general]: Source type map updated: HOME_DEPOT -> 682bdb0e477e692811d861e0 | {"service":"workiz-api"}
2025-08-28 03:12:25 [[34mdebug[39m] [general]: Attempting to initialize scraper for source: Platt Electric Supply (ID: 682bdb1305cc8e619ad861e0, Type: PLATT) | {"service":"workiz-api"}
2025-08-28 03:12:25 [[34mdebug[39m] [general]: [InitScraper:Platt Electric Supply] Attempting to instantiate scraper class for type: PLATT | {"service":"workiz-api"}
2025-08-28 03:12:25 [[32minfo[39m] [general]: [InitScraper:Platt Electric Supply] Creating Crawl4AI scraper for type: PLATT | {"service":"workiz-api"}
2025-08-28 03:12:25 [[34mdebug[39m] [general]: [Crawl4AI ScraperFactory] Creating scraper for source type: PLATT | {"service":"workiz-api"}
2025-08-28 03:12:25 [[34mdebug[39m] [general]: [InitScraper:Platt Electric Supply] Scraper class instantiated successfully. | {"service":"workiz-api"}
2025-08-28 03:12:25 [[34mdebug[39m] [general]: [InitScraper:Platt Electric Supply] Calling scraper.initialize()... | {"service":"workiz-api"}
2025-08-28 03:12:25 [[32minfo[39m] [general]: Initializing Platt Electric Supply scraper with Crawl4AI | {"service":"workiz-api"}
2025-08-28 03:12:25 [[32minfo[39m] [general]: [Platt Crawl4AI] Initializing Platt scraper with enhanced anti-bot measures | {"service":"workiz-api"}
2025-08-28 03:12:29 [[32minfo[39m] [general]: [Platt Crawl4AI] Successfully connected to Platt website | {"service":"workiz-api"}
2025-08-28 03:12:29 [[32minfo[39m] [general]: Scraper for Platt Electric Supply initialized successfully | {"service":"workiz-api"}
2025-08-28 03:12:29 [[34mdebug[39m] [general]: [InitScraper:Platt Electric Supply] scraper.initialize() completed. | {"service":"workiz-api"}
2025-08-28 03:12:29 [[34mdebug[39m] [general]: Added Platt Electric Supply scraper instance to map with key 682bdb1305cc8e619ad861e0. | {"service":"workiz-api"}
2025-08-28 03:12:29 [[32minfo[39m] [general]: Successfully initialized and added scraper for Platt Electric Supply. | {"service":"workiz-api"}
2025-08-28 03:12:29 [[34mdebug[39m] [general]: Source type map updated: PLATT -> 682bdb1305cc8e619ad861e0 | {"service":"workiz-api"}
2025-08-28 03:12:29 [[32minfo[39m] [general]: ScraperService initialization loop finished. | {"service":"workiz-api"}
2025-08-28 03:12:29 [[32minfo[39m] [general]: Final Scraper Count: 3 | {"service":"workiz-api"}
2025-08-28 03:12:29 [[32minfo[39m] [general]: Final Scrapers Map Keys: ["682d20deccc41cc693d861e0","682bdb0e477e692811d861e0","682bdb1305cc8e619ad861e0"] | {"service":"workiz-api"}
2025-08-28 03:12:29 [[32minfo[39m] [general]: Final Source Type Map: {"HOME_DEPOT":"682bdb0e477e692811d861e0","PLATT":"682bdb1305cc8e619ad861e0"} | {"service":"workiz-api"}
2025-08-28 03:12:29 [[32minfo[39m] [general]: Scraper Service initialized successfully. | {"service":"workiz-api"}
2025-08-28 03:12:29 [[32minfo[39m] [general]: Running comprehensive Crawl4AI diagnostics... | {"service":"workiz-api"}
2025-08-28 03:12:29 [[32minfo[39m] [general]: [SCRAPER_HEALTH] Validating material sources... | {"service":"workiz-api"}
2025-08-28 03:12:29 [[32minfo[39m] [general]: [SCRAPER_HEALTH] Material source validation completed: 3 total sources | {"service":"workiz-api"}
2025-08-28 03:12:29 [[32minfo[39m] [general]: [SCRAPER_HEALTH] Home Depot found: true, Platt found: true | {"service":"workiz-api"}
2025-08-28 03:12:29 [[32minfo[39m] [general]: [SCRAPER_HEALTH] Valid sources count: 3 | {"service":"workiz-api"}
2025-08-28 03:12:29 [[32minfo[39m] [general]: [SCRAPER_HEALTH] No validation issues found | {"service":"workiz-api"}
2025-08-28 03:12:29 [[32minfo[39m] [general]: Material Source Validation completed: 3 sources, 3 valid | {"service":"workiz-api"}
2025-08-28 03:12:29 [[32minfo[39m] [general]: [CRAWL4AI_HEALTH] Starting comprehensive health check | {"service":"workiz-api"}
2025-08-28 03:12:29 [[34mdebug[39m] [general]: Testing Python executable: python3 | {"service":"workiz-api"}
2025-08-28 03:12:29 [[32minfo[39m] [general]: Successfully detected Python executable: python3 (Python 3.10.12) | {"service":"workiz-api"}
2025-08-28 03:12:29 [[32minfo[39m] [general]: Starting Price Lookup Poller as additional fallback mechanism... | {"service":"workiz-api"}
2025-08-28 03:12:29 [[32minfo[39m] [general]: [PriceLookupPoller] Starting price lookup polling service | {"service":"workiz-api"}
2025-08-28 03:12:29 [[32minfo[39m] [general]: [CRAWL4AI_HEALTH] Testing actual crawl functionality... | {"service":"workiz-api"}
2025-08-28 03:12:34 [[32minfo[39m] [general]: [CRAWL4AI_HEALTH] ✅ All health checks passed | {"service":"workiz-api"}
2025-08-28 03:12:34 [[32minfo[39m] [general]: Crawl4AI Health Check Results: | {"service":"workiz-api","overall":"healthy","pythonEnvironment":{"available":true,"path":"python3","version":"Python 3.10.12"},"crawl4aiPackage":{"installed":true,"message":"Crawl4AI installed and working properly"},"testCrawl":{"success":true,"url":"https://httpbin.org/json","hasContent":true,"contentLength":381}}
2025-08-28 03:12:34 [[32minfo[39m] [general]: ✅ Crawl4AI is healthy and ready for price scraping | {"service":"workiz-api"}
2025-08-28 03:12:34 [[32minfo[39m] [general]: Configuring Express middleware and routes... | {"service":"workiz-api"}
2025-08-28 03:13:02 [[32minfo[39m] [general]: Loaded preferred search domains: homedepot.com, lowes.com, grainger.com, platt.com, supplyhouse.com, ferguson.com | {"service":"workiz-api"}
2025-08-28 03:13:08 [[32minfo[39m] [general]: Configuring static file serving for /uploads path: /mnt/c/Projects/workiz/backend/uploads | {"service":"workiz-api"}
2025-08-28 03:13:08 [[32minfo[39m] [general]: Express middleware and routes configured. | {"service":"workiz-api"}
2025-08-28 03:13:08 [[32minfo[39m] [general]: Async server setup finished successfully. | {"service":"workiz-api"}
2025-08-28 03:13:08 [[32minfo[39m] [general]: Server running in development mode on port 5000 | {"service":"workiz-api"}
Redis Error: ReplyError: NOAUTH Authentication required.
    at parseError (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:179:12)
    at parseType (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:302:14) {
  command: { name: 'info', args: [] }
}
Redis Error: ReplyError: NOAUTH Authentication required.
    at parseError (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:179:12)
    at parseType (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:302:14) {
  command: { name: 'info', args: [] }
}
Redis Error: ReplyError: NOAUTH Authentication required.
    at parseError (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:179:12)
    at parseType (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:302:14) {
  command: { name: 'info', args: [] }
}
Redis Error: ReplyError: NOAUTH Authentication required.
    at parseError (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:179:12)
    at parseType (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:302:14) {
  command: { name: 'info', args: [] }
}
Redis Error: ReplyError: NOAUTH Authentication required.
    at parseError (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:179:12)
    at parseType (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:302:14) {
  command: { name: 'info', args: [] }
}
Redis Error: ReplyError: NOAUTH Authentication required.
    at parseError (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:179:12)
    at parseType (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:302:14) {
  command: { name: 'info', args: [] }
}
Redis Error: ReplyError: NOAUTH Authentication required.
    at parseError (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:179:12)
    at parseType (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:302:14) {
  command: { name: 'info', args: [] }
}
Redis Error: ReplyError: NOAUTH Authentication required.
    at parseError (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:179:12)
    at parseType (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:302:14) {
  command: { name: 'info', args: [] }
}
Redis Error: ReplyError: NOAUTH Authentication required.
    at parseError (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:179:12)
    at parseType (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:302:14) {
  command: { name: 'info', args: [] }
}
Redis Error: ReplyError: NOAUTH Authentication required.
    at parseError (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:179:12)
    at parseType (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:302:14) {
  command: { name: 'info', args: [] }
}
Redis Error: ReplyError: NOAUTH Authentication required.
    at parseError (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:179:12)
    at parseType (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:302:14) {
  command: { name: 'info', args: [] }
}
Redis Error: ReplyError: NOAUTH Authentication required.
    at parseError (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:179:12)
    at parseType (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:302:14) {
  command: { name: 'info', args: [] }
}
Redis Error: ReplyError: NOAUTH Authentication required.
    at parseError (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:179:12)
    at parseType (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:302:14) {
  command: { name: 'info', args: [] }
}
Redis Error: ReplyError: NOAUTH Authentication required.
    at parseError (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:179:12)
    at parseType (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:302:14) {
  command: { name: 'info', args: [] }
}
Redis Error: ReplyError: NOAUTH Authentication required.
    at parseError (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:179:12)
    at parseType (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:302:14) {
  command: { name: 'info', args: [] }
}
Redis Error: ReplyError: NOAUTH Authentication required.
    at parseError (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:179:12)
    at parseType (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:302:14) {
  command: { name: 'info', args: [] }
}
Redis Error: ReplyError: NOAUTH Authentication required.
    at parseError (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:179:12)
    at parseType (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:302:14) {
  command: { name: 'info', args: [] }
}
Redis Error: ReplyError: NOAUTH Authentication required.
    at parseError (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:179:12)
    at parseType (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:302:14) {
  command: { name: 'info', args: [] }
}
Redis Error: ReplyError: NOAUTH Authentication required.
    at parseError (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:179:12)
    at parseType (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:302:14) {
  command: { name: 'info', args: [] }
}
Redis Error: ReplyError: NOAUTH Authentication required.
    at parseError (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:179:12)
    at parseType (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:302:14) {
  command: { name: 'info', args: [] }
}
Redis Error: ReplyError: NOAUTH Authentication required.
    at parseError (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:179:12)
    at parseType (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:302:14) {
  command: { name: 'info', args: [] }
}
Redis Error: ReplyError: NOAUTH Authentication required.
    at parseError (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:179:12)
    at parseType (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:302:14) {
  command: { name: 'info', args: [] }
}
Redis Error: ReplyError: NOAUTH Authentication required.
    at parseError (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:179:12)
    at parseType (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:302:14) {
  command: { name: 'info', args: [] }
}
Redis Error: ReplyError: NOAUTH Authentication required.
    at parseError (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:179:12)
    at parseType (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:302:14) {
  command: { name: 'info', args: [] }
}
Redis Error: ReplyError: NOAUTH Authentication required.
    at parseError (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:179:12)
    at parseType (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:302:14) {
  command: { name: 'info', args: [] }
}
Redis Error: ReplyError: NOAUTH Authentication required.
    at parseError (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:179:12)
    at parseType (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:302:14) {
  command: { name: 'info', args: [] }
}
Redis Error: ReplyError: NOAUTH Authentication required.
    at parseError (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:179:12)
    at parseType (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:302:14) {
  command: { name: 'info', args: [] }
}
Redis Error: ReplyError: NOAUTH Authentication required.
    at parseError (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:179:12)
    at parseType (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:302:14) {
  command: { name: 'info', args: [] }
}
Redis Error: ReplyError: NOAUTH Authentication required.
    at parseError (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:179:12)
    at parseType (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:302:14) {
  command: { name: 'info', args: [] }
}
Redis Error: ReplyError: NOAUTH Authentication required.
    at parseError (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:179:12)
    at parseType (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:302:14) {
  command: { name: 'info', args: [] }
}
Redis Error: ReplyError: NOAUTH Authentication required.
    at parseError (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:179:12)
    at parseType (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:302:14) {
  command: { name: 'info', args: [] }
}
Redis Error: ReplyError: NOAUTH Authentication required.
    at parseError (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:179:12)
    at parseType (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:302:14) {
  command: { name: 'info', args: [] }
}
Redis Error: ReplyError: NOAUTH Authentication required.
    at parseError (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:179:12)
    at parseType (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:302:14) {
  command: { name: 'info', args: [] }
}
Redis Error: ReplyError: NOAUTH Authentication required.
    at parseError (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:179:12)
    at parseType (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:302:14) {
  command: { name: 'info', args: [] }
}
Redis Error: ReplyError: NOAUTH Authentication required.
    at parseError (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:179:12)
    at parseType (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:302:14) {
  command: { name: 'info', args: [] }
}
Redis Error: ReplyError: NOAUTH Authentication required.
    at parseError (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:179:12)
    at parseType (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:302:14) {
  command: { name: 'info', args: [] }
}
Redis Error: ReplyError: NOAUTH Authentication required.
    at parseError (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:179:12)
    at parseType (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:302:14) {
  command: { name: 'info', args: [] }
}
Redis Error: ReplyError: NOAUTH Authentication required.
    at parseError (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:179:12)
    at parseType (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:302:14) {
  command: { name: 'info', args: [] }
}
Redis Error: ReplyError: NOAUTH Authentication required.
    at parseError (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:179:12)
    at parseType (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:302:14) {
  command: { name: 'info', args: [] }
}
Redis Error: ReplyError: NOAUTH Authentication required.
    at parseError (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:179:12)
    at parseType (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:302:14) {
  command: { name: 'info', args: [] }
}
Redis Error: ReplyError: NOAUTH Authentication required.
    at parseError (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:179:12)
    at parseType (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:302:14) {
  command: { name: 'info', args: [] }
}
Redis Error: ReplyError: NOAUTH Authentication required.
    at parseError (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:179:12)
    at parseType (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:302:14) {
  command: { name: 'info', args: [] }
}
Redis Error: ReplyError: NOAUTH Authentication required.
    at parseError (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:179:12)
    at parseType (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:302:14) {
  command: { name: 'info', args: [] }
}
Redis Error: ReplyError: NOAUTH Authentication required.
    at parseError (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:179:12)
    at parseType (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:302:14) {
  command: { name: 'info', args: [] }
}
Redis Error: ReplyError: NOAUTH Authentication required.
    at parseError (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:179:12)
    at parseType (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:302:14) {
  command: { name: 'info', args: [] }
}
Redis Error: ReplyError: NOAUTH Authentication required.
    at parseError (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:179:12)
    at parseType (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:302:14) {
  command: { name: 'info', args: [] }
}
Redis Error: ReplyError: NOAUTH Authentication required.
    at parseError (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:179:12)
    at parseType (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:302:14) {
  command: { name: 'info', args: [] }
}
Redis Error: ReplyError: NOAUTH Authentication required.
    at parseError (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:179:12)
    at parseType (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:302:14) {
  command: { name: 'info', args: [] }
}
Redis Error: ReplyError: NOAUTH Authentication required.
    at parseError (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:179:12)
    at parseType (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:302:14) {
  command: { name: 'info', args: [] }
}
Redis Error: ReplyError: NOAUTH Authentication required.
    at parseError (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:179:12)
    at parseType (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:302:14) {
  command: { name: 'info', args: [] }
}
Redis Error: ReplyError: NOAUTH Authentication required.
    at parseError (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:179:12)
    at parseType (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:302:14) {
  command: { name: 'info', args: [] }
}
Redis Error: ReplyError: NOAUTH Authentication required.
    at parseError (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:179:12)
    at parseType (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:302:14) {
  command: { name: 'info', args: [] }
}
Redis Error: ReplyError: NOAUTH Authentication required.
    at parseError (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:179:12)
    at parseType (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:302:14) {
  command: { name: 'info', args: [] }
}
Redis Error: ReplyError: NOAUTH Authentication required.
    at parseError (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:179:12)
    at parseType (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:302:14) {
  command: { name: 'info', args: [] }
}
Redis Error: ReplyError: NOAUTH Authentication required.
    at parseError (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:179:12)
    at parseType (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:302:14) {
  command: { name: 'info', args: [] }
}
::ffff:127.0.0.1 - - [28/Aug/2025:10:14:15 +0000] "GET /api/health HTTP/1.1" 404 149 "-" "curl/7.81.0"
Redis Error: ReplyError: NOAUTH Authentication required.
    at parseError (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:179:12)
    at parseType (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:302:14) {
  command: { name: 'info', args: [] }
}
Redis Error: ReplyError: NOAUTH Authentication required.
    at parseError (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:179:12)
    at parseType (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:302:14) {
  command: { name: 'info', args: [] }
}
Redis Error: ReplyError: NOAUTH Authentication required.
    at parseError (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:179:12)
    at parseType (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:302:14) {
  command: { name: 'info', args: [] }
}
Redis Error: ReplyError: NOAUTH Authentication required.
    at parseError (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:179:12)
    at parseType (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:302:14) {
  command: { name: 'info', args: [] }
}
Redis Error: ReplyError: NOAUTH Authentication required.
    at parseError (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:179:12)
    at parseType (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:302:14) {
  command: { name: 'info', args: [] }
}
Redis Error: ReplyError: NOAUTH Authentication required.
    at parseError (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:179:12)
    at parseType (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:302:14) {
  command: { name: 'info', args: [] }
}
Redis Error: ReplyError: NOAUTH Authentication required.
    at parseError (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:179:12)
    at parseType (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:302:14) {
  command: { name: 'info', args: [] }
}
Redis Error: ReplyError: NOAUTH Authentication required.
    at parseError (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:179:12)
    at parseType (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:302:14) {
  command: { name: 'info', args: [] }
}
Redis Error: ReplyError: NOAUTH Authentication required.
    at parseError (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:179:12)
    at parseType (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:302:14) {
  command: { name: 'info', args: [] }
}
Redis Error: ReplyError: NOAUTH Authentication required.
    at parseError (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:179:12)
    at parseType (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:302:14) {
  command: { name: 'info', args: [] }
}
Redis Error: ReplyError: NOAUTH Authentication required.
    at parseError (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:179:12)
    at parseType (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:302:14) {
  command: { name: 'info', args: [] }
}
Redis Error: ReplyError: NOAUTH Authentication required.
    at parseError (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:179:12)
    at parseType (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:302:14) {
  command: { name: 'info', args: [] }
}
Redis Error: ReplyError: NOAUTH Authentication required.
    at parseError (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:179:12)
    at parseType (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:302:14) {
  command: { name: 'info', args: [] }
}
Redis Error: ReplyError: NOAUTH Authentication required.
    at parseError (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:179:12)
    at parseType (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:302:14) {
  command: { name: 'info', args: [] }
}
Redis Error: ReplyError: NOAUTH Authentication required.
    at parseError (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:179:12)
    at parseType (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:302:14) {
  command: { name: 'info', args: [] }
}
Redis Error: ReplyError: NOAUTH Authentication required.
    at parseError (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:179:12)
    at parseType (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:302:14) {
  command: { name: 'info', args: [] }
}
Redis Error: ReplyError: NOAUTH Authentication required.
    at parseError (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:179:12)
    at parseType (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:302:14) {
  command: { name: 'info', args: [] }
}
Redis Error: ReplyError: NOAUTH Authentication required.
    at parseError (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:179:12)
    at parseType (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:302:14) {
  command: { name: 'info', args: [] }
}
Redis Error: ReplyError: NOAUTH Authentication required.
    at parseError (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:179:12)
    at parseType (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:302:14) {
  command: { name: 'info', args: [] }
}
Redis Error: ReplyError: NOAUTH Authentication required.
    at parseError (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:179:12)
    at parseType (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:302:14) {
  command: { name: 'info', args: [] }
}
Redis Error: ReplyError: NOAUTH Authentication required.
    at parseError (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:179:12)
    at parseType (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:302:14) {
  command: { name: 'info', args: [] }
}
Redis Error: ReplyError: NOAUTH Authentication required.
    at parseError (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:179:12)
    at parseType (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:302:14) {
  command: { name: 'info', args: [] }
}
Redis Error: ReplyError: NOAUTH Authentication required.
    at parseError (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:179:12)
    at parseType (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:302:14) {
  command: { name: 'info', args: [] }
}
::1 - - [28/Aug/2025:10:15:00 +0000] "GET /api/health HTTP/1.1" 404 149 "-" "axios/1.11.0"
Redis Error: ReplyError: NOAUTH Authentication required.
    at parseError (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:179:12)
    at parseType (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:302:14) {
  command: { name: 'info', args: [] }
}
Redis Error: ReplyError: NOAUTH Authentication required.
    at parseError (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:179:12)
    at parseType (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:302:14) {
  command: { name: 'info', args: [] }
}
Redis Error: ReplyError: NOAUTH Authentication required.
    at parseError (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:179:12)
    at parseType (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:302:14) {
  command: { name: 'info', args: [] }
}
Redis Error: ReplyError: NOAUTH Authentication required.
    at parseError (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:179:12)
    at parseType (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:302:14) {
  command: { name: 'info', args: [] }
}
::ffff:127.0.0.1 - - [28/Aug/2025:10:15:09 +0000] "GET /api/health HTTP/1.1" 404 149 "-" "curl/7.81.0"
Redis Error: ReplyError: NOAUTH Authentication required.
    at parseError (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:179:12)
    at parseType (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:302:14) {
  command: { name: 'info', args: [] }
}
Redis Error: ReplyError: NOAUTH Authentication required.
    at parseError (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:179:12)
    at parseType (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:302:14) {
  command: { name: 'info', args: [] }
}
Redis Error: ReplyError: NOAUTH Authentication required.
    at parseError (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:179:12)
    at parseType (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:302:14) {
  command: { name: 'info', args: [] }
}
Redis Error: ReplyError: NOAUTH Authentication required.
    at parseError (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:179:12)
    at parseType (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:302:14) {
  command: { name: 'info', args: [] }
}
Redis Error: ReplyError: NOAUTH Authentication required.
    at parseError (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:179:12)
    at parseType (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:302:14) {
  command: { name: 'info', args: [] }
}
Redis Error: ReplyError: NOAUTH Authentication required.
    at parseError (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:179:12)
    at parseType (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:302:14) {
  command: { name: 'info', args: [] }
}
::ffff:127.0.0.1 - - [28/Aug/2025:10:15:18 +0000] "GET /api/health/crawl4ai HTTP/1.1" 404 158 "-" "curl/7.81.0"
Redis Error: ReplyError: NOAUTH Authentication required.
    at parseError (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:179:12)
    at parseType (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:302:14) {
  command: { name: 'info', args: [] }
}
Redis Error: ReplyError: NOAUTH Authentication required.
    at parseError (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:179:12)
    at parseType (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:302:14) {
  command: { name: 'info', args: [] }
}
Redis Error: ReplyError: NOAUTH Authentication required.
    at parseError (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:179:12)
    at parseType (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:302:14) {
  command: { name: 'info', args: [] }
}
Redis Error: ReplyError: NOAUTH Authentication required.
    at parseError (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:179:12)
    at parseType (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:302:14) {
  command: { name: 'info', args: [] }
}
Redis Error: ReplyError: NOAUTH Authentication required.
    at parseError (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:179:12)
    at parseType (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:302:14) {
  command: { name: 'info', args: [] }
}
Redis Error: ReplyError: NOAUTH Authentication required.
    at parseError (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:179:12)
    at parseType (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:302:14) {
  command: { name: 'info', args: [] }
}
Redis Error: ReplyError: NOAUTH Authentication required.
    at parseError (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:179:12)
    at parseType (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:302:14) {
  command: { name: 'info', args: [] }
}
Redis Error: ReplyError: NOAUTH Authentication required.
    at parseError (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:179:12)
    at parseType (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:302:14) {
  command: { name: 'info', args: [] }
}
Redis Error: ReplyError: NOAUTH Authentication required.
    at parseError (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:179:12)
    at parseType (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:302:14) {
  command: { name: 'info', args: [] }
}
Redis Error: ReplyError: NOAUTH Authentication required.
    at parseError (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:179:12)
    at parseType (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:302:14) {
  command: { name: 'info', args: [] }
}
Redis Error: ReplyError: NOAUTH Authentication required.
    at parseError (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:179:12)
    at parseType (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:302:14) {
  command: { name: 'info', args: [] }
}
2025-08-28 03:15:41 [[32minfo[39m] [general]: Login attempt | {"service":"workiz-api","email":"<EMAIL>"}
2025-08-28 03:15:41 [[32minfo[39m] [general]: User logged in: <EMAIL> (Administrators) | {"service":"workiz-api"}
::ffff:127.0.0.1 - - [28/Aug/2025:10:15:41 +0000] "POST /api/users/login HTTP/1.1" 200 347 "-" "curl/7.81.0"
Redis Error: ReplyError: NOAUTH Authentication required.
    at parseError (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:179:12)
    at parseType (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:302:14) {
  command: { name: 'info', args: [] }
}
Redis Error: ReplyError: NOAUTH Authentication required.
    at parseError (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:179:12)
    at parseType (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:302:14) {
  command: { name: 'info', args: [] }
}
Redis Error: ReplyError: NOAUTH Authentication required.
    at parseError (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:179:12)
    at parseType (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:302:14) {
  command: { name: 'info', args: [] }
}
Redis Error: ReplyError: NOAUTH Authentication required.
    at parseError (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:179:12)
    at parseType (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:302:14) {
  command: { name: 'info', args: [] }
}
Redis Error: ReplyError: NOAUTH Authentication required.
    at parseError (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:179:12)
    at parseType (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:302:14) {
  command: { name: 'info', args: [] }
}
Redis Error: ReplyError: NOAUTH Authentication required.
    at parseError (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:179:12)
    at parseType (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:302:14) {
  command: { name: 'info', args: [] }
}
Redis Error: ReplyError: NOAUTH Authentication required.
    at parseError (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:179:12)
    at parseType (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:302:14) {
  command: { name: 'info', args: [] }
}
Redis Error: ReplyError: NOAUTH Authentication required.
    at parseError (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:179:12)
    at parseType (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:302:14) {
  command: { name: 'info', args: [] }
}
Redis Error: ReplyError: NOAUTH Authentication required.
    at parseError (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:179:12)
    at parseType (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:302:14) {
  command: { name: 'info', args: [] }
}
Redis Error: ReplyError: NOAUTH Authentication required.
    at parseError (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:179:12)
    at parseType (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:302:14) {
  command: { name: 'info', args: [] }
}
Redis Error: ReplyError: NOAUTH Authentication required.
    at parseError (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:179:12)
    at parseType (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:302:14) {
  command: { name: 'info', args: [] }
}
Redis Error: ReplyError: NOAUTH Authentication required.
    at parseError (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:179:12)
    at parseType (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:302:14) {
  command: { name: 'info', args: [] }
}
Redis Error: ReplyError: NOAUTH Authentication required.
    at parseError (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:179:12)
    at parseType (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:302:14) {
  command: { name: 'info', args: [] }
}
Redis Error: ReplyError: NOAUTH Authentication required.
    at parseError (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:179:12)
    at parseType (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:302:14) {
  command: { name: 'info', args: [] }
}
Redis Error: ReplyError: NOAUTH Authentication required.
    at parseError (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:179:12)
    at parseType (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:302:14) {
  command: { name: 'info', args: [] }
}
2025-08-28 03:16:10 [[32minfo[39m] [general]: Price lookup request for 2 items from user 68ae4a0f12a2625a4f8b1e2c | {"service":"workiz-api"}
2025-08-28 03:16:10 [[34mdebug[39m] [general]: [ScraperService] getMaterialBySku (Fallback Logic) called for SKU: "TEST-GFCI-001", options: {"skipCache":false} | {"service":"workiz-api"}
2025-08-28 03:16:10 [[32minfo[39m] [general]: [ScraperService Fallback] Attempting primary SKU lookup: Home Depot (ID: 682bdb0e477e692811d861e0) for SKU: TEST-GFCI-001 | {"service":"workiz-api"}
2025-08-28 03:16:10 [[31merror[39m] [general]: [ScraperService Fallback] Error looking up SKU TEST-GFCI-001 from primary source (Home Depot ID: 682bdb0e477e692811d861e0): priceCache.getCacheKey is not a function | {"service":"workiz-api","stack":"TypeError: priceCache.getCacheKey is not a function\n    at ScraperService.getMaterialBySkuFromSource (/mnt/c/Projects/workiz/backend/scrapers/ScraperService.js:587:33)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ScraperService.getMaterialBySku (/mnt/c/Projects/workiz/backend/scrapers/ScraperService.js:473:20)\n    at async lookupPricesWithoutSaving (/mnt/c/Projects/workiz/backend/controllers/quoteController.js:3108:27)"}
2025-08-28 03:16:10 [[33mwarn[39m] [general]: [ScraperService Fallback] Primary SKU lookup failed for TEST-GFCI-001. Attempting fallback: Platt (ID: 682bdb1305cc8e619ad861e0) | {"service":"workiz-api"}
2025-08-28 03:16:10 [[31merror[39m] [general]: [ScraperService Fallback] Error looking up SKU TEST-GFCI-001 from fallback source (Platt ID: 682bdb1305cc8e619ad861e0): priceCache.getCacheKey is not a function | {"service":"workiz-api","stack":"TypeError: priceCache.getCacheKey is not a function\n    at ScraperService.getMaterialBySkuFromSource (/mnt/c/Projects/workiz/backend/scrapers/ScraperService.js:587:33)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ScraperService.getMaterialBySku (/mnt/c/Projects/workiz/backend/scrapers/ScraperService.js:517:20)\n    at async lookupPricesWithoutSaving (/mnt/c/Projects/workiz/backend/controllers/quoteController.js:3108:27)"}
2025-08-28 03:16:10 [[31merror[39m] [general]: Error looking up price for item 0: priceCache.getCacheKey is not a function | {"service":"workiz-api","stack":"TypeError: priceCache.getCacheKey is not a function\n    at ScraperService.getMaterialBySkuFromSource (/mnt/c/Projects/workiz/backend/scrapers/ScraperService.js:587:33)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ScraperService.getMaterialBySku (/mnt/c/Projects/workiz/backend/scrapers/ScraperService.js:473:20)\n    at async lookupPricesWithoutSaving (/mnt/c/Projects/workiz/backend/controllers/quoteController.js:3108:27)"}
2025-08-28 03:16:10 [[34mdebug[39m] [general]: [ScraperService] getMaterialBySku (Fallback Logic) called for SKU: "TEST-LED-001", options: {"skipCache":false} | {"service":"workiz-api"}
2025-08-28 03:16:10 [[32minfo[39m] [general]: [ScraperService Fallback] Attempting primary SKU lookup: Home Depot (ID: 682bdb0e477e692811d861e0) for SKU: TEST-LED-001 | {"service":"workiz-api"}
2025-08-28 03:16:10 [[31merror[39m] [general]: [ScraperService Fallback] Error looking up SKU TEST-LED-001 from primary source (Home Depot ID: 682bdb0e477e692811d861e0): priceCache.getCacheKey is not a function | {"service":"workiz-api","stack":"TypeError: priceCache.getCacheKey is not a function\n    at ScraperService.getMaterialBySkuFromSource (/mnt/c/Projects/workiz/backend/scrapers/ScraperService.js:587:33)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ScraperService.getMaterialBySku (/mnt/c/Projects/workiz/backend/scrapers/ScraperService.js:473:20)\n    at async lookupPricesWithoutSaving (/mnt/c/Projects/workiz/backend/controllers/quoteController.js:3108:27)"}
2025-08-28 03:16:10 [[33mwarn[39m] [general]: [ScraperService Fallback] Primary SKU lookup failed for TEST-LED-001. Attempting fallback: Platt (ID: 682bdb1305cc8e619ad861e0) | {"service":"workiz-api"}
2025-08-28 03:16:10 [[31merror[39m] [general]: [ScraperService Fallback] Error looking up SKU TEST-LED-001 from fallback source (Platt ID: 682bdb1305cc8e619ad861e0): priceCache.getCacheKey is not a function | {"service":"workiz-api","stack":"TypeError: priceCache.getCacheKey is not a function\n    at ScraperService.getMaterialBySkuFromSource (/mnt/c/Projects/workiz/backend/scrapers/ScraperService.js:587:33)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ScraperService.getMaterialBySku (/mnt/c/Projects/workiz/backend/scrapers/ScraperService.js:517:20)\n    at async lookupPricesWithoutSaving (/mnt/c/Projects/workiz/backend/controllers/quoteController.js:3108:27)"}
2025-08-28 03:16:10 [[31merror[39m] [general]: Error looking up price for item 1: priceCache.getCacheKey is not a function | {"service":"workiz-api","stack":"TypeError: priceCache.getCacheKey is not a function\n    at ScraperService.getMaterialBySkuFromSource (/mnt/c/Projects/workiz/backend/scrapers/ScraperService.js:587:33)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ScraperService.getMaterialBySku (/mnt/c/Projects/workiz/backend/scrapers/ScraperService.js:473:20)\n    at async lookupPricesWithoutSaving (/mnt/c/Projects/workiz/backend/controllers/quoteController.js:3108:27)"}
2025-08-28 03:16:10 [[32minfo[39m] [general]: Price lookup completed: 0 found, 2 failed, 4ms total | {"service":"workiz-api"}
::ffff:127.0.0.1 - - [28/Aug/2025:10:16:10 +0000] "POST /api/quotes/lookup-prices HTTP/1.1" 200 681 "-" "curl/7.81.0"
Redis Error: ReplyError: NOAUTH Authentication required.
    at parseError (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:179:12)
    at parseType (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:302:14) {
  command: { name: 'info', args: [] }
}
Redis Error: ReplyError: NOAUTH Authentication required.
    at parseError (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:179:12)
    at parseType (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:302:14) {
  command: { name: 'info', args: [] }
}
Redis Error: ReplyError: NOAUTH Authentication required.
    at parseError (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:179:12)
    at parseType (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:302:14) {
  command: { name: 'info', args: [] }
}
Redis Error: ReplyError: NOAUTH Authentication required.
    at parseError (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:179:12)
    at parseType (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:302:14) {
  command: { name: 'info', args: [] }
}
Redis Error: ReplyError: NOAUTH Authentication required.
    at parseError (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:179:12)
    at parseType (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:302:14) {
  command: { name: 'info', args: [] }
}
Redis Error: ReplyError: NOAUTH Authentication required.
    at parseError (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:179:12)
    at parseType (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:302:14) {
  command: { name: 'info', args: [] }
}
Redis Error: ReplyError: NOAUTH Authentication required.
    at parseError (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:179:12)
    at parseType (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:302:14) {
  command: { name: 'info', args: [] }
}
Redis Error: ReplyError: NOAUTH Authentication required.
    at parseError (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:179:12)
    at parseType (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:302:14) {
  command: { name: 'info', args: [] }
}
Redis Error: ReplyError: NOAUTH Authentication required.
    at parseError (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:179:12)
    at parseType (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:302:14) {
  command: { name: 'info', args: [] }
}
Redis Error: ReplyError: NOAUTH Authentication required.
    at parseError (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:179:12)
    at parseType (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:302:14) {
  command: { name: 'info', args: [] }
}
Redis Error: ReplyError: NOAUTH Authentication required.
    at parseError (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:179:12)
    at parseType (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:302:14) {
  command: { name: 'info', args: [] }
}
Redis Error: ReplyError: NOAUTH Authentication required.
    at parseError (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:179:12)
    at parseType (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:302:14) {
  command: { name: 'info', args: [] }
}
Redis Error: ReplyError: NOAUTH Authentication required.
    at parseError (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:179:12)
    at parseType (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:302:14) {
  command: { name: 'info', args: [] }
}
Redis Error: ReplyError: NOAUTH Authentication required.
    at parseError (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:179:12)
    at parseType (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:302:14) {
  command: { name: 'info', args: [] }
}
Redis Error: ReplyError: NOAUTH Authentication required.
    at parseError (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:179:12)
    at parseType (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:302:14) {
  command: { name: 'info', args: [] }
}
Redis Error: ReplyError: NOAUTH Authentication required.
    at parseError (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:179:12)
    at parseType (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:302:14) {
  command: { name: 'info', args: [] }
}
Redis Error: ReplyError: NOAUTH Authentication required.
    at parseError (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:179:12)
    at parseType (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:302:14) {
  command: { name: 'info', args: [] }
}
Redis Error: ReplyError: NOAUTH Authentication required.
    at parseError (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:179:12)
    at parseType (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:302:14) {
  command: { name: 'info', args: [] }
}
Redis Error: ReplyError: NOAUTH Authentication required.
    at parseError (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:179:12)
    at parseType (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:302:14) {
  command: { name: 'info', args: [] }
}
Redis Error: ReplyError: NOAUTH Authentication required.
    at parseError (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:179:12)
    at parseType (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:302:14) {
  command: { name: 'info', args: [] }
}
Redis Error: ReplyError: NOAUTH Authentication required.
    at parseError (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:179:12)
    at parseType (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:302:14) {
  command: { name: 'info', args: [] }
}
Redis Error: ReplyError: NOAUTH Authentication required.
    at parseError (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:179:12)
    at parseType (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:302:14) {
  command: { name: 'info', args: [] }
}
Redis Error: ReplyError: NOAUTH Authentication required.
    at parseError (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:179:12)
    at parseType (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:302:14) {
  command: { name: 'info', args: [] }
}
Redis Error: ReplyError: NOAUTH Authentication required.
    at parseError (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:179:12)
    at parseType (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:302:14) {
  command: { name: 'info', args: [] }
}
Redis Error: ReplyError: NOAUTH Authentication required.
    at parseError (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:179:12)
    at parseType (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:302:14) {
  command: { name: 'info', args: [] }
}
Redis Error: ReplyError: NOAUTH Authentication required.
    at parseError (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:179:12)
    at parseType (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:302:14) {
  command: { name: 'info', args: [] }
}
Redis Error: ReplyError: NOAUTH Authentication required.
    at parseError (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:179:12)
    at parseType (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:302:14) {
  command: { name: 'info', args: [] }
}
Redis Error: ReplyError: NOAUTH Authentication required.
    at parseError (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:179:12)
    at parseType (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:302:14) {
  command: { name: 'info', args: [] }
}
Redis Error: ReplyError: NOAUTH Authentication required.
    at parseError (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:179:12)
    at parseType (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:302:14) {
  command: { name: 'info', args: [] }
}
Redis Error: ReplyError: NOAUTH Authentication required.
    at parseError (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:179:12)
    at parseType (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:302:14) {
  command: { name: 'info', args: [] }
}
Redis Error: ReplyError: NOAUTH Authentication required.
    at parseError (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:179:12)
    at parseType (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:302:14) {
  command: { name: 'info', args: [] }
}
Redis Error: ReplyError: NOAUTH Authentication required.
    at parseError (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:179:12)
    at parseType (/mnt/c/Projects/workiz/backend/node_modules/redis-parser/lib/parser.js:302:14) {
  command: { name: 'info', args: [] }
}
2025-08-28 03:17:11 [[34mdebug[39m] [general]: [BrowserManager] No active browser instance to close. | {"service":"workiz-api"}
2025-08-28 03:17:11 [[32minfo[39m] [general]: [StreamingAI] Graceful shutdown initiated | {"service":"workiz-api"}
2025-08-28 03:17:11 [[32minfo[39m] [general]: [StreamingAI] All sessions cleaned up | {"service":"workiz-api"}
2025-08-28 03:17:11 [[32minfo[39m] [general]: SIGTERM signal received: closing HTTP server | {"service":"workiz-api"}
2025-08-28 03:17:11 [[32minfo[39m] [general]: [PriceLookupPoller] Stopping price lookup polling service | {"service":"workiz-api"}
2025-08-28 03:17:11 [[32minfo[39m] [general]: [ChangeStreamService] Closing all active change streams... | {"service":"workiz-api"}
2025-08-28 03:17:11 [[34mdebug[39m] [general]: [BrowserManager] No active browser instance to close. | {"service":"workiz-api"}
