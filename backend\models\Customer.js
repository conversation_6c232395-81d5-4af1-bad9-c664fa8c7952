const mongoose = require("mongoose");

const customerSchema = new mongoose.Schema(
  {
    businessName: {
      type: String,
      trim: true,
    },
    contactPerson: {
      firstName: {
        type: String,
        required: true,
        trim: true,
      },
      lastName: {
        type: String,
        required: true,
        trim: true,
      },
      position: {
        type: String,
        trim: true,
      },
    },
    email: {
      type: String,
      trim: true,
      lowercase: true,
    },
    phone: {
      type: String,
      required: true,
      trim: true,
    },
    alternatePhone: {
      type: String,
      trim: true,
    },
    address: {
      street: String,
      city: String,
      state: String,
      zipCode: String,
      country: String,
    },
    notes: {
      type: String,
      trim: true,
    },
    tags: [String],
    source: {
      type: String,
      trim: true,
    },
    jobs: [
      {
        type: mongoose.Schema.Types.ObjectId,
        ref: "Job",
      },
    ],
    invoices: [
      {
        type: mongoose.Schema.Types.ObjectId,
        ref: "Invoice",
      },
    ],
    customerImages: [
      {
        filename: String,
        originalName: String,
        url: String,
        mimeType: String,
        size: Number,
        category: {
          type: String,
          enum: ["before", "after", "other"],
          default: "other",
        },
        title: String,
        description: String,
        uploadedBy: {
          type: mongoose.Schema.Types.ObjectId,
          ref: "User",
        },
        uploadedAt: {
          type: Date,
          default: Date.now,
        },
        // New fields for quote association and AI categorization
        quoteId: {
          type: mongoose.Schema.Types.ObjectId,
          ref: "Quote",
          index: true, // Index for faster lookup
        },
        thumbnailUrl: String, // URL/path to the generated thumbnail
        aiCategory: String, // Category suggested by AI analysis (enhanced with more options)
        aiComponents: [String], // Array of electrical components identified by AI
        aiConcerns: [String], // Array of safety concerns or code violations identified by AI
        aiConfidence: Number, // AI confidence score (0-1)
        aiLocation: String, // Location type identified in the image (e.g., 'residential kitchen')
        aiMetadata: mongoose.Schema.Types.Mixed, // Additional metadata extracted from the image
        aiAnalyzedAt: {
          type: Date,
          default: null,
        },
      },
    ],
  },
  {
    timestamps: true,
  }
);

// Virtual for full name
customerSchema.virtual("fullName").get(function () {
  return `${this.contactPerson.firstName} ${this.contactPerson.lastName}`;
});

// Method to get customer with job history
customerSchema.methods.getWithJobHistory = async function () {
  await this.populate({
    path: "jobs",
    options: { sort: { "schedule.startDate": -1 } },
  }).execPopulate();

  return this;
};

// Method to get customer with invoice history
customerSchema.methods.getWithInvoiceHistory = async function () {
  await this.populate({
    path: "invoices",
    options: { sort: { issueDate: -1 } },
  }).execPopulate();

  return this;
};

// Database indexes for performance optimization
customerSchema.index({ email: 1 }); // Email lookup
customerSchema.index({ phone: 1 }); // Phone lookup
customerSchema.index({
  "contactPerson.firstName": 1,
  "contactPerson.lastName": 1,
}); // Name search
customerSchema.index({ businessName: 1 }); // Business name search
customerSchema.index({ "address.city": 1, "address.state": 1 }); // Location-based queries
customerSchema.index({ tags: 1 }); // Tag-based filtering
customerSchema.index({ source: 1 }); // Lead source analysis
customerSchema.index({ createdAt: -1 }); // Recent customers
customerSchema.index({ "customerImages.quoteId": 1 }); // Quote image lookup (already exists inline)

const Customer = mongoose.model("Customer", customerSchema);

module.exports = Customer;
