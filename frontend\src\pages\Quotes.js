import React, { useEffect, useState, useCallback } from "react"; // Import useCallback
import {
  Container,
  Typography,
  Button,
  Box,
  Paper,
  CircularProgress,
  Alert,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  Chip,
  IconButton,
  Menu,
  MenuItem,
  TextField,
  InputAdornment,
  Select,
  FormControl,
  InputLabel,
  TableSortLabel,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions, // Added Dialog components
} from "@mui/material";
import { Link as RouterLink, useNavigate } from "react-router-dom"; // Added useNavigate
import AddIcon from "@mui/icons-material/Add";
import MoreVertIcon from "@mui/icons-material/MoreVert"; // Icon for action menu
import SearchIcon from "@mui/icons-material/Search"; // Icon for search input
// import FilterListIcon from '@mui/icons-material/FilterList'; // Removed unused import
import { useSelector, useDispatch } from "react-redux";
import {
  getQuotes,
  clearQuoteError,
  emailQuote,
  generateQuotePdf,
  convertQuoteToInvoice,
  deleteQuote, // Import deleteQuote
} from "../slices/quoteSlice";
import { format } from "date-fns";
import { useSnackbar } from "notistack"; // For showing feedback messages
import { debounce } from "lodash"; // For debouncing search input
import { DatePicker } from "@mui/x-date-pickers/DatePicker";
import { LocalizationProvider } from "@mui/x-date-pickers/LocalizationProvider";
import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFns";

const Quotes = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate(); // Hook for navigation
  const { enqueueSnackbar } = useSnackbar(); // Hook for snackbar notifications
  const { quotes, loading, error, totalQuotes } = useSelector(
    (state) => state.quotes
  ); // Removed unused detailedQuote, conversionResult

  // --- State Variables ---
  // Pagination
  const [page, setPage] = useState(0); // MUI TablePagination is 0-based
  const [rowsPerPage, setRowsPerPage] = useState(10);
  // Action Menu
  const [anchorEl, setAnchorEl] = useState(null);
  const [selectedQuoteId, setSelectedQuoteId] = useState(null);
  // Filters
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("");
  const [startDate, setStartDate] = useState(null);
  const [endDate, setEndDate] = useState(null);
  // Sorting
  const [orderBy, setOrderBy] = useState("createdAt"); // Default sort field
  const [order, setOrder] = useState("desc"); // Default sort direction
  // Delete Confirmation Dialog state
  const [openDeleteDialog, setOpenDeleteDialog] = useState(false);
  const [quoteToDelete, setQuoteToDelete] = useState(null);

  // Debounced search handler using useCallback
  // eslint-disable-next-line react-hooks/exhaustive-deps
  const debouncedSearch = useCallback(
    debounce((term) => {
      setPage(0); // Reset page when search term changes
      dispatch(
        getQuotes({
          page: 1, // Reset to page 1 for new search
          limit: rowsPerPage,
          search: term,
          status: statusFilter,
          startDate: startDate ? format(startDate, "yyyy-MM-dd") : undefined,
          endDate: endDate ? format(endDate, "yyyy-MM-dd") : undefined,
          sortBy: orderBy,
          sortOrder: order,
        })
      );
    }, 500), // 500ms debounce
    [dispatch, rowsPerPage, statusFilter, startDate, endDate, orderBy, order] // Dependencies for useCallback
  );

  useEffect(() => {
    // Fetch quotes when filters, sort, or pagination change
    const filters = {
      search: searchTerm,
      status: statusFilter,
      startDate: startDate ? format(startDate, "yyyy-MM-dd") : undefined,
      endDate: endDate ? format(endDate, "yyyy-MM-dd") : undefined,
      sortBy: orderBy,
      sortOrder: order,
    };
    dispatch(getQuotes({ page: page + 1, limit: rowsPerPage, ...filters })); // API is 1-based

    // Clear error on component unmount or re-fetch
    return () => {
      dispatch(clearQuoteError());
      // Cleanup function for useEffect: cancel the debounce timer
      return () => {
        debouncedSearch.cancel();
      };
    };
  }, [
    dispatch,
    page,
    rowsPerPage,
    statusFilter,
    startDate,
    endDate,
    orderBy,
    order,
    searchTerm,
    debouncedSearch,
  ]); // Added debouncedSearch dependency

  // --- Handlers ---
  const handleRequestSort = (_, property) => {
    const isAsc = orderBy === property && order === "asc";
    setOrder(isAsc ? "desc" : "asc");
    setOrderBy(property);
    setPage(0); // Reset page when sorting changes
  };

  const handleSearchChange = (event) => {
    setSearchTerm(event.target.value);
    debouncedSearch(event.target.value);
  };

  const handleStatusChange = (event) => {
    setStatusFilter(event.target.value);
    setPage(0); // Reset page when filter changes
  };

  const handleStartDateChange = (newDate) => {
    setStartDate(newDate);
    setPage(0); // Reset page when filter changes
  };

  const handleEndDateChange = (newDate) => {
    setEndDate(newDate);
    setPage(0); // Reset page when filter changes
  };

  const handleClearFilters = () => {
    setSearchTerm("");
    setStatusFilter("");
    setStartDate(null);
    setEndDate(null);
    setOrderBy("createdAt");
    setOrder("desc");
    setPage(0);
    // Dispatch is handled by useEffect dependency changes
  };

  const handleMenuClick = (event, quoteId) => {
    setAnchorEl(event.currentTarget);
    setSelectedQuoteId(quoteId);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
    setSelectedQuoteId(null);
  };

  const handleView = () => {
    if (selectedQuoteId) {
      navigate(`/quotes/${selectedQuoteId}`);
    }
    handleMenuClose();
  };

  const handleEdit = () => {
    if (selectedQuoteId) {
      navigate(`/quotes/${selectedQuoteId}/edit`); // Corrected path
    }
    handleMenuClose();
  };

  const handleEmail = async () => {
    if (selectedQuoteId) {
      try {
        // TODO: Potentially open a dialog to customize email content
        await dispatch(
          emailQuote({ id: selectedQuoteId, emailData: {} })
        ).unwrap();
        enqueueSnackbar("Quote emailed successfully", { variant: "success" });
      } catch (err) {
        enqueueSnackbar(`Failed to email quote: ${err}`, { variant: "error" });
      }
    }
    handleMenuClose();
  };

  const handlePrint = async () => {
    if (selectedQuoteId) {
      try {
        await dispatch(generateQuotePdf(selectedQuoteId)).unwrap();
        // Snackbar not needed as download starts
      } catch (err) {
        enqueueSnackbar(`Failed to generate PDF: ${err}`, { variant: "error" });
      }
    }
    handleMenuClose();
  };

  const handleConvert = async () => {
    if (selectedQuoteId) {
      try {
        const result = await dispatch(
          convertQuoteToInvoice(selectedQuoteId)
        ).unwrap();
        enqueueSnackbar(
          `Quote converted to Invoice ${result.invoiceNumber || ""}`,
          { variant: "success" }
        );
        // Optionally navigate to the new invoice: navigate(`/invoices/${result.invoiceId}`);
      } catch (err) {
        enqueueSnackbar(`Failed to convert quote: ${err}`, {
          variant: "error",
        });
      }
    }
    handleMenuClose();
  };

  const handleDelete = () => {
    const quoteIdToDelete = selectedQuoteId; // Store ID before closing menu
    handleMenuClose(); // Close menu immediately

    if (quoteIdToDelete) {
      // Find the quote to get its details for the confirmation dialog
      const quote = quotes.find((q) => q._id === quoteIdToDelete);
      setQuoteToDelete(quote);
      setOpenDeleteDialog(true);
    }
  };

  const handleConfirmDelete = async () => {
    if (quoteToDelete) {
      try {
        await dispatch(deleteQuote(quoteToDelete._id)).unwrap();
        enqueueSnackbar("Quote deleted successfully", { variant: "success" });
        // The list should update automatically via the slice reducer
      } catch (err) {
        enqueueSnackbar(`Failed to delete quote: ${err}`, { variant: "error" });
      }
    }
    setOpenDeleteDialog(false);
    setQuoteToDelete(null);
  };

  const handleCancelDelete = () => {
    setOpenDeleteDialog(false);
    setQuoteToDelete(null);
  };

  // Removed unused confirmDelete and closeDeleteDialog handlers

  const handleChangePage = (_, newPage) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0); // Reset to first page
  };

  const getStatusColor = (status) => {
    switch (status) {
      case "DRAFT":
        return "default";
      case "SENT":
        return "info";
      case "APPROVED":
        return "success";
      case "REJECTED":
        return "error";
      case "EXPIRED":
        return "warning";
      default:
        return "default";
    }
  };

  return (
    <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
      <Box
        display="flex"
        justifyContent="space-between"
        alignItems="center"
        mb={3}
      >
        <Typography variant="h4" component="h1">
          Quotes
        </Typography>
        <Button
          variant="contained"
          color="primary"
          component={RouterLink}
          to="/quotes/create" // Updated path
          startIcon={<AddIcon />}
        >
          Create Quote
        </Button>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      {/* Filter Controls */}
      <Box sx={{ mb: 2, display: "flex", gap: 2, flexWrap: "wrap" }}>
        <TextField
          label="Search Quotes"
          variant="outlined"
          size="small"
          value={searchTerm}
          onChange={handleSearchChange}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <SearchIcon />
              </InputAdornment>
            ),
          }}
          sx={{ flexGrow: 1, minWidth: "200px" }}
        />
        <FormControl size="small" sx={{ minWidth: 150 }}>
          <InputLabel>Status</InputLabel>
          <Select
            value={statusFilter}
            label="Status"
            onChange={handleStatusChange}
          >
            <MenuItem value="">
              <em>All</em>
            </MenuItem>
            <MenuItem value="DRAFT">Draft</MenuItem>
            <MenuItem value="SENT">Sent</MenuItem>
            <MenuItem value="APPROVED">Approved</MenuItem>
            <MenuItem value="REJECTED">Rejected</MenuItem>
            <MenuItem value="EXPIRED">Expired</MenuItem>
            <MenuItem value="CONVERTED">Converted</MenuItem>{" "}
            {/* Added CONVERTED */}
          </Select>
        </FormControl>
        <LocalizationProvider dateAdapter={AdapterDateFns}>
          <DatePicker
            label="Start Date"
            value={startDate}
            onChange={handleStartDateChange}
            slots={{
              textField: (params) => (
                <TextField {...params} size="small" sx={{ minWidth: 150 }} />
              ),
            }}
          />
        </LocalizationProvider>
        <LocalizationProvider dateAdapter={AdapterDateFns}>
          <DatePicker
            label="End Date"
            value={endDate}
            onChange={handleEndDateChange}
            slots={{
              textField: (params) => (
                <TextField {...params} size="small" sx={{ minWidth: 150 }} />
              ),
            }}
          />
        </LocalizationProvider>
        <Button
          variant="outlined"
          onClick={handleClearFilters}
          size="small"
          sx={{ height: "40px" }} // Match TextField/Select height
        >
          Clear Filters
        </Button>
      </Box>
      <Paper sx={{ width: "100%", overflow: "hidden" }}>
        <TableContainer>
          <Table stickyHeader aria-label="quotes table">
            <TableHead>
              <TableRow>
                {/* Define table headers */}
                {[
                  { id: "name", label: "Quote Name", numeric: false },
                  { id: "customer.name", label: "Customer", numeric: false }, // Assuming populated name
                  { id: "job.title", label: "Job", numeric: false }, // Assuming populated title
                  { id: "status", label: "Status", numeric: false },
                  { id: "summary.grandTotal", label: "Total", numeric: true },
                  { id: "createdAt", label: "Created", numeric: false },
                  {
                    id: "actions",
                    label: "Actions",
                    numeric: false,
                    disableSorting: true,
                  }, // Actions column, no sorting
                ].map((headCell) => (
                  <TableCell
                    key={headCell.id}
                    align={headCell.numeric ? "right" : "left"}
                    padding={"normal"}
                    sortDirection={orderBy === headCell.id ? order : false}
                  >
                    {headCell.disableSorting ? (
                      headCell.label
                    ) : (
                      <TableSortLabel
                        active={orderBy === headCell.id}
                        direction={orderBy === headCell.id ? order : "asc"}
                        onClick={(event) =>
                          handleRequestSort(event, headCell.id)
                        }
                      >
                        {headCell.label}
                      </TableSortLabel>
                    )}
                  </TableCell>
                ))}
              </TableRow>
            </TableHead>
            <TableBody>
              {loading ? (
                <TableRow>
                  <TableCell colSpan={7} align="center">
                    <CircularProgress />
                  </TableCell>
                </TableRow>
              ) : quotes.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={7} align="center">
                    No quotes found.
                  </TableCell>
                </TableRow>
              ) : (
                quotes.map((quote) => (
                  <TableRow hover role="checkbox" tabIndex={-1} key={quote._id}>
                    <TableCell>{quote.name}</TableCell>
                    <TableCell>
                      {quote.customer
                        ? quote.customer.businessName ||
                          (quote.customer.contactPerson
                            ? `${
                                quote.customer.contactPerson.firstName || ""
                              } ${
                                quote.customer.contactPerson.lastName || ""
                              }`.trim()
                            : "N/A")
                        : "N/A"}
                    </TableCell>
                    <TableCell>{quote.job?.title || "N/A"}</TableCell>
                    <TableCell>
                      <Chip
                        label={quote.status}
                        color={getStatusColor(quote.status)}
                        size="small"
                      />
                    </TableCell>
                    <TableCell align="right">
                      ${quote.summary?.grandTotal?.toFixed(2) || "0.00"}
                    </TableCell>
                    <TableCell>
                      {format(new Date(quote.createdAt), "MM/dd/yyyy")}
                    </TableCell>
                    <TableCell>
                      <IconButton
                        aria-label="actions"
                        onClick={(event) => handleMenuClick(event, quote._id)}
                      >
                        <MoreVertIcon />
                      </IconButton>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </TableContainer>
        <TablePagination
          rowsPerPageOptions={[5, 10, 25]}
          component="div"
          count={totalQuotes}
          rowsPerPage={rowsPerPage}
          page={page}
          onPageChange={handleChangePage}
          onRowsPerPageChange={handleChangeRowsPerPage}
        />
      </Paper>

      {/* Action Menu */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
      >
        <MenuItem onClick={handleView}>View</MenuItem>
        <MenuItem onClick={handleEdit}>Edit</MenuItem>
        <MenuItem onClick={handleEmail}>Email</MenuItem>
        <MenuItem onClick={handlePrint}>Print</MenuItem>
        <MenuItem onClick={handleConvert}>Convert to Invoice</MenuItem>
        <MenuItem onClick={handleDelete} sx={{ color: "error.main" }}>
          Delete
        </MenuItem>
      </Menu>

      {/* Delete Confirmation Dialog */}
      <Dialog open={openDeleteDialog} onClose={handleCancelDelete}>
        <DialogTitle>Delete Quote</DialogTitle>
        <DialogContent>
          <Typography>
            Are you sure you want to delete quote {quoteToDelete?.quoteNumber}?
          </Typography>
          <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
            Customer: {quoteToDelete?.customer?.name || "Unknown"}
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Total:{" "}
            {quoteToDelete?.total
              ? `$${quoteToDelete.total.toFixed(2)}`
              : "N/A"}
          </Typography>
          <Typography
            variant="body2"
            color="error.main"
            sx={{ mt: 2, fontWeight: "bold" }}
          >
            This action cannot be undone.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCancelDelete}>Cancel</Button>
          <Button
            onClick={handleConfirmDelete}
            color="error"
            variant="contained"
          >
            Delete
          </Button>
        </DialogActions>
      </Dialog>
    </Container>
  );
};

export default Quotes;
