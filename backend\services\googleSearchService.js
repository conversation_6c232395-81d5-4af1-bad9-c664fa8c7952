/**
 * googleSearchService.js
 * Service for interacting with the Google Custom Search JSON API.
 */

const axios = require("axios");
const logger = require("../utils/logger");
const { withRetry } = require("../utils/retryUtils");
const { isRetryableError, getRetryDelay } = require("../utils/aiErrors"); // Assuming network/5xx errors are retryable like AI errors

// Load preferred domains from environment variable, split by comma, trim whitespace
const preferredDomainsString =
  process.env.GOOGLE_SEARCH_PREFERRED_DOMAINS || "";
const PREFERRED_DOMAINS = preferredDomainsString
  ? preferredDomainsString
      .split(",")
      .map((domain) => domain.trim())
      .filter((domain) => domain)
  : [];
logger.info(
  `Loaded preferred search domains: ${PREFERRED_DOMAINS.join(", ") || "None"}`
);

/**
 * Performs a web search using the Google Custom Search JSON API as a fallback.
 * @param {string} query - The search query.
 * @returns {Promise<Array<{title: string, link: string, snippet: string}>>} - An array of relevant search results.
 */
async function searchWebFallback(query) {
  const API_KEY = process.env.GOOGLE_CUSTOM_SEARCH_API_KEY;
  const CX = process.env.GOOGLE_CUSTOM_SEARCH_CX;
  const API_URL = "https://www.googleapis.com/customsearch/v1";

  if (!API_KEY || !CX) {
    logger.warn(
      "Google Custom Search API Key or CX not found. Skipping web fallback search."
    );
    return [];
  }

  // Define a specific retry condition for Google Search API errors
  const isGoogleSearchRetryable = (error) => {
    // Retry on network errors or 5xx server errors from Google
    return (
      isRetryableError(error) ||
      (error.response && error.response.status >= 500)
    );
  };

  try {
    logger.debug(`Performing Google Custom Search for: "${query}"`);
    // Wrap the axios call with retry logic
    const response = await withRetry(
      () =>
        axios.get(API_URL, {
          params: {
            key: API_KEY,
            cx: CX,
            q: query,
            num: 5, // Request fewer results for fallback efficiency
          },
        }),
      {
        maxRetries: 3, // Define retry parameters
        baseDelay: 500,
        maxDelay: 5000,
        shouldRetry: isGoogleSearchRetryable, // Use the specific retry condition
        getRetryDelay: getRetryDelay, // Use the standard delay logic
        onRetry: (error, attempt, delay) => {
          logger.warn(
            `Retrying Google Search (Attempt ${attempt}) after ${delay}ms due to error: ${error.message}`
          );
        },
      }
    );

    if (response.data && response.data.items) {
      logger.info(
        `Google Custom Search returned ${response.data.items.length} results.`
      );
      const results = response.data.items.map((item) => ({
        title: item.title,
        link: item.link,
        snippet: item.snippet,
        // Add a score based on domain preference
        score: PREFERRED_DOMAINS.some((domain) => item.link.includes(domain))
          ? 1
          : 0,
      }));

      // Sort results to prioritize preferred domains
      results.sort((a, b) => b.score - a.score);

      // Return only the relevant fields, potentially filtering further if needed
      return results.map(({ title, link, snippet }) => ({
        title,
        link,
        snippet,
      }));
    } else {
      logger.info("Google Custom Search returned no results.");
      return [];
    }
  } catch (error) {
    logger.error(
      `Google Custom Search API request failed: ${
        error.response?.data?.error?.message || error.message
      }`
    );
    // Re-throw the error to allow callers to handle specific failures
    throw error;
  }
}

module.exports = {
  searchWebFallback,
};
