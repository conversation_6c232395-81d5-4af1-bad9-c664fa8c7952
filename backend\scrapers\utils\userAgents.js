/**
 * userAgents.js
 * Collection of realistic user agents for web scraping
 */

/**
 * Common browser user agents for Windows, macOS, and mobile devices
 */
const userAgents = {
  // Chrome for Windows
  chromeWindows: [
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/114.0.0.0 Safari/537.36",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
  ],

  // Chrome for macOS
  chromeMac: [
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/114.0.0.0 Safari/537.36",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
  ],

  // Firefox for Windows
  firefoxWindows: [
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/112.0",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/113.0",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/114.0",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/115.0",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/116.0",
  ],

  // Safari for macOS
  safariMac: [
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.6.1 Safari/605.1.15",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.0 Safari/605.1.15",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.1 Safari/605.1.15",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.2 Safari/605.1.15",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.3 Safari/605.1.15",
  ],

  // Edge for Windows
  edgeWindows: [
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/114.0.0.0 Safari/537.36 Edg/114.0.1823.67",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/115.0.1901.183",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/116.0.1938.62",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/117.0.2045.31",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/118.0.2088.46",
  ],

  // Mobile Chrome (Android)
  chromeAndroid: [
    "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/114.0.0.0 Mobile Safari/537.36",
    "Mozilla/5.0 (Linux; Android 11; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36",
    "Mozilla/5.0 (Linux; Android 12; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36",
    "Mozilla/5.0 (Linux; Android 13; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36",
    "Mozilla/5.0 (Linux; Android 13; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36",
  ],

  // Mobile Safari (iOS)
  safariIOS: [
    "Mozilla/5.0 (iPhone; CPU iPhone OS 15_6_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.6.1 Mobile/15E148 Safari/604.1",
    "Mozilla/5.0 (iPhone; CPU iPhone OS 16_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.0 Mobile/15E148 Safari/604.1",
    "Mozilla/5.0 (iPhone; CPU iPhone OS 16_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.1 Mobile/15E148 Safari/604.1",
    "Mozilla/5.0 (iPhone; CPU iPhone OS 16_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.3 Mobile/15E148 Safari/604.1",
    "Mozilla/5.0 (iPhone; CPU iPhone OS 16_4 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.4 Mobile/15E148 Safari/604.1",
  ],
};

/**
 * Get all user agents as a flat array
 * @returns {Array<string>} - Array of user agents
 */
function getAllUserAgents() {
  return Object.values(userAgents).flat();
}

/**
 * Get a random user agent from all available agents
 * @returns {string} - Random user agent string
 */
function getRandomUserAgent() {
  const allAgents = getAllUserAgents();
  const randomIndex = Math.floor(Math.random() * allAgents.length);
  return allAgents[randomIndex];
}

/**
 * Get a random user agent from a specific browser and platform
 * @param {string} browser - Browser name ('chrome', 'firefox', 'safari', 'edge')
 * @param {string} platform - Platform name ('windows', 'mac', 'android', 'ios')
 * @returns {string} - Random user agent string for the specified browser and platform
 */
function getRandomUserAgentByBrowserAndPlatform(browser, platform) {
  const browserLower = browser.toLowerCase();
  const platformLower = platform.toLowerCase();

  let key;

  if (browserLower === "chrome") {
    if (platformLower === "windows") {
      key = "chromeWindows";
    } else if (platformLower === "mac") {
      key = "chromeMac";
    } else if (platformLower === "android") {
      key = "chromeAndroid";
    }
  } else if (browserLower === "firefox") {
    if (platformLower === "windows") {
      key = "firefoxWindows";
    }
  } else if (browserLower === "safari") {
    if (platformLower === "mac") {
      key = "safariMac";
    } else if (platformLower === "ios") {
      key = "safariIOS";
    }
  } else if (browserLower === "edge") {
    if (platformLower === "windows") {
      key = "edgeWindows";
    }
  }

  if (!key || !userAgents[key] || userAgents[key].length === 0) {
    // If not found, return a random one
    return getRandomUserAgent();
  }

  const agents = userAgents[key];
  const randomIndex = Math.floor(Math.random() * agents.length);
  return agents[randomIndex];
}

/**
 * Get a random desktop user agent
 * @returns {string} - Random desktop user agent
 */
function getRandomDesktopUserAgent() {
  const desktopAgents = [
    ...userAgents.chromeWindows,
    ...userAgents.chromeMac,
    ...userAgents.firefoxWindows,
    ...userAgents.safariMac,
    ...userAgents.edgeWindows,
  ];

  const randomIndex = Math.floor(Math.random() * desktopAgents.length);
  return desktopAgents[randomIndex];
}

/**
 * Get a random mobile user agent
 * @returns {string} - Random mobile user agent
 */
function getRandomMobileUserAgent() {
  const mobileAgents = [...userAgents.chromeAndroid, ...userAgents.safariIOS];

  const randomIndex = Math.floor(Math.random() * mobileAgents.length);
  return mobileAgents[randomIndex];
}

module.exports = {
  userAgents,
  getAllUserAgents,
  getRandomUserAgent,
  getRandomUserAgentByBrowserAndPlatform,
  getRandomDesktopUserAgent,
  getRandomMobileUserAgent,
};
