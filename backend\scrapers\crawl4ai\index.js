/**
 * Crawl4AI module index
 * Exports the necessary components for integrating Crawl4AI scrapers
 * with the existing scraper system.
 */

const ScraperFactory = require("./ScraperFactory");
const crawl4aiService = require("./crawl4ai-service");
const BaseCrawl4AIScraper = require("./BaseCrawl4AIScraper");
const HomeDepotCrawl4AIScraper = require("./HomeDepotCrawl4AIScraper");
const PlattCrawl4AIScraper = require("./PlattCrawl4AIScraper");

module.exports = {
  ScraperFactory,
  crawl4aiService,
  BaseCrawl4AIScraper,
  HomeDepotCrawl4AIScraper,
  PlattCrawl4AIScraper,

  // Helper method to check if a source type is supported
  isSupported: ScraperFactory.isSupported,

  // Helper method to create a scraper instance
  createScraper: ScraperFactory.createScraper,
};
