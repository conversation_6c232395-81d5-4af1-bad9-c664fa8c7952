const mongoose = require("mongoose");
const dotenv = require("dotenv");
const User = require("../models/User");
const connectDB = require("../config/database");

// Load environment variables
dotenv.config();

// Sample user data
const userData = [
  {
    firstName: "Admin",
    lastName: "User",
    email: "<EMAIL>",
    password: "password123", // Will be hashed automatically via User model pre-save hook
    role: "Administrators",
    phone: "************",
    address: {
      street: "123 Admin Street",
      city: "San Francisco",
      state: "CA",
      zipCode: "94105",
      country: "USA",
    },
    isActive: true,
  },
  {
    firstName: "Manager",
    lastName: "User",
    email: "<EMAIL>",
    password: "password123",
    role: "Managers",
    phone: "************",
    address: {
      street: "456 Manager Avenue",
      city: "San Francisco",
      state: "CA",
      zipCode: "94105",
      country: "USA",
    },
    isActive: true,
  },
  {
    firstName: "Tech",
    lastName: "Support",
    email: "<EMAIL>",
    password: "password123",
    role: "Technicians",
    phone: "************",
    address: {
      street: "789 Tech Road",
      city: "San Francisco",
      state: "CA",
      zipCode: "94105",
      country: "USA",
    },
    contractorType: "HVAC",
    skills: ["Air conditioning", "Heating", "System maintenance"],
    hourlyRate: 65,
    isActive: true,
  },
];

// Connect to database
connectDB();

// Seed function
const seedUsers = async () => {
  try {
    // Clear existing users
    await User.deleteMany({});
    console.log("Deleted existing users");

    // Create users one by one to trigger pre-save hooks for password hashing
    const createdUsers = [];
    for (const userInfo of userData) {
      const user = await User.create(userInfo);
      createdUsers.push(user);
      console.log(`Created user: ${user.email} (${user.role})`);
    }

    console.log(`\nTotal created: ${createdUsers.length} users`);

    // Show admin credentials for easy login
    console.log("\nAdmin credentials for login:");
    console.log("Email: <EMAIL>");
    console.log("Password: password123\n");

    // Exit with success
    process.exit(0);
  } catch (error) {
    console.error(`Error: ${error.message}`);
    process.exit(1);
  }
};

// Run the seeder
seedUsers();
