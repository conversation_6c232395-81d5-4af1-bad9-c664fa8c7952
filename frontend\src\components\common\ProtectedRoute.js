import React from "react";
import { Navigate, useLocation } from "react-router-dom";
import { useSelector } from "react-redux";

/**
 * Role-based route protection
 *
 * @param {Object} props Component props
 * @param {React.ReactNode} props.children Child components to render if authorized
 * @param {string[]} [props.requiredRoles] Optional array of roles allowed to access this route
 * @param {string[]} [props.requiredPermissions] Optional array of permissions required to access this route
 * @returns {React.ReactNode} Child components or redirect
 */
const ProtectedRoute = ({
  children,
  requiredRoles = [],
  requiredPermissions = [],
}) => {
  const { userInfo } = useSelector((state) => state.auth);
  const location = useLocation();

  // Check if user is authenticated
  if (!userInfo) {
    // Redirect to login but save the attempted url
    return <Navigate to="/login" state={{ from: location }} replace />;
  }

  // If no specific roles or permissions are required, just being logged in is enough
  if (requiredRoles.length === 0 && requiredPermissions.length === 0) {
    return children;
  }

  // Check for required roles
  if (requiredRoles.length > 0) {
    if (!requiredRoles.includes(userInfo.role)) {
      // User doesn't have the required role
      return (
        <Navigate
          to="/"
          replace
          state={{
            accessDenied: true,
            message: "You don't have the required role to access this page",
          }}
        />
      );
    }
  }

  // Check for required permissions
  if (requiredPermissions.length > 0) {
    // If the user is an Administrator, they have all permissions
    if (userInfo.role === "Administrators") {
      return children;
    }

    // Check if user has all required permissions
    const hasAllPermissions = requiredPermissions.every(
      (permission) =>
        userInfo.permissions && userInfo.permissions.includes(permission)
    );

    if (!hasAllPermissions) {
      // User doesn't have all required permissions
      return (
        <Navigate
          to="/"
          replace
          state={{
            accessDenied: true,
            message:
              "You don't have the required permissions to access this page",
          }}
        />
      );
    }
  }

  return children;
};

export default ProtectedRoute;
