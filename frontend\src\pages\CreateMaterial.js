import React, { useState, useEffect, useRef } from "react"; // Added useEffect, useRef
import { useNavigate } from "react-router-dom";
import { useDispatch } from "react-redux";
import {
  Box,
  Typography,
  Paper,
  TextField,
  Button,
  Grid,
  CircularProgress,
  InputAdornment,
  Alert, // Added Alert import
  Dialog, // Added for image zoom
  DialogContent, // Added for image zoom
  IconButton, // Added for close button
} from "@mui/material";
import CloseIcon from "@mui/icons-material/Close"; // Added for close button
import {
  Save as SaveIcon,
  AutoAwesome as AiIcon,
  Search as SearchIcon,
} from "@mui/icons-material";
import { useSnackbar } from "notistack";
import {
  createMaterialItem,
  generateAiMaterialDescription,
  searchMaterialsPricing,
  refreshMaterialPrice,
  // fetchMaterialDetailsByUrl // Removed unused import
} from "../slices/materialSlice";
import MaterialSearchResultsDisplay from "../components/materials/MaterialSearchResultsDisplay"; // Import the new component

const CreateMaterial = () => {
  const navigate = useNavigate();
  const { enqueueSnackbar } = useSnackbar();
  const dispatch = useDispatch();
  const isMountedRef = useRef(true); // Ref to track mounted state

  // Form state
  const [name, setName] = useState("");
  const [description, setDescription] = useState("");
  const [sku, setSku] = useState("");
  const [category, setCategory] = useState("");
  const [unitPrice, setUnitPrice] = useState("");
  const [costPrice, setCostPrice] = useState("");
  const [quantity, setQuantity] = useState("0");
  const [reorderLevel, setReorderLevel] = useState("5");
  const [supplier, setSupplier] = useState("");
  const [location, setLocation] = useState("");
  const [imageUrl, setImageUrl] = useState("");
  const [searchResults, setSearchResults] = useState([]); // State for search results

  // Loading states
  const [isSaving, setIsSaving] = useState(false);
  const [isAiLoading, setIsAiLoading] = useState(false);
  const [isPriceLoading, setIsPriceLoading] = useState(false);
  const [lookupError, setLookupError] = useState(null);
  // const [isDetailLoading, setIsDetailLoading] = useState(false); // Removed unused state
  const [isZoomModalOpen, setIsZoomModalOpen] = useState(false);
  const [zoomedImageUrl, setZoomedImageUrl] = useState("");

  // Effect to track mounted state
  useEffect(() => {
    isMountedRef.current = true;
    return () => {
      isMountedRef.current = false; // Set to false when unmounted
    };
  }, []);

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSaving(true);
    setLookupError(null);

    if (!name || !unitPrice || !costPrice || !quantity) {
      enqueueSnackbar(
        "Please fill in required fields: Name, Unit Price, Cost Price, Quantity",
        { variant: "warning" }
      );
      setIsSaving(false);
      return;
    }

    const materialData = {
      name,
      description,
      sku,
      category,
      unitPrice: parseFloat(unitPrice) || 0, // Ensure parsing or default
      costPrice: parseFloat(costPrice) || 0, // Ensure parsing or default
      quantity: parseInt(quantity, 10) || 0, // Ensure parsing or default
      reorderLevel: parseInt(reorderLevel, 10) || 0, // Ensure parsing or default
      supplier,
      location,
      images: imageUrl ? [imageUrl.trim()] : [],
    };

    try {
      await dispatch(createMaterialItem(materialData)).unwrap();
      enqueueSnackbar("Material created successfully", { variant: "success" });
      navigate("/materials");
    } catch (error) {
      console.error("Error creating material:", error);
      const errorMsg =
        error?.message ||
        error ||
        "An error occurred while creating the material."; // Improved error handling
      if (
        typeof errorMsg === "string" &&
        errorMsg.includes("SKU already exists")
      ) {
        enqueueSnackbar("SKU already exists. Please use a unique SKU.", {
          variant: "error",
        });
      } else {
        enqueueSnackbar(errorMsg, { variant: "error" });
      }
    } finally {
      if (isMountedRef.current) {
        setIsSaving(false);
      }
    }
  };

  // Helper to handle number input changes
  const handleNumberChange = (setter) => (e) => {
    const value = e.target.value;
    if (value === "" || /^\d*\.?\d*$/.test(value)) {
      if (
        value.length > 1 &&
        value.startsWith("0") &&
        !value.startsWith("0.")
      ) {
        setter(value.substring(1));
      } else {
        setter(value);
      }
    }
  };
  const handleIntegerChange = (setter) => (e) => {
    const value = e.target.value;
    if (value === "" || /^\d+$/.test(value)) {
      if (value.length > 1 && value.startsWith("0")) {
        setter(value.substring(1));
      } else {
        setter(value);
      }
    }
  };

  // Handle AI Description Suggestion
  const handleAiDescription = async () => {
    if (!name) {
      enqueueSnackbar("Please enter a material name first.", {
        variant: "info",
      });
      return;
    }
    setIsAiLoading(true);
    setLookupError(null);
    try {
      const result = await dispatch(
        generateAiMaterialDescription({ name })
      ).unwrap();
      if (isMountedRef.current) {
        setDescription(result || "");
        enqueueSnackbar("AI description generated.", { variant: "success" });
      }
    } catch (err) {
      console.error("AI Description Error:", err);
      const errorMsg =
        err?.message || err || "Failed to generate AI description."; // Improved error handling
      if (isMountedRef.current) {
        setLookupError(errorMsg);
        enqueueSnackbar(errorMsg, { variant: "error" });
      }
    } finally {
      if (isMountedRef.current) {
        setIsAiLoading(false);
      }
    }
  };

  // Handle Price Lookup
  const handlePriceLookup = async () => {
    setLookupError(null);
    setIsPriceLoading(true);
    try {
      let priceData = null;
      if (sku) {
        console.log(`Refreshing price for SKU: ${sku}`);
        priceData = await dispatch(refreshMaterialPrice({ sku })).unwrap();
        if (priceData?.price !== undefined) {
          if (isMountedRef.current) {
            setUnitPrice(priceData.price.toString());
            setCostPrice(priceData.price.toString()); // Populate cost price too
            // Cannot populate other fields from SKU refresh as backend likely only returns price
            enqueueSnackbar(`Price found for SKU ${sku}: $${priceData.price}`, {
              variant: "success",
            });
          }
        } else {
          throw new Error("Price not found for SKU via refresh.");
        }
      } else if (name) {
        console.log(`Searching price for name: ${name}`);
        // Request up to 10 results
        const searchResultsData = await dispatch(
          searchMaterialsPricing({ query: name, limit: 10, skipCache: true })
        ).unwrap();
        if (searchResultsData && searchResultsData.length > 0) {
          if (isMountedRef.current) {
            // Store results in state instead of auto-populating
            setSearchResults(searchResultsData);
            enqueueSnackbar(
              `${searchResultsData.length} potential matches found. Please select one below.`,
              { variant: "info" }
            );
          }
          // Clear previous auto-populated fields if any (optional, but good practice)
          // setUnitPrice('');
          // setCostPrice('');
          // setSku('');
          // setImageUrl('');
          // setDescription('');
          // setSupplier('');
        } else {
          if (isMountedRef.current) {
            setSearchResults([]); // Clear previous results if none found
            enqueueSnackbar("No pricing found for this material name.", {
              variant: "warning",
            });
          }
        }
      } else {
        enqueueSnackbar(
          "Please enter a Material Name or SKU to lookup price.",
          { variant: "info" }
        );
        if (isMountedRef.current) {
          setIsPriceLoading(false);
        }
        return;
      }
    } catch (err) {
      console.error("Price Lookup Error:", err);
      let displayError = "Failed to lookup price."; // Default message

      // Check for specific 503 error from backend
      if (err.response?.status === 503) {
        displayError =
          "The primary pricing source (Home Depot) is temporarily unavailable. Please try again later or contact support.";
        // Optionally use the backend message if it's more specific and user-friendly
        // if (err.response?.data?.message) {
        //   displayError = err.response.data.message;
        // }
      } else if (err.response?.data?.message) {
        // Use backend message for other errors if available
        displayError = err.response.data.message;
      } else if (err.message) {
        // Fallback to generic error message
        displayError = err.message;
      }

      // Add error code for debugging if needed, but don't show to user unless helpful
      // const errorCode = err.response?.status || err.code || 'UNKNOWN';
      // displayError = `${displayError} (Code: ${errorCode})`;

      if (isMountedRef.current) {
        setLookupError(displayError); // Store the user-friendly message
        enqueueSnackbar(displayError, { variant: "error" }); // Show detailed message in snackbar
      }
    } finally {
      if (isMountedRef.current) {
        setIsPriceLoading(false);
      }
    }
  };

  // Helper function to map source codes to display names
  const mapSourceToSupplierName = (sourceCode) => {
    switch (sourceCode?.toUpperCase()) {
      case "HOME_DEPOT":
        return "Home Depot";
      case "PLATT":
        return "Platt Electric Supply";
      case "GRAINGER":
        return "Grainger";
      // Add other mappings as needed
      default:
        return sourceCode || ""; // Return original code or empty string
    }
  };

  // Handle selection from search results
  const handleResultSelection = async (selectedResult) => {
    if (!selectedResult) return;

    if (isMountedRef.current) {
      // Populate basic fields immediately
      setName(selectedResult.name || selectedResult.title || "");
      setSku(selectedResult.sku || "");

      // Safe price handling - try multiple price fields with proper null checking
      let unitCost = "";
      const priceFields = [
        "unitCostPrice",
        "price",
        "unitPrice",
        "cost",
        "finalPrice",
      ];
      for (const field of priceFields) {
        if (selectedResult[field] != null && selectedResult[field] !== "") {
          unitCost = String(selectedResult[field]);
          break;
        }
      }

      setCostPrice(unitCost);
      setUnitPrice(unitCost); // Default selling price to cost price, user can adjust
      setImageUrl(selectedResult.imageUrl || selectedResult.image || "");
      setSupplier(mapSourceToSupplierName(selectedResult.source));
      // Clear description/category initially
      setDescription("");
      setCategory("");

      // Clear the search results list
      setSearchResults([]);
      setLookupError(null);
    }

    // Generate AI description using the selected name
    const materialName = selectedResult.name || selectedResult.title;
    if (materialName) {
      setIsAiLoading(true); // Use AI loading state
      try {
        const aiResult = await dispatch(
          generateAiMaterialDescription({ name: materialName })
        ).unwrap();
        if (isMountedRef.current) {
          setDescription(aiResult || "");
          enqueueSnackbar(
            `Selected: ${materialName}. AI description generated.`,
            { variant: "success" }
          );
        }
      } catch (err) {
        console.error("AI Description Error after selection:", err);
        const errorMsg = err?.message || "Failed to generate AI description.";
        if (isMountedRef.current) {
          enqueueSnackbar(
            `Selected: ${materialName}. Failed to generate AI description: ${errorMsg}`,
            { variant: "warning" }
          );
          setDescription(""); // Clear description on error
        }
      } finally {
        if (isMountedRef.current) {
          setIsAiLoading(false);
        }
      }
    } else {
      if (isMountedRef.current) {
        enqueueSnackbar(
          `Selected item has no name, cannot generate AI description.`,
          { variant: "warning" }
        );
      }
    }
  };

  // Handle image zoom modal
  const handleImageClick = (url) => {
    setZoomedImageUrl(url);
    setIsZoomModalOpen(true);
  };

  const handleCloseZoomModal = () => {
    setIsZoomModalOpen(false);
    setZoomedImageUrl("");
  };

  return (
    <Box sx={{ maxWidth: 900, mx: "auto", p: 3 }}>
      <Typography variant="h4" gutterBottom>
        Create New Material
      </Typography>

      <Paper sx={{ p: 3 }}>
        <form onSubmit={handleSubmit}>
          <Grid container spacing={3}>
            {/* Name */}
            <Grid item xs={12} sm={6}>
              <TextField
                label="Material Name"
                fullWidth
                required
                id="material-name"
                name="material-name"
                value={name}
                onChange={(e) => setName(e.target.value)}
                disabled={isSaving}
                autoComplete="off"
              />
            </Grid>

            {/* SKU with Price Lookup Button */}
            <Grid item xs={12} sm={6}>
              <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                <TextField
                  label="SKU (Unique Identifier)"
                  fullWidth
                  id="material-sku"
                  name="material-sku"
                  value={sku}
                  onChange={(e) => setSku(e.target.value)}
                  disabled={isSaving || isPriceLoading}
                  helperText="Optional, but helps find best price"
                  sx={{ flexGrow: 1 }}
                  autoComplete="off"
                />
                <Button
                  variant="outlined"
                  size="small"
                  onClick={handlePriceLookup}
                  disabled={isPriceLoading || isSaving || (!name && !sku)}
                  startIcon={
                    isPriceLoading ? (
                      <CircularProgress size={16} />
                    ) : (
                      <SearchIcon />
                    )
                  }
                  sx={{ mt: -2 }} // Adjust margin to align better
                >
                  Lookup Price
                </Button>
              </Box>
            </Grid>

            {/* Description with AI Button */}
            <Grid item xs={12}>
              <Box sx={{ display: "flex", alignItems: "flex-start", gap: 1 }}>
                <TextField
                  label="Description"
                  fullWidth
                  multiline
                  rows={3}
                  id="material-description"
                  name="material-description"
                  value={description}
                  onChange={(e) => setDescription(e.target.value)}
                  disabled={isSaving || isAiLoading} // Removed isDetailLoading check
                  sx={{ flexGrow: 1 }}
                  autoComplete="off"
                />
                <Button
                  variant="outlined"
                  size="small"
                  onClick={handleAiDescription}
                  disabled={isAiLoading || isSaving || !name}
                  startIcon={
                    isAiLoading ? <CircularProgress size={16} /> : <AiIcon />
                  }
                  sx={{ mt: 1 }} // Align button roughly with text field top
                >
                  Suggest
                </Button>
              </Box>
            </Grid>

            {/* Category */}
            <Grid item xs={12} sm={6}>
              <TextField
                label="Category"
                fullWidth
                id="material-category"
                name="material-category"
                value={category}
                onChange={(e) => setCategory(e.target.value)}
                disabled={isSaving} // Removed isDetailLoading check
                helperText="e.g., Electrical, Plumbing, HVAC"
                autoComplete="category"
              />
            </Grid>

            {/* Supplier */}
            <Grid item xs={12} sm={6}>
              <TextField
                label="Supplier"
                fullWidth
                id="material-supplier"
                name="material-supplier"
                value={supplier}
                onChange={(e) => setSupplier(e.target.value)}
                disabled={isSaving}
                autoComplete="organization"
              />
            </Grid>

            {/* Cost Price */}
            <Grid item xs={12} sm={6}>
              <TextField
                label="Cost Price (Purchase Price)"
                fullWidth
                required
                type="text"
                inputMode="decimal"
                id="material-cost-price"
                name="material-cost-price"
                value={costPrice}
                onChange={handleNumberChange(setCostPrice)}
                disabled={isSaving}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">$</InputAdornment>
                  ),
                }}
                autoComplete="off"
              />
            </Grid>

            {/* Unit Price */}
            <Grid item xs={12} sm={6}>
              <TextField
                label="Unit Price (Selling Price)"
                fullWidth
                required
                type="text"
                inputMode="decimal"
                id="material-unit-price"
                name="material-unit-price"
                value={unitPrice}
                onChange={handleNumberChange(setUnitPrice)}
                disabled={isSaving}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">$</InputAdornment>
                  ),
                }}
                autoComplete="off"
              />
            </Grid>

            {/* Quantity */}
            <Grid item xs={12} sm={4}>
              <TextField
                label="Initial Quantity"
                fullWidth
                required
                type="text" // Use text for better control
                inputMode="numeric"
                id="material-quantity"
                name="material-quantity"
                value={quantity}
                onChange={handleIntegerChange(setQuantity)}
                disabled={isSaving}
                autoComplete="off"
              />
            </Grid>

            {/* Reorder Level */}
            <Grid item xs={12} sm={4}>
              <TextField
                label="Reorder Level"
                fullWidth
                type="text" // Use text for better control
                inputMode="numeric"
                id="material-reorder-level"
                name="material-reorder-level"
                value={reorderLevel}
                onChange={handleIntegerChange(setReorderLevel)}
                disabled={isSaving}
                helperText="Stock level that triggers reorder reminder"
                autoComplete="off"
              />
            </Grid>

            {/* Location */}
            <Grid item xs={12} sm={4}>
              <TextField
                label="Storage Location"
                fullWidth
                id="material-location"
                name="material-location"
                value={location}
                onChange={(e) => setLocation(e.target.value)}
                disabled={isSaving}
                helperText="e.g., Warehouse A, Shelf 3B"
                autoComplete="off" // Or potentially a custom location type if needed
              />
            </Grid>

            {/* Image URL */}
            <Grid item xs={12}>
              <TextField
                label="Image URL"
                fullWidth
                id="material-image-url"
                name="material-image-url"
                value={imageUrl}
                onChange={(e) => setImageUrl(e.target.value)}
                disabled={isSaving}
                helperText="Optional: URL of the primary product image"
                autoComplete="off"
              />
              {/* Display Image Preview */}
              {imageUrl && (
                <Box sx={{ mt: 2, textAlign: "center" }}>
                  <Typography variant="caption" display="block" gutterBottom>
                    Image Preview:
                  </Typography>
                  <img
                    src={imageUrl}
                    alt="Material Preview"
                    style={{
                      maxHeight: "150px",
                      maxWidth: "100%",
                      border: "1px solid #ccc",
                      borderRadius: "4px",
                      cursor: "zoom-in",
                    }} // Moved style here
                    onError={(e) => {
                      e.target.style.display =
                        "none"; /* Hide if image fails to load */
                    }}
                    onClick={() => handleImageClick(imageUrl)} // Moved onClick here
                  />
                </Box>
              )}
            </Grid>

            {/* Lookup Error Display */}
            {lookupError && (
              <Grid item xs={12}>
                {/* Display the potentially detailed error message */}
                <Alert severity="warning" onClose={() => setLookupError(null)}>
                  {lookupError || "An error occurred during lookup."}
                </Alert>
              </Grid>
            )}

            {/* Search Results Display */}
            {searchResults.length > 0 && (
              <Grid item xs={12}>
                <MaterialSearchResultsDisplay
                  results={searchResults}
                  onSelect={handleResultSelection}
                />
              </Grid>
            )}

            {/* Submit Button */}
            <Grid
              item
              xs={12}
              sx={{ display: "flex", justifyContent: "flex-end", mt: 2 }}
            >
              <Button
                variant="contained"
                color="primary"
                type="submit"
                startIcon={
                  isSaving ? (
                    <CircularProgress size={20} color="inherit" />
                  ) : (
                    <SaveIcon />
                  )
                }
                disabled={isSaving}
              >
                {isSaving ? "Saving..." : "Save Material"}
              </Button>
            </Grid>
          </Grid>
        </form>
      </Paper>

      {/* Image Zoom Modal */}
      <Dialog
        open={isZoomModalOpen}
        onClose={handleCloseZoomModal}
        maxWidth="lg"
      >
        <DialogContent sx={{ position: "relative", p: 0 }}>
          <IconButton
            aria-label="close"
            onClick={handleCloseZoomModal}
            sx={{
              position: "absolute",
              right: 8,
              top: 8,
              color: (theme) => theme.palette.grey[500],
              backgroundColor: "rgba(255, 255, 255, 0.7)",
              "&:hover": {
                backgroundColor: "rgba(255, 255, 255, 0.9)",
              },
            }}
          >
            <CloseIcon />
          </IconButton>
          <img
            src={zoomedImageUrl}
            alt="Zoomed Material Preview"
            style={{ width: "100%", height: "auto", display: "block" }}
          />
        </DialogContent>
      </Dialog>
    </Box>
  );
};

export default CreateMaterial;
