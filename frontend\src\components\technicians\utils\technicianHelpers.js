/**
 * Utility functions for technician-related components
 */

/**
 * Get Material UI color for technician status badges
 * @param {string} status - Technician availability status
 * @returns {string} - MUI color for the status chip
 */
export const getStatusColor = (status) => {
  switch (status) {
    case "available":
      return "success";
    case "busy":
      return "warning";
    case "off_duty":
      return "error";
    case "on_leave":
      return "info";
    default:
      return "default";
  }
};

/**
 * Filter technicians based on search term and filters
 * @param {Array} technicians - List of technicians to filter
 * @param {string} searchTerm - Search text
 * @param {Object} filters - Filter criteria
 * @returns {Array} - Filtered technicians
 */
export const filterTechnicians = (technicians, searchTerm, filters) => {
  return technicians.filter((tech) => {
    // Search term filter
    const searchMatch =
      searchTerm === "" ||
      `${tech.firstName} ${tech.lastName}`
        .toLowerCase()
        .includes(searchTerm.toLowerCase()) ||
      (tech.skills &&
        tech.skills.some((skill) =>
          skill.toLowerCase().includes(searchTerm.toLowerCase())
        ));

    // Status filter
    const statusMatch =
      filters.status === "all" || tech.availabilityStatus === filters.status;

    // Contractor type filter
    const typeMatch =
      filters.contractorType === "all" ||
      tech.contractorType === filters.contractorType;

    return searchMatch && statusMatch && typeMatch;
  });
};

/**
 * Sort technicians based on the specified sort criteria
 * @param {Array} technicians - List of technicians to sort
 * @param {string} sortBy - Sort criteria ('name', 'status', or 'type')
 * @returns {Array} - Sorted technicians
 */
export const sortTechnicians = (technicians, sortBy) => {
  return [...technicians].sort((a, b) => {
    switch (sortBy) {
      case "name":
        return `${a.firstName} ${a.lastName}`.localeCompare(
          `${b.firstName} ${b.lastName}`
        );
      case "status":
        return (a.availabilityStatus || "").localeCompare(
          b.availabilityStatus || ""
        );
      case "type":
        return (a.contractorType || "").localeCompare(b.contractorType || "");
      default:
        return 0;
    }
  });
};
