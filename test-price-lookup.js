const axios = require('axios');

console.log('Testing immediate price lookup functionality...');

async function testPriceLookup() {
  try {
    // First, get an auth token
    console.log('Getting auth token...');
    const loginResponse = await axios.post('http://localhost:5000/api/users/login', {
      email: '<EMAIL>',
      password: 'password123'
    });
    
    const authToken = loginResponse.data.token;
    console.log('✅ Auth token obtained');
    
    // Test the immediate price lookup endpoint
    console.log('Testing immediate price lookup...');
    const priceLookupResponse = await axios.post(
      'http://localhost:5000/api/quotes/lookup-prices',
      {
        items: [
          {
            name: 'GFCI Outlet',
            sku: 'TEST-GFCI-001',
            quantity: 5,
            description: 'Ground fault circuit interrupter outlet'
          },
          {
            name: 'LED Light Fixture',
            sku: 'TEST-LED-001',
            quantity: 3,
            description: '4ft LED linear light fixture'
          }
        ]
      },
      {
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json'
        },
        timeout: 30000
      }
    );
    
    console.log('✅ Immediate price lookup: SUCCESS');
    console.log(`Status: ${priceLookupResponse.status}`);
    console.log(`Success: ${priceLookupResponse.data.success}`);
    console.log(`Items processed: ${priceLookupResponse.data.summary.total}`);
    console.log(`Prices found: ${priceLookupResponse.data.summary.pricesFound}`);
    
    if (priceLookupResponse.data.items && priceLookupResponse.data.items.length > 0) {
      priceLookupResponse.data.items.forEach((item, index) => {
        console.log(`\nItem ${index + 1}: ${item.name}`);
        console.log(`  Price: $${item.price}`);
        console.log(`  Status: ${item.priceInfo.status}`);
        console.log(`  Source: ${item.priceInfo.source}`);
      });
    }
    
  } catch (error) {
    console.log('❌ Price lookup test: FAILED');
    if (error.response) {
      console.log(`Status: ${error.response.status}`);
      console.log(`Error: ${error.response.data.error || error.response.statusText}`);
    } else if (error.request) {
      console.log('No response received from server');
      console.log(`Error: ${error.message}`);
    } else {
      console.log(`Error: ${error.message}`);
    }
  }
}

// Check if server is running first
axios.get('http://localhost:5000/api/health', { timeout: 5000 })
  .then(response => {
    console.log('✅ Server is running');
    testPriceLookup();
  })
  .catch(error => {
    console.log('❌ Server is not running or not accessible');
    console.log('Please start the backend server first: cd backend && npm start');
  });