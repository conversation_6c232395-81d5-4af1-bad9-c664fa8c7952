import React, { Component, createRef } from "react";
import { Calendar } from "react-big-calendar";
import "react-big-calendar/lib/css/react-big-calendar.css";
import { Paper } from "@mui/material";

/**
 * CalendarWrapper component that wraps react-big-calendar with modern lifecycle methods
 * to avoid deprecated warnings in React StrictMode
 */
class CalendarWrapper extends Component {
  constructor(props) {
    super(props);
    this.calendarRef = createRef();
    const currentDate = props.date || props.defaultDate || new Date();
    const currentView = props.view || props.defaultView || "month";
    this.state = {
      currentDate,
      currentView,
      defaultDate: currentDate,
      defaultView: currentView,
    };
  }

  static getDerivedStateFromProps(nextProps, prevState) {
    // This replaces componentWillReceiveProps
    const { date, view } = nextProps;
    const updatedState = {};

    if (date && date !== prevState.currentDate) {
      updatedState.currentDate = date;
    }

    if (view && view !== prevState.currentView) {
      updatedState.currentView = view;
    }

    return Object.keys(updatedState).length > 0 ? updatedState : null;
  }

  componentDidUpdate(prevProps) {
    // Replace side effects previously done in UNSAFE_componentWillReceiveProps
    if (
      this.props.date !== prevProps.date ||
      this.props.view !== prevProps.view
    ) {
      // Any additional logic that needs to run after state updates
    }
  }

  handleNavigate = (newDate) => {
    this.setState({ currentDate: newDate });
    if (this.props.onNavigate) {
      this.props.onNavigate(newDate);
    }
  };

  handleView = (newView) => {
    this.setState({ currentView: newView });
    if (this.props.onView) {
      this.props.onView(newView);
    }
  };

  render() {
    const {
      localizer,
      events,
      components,
      eventPropGetter,
      defaultDate,
      defaultView,
      onSelectEvent,
      onDoubleClickEvent,
      onSelectSlot,
      ...rest
    } = this.props;
    const { currentDate, currentView } = this.state;

    // Validate and transform events with enhanced error checking
    // Guard against null or undefined events array
    const inputEvents = Array.isArray(events) ? events : [];

    // Enhanced event validation and transformation
    const validEvents = inputEvents
      .filter((event) => {
        try {
          // Basic type checking
          if (!event || typeof event !== "object") {
            console.warn("Filtered out null or non-object event");
            return false;
          }

          // Ensure dates are valid - check first since these are most critical
          if (!event.start || !event.end) {
            console.warn("Filtered out event with missing dates:", event);
            return false;
          }

          // Try to parse dates and verify they're valid
          const start = new Date(event.start);
          const end = new Date(event.end);

          if (isNaN(start.getTime()) || isNaN(end.getTime())) {
            console.warn("Filtered out event with invalid dates:", event);
            return false;
          }

          // Title validation is now optional since we'll provide a default
          // if it's missing, to prevent the Calendar render() error

          return true;
        } catch (err) {
          console.error("Event validation error:", err, event);
          return false;
        }
      })
      .map((event) => {
        // Create a new object with only the properties needed by react-big-calendar
        // With improved defensive programming to ensure no properties are undefined
        return {
          id: event.id || `event-${Math.random().toString(36).substr(2, 9)}`,
          // CRITICAL FIX: Always ensure title is a string with fallback
          // This prevents "Cannot read properties of undefined (reading 'title')" error
          title:
            event.title != null ? String(event.title).trim() : "Untitled Event",
          start: new Date(event.start),
          end: new Date(event.end),
          allDay: !!event.allDay,
          resource: event.resource || null,
        };
      });

    // More detailed debug logging in development
    if (process.env.NODE_ENV === "development") {
      if (events?.length !== validEvents.length) {
        console.warn("Event validation report:", {
          total: events?.length || 0,
          valid: validEvents.length,
          invalid: (events?.length || 0) - validEvents.length,
        });
      }

      if (validEvents.length === 0 && events?.length > 0) {
        console.warn("All events were filtered out due to validation issues");
      }
    }

    // Add error boundary UI
    if (!localizer) {
      console.error("Calendar localizer is missing");
      return (
        <div className="calendar-error">
          Calendar configuration error: missing localizer
        </div>
      );
    }

    // Define explicit views with fallbacks
    const calendarViews = {
      month: true,
      week: true,
      day: true,
      agenda: true,
      ...(rest.views || {}),
    };

    // Ensure we have at least one valid view
    if (Object.keys(calendarViews).length === 0) {
      calendarViews.month = true;
    }

    // Make sure current view is valid and exists in our views object
    const safeCurrentView = calendarViews[currentView] ? currentView : "month";

    // Define a safe render method
    try {
      return (
        <Paper className="calendar-wrapper" elevation={3}>
          <Calendar
            ref={this.calendarRef}
            localizer={localizer}
            events={validEvents}
            date={currentDate}
            view={safeCurrentView}
            onNavigate={this.handleNavigate}
            onView={this.handleView}
            components={components || {}}
            eventPropGetter={eventPropGetter}
            onSelectEvent={onSelectEvent}
            onDoubleClickEvent={onDoubleClickEvent}
            onSelectSlot={onSelectSlot}
            views={calendarViews}
            defaultDate={currentDate}
            defaultView={safeCurrentView}
            culture="en"
            // Careful with spreading - only include tested/known props
            className={rest.className}
            style={rest.style}
            messages={rest.messages}
            startAccessor="start"
            endAccessor="end"
            titleAccessor="title"
            tooltipAccessor={rest.tooltipAccessor || "title"}
            min={rest.min}
            max={rest.max}
            formats={rest.formats}
          />
        </Paper>
      );
    } catch (error) {
      console.error("Error rendering calendar:", error);
      return (
        <Paper className="calendar-error" elevation={3}>
          <div style={{ padding: "20px", color: "red" }}>
            Error rendering calendar. Please check your data and try again.
          </div>
        </Paper>
      );
    }
  }
}

export default CalendarWrapper;
