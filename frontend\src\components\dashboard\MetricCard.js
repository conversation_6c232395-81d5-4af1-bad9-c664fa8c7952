import React from "react";
import { Box, Card, CardContent, Typography } from "@mui/material";

/**
 * MetricCard component for displaying dashboard metrics
 *
 * @param {Object} props - Component props
 * @param {string} props.title - Metric title
 * @param {string|number} props.value - Metric value
 * @param {React.ComponentType} props.icon - Material UI Icon component
 * @param {string} [props.color] - Optional color for the card (primary, secondary, etc.)
 * @returns {JSX.Element} MetricCard component
 */
const MetricCard = ({ title, value, icon: Icon, color = "primary" }) => (
  <Card sx={{ height: "100%", borderTop: 3, borderColor: `${color}.main` }}>
    <CardContent>
      <Box sx={{ display: "flex", alignItems: "center", mb: 2 }}>
        <Icon sx={{ mr: 1, color: `${color}.main` }} />
        <Typography variant="h6" component="div">
          {title}
        </Typography>
      </Box>
      <Typography variant="h4" component="div" sx={{ textAlign: "center" }}>
        {value}
      </Typography>
    </CardContent>
  </Card>
);

export default MetricCard;
