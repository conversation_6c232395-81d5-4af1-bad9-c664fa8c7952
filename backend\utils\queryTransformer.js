/**
 * Query Transformer Utility
 * Transforms and optimizes search queries for better electrical material matching
 */

const logger = require("./logger");

/**
 * Electrical brand mappings for better search results
 */
const BRAND_MAPPINGS = {
  // Common electrical brands
  'siemens': ['siemens', 'sie'],
  'square d': ['square d', 'schneider', 'schneider electric'],
  'eaton': ['eaton', 'cutler hammer', 'ch'],
  'ge': ['ge', 'general electric'],
  'leviton': ['leviton'],
  'hubbell': ['hubbell'],
  'cooper': ['cooper', 'cooper wiring'],
  'lutron': ['lutron'],
  'pass & seymour': ['pass seymour', 'p&s'],
  'thomas & betts': ['thomas betts', 't&b'],
  'abb': ['abb'],
  'westinghouse': ['westinghouse'],
  'murray': ['murray'],
  'challenger': ['challenger'],
  'bryant': ['bryant'],
  'leviton': ['leviton'],
  'cooper': ['cooper'],
};

/**
 * Common electrical terms and their variations
 */
const ELECTRICAL_TERMS = {
  // Panel and breaker terms
  'panel': ['panel', 'load center', 'electrical panel', 'breaker panel'],
  'breaker': ['breaker', 'circuit breaker', 'cb'],
  'meter': ['meter', 'electric meter', 'electrical meter'],
  'gang': ['gang', 'space', 'circuit'],
  'amp': ['amp', 'ampere', 'a'],
  'volt': ['volt', 'voltage', 'v'],
  
  // Wire and cable terms
  'wire': ['wire', 'cable', 'conductor'],
  'awg': ['awg', 'gauge', 'ga'],
  'thhn': ['thhn', 'thwn', 'thwn-2'],
  'romex': ['romex', 'nm', 'nm-b'],
  'mc': ['mc', 'metal clad', 'armor cable'],
  'uf': ['uf', 'underground feeder'],
  
  // Conduit terms
  'conduit': ['conduit', 'raceway'],
  'emt': ['emt', 'electrical metallic tubing'],
  'pvc': ['pvc', 'plastic conduit'],
  'rigid': ['rigid', 'rmc', 'rigid metal conduit'],
  
  // Outlet and switch terms
  'outlet': ['outlet', 'receptacle', 'plug'],
  'gfci': ['gfci', 'gfi', 'ground fault'],
  'switch': ['switch', 'toggle', 'rocker'],
  'dimmer': ['dimmer', 'dimmer switch'],
  
  // Lighting terms
  'fixture': ['fixture', 'light fixture', 'luminaire'],
  'led': ['led', 'light emitting diode'],
  'fluorescent': ['fluorescent', 'fl', 't8', 't5'],
  'ballast': ['ballast', 'electronic ballast'],
};

/**
 * Transform a search query for better electrical material matching
 * @param {string} originalQuery - The original search query
 * @param {Object} options - Transformation options
 * @returns {Object} - Transformed query variations
 */
function transformQuery(originalQuery, options = {}) {
  if (!originalQuery || typeof originalQuery !== 'string') {
    return { primary: '', variations: [], metadata: {} };
  }

  const query = originalQuery.toLowerCase().trim();
  logger.debug(`[QueryTransformer] Transforming query: "${originalQuery}"`);

  const transformations = {
    primary: originalQuery, // Keep original as primary
    variations: [],
    metadata: {
      detectedBrand: null,
      detectedType: null,
      specifications: {},
      confidence: 0
    }
  };

  // Extract specifications (amperage, voltage, etc.)
  const specs = extractSpecifications(query);
  transformations.metadata.specifications = specs;

  // Detect brand
  const brand = detectBrand(query);
  if (brand) {
    transformations.metadata.detectedBrand = brand;
  }

  // Detect electrical item type
  const itemType = detectElectricalType(query);
  if (itemType) {
    transformations.metadata.detectedType = itemType;
  }

  // Generate query variations
  transformations.variations = generateQueryVariations(query, {
    brand,
    itemType,
    specifications: specs,
    ...options
  });

  // Calculate confidence score
  transformations.metadata.confidence = calculateConfidence(transformations);

  logger.debug(`[QueryTransformer] Generated ${transformations.variations.length} variations`, {
    brand: transformations.metadata.detectedBrand,
    type: transformations.metadata.detectedType,
    confidence: transformations.metadata.confidence
  });

  return transformations;
}

/**
 * Extract technical specifications from query
 * @param {string} query - Search query
 * @returns {Object} - Extracted specifications
 */
function extractSpecifications(query) {
  const specs = {};

  // Extract amperage (20A, 100 amp, etc.)
  const ampMatch = query.match(/(\d+)\s*(?:a|amp|ampere|amps)/i);
  if (ampMatch) {
    specs.amperage = parseInt(ampMatch[1]);
  }

  // Extract voltage (120V, 240 volt, etc.)
  const voltMatch = query.match(/(\d+)\s*(?:v|volt|voltage|volts)/i);
  if (voltMatch) {
    specs.voltage = parseInt(voltMatch[1]);
  }

  // Extract wire gauge (12 AWG, #14, etc.)
  const gaugeMatch = query.match(/(?:#|awg\s*)?(\d+)\s*(?:awg|gauge|ga)/i);
  if (gaugeMatch) {
    specs.wireGauge = parseInt(gaugeMatch[1]);
  }

  // Extract gang/space count (8-gang, 12 space, etc.)
  const gangMatch = query.match(/(\d+)\s*(?:gang|space|circuit)/i);
  if (gangMatch) {
    specs.gangCount = parseInt(gangMatch[1]);
  }

  // Extract length/footage (100 ft, 50 feet, etc.)
  const lengthMatch = query.match(/(\d+)\s*(?:ft|feet|foot|')/i);
  if (lengthMatch) {
    specs.length = parseInt(lengthMatch[1]);
  }

  return specs;
}

/**
 * Detect brand from query
 * @param {string} query - Search query
 * @returns {string|null} - Detected brand
 */
function detectBrand(query) {
  for (const [brand, variations] of Object.entries(BRAND_MAPPINGS)) {
    for (const variation of variations) {
      if (query.includes(variation.toLowerCase())) {
        return brand;
      }
    }
  }
  return null;
}

/**
 * Detect electrical item type from query
 * @param {string} query - Search query
 * @returns {string|null} - Detected item type
 */
function detectElectricalType(query) {
  for (const [type, variations] of Object.entries(ELECTRICAL_TERMS)) {
    for (const variation of variations) {
      if (query.includes(variation.toLowerCase())) {
        return type;
      }
    }
  }
  return null;
}

/**
 * Generate query variations for better search results
 * @param {string} query - Original query
 * @param {Object} context - Context information
 * @returns {Array} - Array of query variations
 */
function generateQueryVariations(query, context = {}) {
  const variations = [];
  const { brand, itemType, specifications } = context;

  // Variation 1: Simplified query (remove model numbers, keep key terms)
  const simplified = simplifyQuery(query);
  if (simplified !== query) {
    variations.push({
      query: simplified,
      type: 'simplified',
      confidence: 0.8
    });
  }

  // Variation 2: Brand + item type + key specs
  if (brand && itemType) {
    let brandTypeQuery = `${brand} ${itemType}`;
    
    // Add key specifications
    if (specifications.amperage) {
      brandTypeQuery += ` ${specifications.amperage}A`;
    }
    if (specifications.gangCount) {
      brandTypeQuery += ` ${specifications.gangCount} gang`;
    }
    
    variations.push({
      query: brandTypeQuery,
      type: 'brand_type_specs',
      confidence: 0.9
    });
  }

  // Variation 3: Generic item type + specs (no brand)
  if (itemType) {
    let genericQuery = itemType;
    
    if (specifications.amperage) {
      genericQuery += ` ${specifications.amperage} amp`;
    }
    if (specifications.gangCount) {
      genericQuery += ` ${specifications.gangCount} space`;
    }
    if (specifications.voltage) {
      genericQuery += ` ${specifications.voltage}V`;
    }
    
    variations.push({
      query: genericQuery,
      type: 'generic_with_specs',
      confidence: 0.7
    });
  }

  // Variation 4: Alternative brand names
  if (brand && BRAND_MAPPINGS[brand]) {
    for (const altBrand of BRAND_MAPPINGS[brand].slice(1)) { // Skip first (original)
      const altQuery = query.replace(new RegExp(brand, 'gi'), altBrand);
      if (altQuery !== query) {
        variations.push({
          query: altQuery,
          type: 'alternative_brand',
          confidence: 0.6
        });
      }
    }
  }

  // Variation 5: Remove specific model numbers but keep key identifiers
  const withoutModel = removeModelNumbers(query);
  if (withoutModel !== query) {
    variations.push({
      query: withoutModel,
      type: 'no_model_numbers',
      confidence: 0.5
    });
  }

  // Sort by confidence and remove duplicates
  return variations
    .filter((v, index, self) => 
      index === self.findIndex(t => t.query.toLowerCase() === v.query.toLowerCase())
    )
    .sort((a, b) => b.confidence - a.confidence)
    .slice(0, 5); // Limit to top 5 variations
}

/**
 * Simplify query by removing complex model numbers and keeping key terms
 * @param {string} query - Original query
 * @returns {string} - Simplified query
 */
function simplifyQuery(query) {
  return query
    // Remove complex model numbers (letters + numbers + special chars)
    .replace(/[A-Z]{2,}\d+[A-Z]*\d*/gi, '')
    // Remove standalone numbers that might be model numbers
    .replace(/\b\d{4,}\b/g, '')
    // Clean up extra spaces
    .replace(/\s+/g, ' ')
    .trim();
}

/**
 * Remove model numbers but keep specifications
 * @param {string} query - Original query
 * @returns {string} - Query without model numbers
 */
function removeModelNumbers(query) {
  return query
    // Remove model numbers like WMM81125RJ, QO120, etc.
    .replace(/\b[A-Z]{2,}\d+[A-Z]*\d*[A-Z]*\b/g, '')
    // Clean up
    .replace(/\s+/g, ' ')
    .trim();
}

/**
 * Calculate confidence score for transformations
 * @param {Object} transformations - Query transformations
 * @returns {number} - Confidence score (0-1)
 */
function calculateConfidence(transformations) {
  let score = 0;
  
  if (transformations.metadata.detectedBrand) score += 0.3;
  if (transformations.metadata.detectedType) score += 0.3;
  if (Object.keys(transformations.metadata.specifications).length > 0) score += 0.2;
  if (transformations.variations.length > 0) score += 0.2;
  
  return Math.min(score, 1.0);
}

module.exports = {
  transformQuery,
  extractSpecifications,
  detectBrand,
  detectElectricalType,
  generateQueryVariations,
  BRAND_MAPPINGS,
  ELECTRICAL_TERMS
};
