const mongoose = require("mongoose");

const TechnicianSchema = new mongoose.Schema(
  {
    firstName: {
      type: String,
      required: true,
      trim: true,
    },
    lastName: {
      type: String,
      required: true,
      trim: true,
    },
    email: {
      type: String,
      required: true,
      unique: true,
      trim: true,
      lowercase: true,
      match: [
        /^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/,
        "Please enter a valid email",
      ],
    },
    phone: {
      type: String,
      trim: true,
    },
    contractorType: {
      type: String,
      enum: [
        "HVAC",
        "Plumbing",
        "Electrical",
        "Landscaping",
        "Cleaning",
        "General Contractor",
        "Other",
      ],
      default: "General Contractor",
    },
    hourlyRate: {
      type: Number,
      min: 0,
    },
    availabilityStatus: {
      type: String,
      enum: ["available", "busy", "off_duty", "on_leave"],
      default: "available",
    },
    skills: {
      type: [String],
      default: [],
    },
    services: {
      type: [String],
      default: [],
    },
    profileImage: {
      type: String,
    },
    joinDate: {
      type: Date,
      default: Date.now,
    },
    // Reference to associated jobs
    jobs: [
      {
        type: mongoose.Schema.Types.ObjectId,
        ref: "Job",
      },
    ],
  },
  {
    timestamps: true,
  }
);

// Virtual for technician's full name
TechnicianSchema.virtual("fullName").get(function () {
  return `${this.firstName} ${this.lastName}`;
});

// Method to get recent jobs
TechnicianSchema.methods.getRecentJobs = async function (limit = 5) {
  const Job = mongoose.model("Job");
  const recentJobs = await Job.find({ technician: this._id })
    .sort({ updatedAt: -1 })
    .limit(limit)
    .populate("customer", "firstName lastName company");

  return recentJobs;
};

// Database indexes for performance optimization
TechnicianSchema.index({ email: 1 }, { unique: true }); // Email lookup
TechnicianSchema.index({ contractorType: 1 }); // Filter by contractor type
TechnicianSchema.index({ availabilityStatus: 1 }); // Available technicians
TechnicianSchema.index({ skills: 1 }); // Skill-based matching
TechnicianSchema.index({ services: 1 }); // Service-based matching
TechnicianSchema.index({ hourlyRate: 1 }); // Rate-based queries
TechnicianSchema.index({ joinDate: -1 }); // Recent hires
TechnicianSchema.index({ firstName: 1, lastName: 1 }); // Name search
TechnicianSchema.index({ contractorType: 1, availabilityStatus: 1 }); // Available by type
TechnicianSchema.index({ createdAt: -1 }); // Recent technicians

module.exports = mongoose.model("Technician", TechnicianSchema);
