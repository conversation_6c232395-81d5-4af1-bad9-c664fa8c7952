// Common formatter functions

export const formatCurrency = (value) => {
  // Handle null, undefined, or invalid values
  if (value === null || value === undefined || value === "" || isNaN(value)) {
    return "$--.--";
  }

  // Convert string to number if needed
  if (typeof value === "string") {
    const parsed = parseFloat(value.replace(/[^0-9.-]+/g, ""));
    if (isNaN(parsed)) {
      return "$--.--";
    }
    value = parsed;
  }

  return new Intl.NumberFormat("en-US", {
    style: "currency",
    currency: "USD",
  }).format(value);
};

export const formatDate = (date) => {
  return new Date(date).toLocaleDateString("en-US");
};

export const formatPhoneNumber = (phone) => {
  // Format as (XXX) XXX-XXXX
  const cleaned = ("" + phone).replace(/\D/g, "");
  const match = cleaned.match(/^(\d{3})(\d{3})(\d{4})$/);
  if (match) {
    return `(${match[1]}) ${match[2]}-${match[3]}`;
  }
  return phone;
};
