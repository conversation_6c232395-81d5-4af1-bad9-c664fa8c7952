import React from "react";
import {
  Box,
  Paper,
  Typography,
  Di<PERSON>r,
  <PERSON><PERSON>ip,
  CircularProgress,
  Button,
  Alert,
  Stack,
} from "@mui/material";
import {
  Timeline as TimelineIcon,
  Refresh as RefreshIcon,
} from "@mui/icons-material";

const GanttChartView = ({
  loading,
  error,
  jobId,
  progressData,
  onRefreshClick,
}) => {
  // Calculate the total duration of the job in days
  const calculateTotalDuration = () => {
    if (!progressData || !progressData.timeline) return 0;

    const startDate = new Date(progressData.timeline.startDate);
    const endDate = new Date(progressData.timeline.estimatedEndDate);

    // Calculate the difference in milliseconds
    const diffTime = Math.abs(endDate - startDate);
    // Convert to days
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  };

  // Get current progress percentage
  const getCurrentProgress = () => {
    if (!progressData) return 0;
    return progressData.percentComplete || 0;
  };

  // Format date to readable string
  const formatDate = (dateString) => {
    if (!dateString) return "N/A";
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  // Get status color for milestones and tasks
  const getStatusColor = (status) => {
    switch (status) {
      case "completed":
        return "#4caf50"; // Green
      case "in_progress":
        return "#2196f3"; // Blue
      case "delayed":
        return "#ff9800"; // Orange
      case "at_risk":
        return "#f44336"; // Red
      case "pending":
      default:
        return "#9e9e9e"; // Grey
    }
  };

  // Calculate the position and width for each task in the Gantt chart
  const calculateTaskPosition = (task) => {
    if (!progressData || !progressData.timeline) return { left: 0, width: 0 };

    const startDate = new Date(progressData.timeline.startDate);
    const totalDuration = calculateTotalDuration();

    const taskStartDate = new Date(task.startDate);
    const taskEndDate = new Date(task.endDate || task.estimatedEndDate);

    // Calculate days from project start to task start
    const startDiffTime = Math.abs(taskStartDate - startDate);
    const startDiffDays = Math.ceil(startDiffTime / (1000 * 60 * 60 * 24));

    // Calculate task duration in days
    const taskDiffTime = Math.abs(taskEndDate - taskStartDate);
    const taskDuration = Math.ceil(taskDiffTime / (1000 * 60 * 60 * 24));

    // Calculate position as percentage of total duration
    const left = (startDiffDays / totalDuration) * 100;
    const width = (taskDuration / totalDuration) * 100;

    return { left: `${left}%`, width: `${width}%` };
  };

  // Calculate the position for the current date indicator
  const calculateCurrentDatePosition = () => {
    if (!progressData || !progressData.timeline) return { left: 0 };

    const startDate = new Date(progressData.timeline.startDate);
    const endDate = new Date(progressData.timeline.estimatedEndDate);
    const currentDate = new Date();

    // If current date is before start date, position at 0%
    if (currentDate < startDate) return { left: "0%" };

    // If current date is after end date, position at 100%
    if (currentDate > endDate) return { left: "100%" };

    // Calculate position as percentage of total duration
    const totalDuration = calculateTotalDuration();
    const diffTime = Math.abs(currentDate - startDate);
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    return { left: `${(diffDays / totalDuration) * 100}%` };
  };

  return (
    <Paper elevation={0} sx={{ p: 2, height: "100%" }}>
      <Box
        display="flex"
        justifyContent="space-between"
        alignItems="center"
        mb={2}
      >
        <Typography variant="h6" fontWeight="bold">
          Job Timeline
        </Typography>
        <Button
          startIcon={<RefreshIcon />}
          size="small"
          onClick={onRefreshClick}
          disabled={loading}
        >
          Update Prediction
        </Button>
      </Box>

      <Divider sx={{ mb: 2 }} />

      {loading && (
        <Box display="flex" justifyContent="center" p={3}>
          <CircularProgress size={40} />
        </Box>
      )}

      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      {!loading && !error && !progressData && (
        <Box textAlign="center" p={3}>
          <TimelineIcon color="disabled" sx={{ fontSize: 60, mb: 2 }} />
          <Typography color="textSecondary">
            No progress prediction available
          </Typography>
          <Button
            variant="outlined"
            size="small"
            sx={{ mt: 2 }}
            onClick={onRefreshClick}
          >
            Generate Prediction
          </Button>
        </Box>
      )}

      {!loading && !error && progressData && (
        <>
          <Box mb={3}>
            <Stack direction="row" spacing={2} sx={{ mb: 2 }}>
              <Box>
                <Typography variant="body2" color="text.secondary">
                  Start Date
                </Typography>
                <Typography variant="body1" fontWeight="bold">
                  {formatDate(progressData.timeline?.startDate)}
                </Typography>
              </Box>
              <Box>
                <Typography variant="body2" color="text.secondary">
                  Current Progress
                </Typography>
                <Typography variant="body1" fontWeight="bold">
                  {getCurrentProgress()}%
                </Typography>
              </Box>
              <Box>
                <Typography variant="body2" color="text.secondary">
                  Estimated Completion
                </Typography>
                <Typography variant="body1" fontWeight="bold">
                  {formatDate(progressData.timeline?.estimatedEndDate)}
                </Typography>
              </Box>
              {progressData.isDelayed && (
                <Box>
                  <Typography variant="body2" color="error">
                    Delayed
                  </Typography>
                  <Typography variant="body1" fontWeight="bold" color="error">
                    {progressData.delayDays} days
                  </Typography>
                </Box>
              )}
            </Stack>

            {progressData.recommendation && (
              <Alert severity="info" sx={{ mt: 2 }}>
                <Typography variant="body2">
                  {progressData.recommendation}
                </Typography>
              </Alert>
            )}
          </Box>

          {/* Gantt Chart */}
          <Box sx={{ position: "relative", mt: 4, mb: 2 }}>
            {/* Timeline Header */}
            <Box
              sx={{
                display: "flex",
                justifyContent: "space-between",
                borderBottom: "1px solid",
                borderColor: "divider",
                pb: 1,
                mb: 2,
              }}
            >
              <Typography variant="body2">
                {formatDate(progressData.timeline?.startDate)}
              </Typography>
              <Typography variant="body2">
                {formatDate(progressData.timeline?.estimatedEndDate)}
              </Typography>
            </Box>

            {/* Current Date Indicator */}
            <Box
              sx={{
                position: "absolute",
                top: 24,
                bottom: 0,
                width: "2px",
                backgroundColor: "primary.main",
                zIndex: 2,
                ...calculateCurrentDatePosition(),
              }}
            >
              <Tooltip title="Current Date">
                <Box
                  sx={{
                    position: "absolute",
                    top: -4,
                    left: -4,
                    width: 10,
                    height: 10,
                    borderRadius: "50%",
                    backgroundColor: "primary.main",
                  }}
                />
              </Tooltip>
            </Box>

            {/* Tasks / Milestones */}
            {progressData.tasks?.map((task, index) => (
              <Box key={index} sx={{ position: "relative", mb: 2, height: 30 }}>
                <Tooltip
                  title={
                    <>
                      <Typography variant="subtitle2">
                        {task.description}
                      </Typography>
                      <Typography variant="body2">
                        {formatDate(task.startDate)} -{" "}
                        {formatDate(task.endDate || task.estimatedEndDate)}
                      </Typography>
                      <Typography variant="body2">
                        Status: {task.status}
                      </Typography>
                    </>
                  }
                >
                  <Box
                    sx={{
                      position: "absolute",
                      height: "100%",
                      borderRadius: 1,
                      backgroundColor: getStatusColor(task.status),
                      display: "flex",
                      alignItems: "center",
                      pl: 1,
                      ...calculateTaskPosition(task),
                    }}
                  >
                    <Typography
                      variant="caption"
                      sx={{
                        color: "white",
                        whiteSpace: "nowrap",
                        overflow: "hidden",
                        textOverflow: "ellipsis",
                      }}
                    >
                      {task.description}
                    </Typography>
                  </Box>
                </Tooltip>
              </Box>
            ))}

            {/* Milestones */}
            {progressData.milestones?.map((milestone, index) => (
              <Box key={index} sx={{ position: "relative", mt: 3 }}>
                <Tooltip
                  title={
                    <>
                      <Typography variant="subtitle2">
                        {milestone.description}
                      </Typography>
                      <Typography variant="body2">
                        {formatDate(milestone.date)}
                      </Typography>
                      <Typography variant="body2">
                        Status: {milestone.status}
                      </Typography>
                    </>
                  }
                >
                  <Box
                    sx={{
                      position: "absolute",
                      width: 12,
                      height: 12,
                      borderRadius: "50%",
                      backgroundColor: getStatusColor(milestone.status),
                      top: -6,
                      transform: "translateX(-50%)",
                      left: calculateTaskPosition({
                        startDate: milestone.date,
                        endDate: milestone.date,
                      }).left,
                      zIndex: 1,
                    }}
                  />
                </Tooltip>
              </Box>
            ))}
          </Box>

          {/* Legend */}
          <Box
            sx={{
              display: "flex",
              flexWrap: "wrap",
              gap: 2,
              mt: 4,
              pt: 2,
              borderTop: "1px solid",
              borderColor: "divider",
            }}
          >
            <Box sx={{ display: "flex", alignItems: "center" }}>
              <Box
                sx={{
                  width: 12,
                  height: 12,
                  backgroundColor: getStatusColor("completed"),
                  mr: 1,
                }}
              />
              <Typography variant="caption">Completed</Typography>
            </Box>
            <Box sx={{ display: "flex", alignItems: "center" }}>
              <Box
                sx={{
                  width: 12,
                  height: 12,
                  backgroundColor: getStatusColor("in_progress"),
                  mr: 1,
                }}
              />
              <Typography variant="caption">In Progress</Typography>
            </Box>
            <Box sx={{ display: "flex", alignItems: "center" }}>
              <Box
                sx={{
                  width: 12,
                  height: 12,
                  backgroundColor: getStatusColor("delayed"),
                  mr: 1,
                }}
              />
              <Typography variant="caption">Delayed</Typography>
            </Box>
            <Box sx={{ display: "flex", alignItems: "center" }}>
              <Box
                sx={{
                  width: 12,
                  height: 12,
                  backgroundColor: getStatusColor("at_risk"),
                  mr: 1,
                }}
              />
              <Typography variant="caption">At Risk</Typography>
            </Box>
            <Box sx={{ display: "flex", alignItems: "center" }}>
              <Box
                sx={{
                  width: 12,
                  height: 12,
                  backgroundColor: getStatusColor("pending"),
                  mr: 1,
                }}
              />
              <Typography variant="caption">Pending</Typography>
            </Box>
          </Box>
        </>
      )}
    </Paper>
  );
};

export default GanttChartView;
