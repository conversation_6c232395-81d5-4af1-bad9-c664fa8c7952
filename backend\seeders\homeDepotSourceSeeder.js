const mongoose = require("mongoose");
const MaterialSource = require("../models/MaterialSource");
const connectDB = require("../config/database");
require("dotenv").config({
  path: require("path").resolve(__dirname, "../.env"),
}); // Ensure correct .env path

const addHomeDepotSource = async () => {
  await connectDB();

  try {
    // Find existing or create new Home Depot source
    let source = await MaterialSource.findOne({ type: "HOME_DEPOT" });

    if (source) {
      console.log("Home Depot source already exists. Updating...");
      source.name = "Home Depot";
      source.baseUrl = "https://www.homedepot.com";
      source.enabled = true; // Ensure it's enabled
      // Set reasonable rate limits for Home Depot
      source.rateLimit = {
        requestsPerMinute: 15, // Slightly lower than Platt initially
        cooldownPeriod: 1000, // 1 second cooldown
      };
      // Reset scrapeConfig, selectors will be loaded from HomeDepotScraper.js
      source.scrapeConfig = {
        selectors: new Map(),
        allowedDomains: ["www.homedepot.com"],
        excludePatterns: [],
        sessionDuration: 3600,
        userAgents: [],
      };
      await source.save();
      console.log("Home Depot source updated.");
    } else {
      source = new MaterialSource({
        name: "Home Depot",
        type: "HOME_DEPOT",
        baseUrl: "https://www.homedepot.com",
        enabled: true, // Enable by default
        rateLimit: {
          requestsPerMinute: 15,
          cooldownPeriod: 1000,
        },
        scrapeConfig: {
          selectors: new Map(), // Selectors defined in the scraper class
          allowedDomains: ["www.homedepot.com"],
        },
      });

      await source.save();
      console.log("Home Depot source added successfully.");
    }
  } catch (error) {
    console.error("Error adding/updating Home Depot source:", error);
  } finally {
    mongoose.connection.close();
  }
};

addHomeDepotSource();
