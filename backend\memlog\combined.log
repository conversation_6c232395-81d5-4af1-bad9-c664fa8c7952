2025-08-29 18:20:36 [[34m[34mdebug[34m[39m] [general]: [GeminiService Module Load] typeof AiServiceError: function, AiServiceError: undefined, typeof GoogleGenerativeAIResponseError: function, GoogleGenerativeAIResponseError: undefined | {"service":"workiz-api"}
2025-08-29 18:20:36 [[32m[32minfo[32m[39m] [general]: AI Model Configuration initialized with the following models: | {"service":"workiz-api"}
2025-08-29 18:20:36 [[32m[32minfo[32m[39m] [general]: Primary Model: models/gemini-2.5-pro-preview-03-25 (5 RPM, JSON: true) | {"service":"workiz-api"}
2025-08-29 18:20:36 [[32m[32minfo[32m[39m] [general]: Fallback 1: models/gemini-2.0-flash (2 RPM, JSON: true) | {"service":"workiz-api"}
2025-08-29 18:20:36 [[32m[32minfo[32m[39m] [general]: Fallback 2: models/gemini-1.5-flash (15 RPM, JSON: true) | {"service":"workiz-api"}
2025-08-29 18:20:36 [[32m[32minfo[32m[39m] [general]: Fallback 3: models/gemini-2.0-flash-lite (30 RPM, JSON: false) | {"service":"workiz-api"}
2025-08-29 18:20:36 [[32m[32minfo[32m[39m] [general]: Starting async server setup... | {"service":"workiz-api"}
2025-08-29 18:20:36 [[32m[32minfo[32m[39m] [general]: AI generation log file cleaned successfully | {"service":"workiz-api"}
2025-08-29 18:20:36 [[32m[32minfo[32m[39m] [general]: Connecting to Database... | {"service":"workiz-api"}
2025-08-29 18:20:36 [[32m[32minfo[32m[39m] [general]: Database connection established. | {"service":"workiz-api"}
2025-08-29 18:20:36 [[32m[32minfo[32m[39m] [general]: Initializing Change Stream Service... | {"service":"workiz-api"}
2025-08-29 18:20:36 [[32m[32minfo[32m[39m] [general]: [ChangeStreamService] Initializing. Received 'conn' object type: object, is Mongoose singleton: true | {"service":"workiz-api"}
2025-08-29 18:20:36 [[32m[32minfo[32m[39m] [general]: [ChangeStreamService] Mongoose connection readyState: 1 (expected: 1 for connected) | {"service":"workiz-api"}
2025-08-29 18:20:36 [[32m[32minfo[32m[39m] [general]: [ChangeStreamService] Setting up change stream for the "quotes" collection... | {"service":"workiz-api"}
2025-08-29 18:20:36 [[32m[32minfo[32m[39m] [general]: [ChangeStreamService] Change stream successfully created and added to active streams | {"service":"workiz-api"}
2025-08-29 18:20:36 [[32m[32minfo[32m[39m] [general]: [ChangeStreamService] MongoDB replica set detected, Change Streams should work | {"service":"workiz-api"}
2025-08-29 18:20:36 [[32m[32minfo[32m[39m] [general]: [ChangeStreamService] Successfully listening for changes on "quotes" collection. | {"service":"workiz-api"}
2025-08-29 18:20:36 [[32m[32minfo[32m[39m] [general]: [ChangeStreamService] Change stream pipeline: | {"service":"workiz-api"}
2025-08-29 18:20:36 [[32m[32minfo[32m[39m] [general]: [ChangeStreamService] Number of active streams: | {"service":"workiz-api"}
2025-08-29 18:20:36 [[32m[32minfo[32m[39m] [general]: Change Stream Service initialized successfully. | {"service":"workiz-api"}
2025-08-29 18:20:36 [[32m[32minfo[32m[39m] [general]: Initializing Scraper Service... | {"service":"workiz-api"}
2025-08-29 18:20:36 [[32m[32minfo[32m[39m] [general]: Found 3 enabled material sources in DB. | {"service":"workiz-api"}
2025-08-29 18:20:36 [[34m[34mdebug[34m[39m] [general]: Enabled Source Found: ID=682d20deccc41cc693d861e0, Name=Home Depot Test, Type=HOME_DEPOT | {"service":"workiz-api"}
2025-08-29 18:20:36 [[34m[34mdebug[34m[39m] [general]: Enabled Source Found: ID=682bdb0e477e692811d861e0, Name=Home Depot, Type=HOME_DEPOT | {"service":"workiz-api"}
2025-08-29 18:20:36 [[34m[34mdebug[34m[39m] [general]: Enabled Source Found: ID=682bdb1305cc8e619ad861e0, Name=Platt Electric Supply, Type=PLATT | {"service":"workiz-api"}
2025-08-29 18:20:36 [[32m[32minfo[32m[39m] [general]: Initializing scrapers for 3 sources... | {"service":"workiz-api"}
2025-08-29 18:20:36 [[34m[34mdebug[34m[39m] [general]: Attempting to initialize scraper for source: Home Depot Test (ID: 682d20deccc41cc693d861e0, Type: HOME_DEPOT) | {"service":"workiz-api"}
2025-08-29 18:20:36 [[34m[34mdebug[34m[39m] [general]: [InitScraper:Home Depot Test] Attempting to instantiate scraper class for type: HOME_DEPOT | {"service":"workiz-api"}
2025-08-29 18:20:36 [[32m[32minfo[32m[39m] [general]: [InitScraper:Home Depot Test] Creating Crawl4AI scraper for type: HOME_DEPOT | {"service":"workiz-api"}
2025-08-29 18:20:36 [[34m[34mdebug[34m[39m] [general]: [Crawl4AI ScraperFactory] Creating scraper for source type: HOME_DEPOT | {"service":"workiz-api"}
2025-08-29 18:20:36 [[34m[34mdebug[34m[39m] [general]: [InitScraper:Home Depot Test] Scraper class instantiated successfully. | {"service":"workiz-api"}
2025-08-29 18:20:36 [[34m[34mdebug[34m[39m] [general]: [InitScraper:Home Depot Test] Calling scraper.initialize()... | {"service":"workiz-api"}
2025-08-29 18:20:36 [[32m[32minfo[32m[39m] [general]: Initializing Home Depot Test scraper with Crawl4AI | {"service":"workiz-api"}
2025-08-29 18:20:36 [[34m[34mdebug[34m[39m] [general]: Testing Python executable: python | {"service":"workiz-api"}
2025-08-29 18:20:36 [[32m[32minfo[32m[39m] [general]: Successfully detected Python executable: python (Python 3.12.6) | {"service":"workiz-api"}
2025-08-29 18:20:36 [[32m[32minfo[32m[39m] [general]: Using Python executable: python | {"service":"workiz-api"}
2025-08-29 18:20:39 [[32m[32minfo[32m[39m] [general]: Crawl4AI service initialized successfully | {"service":"workiz-api"}
2025-08-29 18:20:39 [[32m[32minfo[32m[39m] [general]: Initializing Home Depot Crawl4AI scraper | {"service":"workiz-api"}
2025-08-29 18:20:39 [[32m[32minfo[32m[39m] [general]: Scraper for Home Depot Test initialized successfully | {"service":"workiz-api"}
2025-08-29 18:20:39 [[34m[34mdebug[34m[39m] [general]: [InitScraper:Home Depot Test] scraper.initialize() completed. | {"service":"workiz-api"}
2025-08-29 18:20:39 [[34m[34mdebug[34m[39m] [general]: Added Home Depot Test scraper instance to map with key 682d20deccc41cc693d861e0. | {"service":"workiz-api"}
2025-08-29 18:20:39 [[32m[32minfo[32m[39m] [general]: Successfully initialized and added scraper for Home Depot Test. | {"service":"workiz-api"}
2025-08-29 18:20:39 [[34m[34mdebug[34m[39m] [general]: Source type map updated: HOME_DEPOT -> 682d20deccc41cc693d861e0 | {"service":"workiz-api"}
2025-08-29 18:20:39 [[34m[34mdebug[34m[39m] [general]: Attempting to initialize scraper for source: Home Depot (ID: 682bdb0e477e692811d861e0, Type: HOME_DEPOT) | {"service":"workiz-api"}
2025-08-29 18:20:39 [[34m[34mdebug[34m[39m] [general]: [InitScraper:Home Depot] Attempting to instantiate scraper class for type: HOME_DEPOT | {"service":"workiz-api"}
2025-08-29 18:20:39 [[32m[32minfo[32m[39m] [general]: [InitScraper:Home Depot] Creating Crawl4AI scraper for type: HOME_DEPOT | {"service":"workiz-api"}
2025-08-29 18:20:39 [[34m[34mdebug[34m[39m] [general]: [Crawl4AI ScraperFactory] Creating scraper for source type: HOME_DEPOT | {"service":"workiz-api"}
2025-08-29 18:20:39 [[34m[34mdebug[34m[39m] [general]: [InitScraper:Home Depot] Scraper class instantiated successfully. | {"service":"workiz-api"}
2025-08-29 18:20:39 [[34m[34mdebug[34m[39m] [general]: [InitScraper:Home Depot] Calling scraper.initialize()... | {"service":"workiz-api"}
2025-08-29 18:20:39 [[32m[32minfo[32m[39m] [general]: Initializing Home Depot scraper with Crawl4AI | {"service":"workiz-api"}
2025-08-29 18:20:39 [[32m[32minfo[32m[39m] [general]: Initializing Home Depot Crawl4AI scraper | {"service":"workiz-api"}
2025-08-29 18:20:39 [[32m[32minfo[32m[39m] [general]: Scraper for Home Depot initialized successfully | {"service":"workiz-api"}
2025-08-29 18:20:39 [[34m[34mdebug[34m[39m] [general]: [InitScraper:Home Depot] scraper.initialize() completed. | {"service":"workiz-api"}
2025-08-29 18:20:39 [[34m[34mdebug[34m[39m] [general]: Added Home Depot scraper instance to map with key 682bdb0e477e692811d861e0. | {"service":"workiz-api"}
2025-08-29 18:20:39 [[32m[32minfo[32m[39m] [general]: Successfully initialized and added scraper for Home Depot. | {"service":"workiz-api"}
2025-08-29 18:20:39 [[34m[34mdebug[34m[39m] [general]: Source type map updated: HOME_DEPOT -> 682bdb0e477e692811d861e0 | {"service":"workiz-api"}
2025-08-29 18:20:39 [[34m[34mdebug[34m[39m] [general]: Attempting to initialize scraper for source: Platt Electric Supply (ID: 682bdb1305cc8e619ad861e0, Type: PLATT) | {"service":"workiz-api"}
2025-08-29 18:20:39 [[34m[34mdebug[34m[39m] [general]: [InitScraper:Platt Electric Supply] Attempting to instantiate scraper class for type: PLATT | {"service":"workiz-api"}
2025-08-29 18:20:39 [[32m[32minfo[32m[39m] [general]: [InitScraper:Platt Electric Supply] Creating Crawl4AI scraper for type: PLATT | {"service":"workiz-api"}
2025-08-29 18:20:39 [[34m[34mdebug[34m[39m] [general]: [Crawl4AI ScraperFactory] Creating scraper for source type: PLATT | {"service":"workiz-api"}
2025-08-29 18:20:39 [[34m[34mdebug[34m[39m] [general]: [InitScraper:Platt Electric Supply] Scraper class instantiated successfully. | {"service":"workiz-api"}
2025-08-29 18:20:39 [[34m[34mdebug[34m[39m] [general]: [InitScraper:Platt Electric Supply] Calling scraper.initialize()... | {"service":"workiz-api"}
2025-08-29 18:20:39 [[32m[32minfo[32m[39m] [general]: Initializing Platt Electric Supply scraper with Crawl4AI | {"service":"workiz-api"}
2025-08-29 18:20:39 [[32m[32minfo[32m[39m] [general]: [Platt Crawl4AI] Initializing Platt scraper with enhanced anti-bot measures | {"service":"workiz-api"}
2025-08-29 18:20:41 [[32m[32minfo[32m[39m] [general]: Starting Price Lookup Poller as additional fallback mechanism... | {"service":"workiz-api"}
2025-08-29 18:20:41 [[32m[32minfo[32m[39m] [general]: [PriceLookupPoller] Starting price lookup polling service | {"service":"workiz-api"}
2025-08-29 18:20:46 [[32m[32minfo[32m[39m] [general]: [Platt Crawl4AI] Successfully connected to Platt website | {"service":"workiz-api"}
2025-08-29 18:20:46 [[32m[32minfo[32m[39m] [general]: Scraper for Platt Electric Supply initialized successfully | {"service":"workiz-api"}
2025-08-29 18:20:46 [[34m[34mdebug[34m[39m] [general]: [InitScraper:Platt Electric Supply] scraper.initialize() completed. | {"service":"workiz-api"}
2025-08-29 18:20:46 [[34m[34mdebug[34m[39m] [general]: Added Platt Electric Supply scraper instance to map with key 682bdb1305cc8e619ad861e0. | {"service":"workiz-api"}
2025-08-29 18:20:46 [[32m[32minfo[32m[39m] [general]: Successfully initialized and added scraper for Platt Electric Supply. | {"service":"workiz-api"}
2025-08-29 18:20:46 [[34m[34mdebug[34m[39m] [general]: Source type map updated: PLATT -> 682bdb1305cc8e619ad861e0 | {"service":"workiz-api"}
2025-08-29 18:20:46 [[32m[32minfo[32m[39m] [general]: ScraperService initialization loop finished. | {"service":"workiz-api"}
2025-08-29 18:20:46 [[32m[32minfo[32m[39m] [general]: Final Scraper Count: 3 | {"service":"workiz-api"}
2025-08-29 18:20:46 [[32m[32minfo[32m[39m] [general]: Final Scrapers Map Keys: ["682d20deccc41cc693d861e0","682bdb0e477e692811d861e0","682bdb1305cc8e619ad861e0"] | {"service":"workiz-api"}
2025-08-29 18:20:46 [[32m[32minfo[32m[39m] [general]: Final Source Type Map: {"HOME_DEPOT":"682bdb0e477e692811d861e0","PLATT":"682bdb1305cc8e619ad861e0"} | {"service":"workiz-api"}
2025-08-29 18:20:46 [[32m[32minfo[32m[39m] [general]: Scraper Service initialized successfully. | {"service":"workiz-api"}
2025-08-29 18:20:46 [[32m[32minfo[32m[39m] [general]: Running basic material source validation... | {"service":"workiz-api"}
2025-08-29 18:20:46 [[32m[32minfo[32m[39m] [general]: [SCRAPER_HEALTH] Validating material sources... | {"service":"workiz-api"}
2025-08-29 18:20:46 [[32m[32minfo[32m[39m] [general]: [SCRAPER_HEALTH] Material source validation completed: 3 total sources | {"service":"workiz-api"}
2025-08-29 18:20:46 [[32m[32minfo[32m[39m] [general]: [SCRAPER_HEALTH] Home Depot found: true, Platt found: true | {"service":"workiz-api"}
2025-08-29 18:20:46 [[32m[32minfo[32m[39m] [general]: [SCRAPER_HEALTH] Valid sources count: 3 | {"service":"workiz-api"}
2025-08-29 18:20:46 [[32m[32minfo[32m[39m] [general]: [SCRAPER_HEALTH] No validation issues found | {"service":"workiz-api"}
2025-08-29 18:20:46 [[32m[32minfo[32m[39m] [general]: Material Source Validation completed: 3 sources, 3 valid | {"service":"workiz-api"}
2025-08-29 18:20:46 [[32m[32minfo[32m[39m] [general]: ✅ Skipping comprehensive Crawl4AI health check for faster startup | {"service":"workiz-api"}
2025-08-29 18:20:46 [[32m[32minfo[32m[39m] [general]: Configuring Express middleware and routes... | {"service":"workiz-api"}
2025-08-29 18:20:48 [[32m[32minfo[32m[39m] [general]: Loaded preferred search domains: homedepot.com, lowes.com, grainger.com, platt.com, supplyhouse.com, ferguson.com | {"service":"workiz-api"}
2025-08-29 18:20:48 [[32m[32minfo[32m[39m] [general]: Configuring static file serving for /uploads path: C:\Projects\workiz\backend\uploads | {"service":"workiz-api"}
2025-08-29 18:20:48 [[32m[32minfo[32m[39m] [general]: Express middleware and routes configured. | {"service":"workiz-api"}
2025-08-29 18:20:48 [[32m[32minfo[32m[39m] [general]: Async server setup finished successfully. | {"service":"workiz-api"}
2025-08-29 18:20:48 [[32m[32minfo[32m[39m] [general]: Server running in development mode on port 5000 | {"service":"workiz-api"}
2025-08-29 18:21:55 [[32m[32minfo[32m[39m] [general]: Login attempt | {"service":"workiz-api","email":"<EMAIL>"}
2025-08-29 18:21:55 [[32m[32minfo[32m[39m] [general]: User logged in: <EMAIL> (Administrators) | {"service":"workiz-api"}
2025-08-29 18:21:55 [[34m[34mdebug[34m[39m] [general]: Auth Token Received: Yes | {"service":"workiz-api"}
2025-08-29 18:21:55 [[34m[34mdebug[34m[39m] [general]: Token Decoded Successfully: User ID 68ae4a5c8cd30c966fd90ee1 | {"service":"workiz-api"}
2025-08-29 18:21:55 [[34m[34mdebug[34m[39m] [general]: Looking up user with ID: 68ae4a5c8cd30c966fd90ee1 | {"service":"workiz-api"}
2025-08-29 18:21:55 [[34m[34mdebug[34m[39m] [general]: User Found: <EMAIL> | {"service":"workiz-api"}
2025-08-29 18:21:55 [[32m[32minfo[32m[39m] [general]: User authenticated: <EMAIL> (Administrators) | {"service":"workiz-api"}
2025-08-29 18:22:02 [[34m[34mdebug[34m[39m] [general]: Auth Token Received: Yes | {"service":"workiz-api"}
2025-08-29 18:22:02 [[34m[34mdebug[34m[39m] [general]: Token Decoded Successfully: User ID 68ae4a5c8cd30c966fd90ee1 | {"service":"workiz-api"}
2025-08-29 18:22:02 [[34m[34mdebug[34m[39m] [general]: Looking up user with ID: 68ae4a5c8cd30c966fd90ee1 | {"service":"workiz-api"}
2025-08-29 18:22:02 [[34m[34mdebug[34m[39m] [general]: User Found: <EMAIL> | {"service":"workiz-api"}
2025-08-29 18:22:02 [[32m[32minfo[32m[39m] [general]: User authenticated: <EMAIL> (Administrators) | {"service":"workiz-api"}
2025-08-29 18:22:02 [[34m[34mdebug[34m[39m] [general]: Auth Token Received: Yes | {"service":"workiz-api"}
2025-08-29 18:22:02 [[34m[34mdebug[34m[39m] [general]: Token Decoded Successfully: User ID 68ae4a5c8cd30c966fd90ee1 | {"service":"workiz-api"}
2025-08-29 18:22:02 [[34m[34mdebug[34m[39m] [general]: Looking up user with ID: 68ae4a5c8cd30c966fd90ee1 | {"service":"workiz-api"}
2025-08-29 18:22:02 [[34m[34mdebug[34m[39m] [general]: User Found: <EMAIL> | {"service":"workiz-api"}
2025-08-29 18:22:02 [[32m[32minfo[32m[39m] [general]: User authenticated: <EMAIL> (Administrators) | {"service":"workiz-api"}
2025-08-29 18:22:19 [[32m[32minfo[32m[39m] [ai-generation]: === COMPREHENSIVE AI GENERATION LOGGING STARTED === | {"service":"workiz-api"}
2025-08-29 18:22:19 [[32m[32minfo[32m[39m] [ai-generation]: === AI Quote Generation Started === | {"service":"workiz-api","inputType":"overview","hasFiles":false,"fileCount":0}
2025-08-29 18:22:19 [[34m[34mdebug[34m[39m] [ai-generation]: [AI Controller] generateQuoteContent called. req.files: | {"service":"workiz-api"}
2025-08-29 18:22:19 [[34m[34mdebug[34m[39m] [ai-generation]: [AI Controller] req.body: | {"service":"workiz-api","inputType":"overview","inputData":"panel Upgrade"}
2025-08-29 18:22:19 [[34m[34mdebug[34m[39m] [ai-generation]: [AI Controller] Input type: overview, Parsed inputData (first 200 chars): panel Upgrade | {"service":"workiz-api"}
2025-08-29 18:22:19 [[32m[32minfo[32m[39m] [ai-generation]: [AI Controller] Streaming mode detected: false | {"service":"workiz-api","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWeb"}
2025-08-29 18:22:19 [[34m[34mdebug[34m[39m] [ai-generation]: [AI Controller] Final userInput for Gemini (first 300 chars): panel Upgrade | {"service":"workiz-api"}
2025-08-29 18:22:19 [[34m[34mdebug[34m[39m] [ai-generation]: [AI Controller] Request summary - URL: /api/ai/generate-quote-content, Files: 0, Content-Type: multipart/form-data; boundary=----WebKitFormBoundarymU09WIOVMTU0u0mJ | {"service":"workiz-api"}
2025-08-29 18:22:19 [[32m[32minfo[32m[39m] [general]: [AI Controller] No images provided or found in request for AI processing. | {"service":"workiz-api"}
2025-08-29 18:22:19 [[34m[34mdebug[34m[39m] [ai-generation]: [AI Controller] req.files value: | {"service":"workiz-api"}
2025-08-29 18:22:19 [[32m[32minfo[32m[39m] [ai-generation]: Calling Gemini AI Service for quote generation | {"service":"workiz-api","inputType":"overview","systemPromptLength":9382,"userInputLength":13,"imageCount":0,"hasImageDetails":false}
2025-08-29 18:22:19 [[32m[32minfo[32m[39m] [general]: [AI Controller] Attempting to call geminiService.getGeminiJsonResponse for inputType: overview. Image count: 0. | {"service":"workiz-api"}
2025-08-29 18:22:19 [[32m[32minfo[32m[39m] [general]: Attempting model: PRIMARY (models/gemini-2.5-pro-preview-03-25) | {"service":"workiz-api"}
2025-08-29 18:22:19 [[34m[34mdebug[34m[39m] [general]: [GeminiService] Using token limits for models/gemini-2.5-pro-preview-03-25: inputLimit=1048576, outputLimit=65536 | {"service":"workiz-api"}
2025-08-29 18:22:19 [[32m[32minfo[32m[39m] [general]: Using JSON mode for model: models/gemini-2.5-pro-preview-03-25 | {"service":"workiz-api"}
2025-08-29 18:22:19 [[32m[32minfo[32m[39m] [general]: [Gemini Service] Executing model models/gemini-2.5-pro-preview-03-25. JSON Mode: true. Image Parts: 0. Tools: false | {"service":"workiz-api"}
2025-08-29 18:22:19 [[34m[34mdebug[34m[39m] [general]: [Gemini Service] Model Params for models/gemini-2.5-pro-preview-03-25: | {"service":"workiz-api","modelParams":{"model":"models/gemini-2.5-pro-preview-03-25","generationConfig":{"temperature":0.4,"maxOutputTokens":65536,"topP":0.95,"topK":40,"responseMimeType":"application/json"}}}
2025-08-29 18:22:19 [[34m[34mdebug[34m[39m] [general]: [Gemini Service] Setting timeout to 60655.36ms for model models/gemini-2.5-pro-preview-03-25 | {"service":"workiz-api"}
2025-08-29 18:22:41 [[34m[34mdebug[34m[39m] [general]: [Gemini Service Stream - models/gemini-2.5-pro-preview-03-25] Stream processing started. | {"service":"workiz-api"}
2025-08-29 18:22:41 [[34m[34mdebug[34m[39m] [general]: [Gemini Service Stream - models/gemini-2.5-pro-preview-03-25] Received chunk. Text length: 41 | {"service":"workiz-api"}
2025-08-29 18:22:42 [[34m[34mdebug[34m[39m] [general]: [Gemini Service Stream - models/gemini-2.5-pro-preview-03-25] Received chunk. Text length: 132 | {"service":"workiz-api"}
2025-08-29 18:22:42 [[34m[34mdebug[34m[39m] [general]: [Gemini Service Stream - models/gemini-2.5-pro-preview-03-25] Received chunk. Text length: 74 | {"service":"workiz-api"}
2025-08-29 18:22:42 [[34m[34mdebug[34m[39m] [general]: [Gemini Service Stream - models/gemini-2.5-pro-preview-03-25] Received chunk. Text length: 116 | {"service":"workiz-api"}
2025-08-29 18:22:42 [[34m[34mdebug[34m[39m] [general]: [Gemini Service Stream - models/gemini-2.5-pro-preview-03-25] Received chunk. Text length: 125 | {"service":"workiz-api"}
2025-08-29 18:22:42 [[34m[34mdebug[34m[39m] [general]: [Gemini Service Stream - models/gemini-2.5-pro-preview-03-25] Received chunk. Text length: 73 | {"service":"workiz-api"}
2025-08-29 18:22:42 [[34m[34mdebug[34m[39m] [general]: [Gemini Service Stream - models/gemini-2.5-pro-preview-03-25] Received chunk. Text length: 112 | {"service":"workiz-api"}
2025-08-29 18:22:43 [[34m[34mdebug[34m[39m] [general]: [Gemini Service Stream - models/gemini-2.5-pro-preview-03-25] Received chunk. Text length: 141 | {"service":"workiz-api"}
2025-08-29 18:22:43 [[34m[34mdebug[34m[39m] [general]: [Gemini Service Stream - models/gemini-2.5-pro-preview-03-25] Received chunk. Text length: 161 | {"service":"workiz-api"}
2025-08-29 18:22:43 [[34m[34mdebug[34m[39m] [general]: [Gemini Service Stream - models/gemini-2.5-pro-preview-03-25] Received chunk. Text length: 86 | {"service":"workiz-api"}
2025-08-29 18:22:43 [[34m[34mdebug[34m[39m] [general]: [Gemini Service Stream - models/gemini-2.5-pro-preview-03-25] Received chunk. Text length: 85 | {"service":"workiz-api"}
2025-08-29 18:22:43 [[34m[34mdebug[34m[39m] [general]: [Gemini Service Stream - models/gemini-2.5-pro-preview-03-25] Received chunk. Text length: 109 | {"service":"workiz-api"}
2025-08-29 18:22:44 [[34m[34mdebug[34m[39m] [general]: [Gemini Service Stream - models/gemini-2.5-pro-preview-03-25] Received chunk. Text length: 72 | {"service":"workiz-api"}
2025-08-29 18:22:44 [[34m[34mdebug[34m[39m] [general]: [Gemini Service Stream - models/gemini-2.5-pro-preview-03-25] Received chunk. Text length: 207 | {"service":"workiz-api"}
2025-08-29 18:22:44 [[34m[34mdebug[34m[39m] [general]: [Gemini Service Stream - models/gemini-2.5-pro-preview-03-25] Received chunk. Text length: 78 | {"service":"workiz-api"}
2025-08-29 18:22:44 [[34m[34mdebug[34m[39m] [general]: [Gemini Service Stream - models/gemini-2.5-pro-preview-03-25] Received chunk. Text length: 80 | {"service":"workiz-api"}
2025-08-29 18:22:44 [[34m[34mdebug[34m[39m] [general]: [Gemini Service Stream - models/gemini-2.5-pro-preview-03-25] Received chunk. Text length: 150 | {"service":"workiz-api"}
2025-08-29 18:22:45 [[34m[34mdebug[34m[39m] [general]: [Gemini Service Stream - models/gemini-2.5-pro-preview-03-25] Received chunk. Text length: 75 | {"service":"workiz-api"}
2025-08-29 18:22:45 [[34m[34mdebug[34m[39m] [general]: [Gemini Service Stream - models/gemini-2.5-pro-preview-03-25] Received chunk. Text length: 84 | {"service":"workiz-api"}
2025-08-29 18:22:45 [[34m[34mdebug[34m[39m] [general]: [Gemini Service Stream - models/gemini-2.5-pro-preview-03-25] Received chunk. Text length: 234 | {"service":"workiz-api"}
2025-08-29 18:22:46 [[34m[34mdebug[34m[39m] [general]: [Gemini Service Stream - models/gemini-2.5-pro-preview-03-25] Received chunk. Text length: 161 | {"service":"workiz-api"}
2025-08-29 18:22:46 [[34m[34mdebug[34m[39m] [general]: [Gemini Service Stream - models/gemini-2.5-pro-preview-03-25] Received chunk. Text length: 68 | {"service":"workiz-api"}
2025-08-29 18:22:46 [[34m[34mdebug[34m[39m] [general]: [Gemini Service Stream - models/gemini-2.5-pro-preview-03-25] Received chunk. Text length: 79 | {"service":"workiz-api"}
2025-08-29 18:22:46 [[34m[34mdebug[34m[39m] [general]: [Gemini Service Stream - models/gemini-2.5-pro-preview-03-25] Received chunk. Text length: 66 | {"service":"workiz-api"}
2025-08-29 18:22:46 [[34m[34mdebug[34m[39m] [general]: [Gemini Service Stream - models/gemini-2.5-pro-preview-03-25] Received chunk. Text length: 49 | {"service":"workiz-api"}
2025-08-29 18:22:47 [[34m[34mdebug[34m[39m] [general]: [Gemini Service Stream - models/gemini-2.5-pro-preview-03-25] Received chunk. Text length: 101 | {"service":"workiz-api"}
2025-08-29 18:22:47 [[34m[34mdebug[34m[39m] [general]: [Gemini Service Stream - models/gemini-2.5-pro-preview-03-25] Received chunk. Text length: 77 | {"service":"workiz-api"}
2025-08-29 18:22:47 [[34m[34mdebug[34m[39m] [general]: [Gemini Service Stream - models/gemini-2.5-pro-preview-03-25] Received chunk. Text length: 111 | {"service":"workiz-api"}
2025-08-29 18:22:47 [[34m[34mdebug[34m[39m] [general]: [Gemini Service Stream - models/gemini-2.5-pro-preview-03-25] Received chunk. Text length: 80 | {"service":"workiz-api"}
2025-08-29 18:22:47 [[34m[34mdebug[34m[39m] [general]: [Gemini Service Stream - models/gemini-2.5-pro-preview-03-25] Received chunk. Text length: 94 | {"service":"workiz-api"}
2025-08-29 18:22:47 [[34m[34mdebug[34m[39m] [general]: [Gemini Service Stream - models/gemini-2.5-pro-preview-03-25] Received chunk. Text length: 66 | {"service":"workiz-api"}
2025-08-29 18:22:48 [[34m[34mdebug[34m[39m] [general]: [Gemini Service Stream - models/gemini-2.5-pro-preview-03-25] Received chunk. Text length: 67 | {"service":"workiz-api"}
2025-08-29 18:22:48 [[34m[34mdebug[34m[39m] [general]: [Gemini Service Stream - models/gemini-2.5-pro-preview-03-25] Received chunk. Text length: 73 | {"service":"workiz-api"}
2025-08-29 18:22:48 [[34m[34mdebug[34m[39m] [general]: [Gemini Service Stream - models/gemini-2.5-pro-preview-03-25] Received chunk. Text length: 89 | {"service":"workiz-api"}
2025-08-29 18:22:48 [[34m[34mdebug[34m[39m] [general]: [Gemini Service Stream - models/gemini-2.5-pro-preview-03-25] Received chunk. Text length: 78 | {"service":"workiz-api"}
2025-08-29 18:22:48 [[34m[34mdebug[34m[39m] [general]: [Gemini Service Stream - models/gemini-2.5-pro-preview-03-25] Received chunk. Text length: 75 | {"service":"workiz-api"}
2025-08-29 18:22:48 [[34m[34mdebug[34m[39m] [general]: [Gemini Service Stream - models/gemini-2.5-pro-preview-03-25] Received chunk. Text length: 68 | {"service":"workiz-api"}
2025-08-29 18:22:48 [[34m[34mdebug[34m[39m] [general]: [Gemini Service Stream - models/gemini-2.5-pro-preview-03-25] Received chunk. Text length: 72 | {"service":"workiz-api"}
2025-08-29 18:22:49 [[34m[34mdebug[34m[39m] [general]: [Gemini Service Stream - models/gemini-2.5-pro-preview-03-25] Received chunk. Text length: 104 | {"service":"workiz-api"}
2025-08-29 18:22:49 [[34m[34mdebug[34m[39m] [general]: [Gemini Service Stream - models/gemini-2.5-pro-preview-03-25] Received chunk. Text length: 70 | {"service":"workiz-api"}
2025-08-29 18:22:49 [[34m[34mdebug[34m[39m] [general]: [Gemini Service Stream - models/gemini-2.5-pro-preview-03-25] Received chunk. Text length: 83 | {"service":"workiz-api"}
2025-08-29 18:22:49 [[34m[34mdebug[34m[39m] [general]: [Gemini Service Stream - models/gemini-2.5-pro-preview-03-25] Received chunk. Text length: 74 | {"service":"workiz-api"}
2025-08-29 18:22:49 [[34m[34mdebug[34m[39m] [general]: [Gemini Service Stream - models/gemini-2.5-pro-preview-03-25] Received chunk. Text length: 98 | {"service":"workiz-api"}
2025-08-29 18:22:50 [[34m[34mdebug[34m[39m] [general]: [Gemini Service Stream - models/gemini-2.5-pro-preview-03-25] Received chunk. Text length: 144 | {"service":"workiz-api"}
2025-08-29 18:22:50 [[34m[34mdebug[34m[39m] [general]: [Gemini Service Stream - models/gemini-2.5-pro-preview-03-25] Received chunk. Text length: 82 | {"service":"workiz-api"}
2025-08-29 18:22:50 [[34m[34mdebug[34m[39m] [general]: [Gemini Service Stream - models/gemini-2.5-pro-preview-03-25] Received chunk. Text length: 106 | {"service":"workiz-api"}
2025-08-29 18:22:50 [[34m[34mdebug[34m[39m] [general]: [Gemini Service Stream - models/gemini-2.5-pro-preview-03-25] Received chunk. Text length: 70 | {"service":"workiz-api"}
2025-08-29 18:22:50 [[34m[34mdebug[34m[39m] [general]: [Gemini Service Stream - models/gemini-2.5-pro-preview-03-25] Received chunk. Text length: 72 | {"service":"workiz-api"}
2025-08-29 18:22:50 [[34m[34mdebug[34m[39m] [general]: [Gemini Service Stream - models/gemini-2.5-pro-preview-03-25] Received chunk. Text length: 75 | {"service":"workiz-api"}
2025-08-29 18:22:50 [[34m[34mdebug[34m[39m] [general]: [Gemini Service Stream - models/gemini-2.5-pro-preview-03-25] Received chunk. Text length: 87 | {"service":"workiz-api"}
2025-08-29 18:22:51 [[34m[34mdebug[34m[39m] [general]: [Gemini Service Stream - models/gemini-2.5-pro-preview-03-25] Received chunk. Text length: 81 | {"service":"workiz-api"}
2025-08-29 18:22:51 [[34m[34mdebug[34m[39m] [general]: [Gemini Service Stream - models/gemini-2.5-pro-preview-03-25] Received chunk. Text length: 64 | {"service":"workiz-api"}
2025-08-29 18:22:51 [[34m[34mdebug[34m[39m] [general]: [Gemini Service Stream - models/gemini-2.5-pro-preview-03-25] Received chunk. Text length: 67 | {"service":"workiz-api"}
2025-08-29 18:22:51 [[34m[34mdebug[34m[39m] [general]: [Gemini Service Stream - models/gemini-2.5-pro-preview-03-25] Received chunk. Text length: 74 | {"service":"workiz-api"}
2025-08-29 18:22:51 [[34m[34mdebug[34m[39m] [general]: [Gemini Service Stream - models/gemini-2.5-pro-preview-03-25] Received chunk. Text length: 170 | {"service":"workiz-api"}
2025-08-29 18:22:51 [[34m[34mdebug[34m[39m] [general]: [Gemini Service Stream - models/gemini-2.5-pro-preview-03-25] Received chunk. Text length: 74 | {"service":"workiz-api"}
2025-08-29 18:22:52 [[34m[34mdebug[34m[39m] [general]: [Gemini Service Stream - models/gemini-2.5-pro-preview-03-25] Received chunk. Text length: 69 | {"service":"workiz-api"}
2025-08-29 18:22:52 [[34m[34mdebug[34m[39m] [general]: [Gemini Service Stream - models/gemini-2.5-pro-preview-03-25] Received chunk. Text length: 107 | {"service":"workiz-api"}
2025-08-29 18:22:52 [[34m[34mdebug[34m[39m] [general]: [Gemini Service Stream - models/gemini-2.5-pro-preview-03-25] Received chunk. Text length: 69 | {"service":"workiz-api"}
2025-08-29 18:22:52 [[34m[34mdebug[34m[39m] [general]: [Gemini Service Stream - models/gemini-2.5-pro-preview-03-25] Received chunk. Text length: 83 | {"service":"workiz-api"}
2025-08-29 18:22:52 [[34m[34mdebug[34m[39m] [general]: [Gemini Service Stream - models/gemini-2.5-pro-preview-03-25] Received chunk. Text length: 85 | {"service":"workiz-api"}
2025-08-29 18:22:53 [[34m[34mdebug[34m[39m] [general]: [Gemini Service Stream - models/gemini-2.5-pro-preview-03-25] Received chunk. Text length: 74 | {"service":"workiz-api"}
2025-08-29 18:22:53 [[34m[34mdebug[34m[39m] [general]: [Gemini Service Stream - models/gemini-2.5-pro-preview-03-25] Received chunk. Text length: 85 | {"service":"workiz-api"}
2025-08-29 18:22:53 [[34m[34mdebug[34m[39m] [general]: [Gemini Service Stream - models/gemini-2.5-pro-preview-03-25] Received chunk. Text length: 95 | {"service":"workiz-api"}
2025-08-29 18:22:53 [[34m[34mdebug[34m[39m] [general]: [Gemini Service Stream - models/gemini-2.5-pro-preview-03-25] Received chunk. Text length: 79 | {"service":"workiz-api"}
2025-08-29 18:22:53 [[34m[34mdebug[34m[39m] [general]: [Gemini Service Stream - models/gemini-2.5-pro-preview-03-25] Received chunk. Text length: 95 | {"service":"workiz-api"}
2025-08-29 18:22:53 [[34m[34mdebug[34m[39m] [general]: [Gemini Service Stream - models/gemini-2.5-pro-preview-03-25] Received chunk. Text length: 98 | {"service":"workiz-api"}
2025-08-29 18:22:53 [[34m[34mdebug[34m[39m] [general]: [Gemini Service Stream - models/gemini-2.5-pro-preview-03-25] Received chunk. Text length: 99 | {"service":"workiz-api"}
2025-08-29 18:22:54 [[34m[34mdebug[34m[39m] [general]: [Gemini Service Stream - models/gemini-2.5-pro-preview-03-25] Received chunk. Text length: 77 | {"service":"workiz-api"}
2025-08-29 18:22:54 [[34m[34mdebug[34m[39m] [general]: [Gemini Service Stream - models/gemini-2.5-pro-preview-03-25] Received chunk. Text length: 84 | {"service":"workiz-api"}
2025-08-29 18:22:54 [[34m[34mdebug[34m[39m] [general]: [Gemini Service Stream - models/gemini-2.5-pro-preview-03-25] Received chunk. Text length: 123 | {"service":"workiz-api"}
2025-08-29 18:22:54 [[34m[34mdebug[34m[39m] [general]: [Gemini Service Stream - models/gemini-2.5-pro-preview-03-25] Received chunk. Text length: 69 | {"service":"workiz-api"}
2025-08-29 18:22:54 [[34m[34mdebug[34m[39m] [general]: [Gemini Service Stream - models/gemini-2.5-pro-preview-03-25] Received chunk. Text length: 94 | {"service":"workiz-api"}
2025-08-29 18:22:54 [[34m[34mdebug[34m[39m] [general]: [Gemini Service Stream - models/gemini-2.5-pro-preview-03-25] Received chunk. Text length: 113 | {"service":"workiz-api"}
2025-08-29 18:22:55 [[34m[34mdebug[34m[39m] [general]: [Gemini Service Stream - models/gemini-2.5-pro-preview-03-25] Received chunk. Text length: 79 | {"service":"workiz-api"}
2025-08-29 18:22:55 [[34m[34mdebug[34m[39m] [general]: [Gemini Service Stream - models/gemini-2.5-pro-preview-03-25] Received chunk. Text length: 95 | {"service":"workiz-api"}
2025-08-29 18:22:55 [[34m[34mdebug[34m[39m] [general]: [Gemini Service Stream - models/gemini-2.5-pro-preview-03-25] Received chunk. Text length: 93 | {"service":"workiz-api"}
2025-08-29 18:22:55 [[34m[34mdebug[34m[39m] [general]: [Gemini Service Stream - models/gemini-2.5-pro-preview-03-25] Received chunk. Text length: 79 | {"service":"workiz-api"}
2025-08-29 18:22:55 [[34m[34mdebug[34m[39m] [general]: [Gemini Service Stream - models/gemini-2.5-pro-preview-03-25] Received chunk. Text length: 76 | {"service":"workiz-api"}
2025-08-29 18:22:55 [[34m[34mdebug[34m[39m] [general]: [Gemini Service Stream - models/gemini-2.5-pro-preview-03-25] Received chunk. Text length: 114 | {"service":"workiz-api"}
2025-08-29 18:22:56 [[34m[34mdebug[34m[39m] [general]: [Gemini Service Stream - models/gemini-2.5-pro-preview-03-25] Received chunk. Text length: 166 | {"service":"workiz-api"}
2025-08-29 18:22:56 [[34m[34mdebug[34m[39m] [general]: [Gemini Service Stream - models/gemini-2.5-pro-preview-03-25] Received chunk. Text length: 92 | {"service":"workiz-api"}
2025-08-29 18:22:56 [[34m[34mdebug[34m[39m] [general]: [Gemini Service Stream - models/gemini-2.5-pro-preview-03-25] Received chunk. Text length: 121 | {"service":"workiz-api"}
2025-08-29 18:22:56 [[34m[34mdebug[34m[39m] [general]: [Gemini Service Stream - models/gemini-2.5-pro-preview-03-25] Received chunk. Text length: 122 | {"service":"workiz-api"}
2025-08-29 18:22:56 [[34m[34mdebug[34m[39m] [general]: [Gemini Service Stream - models/gemini-2.5-pro-preview-03-25] Received chunk. Text length: 120 | {"service":"workiz-api"}
2025-08-29 18:22:57 [[34m[34mdebug[34m[39m] [general]: [Gemini Service Stream - models/gemini-2.5-pro-preview-03-25] Received chunk. Text length: 115 | {"service":"workiz-api"}
2025-08-29 18:22:57 [[34m[34mdebug[34m[39m] [general]: [Gemini Service Stream - models/gemini-2.5-pro-preview-03-25] Received chunk. Text length: 88 | {"service":"workiz-api"}
2025-08-29 18:22:57 [[34m[34mdebug[34m[39m] [general]: [Gemini Service Stream - models/gemini-2.5-pro-preview-03-25] Received chunk. Text length: 86 | {"service":"workiz-api"}
2025-08-29 18:22:57 [[34m[34mdebug[34m[39m] [general]: [Gemini Service Stream - models/gemini-2.5-pro-preview-03-25] Received chunk. Text length: 121 | {"service":"workiz-api"}
2025-08-29 18:22:58 [[34m[34mdebug[34m[39m] [general]: [Gemini Service Stream - models/gemini-2.5-pro-preview-03-25] Received chunk. Text length: 78 | {"service":"workiz-api"}
2025-08-29 18:22:58 [[34m[34mdebug[34m[39m] [general]: [Gemini Service Stream - models/gemini-2.5-pro-preview-03-25] Received chunk. Text length: 121 | {"service":"workiz-api"}
2025-08-29 18:22:58 [[34m[34mdebug[34m[39m] [general]: [Gemini Service Stream - models/gemini-2.5-pro-preview-03-25] Received chunk. Text length: 126 | {"service":"workiz-api"}
2025-08-29 18:22:58 [[34m[34mdebug[34m[39m] [general]: [Gemini Service Stream - models/gemini-2.5-pro-preview-03-25] Received chunk. Text length: 112 | {"service":"workiz-api"}
2025-08-29 18:22:58 [[34m[34mdebug[34m[39m] [general]: [Gemini Service Stream - models/gemini-2.5-pro-preview-03-25] Received chunk. Text length: 128 | {"service":"workiz-api"}
2025-08-29 18:22:59 [[34m[34mdebug[34m[39m] [general]: [Gemini Service Stream - models/gemini-2.5-pro-preview-03-25] Received chunk. Text length: 92 | {"service":"workiz-api"}
2025-08-29 18:22:59 [[34m[34mdebug[34m[39m] [general]: [Gemini Service Stream - models/gemini-2.5-pro-preview-03-25] Received chunk. Text length: 123 | {"service":"workiz-api"}
2025-08-29 18:22:59 [[34m[34mdebug[34m[39m] [general]: [Gemini Service Stream - models/gemini-2.5-pro-preview-03-25] Received chunk. Text length: 83 | {"service":"workiz-api"}
2025-08-29 18:22:59 [[34m[34mdebug[34m[39m] [general]: [Gemini Service Stream - models/gemini-2.5-pro-preview-03-25] Received chunk. Text length: 152 | {"service":"workiz-api"}
2025-08-29 18:23:00 [[34m[34mdebug[34m[39m] [general]: [Gemini Service Stream - models/gemini-2.5-pro-preview-03-25] Received chunk. Text length: 122 | {"service":"workiz-api"}
2025-08-29 18:23:00 [[34m[34mdebug[34m[39m] [general]: [Gemini Service Stream - models/gemini-2.5-pro-preview-03-25] Received chunk. Text length: 141 | {"service":"workiz-api"}
2025-08-29 18:23:00 [[34m[34mdebug[34m[39m] [general]: [Gemini Service Stream - models/gemini-2.5-pro-preview-03-25] Received chunk. Text length: 127 | {"service":"workiz-api"}
2025-08-29 18:23:00 [[34m[34mdebug[34m[39m] [general]: [Gemini Service Stream - models/gemini-2.5-pro-preview-03-25] Received chunk. Text length: 97 | {"service":"workiz-api"}
2025-08-29 18:23:00 [[34m[34mdebug[34m[39m] [general]: [Gemini Service Stream - models/gemini-2.5-pro-preview-03-25] Received chunk. Text length: 127 | {"service":"workiz-api"}
2025-08-29 18:23:01 [[34m[34mdebug[34m[39m] [general]: [Gemini Service Stream - models/gemini-2.5-pro-preview-03-25] Received chunk. Text length: 134 | {"service":"workiz-api"}
2025-08-29 18:23:01 [[34m[34mdebug[34m[39m] [general]: [Gemini Service Stream - models/gemini-2.5-pro-preview-03-25] Received chunk. Text length: 135 | {"service":"workiz-api"}
2025-08-29 18:23:01 [[34m[34mdebug[34m[39m] [general]: [Gemini Service Stream - models/gemini-2.5-pro-preview-03-25] Received chunk. Text length: 114 | {"service":"workiz-api"}
2025-08-29 18:23:01 [[34m[34mdebug[34m[39m] [general]: [Gemini Service Stream - models/gemini-2.5-pro-preview-03-25] Received chunk. Text length: 123 | {"service":"workiz-api"}
2025-08-29 18:23:01 [[34m[34mdebug[34m[39m] [general]: [Gemini Service Stream - models/gemini-2.5-pro-preview-03-25] Received chunk. Text length: 163 | {"service":"workiz-api"}
2025-08-29 18:23:02 [[34m[34mdebug[34m[39m] [general]: [Gemini Service Stream - models/gemini-2.5-pro-preview-03-25] Received chunk. Text length: 152 | {"service":"workiz-api"}
2025-08-29 18:23:02 [[34m[34mdebug[34m[39m] [general]: [Gemini Service Stream - models/gemini-2.5-pro-preview-03-25] Received chunk. Text length: 110 | {"service":"workiz-api"}
2025-08-29 18:23:02 [[34m[34mdebug[34m[39m] [general]: [Gemini Service Stream - models/gemini-2.5-pro-preview-03-25] Received chunk. Text length: 131 | {"service":"workiz-api"}
2025-08-29 18:23:02 [[34m[34mdebug[34m[39m] [general]: [Gemini Service Stream - models/gemini-2.5-pro-preview-03-25] Received chunk. Text length: 147 | {"service":"workiz-api"}
2025-08-29 18:23:03 [[34m[34mdebug[34m[39m] [general]: [Gemini Service Stream - models/gemini-2.5-pro-preview-03-25] Received chunk. Text length: 138 | {"service":"workiz-api"}
2025-08-29 18:23:03 [[34m[34mdebug[34m[39m] [general]: [Gemini Service Stream - models/gemini-2.5-pro-preview-03-25] Received chunk. Text length: 140 | {"service":"workiz-api"}
2025-08-29 18:23:03 [[34m[34mdebug[34m[39m] [general]: [Gemini Service Stream - models/gemini-2.5-pro-preview-03-25] Received chunk. Text length: 138 | {"service":"workiz-api"}
2025-08-29 18:23:03 [[34m[34mdebug[34m[39m] [general]: [Gemini Service Stream - models/gemini-2.5-pro-preview-03-25] Received chunk. Text length: 9 | {"service":"workiz-api"}
2025-08-29 18:23:03 [[34m[34mdebug[34m[39m] [general]: [Gemini Service Stream - models/gemini-2.5-pro-preview-03-25] Stream processing finished. Total aggregated length: 11732 | {"service":"workiz-api"}
2025-08-29 18:23:03 [[34m[34mdebug[34m[39m] [general]: Raw AI response text received for JSON parsing (first 200 chars): {
  "items": [
    {
      "description": "Coordination with local utility provider for service disconnection and reconnection.",
      "category": "administrative",
      "quantity": 1,
      "unit": | {"service":"workiz-api"}
2025-08-29 18:23:03 [[34m[34mdebug[34m[39m] [general]: Cleaned response text for parsing (first 200 chars): {
  "items": [
    {
      "description": "Coordination with local utility provider for service disconnection and reconnection.",
      "category": "administrative",
      "quantity": 1,
      "unit": | {"service":"workiz-api"}
2025-08-29 18:23:03 [[34m[34mdebug[34m[39m] [general]: [models/gemini-2.5-pro-preview-03-25] Attempting to parse JSON response | {"service":"workiz-api"}
2025-08-29 18:23:03 [[32m[32minfo[32m[39m] [general]: [models/gemini-2.5-pro-preview-03-25] Successfully parsed JSON response. Keys: items, detailed_scope_of_work, overall_summary, ai_confidence_score | {"service":"workiz-api"}
2025-08-29 18:23:03 [[32m[32minfo[32m[39m] [general]: [models/gemini-2.5-pro-preview-03-25] Response contains 24 material items | {"service":"workiz-api"}
2025-08-29 18:23:03 [[34m[34mdebug[34m[39m] [general]: [models/gemini-2.5-pro-preview-03-25] First item sample: | {"service":"workiz-api"}
2025-08-29 18:23:03 [[34m[34mdebug[34m[39m] [general]: [models/gemini-2.5-pro-preview-03-25] Item property stats: withName=0, withPrice=0, withQuantity=24, withLookupQuery=18 | {"service":"workiz-api"}
2025-08-29 18:23:03 [[32m[32minfo[32m[39m] [general]: Success with model: PRIMARY | {"service":"workiz-api"}
2025-08-29 18:23:03 [[32m[32minfo[32m[39m] [ai-generation]: AI Response received and parsed successfully | {"service":"workiz-api","hasItems":true,"itemCount":24,"hasOverallSummary":true,"hasDetailedScope":true,"aiConfidenceScore":0.98}
2025-08-29 18:23:03 [[32m[32minfo[32m[39m] [general]: [AI Controller] Successfully received structured AI response. | {"service":"workiz-api"}
2025-08-29 18:23:03 [[34m[34mdebug[34m[39m] [ai-generation]: [AI Controller] Parsed AI Response: {
  "items": [
    {
      "description": "Coordination with local utility provider for service disconnection and reconnection.",
      "category": "administrative",
      "quantity": 1,
      "unit": "task",
      "attributes": {},
      "lookup_query_suggestion": "local utility electrical service reconnection fee"
    },
    {
      "description": "Electrical permit acquisition from the local authority having jurisdiction (AHJ).",
      "category": "administrative",
      "quantity": 1,
      "unit": "permit",
      "attributes": {},
      "lookup_query_suggestion": "residential electrical service upgrade permit fee"
    },
    {
      "description": "Demolition, removal, and disposal of existing Federal Pacific Electric main panel and associated service entrance components.",
      "category": "labor",
      "quantity": 3,
      "unit": "hours",
      "attributes": {},
      "lookup_query_suggestion": ""
    },
    {
      "description": "Installation of new 200A meter-main combination load center.",
      "category": "labor",
      "quantity": 6,
      "unit": "hours",
      "attributes": {},
      "lookup_query_suggestion": ""
    },
    {
      "description": "Installation of new service entrance mast, weatherhead, and conductors.",
      "category": "labor",
      "quantity": 3,
      "unit": "hours",
      "attributes": {},
      "lookup_query_suggestion": ""
    },
    {
      "description": "Installation of new grounding electrode system, including ground rods and bonding to water/gas systems as per NEC.",
      "category": "labor",
      "quantity": 2,
      "unit": "hours",
      "attributes": {},
      "lookup_query_suggestion": ""
    },
    {
      "description": "Termination of existing branch circuits into new circuit breakers. Includes circuit identification and labeling.",
      "category": "labor",
      "quantity": 5,
      "unit": "hours",
      "attributes": {},
      "lookup_query_suggestion": ""
    },
    {
      "description": "Final system testing, commissioning, and walkthrough.",
      "category": "labor",
      "quantity": 1.5,
      "unit": "hours",
      "attributes": {},
      "lookup_query_suggestion": ""
    },
    {
      "description": "Siemens MC4040B1200FEN 200A 40-Space 40-Circuit Outdoor Ringless Meter-Main Combination Load Center.",
      "category": "electrical_material",
      "quantity": 1,
      "unit": "pcs",
      "attributes": {
        "ampacity": "200A",
        "type": "Meter-Main Combo",
        "rating": "NEMA 3R Outdoor"
      },
      "lookup_query_suggestion": "price Siemens MC4040B1200FEN 200A meter main"
    },
    {
      "description": "4/0-4/0-4/0-2/0 Aluminum SER Service Entrance Cable.",
      "category": "electrical_material",
      "quantity": 20,
      "unit": "feet",
      "attributes": {
        "material": "Aluminum",
        "size": "4/0-4/0-4/0-2/0",
        "type": "SER"
      },
      "lookup_query_suggestion": "price 20 ft 4/0 aluminum SER cable"
    },
    {
      "description": "2.5 inch Schedule 80 PVC Conduit for service mast.",
      "category": "electrical_material",
      "quantity": 10,
      "unit": "feet",
      "attributes": {
        "conduit_type": "PVC Schedule 80",
        "conduit_size": "2.5 inch"
      },
      "lookup_query_suggestion": "price 10 ft 2.5 inch schedule 80 PVC conduit"
    },
    {
      "description": "2.5 inch PVC Service Entrance Weatherhead.",
      "category": "electrical_material",
      "quantity": 1,
      "unit": "pcs",
      "attributes": {
        "conduit_type": "PVC",
        "conduit_size": "2.5 inch"
      },
      "lookup_query_suggestion": "price 2.5 inch PVC weatherhead"
    },
    {
      "description": "20A Single-Pole AFCI/GFCI Combination Circuit Breaker.",
      "category": "electrical_material",
      "quantity": 6,
      "unit": "pcs",
      "attributes": {
        "ampacity": "20A",
        "type": "AFCI/GFCI Combination"
      },
      "lookup_query_suggestion": "price Siemens 20A dual function AFCI/GFCI breaker"
    },
    {
      "description": "20A Single-Pole Standard Circuit Breaker.",
      "category": "electrical_material",
      "quantity": 8,
      "unit": "pcs",
      "attributes": {
        "ampacity": "20A",
        "type": "Standard"
      },
      "lookup_query_suggestion": "price Siemens QP 20A single pole breaker"
    },
    {
      "description": "30A Double-Pole Standard Circuit Breaker.",
      "category": "electrical_material",
      "quantity": 2,
      "unit": "pcs",
      "attributes": {
        "ampacity": "30A",
        "type": "Standard 2-Pole"
      },
      "lookup_query_suggestion": "price Siemens Q230 30A double pole breaker"
    },
    {
      "description": "50A Double-Pole Standard Circuit Breaker.",
      "category": "electrical_material",
      "quantity": 1,
      "unit": "pcs",
      "attributes": {
        "ampacity": "50A",
        "type": "Standard 2-Pole"
      },
      "lookup_query_suggestion": "price Siemens Q250 50A double pole breaker"
    },
    {
      "description": "5/8 inch x 8 foot Copper-Clad Ground Rod.",
      "category": "electrical_material",
      "quantity": 2,
      "unit": "pcs",
      "attributes": {
        "material": "Copper-Clad Steel",
        "size": "5/8 in x 8 ft"
      },
      "lookup_query_suggestion": "price 5/8 inch 8 ft ground rod"
    },
    {
      "description": "#4 AWG Solid Bare Copper Grounding Electrode Conductor.",
      "category": "electrical_material",
      "quantity": 30,
      "unit": "feet",
      "attributes": {
        "material": "Copper",
        "size": "4 AWG",
        "type": "Solid Bare"
      },
      "lookup_query_suggestion": "price 30 ft #4 solid bare copper wire"
    },
    {
      "description": "Acorn Ground Rod Clamps for 5/8 inch rod and #4 AWG wire.",
      "category": "electrical_material",
      "quantity": 2,
      "unit": "pcs",
      "attributes": {},
      "lookup_query_suggestion": "price acorn ground rod clamp 5/8 inch"
    },
    {
      "description": "Intersystem Bonding Bridge for low voltage systems.",
      "category": "electrical_material",
      "quantity": 1,
      "unit": "pcs",
      "attributes": {},
      "lookup_query_suggestion": "price intersystem bonding bridge"
    },
    {
      "description": "Electrical Duct Seal Compound.",
      "category": "electrical_material",
      "quantity": 1,
      "unit": "pcs",
      "attributes": {},
      "lookup_query_suggestion": "price 1lb duct seal compound"
    },
    {
      "description": "Printed Circuit Breaker Directory Label Sheet.",
      "category": "electrical_material",
      "quantity": 1,
      "unit": "pcs",
      "attributes": {},
      "lookup_query_suggestion": "price panel schedule labels"
    },
    {
      "description": "Stucco patch mix and bonding agent for repairing wall opening.",
      "category": "non_electrical_material",
      "quantity": 1,
      "unit": "bag",
      "attributes": {},
      "lookup_query_suggestion": "price stucco patch mix"
    },
    {
      "description": "Exterior-grade, paintable silicone caulk for weather sealing.",
      "category": "non_electrical_material",
      "quantity": 1,
      "unit": "tube",
      "attributes": {},
      "lookup_query_suggestion": "price exterior paintable silicone caulk"
    }
  ],
  "detailed_scope_of_work": "## Scope of Work for Main Electrical Service Upgrade\n\nThis project outlines the complete replacement of the existing main electrical service panel and associated components to enhance safety, reliability, and compliance with the current National Electrical Code (NEC).\n\n### Phase 1: Preparation and Demolition\n\n- Coordinate a scheduled power outage with the local utility provider.\n- Obtain all required electrical permits from the city/county building department.\n- Safely de-energize the entire electrical system.\n- Carefully disconnect all existing branch circuits from the old panel.\n- Remove and dispose of the existing, deteriorated main service panel, meter socket, and service entrance wiring.\n\n### Phase 2: Installation of New Service Equipment\n\n- Prepare the exterior wall surface for the new panel, which may include minor stucco modification to ensure a secure and flush fit.\n- Install a new 200-Amp, 40-space, outdoor-rated (NEMA 3R) meter-main combination load center. This modern panel provides a new main breaker, meter socket, and ample space for all circuits in a single, weather-resistant enclosure.\n- Install a new 2.5-inch rigid PVC service mast and weatherhead.\n- Pull new 4/0 aluminum service entrance conductors from the weatherhead to the line-side lugs of the new panel.\n- Install a new code-compliant grounding electrode system. This includes driving two new 8-foot copper-clad ground rods and connecting them to the panel with a #4 AWG solid copper conductor.\n- Bond all necessary metallic systems, such as copper water piping and gas lines (if applicable), to the new grounding system.\n- Install an intersystem bonding bridge for safe connection of low-voltage systems (e.g., cable TV, telephone).\n\n### Phase 3: Circuit Reconnection and Finalization\n\n- Re-terminate all existing branch circuits into new, high-quality circuit breakers appropriately sized for each circuit.\n- Install modern Arc-Fault (AFCI) and/or Ground-Fault (GFCI) combination breakers on circuits for living areas, kitchen, laundry, and bathrooms as required by the current NEC to provide enhanced protection against electrical fires and shock.\n- Create and affix a new, clearly typed panel schedule directory inside the panel door for easy circuit identification.\n- Seal the service entrance conduit penetration with duct seal compound to prevent moisture and pest entry.\n- Weatherproof the exterior perimeter of the new panel with high-quality exterior sealant.\n- Coordinate with the utility provider for the re-installation of the electrical meter and re-energization of the service.\n\n### Phase 4: Testing and Commissioning\n\n- Once power is restored, conduct a full system test to ensure all circuits are functioning correctly and safely.\n- Verify proper voltage, polarity, and grounding at representative locations throughout the property.\n- Schedule and attend the final inspection with the local electrical inspector.\n- Provide a final walkthrough with the client to explain the new system and answer any questions.",
  "overall_summary": "The existing main electrical panel is a Federal Pacific Electric (FPE) model, which is widely recognized in the electrical industry as a significant fire hazard due to a high rate of breaker malfunction. Furthermore, the panel's exterior enclosure exhibits severe rust and corrosion, compromising its weather resistance and indicating potential internal damage from moisture intrusion. This condition presents a serious safety risk and does not meet modern electrical standards.\n\nOur recommendation is a complete main service upgrade. This involves the full replacement of the hazardous FPE panel with a new, 200-Amp Siemens meter-main combination load center. This modern, outdoor-rated unit will provide reliable overcurrent protection and is designed to withstand the elements. The project includes replacing the main service wires from the weatherhead to the panel and establishing a new, code-compliant grounding and bonding system. All existing circuits will be connected to new breakers, including the installation of AFCI/GFCI breakers as required by current code for enhanced safety. This comprehensive upgrade will resolve the immediate safety hazards, ensure compliance with the National Electrical Code, and provide a safe and reliable electrical service for many years.",
  "ai_confidence_score": 0.98
} | {"service":"workiz-api"}
2025-08-29 18:23:03 [[32m[32minfo[32m[39m] [general]: [AI Controller] Processing 24 items for price lookups... | {"service":"workiz-api"}
2025-08-29 18:23:03 [[32m[32minfo[32m[39m] [general]: [AI Controller] Raw AI items: [
  {
    "description": "Coordination with local utility provider for service disconnection and reconnection.",
    "category": "administrative",
    "quantity": 1,
    "unit": "task",
    "attributes": {},
    "lookup_query_suggestion": "local utility electrical service reconnection fee"
  },
  {
    "description": "Electrical permit acquisition from the local authority having jurisdiction (AHJ).",
    "category": "administrative",
    "quantity": 1,
    "unit": "permit",
    "attributes": {},
    "lookup_query_suggestion": "residential electrical service upgrade permit fee"
  },
  {
    "description": "Demolition, removal, and disposal of existing Federal Pacific Electric main panel and associated service entrance components.",
    "category": "labor",
    "quantity": 3,
    "unit": "hours",
    "attributes": {},
    "lookup_query_suggestion": ""
  },
  {
    "description": "Installation of new 200A meter-main combination load center.",
    "category": "labor",
    "quantity": 6,
    "unit": "hours",
    "attributes": {},
    "lookup_query_suggestion": ""
  },
  {
    "description": "Installation of new service entrance mast, weatherhead, and conductors.",
    "category": "labor",
    "quantity": 3,
    "unit": "hours",
    "attributes": {},
    "lookup_query_suggestion": ""
  },
  {
    "description": "Installation of new grounding electrode system, including ground rods and bonding to water/gas systems as per NEC.",
    "category": "labor",
    "quantity": 2,
    "unit": "hours",
    "attributes": {},
    "lookup_query_suggestion": ""
  },
  {
    "description": "Termination of existing branch circuits into new circuit breakers. Includes circuit identification and labeling.",
    "category": "labor",
    "quantity": 5,
    "unit": "hours",
    "attributes": {},
    "lookup_query_suggestion": ""
  },
  {
    "description": "Final system testing, commissioning, and walkthrough.",
    "category": "labor",
    "quantity": 1.5,
    "unit": "hours",
    "attributes": {},
    "lookup_query_suggestion": ""
  },
  {
    "description": "Siemens MC4040B1200FEN 200A 40-Space 40-Circuit Outdoor Ringless Meter-Main Combination Load Center.",
    "category": "electrical_material",
    "quantity": 1,
    "unit": "pcs",
    "attributes": {
      "ampacity": "200A",
      "type": "Meter-Main Combo",
      "rating": "NEMA 3R Outdoor"
    },
    "lookup_query_suggestion": "price Siemens MC4040B1200FEN 200A meter main"
  },
  {
    "description": "4/0-4/0-4/0-2/0 Aluminum SER Service Entrance Cable.",
    "category": "electrical_material",
    "quantity": 20,
    "unit": "feet",
    "attributes": {
      "material": "Aluminum",
      "size": "4/0-4/0-4/0-2/0",
      "type": "SER"
    },
    "lookup_query_suggestion": "price 20 ft 4/0 aluminum SER cable"
  },
  {
    "description": "2.5 inch Schedule 80 PVC Conduit for service mast.",
    "category": "electrical_material",
    "quantity": 10,
    "unit": "feet",
    "attributes": {
      "conduit_type": "PVC Schedule 80",
      "conduit_size": "2.5 inch"
    },
    "lookup_query_suggestion": "price 10 ft 2.5 inch schedule 80 PVC conduit"
  },
  {
    "description": "2.5 inch PVC Service Entrance Weatherhead.",
    "category": "electrical_material",
    "quantity": 1,
    "unit": "pcs",
    "attributes": {
      "conduit_type": "PVC",
      "conduit_size": "2.5 inch"
    },
    "lookup_query_suggestion": "price 2.5 inch PVC weatherhead"
  },
  {
    "description": "20A Single-Pole AFCI/GFCI Combination Circuit Breaker.",
    "category": "electrical_material",
    "quantity": 6,
    "unit": "pcs",
    "attributes": {
      "ampacity": "20A",
      "type": "AFCI/GFCI Combination"
    },
    "lookup_query_suggestion": "price Siemens 20A dual function AFCI/GFCI breaker"
  },
  {
    "description": "20A Single-Pole Standard Circuit Breaker.",
    "category": "electrical_material",
    "quantity": 8,
    "unit": "pcs",
    "attributes": {
      "ampacity": "20A",
      "type": "Standard"
    },
    "lookup_query_suggestion": "price Siemens QP 20A single pole breaker"
  },
  {
    "description": "30A Double-Pole Standard Circuit Breaker.",
    "category": "electrical_material",
    "quantity": 2,
    "unit": "pcs",
    "attributes": {
      "ampacity": "30A",
      "type": "Standard 2-Pole"
    },
    "lookup_query_suggestion": "price Siemens Q230 30A double pole breaker"
  },
  {
    "description": "50A Double-Pole Standard Circuit Breaker.",
    "category": "electrical_material",
    "quantity": 1,
    "unit": "pcs",
    "attributes": {
      "ampacity": "50A",
      "type": "Standard 2-Pole"
    },
    "lookup_query_suggestion": "price Siemens Q250 50A double pole breaker"
  },
  {
    "description": "5/8 inch x 8 foot Copper-Clad Ground Rod.",
    "category": "electrical_material",
    "quantity": 2,
    "unit": "pcs",
    "attributes": {
      "material": "Copper-Clad Steel",
      "size": "5/8 in x 8 ft"
    },
    "lookup_query_suggestion": "price 5/8 inch 8 ft ground rod"
  },
  {
    "description": "#4 AWG Solid Bare Copper Grounding Electrode Conductor.",
    "category": "electrical_material",
    "quantity": 30,
    "unit": "feet",
    "attributes": {
      "material": "Copper",
      "size": "4 AWG",
      "type": "Solid Bare"
    },
    "lookup_query_suggestion": "price 30 ft #4 solid bare copper wire"
  },
  {
    "description": "Acorn Ground Rod Clamps for 5/8 inch rod and #4 AWG wire.",
    "category": "electrical_material",
    "quantity": 2,
    "unit": "pcs",
    "attributes": {},
    "lookup_query_suggestion": "price acorn ground rod clamp 5/8 inch"
  },
  {
    "description": "Intersystem Bonding Bridge for low voltage systems.",
    "category": "electrical_material",
    "quantity": 1,
    "unit": "pcs",
    "attributes": {},
    "lookup_query_suggestion": "price intersystem bonding bridge"
  },
  {
    "description": "Electrical Duct Seal Compound.",
    "category": "electrical_material",
    "quantity": 1,
    "unit": "pcs",
    "attributes": {},
    "lookup_query_suggestion": "price 1lb duct seal compound"
  },
  {
    "description": "Printed Circuit Breaker Directory Label Sheet.",
    "category": "electrical_material",
    "quantity": 1,
    "unit": "pcs",
    "attributes": {},
    "lookup_query_suggestion": "price panel schedule labels"
  },
  {
    "description": "Stucco patch mix and bonding agent for repairing wall opening.",
    "category": "non_electrical_material",
    "quantity": 1,
    "unit": "bag",
    "attributes": {},
    "lookup_query_suggestion": "price stucco patch mix"
  },
  {
    "description": "Exterior-grade, paintable silicone caulk for weather sealing.",
    "category": "non_electrical_material",
    "quantity": 1,
    "unit": "tube",
    "attributes": {},
    "lookup_query_suggestion": "price exterior paintable silicone caulk"
  }
] | {"service":"workiz-api"}
2025-08-29 18:23:03 [[32m[32minfo[32m[39m] [general]: [AI Controller] Item "Coordination with local utility provider for service disconnection and reconnection.": AI Category: administrative, Suggestion: "local utility electrical service reconnection fee" | {"service":"workiz-api"}
2025-08-29 18:23:03 [[34m[34mdebug[34m[39m] [general]: [AI Controller] FULL ITEM DETAILS for "Coordination with local utility provider for service disconnection and reconnection.": {
  "description": "Coordination with local utility provider for service disconnection and reconnection.",
  "category": "administrative",
  "quantity": 1,
  "unit": "task",
  "attributes": {},
  "lookup_query_suggestion": "local utility electrical service reconnection fee",
  "lookup_results": []
} | {"service":"workiz-api"}
2025-08-29 18:23:03 [[32m[32minfo[32m[39m] [general]: [AI Controller] ℹ️  Categorized as "administrative": "Coordination with local utility provider for service disconnection and reconnection." | {"service":"workiz-api"}
2025-08-29 18:23:03 [[32m[32minfo[32m[39m] [general]: [AI Controller] Item "Coordination with local utility provider for service disconnection and reconnection." will SKIP price lookup. Category: administrative | {"service":"workiz-api"}
2025-08-29 18:23:03 [[32m[32minfo[32m[39m] [general]: [AI Controller] Item "Coordination with local utility provider for service disconnection and reconnection." categorized as ADMINISTRATIVE by AI - including in output without price lookup | {"service":"workiz-api"}
2025-08-29 18:23:03 [[32m[32minfo[32m[39m] [general]: [AI Controller] Item "Electrical permit acquisition from the local authority having jurisdiction (AHJ).": AI Category: administrative, Suggestion: "residential electrical service upgrade permit fee" | {"service":"workiz-api"}
2025-08-29 18:23:03 [[34m[34mdebug[34m[39m] [general]: [AI Controller] FULL ITEM DETAILS for "Electrical permit acquisition from the local authority having jurisdiction (AHJ).": {
  "description": "Electrical permit acquisition from the local authority having jurisdiction (AHJ).",
  "category": "administrative",
  "quantity": 1,
  "unit": "permit",
  "attributes": {},
  "lookup_query_suggestion": "residential electrical service upgrade permit fee",
  "lookup_results": []
} | {"service":"workiz-api"}
2025-08-29 18:23:03 [[32m[32minfo[32m[39m] [general]: [AI Controller] ℹ️  Categorized as "administrative": "Electrical permit acquisition from the local authority having jurisdiction (AHJ)." | {"service":"workiz-api"}
2025-08-29 18:23:03 [[32m[32minfo[32m[39m] [general]: [AI Controller] Item "Electrical permit acquisition from the local authority having jurisdiction (AHJ)." will SKIP price lookup. Category: administrative | {"service":"workiz-api"}
2025-08-29 18:23:03 [[32m[32minfo[32m[39m] [general]: [AI Controller] Item "Electrical permit acquisition from the local authority having jurisdiction (AHJ)." categorized as ADMINISTRATIVE by AI - including in output without price lookup | {"service":"workiz-api"}
2025-08-29 18:23:03 [[32m[32minfo[32m[39m] [general]: [AI Controller] Item "Demolition, removal, and disposal of existing Federal Pacific Electric main panel and associated service entrance components.": AI Category: labor, Suggestion: "" | {"service":"workiz-api"}
2025-08-29 18:23:03 [[34m[34mdebug[34m[39m] [general]: [AI Controller] FULL ITEM DETAILS for "Demolition, removal, and disposal of existing Federal Pacific Electric main panel and associated service entrance components.": {
  "description": "Demolition, removal, and disposal of existing Federal Pacific Electric main panel and associated service entrance components.",
  "category": "labor",
  "quantity": 3,
  "unit": "hours",
  "attributes": {},
  "lookup_query_suggestion": "",
  "lookup_results": []
} | {"service":"workiz-api"}
2025-08-29 18:23:03 [[32m[32minfo[32m[39m] [general]: [AI Controller] ⚠️  Categorized as LABOR: "Demolition, removal, and disposal of existing Federal Pacific Electric main panel and associated service entrance components." | {"service":"workiz-api"}
2025-08-29 18:23:03 [[32m[32minfo[32m[39m] [general]: [AI Controller] Item "Demolition, removal, and disposal of existing Federal Pacific Electric main panel and associated service entrance components." will SKIP price lookup. Category: labor | {"service":"workiz-api"}
2025-08-29 18:23:03 [[32m[32minfo[32m[39m] [general]: [AI Controller] Item "Demolition, removal, and disposal of existing Federal Pacific Electric main panel and associated service entrance components." categorized as LABOR by AI - skipping price lookup | {"service":"workiz-api"}
2025-08-29 18:23:03 [[32m[32minfo[32m[39m] [general]: [AI Controller] Item "Installation of new 200A meter-main combination load center.": AI Category: labor, Suggestion: "" | {"service":"workiz-api"}
2025-08-29 18:23:03 [[34m[34mdebug[34m[39m] [general]: [AI Controller] FULL ITEM DETAILS for "Installation of new 200A meter-main combination load center.": {
  "description": "Installation of new 200A meter-main combination load center.",
  "category": "labor",
  "quantity": 6,
  "unit": "hours",
  "attributes": {},
  "lookup_query_suggestion": "",
  "lookup_results": []
} | {"service":"workiz-api"}
2025-08-29 18:23:03 [[32m[32minfo[32m[39m] [general]: [AI Controller] ⚠️  Categorized as LABOR: "Installation of new 200A meter-main combination load center." | {"service":"workiz-api"}
2025-08-29 18:23:03 [[32m[32minfo[32m[39m] [general]: [AI Controller] Item "Installation of new 200A meter-main combination load center." will SKIP price lookup. Category: labor | {"service":"workiz-api"}
2025-08-29 18:23:03 [[32m[32minfo[32m[39m] [general]: [AI Controller] Item "Installation of new 200A meter-main combination load center." categorized as LABOR by AI - skipping price lookup | {"service":"workiz-api"}
2025-08-29 18:23:03 [[32m[32minfo[32m[39m] [general]: [AI Controller] Item "Installation of new service entrance mast, weatherhead, and conductors.": AI Category: labor, Suggestion: "" | {"service":"workiz-api"}
2025-08-29 18:23:03 [[34m[34mdebug[34m[39m] [general]: [AI Controller] FULL ITEM DETAILS for "Installation of new service entrance mast, weatherhead, and conductors.": {
  "description": "Installation of new service entrance mast, weatherhead, and conductors.",
  "category": "labor",
  "quantity": 3,
  "unit": "hours",
  "attributes": {},
  "lookup_query_suggestion": "",
  "lookup_results": []
} | {"service":"workiz-api"}
2025-08-29 18:23:03 [[32m[32minfo[32m[39m] [general]: [AI Controller] ⚠️  Categorized as LABOR: "Installation of new service entrance mast, weatherhead, and conductors." | {"service":"workiz-api"}
2025-08-29 18:23:03 [[32m[32minfo[32m[39m] [general]: [AI Controller] Item "Installation of new service entrance mast, weatherhead, and conductors." will SKIP price lookup. Category: labor | {"service":"workiz-api"}
2025-08-29 18:23:03 [[32m[32minfo[32m[39m] [general]: [AI Controller] Item "Installation of new service entrance mast, weatherhead, and conductors." categorized as LABOR by AI - skipping price lookup | {"service":"workiz-api"}
2025-08-29 18:23:03 [[32m[32minfo[32m[39m] [general]: [AI Controller] Item "Installation of new grounding electrode system, including ground rods and bonding to water/gas systems as per NEC.": AI Category: labor, Suggestion: "" | {"service":"workiz-api"}
2025-08-29 18:23:03 [[34m[34mdebug[34m[39m] [general]: [AI Controller] FULL ITEM DETAILS for "Installation of new grounding electrode system, including ground rods and bonding to water/gas systems as per NEC.": {
  "description": "Installation of new grounding electrode system, including ground rods and bonding to water/gas systems as per NEC.",
  "category": "labor",
  "quantity": 2,
  "unit": "hours",
  "attributes": {},
  "lookup_query_suggestion": "",
  "lookup_results": []
} | {"service":"workiz-api"}
2025-08-29 18:23:03 [[32m[32minfo[32m[39m] [general]: [AI Controller] ⚠️  Categorized as LABOR: "Installation of new grounding electrode system, including ground rods and bonding to water/gas systems as per NEC." | {"service":"workiz-api"}
2025-08-29 18:23:03 [[32m[32minfo[32m[39m] [general]: [AI Controller] Item "Installation of new grounding electrode system, including ground rods and bonding to water/gas systems as per NEC." will SKIP price lookup. Category: labor | {"service":"workiz-api"}
2025-08-29 18:23:03 [[32m[32minfo[32m[39m] [general]: [AI Controller] Item "Installation of new grounding electrode system, including ground rods and bonding to water/gas systems as per NEC." categorized as LABOR by AI - skipping price lookup | {"service":"workiz-api"}
2025-08-29 18:23:03 [[32m[32minfo[32m[39m] [general]: [AI Controller] Item "Termination of existing branch circuits into new circuit breakers. Includes circuit identification and labeling.": AI Category: labor, Suggestion: "" | {"service":"workiz-api"}
2025-08-29 18:23:03 [[34m[34mdebug[34m[39m] [general]: [AI Controller] FULL ITEM DETAILS for "Termination of existing branch circuits into new circuit breakers. Includes circuit identification and labeling.": {
  "description": "Termination of existing branch circuits into new circuit breakers. Includes circuit identification and labeling.",
  "category": "labor",
  "quantity": 5,
  "unit": "hours",
  "attributes": {},
  "lookup_query_suggestion": "",
  "lookup_results": []
} | {"service":"workiz-api"}
2025-08-29 18:23:03 [[32m[32minfo[32m[39m] [general]: [AI Controller] ⚠️  Categorized as LABOR: "Termination of existing branch circuits into new circuit breakers. Includes circuit identification and labeling." | {"service":"workiz-api"}
2025-08-29 18:23:03 [[32m[32minfo[32m[39m] [general]: [AI Controller] Item "Termination of existing branch circuits into new circuit breakers. Includes circuit identification and labeling." will SKIP price lookup. Category: labor | {"service":"workiz-api"}
2025-08-29 18:23:03 [[32m[32minfo[32m[39m] [general]: [AI Controller] Item "Termination of existing branch circuits into new circuit breakers. Includes circuit identification and labeling." categorized as LABOR by AI - skipping price lookup | {"service":"workiz-api"}
2025-08-29 18:23:03 [[32m[32minfo[32m[39m] [general]: [AI Controller] Item "Final system testing, commissioning, and walkthrough.": AI Category: labor, Suggestion: "" | {"service":"workiz-api"}
2025-08-29 18:23:03 [[34m[34mdebug[34m[39m] [general]: [AI Controller] FULL ITEM DETAILS for "Final system testing, commissioning, and walkthrough.": {
  "description": "Final system testing, commissioning, and walkthrough.",
  "category": "labor",
  "quantity": 1.5,
  "unit": "hours",
  "attributes": {},
  "lookup_query_suggestion": "",
  "lookup_results": []
} | {"service":"workiz-api"}
2025-08-29 18:23:03 [[32m[32minfo[32m[39m] [general]: [AI Controller] ⚠️  Categorized as LABOR: "Final system testing, commissioning, and walkthrough." | {"service":"workiz-api"}
2025-08-29 18:23:03 [[32m[32minfo[32m[39m] [general]: [AI Controller] Item "Final system testing, commissioning, and walkthrough." will SKIP price lookup. Category: labor | {"service":"workiz-api"}
2025-08-29 18:23:03 [[32m[32minfo[32m[39m] [general]: [AI Controller] Item "Final system testing, commissioning, and walkthrough." categorized as LABOR by AI - skipping price lookup | {"service":"workiz-api"}
2025-08-29 18:23:03 [[32m[32minfo[32m[39m] [general]: [AI Controller] Item "Siemens MC4040B1200FEN 200A 40-Space 40-Circuit Outdoor Ringless Meter-Main Combination Load Center.": AI Category: electrical_material, Suggestion: "price Siemens MC4040B1200FEN 200A meter main" | {"service":"workiz-api"}
2025-08-29 18:23:03 [[34m[34mdebug[34m[39m] [general]: [AI Controller] FULL ITEM DETAILS for "Siemens MC4040B1200FEN 200A 40-Space 40-Circuit Outdoor Ringless Meter-Main Combination Load Center.": {
  "description": "Siemens MC4040B1200FEN 200A 40-Space 40-Circuit Outdoor Ringless Meter-Main Combination Load Center.",
  "category": "electrical_material",
  "quantity": 1,
  "unit": "pcs",
  "attributes": {
    "ampacity": "200A",
    "type": "Meter-Main Combo",
    "rating": "NEMA 3R Outdoor"
  },
  "lookup_query_suggestion": "price Siemens MC4040B1200FEN 200A meter main",
  "lookup_results": []
} | {"service":"workiz-api"}
2025-08-29 18:23:03 [[32m[32minfo[32m[39m] [general]: [AI Controller] ✅ CORRECTLY categorized as ELECTRICAL MATERIAL: "Siemens MC4040B1200FEN 200A 40-Space 40-Circuit Outdoor Ringless Meter-Main Combination Load Center." | {"service":"workiz-api"}
2025-08-29 18:23:03 [[34m[34mdebug[34m[39m] [general]: [parserUtils] determineLookupTypeForItem: Processing item - Description: "siemens mc4040b1200fen 200a 40-space 40-circuit outdoor ringless meter-main combination load center.", Attributes: {"ampacity":"200A","type":"Meter-Main Combo","rating":"NEMA 3R Outdoor"}, Suggestion: "price Siemens MC4040B1200FEN 200A meter main" | {"service":"workiz-api"}
2025-08-29 18:23:03 [[34m[34mdebug[34m[39m] [general]: [parserUtils] determineLookupTypeForItem: Classified as PRICE_MATERIAL_COMPONENT. | {"service":"workiz-api"}
2025-08-29 18:23:03 [[32m[32minfo[32m[39m] [general]: [AI Controller] Item "Siemens MC4040B1200FEN 200A 40-Space 40-Circuit Outdoor Ringless Meter-Main Combination Load Center." (type: PRICE_MATERIAL_COMPONENT) flagged for INTERNAL backend price lookup. | {"service":"workiz-api"}
2025-08-29 18:23:03 [[34m[34mdebug[34m[39m] [general]: [AI Controller] Item "Siemens MC4040B1200FEN 200A 40-Space 40-Circuit Outdoor Ringless Meter-Main Combination Load Center." (type: PRICE_MATERIAL_COMPONENT) - internal lookup query details: "price Siemens MC4040B1200FEN 200A meter main" | {"service":"workiz-api"}
2025-08-29 18:23:03 [[32m[32minfo[32m[39m] [general]: [AI Controller] 🚀 SENDING TO CRAWL4AI: "Siemens MC4040B1200FEN 200A 40-Space 40-Circuit Outdoor Ringless Meter-Main Combination Load Center." with query: "price Siemens MC4040B1200FEN 200A meter main" | {"service":"workiz-api"}
2025-08-29 18:23:03 [[32m[32minfo[32m[39m] [general]: [AI Controller] Item "4/0-4/0-4/0-2/0 Aluminum SER Service Entrance Cable.": AI Category: electrical_material, Suggestion: "price 20 ft 4/0 aluminum SER cable" | {"service":"workiz-api"}
2025-08-29 18:23:03 [[34m[34mdebug[34m[39m] [general]: [AI Controller] FULL ITEM DETAILS for "4/0-4/0-4/0-2/0 Aluminum SER Service Entrance Cable.": {
  "description": "4/0-4/0-4/0-2/0 Aluminum SER Service Entrance Cable.",
  "category": "electrical_material",
  "quantity": 20,
  "unit": "feet",
  "attributes": {
    "material": "Aluminum",
    "size": "4/0-4/0-4/0-2/0",
    "type": "SER"
  },
  "lookup_query_suggestion": "price 20 ft 4/0 aluminum SER cable",
  "lookup_results": []
} | {"service":"workiz-api"}
2025-08-29 18:23:03 [[32m[32minfo[32m[39m] [general]: [AI Controller] ✅ CORRECTLY categorized as ELECTRICAL MATERIAL: "4/0-4/0-4/0-2/0 Aluminum SER Service Entrance Cable." | {"service":"workiz-api"}
2025-08-29 18:23:03 [[34m[34mdebug[34m[39m] [general]: [parserUtils] determineLookupTypeForItem: Processing item - Description: "4/0-4/0-4/0-2/0 aluminum ser service entrance cable.", Attributes: {"material":"Aluminum","size":"4/0-4/0-4/0-2/0","type":"SER"}, Suggestion: "price 20 ft 4/0 aluminum SER cable" | {"service":"workiz-api"}
2025-08-29 18:23:03 [[34m[34mdebug[34m[39m] [general]: [parserUtils] determineLookupTypeForItem: Classified as PRICE_MATERIAL_COMPONENT. | {"service":"workiz-api"}
2025-08-29 18:23:03 [[32m[32minfo[32m[39m] [general]: [AI Controller] Item "4/0-4/0-4/0-2/0 Aluminum SER Service Entrance Cable." (type: PRICE_MATERIAL_COMPONENT) flagged for INTERNAL backend price lookup. | {"service":"workiz-api"}
2025-08-29 18:23:03 [[34m[34mdebug[34m[39m] [general]: [AI Controller] Item "4/0-4/0-4/0-2/0 Aluminum SER Service Entrance Cable." (type: PRICE_MATERIAL_COMPONENT) - internal lookup query details: "price 20 ft 4/0 aluminum SER cable" | {"service":"workiz-api"}
2025-08-29 18:23:03 [[32m[32minfo[32m[39m] [general]: [AI Controller] 🚀 SENDING TO CRAWL4AI: "4/0-4/0-4/0-2/0 Aluminum SER Service Entrance Cable." with query: "price 20 ft 4/0 aluminum SER cable" | {"service":"workiz-api"}
2025-08-29 18:23:03 [[32m[32minfo[32m[39m] [general]: [AI Controller] Item "2.5 inch Schedule 80 PVC Conduit for service mast.": AI Category: electrical_material, Suggestion: "price 10 ft 2.5 inch schedule 80 PVC conduit" | {"service":"workiz-api"}
2025-08-29 18:23:03 [[34m[34mdebug[34m[39m] [general]: [AI Controller] FULL ITEM DETAILS for "2.5 inch Schedule 80 PVC Conduit for service mast.": {
  "description": "2.5 inch Schedule 80 PVC Conduit for service mast.",
  "category": "electrical_material",
  "quantity": 10,
  "unit": "feet",
  "attributes": {
    "conduit_type": "PVC Schedule 80",
    "conduit_size": "2.5 inch"
  },
  "lookup_query_suggestion": "price 10 ft 2.5 inch schedule 80 PVC conduit",
  "lookup_results": []
} | {"service":"workiz-api"}
2025-08-29 18:23:03 [[32m[32minfo[32m[39m] [general]: [AI Controller] ✅ CORRECTLY categorized as ELECTRICAL MATERIAL: "2.5 inch Schedule 80 PVC Conduit for service mast." | {"service":"workiz-api"}
2025-08-29 18:23:03 [[34m[34mdebug[34m[39m] [general]: [parserUtils] determineLookupTypeForItem: Processing item - Description: "2.5 inch schedule 80 pvc conduit for service mast.", Attributes: {"conduit_type":"PVC Schedule 80","conduit_size":"2.5 inch"}, Suggestion: "price 10 ft 2.5 inch schedule 80 PVC conduit" | {"service":"workiz-api"}
2025-08-29 18:23:03 [[34m[34mdebug[34m[39m] [general]: [parserUtils] determineLookupTypeForItem: Classified as PRICE_MATERIAL_COMPONENT. | {"service":"workiz-api"}
2025-08-29 18:23:03 [[32m[32minfo[32m[39m] [general]: [AI Controller] Item "2.5 inch Schedule 80 PVC Conduit for service mast." (type: PRICE_MATERIAL_COMPONENT) flagged for INTERNAL backend price lookup. | {"service":"workiz-api"}
2025-08-29 18:23:03 [[34m[34mdebug[34m[39m] [general]: [AI Controller] Item "2.5 inch Schedule 80 PVC Conduit for service mast." (type: PRICE_MATERIAL_COMPONENT) - internal lookup query details: "price 10 ft 2.5 inch schedule 80 PVC conduit" | {"service":"workiz-api"}
2025-08-29 18:23:03 [[32m[32minfo[32m[39m] [general]: [AI Controller] 🚀 SENDING TO CRAWL4AI: "2.5 inch Schedule 80 PVC Conduit for service mast." with query: "price 10 ft 2.5 inch schedule 80 PVC conduit" | {"service":"workiz-api"}
2025-08-29 18:23:03 [[32m[32minfo[32m[39m] [general]: [AI Controller] Item "2.5 inch PVC Service Entrance Weatherhead.": AI Category: electrical_material, Suggestion: "price 2.5 inch PVC weatherhead" | {"service":"workiz-api"}
2025-08-29 18:23:03 [[34m[34mdebug[34m[39m] [general]: [AI Controller] FULL ITEM DETAILS for "2.5 inch PVC Service Entrance Weatherhead.": {
  "description": "2.5 inch PVC Service Entrance Weatherhead.",
  "category": "electrical_material",
  "quantity": 1,
  "unit": "pcs",
  "attributes": {
    "conduit_type": "PVC",
    "conduit_size": "2.5 inch"
  },
  "lookup_query_suggestion": "price 2.5 inch PVC weatherhead",
  "lookup_results": []
} | {"service":"workiz-api"}
2025-08-29 18:23:03 [[32m[32minfo[32m[39m] [general]: [AI Controller] ✅ CORRECTLY categorized as ELECTRICAL MATERIAL: "2.5 inch PVC Service Entrance Weatherhead." | {"service":"workiz-api"}
2025-08-29 18:23:03 [[34m[34mdebug[34m[39m] [general]: [parserUtils] determineLookupTypeForItem: Processing item - Description: "2.5 inch pvc service entrance weatherhead.", Attributes: {"conduit_type":"PVC","conduit_size":"2.5 inch"}, Suggestion: "price 2.5 inch PVC weatherhead" | {"service":"workiz-api"}
2025-08-29 18:23:03 [[34m[34mdebug[34m[39m] [general]: [parserUtils] determineLookupTypeForItem: Classified as PRICE_GENERAL. | {"service":"workiz-api"}
2025-08-29 18:23:03 [[32m[32minfo[32m[39m] [general]: [AI Controller] Item "2.5 inch PVC Service Entrance Weatherhead." (type: PRICE_GENERAL) flagged for INTERNAL backend price lookup. | {"service":"workiz-api"}
2025-08-29 18:23:03 [[34m[34mdebug[34m[39m] [general]: [AI Controller] Item "2.5 inch PVC Service Entrance Weatherhead." (type: PRICE_GENERAL) - internal lookup query details: "price 2.5 inch PVC weatherhead" | {"service":"workiz-api"}
2025-08-29 18:23:03 [[32m[32minfo[32m[39m] [general]: [AI Controller] 🚀 SENDING TO CRAWL4AI: "2.5 inch PVC Service Entrance Weatherhead." with query: "price 2.5 inch PVC weatherhead" | {"service":"workiz-api"}
2025-08-29 18:23:03 [[32m[32minfo[32m[39m] [general]: [AI Controller] Item "20A Single-Pole AFCI/GFCI Combination Circuit Breaker.": AI Category: electrical_material, Suggestion: "price Siemens 20A dual function AFCI/GFCI breaker" | {"service":"workiz-api"}
2025-08-29 18:23:03 [[34m[34mdebug[34m[39m] [general]: [AI Controller] FULL ITEM DETAILS for "20A Single-Pole AFCI/GFCI Combination Circuit Breaker.": {
  "description": "20A Single-Pole AFCI/GFCI Combination Circuit Breaker.",
  "category": "electrical_material",
  "quantity": 6,
  "unit": "pcs",
  "attributes": {
    "ampacity": "20A",
    "type": "AFCI/GFCI Combination"
  },
  "lookup_query_suggestion": "price Siemens 20A dual function AFCI/GFCI breaker",
  "lookup_results": []
} | {"service":"workiz-api"}
2025-08-29 18:23:03 [[32m[32minfo[32m[39m] [general]: [AI Controller] ✅ CORRECTLY categorized as ELECTRICAL MATERIAL: "20A Single-Pole AFCI/GFCI Combination Circuit Breaker." | {"service":"workiz-api"}
2025-08-29 18:23:03 [[34m[34mdebug[34m[39m] [general]: [parserUtils] determineLookupTypeForItem: Processing item - Description: "20a single-pole afci/gfci combination circuit breaker.", Attributes: {"ampacity":"20A","type":"AFCI/GFCI Combination"}, Suggestion: "price Siemens 20A dual function AFCI/GFCI breaker" | {"service":"workiz-api"}
2025-08-29 18:23:03 [[34m[34mdebug[34m[39m] [general]: [parserUtils] determineLookupTypeForItem: Classified as PRICE_MATERIAL_COMPONENT. | {"service":"workiz-api"}
2025-08-29 18:23:03 [[32m[32minfo[32m[39m] [general]: [AI Controller] Item "20A Single-Pole AFCI/GFCI Combination Circuit Breaker." (type: PRICE_MATERIAL_COMPONENT) flagged for INTERNAL backend price lookup. | {"service":"workiz-api"}
2025-08-29 18:23:03 [[34m[34mdebug[34m[39m] [general]: [AI Controller] Item "20A Single-Pole AFCI/GFCI Combination Circuit Breaker." (type: PRICE_MATERIAL_COMPONENT) - internal lookup query details: "price Siemens 20A dual function AFCI/GFCI breaker" | {"service":"workiz-api"}
2025-08-29 18:23:03 [[32m[32minfo[32m[39m] [general]: [AI Controller] 🚀 SENDING TO CRAWL4AI: "20A Single-Pole AFCI/GFCI Combination Circuit Breaker." with query: "price Siemens 20A dual function AFCI/GFCI breaker" | {"service":"workiz-api"}
2025-08-29 18:23:03 [[32m[32minfo[32m[39m] [general]: [AI Controller] Item "20A Single-Pole Standard Circuit Breaker.": AI Category: electrical_material, Suggestion: "price Siemens QP 20A single pole breaker" | {"service":"workiz-api"}
2025-08-29 18:23:03 [[34m[34mdebug[34m[39m] [general]: [AI Controller] FULL ITEM DETAILS for "20A Single-Pole Standard Circuit Breaker.": {
  "description": "20A Single-Pole Standard Circuit Breaker.",
  "category": "electrical_material",
  "quantity": 8,
  "unit": "pcs",
  "attributes": {
    "ampacity": "20A",
    "type": "Standard"
  },
  "lookup_query_suggestion": "price Siemens QP 20A single pole breaker",
  "lookup_results": []
} | {"service":"workiz-api"}
2025-08-29 18:23:03 [[32m[32minfo[32m[39m] [general]: [AI Controller] ✅ CORRECTLY categorized as ELECTRICAL MATERIAL: "20A Single-Pole Standard Circuit Breaker." | {"service":"workiz-api"}
2025-08-29 18:23:03 [[34m[34mdebug[34m[39m] [general]: [parserUtils] determineLookupTypeForItem: Processing item - Description: "20a single-pole standard circuit breaker.", Attributes: {"ampacity":"20A","type":"Standard"}, Suggestion: "price Siemens QP 20A single pole breaker" | {"service":"workiz-api"}
2025-08-29 18:23:03 [[34m[34mdebug[34m[39m] [general]: [parserUtils] determineLookupTypeForItem: Classified as PRICE_MATERIAL_COMPONENT. | {"service":"workiz-api"}
2025-08-29 18:23:03 [[32m[32minfo[32m[39m] [general]: [AI Controller] Item "20A Single-Pole Standard Circuit Breaker." (type: PRICE_MATERIAL_COMPONENT) flagged for INTERNAL backend price lookup. | {"service":"workiz-api"}
2025-08-29 18:23:03 [[34m[34mdebug[34m[39m] [general]: [AI Controller] Item "20A Single-Pole Standard Circuit Breaker." (type: PRICE_MATERIAL_COMPONENT) - internal lookup query details: "price Siemens QP 20A single pole breaker" | {"service":"workiz-api"}
2025-08-29 18:23:03 [[32m[32minfo[32m[39m] [general]: [AI Controller] 🚀 SENDING TO CRAWL4AI: "20A Single-Pole Standard Circuit Breaker." with query: "price Siemens QP 20A single pole breaker" | {"service":"workiz-api"}
2025-08-29 18:23:03 [[32m[32minfo[32m[39m] [general]: [AI Controller] Item "30A Double-Pole Standard Circuit Breaker.": AI Category: electrical_material, Suggestion: "price Siemens Q230 30A double pole breaker" | {"service":"workiz-api"}
2025-08-29 18:23:03 [[34m[34mdebug[34m[39m] [general]: [AI Controller] FULL ITEM DETAILS for "30A Double-Pole Standard Circuit Breaker.": {
  "description": "30A Double-Pole Standard Circuit Breaker.",
  "category": "electrical_material",
  "quantity": 2,
  "unit": "pcs",
  "attributes": {
    "ampacity": "30A",
    "type": "Standard 2-Pole"
  },
  "lookup_query_suggestion": "price Siemens Q230 30A double pole breaker",
  "lookup_results": []
} | {"service":"workiz-api"}
2025-08-29 18:23:03 [[32m[32minfo[32m[39m] [general]: [AI Controller] ✅ CORRECTLY categorized as ELECTRICAL MATERIAL: "30A Double-Pole Standard Circuit Breaker." | {"service":"workiz-api"}
2025-08-29 18:23:03 [[34m[34mdebug[34m[39m] [general]: [parserUtils] determineLookupTypeForItem: Processing item - Description: "30a double-pole standard circuit breaker.", Attributes: {"ampacity":"30A","type":"Standard 2-Pole"}, Suggestion: "price Siemens Q230 30A double pole breaker" | {"service":"workiz-api"}
2025-08-29 18:23:03 [[34m[34mdebug[34m[39m] [general]: [parserUtils] determineLookupTypeForItem: Classified as PRICE_MATERIAL_COMPONENT. | {"service":"workiz-api"}
2025-08-29 18:23:03 [[32m[32minfo[32m[39m] [general]: [AI Controller] Item "30A Double-Pole Standard Circuit Breaker." (type: PRICE_MATERIAL_COMPONENT) flagged for INTERNAL backend price lookup. | {"service":"workiz-api"}
2025-08-29 18:23:03 [[34m[34mdebug[34m[39m] [general]: [AI Controller] Item "30A Double-Pole Standard Circuit Breaker." (type: PRICE_MATERIAL_COMPONENT) - internal lookup query details: "price Siemens Q230 30A double pole breaker" | {"service":"workiz-api"}
2025-08-29 18:23:03 [[32m[32minfo[32m[39m] [general]: [AI Controller] 🚀 SENDING TO CRAWL4AI: "30A Double-Pole Standard Circuit Breaker." with query: "price Siemens Q230 30A double pole breaker" | {"service":"workiz-api"}
2025-08-29 18:23:03 [[32m[32minfo[32m[39m] [general]: [AI Controller] Item "50A Double-Pole Standard Circuit Breaker.": AI Category: electrical_material, Suggestion: "price Siemens Q250 50A double pole breaker" | {"service":"workiz-api"}
2025-08-29 18:23:03 [[34m[34mdebug[34m[39m] [general]: [AI Controller] FULL ITEM DETAILS for "50A Double-Pole Standard Circuit Breaker.": {
  "description": "50A Double-Pole Standard Circuit Breaker.",
  "category": "electrical_material",
  "quantity": 1,
  "unit": "pcs",
  "attributes": {
    "ampacity": "50A",
    "type": "Standard 2-Pole"
  },
  "lookup_query_suggestion": "price Siemens Q250 50A double pole breaker",
  "lookup_results": []
} | {"service":"workiz-api"}
2025-08-29 18:23:03 [[32m[32minfo[32m[39m] [general]: [AI Controller] ✅ CORRECTLY categorized as ELECTRICAL MATERIAL: "50A Double-Pole Standard Circuit Breaker." | {"service":"workiz-api"}
2025-08-29 18:23:03 [[34m[34mdebug[34m[39m] [general]: [parserUtils] determineLookupTypeForItem: Processing item - Description: "50a double-pole standard circuit breaker.", Attributes: {"ampacity":"50A","type":"Standard 2-Pole"}, Suggestion: "price Siemens Q250 50A double pole breaker" | {"service":"workiz-api"}
2025-08-29 18:23:03 [[34m[34mdebug[34m[39m] [general]: [parserUtils] determineLookupTypeForItem: Classified as PRICE_MATERIAL_COMPONENT. | {"service":"workiz-api"}
2025-08-29 18:23:03 [[32m[32minfo[32m[39m] [general]: [AI Controller] Item "50A Double-Pole Standard Circuit Breaker." (type: PRICE_MATERIAL_COMPONENT) flagged for INTERNAL backend price lookup. | {"service":"workiz-api"}
2025-08-29 18:23:03 [[34m[34mdebug[34m[39m] [general]: [AI Controller] Item "50A Double-Pole Standard Circuit Breaker." (type: PRICE_MATERIAL_COMPONENT) - internal lookup query details: "price Siemens Q250 50A double pole breaker" | {"service":"workiz-api"}
2025-08-29 18:23:03 [[32m[32minfo[32m[39m] [general]: [AI Controller] 🚀 SENDING TO CRAWL4AI: "50A Double-Pole Standard Circuit Breaker." with query: "price Siemens Q250 50A double pole breaker" | {"service":"workiz-api"}
2025-08-29 18:23:03 [[32m[32minfo[32m[39m] [general]: [AI Controller] Item "5/8 inch x 8 foot Copper-Clad Ground Rod.": AI Category: electrical_material, Suggestion: "price 5/8 inch 8 ft ground rod" | {"service":"workiz-api"}
2025-08-29 18:23:03 [[34m[34mdebug[34m[39m] [general]: [AI Controller] FULL ITEM DETAILS for "5/8 inch x 8 foot Copper-Clad Ground Rod.": {
  "description": "5/8 inch x 8 foot Copper-Clad Ground Rod.",
  "category": "electrical_material",
  "quantity": 2,
  "unit": "pcs",
  "attributes": {
    "material": "Copper-Clad Steel",
    "size": "5/8 in x 8 ft"
  },
  "lookup_query_suggestion": "price 5/8 inch 8 ft ground rod",
  "lookup_results": []
} | {"service":"workiz-api"}
2025-08-29 18:23:03 [[32m[32minfo[32m[39m] [general]: [AI Controller] ✅ CORRECTLY categorized as ELECTRICAL MATERIAL: "5/8 inch x 8 foot Copper-Clad Ground Rod." | {"service":"workiz-api"}
2025-08-29 18:23:03 [[34m[34mdebug[34m[39m] [general]: [parserUtils] determineLookupTypeForItem: Processing item - Description: "5/8 inch x 8 foot copper-clad ground rod.", Attributes: {"material":"Copper-Clad Steel","size":"5/8 in x 8 ft"}, Suggestion: "price 5/8 inch 8 ft ground rod" | {"service":"workiz-api"}
2025-08-29 18:23:03 [[34m[34mdebug[34m[39m] [general]: [parserUtils] determineLookupTypeForItem: Classified as PRICE_MATERIAL_COMPONENT. | {"service":"workiz-api"}
2025-08-29 18:23:03 [[32m[32minfo[32m[39m] [general]: [AI Controller] Item "5/8 inch x 8 foot Copper-Clad Ground Rod." (type: PRICE_MATERIAL_COMPONENT) flagged for INTERNAL backend price lookup. | {"service":"workiz-api"}
2025-08-29 18:23:03 [[34m[34mdebug[34m[39m] [general]: [AI Controller] Item "5/8 inch x 8 foot Copper-Clad Ground Rod." (type: PRICE_MATERIAL_COMPONENT) - internal lookup query details: "price 5/8 inch 8 ft ground rod" | {"service":"workiz-api"}
2025-08-29 18:23:03 [[32m[32minfo[32m[39m] [general]: [AI Controller] 🚀 SENDING TO CRAWL4AI: "5/8 inch x 8 foot Copper-Clad Ground Rod." with query: "price 5/8 inch 8 ft ground rod" | {"service":"workiz-api"}
2025-08-29 18:23:03 [[32m[32minfo[32m[39m] [general]: [AI Controller] Item "#4 AWG Solid Bare Copper Grounding Electrode Conductor.": AI Category: electrical_material, Suggestion: "price 30 ft #4 solid bare copper wire" | {"service":"workiz-api"}
2025-08-29 18:23:03 [[34m[34mdebug[34m[39m] [general]: [AI Controller] FULL ITEM DETAILS for "#4 AWG Solid Bare Copper Grounding Electrode Conductor.": {
  "description": "#4 AWG Solid Bare Copper Grounding Electrode Conductor.",
  "category": "electrical_material",
  "quantity": 30,
  "unit": "feet",
  "attributes": {
    "material": "Copper",
    "size": "4 AWG",
    "type": "Solid Bare"
  },
  "lookup_query_suggestion": "price 30 ft #4 solid bare copper wire",
  "lookup_results": []
} | {"service":"workiz-api"}
2025-08-29 18:23:03 [[32m[32minfo[32m[39m] [general]: [AI Controller] ✅ CORRECTLY categorized as ELECTRICAL MATERIAL: "#4 AWG Solid Bare Copper Grounding Electrode Conductor." | {"service":"workiz-api"}
2025-08-29 18:23:03 [[34m[34mdebug[34m[39m] [general]: [parserUtils] determineLookupTypeForItem: Processing item - Description: "#4 awg solid bare copper grounding electrode conductor.", Attributes: {"material":"Copper","size":"4 AWG","type":"Solid Bare"}, Suggestion: "price 30 ft #4 solid bare copper wire" | {"service":"workiz-api"}
2025-08-29 18:23:03 [[34m[34mdebug[34m[39m] [general]: [parserUtils] determineLookupTypeForItem: Classified as PRICE_MATERIAL_COMPONENT. | {"service":"workiz-api"}
2025-08-29 18:23:03 [[32m[32minfo[32m[39m] [general]: [AI Controller] Item "#4 AWG Solid Bare Copper Grounding Electrode Conductor." (type: PRICE_MATERIAL_COMPONENT) flagged for INTERNAL backend price lookup. | {"service":"workiz-api"}
2025-08-29 18:23:03 [[34m[34mdebug[34m[39m] [general]: [AI Controller] Item "#4 AWG Solid Bare Copper Grounding Electrode Conductor." (type: PRICE_MATERIAL_COMPONENT) - internal lookup query details: "price 30 ft #4 solid bare copper wire" | {"service":"workiz-api"}
2025-08-29 18:23:03 [[32m[32minfo[32m[39m] [general]: [AI Controller] 🚀 SENDING TO CRAWL4AI: "#4 AWG Solid Bare Copper Grounding Electrode Conductor." with query: "price 30 ft #4 solid bare copper wire" | {"service":"workiz-api"}
2025-08-29 18:23:03 [[32m[32minfo[32m[39m] [general]: [AI Controller] Item "Acorn Ground Rod Clamps for 5/8 inch rod and #4 AWG wire.": AI Category: electrical_material, Suggestion: "price acorn ground rod clamp 5/8 inch" | {"service":"workiz-api"}
2025-08-29 18:23:03 [[34m[34mdebug[34m[39m] [general]: [AI Controller] FULL ITEM DETAILS for "Acorn Ground Rod Clamps for 5/8 inch rod and #4 AWG wire.": {
  "description": "Acorn Ground Rod Clamps for 5/8 inch rod and #4 AWG wire.",
  "category": "electrical_material",
  "quantity": 2,
  "unit": "pcs",
  "attributes": {},
  "lookup_query_suggestion": "price acorn ground rod clamp 5/8 inch",
  "lookup_results": []
} | {"service":"workiz-api"}
2025-08-29 18:23:03 [[32m[32minfo[32m[39m] [general]: [AI Controller] ✅ CORRECTLY categorized as ELECTRICAL MATERIAL: "Acorn Ground Rod Clamps for 5/8 inch rod and #4 AWG wire." | {"service":"workiz-api"}
2025-08-29 18:23:03 [[34m[34mdebug[34m[39m] [general]: [parserUtils] determineLookupTypeForItem: Processing item - Description: "acorn ground rod clamps for 5/8 inch rod and #4 awg wire.", Attributes: {}, Suggestion: "price acorn ground rod clamp 5/8 inch" | {"service":"workiz-api"}
2025-08-29 18:23:03 [[34m[34mdebug[34m[39m] [general]: [parserUtils] determineLookupTypeForItem: Classified as PRICE_MATERIAL_COMPONENT. | {"service":"workiz-api"}
2025-08-29 18:23:03 [[32m[32minfo[32m[39m] [general]: [AI Controller] Item "Acorn Ground Rod Clamps for 5/8 inch rod and #4 AWG wire." (type: PRICE_MATERIAL_COMPONENT) flagged for INTERNAL backend price lookup. | {"service":"workiz-api"}
2025-08-29 18:23:03 [[34m[34mdebug[34m[39m] [general]: [AI Controller] Item "Acorn Ground Rod Clamps for 5/8 inch rod and #4 AWG wire." (type: PRICE_MATERIAL_COMPONENT) - internal lookup query details: "price acorn ground rod clamp 5/8 inch" | {"service":"workiz-api"}
2025-08-29 18:23:03 [[32m[32minfo[32m[39m] [general]: [AI Controller] 🚀 SENDING TO CRAWL4AI: "Acorn Ground Rod Clamps for 5/8 inch rod and #4 AWG wire." with query: "price acorn ground rod clamp 5/8 inch" | {"service":"workiz-api"}
2025-08-29 18:23:03 [[32m[32minfo[32m[39m] [general]: [AI Controller] Item "Intersystem Bonding Bridge for low voltage systems.": AI Category: electrical_material, Suggestion: "price intersystem bonding bridge" | {"service":"workiz-api"}
2025-08-29 18:23:03 [[34m[34mdebug[34m[39m] [general]: [AI Controller] FULL ITEM DETAILS for "Intersystem Bonding Bridge for low voltage systems.": {
  "description": "Intersystem Bonding Bridge for low voltage systems.",
  "category": "electrical_material",
  "quantity": 1,
  "unit": "pcs",
  "attributes": {},
  "lookup_query_suggestion": "price intersystem bonding bridge",
  "lookup_results": []
} | {"service":"workiz-api"}
2025-08-29 18:23:03 [[32m[32minfo[32m[39m] [general]: [AI Controller] ✅ CORRECTLY categorized as ELECTRICAL MATERIAL: "Intersystem Bonding Bridge for low voltage systems." | {"service":"workiz-api"}
2025-08-29 18:23:03 [[34m[34mdebug[34m[39m] [general]: [parserUtils] determineLookupTypeForItem: Processing item - Description: "intersystem bonding bridge for low voltage systems.", Attributes: {}, Suggestion: "price intersystem bonding bridge" | {"service":"workiz-api"}
2025-08-29 18:23:03 [[34m[34mdebug[34m[39m] [general]: [parserUtils] determineLookupTypeForItem: Classified as PRICE_GENERAL. | {"service":"workiz-api"}
2025-08-29 18:23:03 [[32m[32minfo[32m[39m] [general]: [AI Controller] Item "Intersystem Bonding Bridge for low voltage systems." (type: PRICE_GENERAL) flagged for INTERNAL backend price lookup. | {"service":"workiz-api"}
2025-08-29 18:23:03 [[34m[34mdebug[34m[39m] [general]: [AI Controller] Item "Intersystem Bonding Bridge for low voltage systems." (type: PRICE_GENERAL) - internal lookup query details: "price intersystem bonding bridge" | {"service":"workiz-api"}
2025-08-29 18:23:03 [[32m[32minfo[32m[39m] [general]: [AI Controller] 🚀 SENDING TO CRAWL4AI: "Intersystem Bonding Bridge for low voltage systems." with query: "price intersystem bonding bridge" | {"service":"workiz-api"}
2025-08-29 18:23:03 [[32m[32minfo[32m[39m] [general]: [AI Controller] Item "Electrical Duct Seal Compound.": AI Category: electrical_material, Suggestion: "price 1lb duct seal compound" | {"service":"workiz-api"}
2025-08-29 18:23:03 [[34m[34mdebug[34m[39m] [general]: [AI Controller] FULL ITEM DETAILS for "Electrical Duct Seal Compound.": {
  "description": "Electrical Duct Seal Compound.",
  "category": "electrical_material",
  "quantity": 1,
  "unit": "pcs",
  "attributes": {},
  "lookup_query_suggestion": "price 1lb duct seal compound",
  "lookup_results": []
} | {"service":"workiz-api"}
2025-08-29 18:23:03 [[32m[32minfo[32m[39m] [general]: [AI Controller] ✅ CORRECTLY categorized as ELECTRICAL MATERIAL: "Electrical Duct Seal Compound." | {"service":"workiz-api"}
2025-08-29 18:23:03 [[34m[34mdebug[34m[39m] [general]: [parserUtils] determineLookupTypeForItem: Processing item - Description: "electrical duct seal compound.", Attributes: {}, Suggestion: "price 1lb duct seal compound" | {"service":"workiz-api"}
2025-08-29 18:23:03 [[34m[34mdebug[34m[39m] [general]: [parserUtils] determineLookupTypeForItem: Classified as PRICE_GENERAL. | {"service":"workiz-api"}
2025-08-29 18:23:03 [[32m[32minfo[32m[39m] [general]: [AI Controller] Item "Electrical Duct Seal Compound." (type: PRICE_GENERAL) flagged for INTERNAL backend price lookup. | {"service":"workiz-api"}
2025-08-29 18:23:03 [[34m[34mdebug[34m[39m] [general]: [AI Controller] Item "Electrical Duct Seal Compound." (type: PRICE_GENERAL) - internal lookup query details: "price 1lb duct seal compound" | {"service":"workiz-api"}
2025-08-29 18:23:03 [[32m[32minfo[32m[39m] [general]: [AI Controller] 🚀 SENDING TO CRAWL4AI: "Electrical Duct Seal Compound." with query: "price 1lb duct seal compound" | {"service":"workiz-api"}
2025-08-29 18:23:03 [[32m[32minfo[32m[39m] [general]: [AI Controller] Item "Printed Circuit Breaker Directory Label Sheet.": AI Category: electrical_material, Suggestion: "price panel schedule labels" | {"service":"workiz-api"}
2025-08-29 18:23:03 [[34m[34mdebug[34m[39m] [general]: [AI Controller] FULL ITEM DETAILS for "Printed Circuit Breaker Directory Label Sheet.": {
  "description": "Printed Circuit Breaker Directory Label Sheet.",
  "category": "electrical_material",
  "quantity": 1,
  "unit": "pcs",
  "attributes": {},
  "lookup_query_suggestion": "price panel schedule labels",
  "lookup_results": []
} | {"service":"workiz-api"}
2025-08-29 18:23:03 [[32m[32minfo[32m[39m] [general]: [AI Controller] ✅ CORRECTLY categorized as ELECTRICAL MATERIAL: "Printed Circuit Breaker Directory Label Sheet." | {"service":"workiz-api"}
2025-08-29 18:23:03 [[34m[34mdebug[34m[39m] [general]: [parserUtils] determineLookupTypeForItem: Processing item - Description: "printed circuit breaker directory label sheet.", Attributes: {}, Suggestion: "price panel schedule labels" | {"service":"workiz-api"}
2025-08-29 18:23:03 [[34m[34mdebug[34m[39m] [general]: [parserUtils] determineLookupTypeForItem: Classified as PRICE_MATERIAL_COMPONENT. | {"service":"workiz-api"}
2025-08-29 18:23:03 [[32m[32minfo[32m[39m] [general]: [AI Controller] Item "Printed Circuit Breaker Directory Label Sheet." (type: PRICE_MATERIAL_COMPONENT) flagged for INTERNAL backend price lookup. | {"service":"workiz-api"}
2025-08-29 18:23:03 [[34m[34mdebug[34m[39m] [general]: [AI Controller] Item "Printed Circuit Breaker Directory Label Sheet." (type: PRICE_MATERIAL_COMPONENT) - internal lookup query details: "price panel schedule labels" | {"service":"workiz-api"}
2025-08-29 18:23:03 [[32m[32minfo[32m[39m] [general]: [AI Controller] 🚀 SENDING TO CRAWL4AI: "Printed Circuit Breaker Directory Label Sheet." with query: "price panel schedule labels" | {"service":"workiz-api"}
2025-08-29 18:23:03 [[32m[32minfo[32m[39m] [general]: [AI Controller] Item "Stucco patch mix and bonding agent for repairing wall opening.": AI Category: non_electrical_material, Suggestion: "price stucco patch mix" | {"service":"workiz-api"}
2025-08-29 18:23:03 [[34m[34mdebug[34m[39m] [general]: [AI Controller] FULL ITEM DETAILS for "Stucco patch mix and bonding agent for repairing wall opening.": {
  "description": "Stucco patch mix and bonding agent for repairing wall opening.",
  "category": "non_electrical_material",
  "quantity": 1,
  "unit": "bag",
  "attributes": {},
  "lookup_query_suggestion": "price stucco patch mix",
  "lookup_results": []
} | {"service":"workiz-api"}
2025-08-29 18:23:03 [[32m[32minfo[32m[39m] [general]: [AI Controller] ℹ️  Categorized as "non_electrical_material": "Stucco patch mix and bonding agent for repairing wall opening." | {"service":"workiz-api"}
2025-08-29 18:23:03 [[32m[32minfo[32m[39m] [general]: [AI Controller] Item "Stucco patch mix and bonding agent for repairing wall opening." will SKIP price lookup. Category: non_electrical_material | {"service":"workiz-api"}
2025-08-29 18:23:03 [[32m[32minfo[32m[39m] [general]: [AI Controller] Item "Stucco patch mix and bonding agent for repairing wall opening." categorized as NON-ELECTRICAL material by AI - including in output without price lookup | {"service":"workiz-api"}
2025-08-29 18:23:03 [[32m[32minfo[32m[39m] [general]: [AI Controller] Item "Exterior-grade, paintable silicone caulk for weather sealing.": AI Category: non_electrical_material, Suggestion: "price exterior paintable silicone caulk" | {"service":"workiz-api"}
2025-08-29 18:23:03 [[34m[34mdebug[34m[39m] [general]: [AI Controller] FULL ITEM DETAILS for "Exterior-grade, paintable silicone caulk for weather sealing.": {
  "description": "Exterior-grade, paintable silicone caulk for weather sealing.",
  "category": "non_electrical_material",
  "quantity": 1,
  "unit": "tube",
  "attributes": {},
  "lookup_query_suggestion": "price exterior paintable silicone caulk",
  "lookup_results": []
} | {"service":"workiz-api"}
2025-08-29 18:23:03 [[32m[32minfo[32m[39m] [general]: [AI Controller] ℹ️  Categorized as "non_electrical_material": "Exterior-grade, paintable silicone caulk for weather sealing." | {"service":"workiz-api"}
2025-08-29 18:23:03 [[32m[32minfo[32m[39m] [general]: [AI Controller] Item "Exterior-grade, paintable silicone caulk for weather sealing." will SKIP price lookup. Category: non_electrical_material | {"service":"workiz-api"}
2025-08-29 18:23:03 [[32m[32minfo[32m[39m] [general]: [AI Controller] Item "Exterior-grade, paintable silicone caulk for weather sealing." categorized as NON-ELECTRICAL material by AI - including in output without price lookup | {"service":"workiz-api"}
2025-08-29 18:23:03 [[32m[32minfo[32m[39m] [general]: [AI Controller] Finished processing items for additional lookups. | {"service":"workiz-api"}
2025-08-29 18:23:04 [[34m[34mdebug[34m[39m] [ai-generation]: [AI Controller] AI Response with Lookups: {
  "items": [
    {
      "description": "Coordination with local utility provider for service disconnection and reconnection.",
      "category": "administrative",
      "quantity": 1,
      "unit": "task",
      "attributes": {},
      "lookup_query_suggestion": "local utility electrical service reconnection fee",
      "lookup_results": [
        {
          "type": "ADMINISTRATIVE_ITEM",
          "status": "administrative_item_no_price",
          "timestamp": "2025-08-30T01:23:03.786Z",
          "reason": "Administrative items included for informational purposes",
          "source": "ai_category_classification"
        }
      ],
      "priceStatus": "not_applicable",
      "source": "Administrative",
      "name": "Coordination with local utility provider for service disconnection and reconnection.",
      "price": 0,
      "currency": "USD"
    },
    {
      "description": "Electrical permit acquisition from the local authority having jurisdiction (AHJ).",
      "category": "administrative",
      "quantity": 1,
      "unit": "permit",
      "attributes": {},
      "lookup_query_suggestion": "residential electrical service upgrade permit fee",
      "lookup_results": [
        {
          "type": "ADMINISTRATIVE_ITEM",
          "status": "administrative_item_no_price",
          "timestamp": "2025-08-30T01:23:03.788Z",
          "reason": "Administrative items included for informational purposes",
          "source": "ai_category_classification"
        }
      ],
      "priceStatus": "not_applicable",
      "source": "Administrative",
      "name": "Electrical permit acquisition from the local authority having jurisdiction (AHJ).",
      "price": 0,
      "currency": "USD"
    },
    {
      "description": "Demolition, removal, and disposal of existing Federal Pacific Electric main panel and associated service entrance components.",
      "category": "labor",
      "quantity": 3,
      "unit": "hours",
      "attributes": {},
      "lookup_query_suggestion": "",
      "lookup_results": [
        {
          "type": "LABOR_ITEM",
          "status": "labor_item_no_price_lookup",
          "timestamp": "2025-08-30T01:23:03.790Z",
          "reason": "Labor items do not require price lookups",
          "source": "ai_category_classification"
        }
      ],
      "priceStatus": "not_applicable",
      "source": "Labor",
      "name": "Demolition",
      "price": 0,
      "currency": "USD"
    },
    {
      "description": "Installation of new 200A meter-main combination load center.",
      "category": "labor",
      "quantity": 6,
      "unit": "hours",
      "attributes": {},
      "lookup_query_suggestion": "",
      "lookup_results": [
        {
          "type": "LABOR_ITEM",
          "status": "labor_item_no_price_lookup",
          "timestamp": "2025-08-30T01:23:03.792Z",
          "reason": "Labor items do not require price lookups",
          "source": "ai_category_classification"
        }
      ],
      "priceStatus": "not_applicable",
      "source": "Labor",
      "name": "Installation of new 200A meter",
      "price": 0,
      "currency": "USD"
    },
    {
      "description": "Installation of new service entrance mast, weatherhead, and conductors.",
      "category": "labor",
      "quantity": 3,
      "unit": "hours",
      "attributes": {},
      "lookup_query_suggestion": "",
      "lookup_results": [
        {
          "type": "LABOR_ITEM",
          "status": "labor_item_no_price_lookup",
          "timestamp": "2025-08-30T01:23:03.795Z",
          "reason": "Labor items do not require price lookups",
          "source": "ai_category_classification"
        }
      ],
      "priceStatus": "not_applicable",
      "source": "Labor",
      "name": "Installation of new service entrance mast",
      "price": 0,
      "currency": "USD"
    },
    {
      "description": "Installation of new grounding electrode system, including ground rods and bonding to water/gas systems as per NEC.",
      "category": "labor",
      "quantity": 2,
      "unit": "hours",
      "attributes": {},
      "lookup_query_suggestion": "",
      "lookup_results": [
        {
          "type": "LABOR_ITEM",
          "status": "labor_item_no_price_lookup",
          "timestamp": "2025-08-30T01:23:03.797Z",
          "reason": "Labor items do not require price lookups",
          "source": "ai_category_classification"
        }
      ],
      "priceStatus": "not_applicable",
      "source": "Labor",
      "name": "Installation of new grounding electrode system",
      "price": 0,
      "currency": "USD"
    },
    {
      "description": "Termination of existing branch circuits into new circuit breakers. Includes circuit identification and labeling.",
      "category": "labor",
      "quantity": 5,
      "unit": "hours",
      "attributes": {},
      "lookup_query_suggestion": "",
      "lookup_results": [
        {
          "type": "LABOR_ITEM",
          "status": "labor_item_no_price_lookup",
          "timestamp": "2025-08-30T01:23:03.799Z",
          "reason": "Labor items do not require price lookups",
          "source": "ai_category_classification"
        }
      ],
      "priceStatus": "not_applicable",
      "source": "Labor",
      "name": "Termination of existing branch circuits into new circuit breakers. Includes circuit...",
      "price": 0,
      "currency": "USD"
    },
    {
      "description": "Final system testing, commissioning, and walkthrough.",
      "category": "labor",
      "quantity": 1.5,
      "unit": "hours",
      "attributes": {},
      "lookup_query_suggestion": "",
      "lookup_results": [
        {
          "type": "LABOR_ITEM",
          "status": "labor_item_no_price_lookup",
          "timestamp": "2025-08-30T01:23:03.801Z",
          "reason": "Labor items do not require price lookups",
          "source": "ai_category_classification"
        }
      ],
      "priceStatus": "not_applicable",
      "source": "Labor",
      "name": "Final system testing",
      "price": 0,
      "currency": "USD"
    },
    {
      "description": "Siemens MC4040B1200FEN 200A 40-Space 40-Circuit Outdoor Ringless Meter-Main Combination Load Center.",
      "category": "electrical_material",
      "quantity": 1,
      "unit": "pcs",
      "attributes": {
        "ampacity": "200A",
        "type": "Meter-Main Combo",
        "rating": "NEMA 3R Outdoor"
      },
      "lookup_query_suggestion": "price Siemens MC4040B1200FEN 200A meter main",
      "lookup_results": [
        {
          "type": "PRICE_MATERIAL_COMPONENT",
          "status": "pending_internal_price_lookup",
          "request_data": {
            "internal_query_details": "price Siemens MC4040B1200FEN 200A meter main"
          },
          "timestamp": "2025-08-30T01:23:03.847Z",
          "source": "ai_suggestion_for_lookup"
        }
      ],
      "name": "Siemens MC4040B1200FEN 200A 40",
      "price": 0,
      "currency": "USD"
    },
    {
      "description": "4/0-4/0-4/0-2/0 Aluminum SER Service Entrance Cable.",
      "category": "electrical_material",
      "quantity": 20,
      "unit": "feet",
      "attributes": {
        "material": "Aluminum",
        "size": "4/0-4/0-4/0-2/0",
        "type": "SER"
      },
      "lookup_query_suggestion": "price 20 ft 4/0 aluminum SER cable",
      "lookup_results": [
        {
          "type": "PRICE_MATERIAL_COMPONENT",
          "status": "pending_internal_price_lookup",
          "request_data": {
            "internal_query_details": "price 20 ft 4/0 aluminum SER cable"
          },
          "timestamp": "2025-08-30T01:23:03.889Z",
          "source": "ai_suggestion_for_lookup"
        }
      ],
      "name": "4/0",
      "price": 0,
      "currency": "USD"
    },
    {
      "description": "2.5 inch Schedule 80 PVC Conduit for service mast.",
      "category": "electrical_material",
      "quantity": 10,
      "unit": "feet",
      "attributes": {
        "conduit_type": "PVC Schedule 80",
        "conduit_size": "2.5 inch"
      },
      "lookup_query_suggestion": "price 10 ft 2.5 inch schedule 80 PVC conduit",
      "lookup_results": [
        {
          "type": "PRICE_MATERIAL_COMPONENT",
          "status": "pending_internal_price_lookup",
          "request_data": {
            "internal_query_details": "price 10 ft 2.5 inch schedule 80 PVC conduit"
          },
          "timestamp": "2025-08-30T01:23:03.891Z",
          "source": "ai_suggestion_for_lookup"
        }
      ],
      "name": "2.5 inch Schedule 80 PVC Conduit for service mast.",
      "price": 0,
      "currency": "USD"
    },
    {
      "description": "2.5 inch PVC Service Entrance Weatherhead.",
      "category": "electrical_material",
      "quantity": 1,
      "unit": "pcs",
      "attributes": {
        "conduit_type": "PVC",
        "conduit_size": "2.5 inch"
      },
      "lookup_query_suggestion": "price 2.5 inch PVC weatherhead",
      "lookup_results": [
        {
          "type": "PRICE_GENERAL",
          "status": "pending_internal_price_lookup",
          "request_data": {
            "internal_query_details": "price 2.5 inch PVC weatherhead"
          },
          "timestamp": "2025-08-30T01:23:03.895Z",
          "source": "ai_suggestion_for_lookup"
        }
      ],
      "name": "2.5 inch PVC Service Entrance Weatherhead.",
      "price": 0,
      "currency": "USD"
    },
    {
      "description": "20A Single-Pole AFCI/GFCI Combination Circuit Breaker.",
      "category": "electrical_material",
      "quantity": 6,
      "unit": "pcs",
      "attributes": {
        "ampacity": "20A",
        "type": "AFCI/GFCI Combination"
      },
      "lookup_query_suggestion": "price Siemens 20A dual function AFCI/GFCI breaker",
      "lookup_results": [
        {
          "type": "PRICE_MATERIAL_COMPONENT",
          "status": "pending_internal_price_lookup",
          "request_data": {
            "internal_query_details": "price Siemens 20A dual function AFCI/GFCI breaker"
          },
          "timestamp": "2025-08-30T01:23:03.898Z",
          "source": "ai_suggestion_for_lookup"
        }
      ],
      "name": "20A Single",
      "price": 0,
      "currency": "USD"
    },
    {
      "description": "20A Single-Pole Standard Circuit Breaker.",
      "category": "electrical_material",
      "quantity": 8,
      "unit": "pcs",
      "attributes": {
        "ampacity": "20A",
        "type": "Standard"
      },
      "lookup_query_suggestion": "price Siemens QP 20A single pole breaker",
      "lookup_results": [
        {
          "type": "PRICE_MATERIAL_COMPONENT",
          "status": "pending_internal_price_lookup",
          "request_data": {
            "internal_query_details": "price Siemens QP 20A single pole breaker"
          },
          "timestamp": "2025-08-30T01:23:03.922Z",
          "source": "ai_suggestion_for_lookup"
        }
      ],
      "name": "20A Single",
      "price": 0,
      "currency": "USD"
    },
    {
      "description": "30A Double-Pole Standard Circuit Breaker.",
      "category": "electrical_material",
      "quantity": 2,
      "unit": "pcs",
      "attributes": {
        "ampacity": "30A",
        "type": "Standard 2-Pole"
      },
      "lookup_query_suggestion": "price Siemens Q230 30A double pole breaker",
      "lookup_results": [
        {
          "type": "PRICE_MATERIAL_COMPONENT",
          "status": "pending_internal_price_lookup",
          "request_data": {
            "internal_query_details": "price Siemens Q230 30A double pole breaker"
          },
          "timestamp": "2025-08-30T01:23:03.926Z",
          "source": "ai_suggestion_for_lookup"
        }
      ],
      "name": "30A Double",
      "price": 0,
      "currency": "USD"
    },
    {
      "description": "50A Double-Pole Standard Circuit Breaker.",
      "category": "electrical_material",
      "quantity": 1,
      "unit": "pcs",
      "attributes": {
        "ampacity": "50A",
        "type": "Standard 2-Pole"
      },
      "lookup_query_suggestion": "price Siemens Q250 50A double pole breaker",
      "lookup_results": [
        {
          "type": "PRICE_MATERIAL_COMPONENT",
          "status": "pending_internal_price_lookup",
          "request_data": {
            "internal_query_details": "price Siemens Q250 50A double pole breaker"
          },
          "timestamp": "2025-08-30T01:23:03.933Z",
          "source": "ai_suggestion_for_lookup"
        }
      ],
      "name": "50A Double",
      "price": 0,
      "currency": "USD"
    },
    {
      "description": "5/8 inch x 8 foot Copper-Clad Ground Rod.",
      "category": "electrical_material",
      "quantity": 2,
      "unit": "pcs",
      "attributes": {
        "material": "Copper-Clad Steel",
        "size": "5/8 in x 8 ft"
      },
      "lookup_query_suggestion": "price 5/8 inch 8 ft ground rod",
      "lookup_results": [
        {
          "type": "PRICE_MATERIAL_COMPONENT",
          "status": "pending_internal_price_lookup",
          "request_data": {
            "internal_query_details": "price 5/8 inch 8 ft ground rod"
          },
          "timestamp": "2025-08-30T01:23:03.940Z",
          "source": "ai_suggestion_for_lookup"
        }
      ],
      "name": "5/8 inch x 8 foot Copper",
      "price": 0,
      "currency": "USD"
    },
    {
      "description": "#4 AWG Solid Bare Copper Grounding Electrode Conductor.",
      "category": "electrical_material",
      "quantity": 30,
      "unit": "feet",
      "attributes": {
        "material": "Copper",
        "size": "4 AWG",
        "type": "Solid Bare"
      },
      "lookup_query_suggestion": "price 30 ft #4 solid bare copper wire",
      "lookup_results": [
        {
          "type": "PRICE_MATERIAL_COMPONENT",
          "status": "pending_internal_price_lookup",
          "request_data": {
            "internal_query_details": "price 30 ft #4 solid bare copper wire"
          },
          "timestamp": "2025-08-30T01:23:03.943Z",
          "source": "ai_suggestion_for_lookup"
        }
      ],
      "name": "#4 AWG Solid Bare Copper Grounding Electrode Conductor.",
      "price": 0,
      "currency": "USD"
    },
    {
      "description": "Acorn Ground Rod Clamps for 5/8 inch rod and #4 AWG wire.",
      "category": "electrical_material",
      "quantity": 2,
      "unit": "pcs",
      "attributes": {},
      "lookup_query_suggestion": "price acorn ground rod clamp 5/8 inch",
      "lookup_results": [
        {
          "type": "PRICE_MATERIAL_COMPONENT",
          "status": "pending_internal_price_lookup",
          "request_data": {
            "internal_query_details": "price acorn ground rod clamp 5/8 inch"
          },
          "timestamp": "2025-08-30T01:23:03.947Z",
          "source": "ai_suggestion_for_lookup"
        }
      ],
      "name": "Acorn Ground Rod Clamps for 5/8 inch rod and #4 AWG wire.",
      "price": 0,
      "currency": "USD"
    },
    {
      "description": "Intersystem Bonding Bridge for low voltage systems.",
      "category": "electrical_material",
      "quantity": 1,
      "unit": "pcs",
      "attributes": {},
      "lookup_query_suggestion": "price intersystem bonding bridge",
      "lookup_results": [
        {
          "type": "PRICE_GENERAL",
          "status": "pending_internal_price_lookup",
          "request_data": {
            "internal_query_details": "price intersystem bonding bridge"
          },
          "timestamp": "2025-08-30T01:23:03.949Z",
          "source": "ai_suggestion_for_lookup"
        }
      ],
      "name": "Intersystem Bonding Bridge for low voltage systems.",
      "price": 0,
      "currency": "USD"
    },
    {
      "description": "Electrical Duct Seal Compound.",
      "category": "electrical_material",
      "quantity": 1,
      "unit": "pcs",
      "attributes": {},
      "lookup_query_suggestion": "price 1lb duct seal compound",
      "lookup_results": [
        {
          "type": "PRICE_GENERAL",
          "status": "pending_internal_price_lookup",
          "request_data": {
            "internal_query_details": "price 1lb duct seal compound"
          },
          "timestamp": "2025-08-30T01:23:03.949Z",
          "source": "ai_suggestion_for_lookup"
        }
      ],
      "name": "Electrical Duct Seal Compound.",
      "price": 0,
      "currency": "USD"
    },
    {
      "description": "Printed Circuit Breaker Directory Label Sheet.",
      "category": "electrical_material",
      "quantity": 1,
      "unit": "pcs",
      "attributes": {},
      "lookup_query_suggestion": "price panel schedule labels",
      "lookup_results": [
        {
          "type": "PRICE_MATERIAL_COMPONENT",
          "status": "pending_internal_price_lookup",
          "request_data": {
            "internal_query_details": "price panel schedule labels"
          },
          "timestamp": "2025-08-30T01:23:03.949Z",
          "source": "ai_suggestion_for_lookup"
        }
      ],
      "name": "Printed Circuit Breaker Directory Label Sheet.",
      "price": 0,
      "currency": "USD"
    },
    {
      "description": "Stucco patch mix and bonding agent for repairing wall opening.",
      "category": "non_electrical_material",
      "quantity": 1,
      "unit": "bag",
      "attributes": {},
      "lookup_query_suggestion": "price stucco patch mix",
      "lookup_results": [
        {
          "type": "NON_ELECTRICAL_MATERIAL",
          "status": "non_electrical_material_no_price",
          "timestamp": "2025-08-30T01:23:03.949Z",
          "reason": "Non-electrical construction materials included for informational purposes",
          "source": "ai_category_classification"
        }
      ],
      "priceStatus": "not_applicable",
      "source": "Non-Electrical",
      "name": "Stucco patch mix and bonding agent for repairing wall opening.",
      "price": 0,
      "currency": "USD"
    },
    {
      "description": "Exterior-grade, paintable silicone caulk for weather sealing.",
      "category": "non_electrical_material",
      "quantity": 1,
      "unit": "tube",
      "attributes": {},
      "lookup_query_suggestion": "price exterior paintable silicone caulk",
      "lookup_results": [
        {
          "type": "NON_ELECTRICAL_MATERIAL",
          "status": "non_electrical_material_no_price",
          "timestamp": "2025-08-30T01:23:03.949Z",
          "reason": "Non-electrical construction materials included for informational purposes",
          "source": "ai_category_classification"
        }
      ],
      "priceStatus": "not_applicable",
      "source": "Non-Electrical",
      "name": "Exterior",
      "price": 0,
      "currency": "USD"
    }
  ],
  "detailed_scope_of_work": "## Scope of Work for Main Electrical Service Upgrade\n\nThis project outlines the complete replacement of the existing main electrical service panel and associated components to enhance safety, reliability, and compliance with the current National Electrical Code (NEC).\n\n### Phase 1: Preparation and Demolition\n\n- Coordinate a scheduled power outage with the local utility provider.\n- Obtain all required electrical permits from the city/county building department.\n- Safely de-energize the entire electrical system.\n- Carefully disconnect all existing branch circuits from the old panel.\n- Remove and dispose of the existing, deteriorated main service panel, meter socket, and service entrance wiring.\n\n### Phase 2: Installation of New Service Equipment\n\n- Prepare the exterior wall surface for the new panel, which may include minor stucco modification to ensure a secure and flush fit.\n- Install a new 200-Amp, 40-space, outdoor-rated (NEMA 3R) meter-main combination load center. This modern panel provides a new main breaker, meter socket, and ample space for all circuits in a single, weather-resistant enclosure.\n- Install a new 2.5-inch rigid PVC service mast and weatherhead.\n- Pull new 4/0 aluminum service entrance conductors from the weatherhead to the line-side lugs of the new panel.\n- Install a new code-compliant grounding electrode system. This includes driving two new 8-foot copper-clad ground rods and connecting them to the panel with a #4 AWG solid copper conductor.\n- Bond all necessary metallic systems, such as copper water piping and gas lines (if applicable), to the new grounding system.\n- Install an intersystem bonding bridge for safe connection of low-voltage systems (e.g., cable TV, telephone).\n\n### Phase 3: Circuit Reconnection and Finalization\n\n- Re-terminate all existing branch circuits into new, high-quality circuit breakers appropriately sized for each circuit.\n- Install modern Arc-Fault (AFCI) and/or Ground-Fault (GFCI) combination breakers on circuits for living areas, kitchen, laundry, and bathrooms as required by the current NEC to provide enhanced protection against electrical fires and shock.\n- Create and affix a new, clearly typed panel schedule directory inside the panel door for easy circuit identification.\n- Seal the service entrance conduit penetration with duct seal compound to prevent moisture and pest entry.\n- Weatherproof the exterior perimeter of the new panel with high-quality exterior sealant.\n- Coordinate with the utility provider for the re-installation of the electrical meter and re-energization of the service.\n\n### Phase 4: Testing and Commissioning\n\n- Once power is restored, conduct a full system test to ensure all circuits are functioning correctly and safely.\n- Verify proper voltage, polarity, and grounding at representative locations throughout the property.\n- Schedule and attend the final inspection with the local electrical inspector.\n- Provide a final walkthrough with the client to explain the new system and answer any questions.",
  "overall_summary": "The existing main electrical panel is a Federal Pacific Electric (FPE) model, which is widely recognized in the electrical industry as a significant fire hazard due to a high rate of breaker malfunction. Furthermore, the panel's exterior enclosure exhibits severe rust and corrosion, compromising its weather resistance and indicating potential internal damage from moisture intrusion. This condition presents a serious safety risk and does not meet modern electrical standards.\n\nOur recommendation is a complete main service upgrade. This involves the full replacement of the hazardous FPE panel with a new, 200-Amp Siemens meter-main combination load center. This modern, outdoor-rated unit will provide reliable overcurrent protection and is designed to withstand the elements. The project includes replacing the main service wires from the weatherhead to the panel and establishing a new, code-compliant grounding and bonding system. All existing circuits will be connected to new breakers, including the installation of AFCI/GFCI breakers as required by current code for enhanced safety. This comprehensive upgrade will resolve the immediate safety hazards, ensure compliance with the National Electrical Code, and provide a safe and reliable electrical service for many years.",
  "ai_confidence_score": 0.98
} | {"service":"workiz-api"}
2025-08-29 18:23:04 [[32m[32minfo[32m[39m] [general]: [AI Controller] 🔍 Items needing price lookup: 14 | {"service":"workiz-api"}
2025-08-29 18:23:04 [[32m[32minfo[32m[39m] [general]: [AI Controller] 🔍 Price lookup item 1: "Siemens MC4040B1200FEN 200A 40-Space 40-Circuit Outdoor Ringless Meter-Main Combination Load Center." (Category: electrical_material) | {"service":"workiz-api"}
2025-08-29 18:23:04 [[32m[32minfo[32m[39m] [general]: [AI Controller] 🔍 Price lookup item 2: "4/0-4/0-4/0-2/0 Aluminum SER Service Entrance Cable." (Category: electrical_material) | {"service":"workiz-api"}
2025-08-29 18:23:04 [[32m[32minfo[32m[39m] [general]: [AI Controller] 🔍 Price lookup item 3: "2.5 inch Schedule 80 PVC Conduit for service mast." (Category: electrical_material) | {"service":"workiz-api"}
2025-08-29 18:23:04 [[32m[32minfo[32m[39m] [general]: [AI Controller] 🔍 Price lookup item 4: "2.5 inch PVC Service Entrance Weatherhead." (Category: electrical_material) | {"service":"workiz-api"}
2025-08-29 18:23:04 [[32m[32minfo[32m[39m] [general]: [AI Controller] 🔍 Price lookup item 5: "20A Single-Pole AFCI/GFCI Combination Circuit Breaker." (Category: electrical_material) | {"service":"workiz-api"}
2025-08-29 18:23:04 [[32m[32minfo[32m[39m] [general]: [AI Controller] 🔍 Price lookup item 6: "20A Single-Pole Standard Circuit Breaker." (Category: electrical_material) | {"service":"workiz-api"}
2025-08-29 18:23:04 [[32m[32minfo[32m[39m] [general]: [AI Controller] 🔍 Price lookup item 7: "30A Double-Pole Standard Circuit Breaker." (Category: electrical_material) | {"service":"workiz-api"}
2025-08-29 18:23:04 [[32m[32minfo[32m[39m] [general]: [AI Controller] 🔍 Price lookup item 8: "50A Double-Pole Standard Circuit Breaker." (Category: electrical_material) | {"service":"workiz-api"}
2025-08-29 18:23:04 [[32m[32minfo[32m[39m] [general]: [AI Controller] 🔍 Price lookup item 9: "5/8 inch x 8 foot Copper-Clad Ground Rod." (Category: electrical_material) | {"service":"workiz-api"}
2025-08-29 18:23:04 [[32m[32minfo[32m[39m] [general]: [AI Controller] 🔍 Price lookup item 10: "#4 AWG Solid Bare Copper Grounding Electrode Conductor." (Category: electrical_material) | {"service":"workiz-api"}
2025-08-29 18:23:04 [[32m[32minfo[32m[39m] [general]: [AI Controller] 🔍 Price lookup item 11: "Acorn Ground Rod Clamps for 5/8 inch rod and #4 AWG wire." (Category: electrical_material) | {"service":"workiz-api"}
2025-08-29 18:23:04 [[32m[32minfo[32m[39m] [general]: [AI Controller] 🔍 Price lookup item 12: "Intersystem Bonding Bridge for low voltage systems." (Category: electrical_material) | {"service":"workiz-api"}
2025-08-29 18:23:04 [[32m[32minfo[32m[39m] [general]: [AI Controller] 🔍 Price lookup item 13: "Electrical Duct Seal Compound." (Category: electrical_material) | {"service":"workiz-api"}
2025-08-29 18:23:04 [[32m[32minfo[32m[39m] [general]: [AI Controller] 🔍 Price lookup item 14: "Printed Circuit Breaker Directory Label Sheet." (Category: electrical_material) | {"service":"workiz-api"}
2025-08-29 18:23:04 [[32m[32minfo[32m[39m] [general]: [AI Controller] 📋 14 items marked for deferred price lookup. Lookups will be triggered when quote is saved. | {"service":"workiz-api"}
2025-08-29 18:23:04 [[32m[32minfo[32m[39m] [general]: [AI Controller] Generating materialsIncluded from items as it was empty | {"service":"workiz-api"}
2025-08-29 18:23:04 [[34m[34mdebug[34m[39m] [ai-generation]: [AI Controller] Generated materialsIncluded: ## Materials Included

- Coordination with local utility provider for service disconnection and reconnection. (1 task)
- Electrical permit acquisition from the local authority having jurisdiction (AHJ). (1 permit)
- Siemens MC4040B1200FEN 200A 40-Space 40-Circuit Outdoor Ringless Meter-Main Combination Load Center. (1 pcs)
  - ampacity: 200A
  - type: Meter-Main Combo
  - rating: NEMA 3R Outdoor
- 4/0-4/0-4/0-2/0 Aluminum SER Service Entrance Cable. (20 feet)
  - material: Aluminum
  - size: 4/0-4/0-4/0-2/0
  - type: SER
- 2.5 inch Schedule 80 PVC Conduit for service mast. (10 feet)
  - conduit type: PVC Schedule 80
  - conduit size: 2.5 inch
- 2.5 inch PVC Service Entrance Weatherhead. (1 pcs)
  - conduit type: PVC
  - conduit size: 2.5 inch
- 20A Single-Pole AFCI/GFCI Combination Circuit Breaker. (6 pcs)
  - ampacity: 20A
  - type: AFCI/GFCI Combination
- 20A Single-Pole Standard Circuit Breaker. (8 pcs)
  - ampacity: 20A
  - type: Standard
- 30A Double-Pole Standard Circuit Breaker. (2 pcs)
  - ampacity: 30A
  - type: Standard 2-Pole
- 50A Double-Pole Standard Circuit Breaker. (1 pcs)
  - ampacity: 50A
  - type: Standard 2-Pole
- 5/8 inch x 8 foot Copper-Clad Ground Rod. (2 pcs)
  - material: Copper-Clad Steel
  - size: 5/8 in x 8 ft
- #4 AWG Solid Bare Copper Grounding Electrode Conductor. (30 feet)
  - material: Copper
  - size: 4 AWG
  - type: Solid Bare
- Acorn Ground Rod Clamps for 5/8 inch rod and #4 AWG wire. (2 pcs)
- Intersystem Bonding Bridge for low voltage systems. (1 pcs)
- Electrical Duct Seal Compound. (1 pcs)
- Printed Circuit Breaker Directory Label Sheet. (1 pcs)
- Stucco patch mix and bonding agent for repairing wall opening. (1 bag)
- Exterior-grade, paintable silicone caulk for weather sealing. (1 tube)
 | {"service":"workiz-api"}
2025-08-29 18:23:04 [[32m[32minfo[32m[39m] [ai-generation]: === AI Quote Generation Completed Successfully === | {"service":"workiz-api","status":"pending_price_lookups","hasQuestions":false,"questionCount":0,"itemCount":24,"itemsPendingPriceLookup":14,"aiConfidenceScore":0.98,"processingTimeMs":44840}
2025-08-29 18:23:04 [[34m[34mdebug[34m[39m] [ai-generation]: [AI Controller] Final response summary - Status: pending_price_lookups, Items: 24, Questions: 0, Confidence: 0.98 | {"service":"workiz-api"}
2025-08-29 18:23:04 [[32m[32minfo[32m[39m] [ai-generation]: === COMPREHENSIVE AI GENERATION LOGGING STOPPED === | {"service":"workiz-api"}
2025-08-29 18:23:29 [[32m[32minfo[32m[39m] [general]: Price lookup request for 25 items from user 68ae4a5c8cd30c966fd90ee1 | {"service":"workiz-api"}
2025-08-29 18:23:29 [[32m[32minfo[32m[39m] [general]: [GenericPriceLookup] Getting estimated price for: Coordination with local utility provider for service disconnection and reconnection. | {"service":"workiz-api"}
2025-08-29 18:23:29 [[32m[32minfo[32m[39m] [general]: [GenericPriceLookup] Using industry average for Coordination with local utility provider for service disconnection and reconnection.: $75 | {"service":"workiz-api"}
2025-08-29 18:23:29 [[32m[32minfo[32m[39m] [general]: [GenericPriceLookup] Getting estimated price for: Electrical permit acquisition from the local authority having jurisdiction (AHJ). | {"service":"workiz-api"}
2025-08-29 18:23:29 [[32m[32minfo[32m[39m] [general]: [GenericPriceLookup] Using industry average for Electrical permit acquisition from the local authority having jurisdiction (AHJ).: $150 | {"service":"workiz-api"}
2025-08-29 18:23:29 [[32m[32minfo[32m[39m] [general]: [GenericPriceLookup] Getting estimated price for: Demolition | {"service":"workiz-api"}
2025-08-29 18:23:29 [[32m[32minfo[32m[39m] [general]: [GenericPriceLookup] No industry average found for Demolition, trying web search fallback | {"service":"workiz-api"}
2025-08-29 18:23:29 [[32m[32minfo[32m[39m] [general]: [GenericPriceLookup] Searching web for pricing: Demolition | {"service":"workiz-api"}
2025-08-29 18:23:29 [[32m[32minfo[32m[39m] [general]: [WebSearchService] Searching for pricing: Demolition price cost estimate | {"service":"workiz-api"}
2025-08-29 18:23:29 [[32m[32minfo[32m[39m] [general]: [WebSearchTool] Searching for: Demolition price cost estimate | {"service":"workiz-api"}
2025-08-29 18:23:29 [[32m[32minfo[32m[39m] [general]: [GenericPriceLookup] Getting estimated price for: Installation of new 200A meter | {"service":"workiz-api"}
2025-08-29 18:23:29 [[32m[32minfo[32m[39m] [general]: [GenericPriceLookup] No industry average found for Installation of new 200A meter, trying web search fallback | {"service":"workiz-api"}
2025-08-29 18:23:29 [[32m[32minfo[32m[39m] [general]: [GenericPriceLookup] Searching web for pricing: Installation of new 200A meter | {"service":"workiz-api"}
2025-08-29 18:23:29 [[32m[32minfo[32m[39m] [general]: [WebSearchService] Searching for pricing: Installation of new 200A meter price cost estimate | {"service":"workiz-api"}
2025-08-29 18:23:29 [[32m[32minfo[32m[39m] [general]: [WebSearchTool] Searching for: Installation of new 200A meter price cost estimate | {"service":"workiz-api"}
2025-08-29 18:23:29 [[32m[32minfo[32m[39m] [general]: [WebSearchService] Extracted 0 pricing results from 1 search results | {"service":"workiz-api"}
2025-08-29 18:23:29 [[32m[32minfo[32m[39m] [general]: [WebSearchService] Extracted 0 pricing results from 1 search results | {"service":"workiz-api"}
2025-08-29 18:23:29 [[32m[32minfo[32m[39m] [general]: [GenericPriceLookup] Getting estimated price for: Installation of new service entrance mast | {"service":"workiz-api"}
2025-08-29 18:23:29 [[32m[32minfo[32m[39m] [general]: [GenericPriceLookup] No industry average found for Installation of new service entrance mast, trying web search fallback | {"service":"workiz-api"}
2025-08-29 18:23:29 [[32m[32minfo[32m[39m] [general]: [GenericPriceLookup] Searching web for pricing: Installation of new service entrance mast | {"service":"workiz-api"}
2025-08-29 18:23:29 [[32m[32minfo[32m[39m] [general]: [WebSearchService] Searching for pricing: Installation of new service entrance mast price cost estimate | {"service":"workiz-api"}
2025-08-29 18:23:29 [[32m[32minfo[32m[39m] [general]: [WebSearchTool] Searching for: Installation of new service entrance mast price cost estimate | {"service":"workiz-api"}
2025-08-29 18:23:29 [[32m[32minfo[32m[39m] [general]: [GenericPriceLookup] Getting estimated price for: Installation of new grounding electrode system | {"service":"workiz-api"}
2025-08-29 18:23:29 [[32m[32minfo[32m[39m] [general]: [GenericPriceLookup] No industry average found for Installation of new grounding electrode system, trying web search fallback | {"service":"workiz-api"}
2025-08-29 18:23:29 [[32m[32minfo[32m[39m] [general]: [GenericPriceLookup] Searching web for pricing: Installation of new grounding electrode system | {"service":"workiz-api"}
2025-08-29 18:23:29 [[32m[32minfo[32m[39m] [general]: [WebSearchService] Searching for pricing: Installation of new grounding electrode system price cost estimate | {"service":"workiz-api"}
2025-08-29 18:23:29 [[32m[32minfo[32m[39m] [general]: [WebSearchTool] Searching for: Installation of new grounding electrode system price cost estimate | {"service":"workiz-api"}
2025-08-29 18:23:29 [[32m[32minfo[32m[39m] [general]: [GenericPriceLookup] Getting estimated price for: Termination of existing branch circuits into new circuit breakers. Includes circuit... | {"service":"workiz-api"}
2025-08-29 18:23:29 [[32m[32minfo[32m[39m] [general]: [GenericPriceLookup] No industry average found for Termination of existing branch circuits into new circuit breakers. Includes circuit..., trying web search fallback | {"service":"workiz-api"}
2025-08-29 18:23:29 [[32m[32minfo[32m[39m] [general]: [GenericPriceLookup] Searching web for pricing: Termination of existing branch circuits into new circuit breakers. Includes circuit... | {"service":"workiz-api"}
2025-08-29 18:23:29 [[32m[32minfo[32m[39m] [general]: [WebSearchService] Searching for pricing: Termination of existing branch circuits into new circuit breakers. Includes circuit... price cost estimate | {"service":"workiz-api"}
2025-08-29 18:23:29 [[32m[32minfo[32m[39m] [general]: [WebSearchTool] Searching for: Termination of existing branch circuits into new circuit breakers. Includes circuit... price cost estimate | {"service":"workiz-api"}
2025-08-29 18:23:29 [[33m[33mwarn[33m[39m] [general]: [GenericPriceLookup] No pricing found for Demolition using any method | {"service":"workiz-api"}
2025-08-29 18:23:29 [[33m[33mwarn[33m[39m] [general]: [GenericPriceLookup] No pricing found for Installation of new 200A meter using any method | {"service":"workiz-api"}
2025-08-29 18:23:29 [[32m[32minfo[32m[39m] [general]: [WebSearchService] Extracted 0 pricing results from 1 search results | {"service":"workiz-api"}
2025-08-29 18:23:29 [[32m[32minfo[32m[39m] [general]: [WebSearchService] Extracted 0 pricing results from 1 search results | {"service":"workiz-api"}
2025-08-29 18:23:29 [[32m[32minfo[32m[39m] [general]: [WebSearchService] Extracted 0 pricing results from 1 search results | {"service":"workiz-api"}
2025-08-29 18:23:29 [[33m[33mwarn[33m[39m] [general]: [GenericPriceLookup] No pricing found for Installation of new service entrance mast using any method | {"service":"workiz-api"}
2025-08-29 18:23:29 [[33m[33mwarn[33m[39m] [general]: [GenericPriceLookup] No pricing found for Installation of new grounding electrode system using any method | {"service":"workiz-api"}
2025-08-29 18:23:29 [[33m[33mwarn[33m[39m] [general]: [GenericPriceLookup] No pricing found for Termination of existing branch circuits into new circuit breakers. Includes circuit... using any method | {"service":"workiz-api"}
2025-08-29 18:23:29 [[32m[32minfo[32m[39m] [general]: [GenericPriceLookup] Getting estimated price for: Final system testing | {"service":"workiz-api"}
2025-08-29 18:23:29 [[32m[32minfo[32m[39m] [general]: [GenericPriceLookup] No industry average found for Final system testing, trying web search fallback | {"service":"workiz-api"}
2025-08-29 18:23:29 [[32m[32minfo[32m[39m] [general]: [GenericPriceLookup] Searching web for pricing: Final system testing | {"service":"workiz-api"}
2025-08-29 18:23:29 [[32m[32minfo[32m[39m] [general]: [WebSearchService] Searching for pricing: Final system testing price cost estimate | {"service":"workiz-api"}
2025-08-29 18:23:29 [[32m[32minfo[32m[39m] [general]: [WebSearchTool] Searching for: Final system testing price cost estimate | {"service":"workiz-api"}
2025-08-29 18:23:29 [[32m[32minfo[32m[39m] [general]: [WebSearchService] Extracted 0 pricing results from 1 search results | {"service":"workiz-api"}
2025-08-29 18:23:29 [[34m[34mdebug[34m[39m] [general]: [ScraperService] searchMaterials (Enhanced with Query Transformation) called with query: "price Siemens MC4040B1200FEN 200A meter main", options: {"skipCache":false,"limit":1,"timeout":20000} | {"service":"workiz-api"}
2025-08-29 18:23:29 [[34m[34mdebug[34m[39m] [general]: [QueryTransformer] Transforming query: "price Siemens MC4040B1200FEN 200A meter main" | {"service":"workiz-api"}
2025-08-29 18:23:29 [[34m[34mdebug[34m[39m] [general]: [QueryTransformer] Generated 4 variations | {"service":"workiz-api","brand":"siemens","type":"meter","confidence":1}
2025-08-29 18:23:29 [[32m[32minfo[32m[39m] [general]: [ScraperService] Query transformation generated 4 variations | {"service":"workiz-api","originalQuery":"price Siemens MC4040B1200FEN 200A meter main","detectedBrand":"siemens","detectedType":"meter","confidence":1}
2025-08-29 18:23:29 [[32m[32minfo[32m[39m] [general]: [ScraperService Fallback] Attempting primary search: Home Depot (ID: 682bdb0e477e692811d861e0) | {"service":"workiz-api"}
2025-08-29 18:23:29 [[32m[32minfo[32m[39m] [general]: [ScraperService] Trying primary query: "price Siemens MC4040B1200FEN 200A meter main" | {"service":"workiz-api"}
2025-08-29 18:23:29 [[34m[34mdebug[34m[39m] [general]: [ScraperService] Calling searchByDescription on Home Depot for query: "price Siemens MC4040B1200FEN 200A meter main" | {"service":"workiz-api"}
2025-08-29 18:23:29 [[34m[34mdebug[34m[39m] [general]: [Cache Base] Using sanitized search cache key: crawl4ai:home depot:search:price_Siemens_MC4040B1200FEN_200A_meter_main | {"service":"workiz-api"}
2025-08-29 18:23:29 [[33m[33mwarn[33m[39m] [general]: [GenericPriceLookup] No pricing found for Final system testing using any method | {"service":"workiz-api"}
2025-08-29 18:23:29 [[34m[34mdebug[34m[39m] [general]: [ScraperService] searchMaterials (Enhanced with Query Transformation) called with query: "price 20 ft 4/0 aluminum SER cable", options: {"skipCache":false,"limit":1,"timeout":20000} | {"service":"workiz-api"}
2025-08-29 18:23:29 [[34m[34mdebug[34m[39m] [general]: [QueryTransformer] Transforming query: "price 20 ft 4/0 aluminum SER cable" | {"service":"workiz-api"}
2025-08-29 18:23:29 [[34m[34mdebug[34m[39m] [general]: [QueryTransformer] Generated 1 variations | {"service":"workiz-api","brand":null,"type":"amp","confidence":0.7}
2025-08-29 18:23:29 [[32m[32minfo[32m[39m] [general]: [ScraperService] Query transformation generated 1 variations | {"service":"workiz-api","originalQuery":"price 20 ft 4/0 aluminum SER cable","detectedBrand":null,"detectedType":"amp","confidence":0.7}
2025-08-29 18:23:29 [[32m[32minfo[32m[39m] [general]: [ScraperService Fallback] Attempting primary search: Home Depot (ID: 682bdb0e477e692811d861e0) | {"service":"workiz-api"}
2025-08-29 18:23:29 [[32m[32minfo[32m[39m] [general]: [ScraperService] Trying primary query: "price 20 ft 4/0 aluminum SER cable" | {"service":"workiz-api"}
2025-08-29 18:23:29 [[34m[34mdebug[34m[39m] [general]: [ScraperService] searchMaterials (Enhanced with Query Transformation) called with query: "price 10 ft 2.5 inch schedule 80 PVC conduit", options: {"skipCache":false,"limit":1,"timeout":20000} | {"service":"workiz-api"}
2025-08-29 18:23:29 [[34m[34mdebug[34m[39m] [general]: [QueryTransformer] Transforming query: "price 10 ft 2.5 inch schedule 80 PVC conduit" | {"service":"workiz-api"}
2025-08-29 18:23:29 [[34m[34mdebug[34m[39m] [general]: [QueryTransformer] Generated 2 variations | {"service":"workiz-api","brand":"eaton","type":"volt","confidence":1}
2025-08-29 18:23:29 [[32m[32minfo[32m[39m] [general]: [ScraperService] Query transformation generated 2 variations | {"service":"workiz-api","originalQuery":"price 10 ft 2.5 inch schedule 80 PVC conduit","detectedBrand":"eaton","detectedType":"volt","confidence":1}
2025-08-29 18:23:29 [[32m[32minfo[32m[39m] [general]: [ScraperService Fallback] Attempting primary search: Home Depot (ID: 682bdb0e477e692811d861e0) | {"service":"workiz-api"}
2025-08-29 18:23:29 [[32m[32minfo[32m[39m] [general]: [ScraperService] Trying primary query: "price 10 ft 2.5 inch schedule 80 PVC conduit" | {"service":"workiz-api"}
2025-08-29 18:23:29 [[34m[34mdebug[34m[39m] [general]: [ScraperService] searchMaterials (Enhanced with Query Transformation) called with query: "price 2.5 inch PVC weatherhead", options: {"skipCache":false,"limit":1,"timeout":20000} | {"service":"workiz-api"}
2025-08-29 18:23:29 [[34m[34mdebug[34m[39m] [general]: [QueryTransformer] Transforming query: "price 2.5 inch PVC weatherhead" | {"service":"workiz-api"}
2025-08-29 18:23:29 [[34m[34mdebug[34m[39m] [general]: [QueryTransformer] Generated 2 variations | {"service":"workiz-api","brand":"eaton","type":"amp","confidence":0.8}
2025-08-29 18:23:29 [[32m[32minfo[32m[39m] [general]: [ScraperService] Query transformation generated 2 variations | {"service":"workiz-api","originalQuery":"price 2.5 inch PVC weatherhead","detectedBrand":"eaton","detectedType":"amp","confidence":0.8}
2025-08-29 18:23:29 [[32m[32minfo[32m[39m] [general]: [ScraperService Fallback] Attempting primary search: Home Depot (ID: 682bdb0e477e692811d861e0) | {"service":"workiz-api"}
2025-08-29 18:23:29 [[32m[32minfo[32m[39m] [general]: [ScraperService] Trying primary query: "price 2.5 inch PVC weatherhead" | {"service":"workiz-api"}
2025-08-29 18:23:29 [[34m[34mdebug[34m[39m] [general]: [ScraperService] Calling searchByDescription on Home Depot for query: "price 20 ft 4/0 aluminum SER cable" | {"service":"workiz-api"}
2025-08-29 18:23:29 [[34m[34mdebug[34m[39m] [general]: [Cache Base] Using sanitized search cache key: crawl4ai:home depot:search:price_20_ft_4/0_aluminum_SER_cable | {"service":"workiz-api"}
2025-08-29 18:23:29 [[34m[34mdebug[34m[39m] [general]: [ScraperService] Calling searchByDescription on Home Depot for query: "price 10 ft 2.5 inch schedule 80 PVC conduit" | {"service":"workiz-api"}
2025-08-29 18:23:29 [[34m[34mdebug[34m[39m] [general]: [Cache Base] Using sanitized search cache key: crawl4ai:home depot:search:price_10_ft_2.5_inch_schedule_80_PVC_conduit | {"service":"workiz-api"}
2025-08-29 18:23:29 [[34m[34mdebug[34m[39m] [general]: [ScraperService] Calling searchByDescription on Home Depot for query: "price 2.5 inch PVC weatherhead" | {"service":"workiz-api"}
2025-08-29 18:23:29 [[34m[34mdebug[34m[39m] [general]: [Cache Base] Using sanitized search cache key: crawl4ai:home depot:search:price_2.5_inch_PVC_weatherhead | {"service":"workiz-api"}
2025-08-29 18:23:29 [[34m[34mdebug[34m[39m] [general]: [ScraperService] searchMaterials (Enhanced with Query Transformation) called with query: "price Siemens 20A dual function AFCI/GFCI breaker", options: {"skipCache":false,"limit":1,"timeout":20000} | {"service":"workiz-api"}
2025-08-29 18:23:29 [[34m[34mdebug[34m[39m] [general]: [QueryTransformer] Transforming query: "price Siemens 20A dual function AFCI/GFCI breaker" | {"service":"workiz-api"}
2025-08-29 18:23:29 [[34m[34mdebug[34m[39m] [general]: [QueryTransformer] Generated 3 variations | {"service":"workiz-api","brand":"siemens","type":"breaker","confidence":1}
2025-08-29 18:23:29 [[32m[32minfo[32m[39m] [general]: [ScraperService] Query transformation generated 3 variations | {"service":"workiz-api","originalQuery":"price Siemens 20A dual function AFCI/GFCI breaker","detectedBrand":"siemens","detectedType":"breaker","confidence":1}
2025-08-29 18:23:29 [[32m[32minfo[32m[39m] [general]: [ScraperService Fallback] Attempting primary search: Home Depot (ID: 682bdb0e477e692811d861e0) | {"service":"workiz-api"}
2025-08-29 18:23:29 [[32m[32minfo[32m[39m] [general]: [ScraperService] Trying primary query: "price Siemens 20A dual function AFCI/GFCI breaker" | {"service":"workiz-api"}
2025-08-29 18:23:29 [[34m[34mdebug[34m[39m] [general]: [ScraperService] Calling searchByDescription on Home Depot for query: "price Siemens 20A dual function AFCI/GFCI breaker" | {"service":"workiz-api"}
2025-08-29 18:23:29 [[34m[34mdebug[34m[39m] [general]: [Cache Base] Using sanitized search cache key: crawl4ai:home depot:search:price_Siemens_20A_dual_function_AFCI/GFCI_breaker | {"service":"workiz-api"}
2025-08-29 18:23:29 [[32m[32minfo[32m[39m] [general]: Connected to Redis server | {"service":"workiz-api"}
2025-08-29 18:23:29 [[32m[32minfo[32m[39m] [general]: [Home Depot Scraper] Searching for "price Siemens MC4040B1200FEN 200A meter main" | {"service":"workiz-api"}
2025-08-29 18:23:29 [[32m[32minfo[32m[39m] [general]: [HD Crawl4AI Scraper] Searching Home Depot for "price Siemens MC4040B1200FEN 200A meter main" | {"service":"workiz-api"}
2025-08-29 18:23:29 [[34m[34mdebug[34m[39m] [general]: [Home Depot Scraper] Performing crawl for URL: https://www.homedepot.com/s/price%20Siemens%20MC4040B1200FEN%20200A%20meter%20main | {"service":"workiz-api"}
2025-08-29 18:23:29 [[32m[32minfo[32m[39m] [general]: [Home Depot Scraper] Searching for "price 20 ft 4/0 aluminum SER cable" | {"service":"workiz-api"}
2025-08-29 18:23:29 [[32m[32minfo[32m[39m] [general]: [HD Crawl4AI Scraper] Searching Home Depot for "price 20 ft 4/0 aluminum SER cable" | {"service":"workiz-api"}
2025-08-29 18:23:29 [[34m[34mdebug[34m[39m] [general]: [Home Depot Scraper] Performing crawl for URL: https://www.homedepot.com/s/price%2020%20ft%204%2F0%20aluminum%20SER%20cable | {"service":"workiz-api"}
2025-08-29 18:23:29 [[32m[32minfo[32m[39m] [general]: [Home Depot Scraper] Searching for "price 10 ft 2.5 inch schedule 80 PVC conduit" | {"service":"workiz-api"}
2025-08-29 18:23:29 [[32m[32minfo[32m[39m] [general]: [HD Crawl4AI Scraper] Searching Home Depot for "price 10 ft 2.5 inch schedule 80 PVC conduit" | {"service":"workiz-api"}
2025-08-29 18:23:29 [[34m[34mdebug[34m[39m] [general]: [Home Depot Scraper] Performing crawl for URL: https://www.homedepot.com/s/price%2010%20ft%202.5%20inch%20schedule%2080%20PVC%20conduit | {"service":"workiz-api"}
2025-08-29 18:23:29 [[32m[32minfo[32m[39m] [general]: [Home Depot Scraper] Searching for "price 2.5 inch PVC weatherhead" | {"service":"workiz-api"}
2025-08-29 18:23:29 [[32m[32minfo[32m[39m] [general]: [HD Crawl4AI Scraper] Searching Home Depot for "price 2.5 inch PVC weatherhead" | {"service":"workiz-api"}
2025-08-29 18:23:29 [[34m[34mdebug[34m[39m] [general]: [Home Depot Scraper] Performing crawl for URL: https://www.homedepot.com/s/price%202.5%20inch%20PVC%20weatherhead | {"service":"workiz-api"}
2025-08-29 18:23:29 [[32m[32minfo[32m[39m] [general]: [Home Depot Scraper] Searching for "price Siemens 20A dual function AFCI/GFCI breaker" | {"service":"workiz-api"}
2025-08-29 18:23:29 [[32m[32minfo[32m[39m] [general]: [HD Crawl4AI Scraper] Searching Home Depot for "price Siemens 20A dual function AFCI/GFCI breaker" | {"service":"workiz-api"}
2025-08-29 18:23:29 [[34m[34mdebug[34m[39m] [general]: [Home Depot Scraper] Performing crawl for URL: https://www.homedepot.com/s/price%20Siemens%2020A%20dual%20function%20AFCI%2FGFCI%20breaker | {"service":"workiz-api"}
2025-08-29 18:23:49 [[34m[34mdebug[34m[39m] [general]: Crawl4AI process completed in 19929ms with code 0 | {"service":"workiz-api"}
2025-08-29 18:23:49 [[33m[33mwarn[33m[39m] [general]: [HD Crawl4AI Scraper] No products extracted for "price 2.5 inch PVC weatherhead" | {"service":"workiz-api"}
2025-08-29 18:23:49 [[34m[34mdebug[34m[39m] [general]: [ScraperService] Received 0 results from Home Depot | {"service":"workiz-api"}
2025-08-29 18:23:49 [[32m[32minfo[32m[39m] [general]: [ScraperService] Trying variation (brand_type_specs, confidence: 0.9): "eaton amp" | {"service":"workiz-api"}
2025-08-29 18:23:49 [[34m[34mdebug[34m[39m] [general]: [ScraperService] Calling searchByDescription on Home Depot for query: "eaton amp" | {"service":"workiz-api"}
2025-08-29 18:23:49 [[34m[34mdebug[34m[39m] [general]: [Cache Base] Using sanitized search cache key: crawl4ai:home depot:search:eaton_amp | {"service":"workiz-api"}
2025-08-29 18:23:49 [[33m[33mwarn[33m[39m] [general]: Crawl4AI process gracefully terminated after 20000ms for URL: https://www.homedepot.com/s/price%20Siemens%20MC4040B1200FEN%20200A%20meter%20main | {"service":"workiz-api"}
2025-08-29 18:23:49 [[31m[31merror[31m[39m] [general]: Crawl4AI process timed out after 20000ms for URL: https://www.homedepot.com/s/price%20Siemens%20MC4040B1200FEN%20200A%20meter%20main | {"service":"workiz-api"}
2025-08-29 18:23:49 [[31m[31merror[31m[39m] [general]: [HD Crawl4AI Scraper] Error searching for "price Siemens MC4040B1200FEN 200A meter main": Crawl operation timed out after 20000ms | {"service":"workiz-api","stack":"Error: Crawl operation timed out after 20000ms\n    at Timeout._onTimeout (C:\\Projects\\workiz\\backend\\scrapers\\crawl4ai\\crawl4ai-service.js:709:18)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)"}
2025-08-29 18:23:49 [[31m[31merror[31m[39m] [general]: [Home Depot Scraper] Error searching for "price Siemens MC4040B1200FEN 200A meter main": Crawl operation timed out after 20000ms | {"service":"workiz-api","stack":"Error: Crawl operation timed out after 20000ms\n    at Timeout._onTimeout (C:\\Projects\\workiz\\backend\\scrapers\\crawl4ai\\crawl4ai-service.js:709:18)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)"}
2025-08-29 18:23:49 [[31m[31merror[31m[39m] [general]: [ScraperService] Error searching in Home Depot for query "price Siemens MC4040B1200FEN 200A meter main": Crawl operation timed out after 20000ms | {"service":"workiz-api","stack":"Error: Crawl operation timed out after 20000ms\n    at Timeout._onTimeout (C:\\Projects\\workiz\\backend\\scrapers\\crawl4ai\\crawl4ai-service.js:709:18)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)"}
2025-08-29 18:23:49 [[31m[31merror[31m[39m] [general]: [ScraperService Fallback] Error searching primary source (Home Depot ID: 682bdb0e477e692811d861e0): Crawl operation timed out after 20000ms | {"service":"workiz-api","stack":"Error: Crawl operation timed out after 20000ms\n    at Timeout._onTimeout (C:\\Projects\\workiz\\backend\\scrapers\\crawl4ai\\crawl4ai-service.js:709:18)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)"}
2025-08-29 18:23:49 [[33m[33mwarn[33m[39m] [general]: [ScraperService Fallback] Primary search failed or yielded no results. Attempting fallback: Platt (ID: 682bdb1305cc8e619ad861e0) | {"service":"workiz-api"}
2025-08-29 18:23:49 [[32m[32minfo[32m[39m] [general]: [ScraperService] Trying primary query: "price Siemens MC4040B1200FEN 200A meter main" | {"service":"workiz-api"}
2025-08-29 18:23:49 [[34m[34mdebug[34m[39m] [general]: [ScraperService] Calling searchByDescription on Platt Electric Supply for query: "price Siemens MC4040B1200FEN 200A meter main" | {"service":"workiz-api"}
2025-08-29 18:23:49 [[34m[34mdebug[34m[39m] [general]: [Cache Base] Using sanitized search cache key: crawl4ai:platt electric supply:search:price_Siemens_MC4040B1200FEN_200A_meter_main | {"service":"workiz-api"}
2025-08-29 18:23:49 [[33m[33mwarn[33m[39m] [general]: Crawl4AI process gracefully terminated after 20000ms for URL: https://www.homedepot.com/s/price%2020%20ft%204%2F0%20aluminum%20SER%20cable | {"service":"workiz-api"}
2025-08-29 18:23:49 [[31m[31merror[31m[39m] [general]: Crawl4AI process timed out after 20000ms for URL: https://www.homedepot.com/s/price%2020%20ft%204%2F0%20aluminum%20SER%20cable | {"service":"workiz-api"}
2025-08-29 18:23:49 [[31m[31merror[31m[39m] [general]: [HD Crawl4AI Scraper] Error searching for "price 20 ft 4/0 aluminum SER cable": Crawl operation timed out after 20000ms | {"service":"workiz-api","stack":"Error: Crawl operation timed out after 20000ms\n    at Timeout._onTimeout (C:\\Projects\\workiz\\backend\\scrapers\\crawl4ai\\crawl4ai-service.js:709:18)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)"}
2025-08-29 18:23:49 [[31m[31merror[31m[39m] [general]: [Home Depot Scraper] Error searching for "price 20 ft 4/0 aluminum SER cable": Crawl operation timed out after 20000ms | {"service":"workiz-api","stack":"Error: Crawl operation timed out after 20000ms\n    at Timeout._onTimeout (C:\\Projects\\workiz\\backend\\scrapers\\crawl4ai\\crawl4ai-service.js:709:18)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)"}
2025-08-29 18:23:49 [[31m[31merror[31m[39m] [general]: [ScraperService] Error searching in Home Depot for query "price 20 ft 4/0 aluminum SER cable": Crawl operation timed out after 20000ms | {"service":"workiz-api","stack":"Error: Crawl operation timed out after 20000ms\n    at Timeout._onTimeout (C:\\Projects\\workiz\\backend\\scrapers\\crawl4ai\\crawl4ai-service.js:709:18)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)"}
2025-08-29 18:23:49 [[31m[31merror[31m[39m] [general]: [ScraperService Fallback] Error searching primary source (Home Depot ID: 682bdb0e477e692811d861e0): Crawl operation timed out after 20000ms | {"service":"workiz-api","stack":"Error: Crawl operation timed out after 20000ms\n    at Timeout._onTimeout (C:\\Projects\\workiz\\backend\\scrapers\\crawl4ai\\crawl4ai-service.js:709:18)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)"}
2025-08-29 18:23:49 [[33m[33mwarn[33m[39m] [general]: [ScraperService Fallback] Primary search failed or yielded no results. Attempting fallback: Platt (ID: 682bdb1305cc8e619ad861e0) | {"service":"workiz-api"}
2025-08-29 18:23:49 [[32m[32minfo[32m[39m] [general]: [ScraperService] Trying primary query: "price 20 ft 4/0 aluminum SER cable" | {"service":"workiz-api"}
2025-08-29 18:23:50 [[34m[34mdebug[34m[39m] [general]: [ScraperService] Calling searchByDescription on Platt Electric Supply for query: "price 20 ft 4/0 aluminum SER cable" | {"service":"workiz-api"}
2025-08-29 18:23:50 [[34m[34mdebug[34m[39m] [general]: [Cache Base] Using sanitized search cache key: crawl4ai:platt electric supply:search:price_20_ft_4/0_aluminum_SER_cable | {"service":"workiz-api"}
2025-08-29 18:23:50 [[33m[33mwarn[33m[39m] [general]: Crawl4AI process gracefully terminated after 20000ms for URL: https://www.homedepot.com/s/price%2010%20ft%202.5%20inch%20schedule%2080%20PVC%20conduit | {"service":"workiz-api"}
2025-08-29 18:23:50 [[31m[31merror[31m[39m] [general]: Crawl4AI process timed out after 20000ms for URL: https://www.homedepot.com/s/price%2010%20ft%202.5%20inch%20schedule%2080%20PVC%20conduit | {"service":"workiz-api"}
2025-08-29 18:23:50 [[31m[31merror[31m[39m] [general]: [HD Crawl4AI Scraper] Error searching for "price 10 ft 2.5 inch schedule 80 PVC conduit": Crawl operation timed out after 20000ms | {"service":"workiz-api","stack":"Error: Crawl operation timed out after 20000ms\n    at Timeout._onTimeout (C:\\Projects\\workiz\\backend\\scrapers\\crawl4ai\\crawl4ai-service.js:709:18)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)"}
2025-08-29 18:23:50 [[31m[31merror[31m[39m] [general]: [Home Depot Scraper] Error searching for "price 10 ft 2.5 inch schedule 80 PVC conduit": Crawl operation timed out after 20000ms | {"service":"workiz-api","stack":"Error: Crawl operation timed out after 20000ms\n    at Timeout._onTimeout (C:\\Projects\\workiz\\backend\\scrapers\\crawl4ai\\crawl4ai-service.js:709:18)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)"}
2025-08-29 18:23:50 [[31m[31merror[31m[39m] [general]: [ScraperService] Error searching in Home Depot for query "price 10 ft 2.5 inch schedule 80 PVC conduit": Crawl operation timed out after 20000ms | {"service":"workiz-api","stack":"Error: Crawl operation timed out after 20000ms\n    at Timeout._onTimeout (C:\\Projects\\workiz\\backend\\scrapers\\crawl4ai\\crawl4ai-service.js:709:18)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)"}
2025-08-29 18:23:50 [[31m[31merror[31m[39m] [general]: [ScraperService Fallback] Error searching primary source (Home Depot ID: 682bdb0e477e692811d861e0): Crawl operation timed out after 20000ms | {"service":"workiz-api","stack":"Error: Crawl operation timed out after 20000ms\n    at Timeout._onTimeout (C:\\Projects\\workiz\\backend\\scrapers\\crawl4ai\\crawl4ai-service.js:709:18)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)"}
2025-08-29 18:23:50 [[33m[33mwarn[33m[39m] [general]: [ScraperService Fallback] Primary search failed or yielded no results. Attempting fallback: Platt (ID: 682bdb1305cc8e619ad861e0) | {"service":"workiz-api"}
2025-08-29 18:23:50 [[32m[32minfo[32m[39m] [general]: [ScraperService] Trying primary query: "price 10 ft 2.5 inch schedule 80 PVC conduit" | {"service":"workiz-api"}
2025-08-29 18:23:50 [[34m[34mdebug[34m[39m] [general]: [ScraperService] Calling searchByDescription on Platt Electric Supply for query: "price 10 ft 2.5 inch schedule 80 PVC conduit" | {"service":"workiz-api"}
2025-08-29 18:23:50 [[34m[34mdebug[34m[39m] [general]: [Cache Base] Using sanitized search cache key: crawl4ai:platt electric supply:search:price_10_ft_2.5_inch_schedule_80_PVC_conduit | {"service":"workiz-api"}
2025-08-29 18:23:50 [[33m[33mwarn[33m[39m] [general]: Crawl4AI process gracefully terminated after 20000ms for URL: https://www.homedepot.com/s/price%20Siemens%2020A%20dual%20function%20AFCI%2FGFCI%20breaker | {"service":"workiz-api"}
2025-08-29 18:23:50 [[31m[31merror[31m[39m] [general]: Crawl4AI process timed out after 20000ms for URL: https://www.homedepot.com/s/price%20Siemens%2020A%20dual%20function%20AFCI%2FGFCI%20breaker | {"service":"workiz-api"}
2025-08-29 18:23:50 [[31m[31merror[31m[39m] [general]: [HD Crawl4AI Scraper] Error searching for "price Siemens 20A dual function AFCI/GFCI breaker": Crawl operation timed out after 20000ms | {"service":"workiz-api","stack":"Error: Crawl operation timed out after 20000ms\n    at Timeout._onTimeout (C:\\Projects\\workiz\\backend\\scrapers\\crawl4ai\\crawl4ai-service.js:709:18)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)"}
2025-08-29 18:23:50 [[31m[31merror[31m[39m] [general]: [Home Depot Scraper] Error searching for "price Siemens 20A dual function AFCI/GFCI breaker": Crawl operation timed out after 20000ms | {"service":"workiz-api","stack":"Error: Crawl operation timed out after 20000ms\n    at Timeout._onTimeout (C:\\Projects\\workiz\\backend\\scrapers\\crawl4ai\\crawl4ai-service.js:709:18)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)"}
2025-08-29 18:23:50 [[31m[31merror[31m[39m] [general]: [ScraperService] Error searching in Home Depot for query "price Siemens 20A dual function AFCI/GFCI breaker": Crawl operation timed out after 20000ms | {"service":"workiz-api","stack":"Error: Crawl operation timed out after 20000ms\n    at Timeout._onTimeout (C:\\Projects\\workiz\\backend\\scrapers\\crawl4ai\\crawl4ai-service.js:709:18)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)"}
2025-08-29 18:23:50 [[31m[31merror[31m[39m] [general]: [ScraperService Fallback] Error searching primary source (Home Depot ID: 682bdb0e477e692811d861e0): Crawl operation timed out after 20000ms | {"service":"workiz-api","stack":"Error: Crawl operation timed out after 20000ms\n    at Timeout._onTimeout (C:\\Projects\\workiz\\backend\\scrapers\\crawl4ai\\crawl4ai-service.js:709:18)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)"}
2025-08-29 18:23:50 [[33m[33mwarn[33m[39m] [general]: [ScraperService Fallback] Primary search failed or yielded no results. Attempting fallback: Platt (ID: 682bdb1305cc8e619ad861e0) | {"service":"workiz-api"}
2025-08-29 18:23:50 [[32m[32minfo[32m[39m] [general]: [ScraperService] Trying primary query: "price Siemens 20A dual function AFCI/GFCI breaker" | {"service":"workiz-api"}
2025-08-29 18:23:50 [[34m[34mdebug[34m[39m] [general]: [ScraperService] Calling searchByDescription on Platt Electric Supply for query: "price Siemens 20A dual function AFCI/GFCI breaker" | {"service":"workiz-api"}
2025-08-29 18:23:50 [[34m[34mdebug[34m[39m] [general]: [Cache Base] Using sanitized search cache key: crawl4ai:platt electric supply:search:price_Siemens_20A_dual_function_AFCI/GFCI_breaker | {"service":"workiz-api"}
2025-08-29 18:23:50 [[32m[32minfo[32m[39m] [general]: [Home Depot Scraper] Searching for "eaton amp" | {"service":"workiz-api"}
2025-08-29 18:23:50 [[32m[32minfo[32m[39m] [general]: [HD Crawl4AI Scraper] Searching Home Depot for "eaton amp" | {"service":"workiz-api"}
2025-08-29 18:23:50 [[34m[34mdebug[34m[39m] [general]: [Home Depot Scraper] Performing crawl for URL: https://www.homedepot.com/s/eaton%20amp | {"service":"workiz-api"}
2025-08-29 18:23:50 [[32m[32minfo[32m[39m] [general]: [Platt Electric Supply Scraper] Searching for "price Siemens MC4040B1200FEN 200A meter main" | {"service":"workiz-api"}
2025-08-29 18:23:50 [[32m[32minfo[32m[39m] [general]: [Platt Crawl4AI] Searching for: "price Siemens MC4040B1200FEN 200A meter main" | {"service":"workiz-api"}
2025-08-29 18:23:50 [[32m[32minfo[32m[39m] [general]: [Platt Electric Supply Scraper] Searching for "price 20 ft 4/0 aluminum SER cable" | {"service":"workiz-api"}
2025-08-29 18:23:50 [[32m[32minfo[32m[39m] [general]: [Platt Crawl4AI] Searching for: "price 20 ft 4/0 aluminum SER cable" | {"service":"workiz-api"}
2025-08-29 18:23:50 [[32m[32minfo[32m[39m] [general]: [Platt Electric Supply Scraper] Searching for "price 10 ft 2.5 inch schedule 80 PVC conduit" | {"service":"workiz-api"}
2025-08-29 18:23:50 [[32m[32minfo[32m[39m] [general]: [Platt Crawl4AI] Searching for: "price 10 ft 2.5 inch schedule 80 PVC conduit" | {"service":"workiz-api"}
2025-08-29 18:23:50 [[32m[32minfo[32m[39m] [general]: [Platt Electric Supply Scraper] Searching for "price Siemens 20A dual function AFCI/GFCI breaker" | {"service":"workiz-api"}
2025-08-29 18:23:50 [[32m[32minfo[32m[39m] [general]: [Platt Crawl4AI] Searching for: "price Siemens 20A dual function AFCI/GFCI breaker" | {"service":"workiz-api"}
2025-08-29 18:24:01 [[34m[34mdebug[34m[39m] [general]: Crawl4AI process completed in 11193ms with code 0 | {"service":"workiz-api"}
2025-08-29 18:24:01 [[33m[33mwarn[33m[39m] [general]: [Platt Crawl4AI] No products found with standard extraction, trying AI vision | {"service":"workiz-api"}
2025-08-29 18:24:01 [[33m[33mwarn[33m[39m] [general]: [Platt Crawl4AI] No screenshot available for AI vision analysis | {"service":"workiz-api"}
2025-08-29 18:24:01 [[34m[34mdebug[34m[39m] [general]: [ScraperService] Received 0 results from Platt Electric Supply | {"service":"workiz-api"}
2025-08-29 18:24:01 [[32m[32minfo[32m[39m] [general]: [ScraperService] Trying variation (brand_type_specs, confidence: 0.9): "siemens meter 200A" | {"service":"workiz-api"}
2025-08-29 18:24:01 [[34m[34mdebug[34m[39m] [general]: [ScraperService] Calling searchByDescription on Platt Electric Supply for query: "siemens meter 200A" | {"service":"workiz-api"}
2025-08-29 18:24:01 [[34m[34mdebug[34m[39m] [general]: [Cache Base] Using sanitized search cache key: crawl4ai:platt electric supply:search:siemens_meter_200A | {"service":"workiz-api"}
2025-08-29 18:24:01 [[32m[32minfo[32m[39m] [general]: [Platt Electric Supply Scraper] Searching for "siemens meter 200A" | {"service":"workiz-api"}
2025-08-29 18:24:01 [[32m[32minfo[32m[39m] [general]: [Platt Crawl4AI] Searching for: "siemens meter 200A" | {"service":"workiz-api"}
2025-08-29 18:24:01 [[34m[34mdebug[34m[39m] [general]: Crawl4AI process completed in 11466ms with code 0 | {"service":"workiz-api"}
2025-08-29 18:24:01 [[33m[33mwarn[33m[39m] [general]: [Platt Crawl4AI] No products found with standard extraction, trying AI vision | {"service":"workiz-api"}
2025-08-29 18:24:01 [[33m[33mwarn[33m[39m] [general]: [Platt Crawl4AI] No screenshot available for AI vision analysis | {"service":"workiz-api"}
2025-08-29 18:24:01 [[34m[34mdebug[34m[39m] [general]: [ScraperService] Received 0 results from Platt Electric Supply | {"service":"workiz-api"}
2025-08-29 18:24:01 [[32m[32minfo[32m[39m] [general]: [ScraperService] Trying variation (brand_type_specs, confidence: 0.9): "eaton volt" | {"service":"workiz-api"}
2025-08-29 18:24:01 [[34m[34mdebug[34m[39m] [general]: [ScraperService] Calling searchByDescription on Platt Electric Supply for query: "eaton volt" | {"service":"workiz-api"}
2025-08-29 18:24:01 [[34m[34mdebug[34m[39m] [general]: [Cache Base] Using sanitized search cache key: crawl4ai:platt electric supply:search:eaton_volt | {"service":"workiz-api"}
2025-08-29 18:24:01 [[32m[32minfo[32m[39m] [general]: [Platt Electric Supply Scraper] Searching for "eaton volt" | {"service":"workiz-api"}
2025-08-29 18:24:01 [[32m[32minfo[32m[39m] [general]: [Platt Crawl4AI] Searching for: "eaton volt" | {"service":"workiz-api"}
2025-08-29 18:24:02 [[34m[34mdebug[34m[39m] [general]: Crawl4AI process completed in 11957ms with code 0 | {"service":"workiz-api"}
2025-08-29 18:24:02 [[33m[33mwarn[33m[39m] [general]: [Platt Crawl4AI] No products found with standard extraction, trying AI vision | {"service":"workiz-api"}
2025-08-29 18:24:02 [[33m[33mwarn[33m[39m] [general]: [Platt Crawl4AI] No screenshot available for AI vision analysis | {"service":"workiz-api"}
2025-08-29 18:24:02 [[34m[34mdebug[34m[39m] [general]: [ScraperService] Received 0 results from Platt Electric Supply | {"service":"workiz-api"}
2025-08-29 18:24:02 [[32m[32minfo[32m[39m] [general]: [ScraperService] Trying variation (generic_with_specs, confidence: 0.7): "amp" | {"service":"workiz-api"}
2025-08-29 18:24:02 [[34m[34mdebug[34m[39m] [general]: [ScraperService] Calling searchByDescription on Platt Electric Supply for query: "amp" | {"service":"workiz-api"}
2025-08-29 18:24:02 [[34m[34mdebug[34m[39m] [general]: [Cache Base] Using sanitized search cache key: crawl4ai:platt electric supply:search:amp | {"service":"workiz-api"}
2025-08-29 18:24:02 [[34m[34mdebug[34m[39m] [general]: Crawl4AI process completed in 11906ms with code 0 | {"service":"workiz-api"}
2025-08-29 18:24:02 [[33m[33mwarn[33m[39m] [general]: [Platt Crawl4AI] No products found with standard extraction, trying AI vision | {"service":"workiz-api"}
2025-08-29 18:24:02 [[33m[33mwarn[33m[39m] [general]: [Platt Crawl4AI] No screenshot available for AI vision analysis | {"service":"workiz-api"}
2025-08-29 18:24:02 [[34m[34mdebug[34m[39m] [general]: [ScraperService] Received 0 results from Platt Electric Supply | {"service":"workiz-api"}
2025-08-29 18:24:02 [[32m[32minfo[32m[39m] [general]: [ScraperService] Trying variation (brand_type_specs, confidence: 0.9): "siemens breaker 20A" | {"service":"workiz-api"}
2025-08-29 18:24:02 [[34m[34mdebug[34m[39m] [general]: [ScraperService] Calling searchByDescription on Platt Electric Supply for query: "siemens breaker 20A" | {"service":"workiz-api"}
2025-08-29 18:24:02 [[34m[34mdebug[34m[39m] [general]: [Cache Base] Using sanitized search cache key: crawl4ai:platt electric supply:search:siemens_breaker_20A | {"service":"workiz-api"}
2025-08-29 18:24:02 [[32m[32minfo[32m[39m] [general]: [Platt Electric Supply Scraper] Searching for "amp" | {"service":"workiz-api"}
2025-08-29 18:24:02 [[32m[32minfo[32m[39m] [general]: [Platt Crawl4AI] Searching for: "amp" | {"service":"workiz-api"}
2025-08-29 18:24:02 [[32m[32minfo[32m[39m] [general]: [Platt Electric Supply Scraper] Searching for "siemens breaker 20A" | {"service":"workiz-api"}
2025-08-29 18:24:02 [[32m[32minfo[32m[39m] [general]: [Platt Crawl4AI] Searching for: "siemens breaker 20A" | {"service":"workiz-api"}
2025-08-29 18:24:08 [[34m[34mdebug[34m[39m] [general]: Crawl4AI process completed in 18492ms with code 0 | {"service":"workiz-api"}
2025-08-29 18:24:08 [[33m[33mwarn[33m[39m] [general]: [HD Crawl4AI Scraper] No products extracted for "eaton amp" | {"service":"workiz-api"}
2025-08-29 18:24:08 [[34m[34mdebug[34m[39m] [general]: [ScraperService] Received 0 results from Home Depot | {"service":"workiz-api"}
2025-08-29 18:24:08 [[32m[32minfo[32m[39m] [general]: [ScraperService] Trying variation (generic_with_specs, confidence: 0.7): "amp" | {"service":"workiz-api"}
2025-08-29 18:24:08 [[34m[34mdebug[34m[39m] [general]: [ScraperService] Calling searchByDescription on Home Depot for query: "amp" | {"service":"workiz-api"}
2025-08-29 18:24:08 [[34m[34mdebug[34m[39m] [general]: [Cache Base] Using sanitized search cache key: crawl4ai:home depot:search:amp | {"service":"workiz-api"}
2025-08-29 18:24:08 [[32m[32minfo[32m[39m] [general]: [Home Depot Scraper] Searching for "amp" | {"service":"workiz-api"}
2025-08-29 18:24:08 [[32m[32minfo[32m[39m] [general]: [HD Crawl4AI Scraper] Searching Home Depot for "amp" | {"service":"workiz-api"}
2025-08-29 18:24:08 [[34m[34mdebug[34m[39m] [general]: [Home Depot Scraper] Performing crawl for URL: https://www.homedepot.com/s/amp | {"service":"workiz-api"}
2025-08-29 18:24:12 [[34m[34mdebug[34m[39m] [general]: Crawl4AI process completed in 11062ms with code 0 | {"service":"workiz-api"}
2025-08-29 18:24:12 [[33m[33mwarn[33m[39m] [general]: [Platt Crawl4AI] No products found with standard extraction, trying AI vision | {"service":"workiz-api"}
2025-08-29 18:24:12 [[33m[33mwarn[33m[39m] [general]: [Platt Crawl4AI] No screenshot available for AI vision analysis | {"service":"workiz-api"}
2025-08-29 18:24:12 [[33m[33mwarn[33m[39m] [general]: [Platt Crawl4AI] Circuit breaker opened due to consecutive failures | {"service":"workiz-api"}
2025-08-29 18:24:12 [[34m[34mdebug[34m[39m] [general]: [ScraperService] Received 0 results from Platt Electric Supply | {"service":"workiz-api"}
2025-08-29 18:24:12 [[32m[32minfo[32m[39m] [general]: [ScraperService] Trying variation (simplified, confidence: 0.8): "price siemens fen 200a meter main" | {"service":"workiz-api"}
2025-08-29 18:24:12 [[33m[33mwarn[33m[39m] [general]: [ScraperService] Platt scraper (ID: 682bdb1305cc8e619ad861e0) is likely blocked. Skipping live search for query: "price siemens fen 200a meter main". | {"service":"workiz-api"}
2025-08-29 18:24:12 [[32m[32minfo[32m[39m] [general]: [ScraperService] Trying variation (generic_with_specs, confidence: 0.7): "meter 200 amp" | {"service":"workiz-api"}
2025-08-29 18:24:12 [[33m[33mwarn[33m[39m] [general]: [ScraperService] Platt scraper (ID: 682bdb1305cc8e619ad861e0) is likely blocked. Skipping live search for query: "meter 200 amp". | {"service":"workiz-api"}
2025-08-29 18:24:12 [[32m[32minfo[32m[39m] [general]: [ScraperService] Trying variation (alternative_brand, confidence: 0.6): "price sie mc4040b1200fen 200a meter main" | {"service":"workiz-api"}
2025-08-29 18:24:12 [[33m[33mwarn[33m[39m] [general]: [ScraperService] Platt scraper (ID: 682bdb1305cc8e619ad861e0) is likely blocked. Skipping live search for query: "price sie mc4040b1200fen 200a meter main". | {"service":"workiz-api"}
2025-08-29 18:24:12 [[33m[33mwarn[33m[39m] [general]: [ScraperService] All query variations failed for source 682bdb1305cc8e619ad861e0 | {"service":"workiz-api"}
2025-08-29 18:24:12 [[33m[33mwarn[33m[39m] [general]: [ScraperService Fallback] Platt search also returned no results for query: "price Siemens MC4040B1200FEN 200A meter main". | {"service":"workiz-api"}
2025-08-29 18:24:12 [[33m[33mwarn[33m[39m] [general]: [ScraperService Fallback] Both primary (Home Depot) and fallback (Platt) failed or returned no results for query: "price Siemens MC4040B1200FEN 200A meter main". | {"service":"workiz-api"}
2025-08-29 18:24:12 [[34m[34mdebug[34m[39m] [general]: [ScraperService] searchMaterials (Enhanced with Query Transformation) called with query: "price Siemens QP 20A single pole breaker", options: {"skipCache":false,"limit":1,"timeout":20000} | {"service":"workiz-api"}
2025-08-29 18:24:12 [[34m[34mdebug[34m[39m] [general]: [QueryTransformer] Transforming query: "price Siemens QP 20A single pole breaker" | {"service":"workiz-api"}
2025-08-29 18:24:12 [[34m[34mdebug[34m[39m] [general]: [QueryTransformer] Generated 3 variations | {"service":"workiz-api","brand":"siemens","type":"breaker","confidence":1}
2025-08-29 18:24:12 [[32m[32minfo[32m[39m] [general]: [ScraperService] Query transformation generated 3 variations | {"service":"workiz-api","originalQuery":"price Siemens QP 20A single pole breaker","detectedBrand":"siemens","detectedType":"breaker","confidence":1}
2025-08-29 18:24:12 [[32m[32minfo[32m[39m] [general]: [ScraperService Fallback] Attempting primary search: Home Depot (ID: 682bdb0e477e692811d861e0) | {"service":"workiz-api"}
2025-08-29 18:24:12 [[32m[32minfo[32m[39m] [general]: [ScraperService] Trying primary query: "price Siemens QP 20A single pole breaker" | {"service":"workiz-api"}
2025-08-29 18:24:12 [[34m[34mdebug[34m[39m] [general]: [ScraperService] Calling searchByDescription on Home Depot for query: "price Siemens QP 20A single pole breaker" | {"service":"workiz-api"}
2025-08-29 18:24:12 [[34m[34mdebug[34m[39m] [general]: [Cache Base] Using sanitized search cache key: crawl4ai:home depot:search:price_Siemens_QP_20A_single_pole_breaker | {"service":"workiz-api"}
2025-08-29 18:24:12 [[32m[32minfo[32m[39m] [general]: [Home Depot Scraper] Searching for "price Siemens QP 20A single pole breaker" | {"service":"workiz-api"}
2025-08-29 18:24:12 [[32m[32minfo[32m[39m] [general]: [HD Crawl4AI Scraper] Searching Home Depot for "price Siemens QP 20A single pole breaker" | {"service":"workiz-api"}
2025-08-29 18:24:12 [[34m[34mdebug[34m[39m] [general]: [Home Depot Scraper] Performing crawl for URL: https://www.homedepot.com/s/price%20Siemens%20QP%2020A%20single%20pole%20breaker | {"service":"workiz-api"}
2025-08-29 18:24:12 [[34m[34mdebug[34m[39m] [general]: Crawl4AI process completed in 10961ms with code 0 | {"service":"workiz-api"}
2025-08-29 18:24:12 [[33m[33mwarn[33m[39m] [general]: [Platt Crawl4AI] No products found with standard extraction, trying AI vision | {"service":"workiz-api"}
2025-08-29 18:24:12 [[33m[33mwarn[33m[39m] [general]: [Platt Crawl4AI] No screenshot available for AI vision analysis | {"service":"workiz-api"}
2025-08-29 18:24:12 [[33m[33mwarn[33m[39m] [general]: [Platt Crawl4AI] Circuit breaker opened due to consecutive failures | {"service":"workiz-api"}
2025-08-29 18:24:12 [[34m[34mdebug[34m[39m] [general]: [ScraperService] Received 0 results from Platt Electric Supply | {"service":"workiz-api"}
2025-08-29 18:24:12 [[32m[32minfo[32m[39m] [general]: [ScraperService] Trying variation (generic_with_specs, confidence: 0.7): "volt" | {"service":"workiz-api"}
2025-08-29 18:24:12 [[33m[33mwarn[33m[39m] [general]: [ScraperService] Platt scraper (ID: 682bdb1305cc8e619ad861e0) is likely blocked. Skipping live search for query: "volt". | {"service":"workiz-api"}
2025-08-29 18:24:12 [[33m[33mwarn[33m[39m] [general]: [ScraperService] All query variations failed for source 682bdb1305cc8e619ad861e0 | {"service":"workiz-api"}
2025-08-29 18:24:12 [[33m[33mwarn[33m[39m] [general]: [ScraperService Fallback] Platt search also returned no results for query: "price 10 ft 2.5 inch schedule 80 PVC conduit". | {"service":"workiz-api"}
2025-08-29 18:24:12 [[33m[33mwarn[33m[39m] [general]: [ScraperService Fallback] Both primary (Home Depot) and fallback (Platt) failed or returned no results for query: "price 10 ft 2.5 inch schedule 80 PVC conduit". | {"service":"workiz-api"}
2025-08-29 18:24:12 [[34m[34mdebug[34m[39m] [general]: [ScraperService] searchMaterials (Enhanced with Query Transformation) called with query: "price Siemens Q230 30A double pole breaker", options: {"skipCache":false,"limit":1,"timeout":20000} | {"service":"workiz-api"}
2025-08-29 18:24:12 [[34m[34mdebug[34m[39m] [general]: [QueryTransformer] Transforming query: "price Siemens Q230 30A double pole breaker" | {"service":"workiz-api"}
2025-08-29 18:24:12 [[34m[34mdebug[34m[39m] [general]: [QueryTransformer] Generated 3 variations | {"service":"workiz-api","brand":"siemens","type":"breaker","confidence":1}
2025-08-29 18:24:12 [[32m[32minfo[32m[39m] [general]: [ScraperService] Query transformation generated 3 variations | {"service":"workiz-api","originalQuery":"price Siemens Q230 30A double pole breaker","detectedBrand":"siemens","detectedType":"breaker","confidence":1}
2025-08-29 18:24:12 [[32m[32minfo[32m[39m] [general]: [ScraperService Fallback] Attempting primary search: Home Depot (ID: 682bdb0e477e692811d861e0) | {"service":"workiz-api"}
2025-08-29 18:24:12 [[32m[32minfo[32m[39m] [general]: [ScraperService] Trying primary query: "price Siemens Q230 30A double pole breaker" | {"service":"workiz-api"}
2025-08-29 18:24:12 [[34m[34mdebug[34m[39m] [general]: [ScraperService] Calling searchByDescription on Home Depot for query: "price Siemens Q230 30A double pole breaker" | {"service":"workiz-api"}
2025-08-29 18:24:12 [[34m[34mdebug[34m[39m] [general]: [Cache Base] Using sanitized search cache key: crawl4ai:home depot:search:price_Siemens_Q230_30A_double_pole_breaker | {"service":"workiz-api"}
2025-08-29 18:24:12 [[32m[32minfo[32m[39m] [general]: [Home Depot Scraper] Searching for "price Siemens Q230 30A double pole breaker" | {"service":"workiz-api"}
2025-08-29 18:24:12 [[32m[32minfo[32m[39m] [general]: [HD Crawl4AI Scraper] Searching Home Depot for "price Siemens Q230 30A double pole breaker" | {"service":"workiz-api"}
2025-08-29 18:24:12 [[34m[34mdebug[34m[39m] [general]: [Home Depot Scraper] Performing crawl for URL: https://www.homedepot.com/s/price%20Siemens%20Q230%2030A%20double%20pole%20breaker | {"service":"workiz-api"}
2025-08-29 18:24:13 [[34m[34mdebug[34m[39m] [general]: Crawl4AI process completed in 10782ms with code 0 | {"service":"workiz-api"}
2025-08-29 18:24:13 [[33m[33mwarn[33m[39m] [general]: [Platt Crawl4AI] No products found with standard extraction, trying AI vision | {"service":"workiz-api"}
2025-08-29 18:24:13 [[33m[33mwarn[33m[39m] [general]: [Platt Crawl4AI] No screenshot available for AI vision analysis | {"service":"workiz-api"}
2025-08-29 18:24:13 [[33m[33mwarn[33m[39m] [general]: [Platt Crawl4AI] Circuit breaker opened due to consecutive failures | {"service":"workiz-api"}
2025-08-29 18:24:13 [[34m[34mdebug[34m[39m] [general]: [ScraperService] Received 0 results from Platt Electric Supply | {"service":"workiz-api"}
2025-08-29 18:24:13 [[32m[32minfo[32m[39m] [general]: [ScraperService] Trying variation (generic_with_specs, confidence: 0.7): "breaker 20 amp" | {"service":"workiz-api"}
2025-08-29 18:24:13 [[33m[33mwarn[33m[39m] [general]: [ScraperService] Platt scraper (ID: 682bdb1305cc8e619ad861e0) is likely blocked. Skipping live search for query: "breaker 20 amp". | {"service":"workiz-api"}
2025-08-29 18:24:13 [[32m[32minfo[32m[39m] [general]: [ScraperService] Trying variation (alternative_brand, confidence: 0.6): "price sie 20a dual function afci/gfci breaker" | {"service":"workiz-api"}
2025-08-29 18:24:13 [[33m[33mwarn[33m[39m] [general]: [ScraperService] Platt scraper (ID: 682bdb1305cc8e619ad861e0) is likely blocked. Skipping live search for query: "price sie 20a dual function afci/gfci breaker". | {"service":"workiz-api"}
2025-08-29 18:24:13 [[33m[33mwarn[33m[39m] [general]: [ScraperService] All query variations failed for source 682bdb1305cc8e619ad861e0 | {"service":"workiz-api"}
2025-08-29 18:24:13 [[33m[33mwarn[33m[39m] [general]: [ScraperService Fallback] Platt search also returned no results for query: "price Siemens 20A dual function AFCI/GFCI breaker". | {"service":"workiz-api"}
2025-08-29 18:24:13 [[33m[33mwarn[33m[39m] [general]: [ScraperService Fallback] Both primary (Home Depot) and fallback (Platt) failed or returned no results for query: "price Siemens 20A dual function AFCI/GFCI breaker". | {"service":"workiz-api"}
2025-08-29 18:24:13 [[34m[34mdebug[34m[39m] [general]: [ScraperService] searchMaterials (Enhanced with Query Transformation) called with query: "price Siemens Q250 50A double pole breaker", options: {"skipCache":false,"limit":1,"timeout":20000} | {"service":"workiz-api"}
2025-08-29 18:24:13 [[34m[34mdebug[34m[39m] [general]: [QueryTransformer] Transforming query: "price Siemens Q250 50A double pole breaker" | {"service":"workiz-api"}
2025-08-29 18:24:13 [[34m[34mdebug[34m[39m] [general]: [QueryTransformer] Generated 3 variations | {"service":"workiz-api","brand":"siemens","type":"breaker","confidence":1}
2025-08-29 18:24:13 [[32m[32minfo[32m[39m] [general]: [ScraperService] Query transformation generated 3 variations | {"service":"workiz-api","originalQuery":"price Siemens Q250 50A double pole breaker","detectedBrand":"siemens","detectedType":"breaker","confidence":1}
2025-08-29 18:24:13 [[32m[32minfo[32m[39m] [general]: [ScraperService Fallback] Attempting primary search: Home Depot (ID: 682bdb0e477e692811d861e0) | {"service":"workiz-api"}
2025-08-29 18:24:13 [[32m[32minfo[32m[39m] [general]: [ScraperService] Trying primary query: "price Siemens Q250 50A double pole breaker" | {"service":"workiz-api"}
2025-08-29 18:24:13 [[34m[34mdebug[34m[39m] [general]: [ScraperService] Calling searchByDescription on Home Depot for query: "price Siemens Q250 50A double pole breaker" | {"service":"workiz-api"}
2025-08-29 18:24:13 [[34m[34mdebug[34m[39m] [general]: [Cache Base] Using sanitized search cache key: crawl4ai:home depot:search:price_Siemens_Q250_50A_double_pole_breaker | {"service":"workiz-api"}
2025-08-29 18:24:13 [[32m[32minfo[32m[39m] [general]: [Home Depot Scraper] Searching for "price Siemens Q250 50A double pole breaker" | {"service":"workiz-api"}
2025-08-29 18:24:13 [[32m[32minfo[32m[39m] [general]: [HD Crawl4AI Scraper] Searching Home Depot for "price Siemens Q250 50A double pole breaker" | {"service":"workiz-api"}
2025-08-29 18:24:13 [[34m[34mdebug[34m[39m] [general]: [Home Depot Scraper] Performing crawl for URL: https://www.homedepot.com/s/price%20Siemens%20Q250%2050A%20double%20pole%20breaker | {"service":"workiz-api"}
2025-08-29 18:24:13 [[34m[34mdebug[34m[39m] [general]: Crawl4AI process completed in 11339ms with code 0 | {"service":"workiz-api"}
2025-08-29 18:24:13 [[33m[33mwarn[33m[39m] [general]: [Platt Crawl4AI] No products found with standard extraction, trying AI vision | {"service":"workiz-api"}
2025-08-29 18:24:13 [[33m[33mwarn[33m[39m] [general]: [Platt Crawl4AI] No screenshot available for AI vision analysis | {"service":"workiz-api"}
2025-08-29 18:24:13 [[33m[33mwarn[33m[39m] [general]: [Platt Crawl4AI] Circuit breaker opened due to consecutive failures | {"service":"workiz-api"}
2025-08-29 18:24:13 [[34m[34mdebug[34m[39m] [general]: [ScraperService] Received 0 results from Platt Electric Supply | {"service":"workiz-api"}
2025-08-29 18:24:13 [[33m[33mwarn[33m[39m] [general]: [ScraperService] All query variations failed for source 682bdb1305cc8e619ad861e0 | {"service":"workiz-api"}
2025-08-29 18:24:13 [[33m[33mwarn[33m[39m] [general]: [ScraperService Fallback] Platt search also returned no results for query: "price 20 ft 4/0 aluminum SER cable". | {"service":"workiz-api"}
2025-08-29 18:24:13 [[33m[33mwarn[33m[39m] [general]: [ScraperService Fallback] Both primary (Home Depot) and fallback (Platt) failed or returned no results for query: "price 20 ft 4/0 aluminum SER cable". | {"service":"workiz-api"}
2025-08-29 18:24:13 [[34m[34mdebug[34m[39m] [general]: [ScraperService] searchMaterials (Enhanced with Query Transformation) called with query: "price 5/8 inch 8 ft ground rod", options: {"skipCache":false,"limit":1,"timeout":20000} | {"service":"workiz-api"}
2025-08-29 18:24:13 [[34m[34mdebug[34m[39m] [general]: [QueryTransformer] Transforming query: "price 5/8 inch 8 ft ground rod" | {"service":"workiz-api"}
2025-08-29 18:24:13 [[34m[34mdebug[34m[39m] [general]: [QueryTransformer] Generated 0 variations | {"service":"workiz-api","brand":"eaton","type":null,"confidence":0.5}
2025-08-29 18:24:13 [[32m[32minfo[32m[39m] [general]: [ScraperService] Query transformation generated 0 variations | {"service":"workiz-api","originalQuery":"price 5/8 inch 8 ft ground rod","detectedBrand":"eaton","detectedType":null,"confidence":0.5}
2025-08-29 18:24:13 [[32m[32minfo[32m[39m] [general]: [ScraperService Fallback] Attempting primary search: Home Depot (ID: 682bdb0e477e692811d861e0) | {"service":"workiz-api"}
2025-08-29 18:24:13 [[32m[32minfo[32m[39m] [general]: [ScraperService] Trying primary query: "price 5/8 inch 8 ft ground rod" | {"service":"workiz-api"}
2025-08-29 18:24:13 [[34m[34mdebug[34m[39m] [general]: [ScraperService] Calling searchByDescription on Home Depot for query: "price 5/8 inch 8 ft ground rod" | {"service":"workiz-api"}
2025-08-29 18:24:13 [[34m[34mdebug[34m[39m] [general]: [Cache Base] Using sanitized search cache key: crawl4ai:home depot:search:price_5/8_inch_8_ft_ground_rod | {"service":"workiz-api"}
2025-08-29 18:24:13 [[32m[32minfo[32m[39m] [general]: [Home Depot Scraper] Searching for "price 5/8 inch 8 ft ground rod" | {"service":"workiz-api"}
2025-08-29 18:24:13 [[32m[32minfo[32m[39m] [general]: [HD Crawl4AI Scraper] Searching Home Depot for "price 5/8 inch 8 ft ground rod" | {"service":"workiz-api"}
2025-08-29 18:24:13 [[34m[34mdebug[34m[39m] [general]: [Home Depot Scraper] Performing crawl for URL: https://www.homedepot.com/s/price%205%2F8%20inch%208%20ft%20ground%20rod | {"service":"workiz-api"}
2025-08-29 18:24:28 [[33m[33mwarn[33m[39m] [general]: Crawl4AI process gracefully terminated after 20000ms for URL: https://www.homedepot.com/s/amp | {"service":"workiz-api"}
2025-08-29 18:24:28 [[31m[31merror[31m[39m] [general]: Crawl4AI process timed out after 20000ms for URL: https://www.homedepot.com/s/amp | {"service":"workiz-api"}
2025-08-29 18:24:28 [[31m[31merror[31m[39m] [general]: [HD Crawl4AI Scraper] Error searching for "amp": Crawl operation timed out after 20000ms | {"service":"workiz-api","stack":"Error: Crawl operation timed out after 20000ms\n    at Timeout._onTimeout (C:\\Projects\\workiz\\backend\\scrapers\\crawl4ai\\crawl4ai-service.js:709:18)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)"}
2025-08-29 18:24:28 [[31m[31merror[31m[39m] [general]: [Home Depot Scraper] Error searching for "amp": Crawl operation timed out after 20000ms | {"service":"workiz-api","stack":"Error: Crawl operation timed out after 20000ms\n    at Timeout._onTimeout (C:\\Projects\\workiz\\backend\\scrapers\\crawl4ai\\crawl4ai-service.js:709:18)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)"}
2025-08-29 18:24:28 [[33m[33mwarn[33m[39m] [general]: [Crawl4AI] Circuit breaker OPENED after 5 failures (0 timeouts) for "home depot-amp". Next attempt in 180s | {"service":"workiz-api"}
2025-08-29 18:24:28 [[31m[31merror[31m[39m] [general]: [ScraperService] Error searching in Home Depot for query "amp": Crawl operation timed out after 20000ms | {"service":"workiz-api","stack":"Error: Crawl operation timed out after 20000ms\n    at Timeout._onTimeout (C:\\Projects\\workiz\\backend\\scrapers\\crawl4ai\\crawl4ai-service.js:709:18)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)"}
2025-08-29 18:24:29 [[33m[33mwarn[33m[39m] [general]: [ScraperService] Query variation "amp" failed: | {"service":"workiz-api"}
2025-08-29 18:24:29 [[33m[33mwarn[33m[39m] [general]: [ScraperService] All query variations failed for source 682bdb0e477e692811d861e0 | {"service":"workiz-api"}
2025-08-29 18:24:29 [[33m[33mwarn[33m[39m] [general]: [ScraperService Fallback] Home Depot search returned no results for query: "price 2.5 inch PVC weatherhead". | {"service":"workiz-api"}
2025-08-29 18:24:29 [[33m[33mwarn[33m[39m] [general]: [ScraperService Fallback] Primary search failed or yielded no results. Attempting fallback: Platt (ID: 682bdb1305cc8e619ad861e0) | {"service":"workiz-api"}
2025-08-29 18:24:29 [[32m[32minfo[32m[39m] [general]: [ScraperService] Trying primary query: "price 2.5 inch PVC weatherhead" | {"service":"workiz-api"}
2025-08-29 18:24:29 [[33m[33mwarn[33m[39m] [general]: [ScraperService] Platt scraper (ID: 682bdb1305cc8e619ad861e0) is likely blocked. Skipping live search for query: "price 2.5 inch PVC weatherhead". | {"service":"workiz-api"}
2025-08-29 18:24:29 [[32m[32minfo[32m[39m] [general]: [ScraperService] Trying variation (brand_type_specs, confidence: 0.9): "eaton amp" | {"service":"workiz-api"}
2025-08-29 18:24:29 [[33m[33mwarn[33m[39m] [general]: [ScraperService] Platt scraper (ID: 682bdb1305cc8e619ad861e0) is likely blocked. Skipping live search for query: "eaton amp". | {"service":"workiz-api"}
2025-08-29 18:24:30 [[32m[32minfo[32m[39m] [general]: [ScraperService] Trying variation (generic_with_specs, confidence: 0.7): "amp" | {"service":"workiz-api"}
2025-08-29 18:24:30 [[33m[33mwarn[33m[39m] [general]: [ScraperService] Platt scraper (ID: 682bdb1305cc8e619ad861e0) is likely blocked. Skipping live search for query: "amp". | {"service":"workiz-api"}
2025-08-29 18:24:30 [[33m[33mwarn[33m[39m] [general]: [ScraperService] All query variations failed for source 682bdb1305cc8e619ad861e0 | {"service":"workiz-api"}
2025-08-29 18:24:30 [[33m[33mwarn[33m[39m] [general]: [ScraperService Fallback] Platt search also returned no results for query: "price 2.5 inch PVC weatherhead". | {"service":"workiz-api"}
2025-08-29 18:24:30 [[33m[33mwarn[33m[39m] [general]: [ScraperService Fallback] Both primary (Home Depot) and fallback (Platt) failed or returned no results for query: "price 2.5 inch PVC weatherhead". | {"service":"workiz-api"}
2025-08-29 18:24:30 [[33m[33mwarn[33m[39m] [general]: No price found for item 12 with query: price 2.5 inch PVC weatherhead | {"service":"workiz-api"}
2025-08-29 18:24:30 [[34m[34mdebug[34m[39m] [general]: [ScraperService] searchMaterials (Enhanced with Query Transformation) called with query: "price 30 ft #4 solid bare copper wire", options: {"skipCache":false,"limit":1,"timeout":20000} | {"service":"workiz-api"}
2025-08-29 18:24:30 [[34m[34mdebug[34m[39m] [general]: [QueryTransformer] Transforming query: "price 30 ft #4 solid bare copper wire" | {"service":"workiz-api"}
2025-08-29 18:24:30 [[34m[34mdebug[34m[39m] [general]: [QueryTransformer] Generated 1 variations | {"service":"workiz-api","brand":null,"type":"amp","confidence":0.7}
2025-08-29 18:24:30 [[32m[32minfo[32m[39m] [general]: [ScraperService] Query transformation generated 1 variations | {"service":"workiz-api","originalQuery":"price 30 ft #4 solid bare copper wire","detectedBrand":null,"detectedType":"amp","confidence":0.7}
2025-08-29 18:24:30 [[32m[32minfo[32m[39m] [general]: [ScraperService Fallback] Attempting primary search: Home Depot (ID: 682bdb0e477e692811d861e0) | {"service":"workiz-api"}
2025-08-29 18:24:30 [[32m[32minfo[32m[39m] [general]: [ScraperService] Trying primary query: "price 30 ft #4 solid bare copper wire" | {"service":"workiz-api"}
2025-08-29 18:24:30 [[34m[34mdebug[34m[39m] [general]: [ScraperService] Calling searchByDescription on Home Depot for query: "price 30 ft #4 solid bare copper wire" | {"service":"workiz-api"}
2025-08-29 18:24:30 [[33m[33mwarn[33m[39m] [general]: [Crawl4AI] Circuit breaker OPEN for "home depot-price 30 ft #4 solid bare copper wire". Retry in 178s | {"service":"workiz-api"}
2025-08-29 18:24:30 [[31m[31merror[31m[39m] [general]: [ScraperService] Error searching in Home Depot for query "price 30 ft #4 solid bare copper wire": Circuit breaker is OPEN. Retry in 178 seconds. | {"service":"workiz-api","stack":"Error: Circuit breaker is OPEN. Retry in 178 seconds.\n    at CircuitBreaker.execute (C:\\Projects\\workiz\\backend\\utils\\circuitBreaker.js:40:15)\n    at ScraperService.searchBySource (C:\\Projects\\workiz\\backend\\scrapers\\ScraperService.js:427:56)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async ScraperService.searchBySourceWithVariations (C:\\Projects\\workiz\\backend\\scrapers\\ScraperService.js:336:19)\n    at async ScraperService.searchMaterials (C:\\Projects\\workiz\\backend\\scrapers\\ScraperService.js:243:19)\n    at async processItem (C:\\Projects\\workiz\\backend\\controllers\\quoteController.js:3318:37)\n    at async processItemsInParallel (C:\\Projects\\workiz\\backend\\controllers\\quoteController.js:3428:13)\n    at async lookupPricesWithoutSaving (C:\\Projects\\workiz\\backend\\controllers\\quoteController.js:3436:26)"}
2025-08-29 18:24:30 [[31m[31merror[31m[39m] [general]: [ScraperService Fallback] Error searching primary source (Home Depot ID: 682bdb0e477e692811d861e0): Circuit breaker is OPEN. Retry in 178 seconds. | {"service":"workiz-api","stack":"Error: Circuit breaker is OPEN. Retry in 178 seconds.\n    at CircuitBreaker.execute (C:\\Projects\\workiz\\backend\\utils\\circuitBreaker.js:40:15)\n    at ScraperService.searchBySource (C:\\Projects\\workiz\\backend\\scrapers\\ScraperService.js:427:56)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async ScraperService.searchBySourceWithVariations (C:\\Projects\\workiz\\backend\\scrapers\\ScraperService.js:336:19)\n    at async ScraperService.searchMaterials (C:\\Projects\\workiz\\backend\\scrapers\\ScraperService.js:243:19)\n    at async processItem (C:\\Projects\\workiz\\backend\\controllers\\quoteController.js:3318:37)\n    at async processItemsInParallel (C:\\Projects\\workiz\\backend\\controllers\\quoteController.js:3428:13)\n    at async lookupPricesWithoutSaving (C:\\Projects\\workiz\\backend\\controllers\\quoteController.js:3436:26)"}
2025-08-29 18:24:30 [[33m[33mwarn[33m[39m] [general]: [ScraperService Fallback] Primary search failed or yielded no results. Attempting fallback: Platt (ID: 682bdb1305cc8e619ad861e0) | {"service":"workiz-api"}
2025-08-29 18:24:30 [[32m[32minfo[32m[39m] [general]: [ScraperService] Trying primary query: "price 30 ft #4 solid bare copper wire" | {"service":"workiz-api"}
2025-08-29 18:24:30 [[33m[33mwarn[33m[39m] [general]: [ScraperService] Platt scraper (ID: 682bdb1305cc8e619ad861e0) is likely blocked. Skipping live search for query: "price 30 ft #4 solid bare copper wire". | {"service":"workiz-api"}
2025-08-29 18:24:30 [[32m[32minfo[32m[39m] [general]: [ScraperService] Trying variation (generic_with_specs, confidence: 0.7): "amp" | {"service":"workiz-api"}
2025-08-29 18:24:30 [[33m[33mwarn[33m[39m] [general]: [ScraperService] Platt scraper (ID: 682bdb1305cc8e619ad861e0) is likely blocked. Skipping live search for query: "amp". | {"service":"workiz-api"}
2025-08-29 18:24:30 [[33m[33mwarn[33m[39m] [general]: [ScraperService] All query variations failed for source 682bdb1305cc8e619ad861e0 | {"service":"workiz-api"}
2025-08-29 18:24:30 [[33m[33mwarn[33m[39m] [general]: [ScraperService Fallback] Platt search also returned no results for query: "price 30 ft #4 solid bare copper wire". | {"service":"workiz-api"}
2025-08-29 18:24:30 [[33m[33mwarn[33m[39m] [general]: [ScraperService Fallback] Both primary (Home Depot) and fallback (Platt) failed or returned no results for query: "price 30 ft #4 solid bare copper wire". | {"service":"workiz-api"}
2025-08-29 18:24:30 [[34m[34mdebug[34m[39m] [general]: [ScraperService] searchMaterials (Enhanced with Query Transformation) called with query: "price acorn ground rod clamp 5/8 inch", options: {"skipCache":false,"limit":1,"timeout":20000} | {"service":"workiz-api"}
2025-08-29 18:24:30 [[34m[34mdebug[34m[39m] [general]: [QueryTransformer] Transforming query: "price acorn ground rod clamp 5/8 inch" | {"service":"workiz-api"}
2025-08-29 18:24:30 [[34m[34mdebug[34m[39m] [general]: [QueryTransformer] Generated 2 variations | {"service":"workiz-api","brand":"eaton","type":"amp","confidence":0.8}
2025-08-29 18:24:30 [[32m[32minfo[32m[39m] [general]: [ScraperService] Query transformation generated 2 variations | {"service":"workiz-api","originalQuery":"price acorn ground rod clamp 5/8 inch","detectedBrand":"eaton","detectedType":"amp","confidence":0.8}
2025-08-29 18:24:30 [[32m[32minfo[32m[39m] [general]: [ScraperService Fallback] Attempting primary search: Home Depot (ID: 682bdb0e477e692811d861e0) | {"service":"workiz-api"}
2025-08-29 18:24:30 [[32m[32minfo[32m[39m] [general]: [ScraperService] Trying primary query: "price acorn ground rod clamp 5/8 inch" | {"service":"workiz-api"}
2025-08-29 18:24:30 [[34m[34mdebug[34m[39m] [general]: [ScraperService] Calling searchByDescription on Home Depot for query: "price acorn ground rod clamp 5/8 inch" | {"service":"workiz-api"}
2025-08-29 18:24:30 [[33m[33mwarn[33m[39m] [general]: [Crawl4AI] Circuit breaker OPEN for "home depot-price acorn ground rod clamp 5/8 inch". Retry in 178s | {"service":"workiz-api"}
2025-08-29 18:24:30 [[31m[31merror[31m[39m] [general]: [ScraperService] Error searching in Home Depot for query "price acorn ground rod clamp 5/8 inch": Circuit breaker is OPEN. Retry in 178 seconds. | {"service":"workiz-api","stack":"Error: Circuit breaker is OPEN. Retry in 178 seconds.\n    at CircuitBreaker.execute (C:\\Projects\\workiz\\backend\\utils\\circuitBreaker.js:40:15)\n    at ScraperService.searchBySource (C:\\Projects\\workiz\\backend\\scrapers\\ScraperService.js:427:56)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async ScraperService.searchBySourceWithVariations (C:\\Projects\\workiz\\backend\\scrapers\\ScraperService.js:336:19)\n    at async ScraperService.searchMaterials (C:\\Projects\\workiz\\backend\\scrapers\\ScraperService.js:243:19)\n    at async processItem (C:\\Projects\\workiz\\backend\\controllers\\quoteController.js:3318:37)\n    at async processItemsInParallel (C:\\Projects\\workiz\\backend\\controllers\\quoteController.js:3428:13)\n    at async lookupPricesWithoutSaving (C:\\Projects\\workiz\\backend\\controllers\\quoteController.js:3436:26)"}
2025-08-29 18:24:30 [[31m[31merror[31m[39m] [general]: [ScraperService Fallback] Error searching primary source (Home Depot ID: 682bdb0e477e692811d861e0): Circuit breaker is OPEN. Retry in 178 seconds. | {"service":"workiz-api","stack":"Error: Circuit breaker is OPEN. Retry in 178 seconds.\n    at CircuitBreaker.execute (C:\\Projects\\workiz\\backend\\utils\\circuitBreaker.js:40:15)\n    at ScraperService.searchBySource (C:\\Projects\\workiz\\backend\\scrapers\\ScraperService.js:427:56)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async ScraperService.searchBySourceWithVariations (C:\\Projects\\workiz\\backend\\scrapers\\ScraperService.js:336:19)\n    at async ScraperService.searchMaterials (C:\\Projects\\workiz\\backend\\scrapers\\ScraperService.js:243:19)\n    at async processItem (C:\\Projects\\workiz\\backend\\controllers\\quoteController.js:3318:37)\n    at async processItemsInParallel (C:\\Projects\\workiz\\backend\\controllers\\quoteController.js:3428:13)\n    at async lookupPricesWithoutSaving (C:\\Projects\\workiz\\backend\\controllers\\quoteController.js:3436:26)"}
2025-08-29 18:24:30 [[33m[33mwarn[33m[39m] [general]: [ScraperService Fallback] Primary search failed or yielded no results. Attempting fallback: Platt (ID: 682bdb1305cc8e619ad861e0) | {"service":"workiz-api"}
2025-08-29 18:24:30 [[32m[32minfo[32m[39m] [general]: [ScraperService] Trying primary query: "price acorn ground rod clamp 5/8 inch" | {"service":"workiz-api"}
2025-08-29 18:24:30 [[33m[33mwarn[33m[39m] [general]: [ScraperService] Platt scraper (ID: 682bdb1305cc8e619ad861e0) is likely blocked. Skipping live search for query: "price acorn ground rod clamp 5/8 inch". | {"service":"workiz-api"}
2025-08-29 18:24:30 [[32m[32minfo[32m[39m] [general]: [ScraperService] Trying variation (brand_type_specs, confidence: 0.9): "eaton amp" | {"service":"workiz-api"}
2025-08-29 18:24:30 [[33m[33mwarn[33m[39m] [general]: [ScraperService] Platt scraper (ID: 682bdb1305cc8e619ad861e0) is likely blocked. Skipping live search for query: "eaton amp". | {"service":"workiz-api"}
2025-08-29 18:24:30 [[32m[32minfo[32m[39m] [general]: [ScraperService] Trying variation (generic_with_specs, confidence: 0.7): "amp" | {"service":"workiz-api"}
2025-08-29 18:24:30 [[33m[33mwarn[33m[39m] [general]: [ScraperService] Platt scraper (ID: 682bdb1305cc8e619ad861e0) is likely blocked. Skipping live search for query: "amp". | {"service":"workiz-api"}
2025-08-29 18:24:30 [[33m[33mwarn[33m[39m] [general]: [ScraperService] All query variations failed for source 682bdb1305cc8e619ad861e0 | {"service":"workiz-api"}
2025-08-29 18:24:30 [[33m[33mwarn[33m[39m] [general]: [ScraperService Fallback] Platt search also returned no results for query: "price acorn ground rod clamp 5/8 inch". | {"service":"workiz-api"}
2025-08-29 18:24:30 [[33m[33mwarn[33m[39m] [general]: [ScraperService Fallback] Both primary (Home Depot) and fallback (Platt) failed or returned no results for query: "price acorn ground rod clamp 5/8 inch". | {"service":"workiz-api"}
2025-08-29 18:24:30 [[34m[34mdebug[34m[39m] [general]: [ScraperService] searchMaterials (Enhanced with Query Transformation) called with query: "price intersystem bonding bridge", options: {"skipCache":false,"limit":1,"timeout":20000} | {"service":"workiz-api"}
2025-08-29 18:24:30 [[34m[34mdebug[34m[39m] [general]: [QueryTransformer] Transforming query: "price intersystem bonding bridge" | {"service":"workiz-api"}
2025-08-29 18:24:30 [[34m[34mdebug[34m[39m] [general]: [QueryTransformer] Generated 1 variations | {"service":"workiz-api","brand":"ge","type":null,"confidence":0.5}
2025-08-29 18:24:30 [[32m[32minfo[32m[39m] [general]: [ScraperService] Query transformation generated 1 variations | {"service":"workiz-api","originalQuery":"price intersystem bonding bridge","detectedBrand":"ge","detectedType":null,"confidence":0.5}
2025-08-29 18:24:30 [[32m[32minfo[32m[39m] [general]: [ScraperService Fallback] Attempting primary search: Home Depot (ID: 682bdb0e477e692811d861e0) | {"service":"workiz-api"}
2025-08-29 18:24:30 [[32m[32minfo[32m[39m] [general]: [ScraperService] Trying primary query: "price intersystem bonding bridge" | {"service":"workiz-api"}
2025-08-29 18:24:30 [[34m[34mdebug[34m[39m] [general]: [ScraperService] Calling searchByDescription on Home Depot for query: "price intersystem bonding bridge" | {"service":"workiz-api"}
2025-08-29 18:24:30 [[33m[33mwarn[33m[39m] [general]: [Crawl4AI] Circuit breaker OPEN for "home depot-price intersystem bonding bridge". Retry in 178s | {"service":"workiz-api"}
2025-08-29 18:24:30 [[31m[31merror[31m[39m] [general]: [ScraperService] Error searching in Home Depot for query "price intersystem bonding bridge": Circuit breaker is OPEN. Retry in 178 seconds. | {"service":"workiz-api","stack":"Error: Circuit breaker is OPEN. Retry in 178 seconds.\n    at CircuitBreaker.execute (C:\\Projects\\workiz\\backend\\utils\\circuitBreaker.js:40:15)\n    at ScraperService.searchBySource (C:\\Projects\\workiz\\backend\\scrapers\\ScraperService.js:427:56)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async ScraperService.searchBySourceWithVariations (C:\\Projects\\workiz\\backend\\scrapers\\ScraperService.js:336:19)\n    at async ScraperService.searchMaterials (C:\\Projects\\workiz\\backend\\scrapers\\ScraperService.js:243:19)\n    at async processItem (C:\\Projects\\workiz\\backend\\controllers\\quoteController.js:3318:37)\n    at async processItemsInParallel (C:\\Projects\\workiz\\backend\\controllers\\quoteController.js:3428:13)\n    at async lookupPricesWithoutSaving (C:\\Projects\\workiz\\backend\\controllers\\quoteController.js:3436:26)"}
2025-08-29 18:24:30 [[31m[31merror[31m[39m] [general]: [ScraperService Fallback] Error searching primary source (Home Depot ID: 682bdb0e477e692811d861e0): Circuit breaker is OPEN. Retry in 178 seconds. | {"service":"workiz-api","stack":"Error: Circuit breaker is OPEN. Retry in 178 seconds.\n    at CircuitBreaker.execute (C:\\Projects\\workiz\\backend\\utils\\circuitBreaker.js:40:15)\n    at ScraperService.searchBySource (C:\\Projects\\workiz\\backend\\scrapers\\ScraperService.js:427:56)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async ScraperService.searchBySourceWithVariations (C:\\Projects\\workiz\\backend\\scrapers\\ScraperService.js:336:19)\n    at async ScraperService.searchMaterials (C:\\Projects\\workiz\\backend\\scrapers\\ScraperService.js:243:19)\n    at async processItem (C:\\Projects\\workiz\\backend\\controllers\\quoteController.js:3318:37)\n    at async processItemsInParallel (C:\\Projects\\workiz\\backend\\controllers\\quoteController.js:3428:13)\n    at async lookupPricesWithoutSaving (C:\\Projects\\workiz\\backend\\controllers\\quoteController.js:3436:26)"}
2025-08-29 18:24:30 [[33m[33mwarn[33m[39m] [general]: [ScraperService Fallback] Primary search failed or yielded no results. Attempting fallback: Platt (ID: 682bdb1305cc8e619ad861e0) | {"service":"workiz-api"}
2025-08-29 18:24:30 [[32m[32minfo[32m[39m] [general]: [ScraperService] Trying primary query: "price intersystem bonding bridge" | {"service":"workiz-api"}
2025-08-29 18:24:30 [[33m[33mwarn[33m[39m] [general]: [ScraperService] Platt scraper (ID: 682bdb1305cc8e619ad861e0) is likely blocked. Skipping live search for query: "price intersystem bonding bridge". | {"service":"workiz-api"}
2025-08-29 18:24:30 [[32m[32minfo[32m[39m] [general]: [ScraperService] Trying variation (alternative_brand, confidence: 0.6): "price intersystem bonding bridgeneral electric" | {"service":"workiz-api"}
2025-08-29 18:24:30 [[33m[33mwarn[33m[39m] [general]: [ScraperService] Platt scraper (ID: 682bdb1305cc8e619ad861e0) is likely blocked. Skipping live search for query: "price intersystem bonding bridgeneral electric". | {"service":"workiz-api"}
2025-08-29 18:24:30 [[33m[33mwarn[33m[39m] [general]: [ScraperService] All query variations failed for source 682bdb1305cc8e619ad861e0 | {"service":"workiz-api"}
2025-08-29 18:24:30 [[33m[33mwarn[33m[39m] [general]: [ScraperService Fallback] Platt search also returned no results for query: "price intersystem bonding bridge". | {"service":"workiz-api"}
2025-08-29 18:24:30 [[33m[33mwarn[33m[39m] [general]: [ScraperService Fallback] Both primary (Home Depot) and fallback (Platt) failed or returned no results for query: "price intersystem bonding bridge". | {"service":"workiz-api"}
2025-08-29 18:24:30 [[34m[34mdebug[34m[39m] [general]: [ScraperService] searchMaterials (Enhanced with Query Transformation) called with query: "price 1lb duct seal compound", options: {"skipCache":false,"limit":1,"timeout":20000} | {"service":"workiz-api"}
2025-08-29 18:24:30 [[34m[34mdebug[34m[39m] [general]: [QueryTransformer] Transforming query: "price 1lb duct seal compound" | {"service":"workiz-api"}
2025-08-29 18:24:30 [[34m[34mdebug[34m[39m] [general]: [QueryTransformer] Generated 1 variations | {"service":"workiz-api","brand":null,"type":"amp","confidence":0.5}
2025-08-29 18:24:30 [[32m[32minfo[32m[39m] [general]: [ScraperService] Query transformation generated 1 variations | {"service":"workiz-api","originalQuery":"price 1lb duct seal compound","detectedBrand":null,"detectedType":"amp","confidence":0.5}
2025-08-29 18:24:30 [[32m[32minfo[32m[39m] [general]: [ScraperService Fallback] Attempting primary search: Home Depot (ID: 682bdb0e477e692811d861e0) | {"service":"workiz-api"}
2025-08-29 18:24:30 [[32m[32minfo[32m[39m] [general]: [ScraperService] Trying primary query: "price 1lb duct seal compound" | {"service":"workiz-api"}
2025-08-29 18:24:30 [[34m[34mdebug[34m[39m] [general]: [ScraperService] Calling searchByDescription on Home Depot for query: "price 1lb duct seal compound" | {"service":"workiz-api"}
2025-08-29 18:24:30 [[33m[33mwarn[33m[39m] [general]: [Crawl4AI] Circuit breaker OPEN for "home depot-price 1lb duct seal compound". Retry in 178s | {"service":"workiz-api"}
2025-08-29 18:24:30 [[31m[31merror[31m[39m] [general]: [ScraperService] Error searching in Home Depot for query "price 1lb duct seal compound": Circuit breaker is OPEN. Retry in 178 seconds. | {"service":"workiz-api","stack":"Error: Circuit breaker is OPEN. Retry in 178 seconds.\n    at CircuitBreaker.execute (C:\\Projects\\workiz\\backend\\utils\\circuitBreaker.js:40:15)\n    at ScraperService.searchBySource (C:\\Projects\\workiz\\backend\\scrapers\\ScraperService.js:427:56)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async ScraperService.searchBySourceWithVariations (C:\\Projects\\workiz\\backend\\scrapers\\ScraperService.js:336:19)\n    at async ScraperService.searchMaterials (C:\\Projects\\workiz\\backend\\scrapers\\ScraperService.js:243:19)\n    at async processItem (C:\\Projects\\workiz\\backend\\controllers\\quoteController.js:3318:37)\n    at async processItemsInParallel (C:\\Projects\\workiz\\backend\\controllers\\quoteController.js:3428:13)\n    at async lookupPricesWithoutSaving (C:\\Projects\\workiz\\backend\\controllers\\quoteController.js:3436:26)"}
2025-08-29 18:24:30 [[31m[31merror[31m[39m] [general]: [ScraperService Fallback] Error searching primary source (Home Depot ID: 682bdb0e477e692811d861e0): Circuit breaker is OPEN. Retry in 178 seconds. | {"service":"workiz-api","stack":"Error: Circuit breaker is OPEN. Retry in 178 seconds.\n    at CircuitBreaker.execute (C:\\Projects\\workiz\\backend\\utils\\circuitBreaker.js:40:15)\n    at ScraperService.searchBySource (C:\\Projects\\workiz\\backend\\scrapers\\ScraperService.js:427:56)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async ScraperService.searchBySourceWithVariations (C:\\Projects\\workiz\\backend\\scrapers\\ScraperService.js:336:19)\n    at async ScraperService.searchMaterials (C:\\Projects\\workiz\\backend\\scrapers\\ScraperService.js:243:19)\n    at async processItem (C:\\Projects\\workiz\\backend\\controllers\\quoteController.js:3318:37)\n    at async processItemsInParallel (C:\\Projects\\workiz\\backend\\controllers\\quoteController.js:3428:13)\n    at async lookupPricesWithoutSaving (C:\\Projects\\workiz\\backend\\controllers\\quoteController.js:3436:26)"}
2025-08-29 18:24:30 [[33m[33mwarn[33m[39m] [general]: [ScraperService Fallback] Primary search failed or yielded no results. Attempting fallback: Platt (ID: 682bdb1305cc8e619ad861e0) | {"service":"workiz-api"}
2025-08-29 18:24:30 [[32m[32minfo[32m[39m] [general]: [ScraperService] Trying primary query: "price 1lb duct seal compound" | {"service":"workiz-api"}
2025-08-29 18:24:30 [[33m[33mwarn[33m[39m] [general]: [ScraperService] Platt scraper (ID: 682bdb1305cc8e619ad861e0) is likely blocked. Skipping live search for query: "price 1lb duct seal compound". | {"service":"workiz-api"}
2025-08-29 18:24:30 [[32m[32minfo[32m[39m] [general]: [ScraperService] Trying variation (generic_with_specs, confidence: 0.7): "amp" | {"service":"workiz-api"}
2025-08-29 18:24:30 [[33m[33mwarn[33m[39m] [general]: [ScraperService] Platt scraper (ID: 682bdb1305cc8e619ad861e0) is likely blocked. Skipping live search for query: "amp". | {"service":"workiz-api"}
2025-08-29 18:24:30 [[33m[33mwarn[33m[39m] [general]: [ScraperService] All query variations failed for source 682bdb1305cc8e619ad861e0 | {"service":"workiz-api"}
2025-08-29 18:24:30 [[33m[33mwarn[33m[39m] [general]: [ScraperService Fallback] Platt search also returned no results for query: "price 1lb duct seal compound". | {"service":"workiz-api"}
2025-08-29 18:24:30 [[33m[33mwarn[33m[39m] [general]: [ScraperService Fallback] Both primary (Home Depot) and fallback (Platt) failed or returned no results for query: "price 1lb duct seal compound". | {"service":"workiz-api"}
2025-08-29 18:24:30 [[34m[34mdebug[34m[39m] [general]: [ScraperService] searchMaterials (Enhanced with Query Transformation) called with query: "price panel schedule labels", options: {"skipCache":false,"limit":1,"timeout":20000} | {"service":"workiz-api"}
2025-08-29 18:24:30 [[34m[34mdebug[34m[39m] [general]: [QueryTransformer] Transforming query: "price panel schedule labels" | {"service":"workiz-api"}
2025-08-29 18:24:30 [[34m[34mdebug[34m[39m] [general]: [QueryTransformer] Generated 2 variations | {"service":"workiz-api","brand":"eaton","type":"panel","confidence":0.8}
2025-08-29 18:24:30 [[32m[32minfo[32m[39m] [general]: [ScraperService] Query transformation generated 2 variations | {"service":"workiz-api","originalQuery":"price panel schedule labels","detectedBrand":"eaton","detectedType":"panel","confidence":0.8}
2025-08-29 18:24:31 [[32m[32minfo[32m[39m] [general]: [ScraperService Fallback] Attempting primary search: Home Depot (ID: 682bdb0e477e692811d861e0) | {"service":"workiz-api"}
2025-08-29 18:24:31 [[32m[32minfo[32m[39m] [general]: [ScraperService] Trying primary query: "price panel schedule labels" | {"service":"workiz-api"}
2025-08-29 18:24:31 [[34m[34mdebug[34m[39m] [general]: [ScraperService] Calling searchByDescription on Home Depot for query: "price panel schedule labels" | {"service":"workiz-api"}
2025-08-29 18:24:31 [[33m[33mwarn[33m[39m] [general]: [Crawl4AI] Circuit breaker OPEN for "home depot-price panel schedule labels". Retry in 178s | {"service":"workiz-api"}
2025-08-29 18:24:31 [[31m[31merror[31m[39m] [general]: [ScraperService] Error searching in Home Depot for query "price panel schedule labels": Circuit breaker is OPEN. Retry in 178 seconds. | {"service":"workiz-api","stack":"Error: Circuit breaker is OPEN. Retry in 178 seconds.\n    at CircuitBreaker.execute (C:\\Projects\\workiz\\backend\\utils\\circuitBreaker.js:40:15)\n    at ScraperService.searchBySource (C:\\Projects\\workiz\\backend\\scrapers\\ScraperService.js:427:56)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async ScraperService.searchBySourceWithVariations (C:\\Projects\\workiz\\backend\\scrapers\\ScraperService.js:336:19)\n    at async ScraperService.searchMaterials (C:\\Projects\\workiz\\backend\\scrapers\\ScraperService.js:243:19)\n    at async processItem (C:\\Projects\\workiz\\backend\\controllers\\quoteController.js:3318:37)\n    at async processItemsInParallel (C:\\Projects\\workiz\\backend\\controllers\\quoteController.js:3428:13)\n    at async lookupPricesWithoutSaving (C:\\Projects\\workiz\\backend\\controllers\\quoteController.js:3436:26)"}
2025-08-29 18:24:31 [[31m[31merror[31m[39m] [general]: [ScraperService Fallback] Error searching primary source (Home Depot ID: 682bdb0e477e692811d861e0): Circuit breaker is OPEN. Retry in 178 seconds. | {"service":"workiz-api","stack":"Error: Circuit breaker is OPEN. Retry in 178 seconds.\n    at CircuitBreaker.execute (C:\\Projects\\workiz\\backend\\utils\\circuitBreaker.js:40:15)\n    at ScraperService.searchBySource (C:\\Projects\\workiz\\backend\\scrapers\\ScraperService.js:427:56)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async ScraperService.searchBySourceWithVariations (C:\\Projects\\workiz\\backend\\scrapers\\ScraperService.js:336:19)\n    at async ScraperService.searchMaterials (C:\\Projects\\workiz\\backend\\scrapers\\ScraperService.js:243:19)\n    at async processItem (C:\\Projects\\workiz\\backend\\controllers\\quoteController.js:3318:37)\n    at async processItemsInParallel (C:\\Projects\\workiz\\backend\\controllers\\quoteController.js:3428:13)\n    at async lookupPricesWithoutSaving (C:\\Projects\\workiz\\backend\\controllers\\quoteController.js:3436:26)"}
2025-08-29 18:24:31 [[33m[33mwarn[33m[39m] [general]: [ScraperService Fallback] Primary search failed or yielded no results. Attempting fallback: Platt (ID: 682bdb1305cc8e619ad861e0) | {"service":"workiz-api"}
2025-08-29 18:24:31 [[32m[32minfo[32m[39m] [general]: [ScraperService] Trying primary query: "price panel schedule labels" | {"service":"workiz-api"}
2025-08-29 18:24:31 [[33m[33mwarn[33m[39m] [general]: [ScraperService] Platt scraper (ID: 682bdb1305cc8e619ad861e0) is likely blocked. Skipping live search for query: "price panel schedule labels". | {"service":"workiz-api"}
2025-08-29 18:24:31 [[32m[32minfo[32m[39m] [general]: [ScraperService] Trying variation (brand_type_specs, confidence: 0.9): "eaton panel" | {"service":"workiz-api"}
2025-08-29 18:24:31 [[33m[33mwarn[33m[39m] [general]: [ScraperService] Platt scraper (ID: 682bdb1305cc8e619ad861e0) is likely blocked. Skipping live search for query: "eaton panel". | {"service":"workiz-api"}
2025-08-29 18:24:31 [[32m[32minfo[32m[39m] [general]: [ScraperService] Trying variation (generic_with_specs, confidence: 0.7): "panel" | {"service":"workiz-api"}
2025-08-29 18:24:31 [[33m[33mwarn[33m[39m] [general]: [ScraperService] Platt scraper (ID: 682bdb1305cc8e619ad861e0) is likely blocked. Skipping live search for query: "panel". | {"service":"workiz-api"}
2025-08-29 18:24:31 [[33m[33mwarn[33m[39m] [general]: [ScraperService] All query variations failed for source 682bdb1305cc8e619ad861e0 | {"service":"workiz-api"}
2025-08-29 18:24:31 [[33m[33mwarn[33m[39m] [general]: [ScraperService Fallback] Platt search also returned no results for query: "price panel schedule labels". | {"service":"workiz-api"}
2025-08-29 18:24:32 [[33m[33mwarn[33m[39m] [general]: [ScraperService Fallback] Both primary (Home Depot) and fallback (Platt) failed or returned no results for query: "price panel schedule labels". | {"service":"workiz-api"}
2025-08-29 18:24:32 [[34m[34mdebug[34m[39m] [general]: [ScraperService] searchMaterials (Enhanced with Query Transformation) called with query: "price stucco patch mix", options: {"skipCache":false,"limit":1,"timeout":20000} | {"service":"workiz-api"}
2025-08-29 18:24:32 [[34m[34mdebug[34m[39m] [general]: [QueryTransformer] Transforming query: "price stucco patch mix" | {"service":"workiz-api"}
2025-08-29 18:24:32 [[34m[34mdebug[34m[39m] [general]: [QueryTransformer] Generated 2 variations | {"service":"workiz-api","brand":"eaton","type":"amp","confidence":0.8}
2025-08-29 18:24:32 [[32m[32minfo[32m[39m] [general]: [ScraperService] Query transformation generated 2 variations | {"service":"workiz-api","originalQuery":"price stucco patch mix","detectedBrand":"eaton","detectedType":"amp","confidence":0.8}
2025-08-29 18:24:32 [[32m[32minfo[32m[39m] [general]: [ScraperService Fallback] Attempting primary search: Home Depot (ID: 682bdb0e477e692811d861e0) | {"service":"workiz-api"}
2025-08-29 18:24:33 [[32m[32minfo[32m[39m] [general]: [ScraperService] Trying primary query: "price stucco patch mix" | {"service":"workiz-api"}
2025-08-29 18:24:33 [[34m[34mdebug[34m[39m] [general]: [ScraperService] Calling searchByDescription on Home Depot for query: "price stucco patch mix" | {"service":"workiz-api"}
2025-08-29 18:24:33 [[33m[33mwarn[33m[39m] [general]: [Crawl4AI] Circuit breaker OPEN for "home depot-price stucco patch mix". Retry in 176s | {"service":"workiz-api"}
2025-08-29 18:24:33 [[31m[31merror[31m[39m] [general]: [ScraperService] Error searching in Home Depot for query "price stucco patch mix": Circuit breaker is OPEN. Retry in 176 seconds. | {"service":"workiz-api","stack":"Error: Circuit breaker is OPEN. Retry in 176 seconds.\n    at CircuitBreaker.execute (C:\\Projects\\workiz\\backend\\utils\\circuitBreaker.js:40:15)\n    at ScraperService.searchBySource (C:\\Projects\\workiz\\backend\\scrapers\\ScraperService.js:427:56)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async ScraperService.searchBySourceWithVariations (C:\\Projects\\workiz\\backend\\scrapers\\ScraperService.js:336:19)\n    at async ScraperService.searchMaterials (C:\\Projects\\workiz\\backend\\scrapers\\ScraperService.js:243:19)\n    at async processItem (C:\\Projects\\workiz\\backend\\controllers\\quoteController.js:3318:37)\n    at async processItemsInParallel (C:\\Projects\\workiz\\backend\\controllers\\quoteController.js:3428:13)\n    at async lookupPricesWithoutSaving (C:\\Projects\\workiz\\backend\\controllers\\quoteController.js:3436:26)"}
2025-08-29 18:24:33 [[31m[31merror[31m[39m] [general]: [ScraperService Fallback] Error searching primary source (Home Depot ID: 682bdb0e477e692811d861e0): Circuit breaker is OPEN. Retry in 176 seconds. | {"service":"workiz-api","stack":"Error: Circuit breaker is OPEN. Retry in 176 seconds.\n    at CircuitBreaker.execute (C:\\Projects\\workiz\\backend\\utils\\circuitBreaker.js:40:15)\n    at ScraperService.searchBySource (C:\\Projects\\workiz\\backend\\scrapers\\ScraperService.js:427:56)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async ScraperService.searchBySourceWithVariations (C:\\Projects\\workiz\\backend\\scrapers\\ScraperService.js:336:19)\n    at async ScraperService.searchMaterials (C:\\Projects\\workiz\\backend\\scrapers\\ScraperService.js:243:19)\n    at async processItem (C:\\Projects\\workiz\\backend\\controllers\\quoteController.js:3318:37)\n    at async processItemsInParallel (C:\\Projects\\workiz\\backend\\controllers\\quoteController.js:3428:13)\n    at async lookupPricesWithoutSaving (C:\\Projects\\workiz\\backend\\controllers\\quoteController.js:3436:26)"}
2025-08-29 18:24:33 [[33m[33mwarn[33m[39m] [general]: [ScraperService Fallback] Primary search failed or yielded no results. Attempting fallback: Platt (ID: 682bdb1305cc8e619ad861e0) | {"service":"workiz-api"}
2025-08-29 18:24:33 [[32m[32minfo[32m[39m] [general]: [ScraperService] Trying primary query: "price stucco patch mix" | {"service":"workiz-api"}
2025-08-29 18:24:33 [[33m[33mwarn[33m[39m] [general]: [ScraperService] Platt scraper (ID: 682bdb1305cc8e619ad861e0) is likely blocked. Skipping live search for query: "price stucco patch mix". | {"service":"workiz-api"}
2025-08-29 18:24:33 [[32m[32minfo[32m[39m] [general]: [ScraperService] Trying variation (brand_type_specs, confidence: 0.9): "eaton amp" | {"service":"workiz-api"}
2025-08-29 18:24:33 [[33m[33mwarn[33m[39m] [general]: [ScraperService] Platt scraper (ID: 682bdb1305cc8e619ad861e0) is likely blocked. Skipping live search for query: "eaton amp". | {"service":"workiz-api"}
2025-08-29 18:24:33 [[32m[32minfo[32m[39m] [general]: [ScraperService] Trying variation (generic_with_specs, confidence: 0.7): "amp" | {"service":"workiz-api"}
2025-08-29 18:24:33 [[33m[33mwarn[33m[39m] [general]: [ScraperService] Platt scraper (ID: 682bdb1305cc8e619ad861e0) is likely blocked. Skipping live search for query: "amp". | {"service":"workiz-api"}
2025-08-29 18:24:33 [[33m[33mwarn[33m[39m] [general]: [ScraperService] All query variations failed for source 682bdb1305cc8e619ad861e0 | {"service":"workiz-api"}
2025-08-29 18:24:33 [[33m[33mwarn[33m[39m] [general]: [ScraperService Fallback] Platt search also returned no results for query: "price stucco patch mix". | {"service":"workiz-api"}
2025-08-29 18:24:33 [[33m[33mwarn[33m[39m] [general]: [ScraperService Fallback] Both primary (Home Depot) and fallback (Platt) failed or returned no results for query: "price stucco patch mix". | {"service":"workiz-api"}
2025-08-29 18:24:33 [[34m[34mdebug[34m[39m] [general]: [ScraperService] searchMaterials (Enhanced with Query Transformation) called with query: "price exterior paintable silicone caulk", options: {"skipCache":false,"limit":1,"timeout":20000} | {"service":"workiz-api"}
2025-08-29 18:24:33 [[34m[34mdebug[34m[39m] [general]: [QueryTransformer] Transforming query: "price exterior paintable silicone caulk" | {"service":"workiz-api"}
2025-08-29 18:24:33 [[34m[34mdebug[34m[39m] [general]: [QueryTransformer] Generated 1 variations | {"service":"workiz-api","brand":null,"type":"amp","confidence":0.5}
2025-08-29 18:24:34 [[32m[32minfo[32m[39m] [general]: [ScraperService] Query transformation generated 1 variations | {"service":"workiz-api","originalQuery":"price exterior paintable silicone caulk","detectedBrand":null,"detectedType":"amp","confidence":0.5}
2025-08-29 18:24:34 [[32m[32minfo[32m[39m] [general]: [ScraperService Fallback] Attempting primary search: Home Depot (ID: 682bdb0e477e692811d861e0) | {"service":"workiz-api"}
2025-08-29 18:24:34 [[32m[32minfo[32m[39m] [general]: [ScraperService] Trying primary query: "price exterior paintable silicone caulk" | {"service":"workiz-api"}
2025-08-29 18:24:34 [[34m[34mdebug[34m[39m] [general]: [ScraperService] Calling searchByDescription on Home Depot for query: "price exterior paintable silicone caulk" | {"service":"workiz-api"}
2025-08-29 18:24:34 [[33m[33mwarn[33m[39m] [general]: [Crawl4AI] Circuit breaker OPEN for "home depot-price exterior paintable silicone caulk". Retry in 175s | {"service":"workiz-api"}
2025-08-29 18:24:34 [[31m[31merror[31m[39m] [general]: [ScraperService] Error searching in Home Depot for query "price exterior paintable silicone caulk": Circuit breaker is OPEN. Retry in 175 seconds. | {"service":"workiz-api","stack":"Error: Circuit breaker is OPEN. Retry in 175 seconds.\n    at CircuitBreaker.execute (C:\\Projects\\workiz\\backend\\utils\\circuitBreaker.js:40:15)\n    at ScraperService.searchBySource (C:\\Projects\\workiz\\backend\\scrapers\\ScraperService.js:427:56)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async ScraperService.searchBySourceWithVariations (C:\\Projects\\workiz\\backend\\scrapers\\ScraperService.js:336:19)\n    at async ScraperService.searchMaterials (C:\\Projects\\workiz\\backend\\scrapers\\ScraperService.js:243:19)\n    at async processItem (C:\\Projects\\workiz\\backend\\controllers\\quoteController.js:3318:37)\n    at async processItemsInParallel (C:\\Projects\\workiz\\backend\\controllers\\quoteController.js:3428:13)\n    at async lookupPricesWithoutSaving (C:\\Projects\\workiz\\backend\\controllers\\quoteController.js:3436:26)"}
2025-08-29 18:24:34 [[31m[31merror[31m[39m] [general]: [ScraperService Fallback] Error searching primary source (Home Depot ID: 682bdb0e477e692811d861e0): Circuit breaker is OPEN. Retry in 175 seconds. | {"service":"workiz-api","stack":"Error: Circuit breaker is OPEN. Retry in 175 seconds.\n    at CircuitBreaker.execute (C:\\Projects\\workiz\\backend\\utils\\circuitBreaker.js:40:15)\n    at ScraperService.searchBySource (C:\\Projects\\workiz\\backend\\scrapers\\ScraperService.js:427:56)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async ScraperService.searchBySourceWithVariations (C:\\Projects\\workiz\\backend\\scrapers\\ScraperService.js:336:19)\n    at async ScraperService.searchMaterials (C:\\Projects\\workiz\\backend\\scrapers\\ScraperService.js:243:19)\n    at async processItem (C:\\Projects\\workiz\\backend\\controllers\\quoteController.js:3318:37)\n    at async processItemsInParallel (C:\\Projects\\workiz\\backend\\controllers\\quoteController.js:3428:13)\n    at async lookupPricesWithoutSaving (C:\\Projects\\workiz\\backend\\controllers\\quoteController.js:3436:26)"}
2025-08-29 18:24:34 [[33m[33mwarn[33m[39m] [general]: [ScraperService Fallback] Primary search failed or yielded no results. Attempting fallback: Platt (ID: 682bdb1305cc8e619ad861e0) | {"service":"workiz-api"}
2025-08-29 18:24:34 [[32m[32minfo[32m[39m] [general]: [ScraperService] Trying primary query: "price exterior paintable silicone caulk" | {"service":"workiz-api"}
2025-08-29 18:24:34 [[33m[33mwarn[33m[39m] [general]: [ScraperService] Platt scraper (ID: 682bdb1305cc8e619ad861e0) is likely blocked. Skipping live search for query: "price exterior paintable silicone caulk". | {"service":"workiz-api"}
2025-08-29 18:24:34 [[32m[32minfo[32m[39m] [general]: [ScraperService] Trying variation (generic_with_specs, confidence: 0.7): "amp" | {"service":"workiz-api"}
2025-08-29 18:24:34 [[33m[33mwarn[33m[39m] [general]: [ScraperService] Platt scraper (ID: 682bdb1305cc8e619ad861e0) is likely blocked. Skipping live search for query: "amp". | {"service":"workiz-api"}
2025-08-29 18:24:34 [[33m[33mwarn[33m[39m] [general]: [ScraperService] All query variations failed for source 682bdb1305cc8e619ad861e0 | {"service":"workiz-api"}
2025-08-29 18:24:34 [[33m[33mwarn[33m[39m] [general]: [ScraperService Fallback] Platt search also returned no results for query: "price exterior paintable silicone caulk". | {"service":"workiz-api"}
2025-08-29 18:24:34 [[33m[33mwarn[33m[39m] [general]: [ScraperService Fallback] Both primary (Home Depot) and fallback (Platt) failed or returned no results for query: "price exterior paintable silicone caulk". | {"service":"workiz-api"}
2025-08-29 18:24:34 [[33m[33mwarn[33m[39m] [general]: Crawl4AI process gracefully terminated after 20000ms for URL: https://www.homedepot.com/s/price%20Siemens%20QP%2020A%20single%20pole%20breaker | {"service":"workiz-api"}
2025-08-29 18:24:34 [[31m[31merror[31m[39m] [general]: Crawl4AI process timed out after 20000ms for URL: https://www.homedepot.com/s/price%20Siemens%20QP%2020A%20single%20pole%20breaker | {"service":"workiz-api"}
2025-08-29 18:24:34 [[31m[31merror[31m[39m] [general]: [HD Crawl4AI Scraper] Error searching for "price Siemens QP 20A single pole breaker": Crawl operation timed out after 20000ms | {"service":"workiz-api","stack":"Error: Crawl operation timed out after 20000ms\n    at Timeout._onTimeout (C:\\Projects\\workiz\\backend\\scrapers\\crawl4ai\\crawl4ai-service.js:709:18)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)"}
2025-08-29 18:24:34 [[31m[31merror[31m[39m] [general]: [Home Depot Scraper] Error searching for "price Siemens QP 20A single pole breaker": Crawl operation timed out after 20000ms | {"service":"workiz-api","stack":"Error: Crawl operation timed out after 20000ms\n    at Timeout._onTimeout (C:\\Projects\\workiz\\backend\\scrapers\\crawl4ai\\crawl4ai-service.js:709:18)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)"}
2025-08-29 18:24:34 [[33m[33mwarn[33m[39m] [general]: [Crawl4AI] Circuit breaker OPENED after 6 failures (0 timeouts) for "home depot-price Siemens QP 20A single pole breaker". Next attempt in 180s | {"service":"workiz-api"}
2025-08-29 18:24:34 [[31m[31merror[31m[39m] [general]: [ScraperService] Error searching in Home Depot for query "price Siemens QP 20A single pole breaker": Crawl operation timed out after 20000ms | {"service":"workiz-api","stack":"Error: Crawl operation timed out after 20000ms\n    at Timeout._onTimeout (C:\\Projects\\workiz\\backend\\scrapers\\crawl4ai\\crawl4ai-service.js:709:18)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)"}
2025-08-29 18:24:34 [[31m[31merror[31m[39m] [general]: [ScraperService Fallback] Error searching primary source (Home Depot ID: 682bdb0e477e692811d861e0): Crawl operation timed out after 20000ms | {"service":"workiz-api","stack":"Error: Crawl operation timed out after 20000ms\n    at Timeout._onTimeout (C:\\Projects\\workiz\\backend\\scrapers\\crawl4ai\\crawl4ai-service.js:709:18)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)"}
2025-08-29 18:24:34 [[33m[33mwarn[33m[39m] [general]: [ScraperService Fallback] Primary search failed or yielded no results. Attempting fallback: Platt (ID: 682bdb1305cc8e619ad861e0) | {"service":"workiz-api"}
2025-08-29 18:24:34 [[32m[32minfo[32m[39m] [general]: [ScraperService] Trying primary query: "price Siemens QP 20A single pole breaker" | {"service":"workiz-api"}
2025-08-29 18:24:35 [[33m[33mwarn[33m[39m] [general]: [ScraperService] Platt scraper (ID: 682bdb1305cc8e619ad861e0) is likely blocked. Skipping live search for query: "price Siemens QP 20A single pole breaker". | {"service":"workiz-api"}
2025-08-29 18:24:35 [[32m[32minfo[32m[39m] [general]: [ScraperService] Trying variation (brand_type_specs, confidence: 0.9): "siemens breaker 20A" | {"service":"workiz-api"}
2025-08-29 18:24:35 [[33m[33mwarn[33m[39m] [general]: [ScraperService] Platt scraper (ID: 682bdb1305cc8e619ad861e0) is likely blocked. Skipping live search for query: "siemens breaker 20A". | {"service":"workiz-api"}
2025-08-29 18:24:35 [[32m[32minfo[32m[39m] [general]: [ScraperService] Trying variation (generic_with_specs, confidence: 0.7): "breaker 20 amp" | {"service":"workiz-api"}
2025-08-29 18:24:35 [[33m[33mwarn[33m[39m] [general]: [ScraperService] Platt scraper (ID: 682bdb1305cc8e619ad861e0) is likely blocked. Skipping live search for query: "breaker 20 amp". | {"service":"workiz-api"}
2025-08-29 18:24:35 [[32m[32minfo[32m[39m] [general]: [ScraperService] Trying variation (alternative_brand, confidence: 0.6): "price sie qp 20a single pole breaker" | {"service":"workiz-api"}
2025-08-29 18:24:35 [[33m[33mwarn[33m[39m] [general]: [ScraperService] Platt scraper (ID: 682bdb1305cc8e619ad861e0) is likely blocked. Skipping live search for query: "price sie qp 20a single pole breaker". | {"service":"workiz-api"}
2025-08-29 18:24:35 [[33m[33mwarn[33m[39m] [general]: [ScraperService] All query variations failed for source 682bdb1305cc8e619ad861e0 | {"service":"workiz-api"}
2025-08-29 18:24:35 [[33m[33mwarn[33m[39m] [general]: [ScraperService Fallback] Platt search also returned no results for query: "price Siemens QP 20A single pole breaker". | {"service":"workiz-api"}
2025-08-29 18:24:35 [[33m[33mwarn[33m[39m] [general]: [ScraperService Fallback] Both primary (Home Depot) and fallback (Platt) failed or returned no results for query: "price Siemens QP 20A single pole breaker". | {"service":"workiz-api"}
2025-08-29 18:24:35 [[33m[33mwarn[33m[39m] [general]: Crawl4AI process gracefully terminated after 20000ms for URL: https://www.homedepot.com/s/price%20Siemens%20Q230%2030A%20double%20pole%20breaker | {"service":"workiz-api"}
2025-08-29 18:24:35 [[31m[31merror[31m[39m] [general]: Crawl4AI process timed out after 20000ms for URL: https://www.homedepot.com/s/price%20Siemens%20Q230%2030A%20double%20pole%20breaker | {"service":"workiz-api"}
2025-08-29 18:24:35 [[31m[31merror[31m[39m] [general]: [HD Crawl4AI Scraper] Error searching for "price Siemens Q230 30A double pole breaker": Crawl operation timed out after 20000ms | {"service":"workiz-api","stack":"Error: Crawl operation timed out after 20000ms\n    at Timeout._onTimeout (C:\\Projects\\workiz\\backend\\scrapers\\crawl4ai\\crawl4ai-service.js:709:18)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)"}
2025-08-29 18:24:35 [[31m[31merror[31m[39m] [general]: [Home Depot Scraper] Error searching for "price Siemens Q230 30A double pole breaker": Crawl operation timed out after 20000ms | {"service":"workiz-api","stack":"Error: Crawl operation timed out after 20000ms\n    at Timeout._onTimeout (C:\\Projects\\workiz\\backend\\scrapers\\crawl4ai\\crawl4ai-service.js:709:18)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)"}
2025-08-29 18:24:35 [[33m[33mwarn[33m[39m] [general]: [Crawl4AI] Circuit breaker OPENED after 7 failures (0 timeouts) for "home depot-price Siemens Q230 30A double pole breaker". Next attempt in 180s | {"service":"workiz-api"}
2025-08-29 18:24:35 [[31m[31merror[31m[39m] [general]: [ScraperService] Error searching in Home Depot for query "price Siemens Q230 30A double pole breaker": Crawl operation timed out after 20000ms | {"service":"workiz-api","stack":"Error: Crawl operation timed out after 20000ms\n    at Timeout._onTimeout (C:\\Projects\\workiz\\backend\\scrapers\\crawl4ai\\crawl4ai-service.js:709:18)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)"}
2025-08-29 18:24:35 [[31m[31merror[31m[39m] [general]: [ScraperService Fallback] Error searching primary source (Home Depot ID: 682bdb0e477e692811d861e0): Crawl operation timed out after 20000ms | {"service":"workiz-api","stack":"Error: Crawl operation timed out after 20000ms\n    at Timeout._onTimeout (C:\\Projects\\workiz\\backend\\scrapers\\crawl4ai\\crawl4ai-service.js:709:18)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)"}
2025-08-29 18:24:35 [[33m[33mwarn[33m[39m] [general]: [ScraperService Fallback] Primary search failed or yielded no results. Attempting fallback: Platt (ID: 682bdb1305cc8e619ad861e0) | {"service":"workiz-api"}
2025-08-29 18:24:35 [[32m[32minfo[32m[39m] [general]: [ScraperService] Trying primary query: "price Siemens Q230 30A double pole breaker" | {"service":"workiz-api"}
2025-08-29 18:24:35 [[33m[33mwarn[33m[39m] [general]: [ScraperService] Platt scraper (ID: 682bdb1305cc8e619ad861e0) is likely blocked. Skipping live search for query: "price Siemens Q230 30A double pole breaker". | {"service":"workiz-api"}
2025-08-29 18:24:35 [[32m[32minfo[32m[39m] [general]: [ScraperService] Trying variation (brand_type_specs, confidence: 0.9): "siemens breaker 30A" | {"service":"workiz-api"}
2025-08-29 18:24:35 [[33m[33mwarn[33m[39m] [general]: [ScraperService] Platt scraper (ID: 682bdb1305cc8e619ad861e0) is likely blocked. Skipping live search for query: "siemens breaker 30A". | {"service":"workiz-api"}
2025-08-29 18:24:35 [[32m[32minfo[32m[39m] [general]: [ScraperService] Trying variation (generic_with_specs, confidence: 0.7): "breaker 30 amp" | {"service":"workiz-api"}
2025-08-29 18:24:35 [[33m[33mwarn[33m[39m] [general]: [ScraperService] Platt scraper (ID: 682bdb1305cc8e619ad861e0) is likely blocked. Skipping live search for query: "breaker 30 amp". | {"service":"workiz-api"}
2025-08-29 18:24:35 [[32m[32minfo[32m[39m] [general]: [ScraperService] Trying variation (alternative_brand, confidence: 0.6): "price sie q230 30a double pole breaker" | {"service":"workiz-api"}
2025-08-29 18:24:35 [[33m[33mwarn[33m[39m] [general]: [ScraperService] Platt scraper (ID: 682bdb1305cc8e619ad861e0) is likely blocked. Skipping live search for query: "price sie q230 30a double pole breaker". | {"service":"workiz-api"}
2025-08-29 18:24:35 [[33m[33mwarn[33m[39m] [general]: [ScraperService] All query variations failed for source 682bdb1305cc8e619ad861e0 | {"service":"workiz-api"}
2025-08-29 18:24:35 [[33m[33mwarn[33m[39m] [general]: [ScraperService Fallback] Platt search also returned no results for query: "price Siemens Q230 30A double pole breaker". | {"service":"workiz-api"}
2025-08-29 18:24:35 [[33m[33mwarn[33m[39m] [general]: [ScraperService Fallback] Both primary (Home Depot) and fallback (Platt) failed or returned no results for query: "price Siemens Q230 30A double pole breaker". | {"service":"workiz-api"}
2025-08-29 18:24:35 [[33m[33mwarn[33m[39m] [general]: Crawl4AI process gracefully terminated after 20000ms for URL: https://www.homedepot.com/s/price%20Siemens%20Q250%2050A%20double%20pole%20breaker | {"service":"workiz-api"}
2025-08-29 18:24:35 [[31m[31merror[31m[39m] [general]: Crawl4AI process timed out after 20000ms for URL: https://www.homedepot.com/s/price%20Siemens%20Q250%2050A%20double%20pole%20breaker | {"service":"workiz-api"}
2025-08-29 18:24:35 [[31m[31merror[31m[39m] [general]: [HD Crawl4AI Scraper] Error searching for "price Siemens Q250 50A double pole breaker": Crawl operation timed out after 20000ms | {"service":"workiz-api","stack":"Error: Crawl operation timed out after 20000ms\n    at Timeout._onTimeout (C:\\Projects\\workiz\\backend\\scrapers\\crawl4ai\\crawl4ai-service.js:709:18)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)"}
2025-08-29 18:24:35 [[31m[31merror[31m[39m] [general]: [Home Depot Scraper] Error searching for "price Siemens Q250 50A double pole breaker": Crawl operation timed out after 20000ms | {"service":"workiz-api","stack":"Error: Crawl operation timed out after 20000ms\n    at Timeout._onTimeout (C:\\Projects\\workiz\\backend\\scrapers\\crawl4ai\\crawl4ai-service.js:709:18)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)"}
2025-08-29 18:24:35 [[33m[33mwarn[33m[39m] [general]: [Crawl4AI] Circuit breaker OPENED after 8 failures (0 timeouts) for "home depot-price Siemens Q250 50A double pole breaker". Next attempt in 180s | {"service":"workiz-api"}
2025-08-29 18:24:35 [[31m[31merror[31m[39m] [general]: [ScraperService] Error searching in Home Depot for query "price Siemens Q250 50A double pole breaker": Crawl operation timed out after 20000ms | {"service":"workiz-api","stack":"Error: Crawl operation timed out after 20000ms\n    at Timeout._onTimeout (C:\\Projects\\workiz\\backend\\scrapers\\crawl4ai\\crawl4ai-service.js:709:18)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)"}
2025-08-29 18:24:36 [[31m[31merror[31m[39m] [general]: [ScraperService Fallback] Error searching primary source (Home Depot ID: 682bdb0e477e692811d861e0): Crawl operation timed out after 20000ms | {"service":"workiz-api","stack":"Error: Crawl operation timed out after 20000ms\n    at Timeout._onTimeout (C:\\Projects\\workiz\\backend\\scrapers\\crawl4ai\\crawl4ai-service.js:709:18)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)"}
2025-08-29 18:24:36 [[33m[33mwarn[33m[39m] [general]: [ScraperService Fallback] Primary search failed or yielded no results. Attempting fallback: Platt (ID: 682bdb1305cc8e619ad861e0) | {"service":"workiz-api"}
2025-08-29 18:24:36 [[32m[32minfo[32m[39m] [general]: [ScraperService] Trying primary query: "price Siemens Q250 50A double pole breaker" | {"service":"workiz-api"}
2025-08-29 18:24:36 [[33m[33mwarn[33m[39m] [general]: [ScraperService] Platt scraper (ID: 682bdb1305cc8e619ad861e0) is likely blocked. Skipping live search for query: "price Siemens Q250 50A double pole breaker". | {"service":"workiz-api"}
2025-08-29 18:24:36 [[32m[32minfo[32m[39m] [general]: [ScraperService] Trying variation (brand_type_specs, confidence: 0.9): "siemens breaker 50A" | {"service":"workiz-api"}
2025-08-29 18:24:36 [[33m[33mwarn[33m[39m] [general]: [ScraperService] Platt scraper (ID: 682bdb1305cc8e619ad861e0) is likely blocked. Skipping live search for query: "siemens breaker 50A". | {"service":"workiz-api"}
2025-08-29 18:24:36 [[32m[32minfo[32m[39m] [general]: [ScraperService] Trying variation (generic_with_specs, confidence: 0.7): "breaker 50 amp" | {"service":"workiz-api"}
2025-08-29 18:24:36 [[33m[33mwarn[33m[39m] [general]: [ScraperService] Platt scraper (ID: 682bdb1305cc8e619ad861e0) is likely blocked. Skipping live search for query: "breaker 50 amp". | {"service":"workiz-api"}
2025-08-29 18:24:36 [[32m[32minfo[32m[39m] [general]: [ScraperService] Trying variation (alternative_brand, confidence: 0.6): "price sie q250 50a double pole breaker" | {"service":"workiz-api"}
2025-08-29 18:24:36 [[33m[33mwarn[33m[39m] [general]: [ScraperService] Platt scraper (ID: 682bdb1305cc8e619ad861e0) is likely blocked. Skipping live search for query: "price sie q250 50a double pole breaker". | {"service":"workiz-api"}
2025-08-29 18:24:36 [[33m[33mwarn[33m[39m] [general]: [ScraperService] All query variations failed for source 682bdb1305cc8e619ad861e0 | {"service":"workiz-api"}
2025-08-29 18:24:36 [[33m[33mwarn[33m[39m] [general]: [ScraperService Fallback] Platt search also returned no results for query: "price Siemens Q250 50A double pole breaker". | {"service":"workiz-api"}
2025-08-29 18:24:36 [[33m[33mwarn[33m[39m] [general]: [ScraperService Fallback] Both primary (Home Depot) and fallback (Platt) failed or returned no results for query: "price Siemens Q250 50A double pole breaker". | {"service":"workiz-api"}
2025-08-29 18:24:36 [[33m[33mwarn[33m[39m] [general]: Crawl4AI process gracefully terminated after 20000ms for URL: https://www.homedepot.com/s/price%205%2F8%20inch%208%20ft%20ground%20rod | {"service":"workiz-api"}
2025-08-29 18:24:36 [[31m[31merror[31m[39m] [general]: Crawl4AI process timed out after 20000ms for URL: https://www.homedepot.com/s/price%205%2F8%20inch%208%20ft%20ground%20rod | {"service":"workiz-api"}
2025-08-29 18:24:36 [[31m[31merror[31m[39m] [general]: [HD Crawl4AI Scraper] Error searching for "price 5/8 inch 8 ft ground rod": Crawl operation timed out after 20000ms | {"service":"workiz-api","stack":"Error: Crawl operation timed out after 20000ms\n    at Timeout._onTimeout (C:\\Projects\\workiz\\backend\\scrapers\\crawl4ai\\crawl4ai-service.js:709:18)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)"}
2025-08-29 18:24:36 [[31m[31merror[31m[39m] [general]: [Home Depot Scraper] Error searching for "price 5/8 inch 8 ft ground rod": Crawl operation timed out after 20000ms | {"service":"workiz-api","stack":"Error: Crawl operation timed out after 20000ms\n    at Timeout._onTimeout (C:\\Projects\\workiz\\backend\\scrapers\\crawl4ai\\crawl4ai-service.js:709:18)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)"}
2025-08-29 18:24:36 [[33m[33mwarn[33m[39m] [general]: [Crawl4AI] Circuit breaker OPENED after 9 failures (0 timeouts) for "home depot-price 5/8 inch 8 ft ground rod". Next attempt in 180s | {"service":"workiz-api"}
2025-08-29 18:24:36 [[31m[31merror[31m[39m] [general]: [ScraperService] Error searching in Home Depot for query "price 5/8 inch 8 ft ground rod": Crawl operation timed out after 20000ms | {"service":"workiz-api","stack":"Error: Crawl operation timed out after 20000ms\n    at Timeout._onTimeout (C:\\Projects\\workiz\\backend\\scrapers\\crawl4ai\\crawl4ai-service.js:709:18)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)"}
2025-08-29 18:24:36 [[31m[31merror[31m[39m] [general]: [ScraperService Fallback] Error searching primary source (Home Depot ID: 682bdb0e477e692811d861e0): Crawl operation timed out after 20000ms | {"service":"workiz-api","stack":"Error: Crawl operation timed out after 20000ms\n    at Timeout._onTimeout (C:\\Projects\\workiz\\backend\\scrapers\\crawl4ai\\crawl4ai-service.js:709:18)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)"}
2025-08-29 18:24:36 [[33m[33mwarn[33m[39m] [general]: [ScraperService Fallback] Primary search failed or yielded no results. Attempting fallback: Platt (ID: 682bdb1305cc8e619ad861e0) | {"service":"workiz-api"}
2025-08-29 18:24:36 [[32m[32minfo[32m[39m] [general]: [ScraperService] Trying primary query: "price 5/8 inch 8 ft ground rod" | {"service":"workiz-api"}
2025-08-29 18:24:36 [[33m[33mwarn[33m[39m] [general]: [ScraperService] Platt scraper (ID: 682bdb1305cc8e619ad861e0) is likely blocked. Skipping live search for query: "price 5/8 inch 8 ft ground rod". | {"service":"workiz-api"}
2025-08-29 18:24:36 [[33m[33mwarn[33m[39m] [general]: [ScraperService] All query variations failed for source 682bdb1305cc8e619ad861e0 | {"service":"workiz-api"}
2025-08-29 18:24:36 [[33m[33mwarn[33m[39m] [general]: [ScraperService Fallback] Platt search also returned no results for query: "price 5/8 inch 8 ft ground rod". | {"service":"workiz-api"}
2025-08-29 18:24:36 [[33m[33mwarn[33m[39m] [general]: [ScraperService Fallback] Both primary (Home Depot) and fallback (Platt) failed or returned no results for query: "price 5/8 inch 8 ft ground rod". | {"service":"workiz-api"}
2025-08-29 18:24:36 [[32m[32minfo[32m[39m] [general]: Price lookup completed: 0 found, 19 failed, 66492ms total | {"service":"workiz-api"}
