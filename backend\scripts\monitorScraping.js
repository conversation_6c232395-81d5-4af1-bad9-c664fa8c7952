/**
 * monitorScraping.js
 * Script to monitor price scraping logs and alert on high failure rates.
 */

require("dotenv").config({ path: "../.env" }); // Adjust path as needed
const mongoose = require("mongoose");
const cron = require("node-cron");
const logger = require("../utils/logger");
const connectDB = require("../config/database");
const PriceScrapingLog = require("../models/PriceScrapingLog");
const MaterialSource = require("../models/MaterialSource");
const { alert } = require("../utils/alerter");

const MONITORING_SCHEDULE =
  process.env.SCRAPING_MONITOR_SCHEDULE || "0 * * * *"; // Default: Run hourly
const FAILURE_THRESHOLD_PERCENT = parseInt(
  process.env.SCRAPING_FAILURE_THRESHOLD || "30",
  10
); // Default: 30%
const LOOKBACK_MINUTES = parseInt(
  process.env.SCRAPING_MONITOR_LOOKBACK || "65",
  10
); // Default: Look back 65 mins

/**
 * Checks scraping logs for high failure rates and sends alerts.
 */
async function checkScrapingStatus() {
  logger.info("Running scraping monitoring check...");
  try {
    await connectDB();

    const sources = await MaterialSource.find({ enabled: true });
    const cutoffDate = new Date(Date.now() - LOOKBACK_MINUTES * 60 * 1000);

    for (const source of sources) {
      const sourceId = source._id;
      logger.debug(`Checking status for source: ${source.name} (${sourceId})`);

      const recentLogs = await PriceScrapingLog.find({
        source: sourceId,
        startTime: { $gte: cutoffDate },
      });

      if (recentLogs.length === 0) {
        logger.info(
          `No recent logs found for source ${source.name}. Skipping.`
        );
        continue;
      }

      const totalLogs = recentLogs.length;
      const failedLogs = recentLogs.filter(
        (log) => log.status !== "SUCCESS" && log.status !== "PENDING"
      ).length;
      const failureRate = totalLogs > 0 ? (failedLogs / totalLogs) * 100 : 0;

      logger.info(
        `Source ${
          source.name
        }: ${failedLogs}/${totalLogs} failures (${failureRate.toFixed(
          1
        )}%) in the last ${LOOKBACK_MINUTES} minutes.`
      );

      if (failureRate >= FAILURE_THRESHOLD_PERCENT) {
        const errorMessage = `High scraping failure rate detected for ${
          source.name
        }! ${failureRate.toFixed(
          1
        )}% failures (${failedLogs}/${totalLogs}) in the last ${LOOKBACK_MINUTES} minutes.`;
        logger.error(errorMessage);

        // Send alert
        await alert.error("Scraping Failure Alert", errorMessage, {
          source: source.name,
          sourceId: source._id.toString(),
          failureRate: failureRate.toFixed(1),
          failedCount: failedLogs,
          totalCount: totalLogs,
          lookbackMinutes: LOOKBACK_MINUTES,
          threshold: FAILURE_THRESHOLD_PERCENT,
        });

        // Optionally disable the source if failure rate is critical
        if (failureRate >= 80 && source.enabled) {
          logger.warn(
            `Disabling source ${
              source.name
            } due to critical failure rate (${failureRate.toFixed(1)}%)`
          );
          source.enabled = false;
          source.lastDisabledAt = new Date();
          source.disabledReason = `Automatic disable: ${failureRate.toFixed(
            1
          )}% failure rate`;
          await source.save();

          await alert.critical(
            "Scraping Source Disabled",
            `Source ${
              source.name
            } has been automatically disabled due to ${failureRate.toFixed(
              1
            )}% failure rate`,
            {
              source: source.name,
              sourceId: source._id.toString(),
              failureRate: failureRate.toFixed(1),
            }
          );
        }
      }
    }

    // Check overall system health
    await checkOverallHealth(sources);
  } catch (error) {
    logger.error("Error during scraping monitoring check:", error);
    await alert.error(
      "Scraping Monitor Error",
      "The scraping monitor encountered an error during execution",
      {
        error: error.message,
        stack: error.stack,
      }
    );
  } finally {
    // Close connection if opened by this script, or manage connections globally
    // await mongoose.disconnect();
    // logger.info('Database connection closed.');
  }
}

/**
 * Check overall system health and send summary alerts
 */
async function checkOverallHealth(sources) {
  try {
    const cutoffDate = new Date(Date.now() - 24 * 60 * 60 * 1000); // Last 24 hours

    // Get all active sources
    const activeSources = sources.filter((s) => s.enabled);
    const disabledSources = sources.filter((s) => !s.enabled);

    // Count successful vs failed scrapes in last 24 hours
    const allLogs = await PriceScrapingLog.find({
      startTime: { $gte: cutoffDate },
    });

    const successCount = allLogs.filter(
      (log) => log.status === "SUCCESS"
    ).length;
    const failureCount = allLogs.filter(
      (log) => log.status !== "SUCCESS" && log.status !== "PENDING"
    ).length;
    const totalCount = successCount + failureCount;
    const overallSuccessRate =
      totalCount > 0 ? (successCount / totalCount) * 100 : 0;

    // Send daily summary if configured
    if (
      process.env.SEND_DAILY_SUMMARY === "true" &&
      new Date().getHours() === 9
    ) {
      // Send at 9 AM
      await alert.info(
        "Daily Scraping Summary",
        `Overall success rate: ${overallSuccessRate.toFixed(
          1
        )}% (${successCount}/${totalCount})`,
        {
          activeSources: activeSources.length,
          disabledSources: disabledSources.length,
          successCount,
          failureCount,
          totalCount,
          successRate: overallSuccessRate.toFixed(1),
          disabledSourceNames: disabledSources.map((s) => s.name),
        }
      );
    }

    // Alert if too many sources are disabled
    if (
      disabledSources.length > 0 &&
      disabledSources.length >= sources.length / 2
    ) {
      await alert.warning(
        "Multiple Sources Disabled",
        `${disabledSources.length} out of ${sources.length} scraping sources are currently disabled`,
        {
          disabledSources: disabledSources.map((s) => ({
            name: s.name,
            reason: s.disabledReason || "Unknown",
          })),
        }
      );
    }
  } catch (error) {
    logger.error("Error checking overall health:", error);
  }
}

// Schedule the task
logger.info(
  `Scheduling scraping monitor with schedule: ${MONITORING_SCHEDULE}`
);
cron.schedule(MONITORING_SCHEDULE, checkScrapingStatus);

// Optional: Run once immediately on start
// checkScrapingStatus();
