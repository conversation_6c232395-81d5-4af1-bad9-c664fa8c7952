const express = require("express");
const router = express.Router();
const {
  createMaterial,
  getMaterials,
  getMaterialById,
  updateMaterial,
  deleteMaterial,
  updateInventoryQuantity,
  getLowStockItems,
  getInventoryCategories,
  getMaterialStats, // Added
  getMaterialTransactions, // Added
} = require("../controllers/materialController");
const { protect } = require("../middleware/authMiddleware");

// All routes are protected
// Apply the 'protect' middleware to all routes below.
// This ensures authentication but means the auth logic (and its logs)
// will run for each request made to these endpoints.
router.use(protect);

router.route("/").post(createMaterial).get(getMaterials);

// Define specific string routes BEFORE the parameterized :id route
router.get("/low-stock", getLowStockItems);
router.get("/categories", getInventoryCategories);
// Add placeholders for other potential specific routes if needed in future
// router.get('/search', searchMaterials);
router.get("/stats", getMaterialStats); // Uncommented

// Parameterized routes should come last
router
  .route("/:id")
  .get(getMaterialById)
  .put(updateMaterial)
  .delete(deleteMaterial);

router.patch("/:id/quantity", updateInventoryQuantity);

// Add GET /:id/transactions route
router.get("/:id/transactions", getMaterialTransactions);

module.exports = router;
