import React, { useEffect } from "react";
import {
  Box,
  Typography,
  Paper,
  Container,
  Grid,
  Button,
  CircularProgress,
  Alert,
} from "@mui/material";
import { momentLocalizer } from "react-big-calendar";
import CalendarWrapper from "../components/CalendarWrapper";
import moment from "moment";
import "react-big-calendar/lib/css/react-big-calendar.css";
import { useDispatch, useSelector } from "react-redux";
import {
  getEvents,
  setSelectedDate,
  setCalendarView,
} from "../slices/calendarSlice";

// Setup the localizer for BigCalendar
const localizer = momentLocalizer(moment);

const Calendar = () => {
  const dispatch = useDispatch();
  const {
    events = [],
    loading = false,
    error = null,
    selectedDate,
    view = "month",
  } = useSelector((state) => state.calendar);
  const { userInfo } = useSelector((state) => state.auth);

  // Initialize default date if not set
  useEffect(() => {
    if (!selectedDate) {
      dispatch(setSelectedDate(new Date()));
    }
  }, [dispatch, selectedDate]);

  // Navigate to new date/view
  const handleNavigate = (date) => {
    dispatch(setSelectedDate(date));
    handleDateRangeChange(date);
  };

  const handleDateRangeChange = (date) => {
    const currentDate = moment(date);
    const start = currentDate.startOf("month").toISOString();
    const end = currentDate.endOf("month").toISOString();

    dispatch(
      getEvents({
        start,
        end,
      })
    );
  };

  useEffect(() => {
    if (userInfo?.token) {
      const viewStart = moment().startOf("month").toISOString(); // First day of current month
      const viewEnd = moment().endOf("month").toISOString(); // Last day of current month

      dispatch(
        getEvents({
          start: viewStart,
          end: viewEnd,
        })
      );
    }
  }, [dispatch, userInfo?.token]);

  // Handle view change to fetch events for new date range
  const handleViewChange = (view) => {
    dispatch(setCalendarView(view));
    let start, end;
    const currentDate = moment(selectedDate);

    switch (view) {
      case "month":
        start = currentDate.startOf("month").toISOString();
        end = currentDate.endOf("month").toISOString();
        break;
      case "week":
        start = currentDate.startOf("week").toISOString();
        end = currentDate.endOf("week").toISOString();
        break;
      case "day":
        start = currentDate.startOf("day").toISOString();
        end = currentDate.endOf("day").toISOString();
        break;
      default:
        start = currentDate.startOf("month").toISOString();
        end = currentDate.endOf("month").toISOString();
    }

    dispatch(
      getEvents({
        start,
        end,
      })
    );
  };

  // Transform backend events to calendar format with enhanced validation
  const transformedEvents = Array.isArray(events)
    ? events
        .filter((event) => {
          // Filter out any null or undefined events
          if (!event) {
            console.warn("Filtered out null or undefined event");
            return false;
          }

          // Check for critical date fields first
          const hasValidDates = event.startTime && event.endTime;
          if (!hasValidDates) {
            console.warn(
              "Filtered out event with missing required dates:",
              event
            );
            return false;
          }

          // Validate dates can be parsed
          try {
            const startDate = new Date(event.startTime);
            const endDate = new Date(event.endTime);
            if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
              console.warn("Filtered out event with invalid dates:", event);
              return false;
            }
          } catch (err) {
            console.warn("Failed to parse event dates:", err, event);
            return false;
          }

          // Filter out events without IDs
          if (!event._id) {
            console.warn("Filtered out event without ID:", event);
            return false;
          }

          return true;
        })
        .map((event) => {
          // Create a valid event object with robust fallbacks for all required properties
          return {
            id: event._id,
            // CRITICAL FIX: Always ensure title is a non-null string
            title:
              event.title != null
                ? String(event.title).trim()
                : "Untitled Event",
            start: new Date(event.startTime),
            end: new Date(event.endTime),
            resourceId: event.assignedTo?.[0]?._id,
            type: event.type || "default",
            status: event.status || "pending",
            description: event.description || "",
            location: event.location || "",
            allDay: Boolean(event.allDay),
          };
        })
    : [];

  // Custom event styles
  const eventStyleGetter = (event) => {
    // Guard against undefined or malformed events
    if (!event || typeof event !== "object") {
      return {
        style: {
          backgroundColor: "#1976d2",
          borderRadius: "4px",
          color: "white",
          border: "none",
          display: "block",
        },
      };
    }

    // Additional check for title property
    if (!event.title) {
      console.warn("Event missing title property:", event);
      return {
        style: {
          backgroundColor: "#1976d2",
          borderRadius: "4px",
          color: "white",
          border: "none",
          display: "block",
        },
      };
    }

    let style = {
      backgroundColor: "#1976d2",
      borderRadius: "4px",
      color: "white",
      border: "none",
      display: "block",
    };

    // Style based on event status
    switch (event.status) {
      case "completed":
        style.backgroundColor = "#4caf50";
        break;
      case "in-progress":
        style.backgroundColor = "#ff9800";
        break;
      case "cancelled":
        style.backgroundColor = "#f44336";
        break;
      default:
        break;
    }

    return {
      style,
    };
  };

  // Custom toolbar to add additional controls
  const CustomToolbar = (toolbar) => {
    const goToToday = () => {
      toolbar.date.setMonth(new Date().getMonth());
      toolbar.date.setYear(new Date().getFullYear());
      toolbar.onNavigate("current");
    };

    return (
      <Box
        display="flex"
        justifyContent="space-between"
        alignItems="center"
        p={2}
        sx={{ borderBottom: "1px solid #e0e0e0" }}
      >
        <Box>
          <Button onClick={() => toolbar.onNavigate("PREV")}>Previous</Button>
          <Button onClick={() => toolbar.onNavigate("NEXT")}>Next</Button>
          <Button onClick={goToToday}>Today</Button>
        </Box>
        <Typography variant="h6">{toolbar.label}</Typography>
        <Box>
          <Button
            onClick={() => {
              toolbar.onView("month");
              handleViewChange("month");
            }}
          >
            Month
          </Button>
          <Button
            onClick={() => {
              toolbar.onView("week");
              handleViewChange("week");
            }}
          >
            Week
          </Button>
          <Button
            onClick={() => {
              toolbar.onView("day");
              handleViewChange("day");
            }}
          >
            Day
          </Button>
          <Button
            onClick={() => {
              toolbar.onView("agenda");
              handleViewChange("agenda");
            }}
          >
            Agenda
          </Button>
        </Box>
      </Box>
    );
  };

  return (
    <Container maxWidth="xl" sx={{ mt: 4, mb: 4 }}>
      <Grid container spacing={3}>
        <Grid item xs={12}>
          <Paper
            sx={{
              p: 2,
              display: "flex",
              flexDirection: "column",
              minHeight: "calc(100vh - 200px)",
            }}
          >
            {loading ? (
              <Box
                display="flex"
                justifyContent="center"
                alignItems="center"
                minHeight="500px"
              >
                <CircularProgress />
              </Box>
            ) : error ? (
              <Alert severity="error">{error}</Alert>
            ) : transformedEvents.length > 0 ? (
              <CalendarWrapper
                localizer={localizer}
                events={transformedEvents}
                startAccessor="start"
                endAccessor="end"
                style={{ height: "calc(100vh - 250px)" }}
                eventPropGetter={eventStyleGetter}
                components={{ toolbar: CustomToolbar }}
                onSelectEvent={(event) => console.log("Selected event:", event)}
                onSelectSlot={(slotInfo) =>
                  console.log("Selected slot:", slotInfo)
                }
                selectable={true}
                view={view}
                date={selectedDate || new Date()}
                onNavigate={handleNavigate}
                onView={handleViewChange}
                defaultDate={selectedDate || new Date()}
                defaultView={view}
                popup
                tooltipAccessor="description"
                min={moment().startOf("day").toDate()}
                max={moment().endOf("day").toDate()}
              />
            ) : (
              <Alert severity="info">No calendar events found.</Alert>
            )}
          </Paper>
        </Grid>
      </Grid>
    </Container>
  );
};

export default Calendar;
