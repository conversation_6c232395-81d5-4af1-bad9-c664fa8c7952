services:
  mongodb:
    image: mongo:latest  # You can specify a version, e.g., mongo:5.0, if needed
    container_name: workiz-mongo-rs # Using a slightly different name for clarity
    hostname: workiz-mongo-rs # Changed to match container_name for consistent name resolution
    ports:
      - "27017:27017"
    volumes:
      - mongo-data:/data/db # This will persist your MongoDB data
    command: ["mongod", "--replSet", "rs0", "--bind_ip_all"]
    networks:
      - app-network

  redis:
    image: redis:alpine
    container_name: workiz-redis
    ports:
      - "6380:6379"
    volumes:
      - redis-data:/data
    command: ["redis-server", "--protected-mode", "no", "--requirepass", ""]
    networks:
      - app-network
  


networks:
  app-network:
    driver: bridge

volumes:
  mongo-data:
    driver: local
  redis-data:
    driver: local
