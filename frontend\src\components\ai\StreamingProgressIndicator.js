import React from "react";
import {
  Box,
  Card,
  CardContent,
  Typography,
  LinearProgress,
  Collapse,
  IconButton,
  Chip,
  Alert,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  CircularProgress,
} from "@mui/material";
import {
  ExpandMore as ExpandMoreIcon,
  ExpandLess as ExpandLessIcon,
  CheckCircle as CheckCircleIcon,
  RadioButtonUnchecked as PendingIcon,
  Error as ErrorIcon,
  Speed as SpeedIcon,
  Analytics as AnalyticsIcon,
  AttachMoney as PriceIcon,
} from "@mui/icons-material";

const PHASE_ICONS = {
  phase_1: <SpeedIcon />,
  phase_2: <AnalyticsIcon />,
  phase_3: <PriceIcon />,
};

const PHASE_NAMES = {
  idle: "Ready",
  connecting: "Connecting...",
  phase_1: "Quick Estimate",
  phase_2: "Detailed Analysis",
  phase_3: "Price Lookup",
  completed: "Completed",
  error: "Error",
};

const StreamingProgressIndicator = ({
  isStreaming,
  currentPhase,
  progress,
  statusMessage,
  streamingData,
  error,
  expanded,
  onToggleExpanded,
}) => {
  if (!isStreaming && currentPhase === "idle") {
    return null;
  }

  const getPhaseStatus = (phase) => {
    const phaseOrder = ["phase_1", "phase_2", "phase_3"];
    const currentIndex = phaseOrder.indexOf(currentPhase);
    const targetIndex = phaseOrder.indexOf(phase);

    if (currentPhase === "completed") return "completed";
    if (currentPhase === "error") return "error";
    if (targetIndex < currentIndex) return "completed";
    if (targetIndex === currentIndex) return "active";
    return "pending";
  };

  const renderPhaseItem = (phase, name) => {
    const status = getPhaseStatus(phase);

    return (
      <ListItem key={phase}>
        <ListItemIcon>
          {status === "completed" ? (
            <CheckCircleIcon color="success" />
          ) : status === "active" ? (
            <CircularProgress size={24} />
          ) : status === "error" ? (
            <ErrorIcon color="error" />
          ) : (
            <PendingIcon color="disabled" />
          )}
        </ListItemIcon>
        <ListItemText
          primary={name}
          secondary={
            status === "active" && statusMessage
              ? statusMessage
              : status === "completed" && streamingData?.[phase]
              ? `Completed in ${streamingData[phase].duration || "0"}s`
              : null
          }
          primaryTypographyProps={{
            color:
              status === "completed"
                ? "success.main"
                : status === "active"
                ? "primary.main"
                : status === "error"
                ? "error.main"
                : "text.secondary",
          }}
        />
        {status === "active" && PHASE_ICONS[phase] && (
          <Box sx={{ ml: "auto" }}>{PHASE_ICONS[phase]}</Box>
        )}
      </ListItem>
    );
  };

  return (
    <Card
      sx={{
        mb: 2,
        boxShadow: isStreaming ? 3 : 1,
        transition: "box-shadow 0.3s",
      }}
    >
      <CardContent>
        <Box sx={{ display: "flex", alignItems: "center", mb: 2 }}>
          <Typography variant="h6" sx={{ flexGrow: 1 }}>
            AI Generation Progress
          </Typography>
          <Chip
            label={PHASE_NAMES[currentPhase] || currentPhase}
            color={
              currentPhase === "completed"
                ? "success"
                : currentPhase === "error"
                ? "error"
                : "primary"
            }
            size="small"
            sx={{ mr: 1 }}
          />
          <IconButton onClick={onToggleExpanded} size="small">
            {expanded ? <ExpandLessIcon /> : <ExpandMoreIcon />}
          </IconButton>
        </Box>

        {/* Progress Bar */}
        {isStreaming && currentPhase !== "error" && (
          <Box sx={{ mb: 2 }}>
            <LinearProgress
              variant="determinate"
              value={progress}
              sx={{ height: 8, borderRadius: 4 }}
            />
            <Typography
              variant="caption"
              color="text.secondary"
              sx={{ mt: 0.5 }}
            >
              {progress}% Complete
            </Typography>
          </Box>
        )}

        {/* Error Display */}
        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}

        {/* Detailed Progress */}
        <Collapse in={expanded}>
          <List dense>
            {renderPhaseItem("phase_1", "Quick Estimate (1-3s)")}
            {renderPhaseItem("phase_2", "Detailed Analysis")}
            {renderPhaseItem("phase_3", "Price Lookup")}
          </List>

          {/* Quick Estimate Results */}
          {streamingData?.quickEstimate && (
            <Box sx={{ mt: 2, p: 2, bgcolor: "grey.50", borderRadius: 1 }}>
              <Typography variant="subtitle2" gutterBottom>
                Quick Estimate Results:
              </Typography>
              <Typography variant="body2">
                Estimated Cost:{" "}
                {streamingData.quickEstimate.estimatedCost || "Calculating..."}
              </Typography>
              <Typography variant="body2">
                Complexity:{" "}
                {streamingData.quickEstimate.complexity || "Analyzing..."}
              </Typography>
            </Box>
          )}

          {/* Item Count */}
          {streamingData?.detailedAnalysis?.itemCount && (
            <Box sx={{ mt: 1 }}>
              <Typography variant="body2" color="text.secondary">
                Found {streamingData.detailedAnalysis.itemCount} materials
              </Typography>
            </Box>
          )}

          {/* Price Lookup Progress */}
          {streamingData?.priceLookupResults && (
            <Box sx={{ mt: 1 }}>
              <Typography variant="body2" color="text.secondary">
                Prices found:{" "}
                {Object.keys(streamingData.priceLookupResults).length} items
              </Typography>
            </Box>
          )}

          {/* Detailed Price Lookup Progress */}
          {streamingData?.priceLookupProgress && (
            <Box sx={{ mt: 2, p: 2, bgcolor: "grey.50", borderRadius: 1 }}>
              <Typography variant="subtitle2" gutterBottom>
                Price Lookup Details:
              </Typography>
              {Object.entries(streamingData.priceLookupProgress).map(
                ([itemIndex, progress]) => (
                  <Box key={itemIndex} sx={{ mb: 1 }}>
                    <Typography variant="body2" sx={{ fontWeight: "medium" }}>
                      {progress.step === "initializing" &&
                        "🔄 Initializing price lookup..."}
                      {progress.step === "searching_suppliers" &&
                        "🔍 Searching supplier databases..."}
                      {progress.step === "searching_homedepot" &&
                        "🏠 Searching Home Depot..."}
                      {progress.step === "homedepot_results" &&
                        "✅ Home Depot results found"}
                      {progress.step === "homedepot_failed" &&
                        "❌ Home Depot search failed"}
                      {progress.step === "searching_platt" &&
                        "⚡ Searching Platt Electric..."}
                      {progress.step === "platt_results" &&
                        "✅ Platt Electric results found"}
                      {progress.step === "suppliers_failed" &&
                        "⚠️ Supplier searches failed"}
                      {progress.step === "preparing_web_search" &&
                        "🌐 Preparing web search..."}
                      {progress.step === "crawling_web" &&
                        "🕷️ Crawling web with Crawl4AI..."}
                      {progress.step === "crawl_complete" &&
                        "✅ Web crawling complete"}
                      {progress.step === "parsing_content" &&
                        "📄 Parsing content for products..."}
                      {progress.step === "parsing_complete" &&
                        `✅ Found ${progress.productsFound || 0} products`}
                      {progress.step === "web_match_found" &&
                        `🎯 Best match found (${progress.confidence}% confidence)`}
                      {progress.step === "no_web_matches" &&
                        "❌ No suitable products found"}
                      {progress.step === "crawl_failed" &&
                        "❌ Web crawling failed"}
                      {progress.step === "web_search_error" &&
                        "⚠️ Web search error"}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      {progress.message}
                    </Typography>
                    {progress.searchQuery && (
                      <Typography
                        variant="caption"
                        color="text.secondary"
                        sx={{ display: "block", fontStyle: "italic" }}
                      >
                        Query: {progress.searchQuery}
                      </Typography>
                    )}
                    {progress.contentLength && (
                      <Typography
                        variant="caption"
                        color="text.secondary"
                        sx={{ display: "block" }}
                      >
                        Content: {Math.round(progress.contentLength / 1000)}KB
                      </Typography>
                    )}
                    {progress.error && (
                      <Typography
                        variant="caption"
                        color="error.main"
                        sx={{ display: "block" }}
                      >
                        Error: {progress.error}
                      </Typography>
                    )}
                  </Box>
                )
              )}
            </Box>
          )}
        </Collapse>
      </CardContent>
    </Card>
  );
};

export default StreamingProgressIndicator;
