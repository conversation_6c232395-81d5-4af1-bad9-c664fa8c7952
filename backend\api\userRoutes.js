const express = require("express");
const router = express.Router();
const {
  registerUser,
  loginUser,
  getUserProfile,
  updateUserProfile,
  getUsers,
  getUserById,
  updateUser,
  deleteUser,
  getTechnicians,
  getTechnicianDetails,
  updateTechnicianAvailability,
  getTechnicianWorkload,
} = require("../controllers/userController");
const { protect, admin } = require("../middleware/authMiddleware");

// Public routes
router.post("/", registerUser);
const logger = require("../utils/logger");

router.post("/login", (req, res, next) => {
  logger.info("Login attempt", { email: req.body.email });
  loginUser(req, res, next).catch((err) => {
    logger.error("Login failed", {
      email: req.body.email,
      error: err.message,
      stack: err.stack,
    });
    next(err);
  });
});

// Protected routes
router
  .route("/profile")
  .get(protect, getUserProfile)
  .put(protect, updateUserProfile);

// Technician routes
router.route("/technicians").get(protect, getTechnicians);

router.route("/technicians/:id").get(protect, getTechnicianDetails);

router
  .route("/technicians/:id/availability")
  .put(protect, admin, updateTechnicianAvailability);

router.route("/technicians/:id/workload").get(protect, getTechnicianWorkload);

// Admin routes
router.route("/").get(protect, admin, getUsers);

router
  .route("/:id")
  .get(protect, admin, getUserById)
  .put(protect, admin, updateUser)
  .delete(protect, admin, deleteUser);

module.exports = router;
