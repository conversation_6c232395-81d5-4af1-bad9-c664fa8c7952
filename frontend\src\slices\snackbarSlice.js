import { createSlice } from "@reduxjs/toolkit";

/**
 * Slice for managing application notifications (snackbars)
 */
const snackbarSlice = createSlice({
  name: "snackbar",
  initialState: {
    notifications: [],
  },
  reducers: {
    enqueueSnackbar: (state, action) => {
      const {
        message,
        variant = "info",
        autoHideDuration = 3000,
      } = typeof action.payload === "string"
        ? { message: action.payload }
        : action.payload;

      state.notifications.push({
        key: new Date().getTime() + Math.random(),
        message,
        variant,
        autoHideDuration,
      });
    },
    removeSnackbar: (state, action) => {
      state.notifications = state.notifications.filter(
        (notification) => notification.key !== action.payload
      );
    },
    clearAllSnackbars: (state) => {
      state.notifications = [];
    },
  },
});

export const { enqueueSnackbar, removeSnackbar, clearAllSnackbars } =
  snackbarSlice.actions;

export default snackbarSlice.reducer;
