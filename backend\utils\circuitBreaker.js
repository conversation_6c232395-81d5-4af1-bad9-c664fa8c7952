const logger = require('./logger');

/**
 * Circuit Breaker implementation to prevent cascading failures
 * Tracks failure rates and temporarily blocks problematic operations
 */
class CircuitBreaker {
  constructor(options = {}) {
    this.failureThreshold = options.failureThreshold || 5; // Number of failures before opening circuit
    this.resetTimeout = options.resetTimeout || 60000; // Time to wait before trying again (1 minute)
    this.monitoringWindow = options.monitoringWindow || 300000; // Window to track failures (5 minutes)
    this.name = options.name || 'CircuitBreaker';
    
    // State tracking
    this.state = 'CLOSED'; // CLOSED, OPEN, HALF_OPEN
    this.failures = [];
    this.lastFailureTime = null;
    this.nextAttemptTime = null;
  }

  /**
   * Execute a function with circuit breaker protection
   * @param {Function} fn - Function to execute
   * @param {string} key - Unique key for this operation (e.g., query string)
   * @returns {Promise} - Result of the function or circuit breaker error
   */
  async execute(fn, key = 'default') {
    const now = Date.now();
    
    // Clean up old failures outside the monitoring window
    this.failures = this.failures.filter(
      failure => now - failure.timestamp < this.monitoringWindow
    );

    // Check circuit state
    if (this.state === 'OPEN') {
      if (now < this.nextAttemptTime) {
        const waitTime = Math.round((this.nextAttemptTime - now) / 1000);
        logger.warn(`[${this.name}] Circuit breaker OPEN for "${key}". Retry in ${waitTime}s`);
        throw new Error(`Circuit breaker is OPEN. Retry in ${waitTime} seconds.`);
      } else {
        // Transition to HALF_OPEN for testing
        this.state = 'HALF_OPEN';
        logger.info(`[${this.name}] Circuit breaker transitioning to HALF_OPEN for "${key}"`);
      }
    }

    try {
      const result = await fn();
      
      // Success - reset circuit if it was HALF_OPEN
      if (this.state === 'HALF_OPEN') {
        this.reset();
        logger.info(`[${this.name}] Circuit breaker reset to CLOSED after successful "${key}"`);
      }
      
      return result;
    } catch (error) {
      this.recordFailure(key, error);
      throw error;
    }
  }

  /**
   * Record a failure and potentially open the circuit
   * @param {string} key - Operation key
   * @param {Error} error - The error that occurred
   */
  recordFailure(key, error) {
    const now = Date.now();
    
    this.failures.push({
      timestamp: now,
      key,
      error: error.message,
      isTimeout: error.message && error.message.toLowerCase().includes('timeout')
    });
    
    this.lastFailureTime = now;
    
    // Check if we should open the circuit
    const recentFailures = this.failures.filter(
      failure => now - failure.timestamp < this.monitoringWindow
    );
    
    if (recentFailures.length >= this.failureThreshold) {
      this.state = 'OPEN';
      this.nextAttemptTime = now + this.resetTimeout;
      
      const timeoutFailures = recentFailures.filter(f => f.isTimeout).length;
      logger.warn(
        `[${this.name}] Circuit breaker OPENED after ${recentFailures.length} failures ` +
        `(${timeoutFailures} timeouts) for "${key}". Next attempt in ${this.resetTimeout/1000}s`
      );
    }
  }

  /**
   * Reset the circuit breaker to CLOSED state
   */
  reset() {
    this.state = 'CLOSED';
    this.failures = [];
    this.lastFailureTime = null;
    this.nextAttemptTime = null;
  }

  /**
   * Get current circuit breaker status
   * @returns {Object} - Status information
   */
  getStatus() {
    const now = Date.now();
    const recentFailures = this.failures.filter(
      failure => now - failure.timestamp < this.monitoringWindow
    );
    
    return {
      state: this.state,
      recentFailures: recentFailures.length,
      failureThreshold: this.failureThreshold,
      lastFailureTime: this.lastFailureTime,
      nextAttemptTime: this.nextAttemptTime,
      timeUntilRetry: this.nextAttemptTime ? Math.max(0, this.nextAttemptTime - now) : 0
    };
  }
}

/**
 * Global circuit breakers for different operations
 */
const circuitBreakers = {
  // Circuit breaker for Home Depot scraping
  homeDepot: new CircuitBreaker({
    name: 'HomeDepot',
    failureThreshold: 3,
    resetTimeout: 120000, // 2 minutes
    monitoringWindow: 300000 // 5 minutes
  }),
  
  // Circuit breaker for Platt scraping
  platt: new CircuitBreaker({
    name: 'Platt',
    failureThreshold: 3,
    resetTimeout: 120000, // 2 minutes
    monitoringWindow: 300000 // 5 minutes
  }),
  
  // Circuit breaker for Crawl4AI operations
  crawl4ai: new CircuitBreaker({
    name: 'Crawl4AI',
    failureThreshold: 5,
    resetTimeout: 180000, // 3 minutes
    monitoringWindow: 600000 // 10 minutes
  })
};

module.exports = {
  CircuitBreaker,
  circuitBreakers
};
