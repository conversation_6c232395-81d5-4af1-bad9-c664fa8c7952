const mongoose = require("mongoose");
const Customer = require("../models/Customer"); // Adjust path if necessary
const path = require("path");

// Load environment variables from backend/.env

async function addMissingImageIds() {
  const dbUri = process.env.MONGODB_URI; // Corrected variable name
  if (!dbUri) {
    console.error(
      "MONGODB_URI not found in environment variables. Make sure backend/.env is configured."
    ); // Corrected variable name in error message
    process.exit(1);
  }

  try {
    console.log("Connecting to database...");
    await mongoose.connect(dbUri, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
      // useCreateIndex: true, // Not needed in Mongoose 6+
      // useFindAndModify: false // Not needed in Mongoose 6+
    });
    console.log("Database connected.");

    console.log("Finding customers...");
    const customers = await Customer.find({
      customerImages: { $exists: true, $ne: [] },
    }); // Find customers with images
    console.log(`Found ${customers.length} customers with images.`);

    let customersUpdated = 0;
    let imagesUpdated = 0;

    for (const customer of customers) {
      let customerNeedsSave = false;
      for (const img of customer.customerImages) {
        if (!img._id) {
          img._id = new mongoose.Types.ObjectId();
          customerNeedsSave = true;
          imagesUpdated++;
          console.log(
            `  - Added missing _id to an image for customer ${customer._id}`
          );
        }
      }
      if (customerNeedsSave) {
        try {
          await customer.save();
          customersUpdated++;
          console.log(`  - Saved updates for customer ${customer._id}`);
        } catch (saveError) {
          console.error(
            `  - Error saving customer ${customer._id}: ${saveError.message}`
          );
        }
      }
    }

    console.log("\nMigration complete.");
    console.log(`Customers processed: ${customers.length}`);
    console.log(`Customers updated: ${customersUpdated}`);
    console.log(`Images updated (missing _id added): ${imagesUpdated}`);
  } catch (error) {
    console.error("Migration script failed:", error);
  } finally {
    await mongoose.disconnect();
    console.log("Database disconnected.");
  }
}

addMissingImageIds();
