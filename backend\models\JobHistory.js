const mongoose = require("mongoose");

const jobHistorySchema = mongoose.Schema(
  {
    jobId: {
      type: mongoose.Schema.Types.ObjectId,
      required: true,
      ref: "Job",
    },
    timestamp: {
      type: Date,
      default: Date.now,
      required: true,
    },
    actionType: {
      type: String,
      required: true,
      enum: [
        "status_change",
        "technician_assigned",
        "technician_removed",
        "note_added",
        "task_added",
        "task_completed",
        "task_updated",
        "job_updated",
        "ai_analysis",
        "risk_assessment",
        "scheduled",
        "equipment_assigned",
        "system_event",
      ],
    },
    userId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
    },
    userDisplayName: {
      type: String,
    },
    previousValue: {
      type: mongoose.Schema.Types.Mixed,
    },
    newValue: {
      type: mongoose.Schema.Types.Mixed,
    },
    description: {
      type: String,
    },
    notes: {
      type: String,
    },
    technician: {
      type: {
        _id: mongoose.Schema.Types.ObjectId,
        name: String,
        email: String,
      },
    },
    task: {
      type: {
        _id: mongoose.Schema.Types.ObjectId,
        description: String,
        status: String,
      },
    },
    updatedFields: [String],
    riskLevel: String,
    scheduledDate: Date,
    equipment: {
      type: {
        _id: mongoose.Schema.Types.ObjectId,
        name: String,
        type: String,
      },
    },
    additionalDetails: {
      type: mongoose.Schema.Types.Mixed,
    },
  },
  {
    timestamps: true,
  }
);

// Database indexes for performance optimization
jobHistorySchema.index({ jobId: 1, timestamp: -1 }); // Job history timeline
jobHistorySchema.index({ actionType: 1, timestamp: -1 }); // Action type queries
jobHistorySchema.index({ userId: 1, timestamp: -1 }); // User activity tracking
jobHistorySchema.index({ timestamp: -1 }); // Recent activities across all jobs
jobHistorySchema.index({ jobId: 1, actionType: 1 }); // Job-specific action types
jobHistorySchema.index({ "technician._id": 1, timestamp: -1 }); // Technician activity
jobHistorySchema.index({ riskLevel: 1 }); // Risk assessment queries
jobHistorySchema.index({ scheduledDate: 1 }); // Scheduled events
jobHistorySchema.index({ createdAt: -1 }); // Recent history entries

const JobHistory = mongoose.model("JobHistory", jobHistorySchema);

module.exports = JobHistory;
