const { createLogger, format, transports } = require("winston");
const { combine, timestamp, printf, colorize, json, label } = format;
const fs = require("fs");
const path = require("path");

// Ensure memlog directory exists
const memlogDir = path.join(process.cwd(), "memlog");
if (!fs.existsSync(memlogDir)) {
  fs.mkdirSync(memlogDir, { recursive: true });
}
// Simple error handler for file transports
const fileTransportErrorHandler = (error) => {
  console.error("Winston File Transport Error:", error);
};

// Custom format for development environment with enhanced structure
const devFormat = printf(
  ({
    level,
    message,
    timestamp,
    category,
    requestId,
    userId,
    path,
    ...metadata
  }) => {
    let logMessage = `${timestamp} [${level}]`;

    // Add structured information if available
    if (category) logMessage += ` [${category}]`;
    if (requestId) logMessage += ` [req:${requestId}]`;
    if (userId) logMessage += ` [user:${userId}]`;
    if (path) logMessage += ` [${path}]`;

    logMessage += `: ${message}`;

    // Add any additional metadata if present
    if (Object.keys(metadata).length > 0) {
      logMessage += ` | ${JSON.stringify(metadata)}`;
    }

    return logMessage;
  }
);

// Add category to log format
const categoryFormat = format((info) => {
  if (!info.category) {
    info.category = "general";
  }
  return info;
});

// Determine which formats to use based on environment
// Base format for production/development - categoryFormat() is applied within this
const baseLogFormat =
  process.env.NODE_ENV === "production"
    ? combine(timestamp(), categoryFormat(), json())
    : combine(
        colorize(),
        timestamp({ format: "YYYY-MM-DD HH:mm:ss" }),
        categoryFormat(),
        devFormat
      );

// Helper function to create a category filter format
const categoryFilter = (allowedCategories) => {
  const categories = Array.isArray(allowedCategories)
    ? allowedCategories
    : [allowedCategories];
  return format((info) => {
    if (categories.includes(info.category)) {
      return info;
    }
    return false; // Discard log if category doesn't match
  })();
};

// Environment variable debugging
console.log("🔧 [LOGGER DEBUG] Environment Variables:");
console.log("  NODE_ENV:", process.env.NODE_ENV);
console.log("  LOG_LEVEL:", process.env.LOG_LEVEL);
console.log("  Process argv:", process.argv.slice(2));

// Determine log level with debugging
const logLevel = process.env.LOG_LEVEL || "debug";
console.log("🔧 [LOGGER DEBUG] Resolved log level:", logLevel);

// Create the logger
const logger = createLogger({
  level: logLevel, // Use environment variable or default to debug
  format: baseLogFormat, // Base format, transports add specifics
  defaultMeta: { service: "workiz-api" },
  transports: [
    // Console transport for all environments - force output
    new transports.Console({
      level: logLevel, // Explicit level for console
      handleExceptions: true,
      handleRejections: true,
    }),

    // Always add file transports for our memlog directory
    // Error logs
    new transports.File({
      filename: "memlog/error.log",
      level: "error", // Only logs of level 'error'
      format: baseLogFormat, // Use the base format for this file
      maxsize: 5242880, // 5MB
      maxFiles: 5,
    }).on("error", fileTransportErrorHandler),
    // User profile logs
    new transports.File({
      filename: "memlog/user-profile.log",
      level: "debug", // Inherits main level, filter is more specific
      format: combine(
        categoryFilter("user-profile"), // Only allow 'user-profile' category
        baseLogFormat // Then apply the base format
      ),
      maxsize: 5242880, // 5MB
      maxFiles: 5,
    }).on("error", fileTransportErrorHandler),
    // Role and permission logs
    new transports.File({
      filename: "memlog/roles-permissions.log",
      level: "debug",
      format: combine(
        categoryFilter(["roles", "permissions"]), // Allow 'roles' or 'permissions'
        baseLogFormat
      ),
      maxsize: 5242880, // 5MB
      maxFiles: 5,
    }).on("error", fileTransportErrorHandler),
    // System configuration logs
    new transports.File({
      filename: "memlog/system-config.log",
      level: "debug",
      format: combine(
        categoryFilter("config"), // Only allow 'config' category
        baseLogFormat
      ),
      maxsize: 5242880, // 5MB
      maxFiles: 5,
    }).on("error", fileTransportErrorHandler),
    // Combined logs
    new transports.File({
      filename: "memlog/combined.log",
      level: "debug", // Capture all debug (and higher) logs
      format: baseLogFormat, // Use the base format
      // Consider adding back maxsize/maxFiles later if needed
      // maxsize: 5242880, // 5MB
      // maxFiles: 5
    }).on("error", fileTransportErrorHandler),

    // AI Generation logs - in root directory for easy access
    new transports.File({
      filename: "../ai-generation.log", // Root directory (one level up from backend)
      level: "debug",
      format: combine(
        categoryFilter("ai-generation"), // Only allow 'ai-generation' category
        baseLogFormat
      ),
      // No size limits - user wants full AI generation logs
    }).on("error", fileTransportErrorHandler),
  ],
});

// Add specialized logging methods for our specific categories
logger.profile = (message) => {
  logger.info(message, { category: "user-profile" });
};

logger.roles = (message) => {
  logger.info(message, { category: "roles" });
};

logger.permissions = (message) => {
  logger.info(message, { category: "permissions" });
};

logger.persistence = (message) => {
  logger.info(message, { category: "persistence" });
};

logger.config = (message) => {
  logger.info(message, { category: "config" });
};

// AI Generation logging methods
logger.aiGeneration = (message, metadata = {}) => {
  logger.info(message, { category: "ai-generation", ...metadata });
};

// Enhanced AI generation logging for different levels
logger.aiGenerationDebug = (message, metadata = {}) => {
  logger.debug(message, { category: "ai-generation", ...metadata });
};

logger.aiGenerationError = (message, metadata = {}) => {
  logger.error(message, { category: "ai-generation", ...metadata });
};

logger.aiGenerationWarn = (message, metadata = {}) => {
  logger.warn(message, { category: "ai-generation", ...metadata });
};

// Global flag to enable enhanced AI generation logging
logger.isAiGenerationActive = false;

// Method to start comprehensive AI generation logging
logger.startAiGenerationLogging = () => {
  logger.isAiGenerationActive = true;
  logger.aiGeneration("=== COMPREHENSIVE AI GENERATION LOGGING STARTED ===");
};

// Method to stop comprehensive AI generation logging
logger.stopAiGenerationLogging = () => {
  logger.aiGeneration("=== COMPREHENSIVE AI GENERATION LOGGING STOPPED ===");
  logger.isAiGenerationActive = false;
};

// Enhanced logging method that captures everything during AI generation
logger.logIfAiActive = (level, message, metadata = {}) => {
  if (logger.isAiGenerationActive) {
    // Log to AI generation category when AI process is active
    const aiMetadata = {
      category: "ai-generation",
      originalLevel: level,
      ...metadata,
    };
    logger[level](message, aiMetadata);
  }
};

// Add stream for Morgan HTTP logger
logger.stream = {
  write: (message) => logger.http(message.trim()),
};

module.exports = logger;
