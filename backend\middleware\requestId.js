/**
 * Request ID middleware
 * Assigns a unique ID to each request for tracking and logging
 */
const { v4: uuidv4 } = require("uuid");

/**
 * Middleware to assign a unique ID to each request
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
const requestId = (req, res, next) => {
  // Generate a unique ID for this request
  const id = uuidv4();
  req.id = id;

  // Add request ID to response headers
  res.setHeader("X-Request-ID", id);

  // Add request start time for performance tracking
  req.startTime = Date.now();

  // Capture original end method to add timing information
  const originalEnd = res.end;
  res.end = function (...args) {
    // Calculate request duration
    const duration = Date.now() - req.startTime;

    // Only set header if headers haven't been sent yet
    if (!res.headersSent) {
      res.setHeader("X-Response-Time", `${duration}ms`);
    }

    // Call original end method
    return originalEnd.apply(this, args);
  };

  next();
};

module.exports = requestId;
