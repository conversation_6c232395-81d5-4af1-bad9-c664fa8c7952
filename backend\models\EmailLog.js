const mongoose = require("mongoose");
const Schema = mongoose.Schema;

/**
 * Email Log Schema
 * Tracks all email communications related to invoices
 */
const emailLogSchema = new Schema(
  {
    invoice: {
      type: Schema.Types.ObjectId,
      ref: "Invoice",
      required: true,
    },
    type: {
      type: String,
      required: true,
      enum: [
        "INVOICE_SENT",
        "REMINDER_SENT",
        "RECEIPT_SENT",
        "PAYMENT_CONFIRMATION",
        "OVERDUE_NOTICE",
        "THANK_YOU",
        "CUSTOM",
      ],
    },
    messageId: {
      type: String,
      required: true,
    },
    timestamp: {
      type: Date,
      required: true,
      default: Date.now,
    },
    status: {
      type: String,
      enum: ["SENT", "DELIVERED", "OPENED", "CLICKED", "BOUNCED", "FAILED"],
      default: "SENT",
    },
    recipient: {
      email: String,
      name: String,
    },
    subject: String,
    templateUsed: String,
    metadata: {
      reminderId: String,
      reminderSequence: Number,
      paymentId: Schema.Types.ObjectId,
      customData: Schema.Types.Mixed,
    },
    deliveryDetails: {
      deliveredAt: Date,
      openedAt: Date,
      clickedAt: Date,
      bounceReason: String,
      failureReason: String,
      ipAddress: String,
      userAgent: String,
    },
    analytics: {
      openCount: {
        type: Number,
        default: 0,
      },
      clickCount: {
        type: Number,
        default: 0,
      },
      lastOpenedAt: Date,
      lastClickedAt: Date,
      linkClicks: [
        {
          url: String,
          timestamp: Date,
          count: Number,
        },
      ],
    },
  },
  {
    timestamps: true,
  }
);

// Database indexes for performance optimization
emailLogSchema.index({ invoice: 1, type: 1 }); // Invoice email history
emailLogSchema.index({ timestamp: 1 }); // Chronological queries
emailLogSchema.index({ "recipient.email": 1 }); // Recipient lookup
emailLogSchema.index({ messageId: 1 }, { unique: true }); // Unique message tracking
emailLogSchema.index({ status: 1, timestamp: -1 }); // Status-based queries
emailLogSchema.index({ type: 1, timestamp: -1 }); // Email type analytics
emailLogSchema.index({ "deliveryDetails.deliveredAt": 1 }); // Delivery tracking
emailLogSchema.index({ "deliveryDetails.openedAt": 1 }); // Open tracking
emailLogSchema.index({ "analytics.openCount": 1 }); // Engagement analytics
emailLogSchema.index({ templateUsed: 1 }); // Template performance
emailLogSchema.index({ "metadata.reminderId": 1 }); // Reminder tracking

// Virtual fields
emailLogSchema.virtual("age").get(function () {
  return Math.floor((Date.now() - this.timestamp) / (1000 * 60 * 60));
});

emailLogSchema.virtual("isRecent").get(function () {
  return this.age < 24; // Less than 24 hours old
});

// Methods
emailLogSchema.methods.updateStatus = async function (status, details = {}) {
  this.status = status;

  switch (status) {
    case "DELIVERED":
      this.deliveryDetails.deliveredAt = new Date();
      break;
    case "OPENED":
      this.deliveryDetails.openedAt = new Date();
      this.analytics.openCount++;
      this.analytics.lastOpenedAt = new Date();
      break;
    case "CLICKED":
      this.deliveryDetails.clickedAt = new Date();
      this.analytics.clickCount++;
      this.analytics.lastClickedAt = new Date();
      if (details.url) {
        const linkClick = this.analytics.linkClicks.find(
          (lc) => lc.url === details.url
        );
        if (linkClick) {
          linkClick.count++;
          linkClick.timestamp = new Date();
        } else {
          this.analytics.linkClicks.push({
            url: details.url,
            timestamp: new Date(),
            count: 1,
          });
        }
      }
      break;
    case "BOUNCED":
      this.deliveryDetails.bounceReason = details.reason;
      break;
    case "FAILED":
      this.deliveryDetails.failureReason = details.reason;
      break;
  }

  if (details.ipAddress) {
    this.deliveryDetails.ipAddress = details.ipAddress;
  }
  if (details.userAgent) {
    this.deliveryDetails.userAgent = details.userAgent;
  }

  await this.save();
};

// Statics
emailLogSchema.statics.getEmailHistory = async function (invoiceId) {
  return this.find({ invoice: invoiceId })
    .sort({ timestamp: -1 })
    .select("-__v")
    .lean();
};

emailLogSchema.statics.getRecentEmails = async function (hours = 24) {
  const cutoff = new Date(Date.now() - hours * 60 * 60 * 1000);
  return this.find({
    timestamp: { $gte: cutoff },
  })
    .sort({ timestamp: -1 })
    .select("-__v")
    .lean();
};

emailLogSchema.statics.getEmailStats = async function (startDate, endDate) {
  return this.aggregate([
    {
      $match: {
        timestamp: {
          $gte: startDate,
          $lte: endDate,
        },
      },
    },
    {
      $group: {
        _id: "$type",
        count: { $sum: 1 },
        delivered: {
          $sum: {
            $cond: [{ $eq: ["$status", "DELIVERED"] }, 1, 0],
          },
        },
        opened: {
          $sum: {
            $cond: [{ $eq: ["$status", "OPENED"] }, 1, 0],
          },
        },
        clicked: {
          $sum: {
            $cond: [{ $eq: ["$status", "CLICKED"] }, 1, 0],
          },
        },
        bounced: {
          $sum: {
            $cond: [{ $eq: ["$status", "BOUNCED"] }, 1, 0],
          },
        },
        failed: {
          $sum: {
            $cond: [{ $eq: ["$status", "FAILED"] }, 1, 0],
          },
        },
      },
    },
  ]);
};

// Pre-save middleware
emailLogSchema.pre("save", function (next) {
  if (this.isNew) {
    // Initialize arrays if they don't exist
    if (!this.analytics.linkClicks) {
      this.analytics.linkClicks = [];
    }
  }
  next();
});

const EmailLog = mongoose.model("EmailLog", emailLogSchema);
module.exports = EmailLog;
