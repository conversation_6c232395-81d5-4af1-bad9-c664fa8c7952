const jwt = require("jsonwebtoken");
const User = require("../models/User");
const logger = require("../utils/logger");

// JWT Secret
const JWT_SECRET = process.env.JWT_SECRET || "your_jwt_secret";

// Protect routes - verify token and set req.user
const protect = async (req, res, next) => {
  let token;

  // Check for token in Authorization header
  if (
    req.headers.authorization &&
    req.headers.authorization.startsWith("Bearer")
  ) {
    try {
      // Get token from header
      token = req.headers.authorization.split(" ")[1];
      logger.debug(`Auth Token Received: ${token ? "Yes" : "No"}`); // Log token presence

      // Verify token
      let decoded;
      try {
        decoded = jwt.verify(token, JWT_SECRET);
        logger.debug(`Token Decoded Successfully: User ID ${decoded?.id}`); // Log decoded ID
      } catch (verifyError) {
        logger.error(`Token Verification Failed: ${verifyError.message}`, {
          token,
        });
        throw verifyError; // Re-throw to be caught by outer catch
      }

      // Get user from the token (exclude password)
      logger.debug(`Looking up user with ID: ${decoded.id}`); // Log ID before lookup
      req.user = await User.findById(decoded.id).select("-password");
      logger.debug(`User Found: ${req.user ? req.user.email : "No"}`); // Log user lookup result

      if (!req.user) {
        res.status(401);
        throw new Error("User not found");
      }

      // Log authentication success
      logger.info(`User authenticated: ${req.user.email} (${req.user.role})`);
      // NOTE: This detailed logging (token check, decode, user lookup) runs for every
      // API request protected by this middleware. Sequential frontend calls to
      // multiple protected endpoints will result in repeated log sequences.

      next();
    } catch (error) {
      logger.error(`Authentication error: ${error.message}`);
      res.status(401).json({
        message: "Not authorized",
        error: error.message,
        stack: process.env.NODE_ENV === "development" ? error.stack : undefined,
      });
    }
  }

  if (!token) {
    res.status(401).json({
      message: "Not authorized",
      error: "No authentication token provided",
    });
  }
};

// Admin middleware - check if user is administrator
const admin = (req, res, next) => {
  if (req.user && req.user.role === "Administrators") {
    logger.info(`Admin access granted to: ${req.user.email}`);
    next();
  } else {
    logger.warn(
      `Admin access denied to: ${req.user?.email || "Unknown user"} (${
        req.user?.role || "No role"
      })`
    );
    res.status(403).json({ message: "Not authorized as an administrator" });
  }
};

// Manager middleware - check if user is administrator or manager
const manager = (req, res, next) => {
  if (
    req.user &&
    (req.user.role === "Administrators" || req.user.role === "Managers")
  ) {
    logger.info(
      `Manager access granted to: ${req.user.email} (${req.user.role})`
    );
    next();
  } else {
    logger.warn(
      `Manager access denied to: ${req.user?.email || "Unknown user"} (${
        req.user?.role || "No role"
      })`
    );
    res.status(403).json({ message: "Not authorized as a manager" });
  }
};

// Supervisor middleware - check if user is administrator, manager, or supervisor
const supervisor = (req, res, next) => {
  if (
    req.user &&
    ["Administrators", "Managers", "Supervisors"].includes(req.user.role)
  ) {
    logger.info(
      `Supervisor access granted to: ${req.user.email} (${req.user.role})`
    );
    next();
  } else {
    logger.warn(
      `Supervisor access denied to: ${req.user?.email || "Unknown user"} (${
        req.user?.role || "No role"
      })`
    );
    res
      .status(403)
      .json({ message: "Not authorized as a supervisor or higher" });
  }
};

// Technician middleware - check if user has any valid role
const technician = (req, res, next) => {
  if (
    req.user &&
    ["Administrators", "Managers", "Supervisors", "Technicians"].includes(
      req.user.role
    )
  ) {
    logger.info(
      `Technician access granted to: ${req.user.email} (${req.user.role})`
    );
    next();
  } else {
    logger.warn(
      `Technician access denied to: ${req.user?.email || "Unknown user"} (${
        req.user?.role || "No role"
      })`
    );
    res.status(403).json({ message: "Not authorized" });
  }
};

module.exports = { protect, admin, manager, supervisor, technician };
