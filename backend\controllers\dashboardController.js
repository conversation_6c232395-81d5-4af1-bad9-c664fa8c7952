const {
  User,
  Customer,
  Job,
  Invoice,
  CalendarEvent,
  Material,
} = require("../models");

/**
 * Get dashboard metrics
 * @route GET /api/dashboard/metrics
 * @access Private
 */
const getDashboardMetrics = async (req, res) => {
  try {
    // Get total customers count
    const totalCustomers = await Customer.countDocuments();

    // Get open jobs count (assuming 'status' field with values like 'Open', 'Scheduled', etc.)
    const openJobs = await Job.countDocuments({
      status: { $in: ["Open", "Scheduled"] },
    });

    // Get outstanding invoices total amount
    const outstandingInvoices = await Invoice.aggregate([
      {
        $match: {
          status: { $in: ["Unpaid", "Overdue"] },
        },
      },
      {
        $group: {
          _id: null,
          total: { $sum: { $ifNull: ["$total", 0] } },
        },
      },
    ]);

    // Get count of upcoming calendar events (next 7 days)
    const now = new Date();
    const sevenDaysLater = new Date();
    sevenDaysLater.setDate(now.getDate() + 7);

    const upcomingEvents = await CalendarEvent.countDocuments({
      "schedule.startDate": {
        $gte: now,
        $lte: sevenDaysLater,
      },
    });

    // Get count of low stock inventory items
    const lowStockItems = await Material.countDocuments({
      quantity: { $lte: 10 }, // Assuming items with quantity <= 10 are considered low stock
    });

    res.json({
      totalCustomers: totalCustomers || 0,
      openJobs: openJobs || 0,
      outstandingInvoices:
        outstandingInvoices.length > 0 ? outstandingInvoices[0].total : 0,
      upcomingEvents: upcomingEvents || 0,
      lowStockItems: lowStockItems || 0,
    });
  } catch (error) {
    console.error("Error fetching dashboard metrics:", error);
    res.status(500).json({ message: "Error fetching dashboard metrics" });
  }
};

module.exports = {
  getDashboardMetrics,
};
