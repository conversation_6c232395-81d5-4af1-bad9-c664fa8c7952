import React, { createContext, useContext, useEffect, useState } from "react";
import { LoadScript } from "@react-google-maps/api";
import { CircularProgress, Box } from "@mui/material";

// Define the libraries we need
const libraries = ["places"];

// Create context
const GoogleMapsContext = createContext(null);

// Custom hook to use the Google Maps context
export const useGoogleMaps = () => useContext(GoogleMapsContext);

// Provider component
export const GoogleMapsProvider = ({ children }) => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [loadError, setLoadError] = useState(null);

  // No services need explicit initialization here anymore

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      setIsLoaded(false);
    };
  }, []);

  const handleLoad = () => {
    setIsLoaded(true);
  };

  const handleError = (error) => {
    console.error("Error loading Google Maps:", error);
    setLoadError(error);
  };

  const value = {
    isLoaded,
    loadError,
  };

  return (
    <GoogleMapsContext.Provider value={value}>
      <LoadScript
        googleMapsApiKey={
          window.REACT_APP_GOOGLE_MAPS_API_KEY ||
          process.env.REACT_APP_GOOGLE_MAPS_API_KEY ||
          ""
        }
        libraries={libraries}
        onLoad={handleLoad}
        onError={handleError}
        loadingElement={
          <Box display="flex" justifyContent="center" my={4}>
            <CircularProgress />
          </Box>
        }
        key="google-maps-script"
      >
        {children}
      </LoadScript>
    </GoogleMapsContext.Provider>
  );
};

export default GoogleMapsProvider;
