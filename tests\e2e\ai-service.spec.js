/**
 * AI Service End-to-End Tests
 * Tests AI endpoints with increased token limits and JSON parsing
 */

const { test, expect } = require('@playwright/test');

test.describe('AI Service Endpoints', () => {
  let authToken;
  
  const testUser = {
    email: '<EMAIL>',
    password: 'password123'
  };

  test.beforeAll(async ({ request }) => {
    // Login to get auth token
    const response = await request.post('http://localhost:5000/api/users/login', {
      data: testUser
    });d
    
    expect(response.status()).toBe(200);
    const responseData = await response.json();
    authToken = responseData.token;
  });

  test('should generate material descriptions with increased token limits', async ({ request }) => {
    const materialData = {
      materials: [
        {
          name: '200A Main Electrical Panel',
          category: 'electrical',
          specifications: {
            amperage: '200A',
            voltage: '240V',
            phases: 'single',
            type: 'main breaker panel'
          }
        },
        {
          name: '12 AWG THHN Copper Wire',
          category: 'electrical',
          specifications: {
            gauge: '12 AWG',
            material: 'copper',
            insulation: 'THHN',
            temperature_rating: '90°C'
          }
        }
      ]
    };

    const response = await request.post('http://localhost:5000/api/ai/generate-material-descriptions', {
      headers: {
        'Authorization': `Bearer ${authToken}`,
        'Content-Type': 'application/json'
      },
      data: materialData
    });

    expect(response.status()).toBe(200);
    const responseData = await response.json();
    expect(responseData.success).toBe(true);
    expect(responseData.descriptions).toBeDefined();
    expect(Array.isArray(responseData.descriptions)).toBe(true);
  });

  test('should handle complex job analysis with high token limits', async ({ request }) => {
    const complexJobData = {
      jobType: 'electrical',
      description: 'Complete electrical system upgrade for a 3-story commercial building including main panel replacement, sub-panel installation, circuit rewiring, outlet and switch upgrades, lighting system modernization, emergency lighting installation, fire alarm system integration, and code compliance verification',
      location: 'Commercial building - 15,000 sq ft',
      floors: 3,
      existingSystem: {
        mainPanel: '100A outdated',
        wiring: 'mix of aluminum and copper, some knob-and-tube',
        outlets: 'mostly 2-prong, insufficient GFCI protection',
        lighting: 'fluorescent fixtures, some incandescent'
      },
      requirements: {
        codeCompliance: 'NEC 2023',
        permits: ['electrical', 'building'],
        inspections: ['rough-in', 'final'],
        timeline: '4-6 weeks',
        budget: '$50,000-$75,000'
      },
      specialConsiderations: [
        'Building remains operational during work',
        'Coordination with HVAC and plumbing contractors',
        'Historic building preservation requirements',
        'ADA compliance for accessibility'
      ]
    };

    const response = await request.post('http://localhost:5000/api/ai/analyze-job', {
      headers: {
        'Authorization': `Bearer ${authToken}`,
        'Content-Type': 'application/json'
      },
      data: complexJobData
    });

    expect(response.status()).toBe(200);
    const responseData = await response.json();
    expect(responseData.success).toBe(true);
    expect(responseData.analysis).toBeDefined();
  });

  test('should generate comprehensive quotes with complex JSON structures', async ({ request }) => {
    const quoteRequest = {
      jobType: 'electrical',
      customerInfo: {
        name: 'ABC Corporation',
        address: '456 Business Ave, Commerce City, CC 67890',
        contactPerson: 'John Smith',
        phone: '555-0123',
        email: '<EMAIL>'
      },
      projectDetails: {
        description: 'Electrical infrastructure upgrade for manufacturing facility',
        scope: [
          'Install 400A main service panel',
          'Add 6 sub-panels for different zones',
          'Upgrade all branch circuits to 20A minimum',
          'Install 480V 3-phase power for machinery',
          'Add emergency power systems',
          'Implement energy monitoring systems'
        ],
        timeline: '8-10 weeks',
        phases: [
          {
            name: 'Phase 1: Main Service Upgrade',
            duration: '2 weeks',
            description: 'Replace main electrical service and panel'
          },
          {
            name: 'Phase 2: Sub-panel Installation',
            duration: '3 weeks',
            description: 'Install and wire all sub-panels'
          },
          {
            name: 'Phase 3: Branch Circuit Upgrades',
            duration: '3 weeks',
            description: 'Upgrade all branch circuits and outlets'
          }
        ]
      },
      materials: [
        {
          category: 'panels',
          items: [
            { name: '400A Main Panel', quantity: 1, specifications: { amperage: '400A', voltage: '480V', phases: 3 } },
            { name: '200A Sub Panel', quantity: 6, specifications: { amperage: '200A', voltage: '240V', phases: 1 } }
          ]
        },
        {
          category: 'wiring',
          items: [
            { name: '4/0 AWG Copper Wire', quantity: 500, unit: 'ft', specifications: { gauge: '4/0', material: 'copper' } },
            { name: '12 AWG THHN Wire', quantity: 5000, unit: 'ft', specifications: { gauge: '12', insulation: 'THHN' } }
          ]
        }
      ]
    };

    const response = await request.post('http://localhost:5000/api/ai/generate-quote', {
      headers: {
        'Authorization': `Bearer ${authToken}`,
        'Content-Type': 'application/json'
      },
      data: quoteRequest
    });

    expect(response.status()).toBe(200);
    const responseData = await response.json();
    expect(responseData.success).toBe(true);
    expect(responseData.quote).toBeDefined();
  });

  test('should handle JSON parsing errors gracefully', async ({ request }) => {
    const response = await request.post('http://localhost:5000/api/ai/generate-quote', {
      headers: {
        'Authorization': `Bearer ${authToken}`,
        'Content-Type': 'application/json'
      },
      data: '{invalid json}'
    });

    expect(response.status()).toBe(400);
    const responseData = await response.json();
    expect(responseData.success).toBe(false);
    expect(responseData.error).toBeDefined();
  });

  test('should reject requests without authentication', async ({ request }) => {
    const response = await request.post('http://localhost:5000/api/ai/generate-quote', {
      headers: {
        'Content-Type': 'application/json'
      },
      data: { jobType: 'electrical' }
    });

    expect(response.status()).toBe(401);
  });
});