const Job = require("../models/Job");
const Customer = require("../models/Customer");
const User = require("../models/User");
const JobHistory = require("../models/JobHistory");
const {
  getGeminiResponse,
  getGeminiJsonResponse,
} = require("../utils/geminiService");
const path = require("path"); // Needed for constructing relative URLs
const logger = require("../utils/logger"); // Import logger
const ApiError = require("../utils/ApiError"); // Import custom error class

// Helper function to calculate priority score using AI
const calculatePriorityScore = async (job) => {
  try {
    const systemPrompt = `You are a business analytics AI that specializes in prioritizing jobs.`;
    const userPrompt = `Calculate a priority score (1-100) for this job based on:
    1. Current status: ${job.status}
    2. Priority level: ${job.priority}
    3. Scheduled date: ${job.scheduledDate}
    4. Tasks completion: ${job.tasks.filter((t) => t.completed).length}/${
      job.tasks.length
    }
    5. Customer history: ${job.customer.jobs?.length || 0} previous jobs
    
    Consider:
    - Higher score for urgent/overdue jobs
    - VIP customers get priority
    - Impact on business operations
    - Resource availability
    
    Respond with ONLY a number between 1-100 representing the priority score.`;

    const response = await getGeminiResponse(systemPrompt, userPrompt, {
      temperature: 0.3,
      maxTokens: 4000, // Increased for complex JSON responses per tokenConfig
    });

    const score = parseInt(response);
    return isNaN(score) ? 50 : score; // Default to medium priority if AI fails
  } catch (error) {
    console.error("Priority Score Calculation Error:", error);
    return 50; // Default to medium priority
  }
};

// Helper function to suggest next steps using AI
const suggestNextSteps = async (job) => {
  try {
    const systemPrompt = `You are a task management AI that specializes in suggesting next steps for job completion.`;
    const userPrompt = `Based on the current job status and progress, suggest the next 3 most important steps:
    
    Current Status: ${job.status}
    Progress: ${job.tasks.filter((t) => t.completed).length}/${
      job.tasks.length
    } tasks completed
    Special Considerations: ${job.aiInsights?.specialConsiderations || []}
    Recent Notes: ${
      job.notes
        ?.slice(-3)
        .map((n) => n.content || "")
        .join("\n") || "No recent notes"
    }
    
    IMPORTANT: Your response MUST be a plain JSON array of strings, like this exact format:
    ["Step 1", "Step 2", "Step 3"]
    
    Do NOT use property names or nested objects. ONLY a flat array of strings.`;

    try {
      // Get response from Gemini
      const response = await getGeminiResponse(systemPrompt, userPrompt, {
        temperature: 0.7,
        maxTokens: 4000, // Increased for complex JSON responses per tokenConfig
      });

      // Try to parse as JSON
      let steps;
      try {
        // Attempt to parse the raw response
        steps = JSON.parse(response);

        // Ensure it's an array
        if (!Array.isArray(steps)) {
          console.warn("AI returned non-array response:", response);
          // If it's an object with a steps/nextSteps/next_steps property, use that
          if (steps.steps) steps = steps.steps;
          else if (steps.nextSteps) steps = steps.nextSteps;
          else if (steps.next_steps) steps = steps.next_steps;
          else
            steps = [
              "Review job status",
              "Check completed tasks",
              "Plan next phase",
            ];
        }
      } catch (parseError) {
        console.warn("Could not parse AI response as JSON:", response);
        // Try to extract array using regex
        const arrayMatch = response.match(/\[(.*)\]/s);
        if (arrayMatch) {
          try {
            steps = JSON.parse(arrayMatch[0]);
          } catch (e) {
            steps = [
              "Review job status",
              "Check completed tasks",
              "Plan next phase",
            ];
          }
        } else {
          steps = [
            "Review job status",
            "Check completed tasks",
            "Plan next phase",
          ];
        }
      }

      // Ensure each step is a string
      steps = steps
        .map((step) => (typeof step === "string" ? step : String(step)))
        .slice(0, 5);

      return steps;
    } catch (error) {
      console.error("Next Steps Error:", error);
      return ["Review job status", "Check completed tasks", "Plan next phase"];
    }
  } catch (error) {
    console.error("Next Steps Suggestion Error:", error);
    return ["Review job status", "Check completed tasks", "Plan next phase"];
  }
};

// Helper function to analyze job complexity using AI
const analyzeJobComplexity = async (description, tasks) => {
  try {
    const systemPrompt = `You are a job analysis expert that determines complexity, required skills, and risk factors.`;
    const userPrompt = `Analyze the following job description and tasks to determine:
    1. Estimated complexity level (Low, Medium, High)
    2. Recommended skill level for technicians
    3. Potential risks or special considerations
    
    Job Description: ${description}
    Tasks: ${tasks.map((t) => t.description).join(", ")}
    
    IMPORTANT: Respond with a valid JSON object with the following structure:
    {
      "complexity": "Low|Medium|High",
      "recommendedSkills": ["skill1", "skill2", ...],
      "specialConsiderations": ["consideration1", "consideration2", ...]
    }
    Do not include any explanation or additional text outside the JSON object.`;

    try {
      // Use getGeminiJsonResponse which handles JSON formatting
      return await getGeminiJsonResponse(systemPrompt, userPrompt, {
        temperature: 0.7,
        maxTokens: 500,
      });
    } catch (parseError) {
      console.error("JSON parsing error:", parseError);
      // Return a default object if parsing fails
      return {
        complexity: "Medium",
        recommendedSkills: ["General Technician"],
        specialConsiderations: ["Standard safety protocols"],
      };
    }
  } catch (error) {
    console.error("AI Analysis Error:", error);
    return {
      complexity: "Medium",
      recommendedSkills: ["General Technician"],
      specialConsiderations: ["Standard safety protocols"],
    };
  }
};

// Helper function to optimize technician assignment
const optimizeTechnicianAssignment = async (job, availableTechnicians) => {
  try {
    const technicianData = availableTechnicians.map((tech) => ({
      id: tech._id,
      skills: tech.skills,
      currentLocation: tech.currentLocation,
      workload: tech.currentJobs?.length || 0,
    }));

    const systemPrompt = `You are a resource allocation expert that optimizes technician assignments.`;
    const userPrompt = `Given the following job and available technicians, recommend the best technician assignment:
    Job Location: ${JSON.stringify(job.location || {})}
    Job Complexity: ${job.complexity || "Medium"}
    Required Skills: ${job.requiredSkills || "General"}
    
    Available Technicians: ${JSON.stringify(technicianData)}
    
    Consider:
    1. Technician skills match
    2. Current workload
    3. Distance to job location
    4. Previous experience with similar jobs
    
    IMPORTANT: Respond with a valid JSON object with the following structure:
    {
      "recommendedTechnician": "technician_id",
      "reasoning": "explanation of why this technician was selected"
    }
    Do not include any explanation or additional text outside the JSON object.`;

    try {
      // Use getGeminiJsonResponse to get a properly formatted JSON response
      const result = await getGeminiJsonResponse(systemPrompt, userPrompt, {
        temperature: 0.7,
        maxTokens: 500,
      });

      // Validate and return the recommendation
      if (result && result.recommendedTechnician) {
        return result;
      } else {
        console.warn("Invalid technician recommendation format:", result);
        return {
          recommendedTechnician: availableTechnicians[0]?._id || null,
          reasoning: "Default assignment based on availability",
        };
      }
    } catch (parseError) {
      console.error("JSON parsing error:", parseError);
      // Return a default object if parsing fails
      return {
        recommendedTechnician: availableTechnicians[0]?._id || null,
        reasoning: "Default assignment based on availability",
      };
    }
  } catch (error) {
    console.error("Technician Assignment Error:", error);
    return {
      recommendedTechnician: availableTechnicians[0]?._id || null,
      reasoning: "Default assignment due to error",
    };
  }
};

// Helper function to generate job number
const generateJobNumber = async () => {
  const date = new Date();
  const year = date.getFullYear().toString().substr(-2);
  const month = (date.getMonth() + 1).toString().padStart(2, "0");

  // Find the latest job to increment the number
  const latestJob = await Job.findOne().sort({ createdAt: -1 });
  let nextNumber = 1;

  if (latestJob && latestJob.jobNumber) {
    // Extract the numeric part from the job number
    const currentNumber = parseInt(latestJob.jobNumber.split("-")[2] || "0");
    nextNumber = currentNumber + 1;
  }

  return `JOB-${year}${month}-${nextNumber.toString().padStart(4, "0")}`;
};

// Helper function to create a job history record
const createJobHistoryRecord = async (jobId, actionType, data = {}) => {
  try {
    const historyRecord = {
      jobId,
      actionType,
      timestamp: new Date(),
      ...data,
    };

    await JobHistory.create(historyRecord);
  } catch (error) {
    console.error("Error creating job history record:", error);
    // Don't throw the error to prevent disrupting main operation flow
  }
};

// @desc    Create a new job with AI assistance
// @route   POST /api/jobs
// @access  Private
const createJob = async (req, res) => {
  try {
    const {
      customer,
      title,
      description,
      status,
      priority,
      schedule,
      scheduledDate, // Accept either schedule or scheduledDate
      estimatedDuration,
      location,
      assignedTo,
      notes,
      tasks,
      materials,
      metadata,
    } = req.body;

    // Generate job number
    const jobNumber = await generateJobNumber();

    // Analyze job complexity using AI
    const complexityAnalysis = await analyzeJobComplexity(description, tasks);

    // Get available technicians
    const availableTechnicians = await User.find({
      role: "Technicians",
      status: "active",
    });

    // Get optimal technician assignment
    const assignment = await optimizeTechnicianAssignment(
      {
        location,
        complexity: complexityAnalysis?.complexity,
        requiredSkills: complexityAnalysis?.recommendedSkills,
      },
      availableTechnicians
    );

    // Create the job with AI insights
    const job = await Job.create({
      jobNumber,
      customer,
      title,
      description,
      status,
      priority,
      // Use the scheduledDate directly if provided, otherwise extract from schedule
      scheduledDate:
        scheduledDate ||
        (schedule?.startDate ? new Date(schedule.startDate) : new Date()),
      estimatedDuration: estimatedDuration || 60, // Default to 60 minutes if not provided
      location,
      assignedTo: assignment?.recommendedTechnician || assignedTo,
      tasks,
      notes,
      materials,
      // Set required metadata fields using the authenticated user
      metadata: {
        createdBy: req.user.id,
        lastModifiedBy: req.user.id,
      },
      aiInsights: {
        complexity: complexityAnalysis?.complexity || "Medium",
        recommendedSkills: complexityAnalysis?.recommendedSkills || [],
        specialConsiderations: complexityAnalysis?.specialConsiderations || [],
        assignmentReasoning: assignment?.reasoning || "Manual assignment",
      },
    });

    // Create history record for job creation
    await createJobHistoryRecord(job._id, "job_created", {
      userId: req.user._id,
      userDisplayName: req.user.name,
      job: {
        _id: job._id,
        title: job.title,
        status: job.status,
      },
    });

    res.status(201).json(job);
  } catch (error) {
    console.error(error);
    res
      .status(500)
      .json({ message: "Error creating job", error: error.message });
  }
};

// @desc    Get all jobs with AI-powered insights
// @route   GET /api/jobs
// @access  Private
const getJobs = async (req, res) => {
  try {
    // Get pagination parameters
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    // Build filter object from query parameters
    const filter = {};

    if (req.query.status) {
      filter.status = req.query.status;
    }

    if (req.query.priority) {
      filter.priority = req.query.priority;
    }

    if (req.query.customer) {
      filter.customer = req.query.customer;
    }

    if (req.query.technician) {
      filter.assignedTechnicians = req.query.technician;
    }

    if (req.query.startDate && req.query.endDate) {
      filter.scheduledDate = {
        $gte: new Date(req.query.startDate),
        $lte: new Date(req.query.endDate),
      };
    }

    // Get jobs with pagination
    const jobs = await Job.find(filter)
      .populate("customer")
      .populate("assignedTechnicians")
      .sort({ scheduledDate: 1 })
      .skip(skip)
      .limit(limit);

    // Get total count for pagination
    const total = await Job.countDocuments(filter);

    // Calculate AI insights and priority scores for displayed jobs
    const jobsWithInsights = await Promise.all(
      jobs.map(async (job) => {
        // Only recalculate insights if necessary (not recently calculated)
        if (
          !job.aiInsights ||
          !job.aiInsights.lastAnalyzed ||
          new Date() - new Date(job.aiInsights.lastAnalyzed) > 3600000
        ) {
          // 1 hour

          // Calculate priority score
          const priorityScore = await calculatePriorityScore(job);

          // Suggest next steps
          const nextSteps = await suggestNextSteps(job);

          // Update job with insights
          job.aiInsights = {
            ...job.aiInsights,
            priorityScore,
            nextSteps,
            lastAnalyzed: new Date(),
          };

          await job.save();
        }

        return job;
      })
    );

    res.json({
      jobs: jobsWithInsights,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    });
  } catch (error) {
    console.error(error);
    res
      .status(500)
      .json({ message: "Error getting jobs", error: error.message });
  }
};

// @desc    Get job by ID
// @route   GET /api/jobs/:id
// @access  Private
const getJobById = async (req, res) => {
  try {
    const job = await Job.findById(req.params.id)
      .populate("customer")
      .populate("assignedTechnicians")
      .populate({
        path: "tasks.completedBy",
        select: "firstName lastName",
      })
      .populate({
        path: "notes.createdBy",
        select: "firstName lastName",
      });

    if (!job) {
      return res.status(404).json({ message: "Job not found" });
    }

    res.json(job);
  } catch (error) {
    console.error(error);
    res
      .status(500)
      .json({ message: "Error retrieving job", error: error.message });
  }
};

// @desc    Update job
// @route   PUT /api/jobs/:id
// @access  Private
const updateJob = async (req, res) => {
  try {
    let job = await Job.findById(req.params.id);

    if (!job) {
      return res.status(404).json({ message: "Job not found" });
    }

    // Update job fields
    const updates = req.body;

    // If description or tasks changed, reanalyze using AI
    if (updates.description || updates.tasks) {
      const complexityAnalysis = await analyzeJobComplexity(
        updates.description || job.description,
        updates.tasks || job.tasks
      );

      if (complexityAnalysis) {
        updates.aiInsights = {
          ...job.aiInsights,
          complexity: complexityAnalysis.complexity,
          recommendedSkills: complexityAnalysis.recommendedSkills,
          specialConsiderations: complexityAnalysis.specialConsiderations,
          lastAnalyzed: new Date(),
        };
      }
    }

    // Update metadata
    updates.metadata = {
      ...job.metadata,
      lastModifiedBy: req.user.id,
    };

    job = await Job.findByIdAndUpdate(
      req.params.id,
      { $set: updates },
      { new: true, runValidators: true }
    )
      .populate("customer")
      .populate("assignedTechnicians");

    // Track which fields were updated for history
    const updatedFields = [];
    const previousValues = {};

    // Fields that are tracked in history
    const trackedFields = [
      "title",
      "description",
      "customer",
      "priority",
      "complexity",
      "scheduledDate",
    ];

    // Store previous values before update
    trackedFields.forEach((field) => {
      if (req.body[field] !== undefined && req.body[field] !== job[field]) {
        updatedFields.push(field);
        previousValues[field] = job[field];
      }
    });

    // Special case for notes - track separately
    const notesUpdated =
      req.body.notes !== undefined && req.body.notes !== job.notes;

    // Create history record for general update if tracked fields were changed
    if (updatedFields.length > 0) {
      await createJobHistoryRecord(job._id, "job_updated", {
        userId: req.user._id,
        userDisplayName: req.user.name,
        updatedFields,
        previousValue: previousValues,
        newValue: updatedFields.reduce((acc, field) => {
          acc[field] = req.body[field];
          return acc;
        }, {}),
      });
    }

    // Create separate history record if notes were updated
    if (notesUpdated) {
      await createJobHistoryRecord(job._id, "note_added", {
        userId: req.user._id,
        userDisplayName: req.user.name,
        notes: req.body.notes,
      });
    }

    res.json(job);
  } catch (error) {
    console.error(error);
    res
      .status(500)
      .json({ message: "Error updating job", error: error.message });
  }
};

// @desc    Delete job
// @route   DELETE /api/jobs/:id
// @access  Private
const deleteJob = async (req, res) => {
  try {
    const job = await Job.findById(req.params.id);

    if (!job) {
      return res.status(404).json({ message: "Job not found" });
    }

    // Check for associated invoice
    if (job.invoice) {
      return res.status(400).json({
        message:
          "Cannot delete job with associated invoice. Delete the invoice first.",
      });
    }

    await job.remove();

    // Create history record for job deletion
    await createJobHistoryRecord(job._id, "job_deleted", {
      userId: req.user._id,
      userDisplayName: req.user.name,
      job: {
        _id: job._id,
        title: job.title,
        status: job.status,
      },
    });

    res.json({ message: "Job removed" });
  } catch (error) {
    console.error(error);
    res
      .status(500)
      .json({ message: "Error deleting job", error: error.message });
  }
};

// @desc    Update job status
// @route   PATCH /api/jobs/:id/status
// @access  Private
const updateJobStatus = async (req, res) => {
  try {
    const { status } = req.body;

    if (
      !["Scheduled", "In Progress", "Completed", "Cancelled"].includes(status)
    ) {
      return res.status(400).json({ message: "Invalid status value" });
    }

    const job = await Job.findByIdAndUpdate(
      req.params.id,
      {
        status,
        "metadata.lastModifiedBy": req.user.id,
      },
      { new: true }
    );

    if (!job) {
      return res.status(404).json({ message: "Job not found" });
    }

    // Create history record for status change
    await createJobHistoryRecord(job._id, "status_change", {
      userId: req.user._id,
      userDisplayName: req.user.name,
      previousValue: job.status,
      newValue: status,
      description: `Status changed from ${job.status} to ${status}`,
    });

    res.json(job);
  } catch (error) {
    console.error(error);
    res
      .status(500)
      .json({ message: "Error updating job status", error: error.message });
  }
};

// @desc    Add task to job
// @route   POST /api/jobs/:id/tasks
// @access  Private
const addJobTask = async (req, res) => {
  try {
    const { description } = req.body;

    if (!description) {
      return res.status(400).json({ message: "Task description is required" });
    }

    const job = await Job.findById(req.params.id);

    if (!job) {
      return res.status(404).json({ message: "Job not found" });
    }

    const newTask = {
      description,
      completed: false,
    };

    job.tasks.push(newTask);
    job.metadata.lastModifiedBy = req.user.id;

    await job.save();

    // Create history record for task addition
    await createJobHistoryRecord(job._id, "task_added", {
      userId: req.user._id,
      userDisplayName: req.user.name,
      task: {
        _id: newTask._id,
        description: newTask.description,
        status: newTask.status,
      },
    });

    res.json(job);
  } catch (error) {
    console.error(error);
    res
      .status(500)
      .json({ message: "Error adding task", error: error.message });
  }
};

// @desc    Update task status
// @route   PATCH /api/jobs/:id/tasks/:taskId
// @access  Private
const updateTaskStatus = async (req, res) => {
  try {
    const { completed } = req.body;

    if (typeof completed !== "boolean") {
      return res
        .status(400)
        .json({ message: "Completed status must be boolean" });
    }

    const job = await Job.findById(req.params.id);

    if (!job) {
      return res.status(404).json({ message: "Job not found" });
    }

    const taskIndex = job.tasks.findIndex(
      (task) => task._id.toString() === req.params.taskId
    );

    if (taskIndex === -1) {
      return res.status(404).json({ message: "Task not found" });
    }

    job.tasks[taskIndex].completed = completed;

    if (completed) {
      job.tasks[taskIndex].completedAt = new Date();
      job.tasks[taskIndex].completedBy = req.user.id;
    } else {
      job.tasks[taskIndex].completedAt = undefined;
      job.tasks[taskIndex].completedBy = undefined;
    }

    job.metadata.lastModifiedBy = req.user.id;

    await job.save();

    // Create appropriate history record based on status
    const actionType = completed ? "task_completed" : "task_updated";

    await createJobHistoryRecord(job._id, actionType, {
      userId: req.user._id,
      userDisplayName: req.user.name,
      task: {
        _id: job.tasks[taskIndex]._id,
        description: job.tasks[taskIndex].description,
        status: job.tasks[taskIndex].status,
      },
      previousValue: !completed,
      newValue: completed,
    });

    res.json(job);
  } catch (error) {
    console.error(error);
    res
      .status(500)
      .json({ message: "Error updating task", error: error.message });
  }
};

// Helper function to analyze risk factors
const analyzeRiskFactors = async (job) => {
  try {
    const systemPrompt = `You are a risk assessment expert specializing in job safety analysis.`;
    const userPrompt = `Analyze the following job for potential risk factors:
    
    Job Title: ${job.title || "Untitled Job"}
    Description: ${job.description || "No description provided"}
    Location: ${job.location?.address || "Unknown"}, ${
      job.location?.city || ""
    }, ${job.location?.state || ""}
    Tasks: ${
      job.tasks?.map((t) => t.description).join(", ") || "No tasks specified"
    }
    
    For each identified risk factor, provide:
    1. Risk factor name
    2. Severity (Low, Medium, High, Critical)
    3. Probability (Low, Medium, High)
    4. Recommended mitigation strategy
    
    IMPORTANT: Respond with a valid JSON array of risk objects with the following structure:
    [
      {
        "name": "Risk factor name",
        "severity": "Low|Medium|High|Critical",
        "probability": "Low|Medium|High",
        "mitigation": "Recommended mitigation strategy"
      },
      ...
    ]
    Do not include any explanation or additional text outside the JSON array.`;

    try {
      // Use getGeminiJsonResponse to get a properly formatted JSON response
      const risks = await getGeminiJsonResponse(systemPrompt, userPrompt, {
        temperature: 0.7,
        maxTokens: 800,
      });

      // Safety check to ensure we have a valid array
      if (!Array.isArray(risks)) {
        console.warn("Risk analysis returned non-array:", risks);
        return {
          risks: [],
          overallRiskLevel: "Low",
        };
      }

      // Determine overall risk level based on highest severity risk
      const severityMap = { Low: 1, Medium: 2, High: 3, Critical: 4 };
      let highestSeverity = "Low";

      for (const risk of risks) {
        if (
          risk.severity &&
          severityMap[risk.severity] > severityMap[highestSeverity]
        ) {
          highestSeverity = risk.severity;
        }
      }

      return {
        risks,
        overallRiskLevel: highestSeverity,
      };
    } catch (parseError) {
      console.error("JSON parsing error:", parseError);
      // Return default if parsing fails
      return {
        risks: [],
        overallRiskLevel: "Low",
      };
    }
  } catch (error) {
    console.error("Risk Analysis Error:", error);
    return {
      risks: [],
      overallRiskLevel: "Low",
    };
  }
};

// Helper function to calculate predicted completion
const calculatePredictedCompletion = async (job, completionRate) => {
  const remainingTasks = job.tasks.filter((t) => !t.completed).length;
  const estimatedHoursRemaining = remainingTasks / (completionRate || 0.5); // Default: 0.5 tasks/hour

  const now = new Date();
  const estimatedCompletion = new Date(
    now.getTime() + estimatedHoursRemaining * 60 * 60 * 1000
  );

  return {
    estimatedCompletion,
    confidenceScore: 70, // Default confidence score
    potentialDelays: [],
  };
};

// Helper function to find similar jobs
const findSimilarJobs = async (job) => {
  try {
    // Find jobs with similar attributes
    const similarJobs = await Job.find({
      _id: { $ne: job._id }, // Exclude current job
      $or: [
        { title: { $regex: new RegExp(job.title.split(" ")[0], "i") } },
        { "location.city": job.location.city },
        { "aiInsights.complexity": job.aiInsights?.complexity },
      ],
    }).limit(10);

    // Calculate similarity scores for each potential match
    const scoredJobs = similarJobs.map((similarJob) => {
      const { similarityScore, matchingFactors } =
        job.calculateSimilarityScore(similarJob);

      return {
        job: {
          _id: similarJob._id,
          title: similarJob.title,
          status: similarJob.status,
          scheduledDate: similarJob.scheduledDate,
        },
        similarityScore,
        matchingFactors,
      };
    });

    // Sort by similarity score (highest first)
    return scoredJobs.sort((a, b) => b.similarityScore - a.similarityScore);
  } catch (error) {
    console.error("Similar Jobs Error:", error);
    return [];
  }
};

// @desc    Assign technicians to job with AI recommendations
// @route   POST /api/jobs/assign
// @access  Private
const assignTechnicians = async (req, res) => {
  try {
    const { title, description, location, customer } = req.body;

    // Find available technicians
    const availableTechnicians = await User.find({
      role: "Technicians",
      status: "active",
    });

    if (availableTechnicians.length === 0) {
      return res
        .status(404)
        .json({ message: "No available technicians found" });
    }

    // Create a job-like object for analysis
    const jobForAnalysis = {
      title,
      description,
      location,
      customer,
    };

    // Analyze complexity to get required skills
    const complexityAnalysis = await analyzeJobComplexity(description, []);

    // Score technicians based on various factors
    const scoredTechnicians = await Promise.all(
      availableTechnicians.map(async (technician) => {
        // Calculate skill match score
        const skills = technician.skills || [];
        const requiredSkills = complexityAnalysis?.recommendedSkills || [];
        const skillMatchCount = skills.filter((s) =>
          requiredSkills.some((rs) =>
            rs.toLowerCase().includes(s.toLowerCase())
          )
        ).length;
        const skillMatchScore =
          requiredSkills.length > 0
            ? (skillMatchCount / requiredSkills.length) * 40
            : 20;

        // Calculate location proximity score (simplified)
        const locationScore = technician.currentLocation ? 30 : 15;

        // Calculate workload score (fewer jobs = higher score)
        const workloadScore = 20 - (technician.currentJobs?.length || 0) * 5;

        // Calculate total score
        const totalScore = Math.min(
          100,
          Math.max(
            0,
            skillMatchScore + locationScore + Math.max(0, workloadScore)
          )
        );

        // Generate reasoning
        const reasoning = [
          skillMatchCount > 0
            ? `Has ${skillMatchCount} matched skills`
            : "No specific skill matches",
          technician.currentLocation
            ? "Location data available"
            : "No location data",
          `Current workload: ${technician.currentJobs?.length || 0} jobs`,
        ].join(". ");

        return {
          technician: {
            _id: technician._id,
            name: `${technician.firstName} ${technician.lastName}`,
            skills: technician.skills,
          },
          score: totalScore,
          reasoning,
        };
      })
    );

    // Sort technicians by score
    const recommendations = scoredTechnicians.sort((a, b) => b.score - a.score);

    // Create history record for technician assignment
    await createJobHistoryRecord(null, "technician_assigned", {
      userId: req.user._id,
      userDisplayName: req.user.name,
      technicians: recommendations.map((rec) => rec.technician),
    });

    res.json({ recommendations });
  } catch (error) {
    console.error(error);
    res
      .status(500)
      .json({ message: "Error assigning technicians", error: error.message });
  }
};

// @desc    Analyze job risks
// @route   POST /api/jobs/analyze-risks
// @access  Private
const analyzeJobRisks = async (req, res) => {
  try {
    const jobData = req.body;

    // Create a job-like object if we're not analyzing an existing job
    let job;
    if (jobData._id) {
      job = await Job.findById(jobData._id);
      if (!job) {
        return res.status(404).json({ message: "Job not found" });
      }
    } else {
      job = jobData;
    }

    // Analyze risks
    const riskAnalysis = await analyzeRiskFactors(job);

    // If this is an existing job, update the risk assessment
    if (job._id) {
      job.riskAssessment = {
        risks: riskAnalysis.risks,
        overallRiskLevel: riskAnalysis.overallRiskLevel,
        lastAssessed: new Date(),
      };
      await job.save();
    }

    // Create history record for risk analysis
    await createJobHistoryRecord(job._id, "risk_analysis", {
      userId: req.user._id,
      userDisplayName: req.user.name,
      risks: riskAnalysis.risks,
      overallRiskLevel: riskAnalysis.overallRiskLevel,
    });

    res.json({
      jobId: job._id || null,
      risks: riskAnalysis.risks,
      overallRiskLevel: riskAnalysis.overallRiskLevel,
    });
  } catch (error) {
    console.error(error);
    res
      .status(500)
      .json({ message: "Error analyzing risks", error: error.message });
  }
};

// @desc    Find similar jobs
// @route   GET /api/jobs/:id/similar
// @access  Private
const getSimilarJobs = async (req, res) => {
  try {
    const job = await Job.findById(req.params.id);

    if (!job) {
      return res.status(404).json({ message: "Job not found" });
    }

    const similarJobs = await findSimilarJobs(job);

    res.json({ similarJobs });
  } catch (error) {
    console.error(error);
    res
      .status(500)
      .json({ message: "Error finding similar jobs", error: error.message });
  }
};

// @desc    Predict job progress
// @route   GET /api/jobs/:id/predict-progress
// @access  Private
const predictJobProgress = async (req, res) => {
  try {
    const job = await Job.findById(req.params.id);

    if (!job) {
      return res.status(404).json({ message: "Job not found" });
    }

    // Calculate completion rate based on completed tasks
    const completedTasks = job.tasks.filter((t) => t.completed);
    const completedWithTimes = completedTasks.filter((t) => t.completedAt);

    let completionRate = 0.5; // Default: 0.5 tasks per hour

    if (completedWithTimes.length >= 2) {
      // Sort completed tasks by completion time
      const sortedTasks = [...completedWithTimes].sort(
        (a, b) =>
          new Date(a.completedAt).getTime() - new Date(b.completedAt).getTime()
      );

      // Calculate time difference between first and last completed task
      const firstTime = new Date(sortedTasks[0].completedAt).getTime();
      const lastTime = new Date(
        sortedTasks[sortedTasks.length - 1].completedAt
      ).getTime();
      const hoursDifference = (lastTime - firstTime) / (1000 * 60 * 60);

      if (hoursDifference > 0) {
        completionRate = (sortedTasks.length - 1) / hoursDifference;
      }
    }

    // Get prediction
    const prediction = await calculatePredictedCompletion(job, completionRate);

    // Analyze potential delays using AI
    const prompt = `Based on the following job information, identify potential factors that could delay completion:
    
    Job Title: ${job.title}
    Description: ${job.description}
    Current Status: ${job.status}
    Task Progress: ${completedTasks.length}/${job.tasks.length} tasks completed
    Risk Level: ${job.riskAssessment?.overallRiskLevel || "Unknown"}
    
    List 2-3 specific potential delay factors as a JSON array of strings.`;

    try {
      const prompt2 = `${prompt}
      
      IMPORTANT: Respond with a valid JSON array of strings, each representing a potential delay factor.
      Format your response as: ["Delay factor 1", "Delay factor 2", "Delay factor 3"]
      Do not include any explanation or additional text outside the JSON array.`;

      const completion = await openai.chat.completions.create({
        model: "gpt-4",
        messages: [{ role: "user", content: prompt2 }],
        temperature: 0.7,
        response_format: { type: "json_object" },
      });

      try {
        // Try to parse the JSON response
        const potentialDelays = JSON.parse(
          completion.choices[0].message.content
        );
        prediction.potentialDelays = Array.isArray(potentialDelays)
          ? potentialDelays
          : ["Analysis of potential delays failed"];
      } catch (parseError) {
        console.error("JSON parsing error:", parseError);
        // If parsing fails, extract what looks like a JSON array
        const content = completion.choices[0].message.content;
        const match = content.match(/\[.*\]/s);
        if (match) {
          try {
            prediction.potentialDelays = JSON.parse(match[0]);
          } catch (e) {
            prediction.potentialDelays = [
              "Analysis of potential delays failed",
            ];
          }
        } else {
          prediction.potentialDelays = ["Analysis of potential delays failed"];
        }
      }
    } catch (error) {
      console.error("Delay Analysis Error:", error);
      prediction.potentialDelays = ["Analysis of potential delays failed"];
    }

    // Save prediction to job
    job.progressPrediction = {
      estimatedCompletion: prediction.estimatedCompletion,
      confidenceScore: prediction.confidenceScore,
      potentialDelays: prediction.potentialDelays,
      completionRate,
      lastUpdated: new Date(),
    };
    await job.save();

    // Create history record for progress prediction
    await createJobHistoryRecord(job._id, "progress_prediction", {
      userId: req.user._id,
      userDisplayName: req.user.name,
      prediction: {
        estimatedCompletion: prediction.estimatedCompletion,
        confidenceScore: prediction.confidenceScore,
        potentialDelays: prediction.potentialDelays,
      },
    });

    res.json({ prediction });
  } catch (error) {
    console.error(error);
    res
      .status(500)
      .json({ message: "Error predicting progress", error: error.message });
  }
};

// @desc    Get job history
// @route   GET /api/jobs/:id/history
// @access  Private
const getJobHistory = async (req, res) => {
  const jobId = req.params.id;

  // Find job to verify it exists and user has access
  const job = await Job.findById(jobId);

  if (!job) {
    res.status(404);
    throw new Error("Job not found");
  }

  // Get job history from the JobHistory collection
  const history = await JobHistory.find({ jobId }).sort({ timestamp: -1 });

  res.json(history);
};

// @desc    Upload attachments (images) for a job
// @route   POST /api/jobs/:id/upload
// @access  Private
const uploadJobAttachment = async (req, res) => {
  const jobId = req.params.id;
  const userId = req.user.id; // Assuming protect middleware adds user to req

  logger.info(
    `Attempting to upload attachments for job ${jobId} by user ${userId}`
  );

  try {
    const job = await Job.findById(jobId);
    if (!job) {
      logger.warn(`Job not found for attachment upload: ${jobId}`);
      return res.status(404).json(ApiError.notFound("Job not found"));
    }

    if (!req.files || req.files.length === 0) {
      logger.warn(`No files uploaded for job ${jobId}`);
      return res
        .status(400)
        .json(
          ApiError.badRequest(
            'No files uploaded. Ensure the field name is "jobImages".'
          )
        );
    }

    const attachments = req.files.map((file) => {
      // Construct relative URL for storage/retrieval
      const relativeUrl = `/uploads/jobs/${jobId}/${file.filename}`;
      logger.debug(
        `Processing uploaded file: ${file.originalname} -> ${file.filename}, URL: ${relativeUrl}, MIME: ${file.mimetype}`
      );
      return {
        filename: file.originalname, // Store original filename for user display
        url: relativeUrl, // Store relative path
        mimeType: file.mimetype,
        tag: req.body.tag || "general", // Get tag from request body or default
        uploadedBy: userId,
        uploadedAt: new Date(),
      };
    });

    // Add new attachments to the job's attachments array
    job.attachments.push(...attachments);
    job.metadata.lastModifiedBy = userId; // Update last modified

    const updatedJob = await job.save();

    logger.info(
      `Successfully uploaded ${attachments.length} attachments for job ${jobId}`
    );

    // Create history record for attachment upload
    await createJobHistoryRecord(job._id, "attachment_added", {
      userId: req.user._id,
      userDisplayName: req.user.name,
      attachments: attachments.map((att) => ({
        filename: att.filename,
        tag: att.tag,
      })), // Log basic info
    });

    res.status(200).json({
      message: `${attachments.length} file(s) uploaded successfully`,
      attachments: updatedJob.attachments, // Return the full updated list
    });
  } catch (error) {
    logger.error(
      `Error uploading attachments for job ${jobId}: ${error.message}`,
      { stack: error.stack }
    );
    // Handle potential multer errors (e.g., file size limit)
    if (error.code === "LIMIT_FILE_SIZE") {
      return res
        .status(400)
        .json(ApiError.badRequest("File size exceeds the limit (10MB)."));
    }
    if (error.message.includes("Invalid file type")) {
      return res.status(400).json(ApiError.badRequest(error.message));
    }
    res.status(500).json(ApiError.internal("Server error during file upload"));
  }
};

module.exports = {
  createJob,
  getJobs,
  getJobById,
  updateJob,
  deleteJob,
  updateJobStatus,
  addJobTask,
  updateTaskStatus,
  assignTechnicians,
  analyzeJobRisks,
  getSimilarJobs,
  predictJobProgress,
  getJobHistory,
  uploadJobAttachment, // Export the new controller function
};
