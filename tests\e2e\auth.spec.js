/**
 * Authentication End-to-End Tests
 * Tests user login, JWT token generation, and protected endpoints
 */

const { test, expect } = require('@playwright/test');

test.describe('Authentication System', () => {
  let authToken;
  const testUser = {
    email: '<EMAIL>',
    password: 'password123'
  };

  test('should login successfully and receive JWT token', async ({ request }) => {
    const response = await request.post('http://localhost:5000/api/users/login', {
      data: testUser
    });

    expect(response.status()).toBe(200);
    const responseData = await response.json();
    expect(responseData.success).toBe(true);
    expect(responseData.token).toBeDefined();
    expect(responseData.user).toBeDefined();
    expect(responseData.user.email).toBe(testUser.email);
    
    authToken = responseData.token;
  });

  test('should access protected endpoint with valid token', async ({ request }) => {
    const response = await request.get('http://localhost:5000/api/users/profile', {
      headers: {
        'Authorization': `<PERSON><PERSON> ${authToken}`
      }
    });

    expect(response.status()).toBe(200);
    const responseData = await response.json();
    expect(responseData.user).toBeDefined();
    expect(responseData.user.email).toBe(testUser.email);
  });

  test('should reject access without token', async ({ request }) => {
    const response = await request.get('http://localhost:5000/api/users/profile');
    expect(response.status()).toBe(401);
  });

  test('should reject access with invalid token', async ({ request }) => {
    const response = await request.get('http://localhost:5000/api/users/profile', {
      headers: {
        'Authorization': 'Bearer invalid-token'
      }
    });
    expect(response.status()).toBe(401);
  });
});