import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import axios from "axios";
import { resolveQuoteActionAPI } from "../services/quoteService"; // ADDED: Import for new service
import logger from "../utils/logger"; // ADDED: Assuming logger is available

// Async thunk for fetching quotes
export const getQuotes = createAsyncThunk(
  "quotes/getQuotes",
  async (
    {
      page = 1,
      limit = 10,
      status,
      customer,
      job,
      startDate,
      endDate,
      search,
      sortBy,
      sortOrder,
    },
    { getState, rejectWithValue }
  ) => {
    try {
      const { auth } = getState();
      // Construct params object, only including defined filters/sort options
      const params = { page, limit };
      if (status) params.status = status;
      if (customer) params.customer = customer;
      if (job) params.job = job;
      if (startDate) params.startDate = startDate;
      if (endDate) params.endDate = endDate;
      if (search) params.search = search;
      if (sortBy) params.sortBy = sortBy;
      if (sortOrder) params.sortOrder = sortOrder;

      const config = {
        headers: { Authorization: `Bearer ${auth?.userInfo?.token}` },
        params, // Use the constructed params object
      };
      const { data } = await axios.get("/api/quotes", config); // Updated path
      return data;
    } catch (error) {
      const message = error.response?.data?.message || error.message;
      return rejectWithValue(message);
    }
  }
);

// Async thunk for creating a quote
export const createQuote = createAsyncThunk(
  "quotes/createQuote",
  async (quoteData, { getState, rejectWithValue }) => {
    try {
      const { auth } = getState();
      const config = {
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${auth?.userInfo?.token}`,
        },
      };
      const { data } = await axios.post("/api/quotes", quoteData, config); // Updated path
      return data.data;
    } catch (error) {
      const message = error.response?.data?.message || error.message;
      return rejectWithValue(message);
    }
  }
);

// Async thunk for fetching a single quote by ID
export const getQuoteById = createAsyncThunk(
  "quotes/getQuoteById",
  async (id, { getState, rejectWithValue }) => {
    try {
      const { auth } = getState();
      const config = {
        headers: { Authorization: `Bearer ${auth?.userInfo?.token}` },
      };
      const { data } = await axios.get(`/api/quotes/${id}`, config); // Updated path
      return data.data;
    } catch (error) {
      const message = error.response?.data?.message || error.message;
      return rejectWithValue(message);
    }
  }
);

// Async thunk for updating quote status
export const updateQuoteStatus = createAsyncThunk(
  "quotes/updateQuoteStatus",
  async ({ id, status }, { getState, rejectWithValue }) => {
    try {
      const { auth } = getState();
      const config = {
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${auth?.userInfo?.token}`,
        },
      };
      // Use PUT to the main quote endpoint, sending only the status field for update
      const { data } = await axios.put(`/api/quotes/${id}`, { status }, config); // Updated path
      return data.data; // Expect updated quote
    } catch (error) {
      const message = error.response?.data?.message || error.message;
      return rejectWithValue(message);
    }
  }
);

// Async thunk for converting quote to invoice
export const convertQuoteToInvoice = createAsyncThunk(
  "quotes/convertQuoteToInvoice",
  async (id, { getState, rejectWithValue }) => {
    try {
      const { auth } = getState();
      const config = {
        headers: { Authorization: `Bearer ${auth?.userInfo?.token}` },
      };
      // Use the existing endpoint
      const { data } = await axios.post(
        `/api/invoices/from-quote/${id}`,
        {},
        config
      );
      return data; // Expect { success: true, invoiceId: '...' }
    } catch (error) {
      const message = error.response?.data?.message || error.message;
      return rejectWithValue(message);
    }
  }
);

// Async thunk for updating a full quote
export const updateQuote = createAsyncThunk(
  "quotes/updateQuote",
  async ({ id, quoteData }, { getState, rejectWithValue }) => {
    try {
      const { auth } = getState();
      const config = {
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${auth?.userInfo?.token}`,
        },
      };
      const { data } = await axios.put(`/api/quotes/${id}`, quoteData, config);
      return data.data; // Expect updated quote
    } catch (error) {
      const message = error.response?.data?.message || error.message;
      return rejectWithValue(message);
    }
  }
);

// Async thunk for deleting a quote
export const deleteQuote = createAsyncThunk(
  "quotes/deleteQuote",
  async (id, { getState, rejectWithValue }) => {
    try {
      const { auth } = getState();
      const config = {
        headers: { Authorization: `Bearer ${auth?.userInfo?.token}` },
      };
      await axios.delete(`/api/quotes/${id}`, config);
      return id; // Return the ID of the deleted quote for state update
    } catch (error) {
      const message = error.response?.data?.message || error.message;
      return rejectWithValue(message);
    }
  }
);

// Async thunk for emailing a quote
export const emailQuote = createAsyncThunk(
  "quotes/emailQuote",
  async ({ id, emailData }, { getState, rejectWithValue }) => {
    try {
      const { auth } = getState();
      const config = {
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${auth?.userInfo?.token}`,
        },
      };
      const { data } = await axios.post(
        `/api/quotes/${id}/send`,
        emailData,
        config
      );
      return data.data; // Return the updated quote (status might change to SENT)
    } catch (error) {
      const message = error.response?.data?.message || error.message;
      return rejectWithValue(message);
    }
  }
);

// Async thunk for generating quote PDF
export const generateQuotePdf = createAsyncThunk(
  "quotes/generateQuotePdf",
  async (id, { getState, rejectWithValue }) => {
    try {
      const { auth } = getState();
      const config = {
        headers: { Authorization: `Bearer ${auth?.userInfo?.token}` },
        responseType: "blob", // Important for handling PDF download
      };
      const response = await axios.get(`/api/quotes/${id}/pdf`, config);
      // Create a URL for the blob
      const fileURL = window.URL.createObjectURL(new Blob([response.data]));
      // Create a link element
      const fileLink = document.createElement("a");
      fileLink.href = fileURL;
      // Extract filename from content-disposition header or generate one
      const contentDisposition = response.headers["content-disposition"];
      let filename = `quote_${id}.pdf`; // Default filename
      if (contentDisposition) {
        const filenameMatch = contentDisposition.match(/filename="?(.+)"?/i);
        if (filenameMatch && filenameMatch.length === 2) {
          filename = filenameMatch[1];
        }
      }
      fileLink.setAttribute("download", filename);
      // Append to body, click, and remove
      document.body.appendChild(fileLink);
      fileLink.click();
      fileLink.remove();
      window.URL.revokeObjectURL(fileURL); // Clean up blob URL
      return { success: true }; // Indicate success
    } catch (error) {
      const message = error.response?.data?.message || error.message;
      // Handle blob error response if necessary
      if (error.response?.data instanceof Blob) {
        try {
          const errorText = await error.response.data.text();
          const errorJson = JSON.parse(errorText);
          return rejectWithValue(errorJson.message || "Failed to generate PDF");
        } catch (parseError) {
          return rejectWithValue(
            "Failed to generate PDF and parse error response."
          );
        }
      }
      return rejectWithValue(message);
    }
  }
);
// Async thunk for AI quote generation
export const generateQuoteAI = createAsyncThunk(
  "quotes/generateAI",
  // Accept the formData object directly
  async (formData, { getState, rejectWithValue }) => {
    try {
      const { auth } = getState();
      const config = {
        headers: {
          // DO NOT set Content-Type here; Axios will set it correctly for FormData
          Authorization: `Bearer ${auth?.userInfo?.token}`,
        },
      };

      // Always use the endpoint configured for multipart/form-data when sending FormData
      // The backend controller can handle cases with or without quoteId based on formData fields
      const { data } = await axios.post(
        "/api/ai/generate-quote-content",
        formData,
        config
      );

      // Determine quoteId from response or formData if needed for state update logic
      const quoteId = formData.quoteId || data.quoteId || null; // Access quoteId directly as a property

      // Return a consistent structure for the reducer
      return {
        ...data, // Include all response data
        status: data.success
          ? data.questions
            ? "pending_questions"
            : "complete"
          : "error", // Infer status if not explicit
        quoteId: quoteId, // Ensure quoteId is included for reducer logic
      };
    } catch (error) {
      const message = error.response?.data?.message || error.message;
      console.error(
        "Error in generateQuoteAI thunk:",
        error.response?.data || error
      ); // Log detailed error
      return rejectWithValue(message);
    }
  }
);

// Async thunk for answering AI questions
export const answerAIQuestions = createAsyncThunk(
  "quotes/answerAI",
  async ({ quoteId, answers }, { getState, rejectWithValue }) => {
    try {
      const { auth } = getState();
      const config = {
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${auth?.userInfo?.token}`,
        },
      };
      const { data } = await axios.post(
        `/api/quotes/${quoteId}/answer-ai`,
        { answers },
        config
      ); // Updated path
      return data; // Expects { success: true, status: 'complete', generatedData: {...}, quoteId: '...' }
    } catch (error) {
      const message = error.response?.data?.message || error.message;
      return rejectWithValue(message);
    }
  }
);

// NEW: Async thunk for answering AI questions for draft quotes (no quoteId required)
export const answerDraftQuoteQuestions = createAsyncThunk(
  "quotes/answerDraftAI",
  async ({ draftQuoteData, answers }, { getState, rejectWithValue }) => {
    try {
      const { auth } = getState();
      const config = {
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${auth?.userInfo?.token}`,
        },
      };

      const requestPayload = {
        draftQuoteData,
        answers,
      };

      const { data } = await axios.post(
        "/api/ai/clarify-draft-quote",
        requestPayload,
        config
      );
      return data; // Expects { success: true, status: 'complete', generatedData: {...}, clarificationApplied: true }
    } catch (error) {
      const message = error.response?.data?.message || error.message;
      return rejectWithValue(message);
    }
  }
);

// Async thunk for uploading images to a specific quote
export const uploadQuoteImages = createAsyncThunk(
  "quotes/uploadQuoteImages",
  async ({ quoteId, formData }, { getState, rejectWithValue }) => {
    try {
      const { auth } = getState();
      const config = {
        headers: {
          // Content-Type is set automatically for FormData by axios
          Authorization: `Bearer ${auth?.userInfo?.token}`,
        },
      };
      const { data } = await axios.post(
        `/api/quotes/${quoteId}/images`,
        formData,
        config
      );
      // Return quoteId along with the image data for the reducer
      return { quoteId, images: data.images };
    } catch (error) {
      const message = error.response?.data?.message || error.message;
      return rejectWithValue(message);
    }
  }
);
// ADDED: Async thunk for resolving a quote item action
export const resolveQuoteItemAction = createAsyncThunk(
  "quotes/resolveQuoteItemAction",
  async (
    { quoteId, actionDetails, itemIdentifier },
    { getState, rejectWithValue }
  ) => {
    try {
      const { auth } = getState();
      const token = auth?.userInfo?.token;
      if (!token) {
        logger.warn("resolveQuoteItemAction: Authentication token not found.");
        return rejectWithValue("Authentication token not found.");
      }

      // The itemIdentifier is passed through to be used in the reducer for updating the specific item
      const resolvedData = await resolveQuoteActionAPI(
        quoteId,
        actionDetails,
        () => token
      );
      return { resolvedData, itemIdentifier, quoteId }; // Pass itemIdentifier and quoteId for reducer
    } catch (error) {
      const message = error.message || "Failed to resolve quote action.";
      logger.error(
        `resolveQuoteItemAction Error for quote ${quoteId}, item ${itemIdentifier}: ${message}`,
        { error }
      );
      return rejectWithValue({ message, itemIdentifier });
    }
  }
);

// Initial state for quotes
const initialState = {
  quotes: [],
  pagination: {},
  currentQuote: null, // For storing a single quote when viewing details
  quote: null, // For storing the active quote being edited/viewed
  itemResolving: {}, // ADDED: To track loading state for individual items, e.g., { [itemIdentifier]: true }
  itemError: {}, // ADDED: To track errors for individual items, e.g., { [itemIdentifier]: 'Error message' }
  loading: false, // General loading for list/detail fetching
  error: null, // General error for list/detail fetching
  filters: {}, // Corrected: filters is an empty object or placeholder for future filter criteria,
  // The following are general state properties
  currentPage: 1,
  totalPages: 1,
  totalQuotes: 0,
  conversionResult: null, // To store result of quote-to-invoice conversion
  // AI Specific State
  aiLoading: false, // Loading state specifically for AI operations
  aiError: null, // Error state specifically for AI operations
  aiStatus: "idle", // Tracks the AI generation process ('idle', 'pending_questions', 'generating', 'complete', 'error')
  aiQuestions: [], // Stores questions asked by AI
  draftQuote: null, // Holds data for a quote being created/edited
};

const quoteSlice = createSlice({
  name: "quotes",
  initialState,
  reducers: {
    resetQuoteState: (state) => {
      state.loading = false;
      state.error = null;
      state.quote = null;
      state.conversionResult = null;
    },
    clearQuoteError: (state) => {
      state.error = null;
    },
    clearAIState: (state) => {
      // Add reducer to clear AI state if needed
      state.aiLoading = false;
      state.aiError = null;
      state.aiStatus = "idle";
      state.aiQuestions = [];
    },
    updateWorkingQuoteData: (state, action) => {
      // FIXED: Merge the payload into the current quote state while preserving all existing data
      // This keeps unsaved changes in Redux temporarily
      if (state.quote) {
        state.quote = { ...state.quote, ...action.payload };
      } else {
        // If there's no current quote (e.g., creating new), initialize it
        state.quote = action.payload;
      }

      // CRITICAL: Also update the draft quote to maintain consistency
      // This ensures that form state and Redux state stay in sync
      if (state.draftQuote) {
        state.draftQuote = { ...state.draftQuote, ...action.payload };
      }
    },
    setDraftQuote: (state, action) => {
      state.draftQuote = action.payload;
    },
    clearDraftQuote: (state) => {
      state.draftQuote = null;
    },
  },
  extraReducers: (builder) => {
    builder
      // Get Quotes List
      .addCase(getQuotes.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getQuotes.fulfilled, (state, action) => {
        state.loading = false;
        state.quotes = action.payload.data || [];
        state.totalQuotes = action.payload.total || 0;
        state.currentPage = action.payload.page || 1;
        state.totalPages = action.payload.totalPages || 1;
      })
      .addCase(getQuotes.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      // Create Quote
      .addCase(createQuote.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(createQuote.fulfilled, (state, action) => {
        state.loading = false; /* Optionally update list */
      })
      .addCase(createQuote.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      // Get Quote By ID
      .addCase(getQuoteById.pending, (state) => {
        state.loading = true;
        state.quote = null;
        state.error = null;
      })
      .addCase(getQuoteById.fulfilled, (state, action) => {
        state.loading = false;
        state.quote = action.payload;
      })
      .addCase(getQuoteById.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
        state.quote = null;
      }) // Clear quote on error
      // Update Quote Status
      .addCase(updateQuoteStatus.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(updateQuoteStatus.fulfilled, (state, action) => {
        state.loading = false;
        // Update the detailed quote view if it's the one being updated
        if (state.quote?._id === action.payload._id) {
          state.quote = { ...state.quote, ...action.payload };
        }
        // Update the quote in the list as well
        const index = state.quotes.findIndex(
          (q) => q._id === action.payload._id
        );
        if (index !== -1) {
          state.quotes[index] = { ...state.quotes[index], ...action.payload };
        }
      })
      .addCase(updateQuoteStatus.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      // Convert Quote to Invoice
      .addCase(convertQuoteToInvoice.pending, (state) => {
        state.loading = true;
        state.error = null;
        state.conversionResult = null;
      })
      .addCase(convertQuoteToInvoice.fulfilled, (state, action) => {
        state.loading = false;
        state.conversionResult = action.payload;
        // Update quote status locally
        const quoteId = action.meta.arg;
        if (state.quote?._id === quoteId) {
          state.quote.status = "CONVERTED"; // Assuming backend sets this status
        }
        const index = state.quotes.findIndex((q) => q._id === quoteId);
        if (index !== -1) {
          state.quotes[index].status = "CONVERTED";
        }
      })
      .addCase(convertQuoteToInvoice.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      // Generate Quote AI
      .addCase(generateQuoteAI.pending, (state) => {
        state.aiLoading = true;
        state.aiError = null;
        state.aiStatus = "generating";
        state.aiQuestions = [];
      })
      .addCase(generateQuoteAI.fulfilled, (state, action) => {
        state.aiLoading = false;

        // Extract all possible fields from the response
        const {
          status,
          questions,
          quoteId,
          generatedData = {},
          items = [],
          overall_summary,
          ai_confidence_score,
          message,
        } = action.payload;

        // Ensure we have items from either top-level or generatedData
        const responseItems = Array.isArray(items)
          ? items
          : generatedData && Array.isArray(generatedData.items)
          ? generatedData.items
          : [];

        // Get the project overview from either source
        const projectOverviewContent =
          generatedData.projectOverview || overall_summary || "";

        // Get the scope of work from either source
        const scopeOfWorkContent = generatedData.scopeOfWork || "";

        // Get the materials included from either source
        const materialsIncludedContent = generatedData.materialsIncluded || "";

        const currentDraft = state.draftQuote || {};
        // FIXED: Preserve ALL existing form data including customer, name, images, and job
        let newDraftData = {
          ...currentDraft, // Start with ALL existing draft data
          quoteId: quoteId || currentDraft.quoteId, // Persist quoteId or keep existing
          projectOverview:
            projectOverviewContent || currentDraft.projectOverview || "",
          scopeOfWork: scopeOfWorkContent || currentDraft.scopeOfWork || "",
          materialsIncluded:
            materialsIncludedContent || currentDraft.materialsIncluded || "",
          items:
            responseItems.length > 0 ? responseItems : currentDraft.items || [],
          ai_confidence_score:
            ai_confidence_score !== undefined
              ? ai_confidence_score
              : currentDraft.ai_confidence_score || null,
          // CRITICAL: Explicitly preserve customer, name, job, and images
          name: currentDraft.name || "", // Preserve quote name/title
          customer: currentDraft.customer || null, // Preserve customer selection
          job: currentDraft.job || null, // Preserve job selection
          validUntil: currentDraft.validUntil || null, // Preserve valid until date
          taxRate:
            currentDraft.taxRate !== undefined ? currentDraft.taxRate : 8.5, // Preserve tax rate
          status: currentDraft.status || "DRAFT", // Preserve status
          // Preserve any image-related data if it exists
          associatedImages: currentDraft.associatedImages || [],
          // Preserve labor settings
          labor: currentDraft.labor || {},
          includeLaborCosts:
            currentDraft.includeLaborCosts !== undefined
              ? currentDraft.includeLaborCosts
              : false,
        };

        if (status === "pending_questions") {
          state.aiStatus = "pending_questions"; // Internal slice status
          state.aiQuestions = questions || [];
          newDraftData = {
            ...newDraftData, // Keep all preserved data
            aiGenerationStatus: "needs_clarification", // For form consumption
            aiClarificationQuestions: questions || [],
          };
          // Prioritize generatedData if available
          if (generatedData) {
            newDraftData.projectOverview =
              generatedData.projectOverview ||
              currentDraft.projectOverview ||
              "";
            newDraftData.scopeOfWork =
              generatedData.scopeOfWork || currentDraft.scopeOfWork || "";
            newDraftData.materialsIncluded =
              generatedData.materialsIncluded ||
              currentDraft.materialsIncluded ||
              "";
            if (generatedData.items && Array.isArray(generatedData.items)) {
              // Append or replace items? Let's append for now, user can edit
              newDraftData.items = [
                ...(currentDraft.items || []),
                ...generatedData.items,
              ];
            }
          } else {
            // Fallback for older structures or if generatedData is missing
            newDraftData.items = items || currentDraft.items || [];
            newDraftData.projectOverview =
              overall_summary || currentDraft.projectOverview || ""; // Use overall_summary as fallback for overview
            // Attempt to construct scopeOfWork/materialsIncluded from items/summary if needed
            if (!newDraftData.scopeOfWork && overall_summary) {
              newDraftData.scopeOfWork = `Scope of Work:\n${overall_summary}`; // Simple construction
            }
            if (
              !newDraftData.materialsIncluded &&
              items &&
              Array.isArray(items)
            ) {
              newDraftData.materialsIncluded =
                "Materials Included:\n" +
                items
                  .map(
                    (item) => `- ${item.name} (${item.quantity} ${item.unit})`
                  )
                  .join("\n"); // Simple construction
            }
          }

          // Update state.quote if it's the one being worked on
          if (quoteId && state.quote?._id === quoteId) {
            state.quote.aiGenerationStatus = "pending_questions";
            state.quote.aiClarificationQuestions = questions || [];
            // Apply similar logic to state.quote
            if (generatedData) {
              state.quote.projectOverview =
                generatedData.projectOverview ||
                state.quote.projectOverview ||
                "";
              state.quote.scopeOfWork =
                generatedData.scopeOfWork || state.quote.scopeOfWork || "";
              state.quote.materialsIncluded =
                generatedData.materialsIncluded ||
                state.quote.materialsIncluded ||
                "";
              if (generatedData.items && Array.isArray(generatedData.items)) {
                state.quote.items = [
                  ...(state.quote.items || []),
                  ...generatedData.items,
                ];
              }
            } else {
              state.quote.items = items || state.quote.items || [];
              state.quote.projectOverview =
                overall_summary || state.quote.projectOverview || "";
              if (!state.quote.scopeOfWork && overall_summary) {
                state.quote.scopeOfWork = `Scope of Work:\n${overall_summary}`;
              }
              if (
                !state.quote.materialsIncluded &&
                items &&
                Array.isArray(items)
              ) {
                state.quote.materialsIncluded =
                  "Materials Included:\n" +
                  items
                    .map(
                      (item) => `- ${item.name} (${item.quantity} ${item.unit})`
                    )
                    .join("\n");
              }
            }
          }
        } else if (
          status === "complete" ||
          status === "complete_needs_review"
        ) {
          state.aiStatus = status;
          state.aiQuestions = [];
          newDraftData = {
            ...newDraftData, // Keep all preserved data
            aiGenerationStatus: status,
            aiClarificationQuestions: [],
          };
          if (ai_confidence_score !== undefined) {
            newDraftData.ai_confidence_score = ai_confidence_score;
          }

          // Prioritize generatedData if available
          if (generatedData) {
            newDraftData.projectOverview = generatedData.projectOverview || "";
            newDraftData.scopeOfWork = generatedData.scopeOfWork || "";
            newDraftData.materialsIncluded =
              generatedData.materialsIncluded || "";
            if (generatedData.items && Array.isArray(generatedData.items)) {
              // Replace items with the final generated list
              newDraftData.items = generatedData.items;
            }
          } else {
            // Fallback for older structures or if generatedData is missing
            newDraftData.items = items || [];
            newDraftData.projectOverview = overall_summary || ""; // Use overall_summary as fallback for overview
            // Attempt to construct scopeOfWork/materialsIncluded from items/summary if needed
            if (!newDraftData.scopeOfWork && overall_summary) {
              newDraftData.scopeOfWork = `Scope of Work:\n${overall_summary}`; // Simple construction
            }
            if (
              !newDraftData.materialsIncluded &&
              items &&
              Array.isArray(items)
            ) {
              newDraftData.materialsIncluded =
                "Materials Included:\n" +
                items
                  .map(
                    (item) => `- ${item.name} (${item.quantity} ${item.unit})`
                  )
                  .join("\n"); // Simple construction
            }
          }

          // Update state.quote (detailed view for an existing quote)
          if (state.quote?._id === quoteId) {
            state.quote.aiGenerationStatus = status;
            state.quote.aiClarificationQuestions = [];
            state.quote.aiUserInputAnswers = {}; // Clear submitted answers
            if (ai_confidence_score !== undefined) {
              state.quote.ai_confidence_score = ai_confidence_score;
            }
            // Apply similar logic to state.quote
            if (generatedData) {
              state.quote.projectOverview = generatedData.projectOverview || "";
              state.quote.scopeOfWork = generatedData.scopeOfWork || "";
              state.quote.materialsIncluded =
                generatedData.materialsIncluded || "";
              if (generatedData.items && Array.isArray(generatedData.items)) {
                state.quote.items = generatedData.items;
              }
            } else {
              state.quote.items = items || state.quote.items || [];
              state.quote.projectOverview =
                overall_summary || state.quote.projectOverview || "";
              if (!state.quote.scopeOfWork && overall_summary) {
                state.quote.scopeOfWork = `Scope of Work:\n${overall_summary}`;
              }
              if (
                !state.quote.materialsIncluded &&
                items &&
                Array.isArray(items)
              ) {
                state.quote.materialsIncluded =
                  "Materials Included:\n" +
                  items
                    .map(
                      (item) => `- ${item.name} (${item.quantity} ${item.unit})`
                    )
                    .join("\n");
              }
            }
          }
        } else {
          // Handle error or unexpected status
          state.aiStatus = "error";
          state.aiError =
            message || "Unexpected AI response status or error structure.";
          newDraftData.aiGenerationStatus = "error";
        }
        state.draftQuote = newDraftData; // Assign the new object reference
      })
      .addCase(generateQuoteAI.rejected, (state, action) => {
        state.aiLoading = false;
        state.aiError = action.payload;
        state.aiStatus = "error";
      })
      // Answer AI Questions
      .addCase(answerAIQuestions.pending, (state) => {
        state.aiLoading = true;
        state.aiError = null;
        state.aiStatus = "generating";
      })
      .addCase(answerAIQuestions.fulfilled, (state, action) => {
        state.aiLoading = false;
        const {
          status: aiStatusResult,
          generatedData,
          quoteId: resultQuoteId,
          message: aiMessage,
          ai_confidence_score,
        } = action.payload;

        const currentDraft = state.draftQuote || {};
        // FIXED: Preserve ALL existing form data including customer, name, images, and job
        let newDraftData = {
          ...currentDraft, // Start with ALL existing draft data
          quoteId: resultQuoteId || currentDraft.quoteId, // Persist quoteId or keep existing
          // CRITICAL: Explicitly preserve customer, name, job, and images
          name: currentDraft.name || "", // Preserve quote name/title
          customer: currentDraft.customer || null, // Preserve customer selection
          job: currentDraft.job || null, // Preserve job selection
          validUntil: currentDraft.validUntil || null, // Preserve valid until date
          taxRate:
            currentDraft.taxRate !== undefined ? currentDraft.taxRate : 8.5, // Preserve tax rate
          status: currentDraft.status || "DRAFT", // Preserve status
          // Preserve any image-related data if it exists
          associatedImages: currentDraft.associatedImages || [],
          // Preserve labor settings
          labor: currentDraft.labor || {},
          includeLaborCosts:
            currentDraft.includeLaborCosts !== undefined
              ? currentDraft.includeLaborCosts
              : false,
        };

        if (
          aiStatusResult === "complete" ||
          aiStatusResult === "complete_needs_review"
        ) {
          state.aiStatus = aiStatusResult;
          state.aiQuestions = []; // Clear any previous questions

          newDraftData = {
            ...newDraftData, // Keep all preserved data
            aiGenerationStatus: aiStatusResult,
            aiClarificationQuestions: [],
          };
          if (ai_confidence_score !== undefined) {
            newDraftData.ai_confidence_score = ai_confidence_score;
          }

          if (generatedData) {
            newDraftData.projectOverview =
              generatedData.projectOverview ||
              currentDraft.projectOverview ||
              "";
            newDraftData.scopeOfWork =
              generatedData.scopeOfWork || currentDraft.scopeOfWork || "";
            newDraftData.materialsIncluded =
              generatedData.materialsIncluded ||
              currentDraft.materialsIncluded ||
              "";
            if (generatedData.items && Array.isArray(generatedData.items)) {
              // Replace items with the final generated list
              newDraftData.items = generatedData.items;
            }
          }

          // Update state.quote (detailed view for an existing quote)
          if (state.quote?._id === resultQuoteId) {
            state.quote.aiGenerationStatus = aiStatusResult;
            state.quote.aiClarificationQuestions = [];
            state.quote.aiUserInputAnswers = {}; // Clear submitted answers
            if (ai_confidence_score !== undefined) {
              state.quote.ai_confidence_score = ai_confidence_score;
            }
            if (generatedData) {
              state.quote.projectOverview =
                generatedData.projectOverview ||
                state.quote.projectOverview ||
                "";
              state.quote.scopeOfWork =
                generatedData.scopeOfWork || state.quote.scopeOfWork || "";
              state.quote.materialsIncluded =
                generatedData.materialsIncluded ||
                state.quote.materialsIncluded ||
                "";
              if (generatedData.items && Array.isArray(generatedData.items)) {
                state.quote.items = generatedData.items;
              }
            }
          }
        } else {
          state.aiStatus = "error";
          state.aiError =
            aiMessage || "Failed to process AI answers or unexpected status.";
          newDraftData.aiGenerationStatus = "error";
        }
        state.draftQuote = newDraftData; // Assign the new object reference
      })
      .addCase(answerAIQuestions.rejected, (state, action) => {
        state.aiLoading = false;
        state.aiError = action.payload;
        state.aiStatus = "error";
      }) // Removed semicolon
      // Answer Draft Quote Questions (NEW)
      .addCase(answerDraftQuoteQuestions.pending, (state) => {
        state.aiLoading = true;
        state.aiError = null;
        state.aiStatus = "generating";
      })
      .addCase(answerDraftQuoteQuestions.fulfilled, (state, action) => {
        state.aiLoading = false;
        const { status, generatedData, message, clarificationApplied } =
          action.payload;

        const currentDraft = state.draftQuote || {};
        // Preserve ALL existing form data while applying clarification results
        let newDraftData = {
          ...currentDraft, // Start with ALL existing draft data
          // CRITICAL: Explicitly preserve customer, name, job, and images
          name: currentDraft.name || "", // Preserve quote name/title
          customer: currentDraft.customer || null, // Preserve customer selection
          job: currentDraft.job || null, // Preserve job selection
          validUntil: currentDraft.validUntil || null, // Preserve valid until date
          taxRate:
            currentDraft.taxRate !== undefined ? currentDraft.taxRate : 8.5, // Preserve tax rate
          status: currentDraft.status || "DRAFT", // Preserve status
          // Preserve any image-related data if it exists
          associatedImages: currentDraft.associatedImages || [],
          // Preserve labor settings
          labor: currentDraft.labor || {},
          includeLaborCosts:
            currentDraft.includeLaborCosts !== undefined
              ? currentDraft.includeLaborCosts
              : false,
        };

        if (status === "complete" && clarificationApplied) {
          state.aiStatus = "complete";
          state.aiQuestions = []; // Clear any previous questions

          newDraftData = {
            ...newDraftData, // Keep all preserved data
            aiGenerationStatus: "complete",
            aiClarificationQuestions: [],
          };

          // Apply the clarification-enhanced data
          if (generatedData) {
            newDraftData.projectOverview =
              generatedData.projectOverview ||
              currentDraft.projectOverview ||
              "";
            newDraftData.scopeOfWork =
              generatedData.scopeOfWork || currentDraft.scopeOfWork || "";
            newDraftData.materialsIncluded =
              generatedData.materialsIncluded ||
              currentDraft.materialsIncluded ||
              "";
            if (generatedData.items && Array.isArray(generatedData.items)) {
              // Replace items with the clarification-enhanced list
              newDraftData.items = generatedData.items;
            }
          }
        } else {
          state.aiStatus = "error";
          state.aiError = message || "Failed to process draft clarification.";
          newDraftData.aiGenerationStatus = "error";
        }

        state.draftQuote = newDraftData; // Assign the new object reference
      })
      .addCase(answerDraftQuoteQuestions.rejected, (state, action) => {
        state.aiLoading = false;
        state.aiError = action.payload;
        state.aiStatus = "error";
      })
      // Update Quote (Full)
      .addCase(updateQuote.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(updateQuote.fulfilled, (state, action) => {
        state.loading = false;
        // Update the detailed quote view if it's the one being updated
        if (state.quote?._id === action.payload._id) {
          state.quote = action.payload; // Replace with the full updated quote
        }
        // Update the quote in the list as well
        const index = state.quotes.findIndex(
          (q) => q._id === action.payload._id
        );
        if (index !== -1) {
          state.quotes[index] = action.payload; // Replace with the full updated quote
        }
      })
      .addCase(updateQuote.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      // Delete Quote
      .addCase(deleteQuote.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(deleteQuote.fulfilled, (state, action) => {
        state.loading = false;
        // Remove the quote from the list
        state.quotes = state.quotes.filter((q) => q._id !== action.payload);
        state.totalQuotes = state.totalQuotes > 0 ? state.totalQuotes - 1 : 0;
        // Clear the detailed view if it was the deleted quote
        if (state.quote?._id === action.payload) {
          state.quote = null;
        }
      })
      .addCase(deleteQuote.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      // Email Quote
      .addCase(emailQuote.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(emailQuote.fulfilled, (state, action) => {
        state.loading = false;
        // Update the quote status in detail view and list if returned
        if (action.payload?._id) {
          if (state.quote?._id === action.payload._id) {
            state.quote = { ...state.quote, ...action.payload };
          }
          const index = state.quotes.findIndex(
            (q) => q._id === action.payload._id
          );
          if (index !== -1) {
            state.quotes[index] = { ...state.quotes[index], ...action.payload };
          }
        }
      })
      .addCase(emailQuote.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      // Generate Quote PDF
      .addCase(generateQuotePdf.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(generateQuotePdf.fulfilled, (state) => {
        state.loading = false; /* No state change needed */
      })
      .addCase(generateQuotePdf.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      // Upload Quote Images
      .addCase(uploadQuoteImages.pending, (state) => {
        // Optionally set a specific loading state for image uploads if needed
        // state.imageUploading = true;
        state.error = null; // Clear previous errors
      })
      .addCase(uploadQuoteImages.fulfilled, (state, action) => {
        // state.imageUploading = false;
        // If the currently viewed/edited quote matches the upload target, add the new images
        if (
          state.quote?._id === action.payload.quoteId &&
          action.payload.images
        ) {
          if (!state.quote.associatedImages) {
            state.quote.associatedImages = [];
          }
          state.quote.associatedImages.push(...action.payload.images);
        }
        // Note: This doesn't update the main quotes list state.
        // If immediate reflection in the list is needed after upload without re-fetching,
        // additional logic would be required here or potentially in the customerSlice
        // if the customer object in the quotes list is deeply populated.
      })
      .addCase(uploadQuoteImages.rejected, (state, action) => {
        // state.imageUploading = false;
        state.error = action.payload; // Set general error or a specific image upload error state
      });
  },
});

export const {
  resetQuoteState,
  clearQuoteError,
  clearAIState,
  updateWorkingQuoteData,
  setDraftQuote,
  clearDraftQuote,
  setQuoteFilters,
  resetCurrentQuote,
  clearItemResolveError, // ADDED: Export new action
} = quoteSlice.actions;

export default quoteSlice.reducer;
