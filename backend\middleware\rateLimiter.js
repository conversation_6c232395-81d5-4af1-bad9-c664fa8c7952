const ApiError = require("../utils/ApiError");
const rateLimit = require("express-rate-limit");
const { RedisStore } = require("rate-limit-redis"); // Use named import
const { redis } = require("./cache");

/**
 * Create rate limiter with Redis store
 * @param {Object} options - Rate limiter options
 * @returns {Function} Express middleware
 */
const rateLimiter = (options = {}) => {
  // Define default options
  const defaultOptions = {
    windowMs: 60 * 1000, // 1 minute
    max: 100, // Limit each IP to 100 requests per windowMs
    message: "Too many requests from this IP, please try again later.",
    statusCode: 429,
    headers: true,
    handler: (req, res, next) => {
      next(
        new ApiError(429, "Rate limit exceeded", {
          retryAfter: Math.ceil(options.windowMs / 1000),
          limit: options.max,
          windowMs: options.windowMs,
        })
      );
    },
    skip: (req) => {
      // Skip rate limiting for trusted IPs or internal requests
      return req.ip === "127.0.0.1" || req.ip === "::1";
    },
    legacyHeaders: false, // Disable the `X-RateLimit-*` headers
    standardHeaders: true, // Use RFC standard rate limit headers
  };

  // Add Redis store if Redis is available (not disabled and not in test)
  if (!process.env.REDIS_DISABLED && process.env.NODE_ENV !== "test") {
    try {
      defaultOptions.store = new RedisStore({
        sendCommand: (...args) => redis.call(...args),
      });
    } catch (error) {
      // Fall back to memory store if Redis fails
      console.warn(
        "Redis store failed, using memory store for rate limiting:",
        error.message
      );
    }
  }

  // Merge default options with user options
  const limiterOptions = { ...defaultOptions, ...options };

  // Create and return the limiter
  return rateLimit(limiterOptions);
};

/**
 * Create endpoint-specific rate limiter
 * @param {string} endpoint - Endpoint identifier
 * @param {Object} options - Rate limiter options
 * @returns {Function} Express middleware
 */
const endpointLimiter = (endpoint, options = {}) => {
  const endpointOptions = {
    ...options,
    keyGenerator: (req) => {
      // Generate unique key based on IP and endpoint
      return `${req.ip}:${endpoint}`;
    },
    skip: (req) => {
      // Skip rate limiting for trusted IPs or internal requests
      return req.ip === "127.0.0.1" || req.ip === "::1";
    },
  };

  return rateLimiter(endpointOptions);
};

/**
 * Create user-specific rate limiter
 * @param {Object} options - Rate limiter options
 * @returns {Function} Express middleware
 */
const userLimiter = (options = {}) => {
  const userOptions = {
    ...options,
    keyGenerator: (req) => {
      // Generate unique key based on user ID
      return `user:${req.user?._id}`;
    },
    skip: (req) => {
      // Skip rate limiting for admins
      return req.user?.role === "admin";
    },
  };

  return rateLimiter(userOptions);
};

/**
 * Create AI endpoint rate limiter
 * @param {Object} options - Rate limiter options
 * @returns {Function} Express middleware
 */
const createAiLimiter = (options = {}) => {
  const aiOptions = {
    windowMs: 60 * 60 * 1000, // 1 hour
    max: 100, // 100 AI requests per hour
    message: "AI request limit exceeded. Please try again later.",
    ...options,
    keyGenerator: (req) => {
      // Generate unique key based on user ID for AI endpoints
      return `ai:${req.user?._id}`;
    },
  };

  return rateLimiter(aiOptions);
};

// Create AI rate limiter instance at initialization time
const aiLimiter = createAiLimiter();

/**
 * Create dynamic rate limiter based on user tier
 * @param {Object} options - Rate limiter options
 * @returns {Function} Express middleware
 */
const tierLimiter = (options = {}) => {
  return async (req, res, next) => {
    const tierLimits = {
      free: 100,
      basic: 500,
      premium: 1000,
      enterprise: 5000,
    };

    const userTier = req.user?.tier || "free";
    const limit = tierLimits[userTier];

    const tierOptions = {
      windowMs: 60 * 60 * 1000, // 1 hour
      max: limit,
      message: `Rate limit of ${limit} requests per hour exceeded for ${userTier} tier.`,
      ...options,
      keyGenerator: (req) => {
        return `tier:${userTier}:${req.user?._id}`;
      },
    };

    const limiter = rateLimiter(tierOptions);
    return limiter(req, res, next);
  };
};

/**
 * Monitor rate limit hits
 * @returns {Object} Rate limit statistics
 */
// Temporarily disabled while using in-memory store
// const monitorRateLimits = async () => {
//   return {
//     total: 0,
//     blocked: 0,
//     endpoints: {}
//   };
// };

// Create standard rate limiter for general API endpoints
const standardLimiter = rateLimiter({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // 100 requests per 15 minutes
  message: "Too many requests from this IP, please try again after 15 minutes",
});

// Create auth rate limiter for authentication endpoints
const authLimiter = rateLimiter({
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 10, // 10 requests per hour
  message: "Too many authentication attempts, please try again after an hour",
});

module.exports = {
  rateLimiter,
  endpointLimiter,
  userLimiter,
  aiLimiter,
  tierLimiter,
  // monitorRateLimits,
  standardLimiter,
  authLimiter,
};
