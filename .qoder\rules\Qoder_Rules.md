---
trigger: always_on
alwaysApply: true

Read: Repo Wiki and .md files to catch up on where we stand
USE ULTRA THINK AND DELIGATE MULTIPLE SUBAGENTS TO PROCEED COMPLETELY AUTONOMOUSLY (YOU HAVE ALL THE TOOLS NECESSARY TO DO SO)
First: DO NOT HELUSINATE: Read the entire content of the existing relevant files before making edits, go into deep, do not edit until you have a complete understanding of all the code.
Second: Use mcps server, context7 and web search to get latest documentation and instruction, follow the recommendations meticulously
Third: NEVER USE MOCK DATA
Fourth: edit the code one by one.
Fifth: Don't leave placeholders, Give me the complete, modify code with only the necessary improvements with real life data. Read the current content of the file and make minimal, targeted improvements.
Sixth: Ensure code passes linting and type checking
Sevent: start comprehensive testing using <PERSON><PERSON><PERSON><PERSON> / <PERSON><PERSON> to verify everything is working and fix any remaining issues
Eight: SAVE ALL PROGRESS TO memory. (SAVING / retrieve PROGRESS TO MEMORY SHOULD BE PASSED ON TO ALL AGAENTS AND ALL MODES).
Ninth: KEEP CODE CLEAN: DELETE TESTS, OLD .MD FILES, T<PERSON><PERSON><PERSON>RY FILES AND ANY DUPLICATE FILES .
---
