/**
 * Token Configuration Guidelines for Gemini AI Models
 *
 * Based on official Gemini documentation:
 * - Gemini 2.5 Pro: Supports up to 65,536 output tokens
 * - Property name: Use 'maxOutputTokens' in Gemini API calls
 * - Our code maps 'maxTokens' to 'maxOutputTokens' in geminiService.js
 */

const TOKEN_LIMITS = {
  // Model-specific maximum limits
  GEMINI_25_PRO: {
    INPUT: 1048576, // 1M tokens
    OUTPUT: 65536, // 65K tokens
  },
  GEMINI_15_PRO: {
    INPUT: 32760,
    OUTPUT: 8192,
  },
  GEMINI_15_FLASH: {
    INPUT: 32760,
    OUTPUT: 8192,
  },

  // Recommended limits by use case
  USE_CASES: {
    // Short responses (single values, scores, brief answers)
    SHORT: 500,

    // Medium responses (descriptions, summaries)
    MEDIUM: 2000,

    // Long responses (detailed analysis, multiple items)
    LONG: 5000,

    // Complex JSON responses (full reports, multiple entities)
    COMPLEX_JSON: 8000,

    // Maximum safe limit (allows for very detailed responses)
    MAX_SAFE: 16000,
  },

  // Specific use case recommendations
  RECOMMENDATIONS: {
    // Priority scoring, simple calculations
    priorityScore: 500,

    // Single material/item descriptions
    materialDescription: 1000,

    // Customer insights, analysis
    customerInsights: 3000,

    // Service recommendations, multiple items
    serviceRecommendations: 2500,

    // Next steps suggestions, action items
    nextSteps: 2000,

    // Email templates, communications
    emailTemplates: 3000,

    // Complex reports, multiple sections
    reports: 8000,

    // General API responses with JSON
    jsonResponse: 4000,
  },
};

/**
 * Get recommended token limit for a specific use case
 * @param {string} useCase - The type of use case
 * @returns {number} Recommended token limit
 */
function getRecommendedTokens(useCase) {
  return TOKEN_LIMITS.RECOMMENDATIONS[useCase] || TOKEN_LIMITS.USE_CASES.MEDIUM;
}

/**
 * Validate token limit against model capabilities
 * @param {number} requestedTokens - Requested token limit
 * @param {string} modelName - Model name
 * @returns {number} Safe token limit
 */
function getSafeTokenLimit(requestedTokens, modelName = "gemini-2.5-pro") {
  const modelLimits = modelName.includes("2.5")
    ? TOKEN_LIMITS.GEMINI_25_PRO
    : TOKEN_LIMITS.GEMINI_15_PRO;

  // Ensure we don't exceed model limits
  return Math.min(requestedTokens, modelLimits.OUTPUT);
}

module.exports = {
  TOKEN_LIMITS,
  getRecommendedTokens,
  getSafeTokenLimit,
};
