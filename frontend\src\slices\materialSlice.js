import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import axios from "axios";

const initialState = {
  materialItems: [],
  item: null, // Keep original 'item' for potential backward compatibility
  material: null, // State for single material detail view
  sourceStatus: null, // Added to store status info (e.g., { status: 'BLOCKED', reason: '...', ... })
  loading: false,
  error: null,
  success: false,
  totalPages: 1,
  currentPage: 1,
  totalItems: 0, // Added totalItems for pagination count consistency
  categories: [],
  stats: {
    totalItems: 0,
    lowStock: 0,
    categories: 0,
    value: 0,
  },
  transactions: [],
  transactionsLoading: false,
  transactionsError: null, // Added specific error state for transactions
  transactionsPagination: {
    currentPage: 1,
    totalPages: 1,
    totalTransactions: 0,
    hasNextPage: false,
    hasPrevPage: false,
  },
};

// --- Async Thunks ---

// Get all material items with pagination and filters
export const getMaterialItems = createAsyncThunk(
  "material/getMaterialItems",
  async (
    { page = 1, limit = 10, filters = {} },
    { getState, rejectWithValue }
  ) => {
    try {
      const { auth } = getState();
      const config = {
        headers: {
          Authorization: `Bearer ${auth?.userInfo?.token}`,
        },
        params: { page, limit, ...filters },
      };
      const { data } = await axios.get("/api/materials", config);
      return {
        items: data.materials || data.data || [],
        totalPages: data.pages || data.totalPages || 1, // Use 'pages' if available from backend
        currentPage: data.page || data.currentPage || 1,
        totalItems: data.total || 0,
      };
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || error.message);
    }
  }
);

// Get material by ID
export const getMaterialById = createAsyncThunk(
  "material/getMaterialById",
  async (id, { getState, rejectWithValue }) => {
    try {
      const { auth } = getState();
      const config = {
        headers: { Authorization: `Bearer ${auth?.userInfo?.token}` },
      };
      const { data } = await axios.get(`/api/materials/${id}`, config);
      return data.data || data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || error.message);
    }
  }
);

// Create new material item
export const createMaterialItem = createAsyncThunk(
  "material/createMaterialItem",
  async (itemData, { getState, rejectWithValue }) => {
    try {
      const { auth } = getState();
      const config = {
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${auth?.userInfo?.token}`,
        },
      };
      const { data } = await axios.post("/api/materials", itemData, config);
      return data.data || data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || error.message);
    }
  }
);

// Update material item
export const updateMaterialItem = createAsyncThunk(
  "material/updateMaterialItem",
  async ({ id, itemData }, { getState, rejectWithValue }) => {
    try {
      const { auth } = getState();
      const config = {
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${auth?.userInfo?.token}`,
        },
      };
      const { data } = await axios.put(
        `/api/materials/${id}`,
        itemData,
        config
      );
      return data.data || data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || error.message);
    }
  }
);

// Delete material item
export const deleteMaterialItem = createAsyncThunk(
  "material/deleteMaterialItem",
  async (id, { getState, rejectWithValue }) => {
    try {
      const { auth } = getState();
      const config = {
        headers: { Authorization: `Bearer ${auth?.userInfo?.token}` },
      };
      await axios.delete(`/api/materials/${id}`, config);
      return id;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || error.message);
    }
  }
);

// Adjust material quantity
export const adjustMaterialQuantity = createAsyncThunk(
  "material/adjustQuantity",
  async (
    { id, change, type, notes, relatedDocId, relatedDocModel },
    { getState, rejectWithValue }
  ) => {
    // Added transaction details
    try {
      const { auth } = getState();
      const config = {
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${auth?.userInfo?.token}`,
        },
      };
      const payload = { change, type, notes, relatedDocId, relatedDocModel }; // Construct payload
      const { data } = await axios.patch(
        `/api/materials/${id}/quantity`,
        payload, // Send full payload
        config
      );
      return data.data || data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || error.message);
    }
  }
);

// Get material categories
export const getMaterialCategories = createAsyncThunk(
  "material/getCategories",
  async (_, { getState, rejectWithValue }) => {
    try {
      const { auth } = getState();
      const config = {
        headers: { Authorization: `Bearer ${auth?.userInfo?.token}` },
      };
      const { data } = await axios.get("/api/materials/categories", config);
      return data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || error.message);
    }
  }
);

// Get material stats
export const getMaterialStats = createAsyncThunk(
  "material/getStats",
  async (_, { getState, rejectWithValue }) => {
    try {
      const { auth } = getState();
      const config = {
        headers: { Authorization: `Bearer ${auth?.userInfo?.token}` },
      };
      const { data } = await axios.get("/api/materials/stats", config);
      return data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || error.message);
    }
  }
);

// Get material transactions history
export const getMaterialTransactions = createAsyncThunk(
  "material/getTransactions",
  async ({ id, page = 1, limit = 10 }, { getState, rejectWithValue }) => {
    try {
      const { auth } = getState();
      const config = {
        headers: { Authorization: `Bearer ${auth?.userInfo?.token}` },
        params: { page, limit },
      };
      const { data } = await axios.get(
        `/api/materials/${id}/transactions`,
        config
      );
      return data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || error.message);
    }
  }
);

// Search materials using the pricing endpoint
export const searchMaterialsPricing = createAsyncThunk(
  "material/searchPricing",
  async (
    { query, sourceId, limit = 10, skipCache },
    { getState, rejectWithValue }
  ) => {
    // Added skipCache
    try {
      const { auth } = getState();
      const config = {
        headers: { Authorization: `Bearer ${auth?.userInfo?.token}` },
        params: { query, sourceId, limit, skipCache }, // Pass skipCache to backend
      };
      const { data } = await axios.get("/api/quotes/materials/search", config); // Updated endpoint to match backend route
      return data.data || [];
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || error.message);
    }
  }
);

// Refresh material price from external source via SKU
export const refreshMaterialPrice = createAsyncThunk(
  "material/refreshPrice",
  async ({ sku, sourceId }, { getState, rejectWithValue }) => {
    try {
      const { auth } = getState();
      const config = {
        headers: { Authorization: `Bearer ${auth?.userInfo?.token}` },
        params: { sourceId, skipCache: true },
      };
      const { data } = await axios.get(
        `/api/material-pricing/sku/${sku}`,
        config
      );
      if (data.success && data.data?.price !== undefined) {
        return { sku, price: data.data.price, source: data.data.source };
      } else {
        throw new Error("Price not found in API response.");
      }
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || error.message);
    }
  }
);

// Get AI Material Suggestions
export const getAiMaterialSuggestions = createAsyncThunk(
  "material/getAiSuggestions",
  async ({ description, jobType }, { getState, rejectWithValue }) => {
    try {
      const { auth } = getState();
      const config = {
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${auth?.userInfo?.token}`,
        },
      };
      const { data } = await axios.post(
        "/api/ai/suggest-materials",
        { description, jobType },
        config
      );
      return data.suggestions || [];
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || error.message);
    }
  }
);

// Get AI Material Description Suggestion
export const generateAiMaterialDescription = createAsyncThunk(
  "material/generateAiDescription",
  async ({ name }, { getState, rejectWithValue }) => {
    if (!name) {
      return rejectWithValue(
        "Material name is required to generate description."
      );
    }
    try {
      const { auth } = getState();
      const config = {
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${auth?.userInfo?.token}`,
        },
      };
      // NOTE: Backend endpoint /api/ai/generate-material-description needs to be created.
      const { data } = await axios.post(
        "/api/ai/generate-material-description",
        { name },
        config
      );
      return data.description || "";
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || error.message);
    }
  }
);

// Fetch detailed material info by URL (for description/category after selection)
export const fetchMaterialDetailsByUrl = createAsyncThunk(
  "material/fetchDetailsByUrl",
  async ({ url }, { getState, rejectWithValue }) => {
    if (!url) {
      return rejectWithValue("URL is required to fetch details.");
    }
    try {
      const { auth } = getState();
      const config = {
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${auth?.userInfo?.token}`,
        },
      };
      // NOTE: Backend endpoint /api/material-pricing/details-by-url needs to be created.
      const { data } = await axios.post(
        "/api/material-pricing/details-by-url",
        { url },
        config
      );
      // Expecting backend to return { description: '...', category: '...' } or similar
      return data.data || data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || error.message);
    }
  }
);

// --- Slice Definition ---

const materialSlice = createSlice({
  name: "material",
  initialState,
  reducers: {
    resetMaterialState: (state) => {
      state.loading = false;
      state.error = null;
      state.success = false;
      state.material = null;
      state.item = null;
    },
    clearMaterialError: (state) => {
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      // Get material items
      .addCase(getMaterialItems.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getMaterialItems.fulfilled, (state, action) => {
        state.loading = false;
        state.materialItems = action.payload.items;
        state.totalPages = action.payload.totalPages;
        state.currentPage = action.payload.currentPage;
        state.totalItems = action.payload.totalItems;
        state.success = true;
      })
      .addCase(getMaterialItems.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Get Material By ID
      .addCase(getMaterialById.pending, (state) => {
        state.loading = true;
        state.material = null;
        state.item = null;
        state.error = null;
      })
      .addCase(getMaterialById.fulfilled, (state, action) => {
        state.loading = false;
        // Check if the payload has the new structure { data: material, sourceStatus: status }
        if (
          action.payload &&
          typeof action.payload === "object" &&
          "data" in action.payload
        ) {
          state.material = action.payload.data; // Material data might be null if blocked + no cache
          state.item = action.payload.data; // Keep item in sync
          state.sourceStatus = action.payload.sourceStatus || null; // Store status if present
        } else {
          // Handle potential old response structure or direct material object
          state.material = action.payload;
          state.item = action.payload;
          state.sourceStatus = null; // No status info provided
        }
        state.success = true;
      })
      .addCase(getMaterialById.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Create material item
      .addCase(createMaterialItem.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(createMaterialItem.fulfilled, (state, action) => {
        state.loading = false;
        const newItem = action.payload;
        // Check if the item returned from the backend (newItem) already exists in our state array
        const index = state.materialItems.findIndex(
          (item) => item._id === newItem._id
        );

        if (index !== -1) {
          // Item exists (it was an update), replace it in the array
          state.materialItems[index] = newItem;
        } else {
          // Item is genuinely new, add it to the beginning of the array
          state.materialItems.unshift(newItem);
        }
        state.success = true;
      })
      .addCase(createMaterialItem.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Update material item
      .addCase(updateMaterialItem.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(updateMaterialItem.fulfilled, (state, action) => {
        state.loading = false;
        const index = state.materialItems.findIndex(
          (item) => item._id === action.payload._id
        );
        if (index !== -1) {
          state.materialItems[index] = action.payload;
        }
        if (state.material?._id === action.payload._id) {
          state.material = action.payload;
        }
        if (state.item?._id === action.payload._id) {
          state.item = action.payload;
        }
        state.success = true;
      })
      .addCase(updateMaterialItem.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Delete material item
      .addCase(deleteMaterialItem.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(deleteMaterialItem.fulfilled, (state, action) => {
        state.loading = false;
        state.materialItems = state.materialItems.filter(
          (item) => item._id !== action.payload
        );
        state.success = true;
      })
      .addCase(deleteMaterialItem.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Adjust material quantity
      .addCase(adjustMaterialQuantity.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(adjustMaterialQuantity.fulfilled, (state, action) => {
        state.loading = false;
        const index = state.materialItems.findIndex(
          (item) => item._id === action.payload._id
        );
        if (index !== -1) {
          state.materialItems[index] = action.payload;
        }
        if (state.material?._id === action.payload._id) {
          state.material = action.payload;
        }
        if (state.item?._id === action.payload._id) {
          state.item = action.payload;
        }
        state.success = true;
      })
      .addCase(adjustMaterialQuantity.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Get material categories
      .addCase(getMaterialCategories.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getMaterialCategories.fulfilled, (state, action) => {
        state.loading = false;
        state.categories = action.payload;
      })
      .addCase(getMaterialCategories.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Get material stats
      .addCase(getMaterialStats.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getMaterialStats.fulfilled, (state, action) => {
        state.loading = false;
        state.stats = action.payload;
      })
      .addCase(getMaterialStats.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Get material transactions
      .addCase(getMaterialTransactions.pending, (state) => {
        state.transactionsLoading = true;
        state.error = null;
        state.transactionsError = null;
      })
      .addCase(getMaterialTransactions.fulfilled, (state, action) => {
        state.transactionsLoading = false;
        state.transactions = action.payload.transactions;
        // Handle pagination for transactions
        state.transactionsPagination = {
          currentPage: action.payload.currentPage || 1,
          totalPages: action.payload.totalPages || 1,
          totalTransactions: action.payload.totalTransactions || 0,
          hasNextPage: action.payload.hasNextPage || false,
          hasPrevPage: action.payload.hasPrevPage || false,
        };
      })
      .addCase(getMaterialTransactions.rejected, (state, action) => {
        state.transactionsLoading = false;
        state.transactionsError = action.payload;
      })

      // Search Materials Pricing
      .addCase(searchMaterialsPricing.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(searchMaterialsPricing.fulfilled, (state, action) => {
        state.loading = false;
        // Search results handled in component state
      })
      .addCase(searchMaterialsPricing.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Refresh Material Price
      .addCase(refreshMaterialPrice.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(refreshMaterialPrice.fulfilled, (state, action) => {
        state.loading = false;
        const index = state.materialItems.findIndex(
          (item) => item.sku === action.payload.sku
        );
        if (index !== -1) {
          state.materialItems[index].unitPrice = action.payload.price;
        }
        if (state.material?.sku === action.payload.sku) {
          state.material.unitPrice = action.payload.price;
        }
        if (state.item?.sku === action.payload.sku) {
          state.item.unitPrice = action.payload.price;
        }
      })
      .addCase(refreshMaterialPrice.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Get AI Material Suggestions
      .addCase(getAiMaterialSuggestions.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getAiMaterialSuggestions.fulfilled, (state, action) => {
        state.loading = false;
        // Suggestions handled in component state
      })
      .addCase(getAiMaterialSuggestions.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Generate AI Material Description
      .addCase(generateAiMaterialDescription.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(generateAiMaterialDescription.fulfilled, (state, action) => {
        state.loading = false;
        // Description handled in component state
      })
      .addCase(generateAiMaterialDescription.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      // Fetch Material Details By URL
      .addCase(fetchMaterialDetailsByUrl.pending, (state) => {
        // Optionally set a specific loading state for details
        // state.detailLoading = true;
        state.error = null; // Clear previous errors
      })
      .addCase(fetchMaterialDetailsByUrl.fulfilled, (state, action) => {
        // state.detailLoading = false;
        // We don't store these details globally, they are used directly in the component
      })
      .addCase(fetchMaterialDetailsByUrl.rejected, (state, action) => {
        // state.detailLoading = false;
        state.error = action.payload; // Store error, maybe display it?
      });
  },
});

export const { resetMaterialState, clearMaterialError } = materialSlice.actions;

export default materialSlice.reducer;
