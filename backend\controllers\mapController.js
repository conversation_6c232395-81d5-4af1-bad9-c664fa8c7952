const axios = require("axios");
const ApiError = require("../utils/ApiError");
const logger = require("../utils/logger");

const GOOGLE_MAPS_API_KEY = process.env.GOOGLE_MAPS_API_KEY;
const PLACES_AUTOCOMPLETE_URL =
  "https://maps.googleapis.com/maps/api/place/autocomplete/json";

/**
 * Fetches place autocomplete suggestions from Google Places API Web Service.
 * @route GET /api/maps/autocomplete
 */
async function getPlaceAutocomplete(req, res, next) {
  const { input, country = "us" } = req.query; // Default country to 'us'

  if (!input) {
    return next(new ApiError(400, "Input query parameter is required"));
  }

  if (!GOOGLE_MAPS_API_KEY) {
    logger.error(
      "Google Maps API Key (GOOGLE_MAPS_API_KEY) is not configured."
    );
    return next(new ApiError(500, "Maps service configuration error"));
  }

  const params = {
    input: input,
    key: GOOGLE_MAPS_API_KEY,
    types: "address", // Restrict to addresses
    components: `country:${country}`,
  };

  try {
    logger.debug(
      `Calling Google Places Autocomplete API for input: "${input}", country: ${country}`
    );
    const response = await axios.get(PLACES_AUTOCOMPLETE_URL, { params });

    if (response.data.status === "OK") {
      // Return only the predictions array
      res.json({ predictions: response.data.predictions || [] });
    } else if (response.data.status === "ZERO_RESULTS") {
      res.json({ predictions: [] });
    } else {
      // Log Google's error message if available
      const googleError = response.data.error_message || response.data.status;
      logger.error(`Google Places Autocomplete API Error: ${googleError}`);
      throw new ApiError(
        500,
        `Failed to fetch place suggestions: ${googleError}`
      );
    }
  } catch (error) {
    logger.error(
      "Error calling Google Places Autocomplete API:",
      error.response ? error.response.data : error.message
    );
    // Avoid exposing detailed internal errors or API keys
    next(new ApiError(500, "Failed to fetch place suggestions"));
  }
}

module.exports = {
  getPlaceAutocomplete,
};
