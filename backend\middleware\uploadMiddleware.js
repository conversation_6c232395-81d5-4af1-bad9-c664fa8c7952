const multer = require("multer");
const path = require("path");
const fs = require("fs");
const logger = require("../utils/logger"); // Import logger

// Define allowed MIME types for images based on Gemini docs
const allowedMimeTypes = [
  "image/png",
  "image/jpeg",
  "image/webp",
  "image/heic",
  "image/heif",
];

// Configure storage engine
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    let uploadPath;

    // Determine the upload path based on the route
    if (
      req.path === "/generate-quote-content" ||
      req.originalUrl.includes("/ai/generate-quote-content")
    ) {
      // Use a temporary directory for AI context images
      uploadPath = path.join(__dirname, "..", "uploads", "ai-context", "temp");
      logger.debug(
        `[Upload Middleware] Using AI context temp path: ${uploadPath} for route ${req.originalUrl}`
      );
    } else if (
      req.originalUrl.includes("/customers/") &&
      req.originalUrl.includes("/images")
    ) {
      // Customer image uploads
      const customerId = req.params.id;
      if (!customerId) {
        logger.error(
          "[Upload Middleware] Customer ID missing for customer image upload."
        );
        return cb(
          new Error("Customer ID is required for this upload destination"),
          null
        );
      }
      // Basic validation for customerId format
      if (!/^[0-9a-fA-F]{24}$/.test(customerId)) {
        logger.error(
          `[Upload Middleware] Invalid Customer ID format: ${customerId}`
        );
        return cb(new Error("Invalid Customer ID format"), null);
      }
      uploadPath = path.join(
        __dirname,
        "..",
        "uploads",
        "customers",
        customerId
      );
      logger.debug(
        `[Upload Middleware] Using customer-specific path: ${uploadPath}`
      );
    } else if (
      req.originalUrl.includes("/jobs/") &&
      req.originalUrl.includes("/upload")
    ) {
      // Job image uploads
      const jobId = req.params.id;
      if (!jobId) {
        logger.error(
          "[Upload Middleware] Job ID missing for job image upload."
        );
        return cb(
          new Error("Job ID is required for this upload destination"),
          null
        );
      }
      // Basic validation for jobId format
      if (!/^[0-9a-fA-F]{24}$/.test(jobId)) {
        logger.error(`[Upload Middleware] Invalid Job ID format: ${jobId}`);
        return cb(new Error("Invalid Job ID format"), null);
      }
      uploadPath = path.join(__dirname, "..", "uploads", "jobs", jobId);
      logger.debug(
        `[Upload Middleware] Using job-specific path: ${uploadPath}`
      );
    } else if (
      req.originalUrl.includes("/quotes/") &&
      req.originalUrl.includes("/images")
    ) {
      // Quote image uploads - Save to a temporary location first
      const quoteId = req.params.id;
      if (!quoteId || !/^[0-9a-fA-F]{24}$/.test(quoteId)) {
        logger.error(`[Upload Middleware] Invalid Quote ID format: ${quoteId}`);
        return cb(new Error("Invalid Quote ID format"), null);
      }
      uploadPath = path.join(__dirname, "..", "uploads", "temp_quote_images"); // Temporary directory
      logger.debug(
        `[Upload Middleware] Using temporary quote image path: ${uploadPath}`
      );
    } else {
      // Default upload path for other routes
      logger.error(
        `[Upload Middleware] Unrecognized upload route: ${req.originalUrl}`
      );
      return cb(new Error("Unrecognized upload route"), null);
    }

    // Ensure the determined directory exists
    try {
      fs.mkdirSync(uploadPath, { recursive: true });
      cb(null, uploadPath);
    } catch (error) {
      logger.error(
        `[Upload Middleware] Failed to create upload directory: ${uploadPath}`,
        { error: error.message }
      );
      cb(error, null);
    }
  },
  filename: (req, file, cb) => {
    // Create a unique filename: timestamp + original filename
    const uniqueSuffix = Date.now() + "-" + Math.round(Math.random() * 1e9);
    const extension = path.extname(file.originalname);
    const safeOriginalName = file.originalname.replace(/[^a-zA-Z0-9._-]/g, "_"); // Sanitize original name
    cb(null, uniqueSuffix + "-" + safeOriginalName);
  },
});

// Configure file filter
const fileFilter = (req, file, cb) => {
  // Log the file field name and type to help debug multer issues
  logger.debug(
    `[Upload Middleware] Processing file upload: field=${file.fieldname}, filename=${file.originalname}, mimetype=${file.mimetype}`
  );

  if (allowedMimeTypes.includes(file.mimetype)) {
    cb(null, true); // Accept file
  } else {
    logger.warn(
      `Upload rejected: Invalid file type ${file.mimetype} for file ${file.originalname}`
    );
    cb(
      new Error(
        "Invalid file type. Only PNG, JPEG, WEBP, HEIC, HEIF are allowed."
      ),
      false
    ); // Reject file
  }
};

// Configure limits (e.g., 10MB per file)
const limits = {
  fileSize: 10 * 1024 * 1024, // 10 MB in bytes
};

// Initialize multer instance
// We'll use upload.array(fieldName, maxCount) in routes to specify the field name and limit
// Each route can specify its own field name (e.g., 'jobImages', 'contextImages', etc.)
const upload = multer({
  storage: storage,
  fileFilter: fileFilter,
  limits: limits,
});

// Export both the configured multer instance and its components for reuse
module.exports = upload;

// Also export individual components for custom multer configurations
module.exports.storage = storage;
module.exports.fileFilter = fileFilter;
module.exports.limits = limits;
