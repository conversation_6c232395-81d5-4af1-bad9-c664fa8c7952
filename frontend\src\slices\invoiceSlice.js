import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import axios from "axios";

const initialState = {
  invoices: [],
  invoice: null,
  loading: false,
  error: null,
  success: false,
  totalPages: 1,
  currentPage: 1,
  stats: {
    totalInvoices: 0,
    paid: 0,
    pending: 0,
    overdue: 0,
    totalAmount: 0,
    paidAmount: 0,
    overdueAmount: 0,
  },
  payments: [],
  paymentsLoading: false,
};

// Get all invoices with pagination and filters
export const getInvoices = createAsyncThunk(
  "invoices/getInvoices",
  async (
    { page = 1, limit = 10, filters = {} },
    { getState, rejectWithValue }
  ) => {
    try {
      const { auth } = getState();

      const config = {
        headers: {
          Authorization: `Bearer ${auth.userInfo.token}`,
        },
        params: {
          page,
          limit,
          ...filters,
        },
      };

      const { data } = await axios.get("/api/invoices", config);
      return data;
    } catch (error) {
      return rejectWithValue(
        error.response && error.response.data.message
          ? error.response.data.message
          : error.message
      );
    }
  }
);

// Get invoice by ID
export const getInvoiceById = createAsyncThunk(
  "invoices/getInvoiceById",
  async (id, { getState, rejectWithValue }) => {
    try {
      const { auth } = getState();

      const config = {
        headers: {
          Authorization: `Bearer ${auth.userInfo.token}`,
        },
      };

      const { data } = await axios.get(`/api/invoices/${id}`, config);
      return data;
    } catch (error) {
      return rejectWithValue(
        error.response && error.response.data.message
          ? error.response.data.message
          : error.message
      );
    }
  }
);

// Create new invoice
export const createInvoice = createAsyncThunk(
  "invoices/createInvoice",
  async (invoiceData, { getState, rejectWithValue }) => {
    try {
      const { auth } = getState();

      const config = {
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${auth.userInfo.token}`,
        },
      };

      const { data } = await axios.post("/api/invoices", invoiceData, config);
      return data;
    } catch (error) {
      return rejectWithValue(
        error.response && error.response.data.message
          ? error.response.data.message
          : error.message
      );
    }
  }
);

// Update invoice
export const updateInvoice = createAsyncThunk(
  "invoices/updateInvoice",
  async ({ id, invoiceData }, { getState, rejectWithValue }) => {
    try {
      const { auth } = getState();

      const config = {
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${auth.userInfo.token}`,
        },
      };

      const { data } = await axios.put(
        `/api/invoices/${id}`,
        invoiceData,
        config
      );
      return data;
    } catch (error) {
      return rejectWithValue(
        error.response && error.response.data.message
          ? error.response.data.message
          : error.message
      );
    }
  }
);

// Delete invoice
export const deleteInvoice = createAsyncThunk(
  "invoices/deleteInvoice",
  async (id, { getState, rejectWithValue }) => {
    try {
      const { auth } = getState();

      const config = {
        headers: {
          Authorization: `Bearer ${auth.userInfo.token}`,
        },
      };

      await axios.delete(`/api/invoices/${id}`, config);
      return id;
    } catch (error) {
      return rejectWithValue(
        error.response && error.response.data.message
          ? error.response.data.message
          : error.message
      );
    }
  }
);

// Send invoice by email
export const sendInvoice = createAsyncThunk(
  "invoices/sendInvoice",
  async ({ id, emailData }, { getState, rejectWithValue }) => {
    try {
      const { auth } = getState();

      const config = {
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${auth.userInfo.token}`,
        },
      };

      const { data } = await axios.post(
        `/api/invoices/${id}/send`,
        emailData,
        config
      );
      return data;
    } catch (error) {
      return rejectWithValue(
        error.response && error.response.data.message
          ? error.response.data.message
          : error.message
      );
    }
  }
);

// Mark invoice as paid
export const markInvoiceAsPaid = createAsyncThunk(
  "invoices/markAsPaid",
  async ({ id, paymentData }, { getState, rejectWithValue }) => {
    try {
      const { auth } = getState();

      const config = {
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${auth.userInfo.token}`,
        },
      };

      const { data } = await axios.post(
        `/api/invoices/${id}/payments`,
        paymentData,
        config
      );
      return data;
    } catch (error) {
      return rejectWithValue(
        error.response && error.response.data.message
          ? error.response.data.message
          : error.message
      );
    }
  }
);

// Generate invoice PDF
export const generateInvoicePdf = createAsyncThunk(
  "invoices/generatePdf",
  async (id, { getState, rejectWithValue }) => {
    try {
      const { auth } = getState();

      const config = {
        headers: {
          Authorization: `Bearer ${auth.userInfo.token}`,
        },
        responseType: "blob",
      };

      const { data } = await axios.get(`/api/invoices/${id}/pdf`, config);
      return data;
    } catch (error) {
      return rejectWithValue(
        error.response && error.response.data.message
          ? error.response.data.message
          : error.message
      );
    }
  }
);

// Get invoice statistics
export const getInvoiceStats = createAsyncThunk(
  "invoices/getStats",
  async (_, { getState, rejectWithValue }) => {
    try {
      const { auth } = getState();

      const config = {
        headers: {
          Authorization: `Bearer ${auth.userInfo.token}`,
        },
      };

      const { data } = await axios.get("/api/invoices/analytics", config); // Changed from /stats to /analytics
      return data;
    } catch (error) {
      return rejectWithValue(
        error.response && error.response.data.message
          ? error.response.data.message
          : error.message
      );
    }
  }
);

// Get invoice payment history
export const getInvoicePayments = createAsyncThunk(
  "invoices/getPayments",
  async (id, { getState, rejectWithValue }) => {
    try {
      const { auth } = getState();

      const config = {
        headers: {
          Authorization: `Bearer ${auth.userInfo.token}`,
        },
      };

      const { data } = await axios.get(`/api/invoices/${id}/payments`, config);
      return data;
    } catch (error) {
      return rejectWithValue(
        error.response && error.response.data.message
          ? error.response.data.message
          : error.message
      );
    }
  }
);

const invoiceSlice = createSlice({
  name: "invoices",
  initialState,
  reducers: {
    resetInvoiceState: (state) => {
      state.loading = false;
      state.error = null;
      state.success = false;
    },
    clearInvoice: (state) => {
      state.invoice = null;
    },
    clearInvoiceError: (state) => {
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      // Get invoices
      .addCase(getInvoices.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getInvoices.fulfilled, (state, action) => {
        state.loading = false;
        state.invoices = action.payload.invoices;
        state.totalPages = action.payload.totalPages;
        state.currentPage = action.payload.currentPage;
        state.success = true;
      })
      .addCase(getInvoices.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Get invoice by ID
      .addCase(getInvoiceById.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getInvoiceById.fulfilled, (state, action) => {
        state.loading = false;
        state.invoice = action.payload;
        state.success = true;
      })
      .addCase(getInvoiceById.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Create invoice
      .addCase(createInvoice.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(createInvoice.fulfilled, (state, action) => {
        state.loading = false;
        state.invoices = [action.payload, ...state.invoices];
        state.success = true;
      })
      .addCase(createInvoice.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Update invoice
      .addCase(updateInvoice.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(updateInvoice.fulfilled, (state, action) => {
        state.loading = false;
        state.invoices = state.invoices.map((invoice) =>
          invoice.id === action.payload.id ? action.payload : invoice
        );
        state.invoice = action.payload;
        state.success = true;
      })
      .addCase(updateInvoice.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Delete invoice
      .addCase(deleteInvoice.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(deleteInvoice.fulfilled, (state, action) => {
        state.loading = false;
        state.invoices = state.invoices.filter(
          (invoice) => invoice.id !== action.payload
        );
        state.success = true;
      })
      .addCase(deleteInvoice.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Send invoice
      .addCase(sendInvoice.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(sendInvoice.fulfilled, (state, action) => {
        state.loading = false;
        state.invoices = state.invoices.map((invoice) =>
          invoice.id === action.payload.id ? action.payload : invoice
        );
        if (state.invoice && state.invoice.id === action.payload.id) {
          state.invoice = action.payload;
        }
        state.success = true;
      })
      .addCase(sendInvoice.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Mark invoice as paid
      .addCase(markInvoiceAsPaid.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(markInvoiceAsPaid.fulfilled, (state, action) => {
        state.loading = false;
        state.invoices = state.invoices.map((invoice) =>
          invoice.id === action.payload.id ? action.payload : invoice
        );
        if (state.invoice && state.invoice.id === action.payload.id) {
          state.invoice = action.payload;
        }
        state.success = true;
      })
      .addCase(markInvoiceAsPaid.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Generate invoice PDF
      .addCase(generateInvoicePdf.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(generateInvoicePdf.fulfilled, (state) => {
        state.loading = false;
        state.success = true;
      })
      .addCase(generateInvoicePdf.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Get invoice stats
      .addCase(getInvoiceStats.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getInvoiceStats.fulfilled, (state, action) => {
        state.loading = false;
        state.stats = action.payload;
        state.success = true;
      })
      .addCase(getInvoiceStats.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Get invoice payments
      .addCase(getInvoicePayments.pending, (state) => {
        state.paymentsLoading = true;
        state.error = null;
      })
      .addCase(getInvoicePayments.fulfilled, (state, action) => {
        state.paymentsLoading = false;
        state.payments = action.payload;
        state.success = true;
      })
      .addCase(getInvoicePayments.rejected, (state, action) => {
        state.paymentsLoading = false;
        state.error = action.payload;
      });
  },
});

export const { resetInvoiceState, clearInvoice, clearInvoiceError } =
  invoiceSlice.actions;

export default invoiceSlice.reducer;
