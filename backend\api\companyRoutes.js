const express = require("express");
const router = express.Router();
const {
  createCompany,
  getCompany,
  updateCompany,
} = require("../controllers/companyController");
const { protect, admin } = require("../middleware/authMiddleware");
const { checkPermissions } = require("../middleware/permissions");

// @route   GET /api/company
// @desc    Get company information
// @access  Private
router.get("/", protect, getCompany);

// @route   POST /api/company
// @desc    Create company information
// @access  Private/Admin
router.post("/", protect, checkPermissions("company:configure"), createCompany);

// @route   PUT /api/company
// @desc    Update company information
// @access  Private/Admin
router.put("/", protect, checkPermissions("company:configure"), updateCompany);

module.exports = router;
