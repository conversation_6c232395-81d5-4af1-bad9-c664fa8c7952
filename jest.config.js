module.exports = {
  // Test environment
  testEnvironment: 'node',

  // Files to test
  testMatch: [
    '**/tests/**/*.test.js',
    '!**/node_modules/**',
  ],

  // Coverage configuration
  collectCoverage: true,
  coverageDirectory: 'coverage',
  coveragePathIgnorePatterns: [
    '/node_modules/',
    '/tests/utils/'
  ],
  coverageReporters: ['text', 'lcov', 'clover'],

  // Test timeout
  testTimeout: 30000,

  // Setup files
  setupFilesAfterEnv: ['./tests/utils/setupTests.js'],

  // Environment variables
  setupFiles: ['./tests/utils/setEnvVars.js'],

  // Module name mapper for absolute imports
  moduleNameMapper: {
    '^@/(.*)$': '<rootDir>/$1'
  },

  // Verbose output
  verbose: true,

  // Global variables
  globals: {
    AI_ENABLED: true
  },

  // Reporter configuration
  reporters: [
    'default',
    [
      'jest-junit',
      {
        outputDirectory: 'test-results',
        outputName: 'junit.xml',
        classNameTemplate: '{classname}',
        titleTemplate: '{title}',
        ancestorSeparator: ' › ',
        usePathForSuiteName: true
      }
    ]
  ]
};
