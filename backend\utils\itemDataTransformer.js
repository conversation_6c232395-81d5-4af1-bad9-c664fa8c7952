/**
 * itemDataTransformer.js
 * Utility to transform item data between backend and frontend formats
 * Provides consistent data structure mapping to avoid confusion
 */

const logger = require("./logger");

/**
 * Transform backend item format to frontend expected format
 * @param {Object} backendItem - Item from MongoDB/backend
 * @returns {Object} - Item in frontend expected format
 */
function transformItemForFrontend(backendItem) {
  if (!backendItem) return null;

  try {
    // Map backend fields to frontend structure
    const transformedItem = {
      // Basic fields - direct mapping
      name: backendItem.name || "",
      description: backendItem.description || "",
      sku: backendItem.sku || "",
      price: backendItem.price || 0,
      quantity: backendItem.quantity || 1,
      unit: backendItem.unit || "each",
      source: backendItem.source || "",
      imageUrl: backendItem.imageUrl || "",
      notes: backendItem.notes || "",
      taxRate: backendItem.taxRate || 0,
      category: backendItem.category || "unknown", // Preserve AI category

      // Frontend-specific _aiData structure
      _aiData: {
        raw_description:
          backendItem.description_raw_ai || backendItem.description || "",
        attributes: backendItem.attributes || {},
        lookup_query_suggestion: backendItem.lookup_query_suggestion || "",
        category: backendItem.category || "unknown", // Include category in _aiData
      },

      // Frontend-specific priceInfo structure
      priceInfo: {
        source: backendItem.source || "",
        sourceId: backendItem.sourceId || "",
        lastUpdated: backendItem.last_lookup_attempt || null,
        status: mapLookupStatusToFrontend(backendItem.lookup_status),
        lookupError: extractLookupError(backendItem.lookup_results),
        priceLookupTimestamp: backendItem.last_lookup_attempt || null,
        perUnitPrice: backendItem.price || 0,
      },

      // Direct mappings for complex fields
      lookup_results: backendItem.lookup_results || [],
      material_options: backendItem.material_options || [],
      selected_option: backendItem.selected_option || {},
      lookup_attempts: backendItem.lookup_attempts || 0,

      // MongoDB _id if exists
      _id: backendItem._id,
    };

    return transformedItem;
  } catch (error) {
    logger.error(
      "[ItemDataTransformer] Error transforming item for frontend:",
      error
    );
    return backendItem; // Return original if transformation fails
  }
}

/**
 * Transform frontend item format to backend format for saving
 * @param {Object} frontendItem - Item from frontend
 * @returns {Object} - Item in backend/MongoDB format
 */
function transformItemForBackend(frontendItem) {
  if (!frontendItem) return null;

  try {
    const transformedItem = {
      // Basic fields - direct mapping
      name: frontendItem.name || "",
      description: frontendItem.description || "",
      sku: frontendItem.sku || "",
      price: frontendItem.price || 0,
      quantity: frontendItem.quantity || 1,
      unit: frontendItem.unit || "each",
      currency: frontendItem.currency || "USD",
      source: frontendItem.source || frontendItem.priceInfo?.source || "",
      sourceId:
        frontendItem.sourceId || frontendItem.priceInfo?.sourceId || null,
      imageUrl: frontendItem.imageUrl || "",
      url: frontendItem.url || "",
      notes: frontendItem.notes || "",
      category: frontendItem.category || "unknown", // Preserve category

      // AI-related fields from _aiData
      description_raw_ai:
        frontendItem._aiData?.raw_description || frontendItem.description || "",
      attributes:
        frontendItem._aiData?.attributes || frontendItem.attributes || {},
      lookup_query_suggestion:
        frontendItem._aiData?.lookup_query_suggestion ||
        frontendItem.lookup_query_suggestion ||
        "",

      // Price lookup related fields
      lookup_status: mapLookupStatusToBackend(frontendItem.priceInfo?.status),
      last_lookup_attempt: frontendItem.priceInfo?.lastUpdated || null,
      lookup_attempts: frontendItem.lookup_attempts || 0,

      // Complex fields - direct mapping
      lookup_results: frontendItem.lookup_results || [],
      material_options: frontendItem.material_options || [],
      selected_option: frontendItem.selected_option || {},
    };

    // Preserve _id if exists
    if (frontendItem._id) {
      transformedItem._id = frontendItem._id;
    }

    return transformedItem;
  } catch (error) {
    logger.error(
      "[ItemDataTransformer] Error transforming item for backend:",
      error
    );
    return frontendItem; // Return original if transformation fails
  }
}

/**
 * Map backend lookup status to frontend status
 */
function mapLookupStatusToFrontend(backendStatus) {
  const statusMap = {
    pending_ai_search: "pending",
    pending_user_selection: "selecting",
    price_found_ai: "found",
    price_confirmed_user: "confirmed",
    not_found_ai: "not_found",
    error_ai_search: "error",
  };

  return statusMap[backendStatus] || "idle";
}

/**
 * Map frontend status to backend lookup status
 */
function mapLookupStatusToBackend(frontendStatus) {
  const statusMap = {
    idle: "pending_ai_search",
    pending: "pending_ai_search",
    selecting: "pending_user_selection",
    found: "price_found_ai",
    confirmed: "price_confirmed_user",
    not_found: "not_found_ai",
    error: "error_ai_search",
  };

  return statusMap[frontendStatus] || "pending_ai_search";
}

/**
 * Extract lookup error from lookup_results array
 */
function extractLookupError(lookupResults) {
  if (!Array.isArray(lookupResults) || lookupResults.length === 0) {
    return null;
  }

  // Find the most recent error
  const errorResult = lookupResults
    .filter((lr) => lr.status === "error" || lr.error)
    .sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp))[0];

  return errorResult?.error || errorResult?.reason || null;
}

/**
 * Transform an array of items for frontend
 */
function transformItemsForFrontend(items) {
  if (!Array.isArray(items)) return [];
  return items.map(transformItemForFrontend);
}

/**
 * Transform an array of items for backend
 */
function transformItemsForBackend(items) {
  if (!Array.isArray(items)) return [];
  return items.map(transformItemForBackend);
}

module.exports = {
  transformItemForFrontend,
  transformItemForBackend,
  transformItemsForFrontend,
  transformItemsForBackend,
};
