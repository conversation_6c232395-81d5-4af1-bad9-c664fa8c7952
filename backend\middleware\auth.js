const jwt = require("jsonwebtoken");
const User = require("../models/User");
const ApiError = require("../utils/ApiError");
const logger = require("../utils/logger");

// Get JWT secret based on environment
const getJwtSecret = () => {
  // In production, use a different secret than development
  if (process.env.NODE_ENV === "production") {
    if (!process.env.JWT_SECRET_PRODUCTION) {
      logger.error("Production JWT secret not set! Using fallback secret.");
      // Still return regular secret as fallback, but log the error
    }
    return process.env.JWT_SECRET_PRODUCTION || process.env.JWT_SECRET;
  }
  return process.env.JWT_SECRET;
};

/**
 * Authentication middleware to protect routes
 * Verifies JWT token and attaches user to request object
 */
exports.protect = async (req, res, next) => {
  try {
    let token;

    // Get token from Authorization header
    if (
      req.headers.authorization &&
      req.headers.authorization.startsWith("Bearer")
    ) {
      token = req.headers.authorization.split(" ")[1];
    }

    // For SSE endpoints, also check query parameters (EventSource doesn't support headers)
    if (!token && req.query.token) {
      token = req.query.token;
    }

    // Check if token exists
    if (!token) {
      return next(new ApiError(401, "Not authorized to access this route"));
    }

    try {
      // Verify token using the proper JWT secret
      const decoded = jwt.verify(token, getJwtSecret());

      // Get user from the token
      req.user = await User.findById(decoded.id).select("-password");

      if (!req.user) {
        return next(new ApiError(401, "User not found"));
      }

      next();
    } catch (error) {
      return next(new ApiError(401, "Not authorized, token invalid"));
    }
  } catch (error) {
    next(error);
  }
};

/**
 * Role-based access control middleware
 * @param {...String} roles - Allowed roles
 * @returns {Function} Middleware function
 */
exports.authorize = (...roles) => {
  return (req, res, next) => {
    if (!req.user || !req.user.role) {
      return next(new ApiError(403, "User role not defined"));
    }

    if (!roles.includes(req.user.role)) {
      return next(
        new ApiError(
          403,
          `Role ${req.user.role} is not authorized to access this route`
        )
      );
    }

    next();
  };
};
