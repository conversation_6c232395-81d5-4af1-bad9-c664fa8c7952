const {
  getGeminiJsonResponse,
  getGeminiResponse,
} = require("../utils/geminiService");
const { getDeepseekJsonResponse } = require("../utils/deepseekService");
const axios = require("axios");
const Customer = require("../models/Customer");
const Invoice = require("../models/Invoice");
const Job = require("../models/Job");
const { cacheGet, cacheSet } = require("../utils/cache");
const { withRetry, circuitBreakers } = require("../utils/retryUtils");
const logger = require("../utils/logger"); // Ensure logger is imported
const { z } = require("zod"); // Import Zod for schema validation

// Model configurations from environment variables
const MODELS = {
  PRIMARY: {
    name: process.env.AI_MODEL_PRIMARY || "gemini-2.5-pro-exp-03-25",
    requestsPerMinute: parseInt(process.env.AI_PRIMARY_RATE_LIMIT || "5"), // Updated default
    lastRequest: 0,
    requestCount: 0,
    type: "gemini",
  },
  FALLBACK_1: {
    name: process.env.AI_MODEL_FALLBACK_1 || "models/gemini-2.0-flash",
    requestsPerMinute: parseInt(process.env.AI_FALLBACK_1_RATE_LIMIT || "2"),
    lastRequest: 0,
    requestCount: 0,
    type: "gemini",
  },
  FALLBACK_2: {
    name: process.env.AI_MODEL_FALLBACK_2 || "models/gemini-1.5-flash",
    requestsPerMinute: parseInt(process.env.AI_FALLBACK_2_RATE_LIMIT || "15"),
    lastRequest: 0,
    requestCount: 0,
    type: "gemini",
  },
  FALLBACK_3: {
    name: process.env.AI_MODEL_FALLBACK_3 || "models/gemini-2.0-flash-lite",
    requestsPerMinute: parseInt(process.env.AI_FALLBACK_3_RATE_LIMIT || "30"),
    lastRequest: 0,
    requestCount: 0,
    type: "gemini",
  },
  DEEPSEEK: {
    // Renamed from FALLBACK2 for clarity
    name: process.env.AI_MODEL_DEEPSEEK || "deepseek-chat", // Use consistent env var name
    requestsPerMinute: parseInt(process.env.AI_FALLBACK2_RATE_LIMIT || "20"),
    lastRequest: 0,
    requestCount: 0,
    type: "deepseek",
  },
};

// Track API health metrics
const healthMetrics = {
  totalRequests: 0,
  successfulRequests: 0,
  failedRequests: 0,
  retryAttempts: 0,
  lastRequestTime: null,
  lastErrorTime: null,
  lastErrorMessage: null,
};

/**
 * Record API failure details
 */
function recordFailure(modelName, error) {
  logger.error(`${modelName} error:`, {
    message: error.message,
    stack: error.stack,
  }); // Use logger and include stack
  healthMetrics.failedRequests++;
  healthMetrics.lastErrorTime = Date.now();
  healthMetrics.lastErrorMessage = `${modelName}: ${error.message}`;
}

/**
 * Execute request with proper fallback logic
 */
async function executeRequestWithFallback(
  systemPrompt,
  userPrompt,
  options = {}
) {
  // Define validateResponse functions within this scope where logger is accessible
  const validatePrimaryResponse = (response, opts) => {
    try {
      logger.debug(
        `[AI Validate] Raw response for primary: ${JSON.stringify(response)}`
      );
      if (
        !response ||
        (typeof response === "string" && response.trim() === "")
      ) {
        logger.warn(`[AI Validate] Primary response is empty or whitespace.`);
        throw new Error("Empty response");
      }
      const parsedResponse =
        typeof response === "string" ? JSON.parse(response) : response;
      if (
        parsedResponse === null ||
        (typeof parsedResponse === "object" &&
          Object.keys(parsedResponse).length === 0) ||
        (Array.isArray(parsedResponse) && parsedResponse.length === 0)
      ) {
        logger.warn(
          `[AI Validate] Primary response parsed to empty object/array or null.`
        );
        throw new Error("Empty or invalid JSON response");
      }
      if (opts.zodSchema) {
        const validationResult = opts.zodSchema.safeParse(parsedResponse);
        if (!validationResult.success) {
          const errorDetails = validationResult.error.errors
            .map((e) => `${e.path.join(".")}: ${e.message}`)
            .join(", ");
          logger.warn(
            `[AI Validate] Primary response failed Zod schema validation: ${errorDetails}`
          );
          throw new Error(`Schema validation failed: ${errorDetails}`);
        }
        return validationResult.data; // Use the validated data from Zod
      } else if (opts.expectedSchema) {
        const missingFields = opts.expectedSchema.filter(
          (field) => !(field in parsedResponse)
        );
        if (missingFields.length > 0) {
          logger.warn(
            `[AI Validate] Primary response missing fields (fallback check): ${missingFields.join(
              ", "
            )}`
          );
          throw new Error(
            `Missing required fields: ${missingFields.join(", ")}`
          );
        }
      }
      if (opts?.expectArray && !Array.isArray(parsedResponse)) {
        logger.warn(
          `[AI Validate] Primary response was not an array as expected.`
        );
        throw new Error("Expected array response");
      }
      return parsedResponse; // Return parsed response for consistency, validation passes if no error thrown
    } catch (e) {
      logger.error(
        `[AI Validate] Error during primary response validation/parsing: ${e.message}`
      );
      throw new Error(`Could not parse AI response: ${e.message}`);
    }
  };

  const validateFallbackResponse = (response, opts) => {
    logger.debug(
      `[AI Validate] Raw response for fallback: ${JSON.stringify(response)}`
    );
    const parsedResponse =
      typeof response === "string" ? JSON.parse(response) : response;
    if (
      parsedResponse === null ||
      (typeof parsedResponse === "object" &&
        Object.keys(parsedResponse).length === 0) ||
      (Array.isArray(parsedResponse) && parsedResponse.length === 0)
    ) {
      logger.warn(
        `[AI Validate] Fallback response parsed to empty object/array or null.`
      );
      throw new Error("Empty or invalid JSON response from fallback");
    }
    if (opts?.expectArray && !Array.isArray(parsedResponse)) {
      logger.warn(
        `[AI Validate] Fallback response was not an array as expected.`
      );
      throw new Error("Expected array response from fallback");
    }
    return parsedResponse; // Return parsed response
  };

  const validateDeepseekResponse = (response, opts) => {
    logger.debug(
      `[AI Validate] Raw response for Deepseek: ${JSON.stringify(response)}`
    );
    const parsedResponse =
      typeof response === "string" ? JSON.parse(response) : response;
    if (
      parsedResponse === null ||
      (typeof parsedResponse === "object" &&
        Object.keys(parsedResponse).length === 0) ||
      (Array.isArray(parsedResponse) && parsedResponse.length === 0)
    ) {
      logger.warn(
        `[AI Validate] Deepseek response parsed to empty object/array or null.`
      );
      throw new Error("Empty or invalid JSON response from Deepseek");
    }
    if (opts?.expectArray && !Array.isArray(parsedResponse)) {
      logger.warn(
        `[AI Validate] Deepseek response was not an array as expected.`
      );
      throw new Error("Expected array response from Deepseek");
    }
    return parsedResponse; // Return parsed response
  };

  // Update health metrics
  healthMetrics.totalRequests++;
  healthMetrics.lastRequestTime = Date.now();

  const modelFallbackOrder = [
    MODELS.PRIMARY,
    MODELS.FALLBACK_1,
    MODELS.FALLBACK_2,
    MODELS.FALLBACK_3,
    MODELS.DEEPSEEK,
  ];

  let lastError = null;

  for (const modelConfig of modelFallbackOrder) {
    try {
      logger.info(
        `[AI Request] Attempting model: ${modelConfig.name} (Type: ${modelConfig.type})`
      );
      let result;
      const currentOptions = { ...options };

      if (currentOptions.zodSchema || currentOptions.jsonMode) {
        currentOptions.jsonMode = true;
      }

      const isPrimary = modelConfig === MODELS.PRIMARY;
      const retryConfig = {
        maxRetries: isPrimary ? 4 : modelConfig.type === "deepseek" ? 2 : 3,
        baseDelay: isPrimary
          ? 1000
          : modelConfig.type === "deepseek"
          ? 500
          : 600,
        maxDelay: 8000,
        shouldRetry: isRetryableError,
        validateResponse:
          modelConfig.type === "deepseek"
            ? validateDeepseekResponse
            : isPrimary
            ? validatePrimaryResponse
            : validateFallbackResponse,
      };

      if (modelConfig.type === "gemini") {
        if (currentOptions.jsonMode) {
          result = await withRetry(
            () =>
              getGeminiJsonResponse(systemPrompt, userPrompt, currentOptions),
            retryConfig
          );
        } else {
          result = await withRetry(
            () => getGeminiResponse(systemPrompt, userPrompt, currentOptions),
            retryConfig
          );
        }
      } else if (modelConfig.type === "deepseek") {
        logger.info("[AI Fallback] Attempting Deepseek fallback...");
        result = await withRetry(
          () =>
            getDeepseekJsonResponse(systemPrompt, userPrompt, currentOptions),
          retryConfig
        );
      } else {
        logger.warn(
          `[AI Request] Unknown model type configured: ${modelConfig.type} for ${modelConfig.name}`
        );
        continue;
      }

      healthMetrics.successfulRequests++;
      if (circuitBreakers[modelConfig.type]) {
        circuitBreakers[modelConfig.type].reset();
      }
      logger.info(`[AI Request] Success with model: ${modelConfig.name}`);
      return result;
    } catch (error) {
      logger.error(
        `[AI Request] Model ${modelConfig.name} failed: ${
          error.name || "Error"
        } - ${error.message}`,
        { statusCode: error.statusCode }
      );
      lastError = error;
      recordFailure(modelConfig.name, error);

      if (circuitBreakers[modelConfig.type]) {
        circuitBreakers[modelConfig.type].recordFailure();
      }

      if (!isRetryableError(error)) {
        logger.warn(
          `[AI Request] Non-retryable error with ${modelConfig.name}, breaking fallback chain.`
        );
        break;
      }
    }
  }

  logger.error(
    `[AI Request] All AI models failed. Last error: ${
      lastError?.message || "Unknown error"
    }`
  );
  // healthMetrics.failedRequests++; // This is already handled by recordFailure
  healthMetrics.lastErrorTime = Date.now();
  healthMetrics.lastErrorMessage = `All models failed: ${
    lastError?.message || "Unknown error"
  }`;
  throw lastError || new Error("All AI models failed to provide a response.");
}

// Removed helper function: canUseModel (Rate limiting handled by underlying services and retry logic)

function isRetryableError(error) {
  // Don't retry validation errors
  if (
    error.message.includes("Invalid response format") ||
    error.message.includes("Missing required fields") ||
    error.message.includes("Expected array response")
  ) {
    return false;
  }

  // Retry empty responses
  if (
    error.message.includes("Empty response") ||
    error.message.includes("Could not parse")
  ) {
    return true;
  }

  return (
    !error.message.includes("invalid") &&
    !error.message.includes("unsupported") &&
    !error.message.includes("authentication")
  );
}

async function executeWithModel(
  modelConfig,
  systemPrompt,
  userPrompt,
  options
) {
  // Removed rate limit check logic from here.
  // It's assumed the underlying service (e.g., geminiService) will handle its own rate limits
  // and throw AiRateLimitError if necessary, which will be caught by withRetry.

  if (modelConfig.name.includes("gemini")) {
    return getGeminiJsonResponse(systemPrompt, userPrompt, options);
  }
  throw new Error(`Unknown model: ${modelConfig.name}`);
}

/**
 * Suggest materials for a job based on description and type.
 * @param {string} description - The job description.
 * @param {string} [jobType='general'] - The type of job (e.g., 'electrical', 'plumbing').
 * @returns {Promise<Array<{name: string, estimatedQuantity: number, unit: string}>>} - Array of suggested materials.
 */
// Define Zod schema for material suggestions
const materialSuggestionSchema = z.array(
  z.object({
    name: z.string().min(1, "Material name cannot be empty"),
    estimatedQuantity: z.number().positive("Quantity must be positive"),
    unit: z.string().min(1, "Unit cannot be empty"),
  })
);

async function suggestMaterials(description, jobType = "general") {
  const systemPrompt = `You are an AI assistant for contractors, specializing in suggesting required materials for jobs. Provide a list of materials based on the job description and type. Focus on common materials needed for a ${jobType} job. Respond ONLY with a valid JSON array of objects, where each object has the keys "name" (string), "estimatedQuantity" (number), and "unit" (string, e.g., 'each', 'feet', 'box').`;

  const userPrompt = `Job Type: ${jobType}
Job Description: ${description}

Suggest materials required for this job in the specified JSON format.`;

  const options = {
    expectArray: true, // Expect the response to be an array
    fallbackResponse: [], // Fallback to an empty array
    // requiredFields: ['name', 'estimatedQuantity', 'unit'], // Replaced by zodSchema
    zodSchema: materialSuggestionSchema, // Pass the Zod schema for validation
  };

  try {
    const suggestions = await executeRequestWithFallback(
      systemPrompt,
      userPrompt,
      options
    );
    // Ensure the response is an array, even if the fallback was used incorrectly
    return Array.isArray(suggestions) ? suggestions : [];
  } catch (error) {
    console.error(`Error in suggestMaterials: ${error.message}`);
    return []; // Return empty array on error
  }
}

/**
 * Generate a description for a material based on its name.
 * @param {string} materialName - The name of the material.
 * @returns {Promise<string>} - The generated description.
 */
// Define Zod schema for material description
const materialDescriptionSchema = z.object({
  description: z.string().min(5, "Description must be at least 5 characters"),
});

async function generateDescriptionForMaterial(materialName) {
  const systemPrompt =
    'You are an AI assistant helping a contractor write a concise and informative description for an inventory item. Based on the material name provided, generate a 1-2 sentence description suitable for an inventory list or quote.\nFocus on the key characteristics and common use cases.\nRespond ONLY with a valid JSON object containing a "description" field with the description text.';

  const userPrompt =
    "Material Name: " + materialName + "\n\nGenerate description:";

  const options = {
    fallbackResponse: { description: "Standard " + materialName + "." }, // JSON fallback
    maxTokens: 4000, // Increased for complex JSON responses per tokenConfig
    temperature: 0.5,
    jsonMode: true, // Use JSON mode for consistent responses
    zodSchema: materialDescriptionSchema, // Validate with Zod schema
  };

  try {
    const result = await executeRequestWithFallback(
      systemPrompt,
      userPrompt,
      options
    );
    // Extract description from JSON response
    const description =
      typeof result === "string" ? result : result?.description;
    if (!description || description.trim().length < 5) {
      console.warn(
        `AI service returned invalid or missing description, using fallback for "${materialName}".`
      );
      return "Standard " + materialName + ".";
    }
    return description.trim().replace(/^"|"$/g, "");
  } catch (error) {
    console.error(
      'Error in generateDescriptionForMaterial for "' +
        materialName +
        '": ' +
        error.message
    );
    return "Standard " + materialName + "."; // Return fallback on error
  }
}

// [Rest of the original file functions would be included here...]

/**
 * Refines a search query using the HuggingFace Inference API.
 * @param {string} query - The user's original search query.
 * @returns {Promise<string>} - The refined search query.
 */
async function refineSearchQueryHF(query) {
  const HUGGINGFACE_API_KEY = process.env.HUGGINGFACE_API_KEY;
  const API_URL =
    process.env.HUGGINGFACE_INFERENCE_API_URL ||
    "https://api-inference.huggingface.co/models/t5-base"; // Use env var or default (changed default to t5-base)

  if (!HUGGINGFACE_API_KEY) {
    console.warn("HuggingFace API key not found. Skipping query refinement.");
    return query; // Return original query if key is missing
  }

  const prompt = `Rewrite the following construction material name into a standard, concise format suitable for a product search engine. Focus on key terms and units. Input: "${query}" Output:`;

  try {
    const response = await axios.post(
      API_URL,
      {
        inputs: prompt,
        parameters: {
          max_new_tokens: 50, // Limit output length
          temperature: 0.6, // Control creativity
          // return_full_text: false // Removed unsupported parameter
        },
      },
      {
        headers: { Authorization: `Bearer ${HUGGINGFACE_API_KEY}` },
      }
    );

    if (
      response.data &&
      Array.isArray(response.data) &&
      response.data[0]?.generated_text
    ) {
      const refinedQuery = response.data[0].generated_text.trim();
      console.log(
        `Refined query "${query}" to "${refinedQuery}" using HuggingFace.`
      );
      return refinedQuery;
    } else {
      console.warn(
        "HuggingFace API returned unexpected response format. Using original query."
      );
      return query;
    }
  } catch (error) {
    logger.error(
      `HuggingFace API error during query refinement: ${
        error.response?.data?.error || error.message
      }`,
      { error }
    ); // Use logger
    // Fallback to original query on error
    return query;
  }
}

/**
 * Enhance job description using AI
 * @param {string} description - Job description to enhance
 * @param {string} title - Job title (optional)
 * @param {string} jobType - Job type (optional)
 * @returns {Promise<Object>} - Enhanced description and metadata
 */
async function enhanceJobDescription(
  description,
  title = "",
  jobType = "Standard"
) {
  if (!description) {
    throw new Error("Description is required for enhanceJobDescription");
  }

  const systemPrompt = `You are an expert job description enhancer for service businesses. Your task is to improve job descriptions while maintaining accuracy and professionalism.`;

  const userPrompt = `Enhance this job description to be more detailed, professional, and comprehensive:

Original Description: ${description}
Job Title: ${title || "Not specified"}
Job Type: ${jobType}

Provide a JSON response with:
{
  "enhancedDescription": "improved description with more detail and professional language",
  "technicalTerms": ["array of relevant technical terms identified"],
  "chainOfThought": {
    "improvements": "what improvements were made",
    "reasoning": "why these improvements help"
  }
}

Focus on:
- Adding technical accuracy
- Improving clarity and professionalism
- Including safety considerations
- Maintaining the original intent`;

  try {
    const result = await executeRequestWithFallback(systemPrompt, userPrompt, {
      temperature: 0.7,
      maxTokens: 8000, // Increased for complex JSON responses per tokenConfig
      expectJson: true,
      expectedFormat: "json",
    });

    return result;
  } catch (error) {
    logger.error("Error enhancing job description:", error);
    throw error;
  }
}

/**
 * Analyze job description for title and duration suggestions
 * @param {string} description - Job description to analyze
 * @param {string} title - Job title (optional)
 * @returns {Promise<Object>} - Job analysis with suggestions
 */
async function analyzeJobDescription(description, title = "") {
  if (!description) {
    throw new Error("Description is required for analyzeJobDescription");
  }

  const systemPrompt = `You are an expert job analyst for service businesses. Analyze job descriptions to suggest appropriate titles, durations, and enhancements.`;

  const userPrompt = `Analyze this job description and provide suggestions:

Description: ${description}
Current Title: ${title || "Not specified"}

Provide a JSON response with:
{
  "suggestedTitle": "professional job title based on description",
  "enhancedDescription": "improved version of the description",
  "estimatedDuration": 120,
  "technicalTerms": ["relevant technical terms"],
  "chainOfThought": {
    "titleReasoning": "why this title fits",
    "durationReasoning": "how duration was estimated",
    "improvements": "what was enhanced in description"
  }
}

Consider:
- Job complexity and scope
- Required skills and tools
- Safety requirements
- Industry standards for similar work`;

  try {
    const result = await executeRequestWithFallback(systemPrompt, userPrompt, {
      temperature: 0.7,
      maxTokens: 8000, // Increased for complex JSON responses per tokenConfig
      expectJson: true,
      expectedFormat: "json",
    });

    return result;
  } catch (error) {
    logger.error("Error analyzing job description:", error);
    throw error;
  }
}

module.exports = {
  executeRequestWithFallback,
  suggestMaterials,
  generateDescriptionForMaterial,
  refineSearchQueryHF, // Added export for HuggingFace refinement
  enhanceJobDescription,
  analyzeJobDescription,
};
