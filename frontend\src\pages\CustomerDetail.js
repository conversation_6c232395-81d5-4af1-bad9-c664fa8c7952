import React, { useEffect, useState, useCallback } from "react";
import { usePara<PERSON>, useNavigate } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import {
  Box,
  Container,
  Grid,
  Paper,
  Typography,
  TextField,
  Button,
  Chip,
  Divider,
  List,
  ListItem,
  ListItemText,
  Card,
  CardContent,
  CircularProgress,
  Alert,
  Tab,
  Tabs,
  IconButton,
  ListItemIcon,
} from "@mui/material";
import {
  People as PeopleIcon,
  Business as BusinessIcon,
  Phone as PhoneIcon,
  Email as EmailIcon,
  LocationOn as LocationIcon,
  Edit as EditIcon,
  Save as SaveIcon,
  Add as AddIcon,
  Assignment as JobIcon,
  Receipt as InvoiceIcon,
  Notes as NotesIcon,
  History as HistoryIcon,
  Lightbulb as InsightIcon,
  Psychology as AiIcon,
  CheckCircleOutline as SuccessIcon,
  Build as ServiceIcon,
  Send as SendIcon,
  Refresh as RefreshIcon,
  PhotoLibrary as PhotoLibraryIcon, // Add icon for Images tab
  ErrorOutline as ErrorIcon, // Import ErrorIcon
} from "@mui/icons-material";
import {
  getCustomerById,
  updateCustomer,
  resetCustomerState,
} from "../slices/customerSlice";
import aiService from "../utils/aiService";
import ImageGallery from "../components/customers/ImageGallery"; // Import the new component
import logger from "../utils/logger"; // Assuming logger exists

// TabPanel component for tab content
function TabPanel(props) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`customer-tabpanel-${index}`}
      aria-labelledby={`customer-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

const CustomerDetail = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const dispatch = useDispatch();

  // Create a ref to track if component is mounted
  const isMounted = React.useRef(true);

  const [tabValue, setTabValue] = useState(0);
  const [editNotes, setEditNotes] = useState(false);
  const [notesValue, setNotesValue] = useState("");

  // AI Insight states - Refactored for specific errors
  const [aiLoading, setAiLoading] = useState(false);
  const [descriptionError, setDescriptionError] = useState(null);
  const [insightsError, setInsightsError] = useState(null);
  const [servicesError, setServicesError] = useState(null);
  const [followUpError, setFollowUpError] = useState(null);
  // Removed aiError, aiRetryCount, aiRetryInProgress, aiServiceStatus (handled by backend/service now)

  const [businessDescription, setBusinessDescription] = useState("");
  const [customerInsights, setCustomerInsights] = useState([]);
  const [suggestedServices, setSuggestedServices] = useState([]);
  const [followUpSuggestion, setFollowUpSuggestion] = useState(null);

  // Get customer data from Redux store
  const {
    customer,
    loading,
    error: customerLoadingError, // Renamed to avoid conflict
  } = useSelector((state) => state.customers);

  // For tag input
  const [tagInput, setTagInput] = useState("");
  const [customerTags, setCustomerTags] = useState([]);

  useEffect(() => {
    dispatch(resetCustomerState());
    dispatch(getCustomerById(id));
  }, [dispatch, id]);

  // Refactored AI Insights Generation
  const generateAiInsights = useCallback(
    async (customerData) => {
      if (!customerData || !isMounted.current) return;

      setAiLoading(true);
      setDescriptionError(null);
      setInsightsError(null);
      setServicesError(null);
      setFollowUpError(null);

      // Reset data states
      setBusinessDescription("");
      setCustomerInsights([]);
      setSuggestedServices([]);
      setFollowUpSuggestion(null);

      // Prepare API calls
      // Construct interaction data from latest job (if available)
      const latestJob =
        customerData.jobs && customerData.jobs.length > 0
          ? customerData.jobs[0]
          : null;
      const interactionData = latestJob
        ? {
            type: "job", // Or derive from job type/status if needed
            date:
              latestJob.schedule?.endDate ||
              latestJob.createdAt ||
              new Date().toISOString(),
            notes: latestJob.description, // Use job description as notes
          }
        : { type: "general", date: new Date().toISOString() }; // Fallback if no jobs

      const apiCalls = [
        {
          key: "description",
          fn: () => aiService.generateBusinessDescription(customerData),
        },
        {
          key: "insights",
          fn: () => aiService.generateCustomerInsights(customerData),
        },
        { key: "services", fn: () => aiService.suggestServices(customerData) },
        {
          key: "followUp",
          fn: () =>
            aiService.getFollowUpSuggestions(customerData, interactionData),
        }, // Use dynamic interactionData
      ];

      try {
        // Run calls concurrently
        const results = await Promise.allSettled(
          apiCalls.map((call) => call.fn())
        );

        // Process results only if component is still mounted
        if (isMounted.current) {
          results.forEach((result, index) => {
            const callKey = apiCalls[index].key;
            if (result.status === "fulfilled") {
              switch (callKey) {
                case "description":
                  setBusinessDescription(result.value || ""); // Handle potentially empty success response
                  break;
                case "insights":
                  setCustomerInsights(
                    Array.isArray(result.value) ? result.value : []
                  );
                  break;
                case "services":
                  setSuggestedServices(
                    Array.isArray(result.value) ? result.value : []
                  );
                  break;
                case "followUp":
                  setFollowUpSuggestion(result.value || null);
                  break;
                default:
                  break;
              }
            } else {
              // status === 'rejected'
              const errorMessage =
                result.reason?.response?.data?.message ||
                result.reason?.message ||
                "Failed to load data";
              logger.error(`AI call failed for ${callKey}:`, result.reason);
              switch (callKey) {
                case "description":
                  setDescriptionError(errorMessage);
                  break;
                case "insights":
                  setInsightsError(errorMessage);
                  break;
                case "services":
                  setServicesError(errorMessage);
                  break;
                case "followUp":
                  setFollowUpError(errorMessage);
                  break;
                default:
                  break;
              }
            }
          });
        }
      } catch (overallError) {
        // This catch block might not be strictly necessary with Promise.allSettled
        // but kept for safety.
        logger.error(
          "Unexpected error during AI insight generation:",
          overallError
        );
        // Set a generic error if something unexpected happened outside the API calls
        setDescriptionError(
          descriptionError || "An unexpected error occurred."
        );
        setInsightsError(insightsError || "An unexpected error occurred.");
        setServicesError(servicesError || "An unexpected error occurred.");
        setFollowUpError(followUpError || "An unexpected error occurred.");
      } finally {
        if (isMounted.current) {
          setAiLoading(false);
        }
      }
    },
    [descriptionError, insightsError, servicesError, followUpError]
  ); // Added missing dependencies

  // Set isMounted to false when component unmounts
  useEffect(() => {
    return () => {
      isMounted.current = false;
    };
  }, []);

  // Single useEffect to handle customer data and AI insights
  useEffect(() => {
    if (customer) {
      setNotesValue(customer.notes || "");
      setCustomerTags(customer.tags || []);
      // Generate insights only once when customer data is loaded
      if (
        !businessDescription &&
        !customerInsights.length &&
        !suggestedServices.length &&
        !followUpSuggestion
      ) {
        generateAiInsights(customer);
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [customer]); // Removed generateAiInsights from dependency array to prevent re-triggering on state changes within it

  // Handle tab change
  const handleTabChange = (_, newValue) => {
    setTabValue(newValue);
  };

  // Handle notes save
  const handleSaveNotes = () => {
    dispatch(
      updateCustomer({
        id: customer._id,
        customerData: {
          ...customer,
          notes: notesValue,
        },
      })
    );
    setEditNotes(false);
  };

  // Handle adding a new tag
  const handleAddTag = () => {
    if (tagInput.trim() && !customerTags.includes(tagInput.trim())) {
      const newTags = [...customerTags, tagInput.trim()];
      setCustomerTags(newTags);

      dispatch(
        updateCustomer({
          id: customer._id,
          customerData: {
            ...customer,
            tags: newTags,
          },
        })
      );

      setTagInput("");
    }
  };

  // Handle deleting a tag
  const handleDeleteTag = (tagToDelete) => {
    const newTags = customerTags.filter((tag) => tag !== tagToDelete);
    setCustomerTags(newTags);

    dispatch(
      updateCustomer({
        id: customer._id,
        customerData: {
          ...customer,
          tags: newTags,
        },
      })
    );
  };

  const handleKeyPress = (e) => {
    if (e.key === "Enter") {
      e.preventDefault();
      handleAddTag();
    }
  };

  // Format address for display
  const formatAddress = (address) => {
    if (!address) return "No address provided";

    const parts = [
      address.street,
      address.city,
      address.state,
      address.zipCode,
      address.country,
    ].filter(Boolean);

    return parts.join(", ");
  };

  // Basic history entries (in a real app, these would come from the backend)
  const historyEntries = customer
    ? [
        {
          type: "Customer Created",
          date: new Date(customer.createdAt).toLocaleDateString(),
          description: `Customer record created`,
        },
        {
          type: "Notes Updated",
          date: new Date(customer.updatedAt).toLocaleDateString(),
          description: `Customer notes were updated`,
        },
        // Add job/invoice history here if needed
      ]
    : [];

  if (loading) {
    return (
      <Box sx={{ display: "flex", justifyContent: "center", mt: 5 }}>
        <CircularProgress />
      </Box>
    );
  }

  if (customerLoadingError) {
    // Use renamed error state
    return (
      <Container maxWidth="lg" sx={{ mt: 4 }}>
        <Alert severity="error">{customerLoadingError}</Alert>
      </Container>
    );
  }

  if (!customer) {
    return (
      <Container maxWidth="lg" sx={{ mt: 4 }}>
        <Alert severity="warning">Customer not found</Alert>
      </Container>
    );
  }

  // Helper to render AI section content based on loading/error/data state
  const renderAiSection = (
    title,
    icon,
    data,
    error,
    emptyMessage,
    renderContent
  ) => {
    let content;
    if (error) {
      content = (
        <Alert
          severity="error"
          icon={<ErrorIcon fontSize="inherit" />}
        >{`Failed to load ${title.toLowerCase()}: ${error}`}</Alert>
      );
    } else if (aiLoading && !data) {
      // Show loading only if data hasn't arrived yet for this section
      content = (
        <Box sx={{ display: "flex", justifyContent: "center", p: 2 }}>
          <CircularProgress size={24} />
        </Box>
      );
    } else if (!data || (Array.isArray(data) && data.length === 0)) {
      content = (
        <Typography
          variant="body2"
          color="text.secondary"
          sx={{ fontStyle: "italic" }}
        >
          {emptyMessage}
        </Typography>
      );
    } else {
      content = renderContent(data);
    }

    return (
      <Paper sx={{ p: 3, mb: 3, height: "100%" }}>
        <Box sx={{ display: "flex", alignItems: "center", mb: 2 }}>
          {icon}
          <Typography variant="h6" sx={{ ml: 1 }}>
            {title}
          </Typography>
        </Box>
        {content}
      </Paper>
    );
  };

  return (
    <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
      <Box
        sx={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          mb: 3,
        }}
      >
        <Typography variant="h4" component="h1">
          Customer Details
        </Typography>
        <Button
          variant="contained"
          color="primary"
          startIcon={<EditIcon />}
          onClick={() => navigate(`/customers/${id}/edit`)}
        >
          Edit Customer
        </Button>
      </Box>

      {/* Customer Information Card */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          {/* ... Grid for customer info ... */}
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <Box sx={{ display: "flex", alignItems: "center", mb: 2 }}>
                <BusinessIcon sx={{ mr: 1 }} />
                <Typography variant="h6">
                  {customer.businessName || "Personal Customer"}
                </Typography>
              </Box>

              <Box sx={{ display: "flex", alignItems: "center", mb: 2 }}>
                <PeopleIcon sx={{ mr: 1 }} />
                <Typography>
                  {`${customer.contactPerson?.firstName || ""} ${
                    customer.contactPerson?.lastName || ""
                  }`.trim()}
                  {customer.contactPerson?.position &&
                    ` - ${customer.contactPerson.position}`}
                </Typography>
              </Box>

              <Box sx={{ display: "flex", alignItems: "center", mb: 2 }}>
                <EmailIcon sx={{ mr: 1 }} />
                <Typography>{customer.email || "No email provided"}</Typography>
              </Box>

              <Box sx={{ display: "flex", alignItems: "center" }}>
                <PhoneIcon sx={{ mr: 1 }} />
                <Typography>
                  {customer.phone}
                  {customer.alternatePhone && ` / ${customer.alternatePhone}`}
                </Typography>
              </Box>
            </Grid>

            <Grid item xs={12} md={6}>
              <Box sx={{ display: "flex", alignItems: "flex-start", mb: 2 }}>
                <LocationIcon sx={{ mr: 1, mt: 0.5 }} />
                <Typography>{formatAddress(customer.address)}</Typography>
              </Box>

              <Box sx={{ mb: 2 }}>
                <Typography variant="subtitle2" sx={{ mb: 1 }}>
                  Tags:
                </Typography>
                <Box sx={{ display: "flex", flexWrap: "wrap", gap: 1 }}>
                  {customerTags.map((tag) => (
                    <Chip
                      key={tag}
                      label={tag}
                      onDelete={() => handleDeleteTag(tag)}
                      color="primary"
                      variant="outlined"
                      size="small"
                    />
                  ))}
                  <Box sx={{ display: "flex", alignItems: "center" }}>
                    <TextField
                      variant="outlined"
                      size="small"
                      placeholder="Add tag..."
                      value={tagInput}
                      onChange={(e) => setTagInput(e.target.value)}
                      onKeyDown={handleKeyPress}
                      sx={{ width: 120 }}
                    />
                    <IconButton size="small" onClick={handleAddTag}>
                      <AddIcon />
                    </IconButton>
                  </Box>
                </Box>
              </Box>

              <Box>
                <Typography variant="subtitle2">
                  Source: {customer.source || "Not specified"}
                </Typography>
                <Typography variant="subtitle2">
                  Customer since:{" "}
                  {new Date(customer.createdAt).toLocaleDateString()}
                </Typography>
              </Box>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Tabs section */}
      <Box sx={{ width: "100%" }}>
        <Box sx={{ borderBottom: 1, borderColor: "divider" }}>
          <Tabs
            value={tabValue}
            onChange={handleTabChange}
            aria-label="customer tabs"
          >
            <Tab icon={<NotesIcon />} label="Notes" />
            <Tab icon={<JobIcon />} label="Jobs" />
            <Tab icon={<InvoiceIcon />} label="Invoices" />
            <Tab icon={<PhotoLibraryIcon />} label="Images" />{" "}
            {/* Add Images Tab */}
            <Tab icon={<HistoryIcon />} label="History" />
            <Tab icon={<AiIcon />} label="AI Insights" />
          </Tabs>
        </Box>

        {/* Notes Tab */}
        <TabPanel value={tabValue} index={0}>
          {/* ... Notes Tab Content ... */}
          <Paper sx={{ p: 2 }}>
            <Box
              sx={{
                display: "flex",
                justifyContent: "space-between",
                alignItems: "center",
                mb: 2,
              }}
            >
              <Typography variant="h6">Customer Notes</Typography>
              <Button
                variant="outlined"
                startIcon={editNotes ? <SaveIcon /> : <EditIcon />}
                onClick={editNotes ? handleSaveNotes : () => setEditNotes(true)}
              >
                {editNotes ? "Save Notes" : "Edit Notes"}
              </Button>
            </Box>

            {editNotes ? (
              <TextField
                fullWidth
                multiline
                rows={6}
                variant="outlined"
                value={notesValue}
                onChange={(e) => setNotesValue(e.target.value)}
                placeholder="Add notes about the customer here..."
              />
            ) : (
              <Typography variant="body1" sx={{ whiteSpace: "pre-wrap" }}>
                {notesValue || "No notes have been added for this customer."}
              </Typography>
            )}
          </Paper>
        </TabPanel>

        {/* Jobs Tab */}
        <TabPanel value={tabValue} index={1}>
          {/* ... Jobs Tab Content ... */}
          <Box
            sx={{
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
              mb: 3,
            }}
          >
            <Typography variant="h6">Jobs</Typography>
            <Button
              variant="contained"
              startIcon={<AddIcon />}
              onClick={() => navigate(`/jobs/create?customerId=${id}`)}
            >
              Create Job
            </Button>
          </Box>

          <Paper>
            <List>
              <ListItem>
                <ListItemText
                  primary="No jobs found for this customer."
                  secondary="Create a new job using the button above."
                />
              </ListItem>
            </List>
          </Paper>
        </TabPanel>

        {/* Invoices Tab */}
        <TabPanel value={tabValue} index={2}>
          {/* ... Invoices Tab Content ... */}
          <Box
            sx={{
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
              mb: 3,
            }}
          >
            <Typography variant="h6">Invoices</Typography>
            <Button
              variant="contained"
              startIcon={<AddIcon />}
              onClick={() => navigate(`/invoices/create?customerId=${id}`)}
            >
              Create Invoice
            </Button>
          </Box>

          <Paper>
            <List>
              <ListItem>
                <ListItemText
                  primary="No invoices found for this customer."
                  secondary="Create a new invoice using the button above."
                />
              </ListItem>
            </List>
          </Paper>
        </TabPanel>

        {/* History Tab */}
        <TabPanel value={tabValue} index={4}>
          {/* ... History Tab Content ... */}
          <Typography variant="h6" sx={{ mb: 2 }}>
            Customer Activity History
          </Typography>

          <Paper>
            <List>
              {historyEntries.length === 0 ? (
                <ListItem>
                  <ListItemText primary="No history entries found for this customer." />
                </ListItem>
              ) : (
                historyEntries.map((entry, index) => (
                  <React.Fragment key={index}>
                    <ListItem>
                      <ListItemText
                        primary={entry.type}
                        secondary={
                          <>
                            <Typography
                              component="span"
                              variant="body2"
                              color="text.primary"
                            >
                              {entry.date}
                            </Typography>
                            {` — ${entry.description}`}
                          </>
                        }
                      />
                    </ListItem>
                    {index < historyEntries.length - 1 && <Divider />}
                  </React.Fragment>
                ))
              )}
            </List>
          </Paper>
        </TabPanel>

        {/* Images Tab */}
        <TabPanel value={tabValue} index={3}>
          <Typography variant="h6" sx={{ mb: 2 }}>
            Customer Images
          </Typography>
          <Paper sx={{ p: 2 }}>
            {/* Render ImageGallery component */}
            {customer && <ImageGallery customerId={customer._id} />}
          </Paper>
        </TabPanel>

        {/* AI Insights Tab - Refactored Rendering */}
        <TabPanel value={tabValue} index={5}>
          <Box
            sx={{
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
              mb: 3,
            }}
          >
            <Box sx={{ display: "flex", alignItems: "center" }}>
              <AiIcon sx={{ mr: 1, color: "primary.main" }} />
              <Typography variant="h6">AI-Powered Insights</Typography>
            </Box>
            <Button
              variant="outlined"
              onClick={() => generateAiInsights(customer)}
              disabled={aiLoading}
              startIcon={
                aiLoading ? <CircularProgress size={20} /> : <RefreshIcon />
              }
            >
              {aiLoading ? "Loading..." : "Refresh Insights"}
            </Button>
          </Box>

          {/* Removed generic aiError/aiLoading block */}

          <Grid container spacing={3}>
            {/* Business Description */}
            <Grid item xs={12}>
              {renderAiSection(
                "Customer Overview",
                <BusinessIcon color="action" />,
                businessDescription,
                descriptionError,
                "Business description is currently unavailable.", // Updated message
                (data) => (
                  <Typography
                    variant="body1"
                    sx={{ whiteSpace: "pre-line", lineHeight: 1.6 }}
                  >
                    {data}
                  </Typography>
                )
              )}
            </Grid>

            {/* Customer Insights */}
            <Grid item xs={12} md={6}>
              {renderAiSection(
                "Customer Insights",
                <InsightIcon color="primary" />,
                customerInsights,
                insightsError,
                "No insights available for this customer.", // Updated message
                (data) => (
                  <List dense>
                    {data.map((insight, index) => (
                      <ListItem key={index} sx={{ pl: 0 }}>
                        <ListItemIcon sx={{ minWidth: "auto", mr: 1 }}>
                          <SuccessIcon color="success" fontSize="small" />
                        </ListItemIcon>
                        <ListItemText
                          primary={insight}
                          primaryTypographyProps={{ variant: "body2" }}
                        />
                      </ListItem>
                    ))}
                  </List>
                )
              )}
            </Grid>

            {/* Suggested Services */}
            <Grid item xs={12} md={6}>
              {renderAiSection(
                "Recommended Services",
                <ServiceIcon color="secondary" />,
                suggestedServices,
                servicesError,
                "No service recommendations available.", // Updated message
                (data) => (
                  <List dense>
                    {data.map((service, index) => (
                      <ListItem key={index} sx={{ pl: 0 }}>
                        <ListItemIcon sx={{ minWidth: "auto", mr: 1 }}>
                          <ServiceIcon color="secondary" fontSize="small" />
                        </ListItemIcon>
                        <ListItemText
                          primary={service.service}
                          secondary={service.description}
                          primaryTypographyProps={{
                            variant: "body2",
                            fontWeight: "medium",
                          }} // Make service name slightly bolder
                          secondaryTypographyProps={{ variant: "body2" }}
                        />
                      </ListItem>
                    ))}
                  </List>
                )
              )}
            </Grid>

            {/* Follow-up Suggestion */}
            <Grid item xs={12}>
              {renderAiSection(
                "Follow-up Recommendation",
                <SendIcon color="info" />,
                followUpSuggestion,
                followUpError,
                "No follow-up recommendation available.", // Updated message
                (data) => (
                  <Grid container spacing={2}>
                    <Grid item xs={12} md={4}>
                      <Box sx={{ mb: 2 }}>
                        <Typography variant="subtitle2" gutterBottom>
                          Suggested Follow-up Date:
                        </Typography>
                        <Typography variant="body1" sx={{ fontWeight: "bold" }}>
                          {data.suggestedFollowUpDate
                            ? new Date(
                                data.suggestedFollowUpDate
                              ).toLocaleDateString()
                            : "N/A"}
                        </Typography>
                      </Box>
                      <Box>
                        <Typography variant="subtitle2" gutterBottom>
                          Priority:
                        </Typography>
                        <Chip
                          label={data.priority?.toUpperCase() || "NORMAL"}
                          color={data.priority === "high" ? "error" : "default"}
                          size="small"
                        />
                      </Box>
                    </Grid>
                    <Grid item xs={12} md={8}>
                      <Typography variant="subtitle2" gutterBottom>
                        Suggested Message Template:
                      </Typography>
                      <TextField
                        fullWidth
                        multiline
                        rows={4}
                        variant="outlined"
                        value={data.messageTemplate}
                        InputProps={{ readOnly: true }}
                      />
                      <Box
                        sx={{
                          mt: 2,
                          display: "flex",
                          justifyContent: "flex-end",
                        }}
                      >
                        <Button
                          variant="contained"
                          color="primary"
                          startIcon={<SendIcon />}
                        >
                          Schedule Follow-up
                        </Button>
                      </Box>
                    </Grid>
                  </Grid>
                )
              )}
            </Grid>
          </Grid>
        </TabPanel>
      </Box>
    </Container>
  );
};

export default CustomerDetail;
