import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import axios from "axios";

const initialState = {
  events: [],
  loading: false,
  error: null,
  success: false,
  filters: {
    startDate: null,
    endDate: null,
    status: "all",
    technician: "all",
  },
  selectedEvent: null,
  selectedDate: new Date(),
  view: "month",
  availableTechnicians: [],
};

// Get all calendar events
export const getEvents = createAsyncThunk(
  "calendar/getEvents",
  async (filters, { getState, rejectWithValue }) => {
    try {
      const { auth } = getState();

      // Format date parameters correctly
      const params = {
        ...filters,
        start: filters?.start
          ? new Date(filters.start).toISOString()
          : undefined,
        end: filters?.end ? new Date(filters.end).toISOString() : undefined,
      };

      const config = {
        headers: {
          Authorization: `Bearer ${auth.userInfo.token}`,
        },
        params,
      };

      const { data } = await axios.get("/api/calendar", config);

      // Transform the response data and validate
      const transformedEvents = Array.isArray(data)
        ? data
            .filter((event) => {
              // Filter out invalid events
              if (!event || typeof event !== "object") {
                console.warn("API returned invalid event object");
                return false;
              }
              if (!event._id) {
                console.warn("API returned event without ID:", event);
                return false;
              }
              return true;
            })
            .map((event) => ({
              ...event,
              id: event._id, // Ensure id field exists
              start: event.startTime ? new Date(event.startTime) : new Date(),
              end: event.endTime
                ? new Date(event.endTime)
                : new Date(Date.now() + 3600000),
              title: event.title || "Untitled Event",
            }))
        : [];

      return transformedEvents;
    } catch (error) {
      console.error("Calendar events fetch error:", error);
      return rejectWithValue(
        error.response && error.response.data.message
          ? error.response.data.message
          : error.message
      );
    }
  }
);

// Get event details
export const getEventById = createAsyncThunk(
  "calendar/getEventById",
  async (id, { getState, rejectWithValue }) => {
    try {
      const { auth } = getState();

      const config = {
        headers: {
          Authorization: `Bearer ${auth.userInfo.token}`,
        },
      };

      const { data } = await axios.get(`/api/calendar/${id}`, config);
      return data;
    } catch (error) {
      return rejectWithValue(
        error.response && error.response.data.message
          ? error.response.data.message
          : error.message
      );
    }
  }
);

// Create new event
export const createEvent = createAsyncThunk(
  "calendar/createEvent",
  async (eventData, { getState, rejectWithValue }) => {
    try {
      const { auth } = getState();

      const config = {
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${auth.userInfo.token}`,
        },
      };

      const { data } = await axios.post("/api/calendar", eventData, config);
      return data;
    } catch (error) {
      return rejectWithValue(
        error.response && error.response.data.message
          ? error.response.data.message
          : error.message
      );
    }
  }
);

// Update event
export const updateEvent = createAsyncThunk(
  "calendar/updateEvent",
  async ({ id, eventData }, { getState, rejectWithValue }) => {
    try {
      const { auth } = getState();

      const config = {
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${auth.userInfo.token}`,
        },
      };

      const { data } = await axios.put(
        `/api/calendar/${id}`,
        eventData,
        config
      );
      return data;
    } catch (error) {
      return rejectWithValue(
        error.response && error.response.data.message
          ? error.response.data.message
          : error.message
      );
    }
  }
);

// Delete event
export const deleteEvent = createAsyncThunk(
  "calendar/deleteEvent",
  async (id, { getState, rejectWithValue }) => {
    try {
      const { auth } = getState();

      const config = {
        headers: {
          Authorization: `Bearer ${auth.userInfo.token}`,
        },
      };

      await axios.delete(`/api/calendar/${id}`, config);
      return id;
    } catch (error) {
      return rejectWithValue(
        error.response && error.response.data.message
          ? error.response.data.message
          : error.message
      );
    }
  }
);

// Get available time slots
export const getAvailableTimeSlots = createAsyncThunk(
  "calendar/getAvailableTimeSlots",
  async ({ date, technicianId }, { getState, rejectWithValue }) => {
    try {
      const { auth } = getState();

      const config = {
        headers: {
          Authorization: `Bearer ${auth.userInfo.token}`,
        },
        params: {
          date,
          technicianId,
        },
      };

      const { data } = await axios.get("/api/calendar/check-overlap", config);
      return data;
    } catch (error) {
      return rejectWithValue(
        error.response && error.response.data.message
          ? error.response.data.message
          : error.message
      );
    }
  }
);

// Get technician availability
export const getTechnicianAvailability = createAsyncThunk(
  "calendar/getTechnicianAvailability",
  async (_, { getState, rejectWithValue }) => {
    try {
      const { auth } = getState();

      const config = {
        headers: {
          Authorization: `Bearer ${auth.userInfo.token}`,
        },
      };

      const { data } = await axios.get(
        "/api/users/technicians/availability",
        config
      );
      return data;
    } catch (error) {
      return rejectWithValue(
        error.response && error.response.data.message
          ? error.response.data.message
          : error.message
      );
    }
  }
);

const calendarSlice = createSlice({
  name: "calendar",
  initialState,
  reducers: {
    setCalendarView: (state, action) => {
      state.view = action.payload;
    },
    setSelectedDate: (state, action) => {
      state.selectedDate = action.payload;
    },
    setFilters: (state, action) => {
      state.filters = {
        ...state.filters,
        ...action.payload,
      };
    },
    resetCalendarState: (state) => {
      state.loading = false;
      state.error = null;
      state.success = false;
    },
    selectEvent: (state, action) => {
      state.selectedEvent = action.payload;
    },
    clearSelectedEvent: (state) => {
      state.selectedEvent = null;
    },
  },
  extraReducers: (builder) => {
    builder
      // Get events
      .addCase(getEvents.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getEvents.fulfilled, (state, action) => {
        state.loading = false;
        // Ensure all events have required fields and proper date objects
        state.events = action.payload.map((event) => ({
          ...event,
          id: event._id || event.id,
          start: new Date(event.startTime || event.start),
          end: new Date(event.endTime || event.end),
          title: event.title || "Untitled Event",
        }));
        state.success = true;
      })
      .addCase(getEvents.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Get event by ID
      .addCase(getEventById.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getEventById.fulfilled, (state, action) => {
        state.loading = false;
        state.selectedEvent = action.payload;
        state.success = true;
      })
      .addCase(getEventById.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Create event
      .addCase(createEvent.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(createEvent.fulfilled, (state, action) => {
        state.loading = false;
        state.events = [...state.events, action.payload];
        state.success = true;
      })
      .addCase(createEvent.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Update event
      .addCase(updateEvent.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(updateEvent.fulfilled, (state, action) => {
        state.loading = false;
        state.events = state.events.map((event) => {
          if (
            event.id === action.payload.id ||
            event._id === action.payload._id
          ) {
            // Transform the updated event
            return {
              ...action.payload,
              id: action.payload._id || action.payload.id,
              start: new Date(action.payload.startTime),
              end: new Date(action.payload.endTime),
            };
          }
          return event;
        });
        if (
          state.selectedEvent &&
          (state.selectedEvent.id === action.payload.id ||
            state.selectedEvent._id === action.payload._id)
        ) {
          state.selectedEvent = action.payload;
        }
        state.success = true;
      })
      .addCase(updateEvent.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Delete event
      .addCase(deleteEvent.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(deleteEvent.fulfilled, (state, action) => {
        state.loading = false;
        state.events = state.events.filter(
          (event) => event.id !== action.payload && event._id !== action.payload
        );
        if (
          state.selectedEvent &&
          (state.selectedEvent.id === action.payload ||
            state.selectedEvent._id === action.payload)
        ) {
          state.selectedEvent = null;
        }
        state.success = true;
      })
      .addCase(deleteEvent.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Get available time slots
      .addCase(getAvailableTimeSlots.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getAvailableTimeSlots.fulfilled, (state, action) => {
        state.loading = false;
        state.availableTimeSlots = action.payload;
        state.success = true;
      })
      .addCase(getAvailableTimeSlots.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Get technician availability
      .addCase(getTechnicianAvailability.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getTechnicianAvailability.fulfilled, (state, action) => {
        state.loading = false;
        state.availableTechnicians = action.payload;
        state.success = true;
      })
      .addCase(getTechnicianAvailability.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      });
  },
});

export const {
  setCalendarView,
  setSelectedDate,
  setFilters,
  resetCalendarState,
  selectEvent,
  clearSelectedEvent,
} = calendarSlice.actions;

export default calendarSlice.reducer;
