import React, { useState, useEffect } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import { useSnackbar } from "notistack";
import {
  Box,
  Typography,
  TextField,
  Button,
  Grid,
  Paper,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  OutlinedInput,
  CircularProgress,
  Alert,
} from "@mui/material";
import { useGoogleMaps } from "../components/GoogleMapsProvider";
import {
  createCustomer,
  getCustomerById,
  updateCustomer,
  clearCustomer,
} from "../slices/customerSlice";
import AddressAutocomplete from "../components/AddressAutocomplete";

const CreateCustomer = () => {
  const { id } = useParams();
  const isEditMode = Boolean(id);
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { enqueueSnackbar } = useSnackbar();
  const { isLoaded } = useGoogleMaps();

  const { loading, error, customer } = useSelector((state) => state.customers);
  const [addressSearchTerm, setAddressSearchTerm] = useState("");

  const [customerData, setCustomerData] = useState({
    businessName: "",
    contactPerson: {
      firstName: "",
      lastName: "",
    },
    email: "",
    phone: "",
    address: {
      street: "",
      city: "",
      state: "",
      zipCode: "",
      fullAddress: "",
    },
    tags: [],
  });

  const [newTag, setNewTag] = useState("");
  const [formErrors, setFormErrors] = useState({});
  const availableTags = ["Residential", "Commercial", "VIP", "Regular", "New"];

  useEffect(() => {
    if (isEditMode) {
      dispatch(getCustomerById(id));
    }

    // Cleanup function to clear customer data when leaving the page
    return () => {
      dispatch(clearCustomer());
    };
  }, [dispatch, id, isEditMode]);

  // Populate form with customer data when it's loaded
  useEffect(() => {
    if (isEditMode && customer) {
      setCustomerData({
        businessName: customer.businessName || "",
        contactPerson: {
          firstName: customer.contactPerson?.firstName || "",
          lastName: customer.contactPerson?.lastName || "",
        },
        email: customer.email || "",
        phone: customer.phone || "",
        address: {
          street: customer.address?.street || "",
          city: customer.address?.city || "",
          state: customer.address?.state || "",
          zipCode: customer.address?.zipCode || "",
          fullAddress: customer.address?.fullAddress || "",
        },
        tags: customer.tags || [],
      });

      if (customer.address?.fullAddress) {
        setAddressSearchTerm(customer.address.fullAddress);
      }
    }
  }, [isEditMode, customer]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    if (name.includes(".")) {
      const [parent, child] = name.split(".");
      setCustomerData({
        ...customerData,
        [parent]: {
          ...customerData[parent],
          [child]: value,
        },
      });
    } else {
      setCustomerData({
        ...customerData,
        [name]: value,
      });
    }
  };

  const handleTagChange = (event) => {
    const {
      target: { value },
    } = event;
    setCustomerData({
      ...customerData,
      tags: typeof value === "string" ? value.split(",") : value,
    });
  };

  const handleNewTagAdd = () => {
    if (newTag && !customerData.tags.includes(newTag)) {
      setCustomerData({
        ...customerData,
        tags: [...customerData.tags, newTag],
      });
      setNewTag("");
    }
  };

  const handleSubmit = (e) => {
    e.preventDefault();

    // Validate form fields
    const errors = {};

    if (!customerData.businessName)
      errors.businessName = "Business name is required";
    if (!customerData.contactPerson.firstName)
      errors.contactPersonFirstName = "First name is required";
    if (!customerData.contactPerson.lastName)
      errors.contactPersonLastName = "Last name is required";
    if (!customerData.email) errors.email = "Email is required";
    if (!customerData.phone) errors.phone = "Phone number is required";

    // Address validation
    if (!customerData.address.street)
      errors.street = "Street address is required";
    if (!customerData.address.city) errors.city = "City is required";
    if (!customerData.address.state) errors.state = "State is required";
    if (!customerData.address.zipCode) errors.zipCode = "ZIP code is required";

    // Check if there are any validation errors
    if (Object.keys(errors).length > 0) {
      setFormErrors(errors);
      return;
    }

    if (isEditMode) {
      dispatch(updateCustomer({ id, customerData }))
        .unwrap()
        .then(() => {
          // Clear form errors after successful submission
          setFormErrors({});
          enqueueSnackbar("Customer updated successfully", {
            variant: "success",
          });
          navigate(`/customers/${id}`);
        })
        .catch((err) => {
          enqueueSnackbar(err || "Failed to update customer", {
            variant: "error",
          });
        });
    } else {
      dispatch(createCustomer(customerData))
        .unwrap()
        .then(() => {
          // Clear form errors after successful submission
          setFormErrors({});
          enqueueSnackbar("Customer created successfully", {
            variant: "success",
          });
          navigate("/customers");
        })
        .catch((err) => {
          enqueueSnackbar(err || "Failed to create customer", {
            variant: "error",
          });
        });
    }
  };

  return (
    <Box>
      <Typography variant="h4" sx={{ mb: 3 }}>
        {isEditMode ? "Edit Customer" : "Create New Customer"}
      </Typography>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      <Paper sx={{ p: 3 }}>
        {loading && isEditMode ? (
          <Box display="flex" justifyContent="center" my={4}>
            <CircularProgress />
          </Box>
        ) : (
          <Box component="form" onSubmit={handleSubmit}>
            <Grid container spacing={3}>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Business Name"
                  name="businessName"
                  value={customerData.businessName}
                  onChange={handleChange}
                  error={formErrors.businessName ? true : false}
                  helperText={formErrors.businessName}
                />
              </Grid>

              <Grid item xs={12} md={6}>
                <TextField
                  required
                  fullWidth
                  label="First Name"
                  name="contactPerson.firstName"
                  value={customerData.contactPerson.firstName}
                  onChange={handleChange}
                  error={formErrors.contactPersonFirstName ? true : false}
                  helperText={formErrors.contactPersonFirstName}
                />
              </Grid>

              <Grid item xs={12} md={6}>
                <TextField
                  required
                  fullWidth
                  label="Last Name"
                  name="contactPerson.lastName"
                  value={customerData.contactPerson.lastName}
                  onChange={handleChange}
                  error={formErrors.contactPersonLastName ? true : false}
                  helperText={formErrors.contactPersonLastName}
                />
              </Grid>

              <Grid item xs={12} md={6}>
                <TextField
                  required
                  fullWidth
                  label="Email"
                  name="email"
                  type="email"
                  value={customerData.email}
                  onChange={handleChange}
                  error={formErrors.email ? true : false}
                  helperText={formErrors.email}
                />
              </Grid>

              <Grid item xs={12} md={6}>
                <TextField
                  required
                  fullWidth
                  label="Phone"
                  name="phone"
                  value={customerData.phone}
                  onChange={handleChange}
                  error={formErrors.phone ? true : false}
                  helperText={formErrors.phone}
                />
              </Grid>

              <Grid item xs={12}>
                <Typography variant="subtitle1" sx={{ mb: 2 }}>
                  Address
                </Typography>
              </Grid>

              <Grid item xs={12}>
                <AddressAutocomplete
                  value={addressSearchTerm}
                  onChange={(value) => setAddressSearchTerm(value)}
                  onAddressSelected={(addressDetails) => {
                    setCustomerData({
                      ...customerData,
                      address: {
                        street: addressDetails.street,
                        city: addressDetails.city,
                        state: addressDetails.state,
                        zipCode: addressDetails.zipCode,
                        fullAddress: addressDetails.fullAddress,
                      },
                    });
                  }}
                  label="Search Address"
                  textFieldProps={{
                    fullWidth: true,
                    helperText: "Start typing to get address suggestions",
                    disabled: !isLoaded,
                  }}
                />
              </Grid>

              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Street"
                  name="address.street"
                  value={customerData.address.street}
                  onChange={handleChange}
                  margin="normal"
                  error={formErrors.street ? true : false}
                  helperText={formErrors.street}
                />
              </Grid>

              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="City"
                  name="address.city"
                  value={customerData.address.city}
                  onChange={handleChange}
                  margin="normal"
                  error={formErrors.city ? true : false}
                  helperText={formErrors.city}
                />
              </Grid>

              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="State"
                  name="address.state"
                  value={customerData.address.state}
                  onChange={handleChange}
                  margin="normal"
                  error={formErrors.state ? true : false}
                  helperText={formErrors.state}
                />
              </Grid>

              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Zip Code"
                  name="address.zipCode"
                  value={customerData.address.zipCode}
                  onChange={handleChange}
                  margin="normal"
                  error={formErrors.zipCode ? true : false}
                  helperText={formErrors.zipCode}
                />
              </Grid>

              <Grid item xs={12}>
                <FormControl fullWidth>
                  <InputLabel id="tags-label">Tags</InputLabel>
                  <Select
                    labelId="tags-label"
                    id="tags-select"
                    multiple
                    value={customerData.tags}
                    onChange={handleTagChange}
                    input={
                      <OutlinedInput id="select-multiple-tags" label="Tags" />
                    }
                    renderValue={(selected) => (
                      <Box sx={{ display: "flex", flexWrap: "wrap", gap: 0.5 }}>
                        {selected.map((value) => (
                          <Chip key={value} label={value} />
                        ))}
                      </Box>
                    )}
                  >
                    {availableTags.map((tag) => (
                      <MenuItem key={tag} value={tag}>
                        {tag}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>

              <Grid item xs={12}>
                <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
                  <TextField
                    fullWidth
                    label="Add Custom Tag"
                    value={newTag}
                    onChange={(e) => setNewTag(e.target.value)}
                  />
                  <Button
                    variant="outlined"
                    color="primary"
                    onClick={handleNewTagAdd}
                    disabled={!newTag}
                  >
                    Add Tag
                  </Button>
                </Box>
              </Grid>

              <Grid item xs={12} sx={{ mt: 2 }}>
                <Button
                  type="submit"
                  variant="contained"
                  color="primary"
                  size="large"
                  fullWidth
                  disabled={loading}
                >
                  {loading ? (
                    <CircularProgress size={24} />
                  ) : isEditMode ? (
                    "Update Customer"
                  ) : (
                    "Create Customer"
                  )}
                </Button>
              </Grid>
            </Grid>
          </Box>
        )}
      </Paper>
    </Box>
  );
};

export default CreateCustomer;
