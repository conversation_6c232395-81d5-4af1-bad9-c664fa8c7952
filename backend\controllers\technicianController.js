const Technician = require("../models/Technician");
const Job = require("../models/Job");
const ApiError = require("../utils/ApiError");

// @desc   Get all technicians
// @route  GET /api/technicians
exports.getTechnicians = async (req, res, next) => {
  try {
    const technicians = await Technician.find();
    res.status(200).json(technicians);
  } catch (error) {
    next(new ApiError(500, error.message));
  }
};

// @desc   Get technician by ID
// @route  GET /api/technicians/:id
exports.getTechnicianById = async (req, res, next) => {
  try {
    const technician = await Technician.findById(req.params.id);

    if (!technician) {
      return next(new ApiError(404, "Technician not found"));
    }

    // Get recent jobs for the technician
    const recentJobs = await technician.getRecentJobs();

    // Create a response object with technician data and recent jobs
    const technicianWithJobs = {
      ...technician.toObject(),
      recentJobs: recentJobs.map((job) => ({
        id: job._id,
        title: job.title,
        date: job.scheduledDate,
        status: job.status,
        description: job.description,
        customer: job.customer
          ? `${job.customer.firstName} ${job.customer.lastName}`
          : "Unknown",
      })),
    };

    res.status(200).json(technicianWithJobs);
  } catch (error) {
    next(new ApiError(500, error.message));
  }
};

// @desc   Create a new technician
// @route  POST /api/technicians
exports.createTechnician = async (req, res, next) => {
  try {
    // Check if user has admin permission
    if (req.user.role !== "Administrators") {
      return next(new ApiError(403, "Not authorized to create technicians"));
    }

    // Check if email already exists
    const existingTechnician = await Technician.findOne({
      email: req.body.email,
    });
    if (existingTechnician) {
      return next(
        new ApiError(400, "Technician with this email already exists")
      );
    }

    const technician = new Technician(req.body);
    const savedTechnician = await technician.save();

    res.status(201).json(savedTechnician);
  } catch (error) {
    next(new ApiError(500, error.message));
  }
};

// @desc   Update a technician
// @route  PUT /api/technicians/:id
exports.updateTechnician = async (req, res, next) => {
  try {
    // Check if user has admin permission
    if (req.user.role !== "Administrators") {
      return next(new ApiError(403, "Not authorized to update technicians"));
    }

    // Check if the technician exists
    const technician = await Technician.findById(req.params.id);
    if (!technician) {
      return next(new ApiError(404, "Technician not found"));
    }

    // Update the technician
    const updatedTechnician = await Technician.findByIdAndUpdate(
      req.params.id,
      req.body,
      { new: true, runValidators: true }
    );

    res.status(200).json(updatedTechnician);
  } catch (error) {
    next(new ApiError(500, error.message));
  }
};

// @desc   Delete a technician
// @route  DELETE /api/technicians/:id
exports.deleteTechnician = async (req, res, next) => {
  try {
    // Check if user has admin permission
    if (req.user.role !== "Administrators") {
      return next(new ApiError(403, "Not authorized to delete technicians"));
    }

    // Check if the technician exists
    const technician = await Technician.findById(req.params.id);
    if (!technician) {
      return next(new ApiError(404, "Technician not found"));
    }

    // Check if technician has any jobs assigned
    const jobCount = await Job.countDocuments({ technician: req.params.id });
    if (jobCount > 0) {
      return next(
        new ApiError(
          400,
          "Cannot delete technician with assigned jobs. Please reassign jobs first."
        )
      );
    }

    // Delete the technician
    await Technician.findByIdAndDelete(req.params.id);

    res.status(200).json({ message: "Technician deleted successfully" });
  } catch (error) {
    next(new ApiError(500, error.message));
  }
};
