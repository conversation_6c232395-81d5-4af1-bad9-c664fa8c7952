import React from "react";
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  <PERSON>alogActions,
  Button,
  TextField,
  Box,
  Typography,
  Card,
  Chip,
  LinearProgress,
} from "@mui/material";
import SmartToyIcon from "@mui/icons-material/SmartToy";

/**
 * Dialog for AI-generated technician insights and recommendations
 */
const AISuggestionsDialog = ({
  open,
  onClose,
  technician,
  aiPrompt,
  onAiPromptChange,
  onGenerateInsights,
  aiLoading,
  aiSuggestions,
  onAssignJob,
}) => {
  if (!open) return null;

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle>
        AI Technician Insights
        {technician && (
          <Typography variant="subtitle1" color="text.secondary">
            for {technician.firstName} {technician.lastName}
          </Typography>
        )}
      </DialogTitle>
      <DialogContent dividers>
        <TextField
          label="What would you like to know?"
          fullWidth
          multiline
          rows={2}
          value={aiPrompt}
          onChange={onAiPromptChange}
          variant="outlined"
          margin="normal"
        />

        <Box display="flex" justifyContent="flex-end" mb={2}>
          <Button
            variant="contained"
            color="secondary"
            startIcon={<SmartToyIcon />}
            onClick={onGenerateInsights}
            disabled={aiLoading || !aiPrompt}
          >
            Generate Insights
          </Button>
        </Box>

        {aiLoading && (
          <Box sx={{ width: "100%", mt: 2, mb: 4 }}>
            <LinearProgress color="secondary" />
            <Typography
              variant="body2"
              color="text.secondary"
              align="center"
              sx={{ mt: 1 }}
            >
              Analyzing technician data and generating insights...
            </Typography>
          </Box>
        )}

        {aiSuggestions && (
          <Box mt={3}>
            <Typography variant="h6" gutterBottom>
              AI Recommendations
            </Typography>

            <Typography variant="subtitle1" sx={{ mt: 2 }}>
              Recommended Skills
            </Typography>
            <Box display="flex" flexWrap="wrap" gap={1} mb={3}>
              {aiSuggestions.recommendedSkills.map((skill, index) => (
                <Chip
                  key={index}
                  label={skill}
                  color="primary"
                  variant="outlined"
                />
              ))}
            </Box>

            {aiSuggestions.recommendedJobs &&
              aiSuggestions.recommendedJobs.length > 0 && (
                <>
                  <Typography variant="subtitle1" sx={{ mt: 2 }}>
                    Recommended Job Assignments
                  </Typography>
                  <Box sx={{ mt: 1 }}>
                    {aiSuggestions.recommendedJobs.map((job) => (
                      <Card key={job.id} sx={{ mb: 2, p: 1 }}>
                        <Box
                          display="flex"
                          justifyContent="space-between"
                          alignItems="center"
                        >
                          <Box>
                            <Typography variant="subtitle2">
                              {job.title}
                            </Typography>
                            <Typography variant="body2" color="text.secondary">
                              Customer: {job.customer}
                            </Typography>
                            <Chip
                              label={job.urgency}
                              size="small"
                              color={
                                job.urgency === "High"
                                  ? "error"
                                  : job.urgency === "Medium"
                                  ? "warning"
                                  : "info"
                              }
                              sx={{ mt: 1 }}
                            />
                          </Box>
                          <Button
                            variant="outlined"
                            color="primary"
                            size="small"
                            onClick={() => onAssignJob(job.id)}
                          >
                            Assign
                          </Button>
                        </Box>
                      </Card>
                    ))}
                  </Box>
                </>
              )}
          </Box>
        )}
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose}>Close</Button>
      </DialogActions>
    </Dialog>
  );
};

export default AISuggestionsDialog;
