const User = require("../models/User");
const jwt = require("jsonwebtoken");
const logger = require("../utils/logger");

// JWT Secret
const JWT_SECRET = process.env.JWT_SECRET || "your_jwt_secret";

// Role permission mappings
const rolePermissions = {
  Administrators: [
    "users:create",
    "users:read",
    "users:update",
    "users:delete",
    "users:assignRoles",
    "jobs:create",
    "jobs:read",
    "jobs:update",
    "jobs:delete",
    "jobs:assign",
    "customers:create",
    "customers:read",
    "customers:update",
    "customers:delete",
    "invoices:create",
    "invoices:read",
    "invoices:update",
    "invoices:delete",
    "invoices:void",
    "inventory:create",
    "inventory:read",
    "inventory:update",
    "inventory:delete",
    "company:configure",
    "permissions:manage",
  ],
  Managers: [
    "users:read",
    "users:update",
    "jobs:create",
    "jobs:read",
    "jobs:update",
    "jobs:delete",
    "jobs:assign",
    "customers:create",
    "customers:read",
    "customers:update",
    "customers:delete",
    "invoices:create",
    "invoices:read",
    "invoices:update",
    "invoices:delete",
    "invoices:void",
    "inventory:read",
    "inventory:update",
  ],
  Supervisors: [
    "users:read",
    "users:update",
    "jobs:read",
    "jobs:update",
    "jobs:assign",
    "customers:read",
    "customers:update",
    "invoices:read",
    "invoices:update",
    "inventory:read",
  ],
  Technicians: [
    "jobs:read",
    "jobs:update",
    "customers:read",
    "invoices:read",
    "inventory:read",
  ],
};

// Technician availability status constants
const TECHNICIAN_STATUS = {
  AVAILABLE: "available",
  BUSY: "busy",
  OFF_DUTY: "off_duty",
  ON_LEAVE: "on_leave",
};

// Generate JWT token
const generateToken = (userId) => {
  return jwt.sign({ id: userId }, JWT_SECRET, {
    expiresIn: "30d",
  });
};

/**
 * Assign default permissions based on role
 * @param {Object} user - User object
 * @returns {Array} - Array of permissions based on user's role
 */
const assignDefaultPermissions = (user) => {
  return rolePermissions[user.role] || [];
};

/**
 * Check if this is the first user being created
 * @returns {Promise<boolean>} - True if no users exist yet
 */
const isFirstUser = async () => {
  const count = await User.countDocuments({});
  return count === 0;
};

// @desc    Register a new user
// @route   POST /api/users
// @access  Public
const registerUser = async (req, res) => {
  try {
    const { firstName, lastName, email, password, role, phone, address } =
      req.body;

    // Check if user already exists
    const userExists = await User.findOne({ email });
    if (userExists) {
      logger.warn(`Registration failed: Email ${email} already exists`);
      return res.status(400).json({ message: "User already exists" });
    }

    // Check if this is the first user - if so, make them an Administrator
    const isAdmin = await isFirstUser();
    const userRole = isAdmin ? "Administrators" : role || "Administrators";

    // Create new user
    const user = await User.create({
      firstName,
      lastName,
      email,
      password,
      role: userRole,
      phone,
      address,
      permissions: assignDefaultPermissions({ role: userRole }),
    });

    // Log the user creation
    if (isAdmin) {
      logger.roles(`Administrator role assigned to first user: ${email}`);
    }
    logger.profile(`User created: ${email} (${userRole})`);

    if (user) {
      // Log persistence event
      logger.persistence(`User data persisted for: ${email}`);

      res.status(201).json({
        _id: user._id,
        firstName: user.firstName,
        lastName: user.lastName,
        email: user.email,
        role: user.role,
        permissions: user.permissions,
        token: generateToken(user._id),
      });
    } else {
      res.status(400).json({ message: "Invalid user data" });
    }
  } catch (error) {
    logger.error(`User registration error: ${error.message}`);
    res.status(500).json({ message: "Server error" });
  }
};

// @desc    Authenticate user & get token
// @route   POST /api/users/login
// @access  Public
const loginUser = async (req, res) => {
  try {
    const { email, password } = req.body;

    // Validate input
    if (!email || !password) {
      logger.warn("Login attempt with missing credentials");
      return res
        .status(400)
        .json({ message: "Please provide both email and password" });
    }

    // Find user by email including password field
    const user = await User.findOne({ email }).select("+password");

    if (!user) {
      logger.warn(`Login failed: No user found for email ${email}`);
      return res.status(401).json({ message: "Invalid credentials" });
    }

    // Check password
    const isMatch = await user.comparePassword(password);
    if (!isMatch) {
      logger.warn(`Login failed: Invalid password for user ${email}`);
      return res.status(401).json({ message: "Invalid credentials" });
    }

    // Update last login
    user.lastLogin = Date.now();
    await user.save();

    logger.info(`User logged in: ${user.email} (${user.role})`);

    return res.json({
      success: true,
      token: generateToken(user._id),
      user: {
        _id: user._id,
        firstName: user.firstName,
        lastName: user.lastName,
        email: user.email,
        role: user.role,
        permissions: user.permissions,
      },
    });
  } catch (error) {
    logger.error(`Login error: ${error.message}`);
    return res.status(500).json({ message: "Server error" });
  }
};

// @desc    Get user profile
// @route   GET /api/users/profile
// @access  Private
const getUserProfile = async (req, res) => {
  try {
    const user = await User.findById(req.user.id).select("-password");

    if (user) {
      logger.profile(`Profile retrieved for: ${user.email}`);
      res.json({ user });
    } else {
      logger.warn(`Profile retrieval failed: User ID ${req.user.id} not found`);
      res.status(404).json({ message: "User not found" });
    }
  } catch (error) {
    logger.error(`Profile retrieval error: ${error.message}`);
    res.status(500).json({ message: "Server error" });
  }
};

// @desc    Update user profile
// @route   PUT /api/users/profile
// @access  Private
const updateUserProfile = async (req, res) => {
  try {
    const user = await User.findById(req.user.id);

    if (user) {
      // Log previous values for audit
      const previousValues = {
        firstName: user.firstName,
        lastName: user.lastName,
        email: user.email,
        phone: user.phone,
        address: user.address,
      };

      user.firstName = req.body.firstName || user.firstName;
      user.lastName = req.body.lastName || user.lastName;
      user.email = req.body.email || user.email;
      user.phone = req.body.phone || user.phone;

      // Handle address as an object or string
      if (req.body.address) {
        if (typeof req.body.address === "object") {
          // If address is sent as an object, update the address field accordingly
          user.address = req.body.address;
        } else {
          // If address is sent as a string (legacy format), update directly
          user.address = req.body.address;
        }
      }

      // Add company and position fields
      user.company = req.body.company || user.company;
      user.position = req.body.position || user.position;

      user.profileImage = req.body.profileImage || user.profileImage;
      user.skills = req.body.skills || user.skills;
      user.hourlyRate = req.body.hourlyRate || user.hourlyRate;
      user.contractorType = req.body.contractorType || user.contractorType;
      user.services = req.body.services || user.services;
      logger.profile(
        `Profile updated for ${user.email}: ${JSON.stringify(
          Object.keys(req.body)
        )}`
      );
      logger.persistence(
        `User data persisted after profile update for: ${user.email}`
      );
      if (req.body.password) {
        user.password = req.body.password;
      }

      const updatedUser = await user.save();

      res.json({
        _id: updatedUser._id,
        firstName: updatedUser.firstName,
        lastName: updatedUser.lastName,
        email: updatedUser.email,
        phone: updatedUser.phone,
        address: updatedUser.address,
        company: updatedUser.company,
        position: updatedUser.position,
        role: updatedUser.role,
        permissions: updatedUser.permissions,
        contractorType: updatedUser.contractorType,
        services: updatedUser.services,
        token: generateToken(updatedUser._id),
      });
    } else {
      logger.warn(`Profile update failed: User ID ${req.user.id} not found`);
      res.status(404).json({ message: "User not found" });
    }
  } catch (error) {
    logger.error(`Profile update error: ${error.message}`);
    res.status(500).json({ message: "Server error" });
  }
};

// @desc    Get all users
// @route   GET /api/users
// @access  Private/Admin
const getUsers = async (req, res) => {
  try {
    const users = await User.find({}).select("-password");
    logger.info(`User list retrieved by: ${req.user.email}`);
    res.json(users);
  } catch (error) {
    logger.error(`Error retrieving user list: ${error.message}`);
    res.status(500).json({ message: "Server error" });
  }
};

// @desc    Get user by ID
// @route   GET /api/users/:id
// @access  Private/Admin
const getUserById = async (req, res) => {
  try {
    const user = await User.findById(req.params.id).select("-password");

    if (user) {
      logger.info(
        `User details for ${user.email} retrieved by: ${req.user.email}`
      );
      res.json(user);
    } else {
      logger.warn(`User retrieval failed: ID ${req.params.id} not found`);
      res.status(404).json({ message: "User not found" });
    }
  } catch (error) {
    logger.error(`Error retrieving user: ${error.message}`);
    res.status(500).json({ message: "Server error" });
  }
};

// @desc    Update user
// @route   PUT /api/users/:id
// @access  Private/Admin
const updateUser = async (req, res) => {
  try {
    const user = await User.findById(req.params.id);

    if (user) {
      // Log previous values for audit
      const previousValues = {
        firstName: user.firstName,
        lastName: user.lastName,
        email: user.email,
        role: user.role,
        isActive: user.isActive,
      };

      user.firstName = req.body.firstName || user.firstName;
      user.lastName = req.body.lastName || user.lastName;
      user.email = req.body.email || user.email;
      user.role = req.body.role || user.role;
      user.isActive =
        req.body.isActive !== undefined ? req.body.isActive : user.isActive;

      // Update permissions based on role if role has changed
      if (req.body.role && req.body.role !== previousValues.role) {
        user.permissions = assignDefaultPermissions(user);
        logger.roles(
          `User ${user.email} role changed from ${previousValues.role} to ${user.role} by ${req.user.email}`
        );
      }

      // Allow manual permission updates by administrators
      if (req.body.permissions && req.user.role === "Administrators") {
        user.permissions = req.body.permissions;
        logger.permissions(
          `Permissions manually updated for ${user.email} by ${req.user.email}`
        );
      }
      logger.profile(`User ${user.email} updated by ${req.user.email}`);
      logger.persistence(
        `User data persisted after admin update for: ${user.email}`
      );
      const updatedUser = await user.save();

      res.json({
        _id: updatedUser._id,
        firstName: updatedUser.firstName,
        lastName: updatedUser.lastName,
        email: updatedUser.email,
        role: updatedUser.role,
        isActive: updatedUser.isActive,
        permissions: updatedUser.permissions,
      });
    } else {
      logger.warn(`User update failed: ID ${req.params.id} not found`);
      res.status(404).json({ message: "User not found" });
    }
  } catch (error) {
    logger.error(`User update error: ${error.message}`);
    res.status(500).json({ message: "Server error" });
  }
};

// @desc    Delete user
// @route   DELETE /api/users/:id
// @access  Private/Admin
const deleteUser = async (req, res) => {
  try {
    const user = await User.findById(req.params.id);

    if (user) {
      await user.remove();
      logger.profile(`User ${user.email} deleted by ${req.user.email}`);
      res.json({ message: "User removed" });
    } else {
      logger.warn(`User deletion failed: ID ${req.params.id} not found`);
      res.status(404).json({ message: "User not found" });
    }
  } catch (error) {
    logger.error(`User deletion error: ${error.message}`);
    res.status(500).json({ message: "Server error" });
  }
};

// @desc    Get all technicians
// @route   GET /api/users/technicians
// @access  Private
const getTechnicians = async (req, res) => {
  try {
    const technicians = await User.find({ role: "Technicians", isActive: true })
      .select("-password")
      .sort({ firstName: 1 });

    // Enhance technician data with availability status
    const enhancedTechnicians = await Promise.all(
      technicians.map(async (tech) => {
        const techObj = tech.toObject();

        // Default to available if no current assignments
        techObj.availabilityStatus = TECHNICIAN_STATUS.AVAILABLE;

        // Here we would check for ongoing jobs or calendar events
        // For now, we're using a simplified version
        return techObj;
      })
    );

    res.json(enhancedTechnicians);
  } catch (error) {
    console.error("Error fetching technicians:", error);
    res.status(500).json({ message: "Server error" });
  }
};

// @desc    Get technician details with schedule
// @route   GET /api/users/technicians/:id
// @access  Private
const getTechnicianDetails = async (req, res) => {
  try {
    const technician = await User.findOne({
      _id: req.params.id,
      role: "Technicians",
    }).select("-password");

    if (!technician) {
      return res.status(404).json({ message: "Technician not found" });
    }

    // Convert to plain object for manipulation
    const techData = technician.toObject();

    // Add availability information
    // In a real implementation, you would fetch this from jobs and calendar
    techData.availability = {
      status: TECHNICIAN_STATUS.AVAILABLE,
      currentJob: null,
      upcomingJobs: [],
    };

    // Add skills details
    techData.skillDetails = (techData.skills || []).map((skill) => ({
      name: skill,
      level: "expert", // In a real implementation, this would come from a skills assessment
    }));

    res.json(techData);
  } catch (error) {
    console.error("Error fetching technician details:", error);
    res.status(500).json({ message: "Server error" });
  }
};

// @desc    Update technician availability
// @route   PUT /api/users/technicians/:id/availability
// @access  Private/Admin
const updateTechnicianAvailability = async (req, res) => {
  try {
    const { status, note } = req.body;

    if (!Object.values(TECHNICIAN_STATUS).includes(status)) {
      return res.status(400).json({
        message: `Invalid status. Must be one of: ${Object.values(
          TECHNICIAN_STATUS
        ).join(", ")}`,
      });
    }

    const technician = await User.findOne({
      _id: req.params.id,
      role: "Technicians",
    });

    if (!technician) {
      return res.status(404).json({ message: "Technician not found" });
    }

    // In a production app, we might store this in a dedicated collection
    // For simplicity, we'll return success without actually storing

    res.json({
      message: "Technician availability updated",
      technician: {
        _id: technician._id,
        firstName: technician.firstName,
        lastName: technician.lastName,
        availabilityStatus: status,
        availabilityNote: note,
      },
    });
  } catch (error) {
    console.error("Error updating technician availability:", error);
    res.status(500).json({ message: "Server error" });
  }
};

// @desc    Get technician workload statistics
// @route   GET /api/users/technicians/:id/workload
// @access  Private/Admin
const getTechnicianWorkload = async (req, res) => {
  try {
    const technician = await User.findOne({
      _id: req.params.id,
      role: "Technicians",
    });

    if (!technician) {
      return res.status(404).json({ message: "Technician not found" });
    }

    // For demo purposes, we're generating sample data
    // In a real application, this would come from Jobs, Invoices, etc.
    const workloadStats = {
      jobsCompleted: Math.floor(Math.random() * 50) + 10,
      averageJobDuration: Math.floor(Math.random() * 120) + 60, // minutes
      customerSatisfaction: Math.random() * 2 + 3, // 3-5 rating
      revenue: Math.floor(Math.random() * 10000) + 5000,
      upcomingJobs: Math.floor(Math.random() * 10) + 1,
    };

    res.json({
      technician: {
        _id: technician._id,
        firstName: technician.firstName,
        lastName: technician.lastName,
      },
      workloadStats,
    });
  } catch (error) {
    console.error("Error fetching technician workload:", error);
    res.status(500).json({ message: "Server error" });
  }
};

module.exports = {
  registerUser,
  loginUser,
  getUserProfile,
  updateUserProfile,
  getUsers,
  getUserById,
  updateUser,
  deleteUser,
  getTechnicians,
  getTechnicianDetails,
  updateTechnicianAvailability,
  getTechnicianWorkload,
};
