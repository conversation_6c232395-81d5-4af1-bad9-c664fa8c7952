{"name": "workiz-clone-frontend", "version": "0.1.0", "private": true, "dependencies": {"@emotion/react": "^11.7.0", "@emotion/styled": "^11.6.0", "@microsoft/fetch-event-source": "^2.0.1", "@mui/icons-material": "^5.2.0", "@mui/lab": "^5.0.0-alpha.58", "@mui/material": "^5.16.14", "@mui/x-date-pickers": "^7.28.0", "@react-google-maps/api": "^2.20.6", "@reduxjs/toolkit": "^1.6.2", "axios": "^1.8.4", "chart.js": "^3.9.1", "date-fns": "^2.26.0", "formik": "^2.2.9", "moment": "^2.30.1", "notistack": "^3.0.2", "process": "^0.11.10", "react": "^17.0.2", "react-big-calendar": "^1.18.0", "react-chartjs-2": "^4.3.1", "react-dom": "^17.0.2", "react-markdown": "^8.0.7", "react-redux": "^7.2.6", "react-router-dom": "^6.0.2", "react-signature-canvas": "^1.0.3", "react-toastify": "^8.1.0", "remark-gfm": "^4.0.1", "web-vitals": "^2.1.2", "yup": "^0.32.11"}, "scripts": {"start": "cross-env NODE_ENV=development NODE_OPTIONS=--openssl-legacy-provider craco start", "build": "cross-env NODE_ENV=production NODE_OPTIONS=--openssl-legacy-provider craco build", "test": "cross-env NODE_ENV=test craco test", "eject": "react-scripts eject"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "proxy": "http://localhost:5000", "devDependencies": {"@babel/eslint-parser": "^7.28.0", "@babel/plugin-transform-nullish-coalescing-operator": "^7.27.1", "@babel/plugin-transform-optional-chaining": "^7.27.1", "@craco/craco": "^7.0.0", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^12.1.5", "@testing-library/user-event": "^13.5.0", "babel-preset-react-app": "^10.1.0", "cross-env": "^7.0.3", "msw": "^2.7.3", "react-dev-utils": "^12.0.1", "react-scripts": "^5.0.1"}}