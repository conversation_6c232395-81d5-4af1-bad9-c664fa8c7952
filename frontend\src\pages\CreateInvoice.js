import React, { useState, useEffect } from "react";
import {
  Container,
  Typography,
  Box,
  Paper,
  Grid,
  TextField,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  CircularProgress,
  Alert,
  Divider,
  IconButton,
  InputAdornment,
  Autocomplete,
  CircularProgress as SearchLoadingIcon, // Re-use for search loading indicator
} from "@mui/material";
import { Add as AddIcon, Delete as DeleteIcon } from "@mui/icons-material"; // Removed SearchIcon
import axios from "axios"; // Import axios
import { useNavigate } from "react-router-dom";
import { useSelector, useDispatch } from "react-redux";
import { createInvoice } from "../slices/invoiceSlice";
import { getCustomers } from "../slices/customerSlice";
import { getJobs } from "../slices/jobSlice";
import { Formik, Form, FieldArray } from "formik";
import * as Yup from "yup";

const CreateInvoice = () => {
  const [submitLoading, setSubmitLoading] = useState(false);
  const [success, setSuccess] = useState(false);
  const [error, setError] = useState(null);
  const [materialSearchResults, setMaterialSearchResults] = useState([]);
  const [materialSearchLoading, setMaterialSearchLoading] = useState(false);
  const [activeMaterialSearchIndex, setActiveMaterialSearchIndex] =
    useState(null); // Track which item is searching

  // Redux state
  const { customers, loading: customersLoading } = useSelector(
    (state) => state.customers
  );
  const { jobs, loading: jobsLoading } = useSelector((state) => state.jobs);
  // We need auth state for authorization, even if not directly used in component
  useSelector((state) => state.auth);
  const dispatch = useDispatch();
  const navigate = useNavigate();

  // Initial form values
  const initialValues = {
    customer: null,
    job: null,
    issueDate: new Date().toISOString().split("T")[0],
    dueDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)
      .toISOString()
      .split("T")[0],
    status: "DRAFT",
    items: [
      { description: "", quantity: 1, unitPrice: 0, tax: 0, type: "SERVICE" },
    ],
    notes: "",
    terms: "Payment is due within 30 days of issue.",
    discount: 0,
  };

  // Validation schema
  const validationSchema = Yup.object({
    customer: Yup.object().nullable().required("Customer is required"),
    job: Yup.object().nullable().required("Job is required"),
    issueDate: Yup.date().required("Issue date is required"),
    dueDate: Yup.date()
      .required("Due date is required")
      .min(Yup.ref("issueDate"), "Due date cannot be before issue date"),
    items: Yup.array()
      .of(
        Yup.object().shape({
          description: Yup.string().required("Description is required"),
          quantity: Yup.number()
            .required("Quantity is required")
            .positive("Quantity must be positive"),
          unitPrice: Yup.number()
            .required("Unit price is required")
            .min(0, "Unit price cannot be negative"),
          tax: Yup.number()
            .required("Tax is required")
            .min(0, "Tax cannot be negative"),
          type: Yup.string().required("Item type is required"),
        })
      )
      .min(1, "At least one item is required"),
    discount: Yup.number()
      .min(0, "Discount cannot be negative")
      .max(100, "Discount cannot be more than 100%"),
  });

  // Fetch customers and jobs on component mount
  useEffect(() => {
    const fetchData = async () => {
      try {
        // Fetch real customers and jobs from API using Redux thunks
        await dispatch(getCustomers({ page: 1, limit: 100 }));
        await dispatch(getJobs({ page: 1, limit: 100 }));
      } catch (err) {
        setError("Failed to load necessary data. Please try again.");
        console.error("Error fetching data:", err);
      }
    };

    fetchData();
  }, [dispatch]); // Remove customers and jobs from dependency array

  // Debug when customers or jobs change
  useEffect(() => {
    if (customers?.length > 0) {
      console.log("Customers loaded:", customers.length);
    }
    if (jobs?.length > 0) {
      console.log("Jobs loaded:", jobs.length);
    }
  }, [customers, jobs]);

  // Calculate subtotal for an item
  const calculateItemTotal = (item) => {
    return item.quantity * item.unitPrice;
  };

  // Calculate subtotal for all items
  const calculateSubtotal = (items) => {
    return items.reduce((sum, item) => sum + calculateItemTotal(item), 0);
  };

  // Calculate tax for all items
  const calculateTax = (items) => {
    return items.reduce((sum, item) => {
      const itemTotal = calculateItemTotal(item);
      return sum + (itemTotal * item.tax) / 100;
    }, 0);
  };

  // Calculate total with discount
  const calculateTotal = (items, discount) => {
    const subtotal = calculateSubtotal(items);
    const tax = calculateTax(items);
    const discountAmount = subtotal * (discount / 100);
    return subtotal + tax - discountAmount;
  };

  // Handle form submission
  const handleSubmit = async (values, { resetForm }) => {
    setSubmitLoading(true);
    setError(null);

    try {
      // Ensure each item has a 'type' field which is required by the backend
      const itemsWithType = values.items.map((item) => ({
        description: item.description,
        quantity: item.quantity,
        unitPrice: item.unitPrice,
        taxRate: item.tax || 0, // Rename tax to taxRate as expected by backend
        discountRate: values.discount || 0, // Apply global discount to each item
        total: item.quantity * item.unitPrice, // Calculate and include total
        type: item.type || "SERVICE", // Ensure type is always provided
      }));

      // Build invoice object from form values, adjusted for backend expectations
      const invoiceData = {
        jobId: values.job?._id || values.job?.id || values.job, // Handle different job ID formats
        customer:
          values.customer?._id || values.customer?.id || values.customer, // Handle different customer ID formats
        items: itemsWithType,
        issueDate: values.issueDate,
        dueDate: values.dueDate,
        status: values.status,
        notes: {
          customer: values.notes || "",
          internal: "", // Assuming internal notes are managed elsewhere or not needed initially
        },
        terms: values.terms || "",
      };

      console.log("Submitting invoice data:", invoiceData);

      // Make the API call using Redux Toolkit's dispatch
      await dispatch(createInvoice(invoiceData)).unwrap();

      setSuccess(true);
      resetForm(); // Reset the form on success

      // Navigate to invoices list after a delay
      setTimeout(() => {
        navigate("/invoices");
      }, 1500);
    } catch (err) {
      console.error("Error creating invoice:", err);
      setError(
        err.message || "Failed to create invoice. Please check your form data."
      );
    } finally {
      setSubmitLoading(false);
    }
  };

  // --- Material Search Handlers ---
  const handleMaterialSearch = async (query, index) => {
    if (!query || query.length < 3) {
      setMaterialSearchResults([]);
      return;
    }
    setActiveMaterialSearchIndex(index);
    setMaterialSearchLoading(true);
    console.log(`Searching materials for invoice item ${index}: ${query}`);
    try {
      // Get auth token from localStorage
      const userInfo = localStorage.getItem("userInfo");
      const token = userInfo ? JSON.parse(userInfo).token : "";
      const config = {
        headers: token ? { Authorization: `Bearer ${token}` } : {},
        params: { query },
      };
      // Use the correct API path based on backend refactor
      const { data } = await axios.get("/api/material-pricing/search", config); // Use pricing search endpoint
      setMaterialSearchResults(data.data || []);
    } catch (searchError) {
      console.error("Material search error:", searchError);
      setError("Failed to search materials. Please try again."); // Inform user
      setMaterialSearchResults([]);
    } finally {
      setMaterialSearchLoading(false);
    }
  };

  const handleSelectMaterial = (material, index, setFieldValue) => {
    if (!material) return;
    setFieldValue(`items[${index}].description`, material.name);
    setFieldValue(`items[${index}].unitPrice`, material.price);
    setFieldValue(`items[${index}].sku`, material.sku); // Add SKU if needed
    // Optionally set unit, source, etc.
    // setFieldValue(`items[${index}].unit`, material.unit || 'each');
    setMaterialSearchResults([]); // Clear results after selection
    setActiveMaterialSearchIndex(null); // Reset active search index
  };
  // --- End Material Search Handlers ---

  // Filter jobs based on selected customer
  const filterJobsByCustomer = (customerId) => {
    if (!jobs || !customerId) return [];
    // Check both customer._id and customer string ID formats
    return jobs.filter((job) => {
      // Handle different possible formats of customer reference in job
      if (job.customer?._id === customerId) return true;
      if (job.customer === customerId) return true;
      return false;
    });
  };

  // Determine if page is loading
  const isLoading = customersLoading || jobsLoading;

  return (
    <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
      <Paper sx={{ p: 3 }}>
        <Typography variant="h5" gutterBottom>
          Create New Invoice
        </Typography>

        {isLoading ? (
          <Box display="flex" justifyContent="center" my={4}>
            <CircularProgress />
          </Box>
        ) : error ? (
          <Alert severity="error" sx={{ my: 2 }}>
            {error}
          </Alert>
        ) : success ? (
          <Alert severity="success" sx={{ my: 2 }}>
            Invoice created successfully! Redirecting to invoices list...
          </Alert>
        ) : (
          <Formik
            initialValues={initialValues}
            validationSchema={validationSchema}
            onSubmit={handleSubmit}
          >
            {({
              values,
              errors,
              touched,
              handleChange,
              handleBlur,
              setFieldValue,
            }) => (
              <Form>
                <Grid container spacing={3}>
                  {/* Customer and Job Selection */}
                  <Grid item xs={12} md={6}>
                    <Autocomplete
                      options={customers || []}
                      getOptionLabel={(option) =>
                        option.name || option.businessName || "Unknown Customer"
                      }
                      value={values.customer}
                      onChange={(e, newValue) => {
                        console.log("Selected customer:", newValue);
                        setFieldValue("customer", newValue);
                        // Reset job when customer changes
                        setFieldValue("job", null);
                      }}
                      renderInput={(params) => (
                        <TextField
                          {...params}
                          label="Customer"
                          error={touched.customer && Boolean(errors.customer)}
                          helperText={touched.customer && errors.customer}
                          fullWidth
                          margin="normal"
                          required
                        />
                      )}
                      isOptionEqualToValue={(option, value) => {
                        if (!option || !value) return false;
                        return (
                          option._id === value._id || option.id === value.id
                        );
                      }}
                    />
                  </Grid>

                  <Grid item xs={12} md={6}>
                    <Autocomplete
                      options={
                        values.customer
                          ? filterJobsByCustomer(values.customer._id)
                          : []
                      }
                      getOptionLabel={(option) =>
                        option.title || option.description || "Unknown Job"
                      }
                      value={values.job}
                      onChange={(e, newValue) => {
                        console.log("Selected job:", newValue);
                        setFieldValue("job", newValue);
                      }}
                      disabled={!values.customer}
                      renderInput={(params) => (
                        <TextField
                          {...params}
                          label="Related Job"
                          error={touched.job && Boolean(errors.job)}
                          helperText={touched.job && errors.job}
                          fullWidth
                          margin="normal"
                          required
                        />
                      )}
                      isOptionEqualToValue={(option, value) => {
                        if (!option || !value) return false;
                        return (
                          option._id === value._id || option.id === value.id
                        );
                      }}
                      noOptionsText={
                        values.customer
                          ? "No jobs found for this customer"
                          : "Please select a customer first"
                      }
                    />
                  </Grid>

                  {/* Invoice Details */}
                  <Grid item xs={12} md={4}>
                    <TextField
                      fullWidth
                      name="issueDate"
                      label="Issue Date"
                      type="date"
                      value={values.issueDate}
                      onChange={handleChange}
                      onBlur={handleBlur}
                      error={touched.issueDate && Boolean(errors.issueDate)}
                      helperText={touched.issueDate && errors.issueDate}
                      margin="normal"
                      required
                      InputLabelProps={{ shrink: true }}
                    />
                  </Grid>

                  <Grid item xs={12} md={4}>
                    <TextField
                      fullWidth
                      name="dueDate"
                      label="Due Date"
                      type="date"
                      value={values.dueDate}
                      onChange={handleChange}
                      onBlur={handleBlur}
                      error={touched.dueDate && Boolean(errors.dueDate)}
                      helperText={touched.dueDate && errors.dueDate}
                      margin="normal"
                      required
                      InputLabelProps={{ shrink: true }}
                    />
                  </Grid>

                  <Grid item xs={12}>
                    <FormControl fullWidth margin="normal">
                      <InputLabel>Status</InputLabel>
                      <Select
                        name="status"
                        value={values.status}
                        onChange={handleChange}
                      >
                        <MenuItem value="DRAFT">Draft</MenuItem>
                        <MenuItem value="SENT">Sent</MenuItem>
                        <MenuItem value="VIEWED">Viewed</MenuItem>
                        <MenuItem value="PARTIAL">Partially Paid</MenuItem>
                        <MenuItem value="PAID">Paid</MenuItem>
                        <MenuItem value="OVERDUE">Overdue</MenuItem>
                        <MenuItem value="VOID">Void</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>

                  {/* Invoice Items */}
                  <Grid item xs={12}>
                    <Typography variant="h6" gutterBottom>
                      Invoice Items
                    </Typography>

                    <FieldArray name="items">
                      {({ push, remove }) => (
                        <>
                          {values.items.map((item, index) => (
                            <Grid
                              container
                              spacing={2}
                              key={index}
                              alignItems="center"
                            >
                              {/* Conditional Rendering for Material Search */}
                              {item.type === "MATERIALS" ? (
                                <>
                                  <Grid item xs={12} sm={4}>
                                    {/* Autocomplete for Material Search */}
                                    <Autocomplete
                                      freeSolo
                                      options={
                                        activeMaterialSearchIndex === index
                                          ? materialSearchResults
                                          : []
                                      } // Show results only for active search
                                      loading={
                                        materialSearchLoading &&
                                        activeMaterialSearchIndex === index
                                      }
                                      value={item.description} // Display selected name or typed query
                                      onInputChange={(
                                        event,
                                        newInputValue,
                                        reason
                                      ) => {
                                        // Update description field as user types
                                        setFieldValue(
                                          `items[${index}].description`,
                                          newInputValue
                                        );
                                        // Trigger search only on input change, not selection
                                        if (reason === "input") {
                                          handleMaterialSearch(
                                            newInputValue,
                                            index
                                          );
                                        }
                                      }}
                                      onChange={(event, newValue) => {
                                        // Handle selection from dropdown
                                        if (
                                          typeof newValue !== "string" &&
                                          newValue
                                        ) {
                                          handleSelectMaterial(
                                            newValue,
                                            index,
                                            setFieldValue
                                          );
                                        }
                                      }}
                                      getOptionLabel={(option) =>
                                        typeof option === "string"
                                          ? option
                                          : `${option.name} (${option.sku})`
                                      }
                                      renderInput={(params) => (
                                        <TextField
                                          {...params}
                                          label="Search Material or Description"
                                          margin="normal"
                                          required
                                          error={
                                            touched.items?.[index]
                                              ?.description &&
                                            Boolean(
                                              errors.items?.[index]?.description
                                            )
                                          }
                                          helperText={
                                            touched.items?.[index]
                                              ?.description &&
                                            errors.items?.[index]?.description
                                          }
                                          InputProps={{
                                            ...params.InputProps,
                                            endAdornment: (
                                              <>
                                                {materialSearchLoading &&
                                                activeMaterialSearchIndex ===
                                                  index ? (
                                                  <SearchLoadingIcon
                                                    color="inherit"
                                                    size={20}
                                                  />
                                                ) : null}
                                                {params.InputProps.endAdornment}
                                              </>
                                            ),
                                          }}
                                        />
                                      )}
                                      renderOption={(props, option) => (
                                        <li
                                          {...props}
                                          key={option.sku || option.name}
                                        >
                                          {option.name} ({option.sku}) - $
                                          {option.price?.toFixed(2)}
                                        </li>
                                      )}
                                    />
                                  </Grid>
                                </>
                              ) : (
                                <>
                                  {/* Standard Description Field for non-materials */}
                                  <Grid item xs={12} sm={4}>
                                    <TextField
                                      fullWidth
                                      name={`items[${index}].description`}
                                      label="Description"
                                      value={item.description}
                                      onChange={handleChange}
                                      onBlur={handleBlur}
                                      error={
                                        touched.items?.[index]?.description &&
                                        Boolean(
                                          errors.items?.[index]?.description
                                        )
                                      }
                                      helperText={
                                        touched.items?.[index]?.description &&
                                        errors.items?.[index]?.description
                                      }
                                      margin="normal"
                                      required
                                    />
                                  </Grid>
                                </>
                              )}

                              {/* Type Selector */}
                              <Grid item xs={6} sm={2}>
                                <FormControl fullWidth margin="normal">
                                  <InputLabel>Type</InputLabel>
                                  <Select
                                    name={`items[${index}].type`}
                                    value={item.type || "SERVICE"}
                                    onChange={(e) => {
                                      handleChange(e);
                                      // Reset price if switching away from MATERIALS? Optional.
                                      // if (e.target.value !== 'MATERIALS') {
                                      //   setFieldValue(`items[${index}].unitPrice`, 0);
                                      // }
                                    }}
                                  >
                                    <MenuItem value="SERVICE">Service</MenuItem>
                                    <MenuItem value="PARTS">Parts</MenuItem>
                                    <MenuItem value="LABOR">Labor</MenuItem>
                                    <MenuItem value="MATERIALS">
                                      Materials
                                    </MenuItem>
                                  </Select>
                                </FormControl>
                              </Grid>

                              {/* Quantity */}
                              <Grid item xs={6} sm={1}>
                                <TextField
                                  fullWidth
                                  name={`items[${index}].quantity`}
                                  label="Qty"
                                  type="number"
                                  value={item.quantity}
                                  onChange={handleChange}
                                  onBlur={handleBlur}
                                  error={
                                    touched.items?.[index]?.quantity &&
                                    Boolean(errors.items?.[index]?.quantity)
                                  }
                                  helperText={
                                    touched.items?.[index]?.quantity &&
                                    errors.items?.[index]?.quantity
                                  }
                                  margin="normal"
                                  required
                                  InputProps={{ inputProps: { min: 1 } }}
                                />
                              </Grid>

                              {/* Unit Price (Readonly for Materials after search) */}
                              <Grid item xs={6} sm={2}>
                                <TextField
                                  fullWidth
                                  name={`items[${index}].unitPrice`}
                                  label="Unit Price"
                                  type="number"
                                  value={item.unitPrice}
                                  onChange={handleChange}
                                  onBlur={handleBlur}
                                  error={
                                    touched.items?.[index]?.unitPrice &&
                                    Boolean(errors.items?.[index]?.unitPrice)
                                  }
                                  helperText={
                                    touched.items?.[index]?.unitPrice &&
                                    errors.items?.[index]?.unitPrice
                                  }
                                  margin="normal"
                                  required
                                  InputProps={{
                                    startAdornment: (
                                      <InputAdornment position="start">
                                        $
                                      </InputAdornment>
                                    ),
                                    inputProps: { min: 0, step: 0.01 },
                                    readOnly:
                                      item.type === "MATERIALS" && item.sku, // Make price readonly if material selected
                                  }}
                                />
                              </Grid>

                              <Grid item xs={6} sm={2}>
                                <TextField
                                  fullWidth
                                  name={`items[${index}].tax`}
                                  label="Tax %"
                                  type="number"
                                  value={item.tax}
                                  onChange={handleChange}
                                  onBlur={handleBlur}
                                  error={
                                    touched.items?.[index]?.tax &&
                                    Boolean(errors.items?.[index]?.tax)
                                  }
                                  helperText={
                                    touched.items?.[index]?.tax &&
                                    errors.items?.[index]?.tax
                                  }
                                  margin="normal"
                                  InputProps={{
                                    endAdornment: (
                                      <InputAdornment position="end">
                                        %
                                      </InputAdornment>
                                    ),
                                    inputProps: { min: 0, step: 0.1 },
                                  }}
                                />
                              </Grid>

                              <Grid item xs={6} sm={1}>
                                {values.items.length > 1 && (
                                  <IconButton
                                    color="error"
                                    onClick={() => remove(index)}
                                    sx={{ mt: 2 }}
                                  >
                                    <DeleteIcon />
                                  </IconButton>
                                )}
                              </Grid>

                              {index === values.items.length - 1 && (
                                <Grid item xs={12} sx={{ mt: 1 }}>
                                  <Button
                                    startIcon={<AddIcon />}
                                    onClick={() =>
                                      push({
                                        description: "",
                                        quantity: 1,
                                        unitPrice: 0,
                                        tax: 0,
                                        type: "SERVICE",
                                      })
                                    }
                                    variant="outlined"
                                    size="small"
                                  >
                                    Add Item
                                  </Button>
                                </Grid>
                              )}
                            </Grid>
                          ))}
                        </>
                      )}
                    </FieldArray>
                  </Grid>

                  {/* Notes and Terms */}
                  <Grid item xs={12} md={6}>
                    <TextField
                      fullWidth
                      name="notes"
                      label="Notes (Visible to Customer)"
                      multiline
                      rows={4}
                      value={values.notes}
                      onChange={handleChange}
                      margin="normal"
                    />
                  </Grid>

                  <Grid item xs={12} md={6}>
                    <TextField
                      fullWidth
                      name="terms"
                      label="Terms and Conditions"
                      multiline
                      rows={4}
                      value={values.terms}
                      onChange={handleChange}
                      margin="normal"
                    />
                  </Grid>

                  {/* Totals */}
                  <Grid item xs={12}>
                    <Divider sx={{ my: 2 }} />
                    <Grid container spacing={2} justifyContent="flex-end">
                      <Grid item xs={12} md={4}>
                        <Typography variant="subtitle1" gutterBottom>
                          Subtotal: $
                          {calculateSubtotal(values.items).toFixed(2)}
                        </Typography>

                        <Typography variant="subtitle1" gutterBottom>
                          Tax: ${calculateTax(values.items).toFixed(2)}
                        </Typography>

                        <TextField
                          fullWidth
                          name="discount"
                          label="Discount"
                          type="number"
                          value={values.discount}
                          onChange={handleChange}
                          onBlur={handleBlur}
                          error={touched.discount && Boolean(errors.discount)}
                          helperText={touched.discount && errors.discount}
                          margin="normal"
                          InputProps={{
                            endAdornment: (
                              <InputAdornment position="end">%</InputAdornment>
                            ),
                            inputProps: { min: 0, max: 100, step: 0.1 },
                          }}
                        />

                        <Typography
                          variant="h6"
                          sx={{ mt: 2, fontWeight: "bold" }}
                        >
                          Total: $
                          {calculateTotal(
                            values.items,
                            values.discount
                          ).toFixed(2)}
                        </Typography>
                      </Grid>
                    </Grid>
                  </Grid>

                  {/* Submit Buttons */}
                  <Grid item xs={12}>
                    <Divider sx={{ my: 2 }} />
                    <Box display="flex" justifyContent="flex-end" gap={2}>
                      <Button
                        variant="outlined"
                        onClick={() => navigate("/invoices")}
                      >
                        Cancel
                      </Button>
                      <Button
                        type="submit"
                        variant="contained"
                        color="primary"
                        disabled={submitLoading}
                        startIcon={
                          submitLoading && (
                            <CircularProgress size={20} color="inherit" />
                          )
                        }
                      >
                        {submitLoading ? "Creating..." : "Create Invoice"}
                      </Button>
                    </Box>
                  </Grid>
                </Grid>
              </Form>
            )}
          </Formik>
        )}
      </Paper>
    </Container>
  );
};

export default CreateInvoice;
