import { useState, useCallback, useRef } from 'react';
import axios from 'axios';
import { useSelector } from 'react-redux';
import logger from '../utils/logger';

/**
 * Custom hook for managing price lookup operations
 * Provides functionality to lookup prices for items without saving quotes
 */
const usePriceLookup = () => {
  const { userInfo } = useSelector((state) => state.auth);
  const [isLookingUp, setIsLookingUp] = useState(false);
  const [lookupProgress, setLookupProgress] = useState({
    total: 0,
    processed: 0,
    found: 0,
    failed: 0,
    percentage: 0,
  });
  const [lookupResults, setLookupResults] = useState([]);
  const [lookupError, setLookupError] = useState(null);
  
  const abortControllerRef = useRef(null);

  /**
   * Lookup prices for an array of items
   * @param {Array} items - Array of items to lookup prices for
   * @param {Object} options - Lookup options
   * @returns {Promise} Resolves with updated items
   */
  const lookupPrices = useCallback(async (items, options = {}) => {
    if (!items || !Array.isArray(items) || items.length === 0) {
      throw new Error('Items array is required for price lookup');
    }

    if (!userInfo?.token) {
      throw new Error('User authentication required for price lookup');
    }

    // Cancel any existing lookup
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    // Create new abort controller
    abortControllerRef.current = new AbortController();

    setIsLookingUp(true);
    setLookupError(null);
    setLookupProgress({
      total: items.length,
      processed: 0,
      found: 0,
      failed: 0,
      percentage: 0,
    });

    try {
      logger.info(`[usePriceLookup] Starting price lookup for ${items.length} items`);

      const requestPayload = {
        items: items.map(item => ({
          name: item.name || '',
          description: item.description || '',
          sku: item.sku || '',
          price: item.price || 0,
          quantity: item.quantity || 1,
          unit: item.unit || 'each',
          lookup_query_suggestion: item.lookup_query_suggestion || item._aiData?.lookup_query_suggestion || '',
          source: item.source || '',
          sourceId: item.sourceId || '',
          url: item.url || '',
          imageUrl: item.imageUrl || '',
        }))
      };

      const config = {
        headers: {
          'Authorization': `Bearer ${userInfo.token}`,
          'Content-Type': 'application/json',
        },
        signal: abortControllerRef.current.signal,
        timeout: options.timeout || 60000, // 60 second timeout
      };

      // Show immediate progress update
      setLookupProgress(prev => ({
        ...prev,
        percentage: 5, // Small initial progress
      }));

      const response = await axios.post('/api/quotes/lookup-prices', requestPayload, config);

      if (!response.data.success) {
        throw new Error(response.data.message || 'Price lookup failed');
      }

      const { items: updatedItems, summary } = response.data;

      // Update progress with final results
      setLookupProgress({
        total: summary.total,
        processed: summary.processed,
        found: summary.pricesFound,
        failed: summary.pricesFailed,
        percentage: 100,
      });

      setLookupResults(updatedItems);

      logger.info(`[usePriceLookup] Price lookup completed:`, {
        total: summary.total,
        found: summary.pricesFound,
        failed: summary.pricesFailed,
        averageTime: summary.averageResponseTime,
      });

      return {
        success: true,
        items: updatedItems,
        summary,
      };

    } catch (error) {
      if (error.name === 'AbortError') {
        logger.info('[usePriceLookup] Price lookup was cancelled');
        setLookupError('Price lookup was cancelled');
      } else {
        logger.error('[usePriceLookup] Price lookup failed:', error);
        const errorMessage = error.response?.data?.message || error.message || 'Price lookup failed';
        setLookupError(errorMessage);
      }

      throw error;
    } finally {
      setIsLookingUp(false);
      abortControllerRef.current = null;
    }
  }, [userInfo?.token]);

  /**
   * Cancel ongoing price lookup
   */
  const cancelLookup = useCallback(() => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }
  }, []);

  /**
   * Reset lookup state
   */
  const resetLookup = useCallback(() => {
    setIsLookingUp(false);
    setLookupProgress({
      total: 0,
      processed: 0,
      found: 0,
      failed: 0,
      percentage: 0,
    });
    setLookupResults([]);
    setLookupError(null);
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }
  }, []);

  /**
   * Retry lookup for specific failed items
   * @param {Array} failedItems - Items that failed lookup
   * @returns {Promise} Resolves with retry results
   */
  const retryFailedItems = useCallback(async (failedItems) => {
    if (!failedItems || failedItems.length === 0) {
      return { success: true, items: [], summary: { total: 0, pricesFound: 0, pricesFailed: 0 } };
    }

    logger.info(`[usePriceLookup] Retrying price lookup for ${failedItems.length} failed items`);

    return await lookupPrices(failedItems, { timeout: 30000 }); // Shorter timeout for retries
  }, [lookupPrices]);

  /**
   * Get summary statistics from current results
   */
  const getSummary = useCallback(() => {
    const totalValue = lookupResults.reduce((sum, item) => {
      if (item.price && item.quantity) {
        return sum + (item.price * item.quantity);
      }
      return sum;
    }, 0);

    const averageResponseTime = lookupResults.reduce((sum, item) => {
      return sum + (item.priceInfo?.responseTime || 0);
    }, 0) / (lookupResults.length || 1);

    return {
      ...lookupProgress,
      totalValue,
      averageResponseTime: Math.round(averageResponseTime),
      successRate: lookupProgress.total > 0 ? (lookupProgress.found / lookupProgress.total * 100) : 0,
    };
  }, [lookupProgress, lookupResults]);

  /**
   * Update a single item's price info
   * @param {number} index - Index of item to update
   * @param {Object} priceInfo - New price information
   */
  const updateItemPriceInfo = useCallback((index, priceInfo) => {
    setLookupResults(prev => {
      const updated = [...prev];
      if (updated[index]) {
        updated[index] = {
          ...updated[index],
          ...priceInfo,
          priceInfo: {
            ...updated[index].priceInfo,
            ...priceInfo.priceInfo,
          },
        };
      }
      return updated;
    });
  }, []);

  return {
    // State
    isLookingUp,
    lookupProgress,
    lookupResults,
    lookupError,
    
    // Actions
    lookupPrices,
    cancelLookup,
    resetLookup,
    retryFailedItems,
    updateItemPriceInfo,
    
    // Computed
    getSummary,
    
    // Helpers
    hasResults: lookupResults.length > 0,
    isComplete: !isLookingUp && lookupProgress.percentage === 100,
    hasFailed: lookupProgress.failed > 0,
    hasSucceeded: lookupProgress.found > 0,
  };
};

export default usePriceLookup;