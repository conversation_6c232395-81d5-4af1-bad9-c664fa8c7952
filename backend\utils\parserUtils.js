/**
 * parserUtils.js
 * Utilities for parsing scraped content, especially Markdown from Firecrawl.
 */
const logger = require("./logger");

/**
 * Parses Markdown content from a Home Depot search results page scraped by Firecrawl.
 * Extracts product name, price, and URL.
 *
 * @param {string} markdownContent - The Markdown content string.
 * @param {string} sourceName - The name of the source (e.g., "HOME_DEPOT").
 * @returns {Array<object>} An array of product objects { name, price, url, source }.
 */
function parseHomeDepotMarkdown(markdownContent, sourceName = "HOME_DEPOT") {
  const products = [];
  if (!markdownContent) {
    return products;
  }

  // Regex revised to be more robust: Looks for Name link, its URL, skips intermediate content until the price.
  // Captures: 1=Name, 2=URL, 3=Price
  const productBlockRegex =
    /\[\*\*([^*]+)\*\*\]\(([^)]+)\)(?:.|\n)*?\$(\d+\.\d+)/g;

  let match;
  while ((match = productBlockRegex.exec(markdownContent)) !== null) {
    try {
      // Adjusted capture groups for the new regex
      const name = match[1].trim();
      const productUrl = match[2];
      const price = parseFloat(match[3]);

      if (name && !isNaN(price) && productUrl) {
        // Standardized format
        products.push({
          name: name,
          price: price,
          url: productUrl,
          sku: null, // SKU not reliably parsed from search markdown yet
          imageUrl: null, // Image URL not parsed from search markdown yet
          source: sourceName,
          method: "firecrawl", // Indicate method
        });
      }
    } catch (e) {
      logger.error(
        `Error parsing product block in Home Depot Markdown: ${e.message}`,
        { block: match[0] }
      );
    }
  }

  // Remove the simpler fallback regex for now, focus on making the primary one work.
  // If the primary regex fails, it indicates a more significant structure change.
  // if (products.length === 0) { ... }

  logger.info(
    `Parsed ${products.length} products from Home Depot Markdown for source ${sourceName}`
  );
  return products;
}

/**
 * Parses Markdown content from a Platt search results page scraped by Firecrawl.
 * Placeholder function - needs implementation based on observed Markdown structure.
 *
 * @param {string} markdownContent - The Markdown content string.
 * @param {string} sourceName - The name of the source (e.g., "GRAINGER").
 * @returns {Array<object>} An array of product objects { name, price, url, sku, imageUrl, source, method }.
 */
function parsePlattMarkdown(markdownContent, sourceName = "PLATT") {
  // Renamed function and default source
  const products = [];
  if (!markdownContent) {
    return products;
  }
  logger.info(
    `Attempting to parse Platt Markdown (Length: ${markdownContent.length})`
  ); // Updated log

  // Regex attempt 1: Look for a pattern like: ![Alt text](Image URL) [Product Name](Product URL) Item # SKU $Price
  // This is a guess based on common list structures. Needs verification with actual Firecrawl output.
  const productBlockRegex1 =
    /!\[[^\]]*\]\(([^)]+)\)\s*\[([^\]]+)\]\(([^)]+)\)\s*Item\s*#\s*([^\s]+)\s*\$(\d+\.\d+)/g;
  let match;

  while ((match = productBlockRegex1.exec(markdownContent)) !== null) {
    try {
      const imageUrl = match[1];
      const name = match[2].trim();
      const productUrl = match[3];
      const sku = match[4].trim();
      const price = parseFloat(match[5]);

      if (name && !isNaN(price) && productUrl && sku) {
        products.push({
          name: name,
          price: price,
          url: productUrl,
          sku: `PLATT-${sku}`, // Add prefix
          imageUrl: imageUrl,
          source: sourceName,
          method: "firecrawl",
        });
      }
    } catch (e) {
      logger.error(
        `Error parsing Platt product block (Regex 1): ${e.message}`,
        { block: match[0] }
      ); // Updated log
    }
  }

  // Add more specific or fallback regex patterns here if needed based on actual output.
  // Example Fallback: Just look for linked price? \[([^\]]+)\]\(([^)]+)\)\s*\$(\d+\.\d+)

  if (products.length === 0) {
    logger.warn(
      `No products parsed from Platt Markdown using current regex patterns.`
    ); // Updated log
    // Consider adding more fallback regex attempts here.
  } else {
    logger.info(`Parsed ${products.length} products from Platt Markdown.`); // Updated log
  }

  return products;
}

// TODO: Implement generic product page parser if needed for fallback links
// function parseProductPageMarkdown(markdownContent, sourceName) { ... }

/**
 * Determines the lookup type and necessary MCP request for a given item.
 * @param {object} item - An item object from the AI's JSON response.
 * @returns {object|null} - An object like { lookupType: string, mcpRequest?: { tool_id: string, tool_params: object, preferred_parser?: string } }
 *                          or null if not determinable.
 *                          For NEC types, mcpRequest will be undefined.
 */
function determineLookupTypeForItem(item) {
  if (!item || !item.description) {
    logger.debug(
      "[parserUtils] determineLookupTypeForItem: Received null or invalid item."
    );
    return null;
  }

  const description = item.description.toLowerCase();
  const attributes = item.attributes || {};
  const suggestion = item.lookup_query_suggestion
    ? item.lookup_query_suggestion.toLowerCase()
    : null;

  logger.debug(
    `[parserUtils] determineLookupTypeForItem: Processing item - Description: "${description}", Attributes: ${JSON.stringify(
      attributes
    )}, Suggestion: "${item.lookup_query_suggestion}"`
  );

  // Note: With AI categorization, this function now only handles NEC lookups and price lookup types
  // The AI provides the category (electrical_material, labor, etc.) so we don't need keyword detection

  // NEC Lookups (prioritize these if specific keywords are present)
  if (
    description.includes("conductor") &&
    description.includes("size") &&
    (attributes.ampacity ||
      attributes.service_conductor_size ||
      (attributes.size && attributes.material))
  ) {
    if (
      description.includes("grounding electrode conductor") ||
      description.includes("gec")
    ) {
      logger.debug(
        "[parserUtils] determineLookupTypeForItem: Classified as NEC_grounding_electrode_conductor_size."
      );
      return { lookupType: "NEC_grounding_electrode_conductor_size" };
    }
    logger.debug(
      "[parserUtils] determineLookupTypeForItem: Classified as NEC_conductor_size."
    );
    return { lookupType: "NEC_conductor_size" };
  }
  if (
    description.includes("conduit") &&
    (description.includes("fill") ||
      (description.includes("size") && attributes.conductor_details))
  ) {
    logger.debug(
      "[parserUtils] determineLookupTypeForItem: Classified as NEC_conduit_size."
    );
    return { lookupType: "NEC_conduit_size" };
  }

  // Crawl4AI request for non-NEC lookups if a suggestion exists
  let crawl4aiRequest = null;
  if (item.lookup_query_suggestion) {
    crawl4aiRequest = {
      method: "searchByDescription",
      query: item.lookup_query_suggestion,
    };
  }

  // Price Lookups (if NEC lookups don't match and suggestion exists)
  if (suggestion) {
    // suggestion is already item.lookup_query_suggestion.toLowerCase() or null
    if (suggestion.includes("price") || suggestion.includes("cost")) {
      if (
        attributes.material ||
        description.includes("wire") ||
        description.includes("cable") ||
        description.includes("conduit") ||
        description.includes("breaker") ||
        description.includes("panel") ||
        description.includes("meter")
      ) {
        logger.debug(
          "[parserUtils] determineLookupTypeForItem: Classified as PRICE_MATERIAL_COMPONENT."
        );
        return {
          lookupType: "PRICE_MATERIAL_COMPONENT",
          crawl4aiRequest: crawl4aiRequest,
        };
      }
      logger.debug(
        "[parserUtils] determineLookupTypeForItem: Classified as PRICE_GENERAL."
      );
      return { lookupType: "PRICE_GENERAL", crawl4aiRequest: crawl4aiRequest };
    }
    if (suggestion.includes("availability") || suggestion.includes("stock")) {
      logger.debug(
        "[parserUtils] determineLookupTypeForItem: Classified as AVAILABILITY_CHECK."
      );
      return {
        lookupType: "AVAILABILITY_CHECK",
        crawl4aiRequest: crawl4aiRequest,
      };
    }
  }

  // For items with lookup suggestions, return appropriate lookup type
  if (suggestion && item.lookup_query_suggestion.length > 5) {
    logger.debug(
      "[parserUtils] determineLookupTypeForItem: Item has lookup suggestion, creating lookup request."
    );
    return {
      lookupType: "PRICE_MATERIAL_COMPONENT",
      crawl4aiRequest: crawl4aiRequest,
    };
  }

  logger.debug(
    "[parserUtils] determineLookupTypeForItem: No specific lookup action determined."
  );
  return null;
}

module.exports = {
  parseHomeDepotMarkdown,
  parsePlattMarkdown,
  determineLookupTypeForItem,
};
