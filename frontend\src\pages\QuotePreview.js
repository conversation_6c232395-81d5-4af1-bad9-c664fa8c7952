import React from "react";
import { useLocation, useNavigate } from "react-router-dom";
import {
  Box,
  Typography,
  Paper,
  Button,
  Grid,
  Divider,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
} from "@mui/material";
import ReactMarkdown from "react-markdown";
import { formatCurrency } from "../utils/formatters"; // Assuming this utility exists and is correct
import { ArrowBack as ArrowBackIcon } from "@mui/icons-material";

const QuotePreview = ({ quoteData: propQuoteData, isModalView }) => {
  const location = useLocation();
  const navigate = useNavigate();
  // Prioritize propQuoteData if in modal view, otherwise use location state
  const quoteData = isModalView
    ? propQuoteData
    : (location.state || {}).quoteData;

  if (!quoteData) {
    return (
      <Box sx={{ p: 3 }}>
        <Typography variant="h6" color="error">
          Error: No quote data found for preview.
        </Typography>
        <Button
          variant="outlined"
          startIcon={<ArrowBackIcon />}
          onClick={() => navigate(-1)}
          sx={{ mt: 2 }}
        >
          Go Back
        </Button>
      </Box>
    );
  }

  // Destructure data for easier access
  const {
    name,
    projectOverview,
    scopeOfWork,
    materialsIncluded,
    items = [],
    customer,
    validUntil,
    labor,
    summary,
  } = quoteData;

  const formatDate = (dateString) => {
    if (!dateString) return "N/A";
    try {
      return new Date(dateString).toLocaleDateString();
    } catch (e) {
      return "Invalid Date";
    }
  };

  // Format customer name safely
  const customerName =
    customer?.businessName ||
    `${customer?.contactPerson?.firstName || ""} ${
      customer?.contactPerson?.lastName || ""
    }`.trim() ||
    "N/A";

  return (
    <Box
      sx={{
        maxWidth: isModalView ? "100%" : 900,
        mx: "auto",
        p: isModalView ? 0 : 3,
      }}
    >
      {!isModalView && (
        <Button
          variant="outlined"
          startIcon={<ArrowBackIcon />}
          onClick={() => navigate(-1)}
          sx={{ mb: 2 }}
        >
          Back to Edit
        </Button>
      )}

      <Paper sx={{ p: 4 }}>
        <Typography variant="h4" gutterBottom align="center">
          Quote Preview: {name || "Unnamed Quote"}
        </Typography>
        <Divider sx={{ my: 2 }} />

        <Grid container spacing={2} sx={{ mb: 3 }}>
          <Grid item xs={6}>
            <Typography variant="subtitle1">Customer:</Typography>
            <Typography>{customerName}</Typography>
            {/* Add more customer details if needed and available */}
          </Grid>
          <Grid item xs={6} sx={{ textAlign: "right" }}>
            <Typography variant="subtitle1">Valid Until:</Typography>
            <Typography>{formatDate(validUntil)}</Typography>
          </Grid>
        </Grid>

        {projectOverview && (
          <Box sx={{ mb: 3 }}>
            <Typography variant="h6" gutterBottom>
              Project Overview
            </Typography>
            <Typography variant="body1" sx={{ whiteSpace: "pre-wrap" }}>
              {projectOverview}
            </Typography>
          </Box>
        )}

        {scopeOfWork && (
          <Box sx={{ mb: 3 }}>
            {/* Render Markdown for Scope of Work */}
            <ReactMarkdown>{scopeOfWork}</ReactMarkdown>
          </Box>
        )}

        {materialsIncluded && (
          <Box sx={{ mb: 3 }}>
            {/* Render Markdown for Materials Included */}
            <ReactMarkdown>{materialsIncluded}</ReactMarkdown>
          </Box>
        )}

        {items.length > 0 && (
          <Box sx={{ mb: 3 }}>
            <Typography variant="h6" gutterBottom>
              Material Items
            </Typography>
            <TableContainer component={Paper} variant="outlined">
              <Table size="small">
                <TableHead>
                  <TableRow>
                    <TableCell sx={{ width: "50px" }}>Image</TableCell>
                    <TableCell>Material</TableCell>
                    <TableCell align="right">Qty</TableCell>
                    <TableCell align="right">Unit Price</TableCell>
                    <TableCell align="right">Total</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {items.map((item, index) => (
                    <TableRow key={index}>
                      <TableCell>
                        {item.imageUrl ? (
                          <img
                            src={item.imageUrl}
                            alt={item.name || "Item"}
                            style={{
                              width: "40px",
                              height: "40px",
                              objectFit: "contain",
                              border: "1px solid #eee",
                            }}
                          />
                        ) : (
                          <Box
                            sx={{
                              width: "40px",
                              height: "40px",
                              bgcolor: "grey.200",
                              display: "inline-block",
                            }}
                          /> // Placeholder box
                        )}
                      </TableCell>
                      <TableCell>{item.name || "N/A"}</TableCell>
                      <TableCell align="right">{item.quantity || 0}</TableCell>
                      <TableCell align="right">
                        {formatCurrency(item.price || 0)}
                      </TableCell>
                      <TableCell align="right">
                        {formatCurrency(
                          (item.quantity || 0) * (item.price || 0)
                        )}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </Box>
        )}

        {/* Summary Section */}
        <Box sx={{ mt: 4, display: "flex", justifyContent: "flex-end" }}>
          <Box sx={{ width: "300px" }}>
            <Grid container spacing={1}>
              <Grid item xs={6}>
                <Typography>Materials Total:</Typography>
              </Grid>
              <Grid item xs={6} sx={{ textAlign: "right" }}>
                <Typography>
                  {formatCurrency(summary?.materialsTotal || 0)}
                </Typography>
              </Grid>

              {labor && (
                <>
                  <Grid item xs={6}>
                    <Typography>Labor Total:</Typography>
                  </Grid>
                  <Grid item xs={6} sx={{ textAlign: "right" }}>
                    <Typography>{formatCurrency(labor.total || 0)}</Typography>
                  </Grid>
                </>
              )}

              <Grid item xs={12}>
                <Divider sx={{ my: 1 }} />
              </Grid>

              <Grid item xs={6}>
                <Typography variant="h6">Grand Total:</Typography>
              </Grid>
              <Grid item xs={6} sx={{ textAlign: "right" }}>
                <Typography variant="h6">
                  {formatCurrency(summary?.grandTotal || 0)}
                </Typography>
              </Grid>
            </Grid>
          </Box>
        </Box>
      </Paper>
    </Box>
  );
};

export default QuotePreview;
