/**
 * humanBehavior.js
 * Utility for simulating realistic human behaviors when interacting with websites
 * Helps reduce CAPTCHA challenges by making automation appear more human-like
 * Enhanced with more sophisticated human behavior patterns
 */

const logger = require("./logger");
const path = require("path");
const fs = require("fs");

/**
 * Adds a random delay between actions to simulate human timing
 * Uses a more natural distribution to better mimic human behavior
 * @param {number} minMs - Minimum delay in milliseconds
 * @param {number} maxMs - Maximum delay in milliseconds
 * @param {string} delayType - Type of delay ('normal', 'thinking', 'reaction')
 * @returns {Promise<number>} - The actual delay used
 */
async function randomDelay(minMs = 500, maxMs = 3000, delayType = "normal") {
  // Different delay strategies for different actions
  let delay;

  switch (delayType) {
    case "thinking":
      // When user is "thinking" - more weighted toward longer times
      // Uses a skewed distribution
      const thinkingBase = Math.floor(
        minMs + Math.random() * (maxMs - minMs) * 0.4
      );
      const thinkingExtra = Math.floor(
        Math.random() * Math.random() * (maxMs - minMs) * 0.6
      );
      delay = thinkingBase + thinkingExtra;
      break;

    case "reaction":
      // Quick reaction times (clicking buttons, etc)
      // Normal people have a minimum reaction time around 200-250ms
      const minReaction = Math.max(200, minMs);
      delay = Math.floor(
        minReaction + Math.random() * Math.random() * (maxMs - minReaction)
      );
      break;

    case "normal":
    default:
      // Standard delay - slightly weighted toward the lower range
      // Uses Box-Muller transform for a more natural normal distribution
      const mean = minMs + (maxMs - minMs) * 0.4; // Skew toward lower values
      const stdDev = (maxMs - minMs) * 0.25;

      // Box-Muller transform to generate normal distribution
      const u1 = Math.random();
      const u2 = Math.random();
      const z0 = Math.sqrt(-2.0 * Math.log(u1)) * Math.cos(2.0 * Math.PI * u2);

      delay = Math.floor(mean + z0 * stdDev);
      // Ensure within bounds
      delay = Math.max(minMs, Math.min(maxMs, delay));
  }

  await new Promise((resolve) => setTimeout(resolve, delay));
  return delay;
}

/**
 * Simulates enhanced human-like interactions on a page (scrolling, mouse movement, etc.)
 * @param {Object} mcpClient - MCP client instance
 * @param {string} pageId - MCP page ID
 * @param {Object} options - Optional configuration
 * @param {boolean} options.advancedScrolling - Whether to perform advanced scrolling behavior
 * @param {boolean} options.mouseMovements - Whether to perform mouse movements
 * @param {boolean} options.idleBehavior - Whether to simulate idle behavior
 * @returns {Promise<boolean>} - Success status
 */
async function simulateHumanInteraction(mcpClient, pageId, options = {}) {
  if (!mcpClient || !pageId) {
    logger.warn(
      `[HumanBehavior] Missing parameters: mcpClient=${!!mcpClient}, pageId=${pageId}`
    );
    return false;
  }

  const {
    advancedScrolling = true,
    mouseMovements = true,
    idleBehavior = Math.random() > 0.7, // Occasionally simulate idle behavior
  } = options;

  try {
    logger.debug(
      `[HumanBehavior] Simulating enhanced human interaction on page ${pageId}`
    );

    // Initial "looking at the page" pause
    await randomDelay(1000, 2500, "thinking");

    // Get page dimensions for more realistic interactions
    const pageDimensions = await mcpClient.callMcpTool("evaluate", {
      script: `() => {
        return {
          width: window.innerWidth || document.documentElement.clientWidth || document.body.clientWidth,
          height: window.innerHeight || document.documentElement.clientHeight || document.body.clientHeight,
          scrollHeight: Math.max(
            document.body.scrollHeight,
            document.documentElement.scrollHeight,
            document.body.offsetHeight,
            document.documentElement.offsetHeight
          )
        };
      }`,
      pageId,
    });

    const {
      width = 1200,
      height = 800,
      scrollHeight = 1500,
    } = pageDimensions || {};

    // PART 1: ADVANCED SCROLLING BEHAVIOR
    if (advancedScrolling) {
      // Reading-like scrolling pattern with natural pauses
      const scrollSteps = Math.floor(3 + Math.random() * 5); // 3-7 scroll actions

      for (let i = 0; i < scrollSteps; i++) {
        // Each scroll amount represents reading a few paragraphs
        const scrollAmount = Math.floor(80 + Math.random() * 250);

        // Natural scrolling with bezier curve for smooth acceleration and deceleration
        await mcpClient.callMcpTool("evaluate", {
          script: `(scrollAmount) => {
            // Smooth scroll with bezier easing for natural movement
            const duration = 400 + Math.random() * 300;
            const startTime = performance.now();
            const startScrollY = window.scrollY;
            
            return new Promise(resolve => {
              function step() {
                const elapsed = performance.now() - startTime;
                const progress = Math.min(elapsed / duration, 1);
                
                // Apply cubic bezier curve for natural easing
                // This creates a more natural acceleration/deceleration
                const t = progress;
                const easeAmount = t < 0.5 
                  ? 4 * t * t * t 
                  : (t - 1) * (2 * t - 2) * (2 * t - 2) + 1; // Cubic easing
                
                window.scrollTo(0, startScrollY + (scrollAmount * easeAmount));
                
                if (progress < 1) {
                  requestAnimationFrame(step);
                } else {
                  resolve({ scrolled: scrollAmount });
                }
              }
              requestAnimationFrame(step);
            });
          }`,
          args: [scrollAmount],
          pageId,
        });

        // Reading pause - time to read the new content
        // Longer pauses simulate actually reading the content
        await randomDelay(800, 3000, "thinking");
      }

      // Occasionally scroll back up a bit, as if revisiting something
      if (Math.random() > 0.6) {
        const upScrollAmount = -1 * Math.floor(50 + Math.random() * 150);

        await mcpClient.callMcpTool("evaluate", {
          script: `(scrollAmount) => {
            return new Promise(resolve => {
              window.scrollBy({ top: scrollAmount, behavior: 'smooth' });
              setTimeout(() => resolve({ scrolled: scrollAmount }), 400);
            });
          }`,
          args: [upScrollAmount],
          pageId,
        });

        // Short pause after scrolling back up
        await randomDelay(500, 1500, "normal");
      }
    }

    // PART 2: MOUSE MOVEMENTS (if enabled)
    if (mouseMovements) {
      // We'll execute a series of natural mouse movements
      // The number of movements depends on the page size
      const movementCount = Math.floor(3 + Math.random() * 4); // 3-6 separate movements

      for (let i = 0; i < movementCount; i++) {
        // Target coordinates (avoid extreme edges)
        const targetX = Math.floor(50 + Math.random() * (width - 100));
        const targetY = Math.floor(
          50 + Math.random() * (Math.min(height, 600) - 100)
        );

        // Execute natural mouse movement with bezier curve
        await mcpClient.callMcpTool("evaluate", {
          script: `(targetX, targetY) => {
            // Get current mouse position from a mouse move event listener
            let currentX = 500;
            let currentY = 200;
            
            // Try to get a more accurate starting position if we can
            document.addEventListener('mousemove', function updatePos(e) {
              currentX = e.clientX;
              currentY = e.clientY;
              document.removeEventListener('mousemove', updatePos);
            }, { once: true });
            
            // Generate control points for a bezier curve
            // This creates natural arcs in mouse movement instead of straight lines
            const cp1x = currentX + (targetX - currentX) * (0.3 + Math.random() * 0.4);
            const cp1y = currentY + (targetY - currentY) * (0.1 + Math.random() * 0.2);
            const cp2x = currentX + (targetX - currentX) * (0.6 + Math.random() * 0.3);
            const cp2y = targetY - (targetY - currentY) * (0.1 + Math.random() * 0.2);
            
            // Generate points along the bezier curve
            const points = [];
            const steps = 20 + Math.floor(Math.random() * 15); // 20-34 steps for smoothness
            
            for (let i = 0; i <= steps; i++) {
              const t = i / steps;
              // Cubic bezier formula
              const x = Math.pow(1 - t, 3) * currentX +
                       3 * Math.pow(1 - t, 2) * t * cp1x +
                       3 * (1 - t) * Math.pow(t, 2) * cp2x +
                       Math.pow(t, 3) * targetX;
                       
              const y = Math.pow(1 - t, 3) * currentY +
                       3 * Math.pow(1 - t, 2) * t * cp1y +
                       3 * (1 - t) * Math.pow(t, 2) * cp2y +
                       Math.pow(t, 3) * targetY;
              
              points.push({ x: Math.round(x), y: Math.round(y) });
            }
            
            // Simulate the movement with variable speed
            return new Promise(resolve => {
              let idx = 0;
              function moveNextPoint() {
                if (idx >= points.length) {
                  resolve({ moved: true, finalX: targetX, finalY: targetY });
                  return;
                }
                
                const point = points[idx++];
                const event = new MouseEvent('mousemove', {
                  bubbles: true,
                  cancelable: true,
                  clientX: point.x,
                  clientY: point.y,
                  screenX: point.x,
                  screenY: point.y + 50, // Approximate screen position
                  view: window
                });
                document.dispatchEvent(event);
                
                // Variable speed - faster in the middle, slower at beginning and end
                const progress = idx / points.length;
                const speed = progress < 0.2 || progress > 0.8 ? 
                  10 + Math.random() * 15 : // Slower at start/end
                  3 + Math.random() * 7;   // Faster in the middle
                
                setTimeout(moveNextPoint, speed);
              }
              moveNextPoint();
            });
          }`,
          args: [targetX, targetY],
          pageId,
        });

        // Pause between movements
        await randomDelay(300, 800, "normal");
      }
    }

    logger.debug(
      `[HumanBehavior] Successfully simulated enhanced human interaction on page ${pageId}`
    );
    return true;
  } catch (error) {
    logger.error(
      `[HumanBehavior] Error simulating human interaction: ${error.message}`,
      { stack: error.stack }
    );
    return false;
  }
}

/**
 * Simulates enhanced human-like typing behavior with realistic patterns
 * @param {Object} mcpClient - MCP client instance
 * @param {string} pageId - MCP page ID
 * @param {string} selector - CSS selector for input element
 * @param {string} text - Text to type
 * @param {Object} options - Optional configuration
 * @param {boolean} options.naturalMistakes - Whether to simulate occasional typing mistakes
 * @param {boolean} options.variableSpeed - Whether to use variable typing speed
 * @returns {Promise<boolean>} - Success status
 */
async function simulateHumanTyping(
  mcpClient,
  pageId,
  selector,
  text,
  options = {}
) {
  if (!mcpClient || !pageId || !selector || !text) {
    logger.warn(`[HumanBehavior] Missing parameters for typing simulation`);
    return false;
  }

  const {
    naturalMistakes = Math.random() > 0.7, // 30% chance of making mistakes
    variableSpeed = true,
  } = options;

  try {
    logger.debug(
      `[HumanBehavior] Simulating enhanced human typing on page ${pageId}`
    );

    // First click the element
    await mcpClient.callMcpTool("click", {
      selector,
      pageId,
    });

    // Wait a moment before typing (like a human would)
    await randomDelay(300, 800, "thinking");

    // Analyze text for typing patterns
    const wordSplits = text.split(/\s+/);
    const isFastTyper = Math.random() > 0.5; // Determine typing persona

    // Base typing speeds based on persona
    const baseDelayRange = isFastTyper
      ? { min: 30, max: 90 } // Fast typist
      : { min: 70, max: 180 }; // Slow typist

    // For each word in the input
    let typedText = "";

    for (let wordIndex = 0; wordIndex < wordSplits.length; wordIndex++) {
      const word = wordSplits[wordIndex];
      let wordToType = word;

      // Add space between words (except before first word)
      if (wordIndex > 0) {
        await randomDelay(
          baseDelayRange.min + 30,
          baseDelayRange.max + 50,
          "normal"
        );

        // Type the space
        await mcpClient.callMcpTool("evaluate", {
          script: `(selector, char) => {
            const element = document.querySelector(selector);
            if (!element) return false;
            element.value = (element.value || '') + char;
            element.dispatchEvent(new Event('input', { bubbles: true }));
            return true;
          }`,
          args: [selector, " "],
          pageId,
        });

        typedText += " ";
      }

      // Decide if we'll make a mistake in this word
      let willMakeTypo =
        naturalMistakes && Math.random() < 0.15 && word.length > 3;
      let typoPosition = -1;
      let originalChar = "";
      let wrongChar = "";

      if (willMakeTypo) {
        // Choose a random position in the word for the typo
        typoPosition = Math.floor(Math.random() * (word.length - 1)) + 1;
        originalChar = word[typoPosition];

        // Common typo patterns: adjacent keyboard keys
        const keyboardNeighbors = {
          a: ["s", "q", "z"],
          b: ["v", "n", "g"],
          c: ["x", "v", "d"],
          d: ["s", "f", "e"],
          e: ["w", "r", "d"],
          f: ["d", "g", "r"],
          g: ["f", "h", "t"],
          h: ["g", "j", "y"],
          i: ["u", "o", "k"],
          j: ["h", "k", "u"],
          k: ["j", "l", "i"],
          l: ["k", "p", "o"],
          m: ["n", ",", "j"],
          n: ["b", "m", "h"],
          o: ["i", "p", "l"],
          p: ["o", "[", "l"],
          q: ["w", "a", "1"],
          r: ["e", "t", "f"],
          s: ["a", "d", "w"],
          t: ["r", "y", "g"],
          u: ["y", "i", "j"],
          v: ["c", "b", "g"],
          w: ["q", "e", "s"],
          x: ["z", "c", "s"],
          y: ["t", "u", "h"],
          z: ["a", "x", "s"],
        };

        // Get a similar character for the typo
        const lowerChar = originalChar.toLowerCase();
        if (keyboardNeighbors[lowerChar]) {
          const neighbors = keyboardNeighbors[lowerChar];
          wrongChar = neighbors[Math.floor(Math.random() * neighbors.length)];

          // Preserve capitalization
          if (originalChar !== lowerChar) {
            wrongChar = wrongChar.toUpperCase();
          }
        } else {
          // If no neighbors defined, just use a random character
          wrongChar = String.fromCharCode(97 + Math.floor(Math.random() * 26));
        }

        // Create the word with the typo
        wordToType =
          word.substring(0, typoPosition) +
          wrongChar +
          word.substring(typoPosition + 1);
      }

      // Type the word (possibly with typo)
      for (let i = 0; i < wordToType.length; i++) {
        const char = wordToType[i];

        // Variable delay between keystrokes
        let typingDelay;

        if (variableSpeed) {
          // Delay depends on character patterns
          if (i > 0 && isCommonBigram(wordToType[i - 1], char)) {
            // Common letter combinations are typed faster
            typingDelay = await randomDelay(
              baseDelayRange.min * 0.7,
              baseDelayRange.max * 0.6,
              "reaction"
            );
          } else if (/[\d.,;:\-_]/.test(char)) {
            // Special characters and digits are slower to type
            typingDelay = await randomDelay(
              baseDelayRange.min * 1.2,
              baseDelayRange.max * 1.3,
              "normal"
            );
          } else {
            typingDelay = await randomDelay(
              baseDelayRange.min,
              baseDelayRange.max,
              "normal"
            );
          }
        } else {
          typingDelay = await randomDelay(
            baseDelayRange.min,
            baseDelayRange.max,
            "normal"
          );
        }

        // Type the character
        await mcpClient.callMcpTool("evaluate", {
          script: `(selector, char) => {
            const element = document.querySelector(selector);
            if (!element) return false;
            element.value = (element.value || '') + char;
            element.dispatchEvent(new Event('input', { bubbles: true }));
            return true;
          }`,
          args: [selector, char],
          pageId,
        });

        typedText += char;
      }

      // If we made a typo, we need to fix it
      if (willMakeTypo) {
        // Notice the typo (pause)
        await randomDelay(500, 1200, "thinking");

        // Backspace to the typo
        const backspaceCount = wordToType.length - typoPosition;
        for (let i = 0; i < backspaceCount; i++) {
          await randomDelay(30, 100, "reaction");

          // Simulate backspace
          await mcpClient.callMcpTool("evaluate", {
            script: `(selector) => {
              const element = document.querySelector(selector);
              if (!element) return false;
              const val = element.value || '';
              if (val.length > 0) {
                element.value = val.substring(0, val.length - 1);
                element.dispatchEvent(new Event('input', { bubbles: true }));
              }
              return true;
            }`,
            args: [selector],
            pageId,
          });

          // Update our tracking of what's been typed
          typedText = typedText.substring(0, typedText.length - 1);
        }

        // Type the correct part of the word
        const correctPart = word.substring(typoPosition);
        for (const char of correctPart) {
          await randomDelay(baseDelayRange.min, baseDelayRange.max, "normal");

          // Type the correct character
          await mcpClient.callMcpTool("evaluate", {
            script: `(selector, char) => {
              const element = document.querySelector(selector);
              if (!element) return false;
              element.value = (element.value || '') + char;
              element.dispatchEvent(new Event('input', { bubbles: true }));
              return true;
            }`,
            args: [selector, char],
            pageId,
          });

          typedText += char;
        }
      }
    }

    // Dispatch change event at the end
    await mcpClient.callMcpTool("evaluate", {
      script: `(selector) => {
        const element = document.querySelector(selector);
        if (!element) return false;
        element.dispatchEvent(new Event('change', { bubbles: true }));
        return true;
      }`,
      args: [selector],
      pageId,
    });

    logger.debug(
      `[HumanBehavior] Successfully simulated enhanced human typing on page ${pageId}`
    );
    return true;
  } catch (error) {
    logger.error(
      `[HumanBehavior] Error simulating human typing: ${error.message}`,
      { stack: error.stack }
    );
    return false;
  }
}

/**
 * Checks if two characters form a common bigram in English
 * @param {string} char1 - First character
 * @param {string} char2 - Second character
 * @returns {boolean} - Whether this is a common bigram
 * @private
 */
function isCommonBigram(char1, char2) {
  // Most common English bigrams
  const commonBigrams = [
    "th",
    "he",
    "in",
    "en",
    "nt",
    "re",
    "er",
    "an",
    "ti",
    "es",
    "on",
    "at",
    "se",
    "nd",
    "or",
    "ar",
    "al",
    "te",
    "co",
    "de",
    "to",
    "ra",
    "et",
    "ed",
    "it",
    "sa",
    "em",
    "ro",
  ];

  const bigram = (char1 + char2).toLowerCase();
  return commonBigrams.includes(bigram);
}

/**
 * Simulates idle behavior (no input but looking at the page)
 * @param {Object} mcpClient - MCP client instance
 * @param {string} pageId - MCP page ID
 * @param {number} durationMs - How long to simulate idle behavior (default: 3-10 seconds)
 * @returns {Promise<boolean>} - Success status
 */
async function simulateIdleBehavior(mcpClient, pageId, durationMs = null) {
  if (!mcpClient || !pageId) {
    logger.warn(
      `[HumanBehavior] Missing parameters for idle behavior simulation`
    );
    return false;
  }

  try {
    // Calculate idle duration if not provided
    const idleDuration = durationMs || 3000 + Math.floor(Math.random() * 7000);
    logger.debug(
      `[HumanBehavior] Simulating idle behavior for ${idleDuration}ms on page ${pageId}`
    );

    // During this idle time, occasionally make small mouse movements
    const movementCount = Math.floor(idleDuration / 2000); // Approx one movement every 2 seconds

    // Get page dimensions
    const pageDimensions = await mcpClient.callMcpTool("evaluate", {
      script: `() => ({
        width: window.innerWidth || document.documentElement.clientWidth || 1200,
        height: window.innerHeight || document.documentElement.clientHeight || 800
      })`,
      pageId,
    });

    const { width = 1200, height = 800 } = pageDimensions || {};

    // Schedule the movements
    const promises = [];

    for (let i = 0; i < movementCount; i++) {
      const delay = Math.floor((i / movementCount) * idleDuration);

      promises.push(
        new Promise((resolve) => {
          setTimeout(async () => {
            try {
              // Small, subtle mouse movement (like fidgeting)
              const x = Math.floor(100 + Math.random() * (width - 200));
              const y = Math.floor(100 + Math.random() * (height - 200));

              await mcpClient.callMcpTool("evaluate", {
                script: `(x, y) => {
                const event = new MouseEvent('mousemove', {
                  bubbles: true,
                  cancelable: true,
                  clientX: x,
                  clientY: y,
                  screenX: x,
                  screenY: y + 50
                });
                document.dispatchEvent(event);
                return true;
              }`,
                args: [x, y],
                pageId,
              });
            } catch (e) {
              // Ignore errors during idle movements
            }
            resolve();
          }, delay);
        })
      );
    }

    // Wait for all movements to complete OR for the full duration
    await Promise.race([
      Promise.all(promises),
      new Promise((resolve) => setTimeout(resolve, idleDuration)),
    ]);

    logger.debug(
      `[HumanBehavior] Completed idle behavior simulation on page ${pageId}`
    );
    return true;
  } catch (error) {
    logger.error(
      `[HumanBehavior] Error simulating idle behavior: ${error.message}`,
      { stack: error.stack }
    );
    return false;
  }
}

/**
 * Detects and handles anti-bot challenges on a page
 * @param {Object} mcpClient - MCP client instance
 * @param {string} pageId - MCP page ID
 * @returns {Promise<Object>} - Result with detection information
 */
async function detectAndHandleChallenges(mcpClient, pageId) {
  if (!mcpClient || !pageId) {
    logger.warn(`[HumanBehavior] Missing parameters for challenge detection`);
    return { detected: false, handled: false, type: null };
  }

  try {
    logger.debug(`[HumanBehavior] Checking for challenges on page ${pageId}`);

    // Detect various types of anti-bot challenges
    const detectionResult = await mcpClient.callMcpTool("evaluate", {
      script: `() => {
        // Check for common CAPTCHA and challenge indicators
        const indicators = {
          // Google reCAPTCHA
          recaptcha: !!document.querySelector('.g-recaptcha, iframe[src*="recaptcha"], iframe[title*="recaptcha"]'),
          // hCaptcha
          hcaptcha: !!document.querySelector('iframe[src*="hcaptcha"], .h-captcha, iframe[title*="hCaptcha"]'),
          // Cloudflare
          cloudflare: !!document.querySelector('#cf-please-wait, .cf-browser-verification, iframe[src*="cloudflare"], #challenge-form'),
          // General CAPTCHA text
          captchaText: !!Array.from(document.querySelectorAll('h1, h2, h3, p, div, span')).find(el => 
            /captcha|bot check|human verification|verify you are human/i.test(el.textContent)),
          // DataDome
          datadome: !!document.querySelector('iframe[src*="datadome"]') || window.DataDome !== undefined,
          // Imperva
          imperva: !!document.querySelector('#waf-captcha-box') || !!document.querySelector('iframe[src*="incapsula"]'),
          // PerimeterX/HUMAN
          perimeterx: !!document.querySelector('iframe[src*="px-captcha"]') || window._pxAppId !== undefined
        };
        
        // Get challenge-related elements that might need interaction
        const interactElements = {
          // Checkboxes (like in reCAPTCHA)
          checkboxes: Array.from(document.querySelectorAll('input[type="checkbox"]')).filter(el => {
            const rect = el.getBoundingClientRect();
            return rect.width > 0 && rect.height > 0; // Only visible elements
          }),
          // Buttons with verification-related text
          verifyButtons: Array.from(document.querySelectorAll('button, input[type="button"], a.button, .btn')).filter(el => {
            const text = el.textContent.toLowerCase();
            return text.includes('verify') || text.includes('human') || 
                   text.includes('continue') || text.includes('confirm') ||
                   text.includes('submit') || text.includes('check');
          }),
          // Confirmation dialogs
          dialogs: document.querySelectorAll('.modal, .dialog, [role="dialog"], [aria-modal="true"]')
        };
        
        // Determine the detected challenge type
        let detectedType = null;
        for (const [type, detected] of Object.entries(indicators)) {
          if (detected) {
            detectedType = type;
            break;
          }
        }
        
        return {
          detected: detectedType !== null,
          type: detectedType,
          elements: interactElements
        };
      }`,
      pageId,
    });

    if (!detectionResult || !detectionResult.detected) {
      return { detected: false, handled: false, type: null };
    }

    logger.info(
      `[HumanBehavior] Detected ${detectionResult.type} challenge on page ${pageId}`
    );

    // Try to handle the challenge based on type
    let handled = false;

    // Allow the page to stabilize before attempting interactions
    await randomDelay(1000, 2000, "thinking");

    // Apply specialized handling based on the type
    if (detectionResult.type === "cloudflare") {
      // Cloudflare sometimes auto-proceeds, just wait
      await randomDelay(4000, 8000, "normal");

      // Try clicking any present verify buttons
      if (detectionResult.elements.verifyButtons.length > 0) {
        const buttonSelector = getElementSelector(
          detectionResult.elements.verifyButtons[0]
        );
        if (buttonSelector) {
          await mcpClient.callMcpTool("click", {
            selector: buttonSelector,
            pageId,
          });
          handled = true;
          logger.debug(
            `[HumanBehavior] Clicked Cloudflare verify button: ${buttonSelector}`
          );
        }
      }
    } else if (["recaptcha", "hcaptcha"].includes(detectionResult.type)) {
      // For CAPTCHA challenges with checkboxes
      if (detectionResult.elements.checkboxes.length > 0) {
        const checkboxSelector = getElementSelector(
          detectionResult.elements.checkboxes[0]
        );
        if (checkboxSelector) {
          // Click with human-like timing
          await randomDelay(1000, 2500, "thinking");
          await mcpClient.callMcpTool("click", {
            selector: checkboxSelector,
            pageId,
          });
          handled = true;
          logger.debug(
            `[HumanBehavior] Clicked CAPTCHA checkbox: ${checkboxSelector}`
          );

          // Wait to see if it resolves
          await randomDelay(3000, 5000, "normal");
        }
      }
    } else if (detectionResult.elements.verifyButtons.length > 0) {
      // Generic approach - try clicking the first verification button
      const buttonSelector = getElementSelector(
        detectionResult.elements.verifyButtons[0]
      );
      if (buttonSelector) {
        await randomDelay(800, 1500, "reaction");
        await mcpClient.callMcpTool("click", {
          selector: buttonSelector,
          pageId,
        });
        handled = true;
        logger.debug(
          `[HumanBehavior] Clicked verification button: ${buttonSelector}`
        );
      }
    }

    return {
      detected: true,
      handled,
      type: detectionResult.type,
    };
  } catch (error) {
    logger.error(
      `[HumanBehavior] Error detecting/handling challenges: ${error.message}`,
      { stack: error.stack }
    );
    return {
      detected: false,
      handled: false,
      type: null,
      error: error.message,
    };
  }
}

/**
 * Helper function to generate a CSS selector for an element
 * @param {Object} element - DOM element information
 * @returns {string|null} - CSS selector or null if couldn't be determined
 * @private
 */
function getElementSelector(element) {
  if (!element) return null;

  // Extract attributes from the element object
  const { id, className, tagName, type, name } = element;

  // Build selector based on available attributes
  if (id) {
    return `#${id}`;
  }

  let selector = tagName || "div";

  if (type) {
    selector += `[type="${type}"]`;
  }

  if (name) {
    selector += `[name="${name}"]`;
  }

  if (className && typeof className === "string") {
    // Convert space-separated classes to individual class selectors
    const classes = className.split(/\s+/).filter((c) => c.length > 0);
    if (classes.length > 0) {
      // Use at most 2 classes to avoid overly specific selectors
      const classSelector = classes
        .slice(0, 2)
        .map((c) => `.${c}`)
        .join("");
      selector += classSelector;
    }
  }

  return selector;
}

module.exports = {
  randomDelay,
  simulateHumanInteraction,
  simulateHumanTyping,
  simulateIdleBehavior,
  detectAndHandleChallenges,
};
