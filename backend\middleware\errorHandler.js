/**
 * Centralized error handling middleware
 * Provides consistent error responses across the application
 */
const ApiError = require("../utils/ApiError");
const logger = require("../utils/logger");

/**
 * Error handler middleware
 * @param {Error} err - Error object
 * @param {Request} req - Express request object
 * @param {Response} res - Express response object
 * @param {Function} next - Express next function
 */
const errorHandler = (err, req, res, next) => {
  // Log error details with structured logging
  logger.error({
    message: `Error: ${err.message}`,
    stack: err.stack,
    path: req.path,
    method: req.method,
    statusCode: err.statusCode || 500,
    requestId: req.id, // Assuming request ID middleware is used
    category: "error-handler",
    user: req.user ? req.user._id : "unauthenticated",
  });

  // Check if error is an ApiError instance
  if (err instanceof ApiError) {
    return res.status(err.statusCode).json({
      success: false,
      statusCode: err.statusCode,
      message: err.message,
      details: err.details,
      timestamp: err.timestamp,
    });
  }

  // Handle Mongoose validation errors
  if (err.name === "ValidationError") {
    const details = Object.values(err.errors).map((error) => ({
      field: error.path,
      message: error.message,
    }));

    return res.status(400).json({
      success: false,
      statusCode: 400,
      message: "Validation Error",
      details,
      timestamp: new Date().toISOString(),
    });
  }

  // Handle Mongoose cast errors (invalid IDs)
  if (err.name === "CastError") {
    return res.status(400).json({
      success: false,
      statusCode: 400,
      message: `Invalid ${err.path}: ${err.value}`,
      timestamp: new Date().toISOString(),
    });
  }

  // Handle JWT errors
  if (err.name === "JsonWebTokenError") {
    return res.status(401).json({
      success: false,
      statusCode: 401,
      message: "Invalid token",
      timestamp: new Date().toISOString(),
    });
  }

  if (err.name === "TokenExpiredError") {
    return res.status(401).json({
      success: false,
      statusCode: 401,
      message: "Token expired",
      timestamp: new Date().toISOString(),
    });
  }

  // Handle duplicate key errors
  if (err.code === 11000) {
    const field = Object.keys(err.keyValue)[0];
    return res.status(409).json({
      success: false,
      statusCode: 409,
      message: `Duplicate value for ${field}`,
      timestamp: new Date().toISOString(),
    });
  }

  // Default to 500 for unknown errors
  const statusCode = err.statusCode || 500;
  // Explicitly log 500 errors to console for better visibility during debugging
  if (statusCode === 500) {
    console.error("--- 500 Server Error Caught by Handler ---");
    console.error(err); // Log the full error object including stack
    console.error("--- End 500 Server Error ---");
  }
  res.status(statusCode).json({
    success: false,
    statusCode,
    message:
      process.env.NODE_ENV === "production"
        ? "Internal server error"
        : err.message,
    // Stack is included in console log now, keep it for non-production JSON response too
    stack: process.env.NODE_ENV === "production" ? null : err.stack,
    timestamp: new Date().toISOString(),
  });
};

module.exports = errorHandler;
