/**
 * Material Fetch Logger
 * Enhanced logging utility for tracking material price lookup flow
 */

const logger = require("./logger");

class MaterialFetchLogger {
  constructor() {
    this.activeLookups = new Map(); // Track active lookups with timing
  }

  /**
   * Log the start of a lookup
   */
  startLookup(quoteId, itemId, description, source = "unknown") {
    const key = `${quoteId}_${itemId}`;
    const startTime = Date.now();

    this.activeLookups.set(key, {
      quoteId,
      itemId,
      description,
      source,
      startTime,
      steps: [],
    });

    logger.info("[MaterialFetch] ▶️ LOOKUP STARTED", {
      quoteId,
      itemId,
      description: description?.substring(0, 100),
      source,
      timestamp: new Date().toISOString(),
    });
  }

  /**
   * Log a step in the lookup process
   */
  logStep(quoteId, itemId, step, data = {}) {
    const key = `${quoteId}_${itemId}`;
    const lookup = this.activeLookups.get(key);

    if (lookup) {
      const stepTime = Date.now() - lookup.startTime;
      lookup.steps.push({
        step,
        data,
        timestamp: new Date().toISOString(),
        elapsedMs: stepTime,
      });
    }

    logger.info(`[MaterialFetch] 📍 ${step}`, {
      quoteId,
      itemId,
      elapsedMs: lookup ? Date.now() - lookup.startTime : 0,
      ...data,
    });
  }

  /**
   * Log an error in the lookup process
   */
  logError(quoteId, itemId, error, context = {}) {
    const key = `${quoteId}_${itemId}`;
    const lookup = this.activeLookups.get(key);

    logger.error("[MaterialFetch] ❌ ERROR", {
      quoteId,
      itemId,
      error: error.message,
      stack: error.stack,
      elapsedMs: lookup ? Date.now() - lookup.startTime : 0,
      ...context,
    });

    if (lookup) {
      this.logStep(quoteId, itemId, "ERROR_OCCURRED", {
        error: error.message,
        context,
      });
    }
  }

  /**
   * Log completion of a lookup
   */
  completeLookup(quoteId, itemId, results = {}) {
    const key = `${quoteId}_${itemId}`;
    const lookup = this.activeLookups.get(key);

    if (lookup) {
      const totalTime = Date.now() - lookup.startTime;

      logger.info("[MaterialFetch] ✅ LOOKUP COMPLETED", {
        quoteId,
        itemId,
        totalTimeMs: totalTime,
        totalTimeSeconds: (totalTime / 1000).toFixed(2),
        stepCount: lookup.steps.length,
        results: {
          optionsFound: results.optionsCount || 0,
          status: results.status || "unknown",
          source: results.source || "unknown",
        },
      });

      // Log summary of steps if taking too long
      if (totalTime > 10000) {
        // More than 10 seconds
        logger.warn("[MaterialFetch] ⚠️ SLOW LOOKUP DETECTED", {
          quoteId,
          itemId,
          totalTimeMs: totalTime,
          steps: lookup.steps.map((s) => ({
            step: s.step,
            elapsedMs: s.elapsedMs,
          })),
        });
      }

      this.activeLookups.delete(key);
    }
  }

  /**
   * Log change stream event
   */
  logChangeStreamEvent(eventType, data = {}) {
    logger.info("[MaterialFetch] 🔄 CHANGE STREAM EVENT", {
      eventType,
      ...data,
    });
  }

  /**
   * Log change stream activity (alias for logChangeStreamEvent)
   */
  logChangeStreamActivity(action, data = {}) {
    logger.info("[MaterialFetch] 🔄 CHANGE STREAM ACTIVITY", {
      action,
      ...data,
    });
  }

  /**
   * Log poller activity
   */
  logPollerActivity(action, data = {}) {
    logger.info("[MaterialFetch] 🔁 POLLER ACTIVITY", {
      action,
      ...data,
    });
  }

  /**
   * Get active lookups summary
   */
  getActiveLookups() {
    const now = Date.now();
    const summary = [];

    for (const [key, lookup] of this.activeLookups.entries()) {
      summary.push({
        key,
        quoteId: lookup.quoteId,
        itemId: lookup.itemId,
        description: lookup.description?.substring(0, 50),
        elapsedSeconds: ((now - lookup.startTime) / 1000).toFixed(2),
        stepCount: lookup.steps.length,
        lastStep: lookup.steps[lookup.steps.length - 1]?.step || "none",
      });
    }

    return summary;
  }

  /**
   * Clean up stale lookups (older than 5 minutes)
   */
  cleanupStaleLookups() {
    const now = Date.now();
    const staleThreshold = 5 * 60 * 1000; // 5 minutes
    let cleaned = 0;

    for (const [key, lookup] of this.activeLookups.entries()) {
      if (now - lookup.startTime > staleThreshold) {
        logger.warn("[MaterialFetch] 🧹 CLEANING STALE LOOKUP", {
          key,
          quoteId: lookup.quoteId,
          itemId: lookup.itemId,
          ageMinutes: ((now - lookup.startTime) / 60000).toFixed(2),
        });
        this.activeLookups.delete(key);
        cleaned++;
      }
    }

    if (cleaned > 0) {
      logger.info(`[MaterialFetch] Cleaned ${cleaned} stale lookups`);
    }
  }
}

// Export singleton instance
module.exports = new MaterialFetchLogger();
