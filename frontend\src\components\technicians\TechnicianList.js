import React, { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import {
  Box,
  Typography,
  Paper,
  Container,
  Grid,
  CircularProgress,
  Alert,
  TextField,
  InputAdornment,
  IconButton,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Tooltip,
  Snackbar,
  Button,
} from "@mui/material";
import SearchIcon from "@mui/icons-material/Search";
import TuneIcon from "@mui/icons-material/Tune";
import SortIcon from "@mui/icons-material/Sort";
import SmartToyIcon from "@mui/icons-material/SmartToy";

// Import components
import TechnicianCard from "./TechnicianCard";
import FilterDialog from "./dialogs/FilterDialog";
import CreateEditDialog from "./dialogs/CreateEditDialog";
import DeleteDialog from "./dialogs/DeleteDialog";
import AISuggestionsDialog from "./dialogs/AISuggestionsDialog";
import AddTechnicianButton from "./AddTechnicianButton";

// Import helpers and actions
import { filterTechnicians, sortTechnicians } from "./utils/technicianHelpers";
import {
  getTechnicians,
  createTechnician,
  updateTechnician,
  deleteTechnician,
} from "../../slices/technicianSlice";

/**
 * Main component for displaying and managing technicians
 */
const TechnicianList = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();

  // Redux state
  const { technicians, loading, error } = useSelector(
    (state) => state.technicians
  );
  const { userInfo } = useSelector((state) => state.auth);

  // Local state
  const [searchTerm, setSearchTerm] = useState("");
  const [filterOpen, setFilterOpen] = useState(false);
  const [filters, setFilters] = useState({
    status: "all",
    contractorType: "all",
  });
  const [sortBy, setSortBy] = useState("name");

  // Dialog states
  const [createTechnicianOpen, setCreateTechnicianOpen] = useState(false);
  const [editTechnicianOpen, setEditTechnicianOpen] = useState(false);
  const [selectedTechnicianForEdit, setSelectedTechnicianForEdit] =
    useState(null);
  const [confirmDeleteOpen, setConfirmDeleteOpen] = useState(false);
  const [selectedTechnicianForDelete, setSelectedTechnicianForDelete] =
    useState(null);
  const [aiSuggestionsOpen, setAiSuggestionsOpen] = useState(false);
  const [validationError, setValidationError] = useState(false);

  // Snackbar state
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState("");
  const [snackbarSeverity, setSnackbarSeverity] = useState("success");

  // Form data for new/edit technician
  const [technicianFormData, setTechnicianFormData] = useState({
    firstName: "",
    lastName: "",
    email: "",
    phone: "",
    contractorType: "",
    hourlyRate: "",
    skills: [],
    services: [],
  });

  // AI suggestions state
  const [aiLoading, setAiLoading] = useState(false);
  const [aiPrompt, setAiPrompt] = useState("");
  const [aiSuggestions, setAiSuggestions] = useState(null);

  // Fetch technicians on component mount
  useEffect(() => {
    if (userInfo?.token) {
      dispatch(getTechnicians());
    }
  }, [dispatch, userInfo]);

  // Event handlers
  const handleSearchChange = (e) => {
    setSearchTerm(e.target.value);
  };

  const handleFilterToggle = () => {
    setFilterOpen(!filterOpen);
  };

  const handleFilterChange = (e) => {
    setFilters({
      ...filters,
      [e.target.name]: e.target.value,
    });
  };

  const handleResetFilters = () => {
    setFilters({
      status: "all",
      contractorType: "all",
    });
  };

  const handleSortChange = (e) => {
    setSortBy(e.target.value);
  };

  const handleTechnicianClick = (id) => {
    navigate(`/technicians/${id}`);
  };

  // Create/Edit dialog handlers
  const handleOpenCreateTechnician = () => {
    setTechnicianFormData({
      firstName: "",
      lastName: "",
      email: "",
      phone: "",
      contractorType: "",
      hourlyRate: "",
      skills: [],
      services: [],
    });
    setCreateTechnicianOpen(true);
    setValidationError(false);
  };

  const handleCloseCreateTechnician = () => {
    setCreateTechnicianOpen(false);
  };

  const handleEditTechnician = (technician) => {
    setSelectedTechnicianForEdit(technician);
    setTechnicianFormData({
      firstName: technician.firstName,
      lastName: technician.lastName,
      email: technician.email,
      phone: technician.phone || "",
      contractorType: technician.contractorType || "",
      hourlyRate: technician.hourlyRate || "",
      skills: technician.skills || [],
      services: technician.services || [],
    });
    setEditTechnicianOpen(true);
    setValidationError(false);
  };

  const handleCloseEditTechnician = () => {
    setEditTechnicianOpen(false);
    setSelectedTechnicianForEdit(null);
  };

  const handleFormChange = (e) => {
    const { name, value } = e.target;
    setTechnicianFormData({
      ...technicianFormData,
      [name]: value,
    });
  };

  const handleCreateTechnician = () => {
    // Check required fields
    if (
      !technicianFormData.firstName ||
      !technicianFormData.lastName ||
      !technicianFormData.email
    ) {
      setValidationError(true);
      return;
    }

    // Dispatch create action
    dispatch(createTechnician(technicianFormData))
      .unwrap()
      .then(() => {
        showSnackbar("Technician created successfully", "success");
        setCreateTechnicianOpen(false);
      })
      .catch((error) => {
        showSnackbar(error || "Failed to create technician", "error");
      });
  };

  const handleUpdateTechnician = () => {
    // Check required fields
    if (
      !technicianFormData.firstName ||
      !technicianFormData.lastName ||
      !technicianFormData.email
    ) {
      setValidationError(true);
      return;
    }

    // Dispatch update action
    dispatch(
      updateTechnician({
        id: selectedTechnicianForEdit._id,
        ...technicianFormData,
      })
    )
      .unwrap()
      .then(() => {
        showSnackbar("Technician updated successfully", "success");
        setEditTechnicianOpen(false);
        setSelectedTechnicianForEdit(null);
      })
      .catch((error) => {
        showSnackbar(error || "Failed to update technician", "error");
      });
  };

  // Delete dialog handlers
  const handleDeleteTechnician = (technician) => {
    setSelectedTechnicianForDelete(technician);
    setConfirmDeleteOpen(true);
  };

  const handleConfirmDelete = () => {
    // Dispatch delete action
    dispatch(deleteTechnician(selectedTechnicianForDelete._id))
      .unwrap()
      .then(() => {
        showSnackbar(
          `Technician ${selectedTechnicianForDelete.firstName} ${selectedTechnicianForDelete.lastName} deleted successfully`,
          "success"
        );
        setConfirmDeleteOpen(false);
        setSelectedTechnicianForDelete(null);
      })
      .catch((error) => {
        showSnackbar(error || "Failed to delete technician", "error");
      });
  };

  const handleCloseDeleteDialog = () => {
    setConfirmDeleteOpen(false);
    setSelectedTechnicianForDelete(null);
  };

  // AI suggestions dialog handlers
  const handleOpenAiSuggestions = (technician) => {
    setSelectedTechnicianForEdit(technician);
    setAiPrompt(
      `Suggest jobs and skills for ${technician.firstName} ${
        technician.lastName
      } who specializes in ${
        technician.contractorType || "general contracting"
      }`
    );
    setAiSuggestions(null);
    setAiSuggestionsOpen(true);
  };

  const handleCloseAiSuggestions = () => {
    setAiSuggestionsOpen(false);
    setSelectedTechnicianForEdit(null);
  };

  const handleAiPromptChange = (e) => {
    setAiPrompt(e.target.value);
  };

  const handleGenerateInsights = () => {
    setAiLoading(true);
    // Simulate AI response
    setTimeout(() => {
      setAiSuggestions({
        recommendedSkills: [
          "Electrical Wiring",
          "Circuit Diagnostics",
          "Panel Installation",
          "Lighting Systems",
          "Safety Compliance",
        ],
        recommendedJobs: [
          {
            id: "job1",
            title: "Home Rewiring",
            customer: "John Smith",
            urgency: "High",
          },
          {
            id: "job2",
            title: "Office Lighting Installation",
            customer: "Acme Corp",
            urgency: "Medium",
          },
        ],
      });
      setAiLoading(false);
    }, 1500);
  };

  const handleAssignAiSuggestedJob = (jobId) => {
    showSnackbar("Job assigned successfully", "success");
    setAiSuggestionsOpen(false);
  };

  // Function to handle assigning a job directly
  const handleAssignJob = (technician) => {
    navigate("/jobs/create", { state: { technicianId: technician._id } });
  };

  // Snackbar handlers
  const showSnackbar = (message, severity) => {
    setSnackbarMessage(message);
    setSnackbarSeverity(severity);
    setSnackbarOpen(true);
  };

  const handleCloseSnackbar = () => {
    setSnackbarOpen(false);
  };

  // Apply filters and sorting to technicians
  const filteredTechnicians = filterTechnicians(
    technicians,
    searchTerm,
    filters
  );
  const sortedTechnicians = sortTechnicians(filteredTechnicians, sortBy);

  return (
    <Container maxWidth="xl" sx={{ mt: 4, mb: 4 }}>
      <Grid container spacing={3}>
        <Grid item xs={12}>
          <Paper sx={{ p: 2, display: "flex", flexDirection: "column" }}>
            {/* Header and Actions */}
            <Box
              display="flex"
              justifyContent="space-between"
              alignItems="center"
              mb={3}
            >
              <Typography variant="h4" component="h1">
                Technicians
              </Typography>

              <Box>
                <Tooltip title="AI Workforce Optimization">
                  <Button
                    variant="outlined"
                    color="secondary"
                    startIcon={<SmartToyIcon />}
                    sx={{ mr: 1 }}
                    onClick={() => {
                      setAiPrompt(
                        "Suggest optimal technician allocation based on current workload"
                      );
                      setAiSuggestions(null);
                      setSelectedTechnicianForEdit(null);
                      setAiSuggestionsOpen(true);
                    }}
                  >
                    AI Optimize
                  </Button>
                </Tooltip>

                <AddTechnicianButton
                  onClick={handleOpenCreateTechnician}
                  userRole={userInfo?.role}
                />
              </Box>
            </Box>

            {/* Search and Sort */}
            <Box
              display="flex"
              justifyContent="space-between"
              alignItems="center"
              mb={3}
            >
              <TextField
                variant="outlined"
                placeholder="Search technicians..."
                value={searchTerm}
                onChange={handleSearchChange}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <SearchIcon />
                    </InputAdornment>
                  ),
                }}
                sx={{ minWidth: 300 }}
              />

              <Box>
                <Tooltip title="Filter Technicians">
                  <IconButton onClick={handleFilterToggle}>
                    <TuneIcon />
                  </IconButton>
                </Tooltip>

                <FormControl sx={{ minWidth: 120, ml: 2 }}>
                  <InputLabel id="sort-by-label">Sort By</InputLabel>
                  <Select
                    labelId="sort-by-label"
                    value={sortBy}
                    label="Sort By"
                    onChange={handleSortChange}
                    size="small"
                    startAdornment={
                      <InputAdornment position="start">
                        <SortIcon fontSize="small" />
                      </InputAdornment>
                    }
                  >
                    <MenuItem value="name">Name</MenuItem>
                    <MenuItem value="status">Status</MenuItem>
                    <MenuItem value="type">Type</MenuItem>
                  </Select>
                </FormControl>
              </Box>
            </Box>

            {/* Main content */}
            {loading ? (
              <Box display="flex" justifyContent="center" p={3}>
                <CircularProgress />
              </Box>
            ) : error ? (
              <Alert severity="error">{error}</Alert>
            ) : (
              <Grid container spacing={3}>
                {sortedTechnicians.length > 0 ? (
                  sortedTechnicians.map((technician) => (
                    <Grid
                      item
                      xs={12}
                      sm={6}
                      md={4}
                      lg={3}
                      key={technician._id}
                    >
                      <TechnicianCard
                        technician={technician}
                        onCardClick={handleTechnicianClick}
                        onEditClick={handleEditTechnician}
                        onDeleteClick={handleDeleteTechnician}
                        onAiSuggestionsClick={handleOpenAiSuggestions}
                        onAssignJobClick={handleAssignJob}
                        userRole={userInfo?.role}
                      />
                    </Grid>
                  ))
                ) : (
                  <Grid item xs={12}>
                    <Alert severity="info">
                      No technicians found matching your criteria.
                    </Alert>
                  </Grid>
                )}
              </Grid>
            )}
          </Paper>
        </Grid>
      </Grid>

      {/* Dialogs */}
      <FilterDialog
        open={filterOpen}
        onClose={handleFilterToggle}
        filters={filters}
        onFilterChange={handleFilterChange}
        onResetFilters={handleResetFilters}
      />

      <CreateEditDialog
        open={createTechnicianOpen || editTechnicianOpen}
        onClose={
          createTechnicianOpen
            ? handleCloseCreateTechnician
            : handleCloseEditTechnician
        }
        isCreateMode={createTechnicianOpen}
        formData={technicianFormData}
        onFormChange={handleFormChange}
        onSubmit={
          createTechnicianOpen ? handleCreateTechnician : handleUpdateTechnician
        }
        validationError={validationError}
      />

      <DeleteDialog
        open={confirmDeleteOpen}
        onClose={handleCloseDeleteDialog}
        onConfirm={handleConfirmDelete}
        technician={selectedTechnicianForDelete}
      />

      <AISuggestionsDialog
        open={aiSuggestionsOpen}
        onClose={handleCloseAiSuggestions}
        technician={selectedTechnicianForEdit}
        aiPrompt={aiPrompt}
        onAiPromptChange={handleAiPromptChange}
        onGenerateInsights={handleGenerateInsights}
        aiLoading={aiLoading}
        aiSuggestions={aiSuggestions}
        onAssignJob={handleAssignAiSuggestedJob}
      />

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbarOpen}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: "bottom", horizontal: "right" }}
      >
        <Alert
          onClose={handleCloseSnackbar}
          severity={snackbarSeverity}
          sx={{ width: "100%" }}
        >
          {snackbarMessage}
        </Alert>
      </Snackbar>
    </Container>
  );
};

export default TechnicianList;
