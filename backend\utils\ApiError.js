class ApiError extends Error {
  constructor(statusCode, message, details = null) {
    super(message);
    this.statusCode = statusCode;
    this.details = details;
    this.success = false;
    this.timestamp = new Date().toISOString();

    // Maintain proper stack trace
    Error.captureStackTrace(this, this.constructor);
  }

  static badRequest(message, details = null) {
    return new ApiError(400, message, details);
  }

  static unauthorized(message = "Unauthorized access") {
    return new ApiError(401, message);
  }

  static forbidden(message = "Forbidden access") {
    return new ApiError(403, message);
  }

  static notFound(message = "Resource not found") {
    return new ApiError(404, message);
  }

  static conflict(message, details = null) {
    return new ApiError(409, message, details);
  }

  static validation(message = "Validation error", details = null) {
    return new ApiError(422, message, details);
  }

  static internal(message = "Internal server error", details = null) {
    return new ApiError(500, message, details);
  }

  static serviceUnavailable(message = "Service unavailable", details = null) {
    return new ApiError(503, message, details);
  }

  toJSON() {
    return {
      success: this.success,
      statusCode: this.statusCode,
      message: this.message,
      details: this.details,
      timestamp: this.timestamp,
      ...(process.env.NODE_ENV === "development" && { stack: this.stack }),
    };
  }

  // Custom error types for invoice management
  static invalidInvoiceStatus(currentStatus, targetStatus) {
    return new ApiError(
      400,
      `Invalid status transition from ${currentStatus} to ${targetStatus}`,
      { currentStatus, targetStatus }
    );
  }

  static invoiceLocked() {
    return new ApiError(403, "Invoice is locked and cannot be modified", {
      reason: "Invoice has been finalized or paid",
    });
  }

  static insufficientFunds(available, required) {
    return new ApiError(400, "Insufficient funds for payment", {
      available,
      required,
      deficit: required - available,
    });
  }

  static duplicateInvoice(invoiceNumber) {
    return new ApiError(409, "Duplicate invoice number", { invoiceNumber });
  }

  static invalidPayment(reason, details = null) {
    return new ApiError(400, `Invalid payment: ${reason}`, details);
  }

  static aiServiceError(operation, error) {
    return new ApiError(503, "AI service unavailable", {
      operation,
      error: error.message,
      retry: true,
    });
  }

  static customerNotFound(customerId) {
    return new ApiError(404, "Customer not found", { customerId });
  }

  static jobNotFound(jobId) {
    return new ApiError(404, "Job not found", { jobId });
  }

  static invoiceNotFound(invoiceId) {
    return new ApiError(404, "Invoice not found", { invoiceId });
  }

  static paymentFailed(reason, transactionId = null) {
    return new ApiError(400, "Payment processing failed", {
      reason,
      transactionId,
    });
  }

  static taxCalculationError(reason, items = null) {
    return new ApiError(400, "Tax calculation error", { reason, items });
  }

  static attachmentError(reason, file = null) {
    return new ApiError(400, "Attachment error", { reason, file });
  }

  static invalidDate(field, value, reason) {
    return new ApiError(400, "Invalid date", { field, value, reason });
  }

  static invalidAmount(field, value, reason) {
    return new ApiError(400, "Invalid amount", { field, value, reason });
  }

  static voidError(reason, invoiceId) {
    return new ApiError(400, "Cannot void invoice", { reason, invoiceId });
  }
}

module.exports = ApiError;
