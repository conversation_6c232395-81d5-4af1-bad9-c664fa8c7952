#!/usr/bin/env node

require('dotenv').config();
const Redis = require('ioredis');

async function testRedisConnection() {
  console.log('Testing Redis connection...');
  console.log('REDIS_HOST:', process.env.REDIS_HOST || 'localhost (default)');
  console.log('REDIS_PORT:', process.env.REDIS_PORT || 6380);
  
  const redis = new Redis({
    host: process.env.REDIS_HOST || 'localhost',
    port: process.env.REDIS_PORT || 6380,
    password: process.env.REDIS_PASSWORD || null,
    maxRetriesPerRequest: 1,
    connectTimeout: 5000
  });

  try {
    const result = await redis.ping();
    console.log('✅ Redis connection successful:', result);
    
    // Test a simple set/get operation
    await redis.set('test_key', 'test_value');
    const value = await redis.get('test_key');
    console.log('✅ Redis set/get test successful:', value);
    
    await redis.del('test_key');
    
  } catch (error) {
    console.error('❌ Redis connection failed:', error.message);
  } finally {
    await redis.quit();
  }
}

testRedisConnection()
  .then(() => process.exit(0))
  .catch(error => {
    console.error('Test failed:', error);
    process.exit(1);
  });