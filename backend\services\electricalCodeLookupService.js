const logger = require("../utils/logger");
const geminiService = require("../utils/geminiService"); // Use internal Gemini service
const browserManager = require("../utils/browserManager"); // Use internal browser manager
const ApiError = require("../utils/ApiError"); // Import ApiError for consistency
const { NEC_LOOKUP_URLS } = require("../config/lookupConfig"); // Import URLs from config
const { circuitBreakers } = require("../utils/retryUtils"); // Import circuit breakers
const necTables = require("../utils/necTables"); // Import internal NEC table lookups
// p-limit will be imported dynamically below using import()
// Use the existing Google Search Service instead of Firecrawl
const googleSearchService = require("./googleSearchService");

// p-limit will be imported dynamically for rate limiting

// --- Helper Functions for Fallback ---

/**
 * Constructs a search query for lookup services based on lookup type and params.
 */
function constructSearchQuery(type, params) {
  logger.debug(
    `[Lookup Service CSQ Entry] Type: ${type} (typeof: ${typeof type}), Params: ${JSON.stringify(
      params
    )}`
  );
  let query = `NEC ${type.replace("NEC_", "").replace(/_/g, " ")}`;
  if (params.ampacity) query += ` ${params.ampacity}A`;
  if (params.material) query += ` ${params.material}`;
  if (params.temperature_rating) query += ` ${params.temperature_rating}C`;
  if (params.service_conductor_size)
    query += ` for ${params.service_conductor_size} service`;
  if (params.conductor_details) query += ` ${params.conductor_details}`; // For conduit fill
  // Add specific table numbers for clarity
  if (type === "NEC_conductor_size") query += " Table 310.16";
  logger.debug(
    `[Lookup Service CSQ Before Line 23] Type: ${type}, Query so far: "${query}"`
  );
  if (type === "NEC_grounding_electrode_conductor_size")
    query += " Table 250.66";
  if (type === "NEC_conduit_size") query += " Chapter 9 Table 4"; // Common table for conduit fill

  logger.debug(`[Lookup Service] Constructed search query: "${query}"`);
  return query;
}

/**
 * Parses scraped content using Gemini to extract the specific NEC value.
 * Uses the isolated 'geminiParsing' circuit breaker.
 */
async function parseScrapedContentWithLLM(scrapedContent, lookupType, params) {
  logger.info(
    `[Lookup Service] Attempting LLM parsing for type: ${lookupType}`
  );
  let specificInstruction = "";
  let expectedFormat = "the specific size or value";

  // Tailor instructions based on type
  if (lookupType === "NEC_conductor_size") {
    specificInstruction = `Find the smallest conductor size (AWG or kcmil) from NEC Table 310.16 (or equivalent) that meets or exceeds ${
      params.ampacity || "the required"
    }A ampacity for ${params.material || "the specified"} conductors at ${
      params.temperature_rating || 75
    }°C.`;
    expectedFormat = 'conductor size (e.g., "4/0 AWG", "500 kcmil")';
  } else if (lookupType === "NEC_grounding_electrode_conductor_size") {
    specificInstruction = `Find the required grounding electrode conductor size from NEC Table 250.66 (or equivalent) based on the service entrance conductor size of ${
      params.service_conductor_size || "unknown"
    } or ampacity ${params.ampacity || "unknown"}. Consider ${
      params.material || "copper"
    } for the GEC.`;
    expectedFormat = 'GEC size (e.g., "#6 AWG", "#4 AWG Copper")';
  } else if (lookupType === "NEC_conduit_size") {
    specificInstruction = `Determine the minimum trade size conduit (e.g., EMT, RMC) required to contain the following conductors: ${
      params.conductor_details || "specified conductors"
    }, according to NEC Chapter 9 tables (like Table 4).`;
    expectedFormat = 'conduit trade size (e.g., "1 inch", "2-1/2 inches")';
  } else {
    specificInstruction = `Find the relevant NEC specification for ${lookupType
      .replace("NEC_", "")
      .replace(/_/g, " ")} based on the parameters: ${JSON.stringify(params)}.`;
  }

  const systemPrompt = `You are an expert electrical code assistant. Analyze the provided web page content (scraped text) which discusses NEC requirements. ${specificInstruction} Extract ONLY ${expectedFormat} and respond in the following JSON format: {"value": "extracted_value"} or {"error": "Could not find the specific value in the provided text."}. Do not include explanations or apologies.`;

  try {
    const context = {
      lookupType: lookupType,
      parameters: params,
      scrapedContent: scrapedContent.substring(0, 15000), // Limit context size
    };
    // Use the ISOLATED circuit breaker for this potentially brittle parsing step
    const llmResponse = await circuitBreakers.geminiParsing.execute(() =>
      geminiService.getGeminiJsonResponse(systemPrompt, JSON.stringify(context))
    );

    if (llmResponse && llmResponse.value) {
      logger.info(
        `[Lookup Service] LLM parsing successful: "${llmResponse.value}"`
      );
      return { success: true, result: llmResponse.value.trim() };
    } else {
      const errorMsg = llmResponse?.error || "LLM could not extract value.";
      logger.warn(`[Lookup Service] LLM parsing failed: ${errorMsg}`);
      return { success: false, error: errorMsg };
    }
  } catch (error) {
    logger.error("[Lookup Service] Error during LLM parsing:", error);
    // Check if the error is from the circuit breaker being open
    const errorMessage = error.message.includes("Circuit breaker is OPEN")
      ? "LLM parsing skipped: Circuit breaker is open."
      : `LLM parsing process failed: ${error.message}`;
    return { success: false, error: errorMessage };
  }
}

/**
 * Attempts to use Gemini's Google Search Grounding feature to find the NEC value.
 * Uses the dedicated grounding function and model.
 */
async function attemptGoogleSearchGrounding(type, params) {
  logger.info(
    `[Lookup Service Grounding] Attempting Google Search Grounding for type: ${type}`
  );
  let specificInstruction = "";
  let expectedFormat = "the specific size or value";

  // Reuse logic from parseScrapedContentWithLLM to tailor instructions
  if (type === "NEC_conductor_size") {
    specificInstruction = `Find the smallest conductor size (AWG or kcmil) from NEC Table 310.16 (or equivalent) that meets or exceeds ${
      params.ampacity || "the required"
    }A ampacity for ${params.material || "copper"} conductors at ${
      params.temperature_rating || 75
    }°C.`;
    expectedFormat = 'conductor size (e.g., "4/0 AWG", "500 kcmil")';
  } else if (type === "NEC_grounding_electrode_conductor_size") {
    specificInstruction = `Find the required grounding electrode conductor size from NEC Table 250.66 (or equivalent) based on the service entrance conductor size of ${
      params.service_conductor_size || "unknown"
    } or ampacity ${params.ampacity || "unknown"}. Consider ${
      params.material || "copper"
    } for the GEC.`;
    expectedFormat = 'GEC size (e.g., "#6 AWG", "#4 AWG Copper")';
  } else if (type === "NEC_conduit_size") {
    specificInstruction = `Determine the minimum trade size conduit (e.g., EMT, RMC) required to contain the following conductors: ${
      params.conductor_details || "specified conductors"
    }, according to NEC Chapter 9 tables (like Table 4).`;
    expectedFormat = 'conduit trade size (e.g., "1 inch", "2-1/2 inches")';
  } else {
    specificInstruction = `Find the relevant NEC specification for ${type
      .replace("NEC_", "")
      .replace(/_/g, " ")} based on the parameters: ${JSON.stringify(params)}.`;
  }

  // Construct the prompt for grounding
  const systemPrompt = `You are an expert electrical code assistant. Use Google Search to answer the user's query accurately. ${specificInstruction} Provide ONLY ${expectedFormat}. Do not include explanations, apologies, or any text other than the direct answer. If you cannot find the exact value, respond with "NOT_FOUND".`;
  const userPrompt = `What is the required ${expectedFormat} based on these parameters: ${JSON.stringify(
    params
  )}?`;

  try {
    // Call the dedicated grounding function in geminiService
    logger.debug(
      `[Lookup Service AGS Before Call] SystemPrompt: ${systemPrompt}, UserPrompt: ${userPrompt}`
    );
    const groundingResponseResult =
      await geminiService.getGroundedGeminiResponse(
        systemPrompt,
        userPrompt
        // Options like temperature can be passed here if needed: , { temperature: 0.2 }
      );

    // Check if grounding actually happened and extract the text
    let groundedText = null;
    logger.debug(
      `[Lookup Service AGS] Raw groundingResponseResult: ${JSON.stringify(
        groundingResponseResult
      )}`
    );

    if (typeof groundingResponseResult === "string") {
      groundedText = groundingResponseResult.trim();
      logger.info(
        "[Lookup Service Grounding] Grounding response was a direct string."
      );
    } else if (
      groundingResponseResult &&
      typeof groundingResponseResult.response === "object" &&
      groundingResponseResult.response !== null && // Ensure response object is not null
      typeof groundingResponseResult.response.text === "function"
    ) {
      try {
        groundedText = groundingResponseResult.response.text()?.trim();
        if (groundingResponseResult.response.groundingMetadata) {
          logger.debug(
            "[Lookup Service Grounding] Grounding metadata found in structured response."
          );
        } else {
          logger.warn(
            "[Lookup Service Grounding] Grounding tool was requested, but no groundingMetadata found in structured response."
          );
        }
      } catch (e) {
        logger.error(
          `[Lookup Service Grounding] Error extracting text from structured response: ${e.message}`
        );
        return {
          success: false,
          error: "Error processing structured grounding response.",
        };
      }
    } else {
      logger.error(
        `[Lookup Service Grounding] Invalid or unexpected response structure received from geminiService. Response: ${JSON.stringify(
          groundingResponseResult
        )}`
      );
      return {
        success: false,
        error: "Invalid or unexpected response structure from grounding call.",
      };
    }

    if (
      groundedText &&
      groundedText !== "NOT_FOUND" &&
      groundedText.length > 0
    ) {
      logger.info(
        `[Lookup Service Grounding] Grounding successful: "${groundedText}"`
      );
      const cleanedResult = groundedText
        .replace(/^["']|["']$/g, "")
        .replace(/^The required size is /i, "")
        .trim();
      return { success: true, result: cleanedResult };
    } else {
      const errorMsg =
        groundedText === "NOT_FOUND"
          ? "Value not found via grounding."
          : "Empty or invalid response from grounding.";
      logger.warn(`[Lookup Service Grounding] Grounding failed: ${errorMsg}`);
      return { success: false, error: errorMsg };
    }
  } catch (error) {
    logger.error(
      "[Lookup Service Grounding] Error during grounding call:",
      error
    );
    const errorMessage =
      error instanceof Error ? error.message : "Unknown grounding error";
    // Check if the error is from the circuit breaker being open
    if (errorMessage.includes("Circuit breaker is OPEN")) {
      return {
        success: false,
        error: "Grounding skipped: Circuit breaker is open.",
      };
    }
    return { success: false, error: `Grounding call failed: ${errorMessage}` };
  }
}

/**
 * Attempts to look up electrical code specifications using a multi-stage fallback:
 * 1. Internal Table Lookup (`necTables.js`).
 * 2. Direct DOM parsing (if applicable/enabled and not covered by internal tables).
 * 3. Google Search Grounding (using compatible model and tool).
 * 4. Firecrawl search/scrape/LLM parse (rate-limited, using isolated breaker).
 * @param {object} lookupRequest - The parsed lookup request object from the AI.
 * @returns {Promise<object>} - Success: { success: true, result: "Specific value" }, Failure: { success: false, error: "Reason", needsManualReview: true }
 */
async function lookupMaterialSpecification(lookupRequest) {
  logger.info(
    `[Lookup Service] Received request: ${JSON.stringify(lookupRequest)}`
  );
  const { type, params } = lookupRequest;
  let lastError = "Lookup not attempted";
  let needsManualReview = false; // Flag to indicate if final failure requires review

  // --- Step 1: Internal Table Lookup ---
  try {
    logger.debug(
      `[Lookup Service] Attempting internal table lookup for type: ${type}`
    );
    let internalResult = null;
    if (type === "NEC_conductor_size" && params.ampacity) {
      internalResult = necTables.findMinConductorSize(
        params.ampacity,
        params.material,
        params.temperature_rating
      );
    } else if (
      type === "NEC_grounding_electrode_conductor_size" &&
      params.service_conductor_size
    ) {
      internalResult = necTables.findGECSize(
        params.service_conductor_size,
        params.material
      );
    } else if (type === "NEC_conduit_size" && params.ampacity) {
      internalResult = necTables.findServiceEntranceConduitSize(
        params.ampacity,
        params.service_conductor_size
      );
    }
    // Add more internal table lookups here

    if (internalResult) {
      logger.info(
        `[Lookup Service] Internal table lookup successful: "${internalResult}"`
      );
      return { success: true, result: internalResult }; // Success!
    } else {
      logger.debug(
        `[Lookup Service] Internal table lookup did not yield a result for type: ${type}.`
      );
      lastError = "Internal table lookup failed or not applicable.";
    }
  } catch (tableError) {
    logger.error(
      `[Lookup Service] Error during internal table lookup for type ${type}:`,
      tableError
    );
    lastError = `Internal table lookup error: ${tableError.message}`;
  }

  // --- Step 2: Direct Scraping (If Applicable) ---
  const targetUrl = NEC_LOOKUP_URLS[type];
  const skipDirectLookup =
    type === "NEC_conductor_size" || // Skip as covered by internal table
    type === "NEC_grounding_electrode_conductor_size" || // Skip as covered by internal table
    type === "NEC_conduit_size"; // Always skip for now (unreliable)

  if (targetUrl && !skipDirectLookup) {
    logger.debug(
      `[Lookup Service] Attempting direct lookup for type: ${type} at URL: ${targetUrl}`
    );
    // ... (Direct scraping logic - currently bypassed) ...
    logger.warn(
      `[Lookup Service] Direct scraping logic for ${type} is currently bypassed.`
    );
    lastError = `Skipped direct lookup for ${type}`;
  } else if (skipDirectLookup) {
    logger.debug(`[Lookup Service] Skipping direct lookup for ${type}.`);
    if (
      lastError === "Lookup not attempted" ||
      lastError === "Internal table lookup failed or not applicable."
    ) {
      lastError = `Skipped direct lookup for ${type}`;
    }
  } else {
    logger.debug(
      `[Lookup Service] No target URL configured for direct lookup type: ${type}.`
    );
    if (
      lastError === "Lookup not attempted" ||
      lastError === "Internal table lookup failed or not applicable."
    ) {
      lastError = `No direct URL for type ${type}`;
    }
  }

  // --- Step 3: Google Search Grounding ---
  logger.info(
    `[Lookup Service] Internal/Direct lookup failed/skipped. Attempting Fallback 1: Google Search Grounding...`
  );
  let groundingResult = { success: false, error: "Grounding not attempted." };
  try {
    groundingResult = await attemptGoogleSearchGrounding(type, params);
    if (groundingResult.success) {
      logger.info(
        `[Lookup Service] Google Search Grounding successful: "${groundingResult.result}"`
      );
      return groundingResult; // Success!
    } else {
      logger.warn(
        `[Lookup Service] Google Search Grounding failed: ${groundingResult.error}. Proceeding to Fallback 2.`
      );
      lastError = groundingResult.error; // Update last error
    }
  } catch (groundingError) {
    // Should be caught within attemptGoogleSearchGrounding, but handle defensively
    logger.error(
      "[Lookup Service] Unexpected error during Google Search Grounding attempt:",
      groundingError
    );
    lastError = `Google Search Grounding process failed: ${groundingError.message}`;
  }

  // --- Step 4: Google Search API + LLM Parse (Rate-Limited & Isolated Breaker) ---
  logger.warn(
    `[Lookup Service] Grounding fallback failed. Initiating Fallback 2: Google Search API... Last error: ${lastError}`
  );

  if (
    !process.env.GOOGLE_CUSTOM_SEARCH_API_KEY ||
    !process.env.GOOGLE_CUSTOM_SEARCH_CX
  ) {
    logger.error(
      "[Lookup Service Fallback] GOOGLE_CUSTOM_SEARCH_API_KEY or GOOGLE_CUSTOM_SEARCH_CX environment variable not set."
    );
    return {
      success: false,
      error: "Google Custom Search API not configured.",
      needsManualReview: true,
    };
  }

  try {
    const pLimit = (await import("p-limit")).default;
    const googleSearchLimit = pLimit(1);

    const googleSearchResult = await googleSearchLimit(async () => {
      logger.info(
        "[Lookup Service Fallback] Executing Google Search API logic within rate limit."
      );
      const searchQuery = constructSearchQuery(type, params);
      logger.info(
        `[Lookup Service Fallback] Performing Google Search: "${searchQuery}"`
      );

      // Use the existing googleSearchService for the search
      const searchResults = await googleSearchService.searchWebFallback(
        searchQuery
      );

      if (
        !searchResults ||
        !Array.isArray(searchResults) ||
        searchResults.length === 0
      ) {
        logger.warn(
          "[Lookup Service Fallback] Google Search returned no results or invalid data."
        );
        return {
          internalSuccess: false,
          error: "Fallback search yielded no results or invalid data.",
        };
      }

      // Process up to 3 results to find the information we need
      const processLimit = Math.min(searchResults.length, 3);
      for (let i = 0; i < processLimit; i++) {
        const result = searchResults[i];
        try {
          // Use browserManager to get the page content as markdown
          logger.debug(
            `[Lookup Service Fallback] Attempting to fetch content from: ${result.link}`
          );
          // Get a page directly instead of getting browser first
          const page = await browserManager.getPage();
          await page.goto(result.link, {
            timeout: 15000,
            waitUntil: "domcontentloaded",
          });
          const content = await page.content();
          await page.close();

          // Convert HTML to markdown-like format for the LLM
          const textContent = await page.evaluate(() => {
            return document.body.innerText;
          });

          if (textContent) {
            logger.debug(
              `[Lookup Service Fallback] Attempting LLM parsing on content from: ${result.link}`
            );
            const llmParseResult = await parseScrapedContentWithLLM(
              textContent,
              type,
              params
            ); // Uses isolated breaker
            if (llmParseResult.success) {
              logger.info(
                `[Lookup Service Fallback] Successfully extracted value via fallback: ${llmParseResult.result}`
              );
              return { internalSuccess: true, result: llmParseResult.result };
            } else {
              logger.warn(
                `[Lookup Service Fallback] LLM parsing failed for ${result.link}: ${llmParseResult.error}`
              );
              lastError = `LLM parsing failed: ${llmParseResult.error}`;
            }
          } else {
            logger.warn(
              `[Lookup Service Fallback] No text content fetched for result: ${result.link}`
            );
          }
        } catch (fetchError) {
          logger.warn(
            `[Lookup Service Fallback] Error fetching content from ${result.link}: ${fetchError.message}`
          );
          // Continue to the next result
        }
      }

      logger.error(
        "[Lookup Service Fallback] Google Search fallback failed: Could not extract value from any search result."
      );
      return {
        internalSuccess: false,
        error: "Fallback failed: Could not extract value from search results.",
      };
    });

    if (googleSearchResult.internalSuccess) {
      return { success: true, result: googleSearchResult.result }; // Final success
    } else {
      lastError = googleSearchResult.error;
      needsManualReview = true;
    }
  } catch (googleSearchError) {
    logger.error(
      "[Lookup Service Fallback] Error during Google Search fallback process:",
      googleSearchError
    );
    lastError = `Fallback process failed: ${googleSearchError.message}`;
    needsManualReview = true;
  }

  // If we reach here, all attempts failed
  logger.error(
    `[Lookup Service] All lookup attempts failed for type ${type} with params ${JSON.stringify(
      params
    )}. Last error: ${lastError}`
  );
  return {
    success: false,
    error: `All lookup attempts failed. Last error: ${lastError}`,
    needsManualReview: true,
  };
}

module.exports = {
  lookupMaterialSpecification,
};
