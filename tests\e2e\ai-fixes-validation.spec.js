/**
 * AI Fixes Validation Tests
 * Comprehensive tests to validate all AI-related fixes and prevent regressions
 */

const { test, expect } = require('@playwright/test');

test.describe('AI Fixes Validation', () => {
  let authToken;
  let page;
  
  const testUser = {
    email: '<EMAIL>',
    password: 'password123'
  };

  test.beforeAll(async ({ request }) => {
    // Login to get auth token
    const response = await request.post('http://localhost:5000/api/users/login', {
      data: testUser
    });
    
    expect(response.status()).toBe(200);
    const responseData = await response.json();
    authToken = responseData.token;
  });

  test.beforeEach(async ({ browser }) => {
    page = await browser.newPage();
    // Set auth token in localStorage
    await page.goto('http://localhost:3000');
    await page.evaluate((token) => {
      localStorage.setItem('token', token);
    }, authToken);
  });

  test.afterEach(async () => {
    await page.close();
  });

  test.describe('Streaming AI Fixes', () => {
    test('should handle session not found (404) errors gracefully', async ({ request }) => {
      // Test the fix for session race conditions
      const response = await request.get('http://localhost:5000/api/streaming/connect/invalid-session-id', {
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Accept': 'text/event-stream'
        }
      });

      expect(response.status()).toBe(404);
      const responseData = await response.json();
      expect(responseData.error).toContain('Session not found');
    });

    test('should include type field in streaming messages', async ({ request }) => {
      // Create a streaming session first
      const sessionResponse = await request.post('http://localhost:5000/api/streaming/create-session', {
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json'
        },
        data: {
          inputType: 'materials',
          textInputData: [{ name: 'Test Material', quantity: 1 }],
          context: { jobType: 'electrical' }
        }
      });

      expect(sessionResponse.status()).toBe(200);
      const sessionData = await sessionResponse.json();
      const sessionId = sessionData.sessionId;

      // Connect to streaming endpoint
      const streamResponse = await request.get(`http://localhost:5000/api/streaming/connect/${sessionId}`, {
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Accept': 'text/event-stream'
        }
      });

      expect(streamResponse.status()).toBe(200);
      
      // Verify that messages include type field (this would be tested in integration)
      // For now, we just verify the connection is successful
    });

    test('should handle timeout errors with 60s timeout', async () => {
      await page.goto('http://localhost:3000/create-quote');
      
      // Fill in basic form data
      await page.fill('[data-testid="customer-name"]', 'Test Customer');
      await page.fill('[data-testid="project-overview"]', 'Test project for timeout validation');
      
      // Mock a slow AI response to test timeout handling
      await page.route('**/api/ai/**', async (route) => {
        // Delay response by 65 seconds to test timeout
        await new Promise(resolve => setTimeout(resolve, 65000));
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({ success: true, data: 'test' })
        });
      });
      
      // Start AI generation
      await page.click('[data-testid="generate-ai-quote"]');
      
      // Should show timeout error after 60 seconds
      await expect(page.locator('.error-message')).toContainText('timeout', { timeout: 65000 });
    });
  });

  test.describe('Clarification Dialog Fixes', () => {
    test('should display message when no questions are available', async () => {
      await page.goto('http://localhost:3000/create-quote');
      
      // Mock AI response with no questions
      await page.route('**/api/ai/**', async (route) => {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            success: true,
            requiresClarification: true,
            questions: [] // Empty questions array
          })
        });
      });
      
      // Fill form and trigger AI generation
      await page.fill('[data-testid="customer-name"]', 'Test Customer');
      await page.fill('[data-testid="project-overview"]', 'Test project');
      await page.click('[data-testid="generate-ai-quote"]');
      
      // Wait for clarification dialog
      await expect(page.locator('[data-testid="clarification-dialog"]')).toBeVisible();
      
      // Should show "No clarification questions available" message
      await expect(page.locator('.clarification-dialog')).toContainText('No clarification questions are available');
    });

    test('should handle different modes in clarification dialog', async () => {
      await page.goto('http://localhost:3000/create-quote');
      
      // Test streaming mode
      await page.route('**/api/streaming/**', async (route) => {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            success: true,
            sessionId: 'test-session-id',
            questions: []
          })
        });
      });
      
      // Fill form and trigger streaming AI generation
      await page.fill('[data-testid="customer-name"]', 'Test Customer');
      await page.fill('[data-testid="project-overview"]', 'Test project');
      await page.click('[data-testid="generate-streaming-quote"]');
      
      // Should show streaming-specific message
      await expect(page.locator('.clarification-dialog')).toContainText('AI streaming service will continue processing');
    });
  });

  test.describe('Materials Validation Fixes', () => {
    test('should handle materials with missing names gracefully', async ({ request }) => {
      const materialsData = {
        inputType: 'materials',
        textInputData: [
          { name: 'Valid Material', quantity: 1 },
          { name: null, description: 'Material with null name', quantity: 1 },
          { name: '', description: 'Material with empty name', quantity: 1 },
          { description: 'Material with no name property', quantity: 1 }
        ],
        context: { jobType: 'electrical' }
      };

      const response = await request.post('http://localhost:5000/api/ai/generate-quote', {
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json'
        },
        data: materialsData
      });

      // Should not throw validation error, should filter out invalid items
      expect(response.status()).toBe(200);
      const responseData = await response.json();
      expect(responseData.success).toBe(true);
    });

    test('should validate materials list in frontend form', async () => {
      await page.goto('http://localhost:3000/create-quote');
      
      // Add materials with invalid names
      await page.click('[data-testid="add-material"]');
      await page.fill('[data-testid="material-name-0"]', ''); // Empty name
      await page.fill('[data-testid="material-description-0"]', 'Test description');
      
      await page.click('[data-testid="add-material"]');
      await page.fill('[data-testid="material-name-1"]', 'Valid Material');
      
      // Fill other required fields
      await page.fill('[data-testid="customer-name"]', 'Test Customer');
      
      // Try to generate quote
      await page.click('[data-testid="generate-ai-quote"]');
      
      // Should not show the old validation error
      await expect(page.locator('.error-message')).not.toContainText('Please ensure your materials list contains items with valid names');
    });
  });

  test.describe('Session Management', () => {
    test('should handle duplicate session stats checks', async ({ request }) => {
      // Create a session
      const sessionResponse = await request.post('http://localhost:5000/api/streaming/create-session', {
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json'
        },
        data: {
          inputType: 'materials',
          textInputData: [{ name: 'Test Material', quantity: 1 }],
          context: { jobType: 'electrical' }
        }
      });

      expect(sessionResponse.status()).toBe(200);
      const sessionData = await sessionResponse.json();
      const sessionId = sessionData.sessionId;

      // Make multiple concurrent requests to test race condition handling
      const requests = Array(5).fill().map(() => 
        request.get(`http://localhost:5000/api/streaming/session-stats/${sessionId}`, {
          headers: {
            'Authorization': `Bearer ${authToken}`
          }
        })
      );

      const responses = await Promise.all(requests);
      
      // All requests should succeed without race condition errors
      responses.forEach(response => {
        expect(response.status()).toBe(200);
      });
    });
  });

  test.describe('Error Handling', () => {
    test('should handle network errors gracefully', async () => {
      await page.goto('http://localhost:3000/create-quote');
      
      // Mock network failure
      await page.route('**/api/ai/**', async (route) => {
        await route.abort('failed');
      });
      
      // Fill form and trigger AI generation
      await page.fill('[data-testid="customer-name"]', 'Test Customer');
      await page.fill('[data-testid="project-overview"]', 'Test project');
      await page.click('[data-testid="generate-ai-quote"]');
      
      // Should show network error message
      await expect(page.locator('.error-message')).toBeVisible();
      await expect(page.locator('.error-message')).toContainText('network');
    });

    test('should handle server errors gracefully', async () => {
      await page.goto('http://localhost:3000/create-quote');
      
      // Mock server error
      await page.route('**/api/ai/**', async (route) => {
        await route.fulfill({
          status: 500,
          contentType: 'application/json',
          body: JSON.stringify({ error: 'Internal server error' })
        });
      });
      
      // Fill form and trigger AI generation
      await page.fill('[data-testid="customer-name"]', 'Test Customer');
      await page.fill('[data-testid="project-overview"]', 'Test project');
      await page.click('[data-testid="generate-ai-quote"]');
      
      // Should show server error message
      await expect(page.locator('.error-message')).toBeVisible();
      await expect(page.locator('.error-message')).toContainText('server error');
    });
  });

  test.describe('Integration Tests', () => {
    test('should complete full AI quote generation workflow', async () => {
      await page.goto('http://localhost:3000/create-quote');
      
      // Fill complete form
      await page.fill('[data-testid="customer-name"]', 'Integration Test Customer');
      await page.fill('[data-testid="customer-email"]', '<EMAIL>');
      await page.fill('[data-testid="project-overview"]', 'Complete electrical system upgrade for commercial building');
      
      // Add materials
      await page.click('[data-testid="add-material"]');
      await page.fill('[data-testid="material-name-0"]', '200A Main Panel');
      await page.fill('[data-testid="material-quantity-0"]', '1');
      
      // Generate quote
      await page.click('[data-testid="generate-ai-quote"]');
      
      // Should show progress indicator
      await expect(page.locator('[data-testid="ai-progress"]')).toBeVisible();
      
      // Should complete successfully
      await expect(page.locator('[data-testid="quote-result"]')).toBeVisible({ timeout: 60000 });
      
      // Should display generated quote
      await expect(page.locator('[data-testid="generated-quote"]')).toContainText('200A Main Panel');
    });

    test('should handle streaming AI workflow end-to-end', async () => {
      await page.goto('http://localhost:3000/create-quote');
      
      // Fill form for streaming
      await page.fill('[data-testid="customer-name"]', 'Streaming Test Customer');
      await page.fill('[data-testid="project-overview"]', 'Streaming AI test project');
      
      // Enable streaming mode
      await page.check('[data-testid="enable-streaming"]');
      
      // Generate streaming quote
      await page.click('[data-testid="generate-streaming-quote"]');
      
      // Should show streaming progress
      await expect(page.locator('[data-testid="streaming-progress"]')).toBeVisible();
      
      // Should show real-time updates
      await expect(page.locator('[data-testid="streaming-status"]')).toContainText('Processing');
      
      // Should complete successfully
      await expect(page.locator('[data-testid="streaming-complete"]')).toBeVisible({ timeout: 120000 });
    });
  });
});