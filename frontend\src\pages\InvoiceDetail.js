import React, { useState, useEffect } from "react";
import {
  Container,
  Typography,
  Box,
  Paper,
  Grid,
  Divider,
  Button,
  TextField,
  CircularProgress,
  Alert,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  Breadcrumbs,
  Link,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
} from "@mui/material";
import {
  ArrowBack as ArrowBackIcon,
  Edit as EditIcon,
  Print as PrintIcon,
  Email as EmailIcon,
  AttachMoney as PaymentIcon,
  Schedule as ScheduleIcon,
  Receipt as ReceiptIcon,
  Person as PersonIcon,
  Business as BusinessIcon,
  HomeWork as JobIcon,
  Notes as NotesIcon,
} from "@mui/icons-material";
import { useParams, useNavigate } from "react-router-dom";
import { useSelector, useDispatch } from "react-redux";
import {
  getInvoiceById,
  sendInvoice,
  markInvoiceAsPaid,
  generateInvoicePdf,
} from "../slices/invoiceSlice";
import { enqueueSnackbar } from "../slices/snackbarSlice";

const InvoiceDetail = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const dispatch = useDispatch();

  // We need auth state for authorization in other functions, even if not directly used
  useSelector((state) => state.auth);
  const { invoice, loading, error } = useSelector((state) => state.invoices);

  const [sendingEmail, setSendingEmail] = useState(false);
  const [emailDialogOpen, setEmailDialogOpen] = useState(false);
  const [paymentDialogOpen, setPaymentDialogOpen] = useState(false);
  const [emailAddress, setEmailAddress] = useState("");
  const [paymentAmount, setPaymentAmount] = useState("");
  const [paymentMethod, setPaymentMethod] = useState("CREDIT_CARD");
  const [paymentNote, setPaymentNote] = useState("");
  const [paymentDate, setPaymentDate] = useState(
    new Date().toISOString().split("T")[0]
  );

  useEffect(() => {
    // Fetch invoice data from API
    dispatch(getInvoiceById(id));
  }, [id, dispatch]);

  // Set email address when invoice data is loaded
  useEffect(() => {
    if (invoice && invoice.customer && invoice.customer.email) {
      setEmailAddress(invoice.customer.email);
    }
  }, [invoice]);

  // Handle email sending
  const handleSendEmail = async () => {
    try {
      setSendingEmail(true);

      const emailData = {
        to: emailAddress,
        subject: `Invoice ${invoice.number}`,
        message: `Please find attached your invoice ${
          invoice.number
        } due on ${formatDate(invoice.dueDate)}.`,
      };

      await dispatch(sendInvoice({ id: invoice._id, emailData })).unwrap();

      dispatch(
        enqueueSnackbar({
          message: "Invoice sent successfully",
          severity: "success",
        })
      );

      setSendingEmail(false);
      setEmailDialogOpen(false);

      // Refresh invoice data to get updated history
      dispatch(getInvoiceById(id));
    } catch (err) {
      dispatch(
        enqueueSnackbar({
          message: `Failed to send email: ${err}`,
          severity: "error",
        })
      );
      setSendingEmail(false);
    }
  };

  // Handle payment recording
  const handleRecordPayment = async () => {
    try {
      if (
        !paymentAmount ||
        isNaN(parseFloat(paymentAmount)) ||
        parseFloat(paymentAmount) <= 0
      ) {
        dispatch(
          enqueueSnackbar({
            message: "Please enter a valid payment amount",
            severity: "error",
          })
        );
        return;
      }

      const paymentData = {
        amount: parseFloat(paymentAmount),
        method: paymentMethod,
        date: paymentDate,
        notes: paymentNote || "Payment recorded manually",
      };

      await dispatch(
        markInvoiceAsPaid({ id: invoice._id, paymentData })
      ).unwrap();

      dispatch(
        enqueueSnackbar({
          message: "Payment recorded successfully",
          severity: "success",
        })
      );

      setPaymentDialogOpen(false);
      setPaymentAmount("");
      setPaymentMethod("CREDIT_CARD");
      setPaymentNote("");

      // Refresh invoice data
      dispatch(getInvoiceById(id));
    } catch (err) {
      dispatch(
        enqueueSnackbar({
          message: `Failed to record payment: ${err}`,
          severity: "error",
        })
      );
    }
  };

  // Handle print/generate PDF
  const handlePrint = async () => {
    try {
      const pdfBlob = await dispatch(generateInvoicePdf(invoice._id)).unwrap();

      // Create a URL for the blob and open it in a new window
      const pdfUrl = URL.createObjectURL(pdfBlob);
      window.open(pdfUrl, "_blank");
    } catch (err) {
      dispatch(
        enqueueSnackbar({
          message: `Failed to generate PDF: ${err}`,
          severity: "error",
        })
      );
    }
  };

  // Format date
  const formatDate = (dateString) => {
    if (!dateString) return "N/A";
    const options = { year: "numeric", month: "long", day: "numeric" };
    return new Date(dateString).toLocaleDateString("en-US", options);
  };

  // Format timestamp
  const formatTimestamp = (dateString) => {
    if (!dateString) return "N/A";
    const options = {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    };
    return new Date(dateString).toLocaleDateString("en-US", options);
  };

  // Get status chip based on status value
  const getStatusChip = (status) => {
    const statusMap = {
      PAID: { label: "Paid", color: "success" },
      PARTIAL: { label: "Partially Paid", color: "warning" },
      DRAFT: { label: "Draft", color: "default" },
      SENT: { label: "Sent", color: "primary" },
      VIEWED: { label: "Viewed", color: "info" },
      OVERDUE: { label: "Overdue", color: "error" },
      VOID: { label: "Void", color: "default" },
    };

    const statusConfig = statusMap[status] || {
      label: status,
      color: "default",
    };
    return (
      <Chip
        label={statusConfig.label}
        color={statusConfig.color}
        size="small"
      />
    );
  };

  return (
    <Container maxWidth="xl" sx={{ mt: 4, mb: 4 }}>
      {/* Breadcrumbs */}
      <Breadcrumbs aria-label="breadcrumb" sx={{ mb: 2 }}>
        <Link
          underline="hover"
          color="inherit"
          component="button"
          sx={{ display: "flex", alignItems: "center" }}
          onClick={() => navigate("/invoices")}
        >
          <ReceiptIcon sx={{ mr: 0.5 }} fontSize="inherit" />
          Invoices
        </Link>
        <Typography
          color="text.primary"
          sx={{ display: "flex", alignItems: "center" }}
        >
          {loading ? "Loading..." : invoice?.number}
        </Typography>
      </Breadcrumbs>

      {/* Back Button */}
      <Button
        startIcon={<ArrowBackIcon />}
        onClick={() => navigate("/invoices")}
        sx={{ mb: 3 }}
      >
        Back to Invoices
      </Button>

      {loading ? (
        <Box display="flex" justifyContent="center" p={3}>
          <CircularProgress />
        </Box>
      ) : error ? (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      ) : invoice ? (
        <>
          {/* Invoice Actions */}
          <Paper sx={{ p: 2, mb: 3 }}>
            <Grid container spacing={2} alignItems="center">
              <Grid item>
                <Button
                  variant="outlined"
                  startIcon={<PrintIcon />}
                  onClick={handlePrint}
                >
                  Print
                </Button>
              </Grid>
              <Grid item>
                <Button
                  variant="outlined"
                  startIcon={<EmailIcon />}
                  onClick={() => setEmailDialogOpen(true)}
                >
                  Email
                </Button>
              </Grid>
              <Grid item>
                <Button
                  variant="outlined"
                  startIcon={<EditIcon />}
                  onClick={() => navigate(`/invoices/edit/${invoice._id}`)}
                >
                  Edit
                </Button>
              </Grid>
              <Grid item>
                <Button
                  variant="contained"
                  color="primary"
                  startIcon={<PaymentIcon />}
                  onClick={() => setPaymentDialogOpen(true)}
                  disabled={invoice.status === "PAID"}
                >
                  Record Payment
                </Button>
              </Grid>
              <Grid item sx={{ marginLeft: "auto" }}>
                {getStatusChip(invoice.status)}
              </Grid>
            </Grid>
          </Paper>

          {/* Invoice Header */}
          <Paper sx={{ p: 3, mb: 3 }}>
            <Box sx={{ mb: 4 }}>
              <Grid container>
                <Grid item xs={12} md={6}>
                  <Box sx={{ mb: 3 }}>
                    <Typography variant="h4" component="h1" gutterBottom>
                      Invoice
                    </Typography>
                    <Typography variant="h6" color="text.secondary">
                      {invoice.number}
                    </Typography>
                  </Box>

                  <Box
                    sx={{ display: "flex", flexDirection: "column", gap: 1 }}
                  >
                    <Box sx={{ display: "flex", alignItems: "center" }}>
                      <ScheduleIcon
                        fontSize="small"
                        sx={{ mr: 1, color: "text.secondary" }}
                      />
                      <Typography variant="body2">
                        <strong>Date Issued:</strong>{" "}
                        {formatDate(invoice.issueDate)}
                      </Typography>
                    </Box>
                    <Box sx={{ display: "flex", alignItems: "center" }}>
                      <ScheduleIcon
                        fontSize="small"
                        sx={{ mr: 1, color: "text.secondary" }}
                      />
                      <Typography variant="body2">
                        <strong>Due Date:</strong> {formatDate(invoice.dueDate)}
                      </Typography>
                    </Box>
                    <Box sx={{ display: "flex", alignItems: "center" }}>
                      <JobIcon
                        fontSize="small"
                        sx={{ mr: 1, color: "text.secondary" }}
                      />
                      <Typography variant="body2">
                        <strong>Job:</strong> {invoice.job?.title || "N/A"}
                      </Typography>
                    </Box>
                  </Box>
                </Grid>

                <Grid item xs={12} md={6} sx={{ mt: { xs: 3, md: 0 } }}>
                  <Box
                    sx={{
                      display: "flex",
                      flexDirection: "column",
                      alignItems: { xs: "flex-start", md: "flex-end" },
                    }}
                  >
                    <Typography variant="h6" gutterBottom>
                      Balance Due
                    </Typography>
                    <Typography
                      variant="h4"
                      color={
                        invoice.balance > 0 ? "error.main" : "success.main"
                      }
                      gutterBottom
                    >
                      ${invoice.balance?.toFixed(2) || "0.00"}
                    </Typography>
                    <Box
                      sx={{
                        display: "flex",
                        gap: 2,
                        alignItems: "center",
                        mt: 1,
                      }}
                    >
                      <Typography variant="body2" color="text.secondary">
                        Total: ${invoice.total?.toFixed(2) || "0.00"}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Paid: ${invoice.amountPaid?.toFixed(2) || "0.00"}
                      </Typography>
                    </Box>
                  </Box>
                </Grid>
              </Grid>
            </Box>

            <Divider sx={{ my: 3 }} />

            {/* Customer and Company Info */}
            <Grid container spacing={4}>
              <Grid item xs={12} md={6}>
                <Typography
                  variant="subtitle1"
                  gutterBottom
                  sx={{ display: "flex", alignItems: "center" }}
                >
                  <PersonIcon sx={{ mr: 1 }} /> Bill To
                </Typography>
                <Typography variant="body1" gutterBottom>
                  <strong>
                    {typeof invoice.customer?.businessName === "string"
                      ? invoice.customer.businessName
                      : typeof invoice.customer?.name === "string"
                      ? invoice.customer.name
                      : "N/A"}
                  </strong>
                </Typography>
                {typeof invoice.customer?.contactPerson === "string" &&
                  invoice.customer.contactPerson && (
                    <Typography variant="body2" gutterBottom>
                      Attn: {invoice.customer.contactPerson}
                    </Typography>
                  )}
                {typeof invoice.customer?.address === "string" &&
                  invoice.customer.address && (
                    <Typography variant="body2" gutterBottom>
                      {invoice.customer.address}
                    </Typography>
                  )}
                {typeof invoice.customer?.city === "string" &&
                  typeof invoice.customer?.state === "string" &&
                  typeof invoice.customer?.zip === "string" && (
                    <Typography variant="body2" gutterBottom>
                      {`${invoice.customer.city}, ${invoice.customer.state} ${invoice.customer.zip}`}
                    </Typography>
                  )}
                <Typography variant="body2" gutterBottom>
                  Email:{" "}
                  {typeof invoice.customer?.email === "string"
                    ? invoice.customer.email
                    : "N/A"}
                </Typography>
                <Typography variant="body2">
                  Phone:{" "}
                  {typeof invoice.customer?.phone === "string"
                    ? invoice.customer.phone
                    : typeof invoice.customer?.phoneNumber === "string"
                    ? invoice.customer.phoneNumber
                    : "N/A"}
                </Typography>
              </Grid>

              <Grid item xs={12} md={6}>
                <Typography
                  variant="subtitle1"
                  gutterBottom
                  sx={{ display: "flex", alignItems: "center" }}
                >
                  <BusinessIcon sx={{ mr: 1 }} /> From
                </Typography>
                <Typography variant="body1" gutterBottom>
                  <strong>My Field Service Company</strong>
                </Typography>
                <Typography variant="body2" gutterBottom>
                  123 Main Street, Suite 100
                </Typography>
                <Typography variant="body2" gutterBottom>
                  San Francisco, CA 94105
                </Typography>
                <Typography variant="body2" gutterBottom>
                  Email: <EMAIL>
                </Typography>
                <Typography variant="body2">Phone: (*************</Typography>
              </Grid>
            </Grid>
          </Paper>

          {/* Invoice Line Items */}
          <Paper sx={{ p: 3, mb: 3 }}>
            <Typography variant="h6" gutterBottom>
              Items & Services
            </Typography>
            <TableContainer>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Description</TableCell>
                    <TableCell align="right">Quantity</TableCell>
                    <TableCell align="right">Unit Price</TableCell>
                    <TableCell align="right">Amount</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {invoice.items &&
                    invoice.items.map((item, index) => (
                      <TableRow key={index}>
                        <TableCell>{item.description}</TableCell>
                        <TableCell align="right">{item.quantity}</TableCell>
                        <TableCell align="right">
                          ${item.unitPrice?.toFixed(2) || "0.00"}
                        </TableCell>
                        <TableCell align="right">
                          ${(item.quantity * item.unitPrice).toFixed(2)}
                        </TableCell>
                      </TableRow>
                    ))}

                  {/* Subtotal, Tax, and Total rows */}
                  <TableRow>
                    <TableCell rowSpan={4} />
                    <TableCell colSpan={2} align="right">
                      <strong>Subtotal</strong>
                    </TableCell>
                    <TableCell align="right">
                      ${invoice.subtotal?.toFixed(2) || "0.00"}
                    </TableCell>
                  </TableRow>

                  {invoice.discountTotal > 0 && (
                    <TableRow>
                      <TableCell colSpan={2} align="right">
                        <strong>Discount</strong>
                      </TableCell>
                      <TableCell align="right">
                        -${invoice.discountTotal?.toFixed(2) || "0.00"}
                      </TableCell>
                    </TableRow>
                  )}

                  <TableRow>
                    <TableCell colSpan={2} align="right">
                      <strong>Tax</strong>
                    </TableCell>
                    <TableCell align="right">
                      ${invoice.taxTotal?.toFixed(2) || "0.00"}
                    </TableCell>
                  </TableRow>

                  <TableRow>
                    <TableCell colSpan={2} align="right">
                      <strong>Total</strong>
                    </TableCell>
                    <TableCell align="right">
                      <strong>${invoice.total?.toFixed(2) || "0.00"}</strong>
                    </TableCell>
                  </TableRow>
                </TableBody>
              </Table>
            </TableContainer>

            {invoice.notes && invoice.notes.customer && (
              <Box sx={{ mt: 3 }}>
                <Typography
                  variant="subtitle2"
                  gutterBottom
                  sx={{ display: "flex", alignItems: "center" }}
                >
                  <NotesIcon sx={{ mr: 1, fontSize: "small" }} /> Notes
                </Typography>
                <Typography variant="body2" sx={{ pl: 3 }}>
                  {invoice.notes.customer}
                </Typography>
              </Box>
            )}
          </Paper>

          {/* Payments Section */}
          <Paper sx={{ p: 3, mb: 3 }}>
            <Typography variant="h6" gutterBottom>
              Payments
            </Typography>

            {!invoice.payments || invoice.payments.length === 0 ? (
              <Alert severity="info">
                No payments have been recorded for this invoice.
              </Alert>
            ) : (
              <TableContainer>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Date</TableCell>
                      <TableCell>Method</TableCell>
                      <TableCell>Reference</TableCell>
                      <TableCell align="right">Amount</TableCell>
                      <TableCell>Notes</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {invoice.payments.map((payment, index) => (
                      <TableRow key={index}>
                        <TableCell>{formatDate(payment.date)}</TableCell>
                        <TableCell>
                          {payment.method.replace("_", " ")}
                        </TableCell>
                        <TableCell>{payment.transactionId || "N/A"}</TableCell>
                        <TableCell align="right">
                          ${payment.amount?.toFixed(2) || "0.00"}
                        </TableCell>
                        <TableCell>{payment.notes || ""}</TableCell>
                      </TableRow>
                    ))}
                    <TableRow>
                      <TableCell colSpan={3} align="right">
                        <strong>Total Paid</strong>
                      </TableCell>
                      <TableCell align="right">
                        <strong>
                          ${invoice.amountPaid?.toFixed(2) || "0.00"}
                        </strong>
                      </TableCell>
                      <TableCell />
                    </TableRow>
                  </TableBody>
                </Table>
              </TableContainer>
            )}
          </Paper>

          {/* History Section */}
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Invoice History
            </Typography>

            {!invoice.history || invoice.history.length === 0 ? (
              <Alert severity="info">
                No history available for this invoice.
              </Alert>
            ) : (
              <TableContainer>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Date</TableCell>
                      <TableCell>Action</TableCell>
                      <TableCell>User</TableCell>
                      <TableCell>Details</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {invoice.history.map((entry, index) => (
                      <TableRow key={index}>
                        <TableCell>{formatTimestamp(entry.date)}</TableCell>
                        <TableCell>
                          <Chip
                            label={entry.action}
                            size="small"
                            color={
                              entry.action === "PAYMENT"
                                ? "success"
                                : entry.action === "SENT"
                                ? "primary"
                                : "default"
                            }
                          />
                        </TableCell>
                        <TableCell>
                          {typeof entry.user === "object"
                            ? `${entry.user?.firstName || ""} ${
                                entry.user?.lastName || ""
                              }`.trim() || "System"
                            : "System"}
                        </TableCell>
                        <TableCell>
                          {typeof entry.details === "object"
                            ? JSON.stringify(entry.details)
                            : entry.details || ""}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            )}
          </Paper>

          {/* Email Dialog */}
          <Dialog
            open={emailDialogOpen}
            onClose={() => setEmailDialogOpen(false)}
          >
            <DialogTitle>Send Invoice via Email</DialogTitle>
            <DialogContent>
              <DialogContentText>
                Enter the email address where you'd like to send this invoice:
              </DialogContentText>
              <TextField
                autoFocus
                margin="dense"
                label="Email Address"
                type="email"
                fullWidth
                value={emailAddress}
                onChange={(e) => setEmailAddress(e.target.value)}
              />
            </DialogContent>
            <DialogActions>
              <Button onClick={() => setEmailDialogOpen(false)}>Cancel</Button>
              <Button
                onClick={handleSendEmail}
                disabled={!emailAddress || sendingEmail}
                variant="contained"
              >
                {sendingEmail ? "Sending..." : "Send"}
              </Button>
            </DialogActions>
          </Dialog>

          {/* Payment Dialog */}
          <Dialog
            open={paymentDialogOpen}
            onClose={() => setPaymentDialogOpen(false)}
          >
            <DialogTitle>Record Payment</DialogTitle>
            <DialogContent>
              <DialogContentText>
                Record a payment for invoice {invoice.number}:
              </DialogContentText>
              <Grid container spacing={2} sx={{ mt: 1 }}>
                <Grid item xs={12}>
                  <TextField
                    autoFocus
                    label="Amount"
                    type="number"
                    fullWidth
                    value={paymentAmount}
                    onChange={(e) => setPaymentAmount(e.target.value)}
                    InputProps={{
                      startAdornment: (
                        <Box component="span" sx={{ mr: 1 }}>
                          $
                        </Box>
                      ),
                    }}
                  />
                </Grid>
                <Grid item xs={12}>
                  <TextField
                    select
                    label="Payment Method"
                    fullWidth
                    value={paymentMethod}
                    onChange={(e) => setPaymentMethod(e.target.value)}
                    SelectProps={{
                      native: true,
                    }}
                  >
                    <option value="CREDIT_CARD">Credit Card</option>
                    <option value="CHECK">Check</option>
                    <option value="CASH">Cash</option>
                    <option value="BANK_TRANSFER">Bank Transfer</option>
                    <option value="OTHER">Other</option>
                  </TextField>
                </Grid>
                <Grid item xs={12}>
                  <TextField
                    label="Date"
                    type="date"
                    fullWidth
                    value={paymentDate}
                    onChange={(e) => setPaymentDate(e.target.value)}
                    InputLabelProps={{ shrink: true }}
                  />
                </Grid>
                <Grid item xs={12}>
                  <TextField
                    label="Notes"
                    fullWidth
                    multiline
                    rows={2}
                    value={paymentNote}
                    onChange={(e) => setPaymentNote(e.target.value)}
                  />
                </Grid>
              </Grid>
            </DialogContent>
            <DialogActions>
              <Button onClick={() => setPaymentDialogOpen(false)}>
                Cancel
              </Button>
              <Button
                onClick={handleRecordPayment}
                disabled={!paymentAmount || parseFloat(paymentAmount) <= 0}
                variant="contained"
              >
                Record Payment
              </Button>
            </DialogActions>
          </Dialog>
        </>
      ) : (
        <Alert severity="error">
          Invoice not found. The invoice may have been deleted or you don't have
          permission to view it.
        </Alert>
      )}
    </Container>
  );
};

export default InvoiceDetail;
