const mongoose = require("mongoose");

const companySchema = new mongoose.Schema(
  {
    name: {
      type: String,
      required: true,
      trim: true,
    },
    email: {
      type: String,
      required: true,
      trim: true,
      lowercase: true,
    },
    phone: {
      type: String,
      trim: true,
    },
    address: {
      street: {
        type: String,
        required: true,
      },
      city: {
        type: String,
        required: true,
      },
      state: {
        type: String,
        required: true,
      },
      zipCode: {
        type: String,
        required: true,
      },
      country: {
        type: String,
        default: "US",
      },
    },
    logo: {
      type: String,
      trim: true,
    },
    website: {
      type: String,
      trim: true,
    },
    taxId: {
      type: String,
      trim: true,
    },
    businessType: {
      type: String,
      enum: [
        "HVAC",
        "Plumbing",
        "Electrical",
        "Landscaping",
        "Cleaning",
        "General Contractor",
        "Other",
      ],
      default: "Other",
    },
    primaryServices: [
      {
        type: String,
        trim: true,
      },
    ],
    createdBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
      required: true,
    },
    updatedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
    },
  },
  {
    timestamps: true,
  }
);

// Static method to get the primary company
companySchema.statics.getPrimaryCompany = async function () {
  // Get the first company in the collection (in most cases there will only be one)
  const company = await this.findOne({}).sort({ createdAt: 1 });
  return company;
};

// Database indexes for performance optimization
companySchema.index({ name: 1 }); // Company name lookup
companySchema.index({ email: 1 }); // Email lookup
companySchema.index({ businessType: 1 }); // Business type filtering
companySchema.index({ "address.city": 1, "address.state": 1 }); // Location-based queries
companySchema.index({ primaryServices: 1 }); // Service-based filtering
companySchema.index({ createdBy: 1 }); // Creator tracking
companySchema.index({ createdAt: 1 }); // Creation date sorting

const Company = mongoose.model("Company", companySchema);

module.exports = Company;
