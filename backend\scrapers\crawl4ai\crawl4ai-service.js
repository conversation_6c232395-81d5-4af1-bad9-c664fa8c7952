/**
 * crawl4ai-service.js
 * Service module for interfacing with the Crawl4AI Python library.
 * This module handles spawning the Python process, communicating via stdin/stdout,
 * and managing the results.
 */

const { spawn } = require("child_process");
const path = require("path");
const fs = require("fs");
const logger = require("../../utils/logger");

/**
 * Service class for interacting with Crawl4AI
 */
class Crawl4AIService {
  /**
   * Constructor for the Crawl4AI service
   */
  constructor() {
    this.pythonPath = process.env.CRAWL4AI_PYTHON_PATH || null;
    this.scriptPath = path.join(__dirname, "python", "crawl4ai_wrapper.py");
    this.initialized = false;
    this.initPromise = null;
    this.detectedPythonPath = null;
    this.pathCache = new Map(); // Cache PATH executable checks
  }

  /**
   * Expands glob patterns in paths to actual file paths
   * @param {string[]} paths - Array of paths that may contain glob patterns
   * @returns {string[]} Array of expanded paths
   */
  expandGlobPaths(paths) {
    const expandedPaths = [];
    const fs = require("fs");
    const path = require("path");
    
    for (const pathPattern of paths) {
      if (pathPattern.includes("*")) {
        try {
          // Simple glob expansion for Python paths
          if (pathPattern.includes("/.conda/envs/*/bin/python3")) {
            const condaEnvsPath = pathPattern.replace("/.conda/envs/*/bin/python3", "/.conda/envs");
            if (fs.existsSync(condaEnvsPath)) {
              const envDirs = fs.readdirSync(condaEnvsPath, { withFileTypes: true })
                .filter(dirent => dirent.isDirectory())
                .map(dirent => path.join(condaEnvsPath, dirent.name, "bin", "python3"))
                .filter(pythonPath => fs.existsSync(pythonPath));
              expandedPaths.push(...envDirs);
              if (envDirs.length > 0) {
                logger.debug(`Expanded conda envs pattern to ${envDirs.length} paths`);
              }
            }
          } else {
            // For other patterns, just add the original path
            expandedPaths.push(pathPattern);
          }
        } catch (error) {
          logger.debug(`Error expanding glob pattern ${pathPattern}: ${error.message}`);
          // Add the original path as fallback
          expandedPaths.push(pathPattern);
        }
      } else {
        expandedPaths.push(pathPattern);
      }
    }
    
    // Remove duplicates and return
    return [...new Set(expandedPaths)];
  }

  /**
   * Validates if Python version is compatible with Crawl4AI
   * @param {string} versionString - Python version string (e.g., "Python 3.9.7")
   * @returns {object} Validation result with valid flag and reason
   */
  validatePythonVersion(versionString) {
    try {
      // Extract version number from string like "Python 3.9.7"
      const versionMatch = versionString.match(/(\d+)\.(\d+)(?:\.(\d+))?/);
      if (!versionMatch) {
        return {
          valid: false,
          reason: "Could not parse Python version string"
        };
      }

      const major = parseInt(versionMatch[1]);
      const minor = parseInt(versionMatch[2]);
      
      // Require Python 3.8+
      if (major < 3 || (major === 3 && minor < 8)) {
        return {
          valid: false,
          reason: `Python ${major}.${minor} is not supported. Crawl4AI requires Python 3.8 or higher.`
        };
      }

      // Warn about very new versions that might not be tested
      if (major > 3 || (major === 3 && minor > 12)) {
        logger.warn(`Python ${major}.${minor} is newer than tested versions. Crawl4AI is tested up to Python 3.12.`);
      }

      return {
        valid: true,
        reason: `Python ${major}.${minor} is compatible`
      };
    } catch (error) {
      return {
        valid: false,
        reason: `Error validating Python version: ${error.message}`
      };
    }
  }

  /**
   * Checks if an executable exists in PATH without spawning a process
   * @param {string} executable - Executable name to check
   * @returns {boolean} True if executable likely exists in PATH
   */
  isExecutableInPath(executable) {
    // Return cached result if available
    if (this.pathCache.has(executable)) {
      return this.pathCache.get(executable);
    }

    const path = require("path");
    const fs = require("fs");
    const pathEnv = process.env.PATH || "";
    const pathExtensions = process.platform === "win32" ? [".exe", ".cmd", ".bat"] : [""];
    const pathSeparator = process.platform === "win32" ? ";" : ":";

    const pathDirs = pathEnv.split(pathSeparator).filter(Boolean);

    for (const dir of pathDirs) {
      for (const ext of pathExtensions) {
        const fullPath = path.join(dir, executable + ext);
        try {
          if (fs.existsSync(fullPath)) {
            this.pathCache.set(executable, true);
            return true;
          }
        } catch (error) {
          // Ignore errors when checking existence
          continue;
        }
      }
    }

    this.pathCache.set(executable, false);
    return false;
  }

  /**
   * Initializes the service by verifying the Python environment and script
   * @returns {Promise<boolean>} True if initialization succeeded
   */
  async initialize() {
    if (this.initialized) {
      return true;
    }

    if (this.initPromise) {
      return this.initPromise;
    }

    this.initPromise = new Promise(async (resolve, reject) => {
      try {
        // Verify Python script exists
        if (!fs.existsSync(this.scriptPath)) {
          logger.error(
            `Crawl4AI Python wrapper script not found at: ${this.scriptPath}`
          );
          reject(
            new Error(
              `Crawl4AI Python wrapper script not found at: ${this.scriptPath}`
            )
          );
          return;
        }

        // Detect and test Python environment
        const pythonDetectionResult = await this.detectPythonExecutable();
        if (!pythonDetectionResult.success) {
          logger.error(
            `Failed to detect Python executable: ${pythonDetectionResult.error}`
          );
          reject(
            new Error(
              `Failed to detect Python executable: ${pythonDetectionResult.error}`
            )
          );
          return;
        }

        this.detectedPythonPath = pythonDetectionResult.pythonPath;
        logger.info(`Using Python executable: ${this.detectedPythonPath}`);

        // Test Python environment with detected executable
        const testResult = await this.testPythonEnvironment();
        if (!testResult.success) {
          logger.error(
            `Failed to verify Python environment: ${testResult.error}`
          );
          reject(
            new Error(
              `Failed to verify Python environment: ${testResult.error}`
            )
          );
          return;
        }

        this.initialized = true;
        logger.info("Crawl4AI service initialized successfully");
        resolve(true);
      } catch (error) {
        logger.error(
          `Failed to initialize Crawl4AI service: ${error.message}`,
          { stack: error.stack }
        );
        reject(error);
      }
    });

    return this.initPromise;
  }

  /**
   * Detects available Python executable on the system with enhanced detection
   * @returns {Promise<object>} Result with detected Python path
   */
  async detectPythonExecutable() {
    const platform = process.platform;
    let candidatePaths = [];

    // Platform-specific detection order with enhanced paths
    if (platform === "win32") {
      // Expand USERNAME in paths
      const username = process.env.USERNAME || process.env.USER || "User";
      const userProfilePath = process.env.USERPROFILE || `C:\\Users\\<USER>\\AppData\\Local`;
      const programFiles = process.env.PROGRAMFILES || "C:\\Program Files";
      const programFilesx86 = process.env["PROGRAMFILES(X86)"] || "C:\\Program Files (x86)";

      candidatePaths = [
        this.pythonPath, // User-specified path (if any)
        "python", // Windows standard (try first as it's most reliable)
        "py", // Windows Python Launcher
        "python3", // Sometimes available on Windows
        // Common Windows install locations with expanded paths
        "C:\\Python\\python.exe",
        "C:\\Python39\\python.exe",
        "C:\\Python310\\python.exe",
        "C:\\Python311\\python.exe",
        "C:\\Python312\\python.exe",
        `${programFiles}\\Python\\Python39\\python.exe`,
        `${programFiles}\\Python\\Python310\\python.exe`, 
        `${programFiles}\\Python\\Python311\\python.exe`,
        `${programFiles}\\Python\\Python312\\python.exe`,
        `${programFilesx86}\\Python\\Python39\\python.exe`,
        `${localAppData}\\Programs\\Python\\Python39\\python.exe`,
        `${localAppData}\\Programs\\Python\\Python310\\python.exe`,
        `${localAppData}\\Programs\\Python\\Python311\\python.exe`,
        `${localAppData}\\Programs\\Python\\Python312\\python.exe`,
        `${localAppData}\\Microsoft\\WindowsApps\\python.exe`, // Windows Store Python
        `${localAppData}\\Microsoft\\WindowsApps\\python3.exe`,
      ].filter(Boolean);
    } else {
      candidatePaths = [
        this.pythonPath, // User-specified path (if any)
        "python3", // Unix/Linux/macOS standard (prioritize python3)
        "python", // Fallback
        "/usr/bin/python3", // Common Unix location
        "/usr/bin/python3.8",
        "/usr/bin/python3.9", 
        "/usr/bin/python3.10",
        "/usr/bin/python3.11",
        "/usr/bin/python3.12",
        "/usr/local/bin/python3", // Homebrew/local install
        "/usr/local/bin/python3.8",
        "/usr/local/bin/python3.9",
        "/usr/local/bin/python3.10", 
        "/usr/local/bin/python3.11",
        "/usr/local/bin/python3.12",
        "/opt/homebrew/bin/python3", // Apple Silicon Homebrew
        "/opt/homebrew/bin/python3.8",
        "/opt/homebrew/bin/python3.9",
        "/opt/homebrew/bin/python3.10",
        "/opt/homebrew/bin/python3.11",
        "/opt/homebrew/bin/python3.12",
        // Conda environments
        `${process.env.HOME}/.conda/envs/*/bin/python3`,
        `${process.env.HOME}/anaconda3/bin/python3`,
        `${process.env.HOME}/miniconda3/bin/python3`,
        // pyenv locations
        `${process.env.HOME}/.pyenv/shims/python3`,
        `${process.env.HOME}/.pyenv/shims/python`,
      ].filter(Boolean);
    }

    let lastError = null;
    const expandedPaths = this.expandGlobPaths(candidatePaths);

    for (const pythonPath of expandedPaths) {
      // Skip expensive tests for paths that obviously don't exist
      if (pythonPath.includes("\\") || pythonPath.includes("/")) {
        const fs = require("fs");
        try {
          if (!fs.existsSync(pythonPath)) {
            logger.debug(`Skipping non-existent path: ${pythonPath}`);
            continue;
          }
          // Check if it's actually executable (not just a file)
          const stats = fs.statSync(pythonPath);
          if (!stats.isFile()) {
            logger.debug(`Skipping non-file path: ${pythonPath}`);
            continue;
          }
        } catch (error) {
          logger.debug(`Error checking path ${pythonPath}: ${error.message}`);
          continue;
        }
      } else {
        // For simple executable names, check PATH first
        if (!this.isExecutableInPath(pythonPath)) {
          logger.debug(`Skipping ${pythonPath}: not found in PATH`);
          continue;
        }
      }

      logger.debug(`Testing Python executable: ${pythonPath}`);

      const testResult = await this.testPythonExecutable(pythonPath);
      if (testResult.success) {
        // Validate Python version (require 3.8+)
        const versionValidation = this.validatePythonVersion(testResult.version);
        if (!versionValidation.valid) {
          logger.warn(`Python at ${pythonPath} has incompatible version: ${testResult.version}. ${versionValidation.reason}`);
          lastError = versionValidation.reason;
          continue;
        }
        
        logger.info(`Successfully detected Python executable: ${pythonPath} (${testResult.version})`);
        return {
          success: true,
          pythonPath: pythonPath,
          version: testResult.version,
          platform: platform,
        };
      }

      lastError = testResult.error;
      // Categorize error logging based on error type
      if (testResult.errorCategory === "not_found") {
        logger.debug(`Python executable ${pythonPath} not found`);
      } else if (testResult.errorCategory === "permission_denied") {
        logger.warn(`Python executable ${pythonPath} found but access denied`);
      } else {
        logger.debug(`Python executable ${pythonPath} failed: ${testResult.error}`);
      }
    }

    // If we get here, no Python executable was found
    const errorMessage = [
      "No working Python executable found. Tried: " + candidatePaths.join(", "),
      "",
      "To fix this issue:",
      "1. Install Python 3.8+ from https://www.python.org/downloads/",
      "2. Ensure Python is added to your system PATH",
      "3. Install crawl4ai: pip install crawl4ai",
      "4. Or set CRAWL4AI_PYTHON_PATH environment variable to your Python executable",
      "",
      "For Windows users: Try installing Python from Microsoft Store or python.org",
      "For macOS users: Try: brew install python3",
      "For Linux users: Try: sudo apt update && sudo apt install python3 python3-pip",
    ].join("\n");

    return {
      success: false,
      error: errorMessage,
    };
  }

  /**
   * Tests a specific Python executable with timeout and enhanced error handling
   * @param {string} pythonPath - Path to Python executable to test
   * @param {number} retryCount - Number of retries attempted (for internal use)
   * @returns {Promise<object>} Result of the test
   */
  async testPythonExecutable(pythonPath, retryCount = 0) {
    const maxRetries = 2;
    const timeout = 15000; // Increased to 15 second timeout for version check
    
    return new Promise((resolve) => {
      let timeoutId;
      let pythonProcess;

      const cleanupAndResolve = async (result) => {
        if (timeoutId) {
          clearTimeout(timeoutId);
        }
        if (pythonProcess && !pythonProcess.killed) {
          pythonProcess.kill();
        }
        
        // Retry logic for transient failures
        if (!result.success && retryCount < maxRetries) {
          const shouldRetry = result.errorCategory === "timeout" || 
                             result.errorCategory === "spawn_exception" ||
                             (result.error && result.error.includes("EAGAIN"));
          
          if (shouldRetry) {
            logger.debug(`Retrying Python executable test for ${pythonPath} (attempt ${retryCount + 1}/${maxRetries})`);
            // Wait a bit before retrying
            setTimeout(async () => {
              const retryResult = await this.testPythonExecutable(pythonPath, retryCount + 1);
              resolve(retryResult);
            }, 1000 * (retryCount + 1)); // Exponential backoff
            return;
          }
        }
        
        resolve(result);
      };

      // Set timeout for the entire operation
      timeoutId = setTimeout(() => {
        cleanupAndResolve({
          success: false,
          error: `Python version check timed out after ${timeout}ms`,
          errorCategory: "timeout",
        });
      }, timeout);

      try {
        pythonProcess = spawn(pythonPath, ["--version"], {
          timeout: timeout,
          stdio: ['ignore', 'pipe', 'pipe'],
        });

        let output = "";
        let errorOutput = "";

        pythonProcess.stdout.on("data", (data) => {
          output += data.toString();
        });

        pythonProcess.stderr.on("data", (data) => {
          errorOutput += data.toString();
        });

        pythonProcess.on("close", (code) => {
          if (code !== 0) {
            cleanupAndResolve({
              success: false,
              error: `Python process exited with code ${code}: ${errorOutput.trim() || 'No error output'}`,
            });
            return;
          }

          // Python version output can be on stdout or stderr
          const versionOutput = output || errorOutput;
          if (versionOutput.includes("Python")) {
            const version = versionOutput.trim();
            // Extract just the version number for cleaner logging
            const versionMatch = version.match(/Python\s+(\d+\.\d+\.\d+)/);
            const cleanVersion = versionMatch ? `Python ${versionMatch[1]}` : version;
            
            cleanupAndResolve({
              success: true,
              version: cleanVersion,
              fullVersionOutput: version,
            });
          } else {
            cleanupAndResolve({
              success: false,
              error: "No Python version information found in output",
            });
          }
        });

        pythonProcess.on("error", (error) => {
          // Categorize error types for better handling
          let errorCategory = "unknown";
          if (error.code === "ENOENT") {
            errorCategory = "not_found";
          } else if (error.code === "EACCES") {
            errorCategory = "permission_denied";
          } else if (error.code === "ENOTDIR") {
            errorCategory = "invalid_path";
          }

          cleanupAndResolve({
            success: false,
            error: `Failed to spawn ${pythonPath}: ${error.message}`,
            errorCode: error.code,
            errorCategory: errorCategory,
          });
        });

      } catch (spawnError) {
        cleanupAndResolve({
          success: false,
          error: `Exception during spawn: ${spawnError.message}`,
          errorCategory: "spawn_exception",
        });
      }
    });
  }

  /**
   * Tests the Python environment to ensure Crawl4AI is properly installed
   * @returns {Promise<object>} Result of the test
   */
  async testPythonEnvironment() {
    const pythonPath = this.detectedPythonPath || this.pythonPath;

    if (!pythonPath) {
      return {
        success: false,
        error:
          "No Python executable found. Please ensure Python is installed and accessible.",
      };
    }

    return new Promise((resolve) => {
      const timeout = 30000; // 30 second timeout for package import
      let timeoutId;
      let pythonProcess;

      const cleanupAndResolve = (result) => {
        if (timeoutId) {
          clearTimeout(timeoutId);
        }
        if (pythonProcess && !pythonProcess.killed) {
          pythonProcess.kill();
        }
        resolve(result);
      };

      // Set timeout for the entire operation
      timeoutId = setTimeout(() => {
        cleanupAndResolve({
          success: false,
          error: `Crawl4AI package test timed out after ${timeout}ms. This may indicate installation issues.`,
        });
      }, timeout);

      try {
        pythonProcess = spawn(pythonPath, [
          "-c",
          'import crawl4ai; print("Crawl4AI installed successfully")',
        ], {
          timeout: timeout,
          stdio: ['ignore', 'pipe', 'pipe'],
        });

        let output = "";
        let errorOutput = "";

        pythonProcess.stdout.on("data", (data) => {
          output += data.toString();
        });

        pythonProcess.stderr.on("data", (data) => {
          errorOutput += data.toString();
        });

        pythonProcess.on("close", (code) => {
          if (code !== 0) {
            // Provide more specific error messages based on common issues
            let specificError = errorOutput;
            if (errorOutput.includes("ModuleNotFoundError")) {
              specificError = "Crawl4AI package is not installed.";
            } else if (errorOutput.includes("ImportError")) {
              specificError = "Crawl4AI package is installed but has dependency issues.";
            } else if (errorOutput.includes("SyntaxError")) {
              specificError = "Python version incompatibility with Crawl4AI.";
            }

            cleanupAndResolve({
              success: false,
              error: `Python process exited with code ${code}: ${specificError}`,
              rawError: errorOutput,
            });
            return;
          }

          if (output.includes("Crawl4AI installed successfully")) {
            cleanupAndResolve({
              success: true,
              message: "Crawl4AI installed and working properly",
              pythonPath: pythonPath,
            });
          } else {
            // Determine appropriate pip command based on Python executable
            let pipCommand = "pip";
            if (pythonPath.includes("python3")) {
              pipCommand = "pip3";
            } else if (pythonPath.includes("py")) {
              pipCommand = "pip";
            }

            const installInstructions = [
              "Crawl4AI not properly installed.",
              "",
              "To install Crawl4AI:",
              `1. Using pip: ${pipCommand} install crawl4ai`,
              `2. Alternative: ${pythonPath} -m pip install crawl4ai`,
              "3. Run setup: python -m crawl4ai.cli.setup",
              "4. Test installation: python -m crawl4ai.cli.test_env",
              "",
              "For more information, visit: https://github.com/unclecode/crawl4ai",
              "",
              "If issues persist, try:",
              "- Upgrading pip: python -m pip install --upgrade pip",
              "- Using virtual environment: python -m venv venv && source venv/bin/activate (Linux/Mac) or venv\\Scripts\\activate (Windows)",
            ].join("\n");

            cleanupAndResolve({
              success: false,
              error: installInstructions,
              output,
              errorOutput,
            });
          }
        });

        pythonProcess.on("error", (error) => {
          cleanupAndResolve({
            success: false,
            error: `Failed to test Python environment: ${error.message}`,
            errorCode: error.code,
          });
        });

      } catch (spawnError) {
        cleanupAndResolve({
          success: false,
          error: `Exception during Python environment test: ${spawnError.message}`,
        });
      }
    });
  }

  /**
   * Crawls a URL using Crawl4AI
   * @param {string} url - The URL to crawl
   * @param {object} options - Options for the crawler
   * @returns {Promise<object>} The crawl results
   */
  async crawl(url, options = {}) {
    try {
      await this.initialize();

      return new Promise((resolve, reject) => {
        const startTime = Date.now();
        // Reduced logging - only log start for non-routine operations
        // Full logging is available in the Python wrapper log files if needed

        const pythonPath = this.detectedPythonPath || this.pythonPath;

        if (!pythonPath) {
          throw new Error(
            "No Python executable found. Please ensure Python is installed and accessible."
          );
        }

        const pythonProcess = spawn(pythonPath, [this.scriptPath]);
        let dataString = "";
        let errorString = "";
        let timedOut = false;

        // Default timeout can be overridden via env var CRAWL4AI_DEFAULT_TIMEOUT
        const defaultTimeout = parseInt(process.env.CRAWL4AI_DEFAULT_TIMEOUT || "45000", 10);
        const timeout = options.timeout || defaultTimeout; // Default 45 seconds unless overridden

        // Enhanced timeout handling with proper process cleanup
        const timeoutId = setTimeout(() => {
          timedOut = true;

          // Try graceful termination first
          try {
            pythonProcess.kill('SIGTERM');
            logger.warn(`Crawl4AI process gracefully terminated after ${timeout}ms for URL: ${url}`);

            // Force kill after 5 seconds if still running
            setTimeout(() => {
              if (!pythonProcess.killed) {
                pythonProcess.kill('SIGKILL');
                logger.error(`Crawl4AI process force-killed after timeout for URL: ${url}`);
              }
            }, 5000);

          } catch (killError) {
            logger.error(`Error killing Crawl4AI process: ${killError.message}`);
            // Try force kill as fallback
            try {
              pythonProcess.kill('SIGKILL');
            } catch (forceKillError) {
              logger.error(`Error force-killing Crawl4AI process: ${forceKillError.message}`);
            }
          }

          logger.error(
            `Crawl4AI process timed out after ${timeout}ms for URL: ${url}`
          );
          reject(new Error(`Crawl operation timed out after ${timeout}ms`));
        }, timeout);

        pythonProcess.stdout.on("data", (data) => {
          dataString += data.toString();
        });

        pythonProcess.stderr.on("data", (data) => {
          errorString += data.toString();
          const stderrText = data.toString();
          
          // Only log important messages, filter out routine logging noise
          const isImportantMessage = 
            stderrText.includes('[ERROR]') ||
            stderrText.includes('[CRITICAL]') ||
            stderrText.includes('Failed') ||
            stderrText.includes('Exception') ||
            stderrText.includes('Error') ||
            stderrText.includes('WARNING');
          
          if (isImportantMessage) {
            // Log to AI generation file if AI process is active, otherwise normal debug log
            if (logger.isAiGenerationActive) {
              logger.aiGenerationDebug(`Crawl4AI stderr: ${stderrText}`);
            } else {
              logger.debug(`Crawl4AI stderr: ${stderrText}`);
            }
          }
          // Silently accumulate all stderr for error reporting if process fails
        });

        pythonProcess.on("close", (code, signal) => {
          clearTimeout(timeoutId);

          if (timedOut) {
            return; // Already handled by the timeout
          }

          const duration = Date.now() - startTime;

          // Enhanced logging with signal information
          if (code !== 0 || duration > 10000 || signal) {
            const logMessage = `Crawl4AI process completed in ${duration}ms with code ${code}${signal ? ` (signal: ${signal})` : ''}`;

            // Log to AI generation file if AI process is active, otherwise normal debug log
            if (logger.isAiGenerationActive) {
              logger.aiGenerationDebug(logMessage);
            } else {
              logger.debug(logMessage);
            }
          }

          // Handle different exit scenarios
          if (signal === 'SIGTERM' || signal === 'SIGKILL') {
            logger.warn(`Crawl4AI process was terminated by signal ${signal} for URL: ${url}`);
            reject(new Error(`Crawl4AI process was terminated (${signal})`));
            return;
          }

          if (code !== 0) {
            const errorMessage = `Crawl4AI process exited with code ${code}: ${errorString}`;
            logger.error(errorMessage);
            reject(new Error(errorMessage));
            return;
          }

          try {
            const result = JSON.parse(dataString);
            result.metadata = result.metadata || {};
            result.metadata.processDuration = duration;

            if (!result.success) {
              logger.error(`Crawl4AI reported error: ${result.error}`);
              reject(
                new Error(result.error || "Unknown error in Crawl4AI operation")
              );
              return;
            }

            resolve(result);
          } catch (error) {
            logger.error(`Failed to parse Crawl4AI output: ${error.message}`, {
              error,
              output: dataString.substring(0, 1000), // Log first 1000 chars
            });
            reject(
              new Error(`Failed to parse Crawl4AI output: ${error.message}`)
            );
          }
        });

        pythonProcess.on("error", (error) => {
          clearTimeout(timeoutId);

          // Enhanced error logging with more context
          logger.error(`Failed to start Crawl4AI process for URL: ${url}`, {
            error: error.message,
            code: error.code,
            errno: error.errno,
            syscall: error.syscall,
            path: error.path,
            pythonPath: this.pythonPath
          });

          // Provide more specific error messages based on error type
          let errorMessage = `Failed to start Crawl4AI process: ${error.message}`;
          if (error.code === 'ENOENT') {
            errorMessage = `Python executable not found at path: ${this.pythonPath}. Please check Python installation.`;
          } else if (error.code === 'EACCES') {
            errorMessage = `Permission denied accessing Python executable: ${this.pythonPath}`;
          }

          reject(new Error(errorMessage));
        });

        // Send data to the Python script
        pythonProcess.stdin.write(JSON.stringify({ url, options }));
        pythonProcess.stdin.end();
      });
    } catch (error) {
      logger.error(`Error in crawl operation: ${error.message}`, {
        stack: error.stack,
      });
      throw error;
    }
  }
  /**
   * Comprehensive health check for Crawl4AI system
   * @returns {Promise<object>} Detailed health status
   */
  async healthCheck() {
    logger.info("[CRAWL4AI_HEALTH] Starting comprehensive health check");

    const healthResult = {
      overall: "unknown",
      pythonEnvironment: null,
      crawl4aiPackage: null,
      testCrawl: null,
      timestamp: new Date().toISOString(),
    };

    try {
      // Test Python environment
      const pythonResult = await this.detectPythonExecutable();
      healthResult.pythonEnvironment = {
        available: pythonResult.success,
        path: pythonResult.pythonPath,
        version: pythonResult.version,
        error: pythonResult.error,
      };

      if (!pythonResult.success) {
        healthResult.overall = "failed";
        logger.error(
          "[CRAWL4AI_HEALTH] Python environment check failed:",
          pythonResult.error
        );
        return healthResult;
      }

      // Set the detected Python path for subsequent tests
      this.detectedPythonPath = pythonResult.pythonPath;

      // Test Crawl4AI package
      const packageResult = await this.testPythonEnvironment();
      healthResult.crawl4aiPackage = {
        installed: packageResult.success,
        message: packageResult.message,
        error: packageResult.error,
      };

      if (!packageResult.success) {
        healthResult.overall = "failed";
        logger.error(
          "[CRAWL4AI_HEALTH] Crawl4AI package check failed:",
          packageResult.error
        );
        return healthResult;
      }

      // Test actual crawling
      logger.info("[CRAWL4AI_HEALTH] Testing actual crawl functionality...");
      const testUrl = "https://httpbin.org/json";
      const testResult = await this.crawl(testUrl, {
        timeout: 30000,
        headless: true,
      });

      healthResult.testCrawl = {
        success: testResult.success,
        url: testUrl,
        hasContent: !!(testResult.markdown || testResult.html),
        contentLength: testResult.markdown?.length || 0,
        error: testResult.error,
      };

      if (testResult.success && testResult.markdown) {
        healthResult.overall = "healthy";
        logger.info("[CRAWL4AI_HEALTH] ✅ All health checks passed");
      } else {
        healthResult.overall = "degraded";
        logger.warn(
          "[CRAWL4AI_HEALTH] ⚠️  Test crawl failed or returned no content"
        );
      }
    } catch (error) {
      healthResult.overall = "failed";
      healthResult.error = error.message;
      logger.error("[CRAWL4AI_HEALTH] Health check exception:", error);
    }

    return healthResult;
  }

  /**
   * Get diagnostic information for troubleshooting
   * @returns {Promise<object>} Diagnostic data
   */
  async getDiagnostics() {
    const diagnostics = {
      service: {
        initialized: this.initialized,
        pythonPath: this.pythonPath,
        detectedPythonPath: this.detectedPythonPath,
        scriptPath: this.scriptPath,
        scriptExists: fs.existsSync(this.scriptPath),
      },
      environment: {
        nodeVersion: process.version,
        platform: process.platform,
        arch: process.arch,
        cwd: process.cwd(),
        env: {
          CRAWL4AI_PYTHON_PATH: process.env.CRAWL4AI_PYTHON_PATH,
          PATH_PYTHON_FOUND: process.env.PATH?.split(path.delimiter)?.some(
            (p) =>
              fs.existsSync(path.join(p, "python.exe")) ||
              fs.existsSync(path.join(p, "python3.exe")) ||
              fs.existsSync(path.join(p, "py.exe"))
          ),
        },
      },
      timestamp: new Date().toISOString(),
    };

    return diagnostics;
  }

  /**
   * Generate installation instructions based on current environment
   * @returns {Promise<object>} Installation guide
   */
  async getInstallationGuide() {
    const platform = process.platform;
    const guide = {
      platform,
      steps: [],
      timestamp: new Date().toISOString(),
    };

    if (platform === "win32") {
      guide.steps = [
        "1. Download Python 3.8+ from https://www.python.org/downloads/",
        '2. During installation, check "Add Python to PATH"',
        "3. Open Command Prompt as Administrator",
        "4. Install Crawl4AI: pip install crawl4ai",
        "5. Run setup: python -m crawl4ai.cli.setup",
        "6. Test installation: python -m crawl4ai.cli.test_env",
        "7. Restart your application",
      ];
    } else if (platform === "darwin") {
      guide.steps = [
        '1. Install Homebrew: /bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"',
        "2. Install Python: brew install python3",
        "3. Install Crawl4AI: pip3 install crawl4ai",
        "4. Run setup: python3 -m crawl4ai.cli.setup",
        "5. Test installation: python3 -m crawl4ai.cli.test_env",
        "6. Restart your application",
      ];
    } else {
      guide.steps = [
        "1. Update package list: sudo apt update",
        "2. Install Python: sudo apt install python3 python3-pip",
        "3. Install Crawl4AI: pip3 install crawl4ai",
        "4. Run setup: python3 -m crawl4ai.cli.setup",
        "5. Test installation: python3 -m crawl4ai.cli.test_env",
        "6. Restart your application",
      ];
    }

    return guide;
  }
}

module.exports = new Crawl4AIService();
