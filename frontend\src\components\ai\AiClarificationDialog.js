import React, { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON>alogTitle,
  DialogContent,
  <PERSON>alogActions,
  Button,
  TextField,
  Typography,
  Box,
  CircularProgress,
  List,
  ListItem,
  ListItemText,
  Divider,
} from "@mui/material";
import { useDispatch, useSelector } from "react-redux";
import {
  answerAIQuestions,
  answerDraftQuoteQuestions,
} from "../../slices/quoteSlice";

/**
 * Dialog component for handling AI clarification questions
 * Supports both traditional mode (requires saved quote) and draft mode (works with unsaved data)
 */
const AiClarificationDialog = ({
  open,
  onClose,
  onSubmitAnswers,
  currentFormValues,
  quoteId,
  // NEW: Draft mode support + Streaming mode support
  mode = "traditional", // 'traditional', 'draft', or 'streaming'
  draftQuoteData = null, // Required for draft mode: { inputType, inputData, aiQuestions, formData }
}) => {
  const dispatch = useDispatch();
  const { aiQuestions, aiLoading } = useSelector((state) => state.quotes);
  const [answers, setAnswers] = useState({});
  const [submitError, setSubmitError] = useState(null);
  const [draftLoading, setDraftLoading] = useState(false);

  // 🔧 FIX: Use direct prop evaluation instead of useState to ensure mode changes are reflected
  const isDraftMode = mode === "draft";
  const isStreamingMode = mode === "streaming";

  // Get questions from appropriate source based on mode
  const questionsToUse = isStreamingMode
    ? draftQuoteData?.aiQuestions
    : isDraftMode
    ? draftQuoteData?.aiQuestions
    : aiQuestions;
  const isLoading = isDraftMode ? draftLoading : aiLoading;

  // Reset answers when dialog opens with new questions - fixed dependency management
  useEffect(() => {
    if (open && questionsToUse?.length > 0) {
      const currentMode = isStreamingMode
        ? "STREAMING"
        : isDraftMode
        ? "DRAFT"
        : "PHASE 3";
      console.group(
        `🧪 [${currentMode}] AiClarificationDialog - Dialog Initialization (Enhanced Monitoring)`
      );
      // eslint-disable-next-line react-hooks/exhaustive-deps -- currentFormValues, draftQuoteData, onClose, onSubmitAnswers are logging/validation only and don't affect the core initialization logic
      console.log(`📊 [${currentMode}] Props validation:`, {
        mode: currentMode,
        open,
        quoteId:
          quoteId ||
          (isDraftMode || isStreamingMode
            ? "NOT_REQUIRED_IN_DRAFT/STREAMING"
            : "NOT_PROVIDED"),
        questionsCount: questionsToUse?.length || 0,
        currentFormValues: currentFormValues ? "PROVIDED" : "NOT_PROVIDED",
        draftQuoteData:
          isDraftMode || isStreamingMode
            ? draftQuoteData
              ? "PROVIDED"
              : "MISSING"
            : "NOT_APPLICABLE",
        onClose: typeof onClose,
        onSubmitAnswers: typeof onSubmitAnswers,
      });

      console.log(`📊 [${currentMode}] Dialog opened with questions:`, {
        mode: currentMode,
        questionsCount: questionsToUse.length,
        quoteId: isDraftMode || isStreamingMode ? "NOT_REQUIRED" : quoteId,
        questions: questionsToUse,
      });

      if (questionsToUse?.length > 0) {
        const currentMode = isStreamingMode
          ? "STREAMING"
          : isDraftMode
          ? "DRAFT"
          : "PHASE 3";
        console.log(
          `❓ [${currentMode}] AI Questions received:`,
          questionsToUse.map((q, idx) => ({
            index: idx,
            question:
              typeof q === "string"
                ? q.substring(0, 100) + "..."
                : q.question?.substring(0, 100) + "...",
            type: typeof q === "string" ? "string" : "object",
            hasOptions: typeof q === "object" && q.options?.length > 0,
          }))
        );
      }

      if (!isDraftMode && !isStreamingMode && !quoteId) {
        console.error(
          "⚠️ [PHASE 3 CRITICAL] quoteId missing - this WILL cause API failures!"
        );
        console.error(
          '⚠️ [PHASE 3] This was the root cause of "no way to respond" issue'
        );
      } else if (!isDraftMode && !isStreamingMode) {
        console.log("✅ [PHASE 3] quoteId validation passed:", quoteId);
      } else {
        console.log(
          `✅ [${
            isStreamingMode ? "STREAMING" : "DRAFT"
          }] Mode - no quoteId required`
        );
      }

      const initialAnswers = {};
      // Handle both string array (backend format) and object array formats
      const normalizedQuestions = questionsToUse.map((q, index) => {
        if (typeof q === "string") {
          console.log(
            `[AiClarificationDialog] Normalizing string question ${index}:`,
            q
          );
          return { id: `question_${index}`, question: q };
        }
        console.log(
          `[AiClarificationDialog] Using object question ${index}:`,
          q
        );
        return q;
      });

      normalizedQuestions.forEach((q) => {
        initialAnswers[q.id] = "";
      });

      console.log(
        "[AiClarificationDialog] Initialized answers structure:",
        initialAnswers
      );
      setAnswers(initialAnswers);
      console.groupEnd();
    } else if (open) {
      const currentMode = isStreamingMode
        ? "STREAMING"
        : isDraftMode
        ? "DRAFT"
        : "TRADITIONAL";
      console.warn(
        "[AiClarificationDialog] Dialog opened but no questions available:",
        {
          open,
          questionsAvailable: questionsToUse?.length || 0,
          mode: currentMode,
        }
      );
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    open,
    questionsToUse?.length,
    quoteId,
    isDraftMode,
    isStreamingMode,
    questionsToUse,
  ]); // Note: currentFormValues, draftQuoteData, onClose, and onSubmitAnswers are only used in handleSubmit, not in this initialization effect

  const handleAnswerChange = (questionId, value) => {
    setAnswers((prev) => ({
      ...prev,
      [questionId]: value,
    }));
  };

  const handleSubmit = async () => {
    // Clear any previous errors
    setSubmitError(null);

    const logPrefix = isStreamingMode
      ? "[STREAMING CLARIFICATION]"
      : isDraftMode
      ? "[DRAFT CLARIFICATION]"
      : "[PHASE 3]";

    console.group(
      `🚀 ${logPrefix} AiClarificationDialog - Submission Process (Enhanced Monitoring)`
    );
    console.log(`📊 ${logPrefix} Starting submission validation`);
    console.log(`📊 ${logPrefix} Current state:`, {
      mode: isStreamingMode
        ? "STREAMING"
        : isDraftMode
        ? "DRAFT"
        : "TRADITIONAL",
      quoteId:
        isDraftMode || isStreamingMode ? "NOT_REQUIRED" : quoteId || "MISSING",
      answersCount: Object.keys(answers).length,
      questionsCount: questionsToUse?.length || 0,
      isLoading,
      answers,
      draftQuoteData:
        isDraftMode || isStreamingMode
          ? draftQuoteData
            ? "PROVIDED"
            : "MISSING"
          : "NOT_APPLICABLE",
    });

    // Validate all questions are answered
    if (Object.values(answers).some((a) => !a.trim())) {
      const emptyAnswers = Object.entries(answers)
        .filter(([_, value]) => !value.trim())
        .map(([key]) => key);
      console.warn(
        `⚠️ ${logPrefix} Validation failed - empty answers:`,
        emptyAnswers
      );
      setSubmitError("Please answer all questions before continuing.");
      console.groupEnd();
      return;
    }

    try {
      if (isStreamingMode) {
        // 🆕 STREAMING MODE: Pass answers back to parent for streaming API submission
        console.log(
          `🚀 ${logPrefix} Processing streaming clarification - passing answers to parent`
        );
        console.log(`🚀 ${logPrefix} User answers:`, answers);

        const startTime = Date.now();

        // For streaming mode, we just pass the answers back to the parent
        // The parent (CreateQuoteFormik) will handle the actual API submission
        if (onSubmitAnswers) {
          console.log(
            `🔗 ${logPrefix} Calling parent onSubmitAnswers handler with streaming answers`
          );
          await onSubmitAnswers(answers);
          console.log(`✅ ${logPrefix} Parent handler completed successfully`);
        } else {
          console.log(
            `📝 ${logPrefix} No parent onSubmitAnswers handler provided`
          );
        }

        const duration = Date.now() - startTime;
        console.log(
          `✅ ${logPrefix} ✨ SUCCESS! Streaming clarification completed in`,
          duration + "ms"
        );
      } else if (isDraftMode) {
        // DRAFT MODE: Use Redux action for draft clarification
        console.log(
          `🚀 ${logPrefix} Processing draft quote clarification via Redux`
        );
        console.log(`🚀 ${logPrefix} Draft data:`, draftQuoteData);
        console.log(`🚀 ${logPrefix} User answers:`, answers);

        const startTime = Date.now();

        const result = await dispatch(
          answerDraftQuoteQuestions({
            draftQuoteData: {
              inputType: draftQuoteData.inputType,
              inputData: draftQuoteData.inputData,
              aiQuestions: draftQuoteData.aiQuestions,
              formData: draftQuoteData.formData || currentFormValues,
            },
            answers: answers,
          })
        ).unwrap();

        const duration = Date.now() - startTime;
        console.log(
          `✅ ${logPrefix} ✨ SUCCESS! Draft clarification completed in`,
          duration + "ms"
        );
        console.log(`✅ ${logPrefix} Response result:`, result);

        // Call the external handler with the enhanced data
        if (onSubmitAnswers) {
          console.log(
            `🔗 ${logPrefix} Calling external onSubmitAnswers handler with enhanced data`
          );
          onSubmitAnswers(result.generatedData || result);
          console.log(
            `✅ ${logPrefix} External handler completed successfully`
          );
        } else {
          console.log(
            `📝 ${logPrefix} No external onSubmitAnswers handler provided`
          );
        }
      } else {
        // TRADITIONAL MODE: Use existing Redux action
        console.log(`🔍 ${logPrefix} Redux action signature check:`);
        console.log(`🔍 answerAIQuestions expects: { quoteId, answers }`);
        console.log(`🔍 We are providing:`, {
          quoteId: quoteId,
          answers: answers,
        });

        // Validate quoteId is available for traditional mode
        if (!quoteId) {
          console.error(
            `❌ ${logPrefix} CRITICAL VALIDATION FAILED - missing quoteId`
          );
          console.error(
            `❌ ${logPrefix} This is the ROOT CAUSE of "no way to respond" issue!`
          );
          console.error(
            `❌ ${logPrefix} API call will be: /api/quotes/undefined/answer-ai`
          );
          setSubmitError(
            "Quote ID is missing. Please save the quote first before answering AI questions."
          );
          console.error(
            "AiClarificationDialog: quoteId is required but not provided"
          );
          console.groupEnd();
          return;
        } else {
          console.log(`✅ ${logPrefix} quoteId validation PASSED:`, quoteId);
          console.log(
            `✅ ${logPrefix} API call will be: /api/quotes/` +
              quoteId +
              "/answer-ai"
          );
        }

        console.log(`🚀 ${logPrefix} Submitting answers for quote:`, quoteId);
        console.log(`🚀 ${logPrefix} Answers payload:`, answers);
        console.log(`🚀 ${logPrefix} Redux action parameters (FIXED):`, {
          quoteId,
          answers,
        });
        console.log(
          `🚀 ${logPrefix} About to dispatch answerAIQuestions action`
        );

        const startTime = Date.now();

        // Dispatch the answers to Redux with the required quoteId (FIXED IMPLEMENTATION)
        console.log(
          `🚀 ${logPrefix} Dispatching answerAIQuestions with CORRECT parameters`
        );
        const result = await dispatch(
          answerAIQuestions({
            quoteId: quoteId, // ✅ FIXED: Now correctly providing quoteId
            answers: answers, // ✅ FIXED: Answers in correct format
          })
        ).unwrap();

        const duration = Date.now() - startTime;
        console.log(
          `✅ ${logPrefix} ✨ SUCCESS! AI clarification submission completed in`,
          duration + "ms"
        );
        console.log(`✅ ${logPrefix} Response result:`, result);

        // Validate result structure
        if (!result || typeof result !== "object") {
          console.warn(
            `⚠️ ${logPrefix} Unexpected result format:`,
            typeof result
          );
        } else {
          console.log(
            `✅ ${logPrefix} Result validation passed - received object response`
          );
        }

        // Call the external handler with answers
        if (onSubmitAnswers) {
          console.log(
            `🔗 ${logPrefix} Calling external onSubmitAnswers handler`
          );
          onSubmitAnswers(answers);
          console.log(
            `✅ ${logPrefix} External handler completed successfully`
          );
        } else {
          console.log(
            `📝 ${logPrefix} No external onSubmitAnswers handler provided`
          );
        }
      }

      // Close the dialog
      console.log(`🚪 ${logPrefix} Closing clarification dialog`);
      onClose();

      console.log(
        `✨ ${logPrefix} 🎉 AI CLARIFICATION WORKFLOW COMPLETED SUCCESSFULLY! 🎉`
      );
      if (!isDraftMode) {
        console.log(
          `✨ ${logPrefix} "No way to respond" issue has been RESOLVED!`
        );
      }
      console.groupEnd();
    } catch (error) {
      console.groupCollapsed(
        `❌ ${logPrefix} ERROR in AI clarification submission`
      );
      console.error(`❌ ${logPrefix} Error details:`, {
        message: error?.message,
        status: error?.response?.status,
        statusText: error?.response?.statusText,
        url: error?.config?.url,
        data: error?.response?.data,
        stack: error?.stack?.substring(0, 500),
      });

      console.error(`❌ ${logPrefix} Request details:`, {
        method: error?.config?.method,
        headers: error?.config?.headers,
        baseURL: error?.config?.baseURL,
        timeout: error?.config?.timeout,
      });

      if (isDraftMode) {
        setDraftLoading(false);
      }

      // Check if this is the old "Invalid quote ID" error that we fixed
      if (
        error?.response?.status === 400 &&
        error?.response?.data?.message?.includes("Invalid quote ID")
      ) {
        console.error(
          `❌ ${logPrefix} REGRESSION DETECTED! The quoteId fix is not working!`
        );
        console.error(`❌ ${logPrefix} API still receiving undefined quoteId`);
      } else {
        console.log(
          `✅ ${logPrefix} This is NOT the "Invalid quote ID" error we fixed`
        );
      }

      // Provide user-friendly error messages based on error type
      let errorMessage =
        "There was an error processing your answers. Please try again.";

      if (error?.message) {
        if (error.message.includes("Invalid quote ID")) {
          errorMessage = isDraftMode
            ? "Error processing draft clarification. Please try again."
            : "Invalid quote ID. Please save the quote first before answering AI questions.";
          console.error(
            "[AiClarificationDialog] Invalid quote ID error detected"
          );
        } else if (error.message.includes("not found")) {
          errorMessage = isDraftMode
            ? "Draft clarification service not available. Please try again later."
            : "Quote not found. Please refresh the page and try again.";
          console.error("[AiClarificationDialog] Not found error detected");
        } else if (
          error.message.includes("network") ||
          error.message.includes("timeout")
        ) {
          errorMessage =
            "Network error. Please check your connection and try again.";
          console.error("[AiClarificationDialog] Network error detected");
        } else {
          errorMessage = error.message;
          console.error(
            "[AiClarificationDialog] Custom error message:",
            error.message
          );
        }
      }

      setSubmitError(errorMessage);
      console.groupEnd();
    }
  };

  return (
    <Dialog
      open={open}
      onClose={() => (isLoading ? null : onClose())}
      maxWidth="md"
      fullWidth
    >
      <DialogTitle>
        AI Needs Clarification{" "}
        {isStreamingMode ? "(Streaming Mode)" : isDraftMode && "(Draft Mode)"}
      </DialogTitle>
      <DialogContent>
        {isLoading ? (
          <Box sx={{ display: "flex", justifyContent: "center", my: 4 }}>
            <CircularProgress />
          </Box>
        ) : (
          <>
            <Typography variant="body1" sx={{ mb: 2 }}>
              To generate more accurate results, please answer these questions:
            </Typography>

            {submitError && (
              <Box
                sx={{ mb: 2, p: 2, bgcolor: "error.light", borderRadius: 1 }}
              >
                <Typography variant="body2" color="error.main">
                  {submitError}
                </Typography>
              </Box>
            )}

            {!isDraftMode && !isStreamingMode && !quoteId && (
              <Box
                sx={{ mb: 2, p: 2, bgcolor: "warning.light", borderRadius: 1 }}
              >
                <Typography variant="body2" color="warning.main">
                  ⚠️ Please save your quote first before answering AI questions.
                  The quote ID is required to process your responses.
                </Typography>
              </Box>
            )}

            {isDraftMode && (
              <Box sx={{ mb: 2, p: 2, bgcolor: "info.light", borderRadius: 1 }}>
                <Typography variant="body2" color="info.main">
                  ✨ Draft Mode: You can answer these questions without saving
                  the quote first.
                </Typography>
              </Box>
            )}

            {isStreamingMode && (
              <Box
                sx={{ mb: 2, p: 2, bgcolor: "primary.light", borderRadius: 1 }}
              >
                <Typography variant="body2" color="primary.main">
                  🚀 Streaming Mode: Your answers will be processed in real-time
                  and streaming will continue automatically.
                </Typography>
              </Box>
            )}

            {questionsToUse && questionsToUse.length > 0 ? (
              <List>
                {questionsToUse.map((question, index) => {
                  // Handle both string array (backend format) and object array formats
                  const normalizedQuestion =
                    typeof question === "string"
                      ? { id: `question_${index}`, question: question }
                      : question;

                  return (
                    <React.Fragment key={normalizedQuestion.id || index}>
                      <ListItem
                        sx={{
                          flexDirection: "column",
                          alignItems: "flex-start",
                        }}
                      >
                        <ListItemText
                          primary={
                            <Typography variant="subtitle1">
                              {normalizedQuestion.question}
                            </Typography>
                          }
                          secondary={
                            normalizedQuestion.explanation && (
                              <Typography
                                variant="body2"
                                color="text.secondary"
                              >
                                {normalizedQuestion.explanation}
                              </Typography>
                            )
                          }
                        />
                        <Box sx={{ width: "100%", mt: 1 }}>
                          <TextField
                            fullWidth
                            multiline
                            rows={2}
                            variant="outlined"
                            placeholder="Your answer..."
                            value={answers[normalizedQuestion.id] || ""}
                            onChange={(e) =>
                              handleAnswerChange(
                                normalizedQuestion.id,
                                e.target.value
                              )
                            }
                            disabled={isLoading}
                          />
                        </Box>
                      </ListItem>
                      {index < questionsToUse.length - 1 && (
                        <Divider component="li" />
                      )}
                    </React.Fragment>
                  );
                })}
              </List>
            ) : (
              <Box sx={{ my: 4, textAlign: "center" }}>
                <Typography
                  variant="body1"
                  color="text.secondary"
                  sx={{ mb: 2 }}
                >
                  No clarification questions are available at this time.
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  {isStreamingMode
                    ? "The AI streaming service will continue processing your request automatically."
                    : isDraftMode
                    ? "You can proceed with your quote generation."
                    : "Please try refreshing or contact support if this issue persists."}
                </Typography>
              </Box>
            )}
          </>
        )}
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose} disabled={isLoading}>
          Cancel
        </Button>
        <Button
          onClick={handleSubmit}
          variant="contained"
          disabled={
            isLoading ||
            !questionsToUse?.length ||
            Object.values(answers).some((a) => !a.trim()) ||
            (!isDraftMode && !isStreamingMode && !quoteId)
          }
        >
          {isLoading ? "Processing..." : "Submit Answers"}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default AiClarificationDialog;
