/**
 * AI Service Utility
 *
 * This service provides AI-powered capabilities for the Workiz application
 * including smart suggestions, data enrichment, and natural language processing.
 */
import axios from "axios";
import logger from "./logger"; // Assuming a logger utility exists or can be added

// Base API endpoint for AI services
// Safely access environment variables with fallbacks
const getEnvVar = (name, defaultValue) => {
  // Try window first (for runtime injected env vars)
  if (window[name]) return window[name];
  // Then try process.env (for build-time env vars)
  try {
    return process.env[name] || defaultValue;
  } catch (e) {
    // If process is not defined, return default
    return defaultValue;
  }
};

const API_BASE_URL = getEnvVar(
  "REACT_APP_API_BASE_URL",
  "http://localhost:5000/api"
);
const AI_API_ENDPOINT = `${API_BASE_URL}/ai`;

// Create authenticated axios instance
const authAxios = axios.create({
  timeout: parseInt(getEnvVar("REACT_APP_API_TIMEOUT", "30000")), // Use env var, default 30s
});

// Add auth token to requests
authAxios.interceptors.request.use(
  (config) => {
    try {
      const userInfoString = localStorage.getItem("userInfo");
      if (userInfoString) {
        const userInfo = JSON.parse(userInfoString);
        if (userInfo && userInfo.token) {
          config.headers.Authorization = `Bearer ${userInfo.token}`;
        }
      }
    } catch (e) {
      logger.error("Failed to parse userInfo from localStorage", e);
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Removed frontend retry and circuit breaker logic (Issue #43)
// Relying on backend resilience mechanisms and error handling.

/**
 * Generate a business description based on customer data
 * @param {Object} customerData - Customer information (needs at least _id)
 * @returns {Promise<string>} - AI-generated business description
 */
export const generateBusinessDescription = async (customerData) => {
  if (!customerData?._id) {
    logger.error("Customer ID is required for generateBusinessDescription");
    throw new Error("Customer ID is required");
  }
  try {
    // Call backend directly, without frontend retry wrapper
    const response = await authAxios.get(
      `${AI_API_ENDPOINT}/customer/${customerData._id}/description`
    );

    // Expect backend to return { description: "..." } or throw an error
    if (!response.data?.description) {
      logger.warn("Backend returned success but description is missing.");
      throw new Error("Invalid response from AI service (missing description)");
    }
    return response.data.description;
  } catch (error) {
    logger.error("Error generating business description:", {
      message: error.response?.data?.message || error.message,
      status: error.response?.status,
      customerId: customerData._id,
    });
    // Re-throw the error to be handled by the calling component
    throw error;
  }
};

/**
 * Generate customer insights based on historical data
 * @param {Object} customerData - Customer information (needs at least _id)
 * @returns {Promise<Array>} - List of insights
 */
export const generateCustomerInsights = async (customerData) => {
  if (!customerData?._id) {
    logger.error("Customer ID is required for generateCustomerInsights");
    throw new Error("Customer ID is required");
  }
  try {
    // Call backend directly, without frontend retry wrapper
    const response = await authAxios.get(
      `${AI_API_ENDPOINT}/customer/${customerData._id}/insights`
    );

    // Expect backend to return { insights: ["...", "..."] } or throw an error
    if (!response.data || !Array.isArray(response.data.insights)) {
      logger.warn(
        "Backend returned success but insights array is missing or invalid."
      );
      throw new Error(
        "Invalid response from AI service (invalid insights format)"
      );
    }
    return response.data.insights;
  } catch (error) {
    logger.error("Error generating customer insights:", {
      message: error.response?.data?.message || error.message,
      status: error.response?.status,
      customerId: customerData._id,
    });
    // Re-throw the error
    throw error;
  }
};

/**
 * Suggest relevant services based on customer profile
 * @param {Object} customerData - Customer information (needs at least _id)
 * @returns {Promise<Array>} - List of suggested services
 */
export const suggestServices = async (customerData) => {
  if (!customerData?._id) {
    logger.error("Customer ID is required for suggestServices");
    throw new Error("Customer ID is required");
  }
  try {
    // Call backend directly, without frontend retry wrapper
    const response = await authAxios.get(
      `${AI_API_ENDPOINT}/customer/${customerData._id}/services`
    );

    // Expect backend to return { services: ["...", "..."] } or throw an error
    if (!response.data || !Array.isArray(response.data.services)) {
      logger.warn(
        "Backend returned success but services array is missing or invalid."
      );
      throw new Error(
        "Invalid response from AI service (invalid services format)"
      );
    }
    return response.data.services;
  } catch (error) {
    logger.error("Error suggesting services:", {
      message: error.response?.data?.message || error.message,
      status: error.response?.status,
      customerId: customerData._id,
    });
    // Re-throw the error
    throw error;
  }
};

/**
 * Process and categorize customer feedback
 * @param {string} feedback - Customer feedback text
 * @returns {Promise<Object>} - Processed feedback with sentiment and categories
 */
export const processFeedback = async (feedback) => {
  try {
    // Get auth token from localStorage
    const userInfo = localStorage.getItem("userInfo");
    const token = userInfo ? JSON.parse(userInfo).token : "";

    const config = {
      headers: token ? { Authorization: `Bearer ${token}` } : {},
    };

    // Call backend AI feedback processing endpoint
    const { data } = await axios.post(
      "/api/ai/feedback/process",
      { feedback },
      config
    );

    return {
      sentiment: data.sentiment || "neutral",
      confidence: data.confidence || 0.5,
      categories: data.categories || [],
      keywords: data.keywords || [],
      actionItems: data.actionItems || [],
      processed: true,
    };
  } catch (error) {
    logger.error(
      "Backend feedback processing failed, falling back to frontend simulation:",
      error
    );

    // Fallback to simple sentiment analysis simulation
    const lowerFeedback = feedback.toLowerCase();
    const positiveWords = [
      "good",
      "great",
      "excellent",
      "amazing",
      "happy",
      "satisfied",
      "love",
      "like",
    ];
    const negativeWords = [
      "bad",
      "poor",
      "terrible",
      "horrible",
      "unhappy",
      "dissatisfied",
      "hate",
      "dislike",
    ];

    let sentiment = "neutral";
    let positiveCount = positiveWords.filter((word) =>
      lowerFeedback.includes(word)
    ).length;
    let negativeCount = negativeWords.filter((word) =>
      lowerFeedback.includes(word)
    ).length;

    if (positiveCount > negativeCount) sentiment = "positive";
    else if (negativeCount > positiveCount) sentiment = "negative";

    // Simple category extraction
    const categories = [];
    if (lowerFeedback.includes("service") || lowerFeedback.includes("support"))
      categories.push("Customer Service");
    if (lowerFeedback.includes("quality") || lowerFeedback.includes("product"))
      categories.push("Product Quality");
    if (
      lowerFeedback.includes("price") ||
      lowerFeedback.includes("cost") ||
      lowerFeedback.includes("expensive")
    )
      categories.push("Pricing");
    if (
      lowerFeedback.includes("delivery") ||
      lowerFeedback.includes("time") ||
      lowerFeedback.includes("schedule")
    )
      categories.push("Timing/Delivery");
    if (categories.length === 0) categories.push("General Feedback");

    return { originalText: feedback, sentiment, categories, processed: true };
  }
};

/**
 * Smart follow-up suggestions based on customer interaction
 * @param {Object} customerData - Customer information (needs at least _id)
 * @param {Object} interaction - Details of the last interaction
 * @returns {Promise<Object>} - Follow-up suggestions
 */
export const getFollowUpSuggestions = async (customerData, interaction) => {
  if (!customerData?._id) {
    logger.error("Customer ID is required for getFollowUpSuggestions");
    throw new Error("Customer ID is required");
  }
  try {
    // Call backend directly, without frontend retry wrapper
    const response = await authAxios.post(
      `${AI_API_ENDPOINT}/customer/${customerData._id}/followup`,
      { interaction } // Send interaction data in the body
    );

    // Expect backend to return a specific structure or throw an error
    if (
      !response.data ||
      !response.data.messageTemplate ||
      !response.data.suggestedFollowUpDate
    ) {
      logger.warn(
        "Backend returned success but follow-up data is missing or invalid."
      );
      throw new Error(
        "Invalid response from AI service (invalid follow-up format)"
      );
    }
    return response.data;
  } catch (error) {
    logger.error("Error generating follow-up suggestions:", {
      message: error.response?.data?.message || error.message,
      status: error.response?.status,
      customerId: customerData._id,
    });
    // Re-throw the error
    throw error;
  }
};

/**
 * Filter customers using AI based on natural language query
 * @param {string} query - Natural language query for filtering
 * @returns {Promise<Array>} - Filtered list of customer objects from backend
 */
export const filterCustomersByAI = async (query) => {
  if (!query) {
    logger.error("Query is required for filterCustomersByAI");
    throw new Error("Query is required");
  }
  try {
    // Call backend directly, without frontend retry wrapper
    const response = await authAxios.post(
      `${AI_API_ENDPOINT}/customers/filter`,
      { query }
    );

    // Expect backend to return { customers: [...] } or throw an error
    if (!response.data || !Array.isArray(response.data.customers)) {
      logger.warn(
        "Backend returned success but customer list is missing or invalid."
      );
      throw new Error(
        "Invalid response from AI service (invalid customer filter format)"
      );
    }
    return response.data.customers; // Return the customer list from backend
  } catch (error) {
    logger.error("Error filtering customers by AI:", {
      message: error.response?.data?.message || error.message,
      status: error.response?.status,
      query,
    });
    // Re-throw the error
    throw error;
  }
};

/**
 * Enhance job description using AI
 * @param {string} description - Job description to enhance
 * @param {string} title - Job title (optional)
 * @param {string} jobType - Job type (optional)
 * @returns {Promise<Object>} - Enhanced description and metadata
 */
export const enhanceJobDescription = async (
  description,
  title = "",
  jobType = "Standard"
) => {
  if (!description) {
    logger.error("Description is required for enhanceJobDescription");
    throw new Error("Description is required");
  }
  try {
    const response = await authAxios.post(
      `${AI_API_ENDPOINT}/enhance-description`,
      {
        description,
        title,
        jobType,
      }
    );

    if (!response.data) {
      logger.warn("Backend returned success but enhancement data is missing.");
      throw new Error(
        "Invalid response from AI service (missing enhancement data)"
      );
    }
    return response.data;
  } catch (error) {
    logger.error("Error enhancing job description:", {
      message: error.response?.data?.message || error.message,
      status: error.response?.status,
      description: description.substring(0, 50),
    });
    throw error;
  }
};

/**
 * Analyze complete job for title and duration suggestions
 * @param {string} description - Job description to analyze
 * @param {string} title - Job title (optional)
 * @returns {Promise<Object>} - Job analysis with suggestions
 */
export const analyzeCompleteJob = async (description, title = "") => {
  if (!description) {
    logger.error("Description is required for analyzeCompleteJob");
    throw new Error("Description is required");
  }
  try {
    const response = await authAxios.post(
      `${AI_API_ENDPOINT}/analyze-complete-job`,
      {
        description,
        title,
      }
    );

    if (!response.data) {
      logger.warn("Backend returned success but analysis data is missing.");
      throw new Error(
        "Invalid response from AI service (missing analysis data)"
      );
    }
    return response.data;
  } catch (error) {
    logger.error("Error analyzing complete job:", {
      message: error.response?.data?.message || error.message,
      status: error.response?.status,
      description: description.substring(0, 50),
    });
    throw error;
  }
};

/**
 * Analyze job data for AI suggestions
 * @param {string} description - Job description to analyze
 * @param {string} title - Job title (optional)
 * @returns {Promise<Object>} - Job analysis with AI suggestions
 */
export const analyzeJob = async (description, title = "") => {
  if (!description) {
    logger.error("Description is required for analyzeJob");
    throw new Error("Description is required");
  }
  try {
    const response = await authAxios.post(`${AI_API_ENDPOINT}/analyze-job`, {
      description,
      title,
    });

    if (!response.data) {
      logger.warn("Backend returned success but analysis data is missing.");
      throw new Error(
        "Invalid response from AI service (missing analysis data)"
      );
    }
    return response.data;
  } catch (error) {
    logger.error("Error analyzing job:", {
      message: error.response?.data?.message || error.message,
      status: error.response?.status,
      description: description.substring(0, 50),
    });
    throw error;
  }
};

// Removed getServiceHealth and resetCircuitBreaker as they related to the removed frontend resilience logic

// Create a service object containing all exported functions
const aiService = {
  generateBusinessDescription,
  generateCustomerInsights,
  suggestServices,
  processFeedback, // Keeping simulated version until backend is implemented
  getFollowUpSuggestions,
  filterCustomersByAI,
  enhanceJobDescription,
  analyzeCompleteJob,
  analyzeJob,
  // Removed getServiceHealth, resetCircuitBreaker
};

export default aiService;
