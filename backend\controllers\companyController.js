const Company = require("../models/Company");
const logger = require("../utils/logger");

// @desc    Create company info
// @route   POST /api/company
// @access  Private/Admin
const createCompany = async (req, res) => {
  try {
    // Check if a company already exists
    const existingCompany = await Company.findOne({});
    if (existingCompany) {
      logger.warn(
        `Company creation attempt failed - company already exists. Attempted by: ${req.user.email}`
      );
      return res
        .status(400)
        .json({ message: "Company already exists. Use update instead." });
    }

    // Create the company with the current user as creator
    const company = await Company.create({
      ...req.body,
      createdBy: req.user._id,
      updatedBy: req.user._id,
    });

    logger.config(`Company created by ${req.user.email}`);
    logger.persistence(`Company data persisted: ${company.name}`);

    res.status(201).json(company);
  } catch (error) {
    logger.error(`Company creation error: ${error.message}`);
    res.status(500).json({ message: "Server error" });
  }
};

// @desc    Get company info
// @route   GET /api/company
// @access  Private
const getCompany = async (req, res) => {
  try {
    const company = await Company.getPrimaryCompany();

    if (!company) {
      return res.status(404).json({ message: "Company information not found" });
    }

    logger.info(`Company information retrieved by ${req.user.email}`);
    res.json(company);
  } catch (error) {
    logger.error(`Company retrieval error: ${error.message}`);
    res.status(500).json({ message: "Server error" });
  }
};

// @desc    Update company info
// @route   PUT /api/company
// @access  Private/Admin
const updateCompany = async (req, res) => {
  try {
    // Get the current company
    let company = await Company.getPrimaryCompany();

    if (!company) {
      logger.warn(
        `Company update failed - no company exists. Attempted by: ${req.user.email}`
      );
      return res
        .status(404)
        .json({ message: "Company not found. Create first." });
    }

    // Log previous values
    const previousValues = {
      name: company.name,
      email: company.email,
      phone: company.phone,
      address: company.address,
    };

    // Update fields
    Object.keys(req.body).forEach((key) => {
      company[key] = req.body[key];
    });

    // Update the updatedBy field
    company.updatedBy = req.user._id;

    // Save the updated company
    company = await company.save();

    logger.config(
      `Company information updated by ${req.user.email}: ${JSON.stringify(
        Object.keys(req.body)
      )}`
    );
    logger.persistence(`Company data persisted after update: ${company.name}`);

    res.json(company);
  } catch (error) {
    logger.error(`Company update error: ${error.message}`);
    res.status(500).json({ message: "Server error" });
  }
};

module.exports = {
  createCompany,
  getCompany,
  updateCompany,
};
