const mongoose = require("mongoose");
const dotenv = require("dotenv");
const Company = require("../models/Company");
const User = require("../models/User");
const connectDB = require("../config/database");

// Load environment variables
dotenv.config();

// Sample company data
const companyData = {
  name: "Workiz Demo Company",
  email: "<EMAIL>",
  phone: "************",
  address: {
    street: "123 Business Street",
    city: "San Francisco",
    state: "CA",
    zipCode: "94105",
    country: "US",
  },
  website: "https://workizdemo.com",
  taxId: "12-3456789",
  businessType: "HVAC",
  primaryServices: [
    "Air Conditioning Installation",
    "Heating System Repair",
    "HVAC Maintenance",
    "Duct Cleaning",
    "System Diagnostics",
  ],
};

// Connect to database
connectDB();

// Seed function
const seedCompany = async () => {
  try {
    // Clear existing companies
    await Company.deleteMany({});
    console.log("Deleted existing companies");

    // Find the admin user to set as creator
    const adminUser = await User.findOne({ email: "<EMAIL>" });
    if (!adminUser) {
      console.error("Admin user not found. Please run userSeeder first.");
      process.exit(1);
    }

    // Create company with admin as creator
    const companyWithCreator = {
      ...companyData,
      createdBy: adminUser._id,
    };

    const company = await Company.create(companyWithCreator);
    console.log(`Created company: ${company.name}`);

    // Update all users to be associated with this company
    const updateResult = await User.updateMany({}, { company: company._id });

    console.log(
      `Updated ${updateResult.modifiedCount} users with company association`
    );

    console.log("\nCompany seeding completed successfully!");
    console.log(`Company ID: ${company._id}`);
    console.log(`Company Name: ${company.name}`);

    // Exit with success
    process.exit(0);
  } catch (error) {
    console.error(`Error: ${error.message}`);
    process.exit(1);
  }
};

// Run the seeder
seedCompany();
