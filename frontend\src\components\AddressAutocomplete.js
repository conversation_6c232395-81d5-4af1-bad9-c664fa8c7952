import React, { useState, useEffect, useCallback, useRef } from "react";
import { TextField, Box, Autocomplete } from "@mui/material";
import LocationOnIcon from "@mui/icons-material/LocationOn";
import axios from "axios"; // Import base axios

// Create authenticated axios instance at module level
const authAxios = axios.create({
  timeout: 10000, // 10 second timeout for autocomplete requests
});

// Add auth token interceptor at module level
authAxios.interceptors.request.use(
  (config) => {
    const userInfo = JSON.parse(localStorage.getItem("userInfo"));
    if (userInfo && userInfo.token) {
      config.headers.Authorization = `Bearer ${userInfo.token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

/**
 * AddressAutocomplete component for standardized address input across the application
 *
 * @param {Object} props Component props
 * @param {string} props.value Current address value
 * @param {Function} props.onChange Callback when address changes
 * @param {string} props.label Label for input field
 * @param {Object} props.textFieldProps Additional props for TextField
 * @param {string} props.country Country restriction (e.g., 'us', 'ca')
 * @returns {JSX.Element} AddressAutocomplete component
 */
const AddressAutocomplete = ({
  value = "",
  onChange,
  onAddressSelected,
  label = "Address",
  textFieldProps = {},
  country = "us",
}) => {
  // Remove unused hook call
  const [addressSearchTerm, setAddressSearchTerm] = useState(value);
  const [addressPredictions, setAddressPredictions] = useState([]);
  const isMountedRef = useRef(true);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      isMountedRef.current = false;
    };
  }, []);

  // Axios instance moved outside component

  // Update internal state when external value changes
  useEffect(() => {
    if (isMountedRef.current) {
      setAddressSearchTerm(value);
    }
  }, [value]);

  // Renamed line numbers due to insertion above
  const fetchAddressPredictions = useCallback(
    async (searchInput) => {
      // Line 50
      if (!searchInput || searchInput.length < 3) {
        // Basic validation
        if (isMountedRef.current) {
          setAddressPredictions([]);
        }
        return;
      }

      try {
        // Use the locally defined authAxios instance
        const response = await authAxios.get("/api/maps/autocomplete", {
          // Line 58
          params: {
            input: searchInput,
            country: country,
          },
        });

        // Only update state if component is still mounted
        if (isMountedRef.current) {
          if (response.data && Array.isArray(response.data.predictions)) {
            // The backend now returns the predictions array directly
            setAddressPredictions(response.data.predictions); // Line 66
          } else {
            console.warn(
              "Received unexpected format from autocomplete backend:",
              response.data
            );
            setAddressPredictions([]);
          }
        }
      } catch (error) {
        console.error(
          "Error fetching address predictions from backend:",
          error
        );
        if (isMountedRef.current) {
          setAddressPredictions([]); // Clear predictions on error
        }
      }
    },
    [country]
  ); // Dependency is now only 'country'

  // Debounce search input to avoid too many API calls
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (addressSearchTerm && addressSearchTerm.length > 3) {
        fetchAddressPredictions(addressSearchTerm);
      }
    }, 300);

    return () => clearTimeout(timeoutId);
  }, [addressSearchTerm, fetchAddressPredictions]);

  const handlePlaceSelect = async (placeId) => {
    // Make async
    // Check for Place API availability
    if (!placeId || !window.google?.maps?.places?.Place) {
      console.error("Place ID or Google Maps Place API not available");
      return;
    }

    const placeInstance = new window.google.maps.places.Place({ id: placeId });

    try {
      // Use await for the promise returned by fetchFields
      // Note: Field names are camelCase in the new API response
      const { place } = await placeInstance.fetchFields({
        fields: ["addressComponents", "formattedAddress"],
      });

      if (place) {
        const addressComponents = place.addressComponents;
        let street = "";
        let city = "";
        let state = "";
        let zipCode = "";

        // Extract address components (using longText/shortText)
        if (addressComponents) {
          for (const component of addressComponents) {
            const types = component.types;
            if (types.includes("street_number")) {
              street = component.longText ?? ""; // Use longText/shortText and nullish coalescing
            } else if (types.includes("route")) {
              street += (street ? " " : "") + (component.longText ?? "");
            } else if (types.includes("locality")) {
              city = component.longText ?? "";
            } else if (types.includes("administrative_area_level_1")) {
              state = component.shortText ?? "";
            } else if (types.includes("postal_code")) {
              zipCode = component.longText ?? "";
            }
          }
        }

        const addressDetails = {
          street,
          city,
          state,
          zipCode,
          fullAddress: place.formattedAddress ?? "", // Use camelCase property
        };

        // Call both callbacks with appropriate data
        if (isMountedRef.current) {
          onChange(place.formattedAddress ?? "");

          if (onAddressSelected) {
            onAddressSelected(addressDetails);
          }

          setAddressSearchTerm(place.formattedAddress ?? "");
        }

        // Log the address details to help with debugging
        console.log("Selected address details:", addressDetails);
      }
    } catch (error) {
      console.error("Error fetching place details:", error);
      // Optionally show an error to the user via snackbar or other means
    }
  };

  return (
    <Autocomplete
      freeSolo
      options={addressPredictions.map((prediction) => prediction.description)}
      value={addressSearchTerm}
      onChange={(event, newValue) => {
        if (isMountedRef.current) {
          setAddressSearchTerm(newValue);
          onChange(newValue);

          const selectedPrediction = addressPredictions.find(
            (p) => p.description === newValue
          );
          if (selectedPrediction) {
            handlePlaceSelect(selectedPrediction.place_id);
          }
        }
      }}
      onInputChange={(event, newInputValue) => {
        if (isMountedRef.current) {
          setAddressSearchTerm(newInputValue);
          onChange(newInputValue);
        }
      }}
      renderInput={(params) => (
        <TextField
          {...params}
          label={label}
          fullWidth
          InputProps={{
            ...params.InputProps,
            startAdornment: (
              <Box sx={{ display: "flex", alignItems: "center" }}>
                <LocationOnIcon color="action" sx={{ mr: 1 }} />
                {params.InputProps.startAdornment}
              </Box>
            ),
          }}
          {...textFieldProps}
        />
      )}
    />
  );
};

export default AddressAutocomplete;
