const Quote = require("../models/Quote");
const logger = require("../utils/logger");
const scraperService = require("../scrapers/ScraperService"); // Import ScraperService
const PriceScrapingLog = require("../models/PriceScrapingLog"); // For logging
const axios = require("axios"); // For making HTTP requests
const { v4: uuidv4 } = require("uuid"); // For generating unique request IDs
const mongoose = require("mongoose");
const {
  parseHomeDepotMarkdown,
  parsePlattMarkdown,
} = require("../utils/parserUtils"); // For parsing markdown content
const Crawl4AIService = require("../scrapers/crawl4ai/crawl4ai-service"); // Added for Crawl4AI integration
const materialFetchLogger = require("../utils/materialFetchLogger"); // Enhanced logging

/**
 * Initiates the internal price lookup process for a given quote item.
 * This function now directly calls the ScraperService to fetch price information.
 *
 * @param {string} quoteId - The ID of the quote.
 * @param {string} itemId - The ID of the item within the quote.
 * @param {object} originalToolParams - Original parameters from the 'internal_price_lookup' trigger
 *                                      (e.g., description, quantity, userId, companyId).
 */
async function initiatePriceLookup(quoteId, itemId, originalToolParams) {
  logger.info(
    `[PriceLookupService] Initiating direct price lookup for item ${itemId} on quote ${quoteId}`,
    { quoteId, itemId, originalToolParams }
  );

  // Start enhanced logging
  materialFetchLogger.startLookup(
    quoteId,
    itemId,
    originalToolParams.description,
    "priceLookupService"
  );

  let quote;
  try {
    materialFetchLogger.logStep(quoteId, itemId, "FETCHING_QUOTE");
    quote = await Quote.findById(quoteId);
    if (!quote) {
      logger.error(`[PriceLookupService] Quote ${quoteId} not found.`);
      materialFetchLogger.logError(
        quoteId,
        itemId,
        new Error("Quote not found")
      );
      return;
    }

    materialFetchLogger.logStep(quoteId, itemId, "FINDING_ITEM");
    const item = quote.items.id(itemId);
    if (!item) {
      logger.error(
        `[PriceLookupService] Item ${itemId} not found in quote ${quoteId}.`
      );
      materialFetchLogger.logError(
        quoteId,
        itemId,
        new Error("Item not found in quote")
      );
      return;
    }

    // Find the lookup_results entry that triggered this internal lookup.
    let lookupEntry = item.lookup_results.find(
      (lr) =>
        lr.status === "pending_internal_price_lookup" &&
        lr.request_data &&
        lr.request_data.tool_id === "internal_price_lookup"
    );

    if (!lookupEntry) {
      lookupEntry = item.lookup_results.find(
        (lr) => lr.status === "pending_internal_price_lookup"
      );
      if (lookupEntry) {
        logger.warn(
          `[PriceLookupService] Found 'pending_internal_price_lookup' entry for item ${itemId} without specific tool_id. Proceeding. Original type: ${lookupEntry.type}`,
          { quoteId, itemId }
        );
      } else {
        // If no entry, create a new one to track this attempt
        const newLookupResult = {
          type: "internal_price_lookup",
          status: "processing_direct_lookup",
          data: {
            price_lookup_flow_state: {
              original_query: item.name || item.description,
              current_stage: "direct_scraper_service_call",
            },
          },
          messages: [
            {
              message: `Price lookup initiated by system for item: ${
                item.name || item.description
              }`,
              timestamp: new Date().toISOString(),
            },
          ],
          timestamp: new Date(),
          source: "ai_suggestion_for_lookup",
        };
        item.lookup_results.push(newLookupResult);
        lookupEntry = item.lookup_results[item.lookup_results.length - 1];
        logger.info(
          `[PriceLookupService] Created new lookup_results entry for item ${itemId} for direct lookup.`,
          { quoteId, itemId }
        );
      }
    }

    const itemDescription =
      originalToolParams.description ||
      originalToolParams.name ||
      item.description ||
      item.name ||
      "unknown_item_query";
    const preferredSource =
      originalToolParams.preferredSourceType || "HOME_DEPOT"; // Or determine from item if available

    lookupEntry.status = "processing_direct_lookup";
    lookupEntry.messages = lookupEntry.messages || [];
    lookupEntry.messages.push({
      message: `Attempting to fetch price for "${itemDescription}" via ScraperService. Preferred: ${preferredSource}`,
      timestamp: new Date().toISOString(),
    });
    lookupEntry.data = lookupEntry.data || {};
    lookupEntry.data.price_lookup_flow_state = {
      original_query: itemDescription,
      current_stage: "scraper_service_invoked",
      preferred_source: preferredSource,
    };

    // Ensure quote is saved before async call to scraperService
    materialFetchLogger.logStep(quoteId, itemId, "SAVING_QUOTE_BEFORE_SCRAPER");
    await quote.save();
    logger.info(
      `[PriceLookupService] Item ${itemId} on quote ${quoteId} updated before calling ScraperService.`,
      { quoteId, itemId }
    );

    // Call ScraperService to fetch the price
    materialFetchLogger.logStep(quoteId, itemId, "CALLING_SCRAPER_SERVICE", {
      description: itemDescription,
      preferredSource: preferredSource,
    });

    const priceResult = await scraperService.fetchPriceForMaterial(
      itemDescription,
      preferredSource,
      {
        userId: originalToolParams.userId, // Pass userId if available
        companyId: quote.company, // Pass companyId from quote
      }
    );

    // Check if priceResult is null (all sources failed)
    if (!priceResult) {
      materialFetchLogger.logStep(quoteId, itemId, "SCRAPER_SERVICE_RETURNED", {
        success: false,
        optionsCount: 0,
        source: "none",
      });
      logger.warn(
        `[PriceLookupService] No price results found for item "${itemDescription}" from any source.`
      );
      materialFetchLogger.logStep(quoteId, itemId, "NO_PRICE_FOUND", {
        message: "All scraping sources failed to return results",
      });

      // Update item with failed status
      await updateQuoteItemLookupStatus(
        quoteId,
        itemId,
        "failed",
        [],
        "No price found from any source"
      );
      return;
    }

    // Log successful scraper service response
    materialFetchLogger.logStep(quoteId, itemId, "SCRAPER_SERVICE_RETURNED", {
      success: priceResult.success || false,
      optionsCount: priceResult.options?.length || 0,
      source: priceResult.source || "unknown",
    });

    // Re-fetch quote and item to ensure we have the latest version after async operation
    quote = await Quote.findById(quoteId);
    if (!quote) {
      logger.error(
        `[PriceLookupService] Quote ${quoteId} not found after scraperService call.`
      );
      // Log to a general system log or a specific PriceScrapingLog if possible
      await PriceScrapingLog.startScrape(null, {
        // null for sourceId if unknown at this point
        query: itemDescription,
        status: "ERROR",
        error: {
          message: `Quote ${quoteId} disappeared post-scrape for item ${itemId}`,
        },
        initiatedBy: originalToolParams.userId,
        company: quote ? quote.company : null, // companyId might not be available if quote is gone
        methodUsed: "service_error",
      }).then((log) =>
        log.fail(
          new Error(
            `Quote ${quoteId} disappeared post-scrape for item ${itemId}`
          )
        )
      );
      return;
    }
    const freshItem = quote.items.id(itemId);
    if (!freshItem) {
      logger.error(
        `[PriceLookupService] Item ${itemId} not found in quote ${quoteId} after scraperService call.`
      );
      await PriceScrapingLog.startScrape(null, {
        query: itemDescription,
        status: "ERROR",
        error: {
          message: `Item ${itemId} disappeared post-scrape from quote ${quoteId}`,
        },
        initiatedBy: originalToolParams.userId,
        company: quote.company,
        methodUsed: "service_error",
      }).then((log) =>
        log.fail(
          new Error(
            `Item ${itemId} disappeared post-scrape from quote ${quoteId}`
          )
        )
      );
      return;
    }

    // Find the lookup entry again on the fresh item
    let freshLookupEntry = freshItem.lookup_results.id(lookupEntry._id);
    if (!freshLookupEntry) {
      // If the specific entry is gone, try to find one that matches the context
      freshLookupEntry = freshItem.lookup_results.find(
        (lr) =>
          lr.data &&
          lr.data.price_lookup_flow_state &&
          lr.data.price_lookup_flow_state.original_query === itemDescription &&
          lr.status === "processing_direct_lookup" // Status before it was updated
      );
      if (!freshLookupEntry) {
        logger.error(
          `[PriceLookupService] CRITICAL: Lookup entry for item ${itemId} (original query: ${itemDescription}) not found after scraperService call. Cannot update status.`
        );
        // Log this critical failure
        await PriceScrapingLog.startScrape(null, {
          query: itemDescription,
          status: "ERROR",
          error: {
            message: `Lookup entry for item ${itemId} lost post-scrape`,
          },
          initiatedBy: originalToolParams.userId,
          company: quote.company,
          methodUsed: "service_error",
        }).then((log) =>
          log.fail(
            new Error(`Lookup entry for item ${itemId} lost post-scrape`)
          )
        );
        return;
      }
      logger.warn(
        `[PriceLookupService] Original lookup entry ID ${lookupEntry._id} not found, but matched by content. Proceeding with update.`
      );
    }

    if (priceResult && priceResult.success) {
      logger.info(
        `[PriceLookupService] Price found for item ${itemId}: ${
          priceResult.price
        } ${priceResult.currency || "USD"}. Source: ${priceResult.sourceName}`
      );
      freshItem.price = priceResult.price;
      freshItem.currency = priceResult.currency || "USD"; // Default to USD if not provided
      if (priceResult.sku) freshItem.sku = priceResult.sku;
      if (priceResult.url) freshItem.url = priceResult.url;
      if (priceResult.name) freshItem.name = priceResult.name; // Update name if scraper found a more specific one
      if (priceResult.description)
        freshItem.description = priceResult.description; // Update description
      freshItem.source = priceResult.sourceName || "Unknown Scraper";
      freshItem.sourceId = priceResult.sourceId || null;

      freshLookupEntry.status = "price_lookup_success";
      freshLookupEntry.data = {
        price: priceResult.price,
        currency: priceResult.currency || "USD",
        sku: priceResult.sku,
        url: priceResult.url,
        sourceName: priceResult.sourceName,
        sourceId: priceResult.sourceId,
      };
      freshLookupEntry.messages.push({
        message: `Successfully fetched price: ${priceResult.price} ${
          priceResult.currency || "USD"
        } from ${priceResult.sourceName}.`,
        timestamp: new Date().toISOString(),
      });
      freshLookupEntry.data.price_lookup_flow_state.current_stage =
        "completed_success";
    } else {
      logger.warn(
        `[PriceLookupService] Price not found for item ${itemId} via ScraperService for "${itemDescription}".`
      );
      freshLookupEntry.status = "price_lookup_failed";
      freshLookupEntry.messages.push({
        message: `Failed to fetch price for "${itemDescription}" after trying all sources.`,
        timestamp: new Date().toISOString(),
      });
      freshLookupEntry.data.price_lookup_flow_state.current_stage =
        "completed_failure";
    }

    freshLookupEntry.request_data = null; // Clear request data as this flow is now internal
    freshLookupEntry.timestamp = new Date().toISOString();

    materialFetchLogger.logStep(quoteId, itemId, "SAVING_FINAL_RESULTS");
    await quote.save();
    logger.info(
      `[PriceLookupService] Price lookup for item ${itemId} finalized with status: ${freshLookupEntry.status}.`
    );

    // Complete the lookup logging
    materialFetchLogger.completeLookup(quoteId, itemId, {
      status: freshLookupEntry.status,
      optionsCount: priceResult.options?.length || 0,
      source: priceResult.sourceName || "Unknown",
    });
  } catch (error) {
    logger.error(
      `[PriceLookupService] Error in initiatePriceLookup (direct) for item ${itemId} on quote ${quoteId}: ${error.message}`,
      { quoteId, itemId, stack: error.stack }
    );
    materialFetchLogger.logError(quoteId, itemId, error, {
      stage: "initiatePriceLookup",
    });
    // Attempt to mark the original lookup as failed
    try {
      const errorQuote = await Quote.findById(quoteId);
      if (errorQuote) {
        const errorItem = errorQuote.items.id(itemId);
        if (errorItem) {
          let errorLookupEntry = errorItem.lookup_results.find(
            (lr) =>
              lr.status === "processing_direct_lookup" ||
              lr.status === "pending_internal_price_lookup"
          );
          if (errorLookupEntry) {
            errorLookupEntry.status = "price_lookup_orchestration_error";
            errorLookupEntry.messages = errorLookupEntry.messages || [];
            errorLookupEntry.messages.push({
              message: `Failed during direct price lookup orchestration: ${error.message}`,
              timestamp: new Date().toISOString(),
            });
            await errorQuote.save();
          }
        }
      }
    } catch (saveError) {
      logger.error(
        `[PriceLookupService] CRITICAL: Failed to save error status for item ${itemId} during direct lookup failure: ${saveError.message}`,
        { quoteId, itemId }
      );
    }
  }
}

async function fetchMaterialDetailsFromAIList(
  quoteId,
  aiGeneratedMaterialText
) {
  logger.info(
    `[PriceLookupService] Starting fetchMaterialDetailsFromAIList for quote ${quoteId}`
  );

  // Placeholder for parsing aiGeneratedMaterialText into a list of descriptions
  // For now, using a hardcoded list for testing
  const materialDescriptions = [
    "1/2 inch EMT conduit",
    "NEMA 3R 200A meter socket",
    "500 kcmil copper THHN/THWN-2 wire",
  ];
  // In a real scenario, this would come from parsing aiGeneratedMaterialText
  // and associating with specific item IDs in the quote.

  const quote = await Quote.findById(quoteId);
  if (!quote) {
    logger.error(
      `[PriceLookupService] fetchMaterialDetailsFromAIList: Quote ${quoteId} not found.`
    );
    return;
  }

  for (const description of materialDescriptions) {
    // Find or create a placeholder item in the quote for this description if it doesn't exist.
    // This part needs careful handling to map descriptions to actual quote items.
    // For this initial test, we'll assume we'd update an existing item or log to a general place.
    logger.info(
      `[PriceLookupService] Processing AI material: "${description}" for quote ${quoteId}`
    );

    const mcpPayload = {
      query: `${description} site:homedepot.com`, // Example: targeting Home Depot
      count: 3, // Limit to 3 search results for now
    };

    try {
      // const mcpToolUrl = 'http://mcp-server:3001/api/mcp/execute'; // DISABLED: MCP server removed
      logger.info(
        `[PriceLookupService] Calling MCP tool: mcp4_brave_web_search for query: "${mcpPayload.query}"`
      );

      // Use the MCP_API_KEY_JWT for authentication
      const mcpApiKeyJwt = process.env.MCP_API_KEY_JWT;
      if (!mcpApiKeyJwt) {
        logger.error(
          "[PriceLookupService] MCP_API_KEY_JWT environment variable is not set!"
        );
        throw new Error("MCP API Key JWT is missing");
      }

      const headers = {
        "Content-Type": "application/json",
        "x-api-key": mcpApiKeyJwt, // Using API key JWT for authentication
      };

      // Format the payload for the /api/mcp/execute endpoint
      const executePayload = {
        toolName: "mcp4_brave_web_search",
        parameters: mcpPayload,
      };

      const mcpResponse = await axios.post(mcpToolUrl, executePayload, {
        headers,
      });

      logger.info(
        `[PriceLookupService] MCP response received for "${description}"`
      );

      // The response from /api/mcp/execute has a different structure
      // It contains { success: true, data: {...}, executionTime: '123ms' }
      if (
        mcpResponse.data &&
        mcpResponse.data.success &&
        mcpResponse.data.data
      ) {
        const searchResults = mcpResponse.data.data;
        logger.debug(
          `[PriceLookupService] MCP execution time: ${mcpResponse.data.executionTime}`
        );

        if (Array.isArray(searchResults)) {
          // Process the search results
          logger.info(
            `[PriceLookupService] Found ${searchResults.length} results for "${description}"`
          );

          // For each result: extract name, price, URL, image URL
          searchResults.forEach((result, index) => {
            logger.info(
              `[PriceLookupService] Result ${
                index + 1
              } for "${description}": Title: ${result.title}, URL: ${
                result.url
              }, Markdown: ${
                result.markdown
                  ? result.markdown.substring(0, 100) + "..."
                  : "N/A"
              }`
            );
            // Placeholder: Update quote item logic here
            // const itemToUpdate = quote.items.find(it => it.description_raw_ai === description); // Need a way to link back
            // if(itemToUpdate) { ... update itemToUpdate.lookup_results or item fields ... }
          });

          // If we found results, we could return them here for further processing
          return searchResults;
        } else {
          logger.warn(
            `[PriceLookupService] MCP search results for "${description}" was not an array:`,
            searchResults
          );
        }
      } else {
        logger.warn(
          `[PriceLookupService] MCP response for "${description}" was unsuccessful or malformed:`,
          mcpResponse.data
        );
      }
    } catch (error) {
      let errorMessage = error.message;
      if (error.response) {
        // Axios error with response from server
        errorMessage = `Status: ${
          error.response.status
        }, Data: ${JSON.stringify(error.response.data)}`;
      }
      logger.error(
        `[PriceLookupService] Error calling MCP for "${description}": ${errorMessage}`,
        { stack: error.stack }
      );
      // Placeholder: Update quote item status to 'mcp_call_failed'
    }
  }
  // await quote.save(); // Save changes after processing all descriptions
  logger.info(
    `[PriceLookupService] Finished fetchMaterialDetailsFromAIList for quote ${quoteId}`
  );
}

/**
 * Stores an MCP request's metadata for later retrieval when results come back
 * @param {Object} metadata - Metadata about the request, including quoteId and itemId
 * @returns {Promise}
 */
async function storeMcpRequestMetadata(metadata) {
  try {
    // Create a new entry in the PriceScrapingLog for this request
    const requestLog = new PriceScrapingLog({
      requestId: metadata.requestId,
      quoteId: metadata.quoteId,
      itemId: metadata.itemId,
      toolId: metadata.toolId,
      toolParams: metadata.toolParams,
      status: "pending",
      createdAt: new Date(),
    });

    await requestLog.save();
    logger.info(
      `[PriceLookupService] Stored MCP request metadata for request ${metadata.requestId}`,
      {
        quoteId: metadata.quoteId,
        itemId: metadata.itemId,
        toolId: metadata.toolId,
      }
    );

    return metadata.requestId;
  } catch (error) {
    logger.error(
      `[PriceLookupService] Error storing MCP request metadata: ${error.message}`,
      { stack: error.stack }
    );
    throw error;
  }
}

/**
 * Retrieves an MCP request's metadata from the database
 * @param {string} requestId - The unique ID of the request
 * @returns {Promise<Object>} - The request metadata
 */
async function getMcpRequestMetadata(requestId) {
  try {
    const requestLog = await PriceScrapingLog.findOne({ requestId });

    if (!requestLog) {
      logger.warn(
        `[PriceLookupService] No request metadata found for requestId ${requestId}`
      );
      return null;
    }

    return {
      requestId: requestLog.requestId,
      quoteId: requestLog.quoteId,
      itemId: requestLog.itemId,
      toolId: requestLog.toolId,
      toolParams: requestLog.toolParams,
      status: requestLog.status,
    };
  } catch (error) {
    logger.error(
      `[PriceLookupService] Error retrieving MCP request metadata: ${error.message}`,
      { stack: error.stack }
    );
    return null;
  }
}

/**
 * Parse results from the Brave web search MCP tool
 * @param {Object} results - The raw search results from Brave
 * @returns {Array} - Array of standardized material options
 */
function parseBraveSearchResults(results) {
  try {
    if (!results || !results.organic || !Array.isArray(results.organic)) {
      logger.warn("[PriceLookupService] Invalid Brave search results format");
      return [];
    }

    const options = [];
    // Process organic search results
    results.organic.forEach((item) => {
      if (item.title && item.url) {
        const option = {
          name: item.title,
          description: item.description || "",
          price: extractPriceFromText(item.description || item.title || ""),
          numerical_price: extractNumericPrice(
            item.description || item.title || ""
          ),
          imageUrl: item.thumbnail || "",
          productUrl: item.url,
          source: "brave_search",
          available: true, // Assume available by default
          metadata: {
            position: item.position,
            age: item.age || null,
            searchEngine: "brave",
          },
        };
        options.push(option);
      }
    });

    logger.info(
      `[PriceLookupService] Parsed ${options.length} options from Brave search results`
    );
    return options;
  } catch (error) {
    logger.error(
      `[PriceLookupService] Error parsing Brave search results: ${error.message}`,
      { stack: error.stack }
    );
    return [];
  }
}

/**
 * Parse results from the Firecrawl search MCP tool
 * @param {Object} results - The raw search results from Firecrawl
 * @returns {Array} - Array of standardized material options
 */
function parseFirecrawlSearchResults(results) {
  try {
    if (!results || !Array.isArray(results)) {
      logger.warn(
        "[PriceLookupService] Invalid Firecrawl search results format"
      );
      return [];
    }

    const options = [];
    results.forEach((item) => {
      if (item.title && item.url) {
        // Try to parse markdown content for specific product information
        let parsedMarkdownResults = [];
        if (item.markdown) {
          // Check if this is from a known source and use appropriate parser
          if (item.url.includes("homedepot.com")) {
            parsedMarkdownResults = parseHomeDepotMarkdown(
              item.markdown,
              "HOME_DEPOT"
            );
          } else if (item.url.includes("platt.com")) {
            parsedMarkdownResults = parsePlattMarkdown(item.markdown, "PLATT");
          }
        }

        // If we got specific product data from the markdown, use that
        if (parsedMarkdownResults && parsedMarkdownResults.length > 0) {
          parsedMarkdownResults.forEach((parsedItem) => {
            options.push({
              name: parsedItem.name,
              description: item.title, // Use the page title as fallback description
              price: parsedItem.price
                ? `$${parsedItem.price.toFixed(2)}`
                : extractPriceFromText(item.title),
              numerical_price:
                parsedItem.price || extractNumericPrice(item.title),
              imageUrl: parsedItem.imageUrl || "",
              productUrl: parsedItem.url || item.url,
              sku: parsedItem.sku || "",
              source: parsedItem.source || "firecrawl_search",
              available: true,
              metadata: {
                markdown: item.markdown ? item.markdown.substring(0, 500) : "",
                source_detail: parsedItem.source || "unknown",
              },
            });
          });
        } else {
          // If no specific product data, create a generic entry
          options.push({
            name: item.title,
            description:
              item.description ||
              extractDescriptionFromMarkdown(item.markdown) ||
              "",
            price: extractPriceFromText(item.title || item.markdown || ""),
            numerical_price: extractNumericPrice(
              item.title || item.markdown || ""
            ),
            imageUrl: "", // Firecrawl search doesn't directly return images
            productUrl: item.url,
            source: "firecrawl_search",
            available: true,
            metadata: {
              markdown: item.markdown ? item.markdown.substring(0, 500) : "",
              rawHtml: item.rawHtml
                ? "Available but not displayed"
                : "Not available",
            },
          });
        }
      }
    });

    logger.info(
      `[PriceLookupService] Parsed ${options.length} options from Firecrawl search results`
    );
    return options;
  } catch (error) {
    logger.error(
      `[PriceLookupService] Error parsing Firecrawl search results: ${error.message}`,
      { stack: error.stack }
    );
    return [];
  }
}

/**
 * Parse results from the Firecrawl scrape MCP tool
 * @param {Object} results - The raw scrape results from Firecrawl
 * @returns {Array} - Array of standardized material options
 */
function parseFirecrawlScrapeResults(results) {
  try {
    // Firecrawl scrape returns a single item with detailed information
    if (!results || typeof results !== "object") {
      logger.warn(
        "[PriceLookupService] Invalid Firecrawl scrape results format"
      );
      return [];
    }

    const options = [];

    // Determine source based on URL
    let source = "firecrawl_scrape";
    if (results.url) {
      if (results.url.includes("homedepot.com")) source = "HOME_DEPOT";
      else if (results.url.includes("platt.com")) source = "PLATT";
      // Add more source determinations as needed
    }

    // Extract product details from the HTML/markdown
    let productName = results.title || "";
    let productPrice = "";
    let numericPrice = 0;
    let imageUrl = "";
    let description = "";
    let sku = "";

    // If we have HTML, try to extract structured data
    if (results.html || results.rawHtml) {
      const htmlContent = results.html || results.rawHtml;
      // Basic extraction using regex for price
      const priceMatch = htmlContent.match(/\$\s*(\d+\.\d{2})/i);
      if (priceMatch && priceMatch[1]) {
        productPrice = `$${priceMatch[1]}`;
        numericPrice = parseFloat(priceMatch[1]);
      }

      // Try to find image URL
      const imgMatch = htmlContent.match(
        /src=["']([^"']*\.(?:jpg|jpeg|png|gif|webp)[^"']*)["']/i
      );
      if (imgMatch && imgMatch[1]) {
        imageUrl = imgMatch[1];
        // Ensure it's an absolute URL
        if (imageUrl.startsWith("/") && results.url) {
          try {
            const baseUrl = new URL(results.url).origin;
            imageUrl = `${baseUrl}${imageUrl}`;
          } catch (e) {
            logger.warn(
              `[PriceLookupService] Error creating absolute URL for image: ${e.message}`
            );
          }
        }
      }

      // Try to find SKU
      const skuMatch = htmlContent.match(/sku[:\s"']*([A-Z0-9\-]+)/i);
      if (skuMatch && skuMatch[1]) {
        sku = skuMatch[1];
      }

      // Try to extract description
      const descMatch = htmlContent.match(
        /<meta[^>]*name=["']description["'][^>]*content=["']([^"']*)["']/i
      );
      if (descMatch && descMatch[1]) {
        description = descMatch[1];
      }
    }

    // If we have markdown, try to extract information from it as well
    if (results.markdown) {
      // If we didn't get a price from HTML, try from markdown
      if (!productPrice) {
        productPrice = extractPriceFromText(results.markdown);
        numericPrice = extractNumericPrice(results.markdown);
      }

      // If we didn't get a description, extract from markdown
      if (!description) {
        description = extractDescriptionFromMarkdown(results.markdown);
      }
    }

    // Create the option
    options.push({
      name: productName,
      description: description,
      price: productPrice,
      numerical_price: numericPrice,
      imageUrl: imageUrl,
      productUrl: results.url || "",
      sku: sku,
      source: source,
      available: true,
      metadata: {
        markdown: results.markdown ? results.markdown.substring(0, 500) : "",
        source_detail: source,
      },
    });

    logger.info(
      `[PriceLookupService] Parsed ${options.length} options from Firecrawl scrape results`
    );
    return options;
  } catch (error) {
    logger.error(
      `[PriceLookupService] Error parsing Firecrawl scrape results: ${error.message}`,
      { stack: error.stack }
    );
    return [];
  }
}

/**
 * Generic parser for unrecognized result formats
 * @param {Object} results - The raw results from any MCP tool
 * @returns {Array} - Array of standardized material options
 */
function parseGenericResults(results) {
  try {
    // Try to intelligently parse various result formats
    const options = [];

    // If results is an array, try to process each item
    if (Array.isArray(results)) {
      results.forEach((item) => {
        if (typeof item === "object" && item !== null) {
          // Extract the best information we can find
          const name =
            item.title ||
            item.name ||
            item.productName ||
            item.product_name ||
            "";
          const url =
            item.url || item.link || item.productUrl || item.product_url || "";
          const price =
            item.price || extractPriceFromText(JSON.stringify(item));
          const imageUrl =
            item.image ||
            item.imageUrl ||
            item.image_url ||
            item.thumbnail ||
            "";

          if (name && (url || price)) {
            options.push({
              name: name,
              description: item.description || "",
              price:
                typeof price === "string"
                  ? price
                  : price
                  ? `$${price.toFixed(2)}`
                  : "",
              numerical_price:
                typeof price === "number"
                  ? price
                  : extractNumericPrice(price || ""),
              imageUrl: imageUrl,
              productUrl: url,
              source: "generic_mcp",
              available: true,
              metadata: {
                raw_result: JSON.stringify(item).substring(0, 500),
              },
            });
          }
        }
      });
    } else if (typeof results === "object" && results !== null) {
      // Try to handle single object result or object with arrays inside
      // Check for common result structures
      if (results.items && Array.isArray(results.items)) {
        // Process items array
        return parseGenericResults(results.items);
      } else if (results.results && Array.isArray(results.results)) {
        // Process results array
        return parseGenericResults(results.results);
      } else if (results.products && Array.isArray(results.products)) {
        // Process products array
        return parseGenericResults(results.products);
      } else {
        // Try to extract a single item from the object
        const name =
          results.title ||
          results.name ||
          results.productName ||
          results.product_name ||
          "";
        const url =
          results.url ||
          results.link ||
          results.productUrl ||
          results.product_url ||
          "";
        const price =
          results.price || extractPriceFromText(JSON.stringify(results));
        const imageUrl =
          results.image ||
          results.imageUrl ||
          results.image_url ||
          results.thumbnail ||
          "";

        if (name && (url || price)) {
          options.push({
            name: name,
            description: results.description || "",
            price:
              typeof price === "string"
                ? price
                : price
                ? `$${price.toFixed(2)}`
                : "",
            numerical_price:
              typeof price === "number"
                ? price
                : extractNumericPrice(price || ""),
            imageUrl: imageUrl,
            productUrl: url,
            source: "generic_mcp",
            available: true,
            metadata: {
              raw_result: JSON.stringify(results).substring(0, 500),
            },
          });
        }
      }
    }

    logger.info(
      `[PriceLookupService] Parsed ${options.length} options from generic results`
    );
    return options;
  } catch (error) {
    logger.error(
      `[PriceLookupService] Error parsing generic results: ${error.message}`,
      { stack: error.stack }
    );
    return [];
  }
}

/**
 * Helper function to extract price from text
 * @param {string} text - Text to extract price from
 * @returns {string} - Extracted price as a formatted string
 */
function extractPriceFromText(text) {
  if (!text) return "";

  // Try to find price patterns like $XX.XX or $X,XXX.XX
  const priceMatch = text.match(
    /\$\s*(\d{1,3}(?:,\d{3})*\.\d{2}|\d+\.\d{2}|\d+)/i
  );
  return priceMatch ? `$${priceMatch[1]}` : "";
}

/**
 * Helper function to extract numeric price value
 * @param {string} text - Text to extract price from
 * @returns {number} - Extracted price as a number
 */
function extractNumericPrice(text) {
  if (!text) return 0;

  // Try to find price patterns and convert to number
  const priceMatch = text.match(
    /\$\s*(\d{1,3}(?:,\d{3})*\.\d{2}|\d+\.\d{2}|\d+)/i
  );
  if (priceMatch && priceMatch[1]) {
    // Remove commas and convert to float
    return parseFloat(priceMatch[1].replace(/,/g, ""));
  }
  return 0;
}

/**
 * Helper function to extract description from markdown
 * @param {string} markdown - Markdown text
 * @returns {string} - Extracted description
 */
function extractDescriptionFromMarkdown(markdown) {
  if (!markdown) return "";

  // Take the first few sentences as description
  const textOnly = markdown
    .replace(/\[.*?\]\(.*?\)/g, "")
    .replace(/[\*_#]/g, "");
  const sentences = textOnly.split(/[.!?]\s+/);

  // Take up to 3 sentences for description
  return (
    sentences.slice(0, 3).join(". ").trim() +
    (sentences.length > 3 ? "..." : "")
  );
}

/**
 * Processes an MCP search result and stores material options in the quote document
 * @param {string} quoteId - ID of the quote
 * @param {string} itemId - ID of the item in the quote
 * @param {Object} results - The MCP search results
 * @param {string} toolId - The MCP tool ID that produced the results
 * @returns {Promise<Array>} - The processed material options
 */
/**
 * Execute an MCP tool and handle the results
 * @param {string} toolId - The MCP tool ID to execute
 * @param {Object} toolParams - Parameters for the MCP tool
 * @param {Object} metadata - Metadata about the request including quoteId and itemId
 * @returns {Promise<string>} - The request ID
 */
async function executeMcpTool(toolId, toolParams, metadata) {
  try {
    // Add the unique request ID to the metadata
    const requestMetadata = {
      ...metadata,
      requestId: uuidv4(),
      toolId: toolId,
      toolParams: toolParams,
    };

    // Store the request metadata for later retrieval when results come back
    await storeMcpRequestMetadata(requestMetadata);

    // Prepare to make the MCP tool call
    const mcpApiKeyJwt = process.env.MCP_API_KEY_JWT;
    if (!mcpApiKeyJwt) {
      logger.error(
        "[PriceLookupService] MCP_API_KEY_JWT environment variable is not set!"
      );
      throw new Error("MCP API Key JWT is missing");
    }

    // const mcpServerUrl = process.env.MCP_SERVER_URL || 'http://mcp-server:3001'; // DISABLED: MCP server removed
    const mcpToolUrl = `${mcpServerUrl}/api/mcp/execute`;

    const headers = {
      "Content-Type": "application/json",
      "x-api-key": mcpApiKeyJwt,
    };

    // Format the payload for the execute endpoint
    const executePayload = {
      toolName: toolId,
      parameters: toolParams,
      metadata: { requestId: requestMetadata.requestId },
    };

    logger.info(
      `[PriceLookupService] Executing MCP tool ${toolId} with request ID ${requestMetadata.requestId}`
    );

    // Make the MCP API call
    const mcpResponse = await axios.post(mcpToolUrl, executePayload, {
      headers,
    });

    // Handle synchronous responses
    if (
      mcpResponse.data &&
      mcpResponse.data.success &&
      !mcpResponse.data.async
    ) {
      logger.info(
        `[PriceLookupService] Received synchronous MCP response for request ${requestMetadata.requestId}`
      );

      // Process the results right away
      await handleMcpCallback(requestMetadata.requestId, mcpResponse.data.data);
    } else if (
      mcpResponse.data &&
      mcpResponse.data.success &&
      mcpResponse.data.async
    ) {
      logger.info(
        `[PriceLookupService] MCP request ${requestMetadata.requestId} is being processed asynchronously`
      );

      // Update the request status to 'processing'
      await PriceScrapingLog.findOneAndUpdate(
        { requestId: requestMetadata.requestId },
        { $set: { status: "processing", updatedAt: new Date() } }
      );
    } else {
      logger.warn(
        `[PriceLookupService] MCP response for request ${requestMetadata.requestId} did not indicate success`
      );

      // Update the request status to 'failed'
      await PriceScrapingLog.findOneAndUpdate(
        { requestId: requestMetadata.requestId },
        {
          $set: {
            status: "failed",
            error: JSON.stringify(mcpResponse.data),
            updatedAt: new Date(),
          },
        }
      );

      // Update the quote item status
      const { quoteId, itemId } = requestMetadata;
      if (quoteId && itemId) {
        await updateQuoteItemLookupStatus(
          quoteId,
          itemId,
          "error_ai_search",
          [],
          "MCP tool execution failed"
        );
      }
    }

    return requestMetadata.requestId;
  } catch (error) {
    logger.error(
      `[PriceLookupService] Error executing MCP tool ${toolId}: ${error.message}`,
      { stack: error.stack }
    );

    // Update the quote item status if we have the metadata
    if (metadata && metadata.quoteId && metadata.itemId) {
      await updateQuoteItemLookupStatus(
        metadata.quoteId,
        metadata.itemId,
        "error_ai_search",
        [],
        `Error executing MCP tool: ${error.message}`
      );
    }

    throw error;
  }
}

/**
 * Handle MCP callback when results come back
 * @param {string} requestId - The unique ID of the original request
 * @param {Object} results - The MCP tool results
 * @returns {Promise}
 */
async function handleMcpCallback(requestId, results) {
  try {
    logger.info(
      `[PriceLookupService] Handling MCP callback for request ${requestId}`
    );

    // Get the original request details
    const requestMetadata = await getMcpRequestMetadata(requestId);
    if (!requestMetadata) {
      logger.warn(
        `[PriceLookupService] No request metadata found for MCP callback ${requestId}`
      );
      return;
    }

    const { quoteId, itemId, toolId } = requestMetadata;

    // Update the request status to 'completed'
    await PriceScrapingLog.findOneAndUpdate(
      { requestId },
      {
        $set: {
          status: "completed",
          result:
            JSON.stringify(results).length > 10000
              ? "Result too large to store"
              : JSON.stringify(results),
          updatedAt: new Date(),
        },
      }
    );

    // Process the results and store options in the quote
    await processAndStoreMcpResults(quoteId, itemId, results, toolId);

    logger.info(
      `[PriceLookupService] Successfully processed MCP callback for request ${requestId}`
    );
    return true;
  } catch (error) {
    logger.error(
      `[PriceLookupService] Error handling MCP callback for request ${requestId}: ${error.message}`,
      { stack: error.stack }
    );
    return false;
  }
}

/**
 * Handle user selection of a material option
 * @param {string} quoteId - ID of the quote
 * @param {string} itemId - ID of the item in the quote
 * @param {number} optionIndex - Index of the selected option
 * @param {string} userId - ID of the user making the selection
 * @returns {Promise}
 */
async function handleMaterialOptionSelection(
  quoteId,
  itemId,
  optionIndex,
  userId
) {
  try {
    logger.info(
      `[PriceLookupService] Handling material option selection for quote ${quoteId}, item ${itemId}, option index ${optionIndex}`
    );

    // Convert ObjectId string to actual ObjectId if needed
    const quoteObjId =
      typeof quoteId === "string" ? mongoose.Types.ObjectId(quoteId) : quoteId;
    const itemObjId =
      typeof itemId === "string" ? mongoose.Types.ObjectId(itemId) : itemId;

    // Get the quote and item
    const quote = await Quote.findById(quoteObjId);
    if (!quote) {
      logger.error(
        `[PriceLookupService] Quote ${quoteId} not found when selecting material option`
      );
      throw new Error(`Quote ${quoteId} not found`);
    }

    const item = quote.items.id(itemObjId);
    if (!item) {
      logger.error(
        `[PriceLookupService] Item ${itemId} not found in quote ${quoteId} when selecting material option`
      );
      throw new Error(`Item ${itemId} not found in quote ${quoteId}`);
    }

    // Validate option index
    if (!item.material_options || !item.material_options[optionIndex]) {
      logger.error(
        `[PriceLookupService] Invalid option index ${optionIndex} for item ${itemId} in quote ${quoteId}`
      );
      throw new Error(`Invalid option index ${optionIndex}`);
    }

    const selectedOption = item.material_options[optionIndex];

    // Update the item with the selected option
    item.selected_option = {
      ...selectedOption,
      selectedAt: new Date(),
      selectedBy: userId,
    };

    // Update item fields with selected option data
    item.name = selectedOption.name;
    item.description = selectedOption.description || item.description;

    // Update price if available
    if (
      selectedOption.numerical_price &&
      !isNaN(selectedOption.numerical_price)
    ) {
      item.price = selectedOption.numerical_price;
    } else if (selectedOption.price) {
      // Try to extract numeric price from the price string
      const numericPrice = extractNumericPrice(selectedOption.price);
      if (numericPrice > 0) {
        item.price = numericPrice;
      }
    }

    // Update other fields
    if (selectedOption.sku) item.sku = selectedOption.sku;
    if (selectedOption.imageUrl) item.imageUrl = selectedOption.imageUrl;
    if (selectedOption.productUrl) item.url = selectedOption.productUrl;
    if (selectedOption.source) item.source = selectedOption.source;

    // Update status
    item.lookup_status = "price_confirmed_user";

    // Add a lookup result entry
    item.lookup_results = item.lookup_results || [];
    item.lookup_results.push({
      type: "USER_SELECTION",
      status: "success",
      data: selectedOption,
      messages: [
        {
          message: `User selected option: ${selectedOption.name} (${selectedOption.price})`,
          timestamp: new Date(),
        },
      ],
      timestamp: new Date(),
      source: "user_selection",
    });

    // Save the quote
    await quote.save();

    logger.info(
      `[PriceLookupService] Successfully selected option ${optionIndex} for item ${itemId} in quote ${quoteId}`
    );
    return true;
  } catch (error) {
    logger.error(
      `[PriceLookupService] Error selecting material option: ${error.message}`,
      { stack: error.stack }
    );
    throw error;
  }
}

async function processAndStoreMcpResults(
  quoteId,
  itemId,
  results,
  toolId,
  mcpRequestId = null
) {
  try {
    logger.info(
      `[PriceLookupService] Processing MCP results for quote ${quoteId}, item ${itemId}, tool ${toolId}`
    );

    // Parse the results based on the tool type
    let parsedOptions = [];

    if (toolId === "mcp3_firecrawl_search") {
      parsedOptions = parseFirecrawlSearchResults(results);
    } else if (toolId === "mcp4_brave_web_search") {
      logger.info(
        `[PriceLookupService] Processing mcp4_brave_web_search results for item ${itemId} on quote ${quoteId}. Request ID: ${mcpRequestId}`
      );
      const braveSearchResults = parseBraveSearchResults(results);

      if (braveSearchResults && braveSearchResults.length > 0) {
        const firstResult = braveSearchResults[0];
        const selectedUrl = firstResult.productUrl || firstResult.url;

        if (selectedUrl) {
          logger.info(
            `[PriceLookupService] Selected URL from Brave Search: ${selectedUrl} for item ${itemId}. Proceeding with Crawl4AI.`
          );

          // Update status to indicate Crawl4AI is next
          await updateQuoteItemLookupStatus(
            quoteId,
            itemId,
            "pending_crawl4ai_extraction",
            [],
            `Selected URL ${selectedUrl} for Crawl4AI.`
          );

          try {
            const crawlOptions = { output_type: "markdown", timeout: 120000 }; // Default options
            logger.info(
              `[PriceLookupService] Calling Crawl4AIService.crawl for URL: ${selectedUrl} with options:`,
              crawlOptions
            );
            const crawl4aiResult = await Crawl4AIService.crawl(
              selectedUrl,
              crawlOptions
            );
            logger.info(
              `[PriceLookupService] Crawl4AI result for ${selectedUrl}:`,
              {
                success: crawl4aiResult.success,
                hasData: !!crawl4aiResult.data,
              }
            );

            if (crawl4aiResult.success && crawl4aiResult.data) {
              // Store the markdown content from Crawl4AI
              // The actual price parsing from this markdown will be a subsequent step.
              const markdownContent =
                crawl4aiResult.data.markdown ||
                crawl4aiResult.data.text ||
                (typeof crawl4aiResult.data === "string"
                  ? crawl4aiResult.data
                  : "");

              if (markdownContent) {
                logger.info(
                  `[PriceLookupService] Successfully retrieved markdown from Crawl4AI for ${selectedUrl}. Length: ${markdownContent.length}`
                );
                // We need a way to store this raw markdown. For now, let's add it to a new field in lookup_results or data.
                // This part needs careful consideration of data structure.
                // For now, let's assume we update the item with this content directly for simplicity in this step.
                // This might involve creating a new 'material_option' like structure or adding to existing 'messages' or 'data'.

                // Find the quote and item again to update lookup_results specifically for this stage
                const quote = await Quote.findById(quoteId);
                if (quote) {
                  const item = quote.items.id(itemId);
                  if (item) {
                    item.lookup_results.push({
                      type: "CRAWL4AI_MARKDOWN_RESULT",
                      status: "success_crawl4ai_extraction",
                      messages: [
                        {
                          message: `Markdown content retrieved from ${selectedUrl}`,
                          timestamp: new Date().toISOString(),
                        },
                      ],
                      data: {
                        markdown: markdownContent,
                        sourceUrl: selectedUrl,
                      },
                      timestamp: new Date().toISOString(),
                      source: "crawl4ai_service",
                    });
                    // Potentially set overall item status to something like 'pending_markdown_parsing'
                    item.lookup_status = "pending_markdown_parsing";
                    await quote.save();
                    logger.info(
                      `[PriceLookupService] Stored Crawl4AI markdown for item ${itemId}. Ready for parsing.`
                    );

                    // Parse the markdown content immediately to extract product options
                    try {
                      const extractedProductOptions =
                        await this.parseGenericMarkdownForProducts(
                          markdownContent,
                          selectedUrl
                        );

                      if (
                        extractedProductOptions &&
                        extractedProductOptions.length > 0
                      ) {
                        logger.info(
                          `[PriceLookupService] Successfully parsed ${extractedProductOptions.length} product options from markdown for item ${itemId}.`
                        );

                        // Add each extracted option to lookup_results
                        for (const option of extractedProductOptions) {
                          // Re-fetch quote to ensure we have the latest version
                          const updatedQuote = await Quote.findById(quoteId);
                          if (!updatedQuote) {
                            logger.error(
                              `[PriceLookupService] Quote ${quoteId} not found during product option addition.`
                            );
                            continue;
                          }

                          const updatedItem = updatedQuote.items.id(itemId);
                          if (!updatedItem) {
                            logger.error(
                              `[PriceLookupService] Item ${itemId} not found in quote ${quoteId} during product option addition.`
                            );
                            continue;
                          }

                          updatedItem.lookup_results.push({
                            type: "PARSED_CRAWL4AI_OPTION",
                            status: "success_markdown_parsed",
                            timestamp: new Date().toISOString(),
                            source: "crawl4ai_markdown_parser",
                            source_url: option.sourceUrl,
                            data: {
                              name: option.name,
                              price: option.price,
                              sku: option.sku,
                              description: option.description,
                              numerical_price: option.price
                                ? parseFloat(
                                    option.price
                                      .toString()
                                      .replace(/[^0-9.]/g, "")
                                  )
                                : null,
                            },
                            confidence_score: option.confidenceScore,
                            raw_markdown_snippet:
                              option.rawExtractedFields?.blockSnippet || "",
                          });

                          // Update the overall lookup status
                          updatedItem.lookup_status =
                            "success_manual_selection_pending";
                          await updatedQuote.save();
                        }

                        // Log the event for tracking
                        this.logEvent(
                          this.eventEmitter,
                          quoteId,
                          itemId,
                          "PRICE_LOOKUP_MARKDOWN_PARSED",
                          {
                            itemCount: extractedProductOptions.length,
                            sourceUrl: selectedUrl,
                          }
                        );
                      } else {
                        logger.warn(
                          `[PriceLookupService] Markdown parsing yielded no product options for item ${itemId} from URL: ${selectedUrl}.`
                        );

                        // Update the quote item status to reflect the failure
                        const updatedQuote = await Quote.findById(quoteId);
                        if (updatedQuote) {
                          const updatedItem = updatedQuote.items.id(itemId);
                          if (updatedItem) {
                            updatedItem.lookup_status =
                              "failed_markdown_parsing";
                            updatedItem.lookup_results.push({
                              type: "PARSED_CRAWL4AI_OPTION_FAILURE",
                              status: "failed_markdown_parsing",
                              timestamp: new Date().toISOString(),
                              source: "crawl4ai_markdown_parser",
                              source_url: selectedUrl,
                              error_message:
                                "No product options could be extracted from the markdown content.",
                            });
                            await updatedQuote.save();
                          }
                        }

                        this.logEvent(
                          this.eventEmitter,
                          quoteId,
                          itemId,
                          "PRICE_LOOKUP_MARKDOWN_PARSE_FAILED",
                          {
                            sourceUrl: selectedUrl,
                            reason: "No options extracted",
                          }
                        );
                      }
                    } catch (parseError) {
                      logger.error(
                        `[PriceLookupService] Error parsing markdown for item ${itemId} from URL ${selectedUrl}: ${parseError.message}`,
                        { stack: parseError.stack }
                      );

                      // Update the quote item status to reflect the parsing error
                      const updatedQuote = await Quote.findById(quoteId);
                      if (updatedQuote) {
                        const updatedItem = updatedQuote.items.id(itemId);
                        if (updatedItem) {
                          updatedItem.lookup_status = "failed_markdown_parsing";
                          updatedItem.lookup_results.push({
                            type: "PARSED_CRAWL4AI_OPTION_FAILURE",
                            status: "failed_markdown_parsing_exception",
                            timestamp: new Date().toISOString(),
                            source: "crawl4ai_markdown_parser",
                            source_url: selectedUrl,
                            error_message: parseError.message,
                          });
                          await updatedQuote.save();
                        }
                      }

                      this.logEvent(
                        this.eventEmitter,
                        quoteId,
                        itemId,
                        "PRICE_LOOKUP_MARKDOWN_PARSE_ERROR",
                        {
                          sourceUrl: selectedUrl,
                          error: parseError.message,
                        }
                      );
                    }
                  } else {
                    logger.error(
                      `[PriceLookupService] Item ${itemId} not found in quote ${quoteId} after Crawl4AI success.`
                    );
                  }
                } else {
                  logger.error(
                    `[PriceLookupService] Quote ${quoteId} not found after Crawl4AI success.`
                  );
                }
                // Placeholder for actual price extraction and option creation from markdown
                // For now, we've stored the markdown. The next step would be to parse it.
                // await updateQuoteItemWithOptions(quoteId, itemId, [{ name: `Content from ${selectedUrl}`, description: markdownContent.substring(0,500), price: "N/A", source: "Crawl4AI"}]);
              } else {
                logger.warn(
                  `[PriceLookupService] Crawl4AI succeeded for ${selectedUrl} but returned no markdown/text content.`
                );
                await updateQuoteItemLookupStatus(
                  quoteId,
                  itemId,
                  "failed_crawl4ai_no_content",
                  [],
                  `Crawl4AI returned no content from ${selectedUrl}.`
                );
              }
            } else {
              logger.error(
                `[PriceLookupService] Crawl4AI failed for URL: ${selectedUrl}. Error: ${crawl4aiResult.error}`
              );
              await updateQuoteItemLookupStatus(
                quoteId,
                itemId,
                "failed_crawl4ai_extraction",
                [],
                `Crawl4AI failed for ${selectedUrl}: ${crawl4aiResult.error}`
              );
            }
          } catch (crawlError) {
            logger.error(
              `[PriceLookupService] Error during Crawl4AIService.crawl for URL: ${selectedUrl}: ${crawlError.message}`,
              { stack: crawlError.stack }
            );
            await updateQuoteItemLookupStatus(
              quoteId,
              itemId,
              "failed_crawl4ai_exception",
              [],
              `Exception during Crawl4AI for ${selectedUrl}: ${crawlError.message}`
            );
          }
        } else {
          logger.warn(
            `[PriceLookupService] No URL found in Brave Search results for item ${itemId}.`
          );
          await updateQuoteItemLookupStatus(
            quoteId,
            itemId,
            "failed_no_url_from_search",
            [],
            "No URL found in Brave search results."
          );
        }
      } else {
        logger.info(
          `[PriceLookupService] No results from mcp4_brave_web_search for item ${itemId}.`
        );
        await updateQuoteItemLookupStatus(
          quoteId,
          itemId,
          "failed_brave_search_no_results",
          [],
          "No results from Brave search."
        );
      }
      // Original processing of Brave search results (if any) is now superseded by Crawl4AI flow if a URL is found.
      // If we wanted to keep Brave results as a fallback, that logic would go here.
      return; // End processing for mcp4_brave_web_search here as Crawl4AI takes over.
    } else if (toolId === "mcp3_firecrawl_scrape") {
      parsedOptions = parseFirecrawlScrapeResults(results);
    } else {
      // Generic fallback parser
      parsedOptions = parseGenericResults(results);
    }

    // Filter out invalid options
    const validOptions = parsedOptions.filter((option) => {
      return (
        option &&
        option.name &&
        (option.price || option.numerical_price || option.productUrl)
      );
    });

    if (!validOptions || validOptions.length === 0) {
      logger.warn(
        `[PriceLookupService] No valid material options found for quote ${quoteId}, item ${itemId}`
      );
      await updateQuoteItemLookupStatus(
        quoteId,
        itemId,
        "no_results_ai",
        [],
        "No valid options found in search results"
      );
      return [];
    }

    // Update the quote item with the options
    await updateQuoteItemWithMaterialOptions(quoteId, itemId, validOptions);

    logger.info(
      `[PriceLookupService] Successfully stored ${validOptions.length} material options for quote ${quoteId}, item ${itemId}`
    );
    return validOptions;
  } catch (error) {
    logger.error(
      `[PriceLookupService] Error processing MCP results: ${error.message}`,
      { stack: error.stack }
    );
    await updateQuoteItemLookupStatus(
      quoteId,
      itemId,
      "error_ai_search",
      [],
      `Error processing MCP results: ${error.message}`
    );
    return [];
  }
}

// These functions are now defined elsewhere in the file

/**
 * Update a quote item with material options and set its status
 * @param {string} quoteId - ID of the quote
 * @param {string} itemId - ID of the item in the quote
 * @param {Array} options - The material options to store
 * @returns {Promise}
 */
async function updateQuoteItemWithOptions(quoteId, itemId, options) {
  try {
    // Convert ObjectId string to actual ObjectId if needed
    const quoteObjId =
      typeof quoteId === "string" ? mongoose.Types.ObjectId(quoteId) : quoteId;
    const itemObjId =
      typeof itemId === "string" ? mongoose.Types.ObjectId(itemId) : itemId;

    // Update the item with options and set status to pending user selection
    const result = await Quote.findOneAndUpdate(
      { _id: quoteObjId, "items._id": itemObjId },
      {
        $set: {
          "items.$.lookup_status": "pending_user_selection",
          "items.$.material_options": options,
          "items.$.last_lookup_attempt": new Date(),
        },
        $inc: { "items.$.lookup_attempts": 1 },
      },
      { new: true }
    );

    if (!result) {
      logger.error(
        `[PriceLookupService] Failed to update quote ${quoteId} with item ${itemId} options. Quote or item not found.`
      );
      return false;
    }

    logger.info(
      `[PriceLookupService] Updated quote ${quoteId}, item ${itemId} with ${options.length} options. Status set to pending_user_selection.`
    );
    return true;
  } catch (error) {
    logger.error(
      `[PriceLookupService] Error updating quote item with options: ${error.message}`,
      { stack: error.stack }
    );
    throw error;
  }
}

/**
 * Updates the lookup status of a quote item
 * @param {string} quoteId - ID of the quote
 * @param {string} itemId - ID of the item in the quote
 * @param {string} status - The new status
 * @param {Array} options - Optional material options to store
 * @param {string} message - Optional message to store
 * @returns {Promise}
 */
async function updateQuoteItemLookupStatus(
  quoteId,
  itemId,
  status,
  options = [],
  message = ""
) {
  try {
    // Convert ObjectId string to actual ObjectId if needed
    const quoteObjId =
      typeof quoteId === "string" ? mongoose.Types.ObjectId(quoteId) : quoteId;
    const itemObjId =
      typeof itemId === "string" ? mongoose.Types.ObjectId(itemId) : itemId;

    const updateObj = {
      "items.$.lookup_status": status,
      "items.$.last_lookup_attempt": new Date(),
    };

    if (options && options.length > 0) {
      updateObj["items.$.material_options"] = options;
    }

    // Add a lookup result with the message if provided
    if (message) {
      // Get the quote item first to access its lookup_results
      const quote = await Quote.findById(quoteObjId);
      if (!quote) {
        logger.error(
          `[PriceLookupService] Failed to find quote ${quoteId} when updating lookup status.`
        );
        return false;
      }

      const item = quote.items.id(itemObjId);
      if (!item) {
        logger.error(
          `[PriceLookupService] Failed to find item ${itemId} in quote ${quoteId} when updating lookup status.`
        );
        return false;
      }

      // Create a new lookup result
      const newLookupResult = {
        type: "STATUS_UPDATE",
        status: status,
        messages: [{ message: message, timestamp: new Date() }],
        timestamp: new Date(),
        source: "price_lookup_service",
      };

      // Add the lookup result and save
      item.lookup_results = item.lookup_results || [];
      item.lookup_results.push(newLookupResult);
      await quote.save();

      logger.info(
        `[PriceLookupService] Updated quote ${quoteId}, item ${itemId} lookup status to ${status} with message: ${message}`
      );
      return true;
    } else {
      // Just update the status directly
      const result = await Quote.findOneAndUpdate(
        { _id: quoteObjId, "items._id": itemObjId },
        {
          $set: updateObj,
          $inc: { "items.$.lookup_attempts": 1 },
        },
        { new: true }
      );

      if (!result) {
        logger.error(
          `[PriceLookupService] Failed to update quote ${quoteId} with item ${itemId} status. Quote or item not found.`
        );
        return false;
      }

      logger.info(
        `[PriceLookupService] Updated quote ${quoteId}, item ${itemId} lookup status to ${status}.`
      );
      return true;
    }
  } catch (error) {
    logger.error(
      `[PriceLookupService] Error updating quote item lookup status: ${error.message}`,
      { stack: error.stack }
    );
    return false;
  }
}

/**
 * Initiate AI material scraping for a quote item
 * @param {string} quoteId - ID of the quote
 * @param {string} itemId - ID of the item in the quote
 * @param {Object} options - Options for the lookup
 * @returns {Promise<string>} - The request ID
 */
async function initiateAiMaterialScraping(quoteId, itemId, options = {}) {
  try {
    logger.info(
      `[PriceLookupService] Initiating AI material scraping for quote ${quoteId}, item ${itemId}`
    );

    // Convert ObjectId string to actual ObjectId if needed
    const quoteObjId =
      typeof quoteId === "string" ? mongoose.Types.ObjectId(quoteId) : quoteId;
    const itemObjId =
      typeof itemId === "string" ? mongoose.Types.ObjectId(itemId) : itemId;

    // Get the quote and the specific item
    const quote = await Quote.findById(quoteObjId);
    if (!quote) {
      logger.error(
        `[PriceLookupService] Quote ${quoteId} not found when initiating AI material scraping`
      );
      throw new Error(`Quote ${quoteId} not found`);
    }

    const item = quote.items.id(itemObjId);
    if (!item) {
      logger.error(
        `[PriceLookupService] Item ${itemId} not found in quote ${quoteId} when initiating AI material scraping`
      );
      throw new Error(`Item ${itemId} not found in quote ${quoteId}`);
    }

    // Update the item's status to indicate AI search is in progress
    item.lookup_status = "pending_ai_search";
    item.last_lookup_attempt = new Date();
    item.lookup_attempts = (item.lookup_attempts || 0) + 1;

    // Prepare the search query - use the provided query suggestion or item name/description
    const searchQuery =
      item.lookup_query_suggestion ||
      options.searchQuery ||
      `${item.name} ${item.description}`.trim();

    if (!searchQuery) {
      logger.error(
        `[PriceLookupService] No search query available for quote ${quoteId}, item ${itemId}`
      );
      await updateQuoteItemLookupStatus(
        quoteId,
        itemId,
        "error_ai_search",
        [],
        "No search query available"
      );
      throw new Error("No search query available");
    }

    // Add a lookup result entry
    item.lookup_results = item.lookup_results || [];
    item.lookup_results.push({
      type: "AI_MATERIAL_SEARCH_INITIATED",
      status: "pending",
      data: { search_query: searchQuery },
      messages: [
        {
          message: `Initiating AI material search with query: ${searchQuery}`,
          timestamp: new Date(),
        },
      ],
      timestamp: new Date(),
      source: "ai_material_search_initiated",
    });

    // Save the quote
    await quote.save();

    // Use Brave web search as our standard tool for material lookups
    const toolId = "mcp4_brave_web_search";

    // Prepare tool parameters
    const toolParams = {
      query: `${searchQuery} price`,
      count: options.limit || 10,
    };

    // If a specific URL was provided for direct scraping, we'll handle this later
    // by first getting search results and then scraping the most relevant one
    if (options.url) {
      logger.info(
        `[PriceLookupService] URL provided for direct browsing: ${options.url}`
      );
      // We could add URL to metadata for future reference
    }

    // Prepare metadata for tracking the request
    const metadata = {
      quoteId,
      itemId,
      searchQuery,
      toolId,
      initiatedAt: new Date(),
    };

    // Execute the MCP tool
    const requestId = await executeMcpTool(toolId, toolParams, metadata);

    logger.info(
      `[PriceLookupService] Successfully initiated AI material scraping for quote ${quoteId}, item ${itemId}, request ID ${requestId}`
    );
    return requestId;
  } catch (error) {
    logger.error(
      `[PriceLookupService] Error initiating AI material scraping: ${error.message}`,
      { stack: error.stack }
    );
    throw error;
  }
}

/**
 * Update a quote item with material options from AI/MCP scraping
 * @param {string} quoteId - ID of the quote
 * @param {string} itemId - ID of the item in the quote
 * @param {Array} options - The material options to store
 * @param {string} status - Optional new status for the lookup
 * @returns {Promise<boolean>} - Success or failure
 */
async function updateQuoteItemWithMaterialOptions(
  quoteId,
  itemId,
  options,
  status = "pending_user_selection"
) {
  try {
    // Validate inputs
    if (!quoteId || !itemId || !Array.isArray(options)) {
      logger.error(
        `[PriceLookupService] Invalid parameters for updateQuoteItemWithMaterialOptions: ${JSON.stringify(
          { quoteId, itemId, optionsCount: options ? options.length : 0 }
        )}`
      );
      return false;
    }

    // Convert ObjectIds if needed
    const quoteObjId =
      typeof quoteId === "string" ? mongoose.Types.ObjectId(quoteId) : quoteId;
    const itemObjId =
      typeof itemId === "string" ? mongoose.Types.ObjectId(itemId) : itemId;

    // Format options to ensure they have the right structure
    const formattedOptions = options.map((option) => ({
      name: option.name || option.title || "Unknown Material",
      description: option.description || "",
      price: option.price || option.formattedPrice || "$0.00",
      numerical_price:
        option.numerical_price ||
        option.numericPrice ||
        parseFloat(option.price?.replace(/[^0-9.]/g, "") || "0"),
      imageUrl: option.imageUrl || option.image || "",
      productUrl: option.productUrl || option.url || "",
      sku: option.sku || option.productId || "",
      source: option.source || option.vendor || "ai_material_search",
      available: option.available !== false,
      metadata: option.metadata || {},
    }));

    // Update the quote item with the options and status
    const result = await Quote.findOneAndUpdate(
      { _id: quoteObjId, "items._id": itemObjId },
      {
        $set: {
          "items.$.material_options": formattedOptions,
          "items.$.lookup_status": status,
          "items.$.last_lookup_attempt": new Date(),
        },
        $inc: { "items.$.lookup_attempts": 1 },
      },
      { new: true }
    );

    if (!result) {
      logger.error(
        `[PriceLookupService] Failed to update quote ${quoteId} with item ${itemId} material options. Quote or item not found.`
      );
      return false;
    }

    // Add a lookup result entry to track this update
    const quote = await Quote.findById(quoteObjId);
    if (quote) {
      const item = quote.items.id(itemObjId);
      if (item) {
        item.lookup_results = item.lookup_results || [];
        item.lookup_results.push({
          type: "MATERIAL_OPTIONS_UPDATE",
          status: status,
          messages: [
            {
              message: `Updated material options with ${formattedOptions.length} choices from AI material search`,
              timestamp: new Date(),
            },
          ],
          timestamp: new Date(),
          source: "ai_material_search",
        });
        await quote.save();
      }
    }

    logger.info(
      `[PriceLookupService] Updated quote ${quoteId}, item ${itemId} with ${formattedOptions.length} material options. Status set to ${status}.`
    );
    return true;
  } catch (error) {
    logger.error(
      `[PriceLookupService] Error updating quote item with material options: ${error.message}`,
      { stack: error.stack }
    );
    return false;
  }
}

/**
 * Parse generic markdown content from Crawl4AI to extract product information.
 * This function attempts to identify products in the markdown by looking for
 * common patterns like prices, product names, and SKUs.
 *
 * @param {string} markdownContent - The markdown content to parse
 * @param {string} sourceUrl - The URL from which the markdown was obtained
 * @returns {Array<Object>} An array of extracted product options
 */
async function parseGenericMarkdownForProducts(markdownContent, sourceUrl) {
  logger.info(
    `[parseGenericMarkdownForProducts] Starting parsing for URL: ${sourceUrl}`
  );
  const options = [];

  if (!markdownContent || typeof markdownContent !== "string") {
    logger.warn(
      "[parseGenericMarkdownForProducts] Markdown content is null, undefined, or not a string."
    );
    return options;
  }

  // Split markdown into potential product blocks (e.g., by headers or multiple newlines)
  const blocks = markdownContent.split(/\n#{1,3}\s+|\n\n\n+/); // Split by H1/H2/H3 or triple newlines

  for (const block of blocks) {
    if (block.trim().length < 50 && !block.includes("$")) continue; // Skip very short blocks unlikely to be products

    let confidenceScore = 0;
    let extractedName = null;
    let extractedPrice = null;
    let extractedSku = null;
    let extractedDescription = "";

    // Attempt to find a name (often a heading or prominent line)
    // First check for headings which are most likely product names
    const headingMatch = block.match(/^#{1,3}\s+([^\n]{5,150})$/m);
    if (headingMatch && headingMatch[1].trim().length > 0) {
      extractedName = headingMatch[1].trim();
      confidenceScore += 0.3; // Higher confidence for heading-based names
    } else {
      // Otherwise look for a prominent line that could be a title
      const nameMatch = block.match(/^([^\n]{5,150})$/m); // First non-empty line as potential name
      if (nameMatch && nameMatch[1].trim().length > 0) {
        // Crude filter: avoid lines that are clearly not product titles
        if (
          !nameMatch[1].toLowerCase().includes("copyright") &&
          !nameMatch[1].toLowerCase().includes("all rights reserved") &&
          nameMatch[1].length < 150
        ) {
          extractedName = nameMatch[1].trim();
          confidenceScore += 0.2;
        }
      }
    }

    // Price Extraction - look for currency symbols and numeric patterns
    // Regex matches common price formats: $1,234.56, $1234.56, 1234.56
    const priceRegex =
      /\$(\d{1,3}(?:,\d{3})*(?:\.\d{2})?|\d+(?:\.\d{2}))|€(\d{1,3}(?:\.\d{3})*(?:,\d{2})?|\d+(?:,\d{2}))/g;
    let priceMatches;
    let bestPriceMatch = null;
    let bestPriceValue = 0;

    while ((priceMatches = priceRegex.exec(block)) !== null) {
      const isUSD = !!priceMatches[1];
      const priceString = isUSD
        ? priceMatches[1].replace(/,/g, "") // US format
        : priceMatches[2].replace(/\./g, "").replace(/,/g, "."); // EU format

      const priceValue = parseFloat(priceString);
      if (!isNaN(priceValue) && priceValue > 0) {
        // Keep track of all valid prices, but prefer ones that seem more realistic
        // (e.g., not extremely high or low)
        if (
          !bestPriceMatch ||
          (priceValue > 0.5 &&
            priceValue < 10000 &&
            (bestPriceValue <= 0.5 ||
              bestPriceValue >= 10000 ||
              priceValue < bestPriceValue))
        ) {
          bestPriceMatch = {
            priceValue,
            match: priceMatches[0],
            currency: isUSD ? "$" : "€",
          };
          bestPriceValue = priceValue;
        }
      }
    }

    if (bestPriceMatch) {
      extractedPrice = bestPriceMatch.priceValue;
      confidenceScore += 0.4; // Price is a strong indicator of a product

      // If name is still null, try to get text near price
      if (!extractedName) {
        const priceIndex = block.indexOf(bestPriceMatch.match);
        const potentialNameLines = block
          .substring(Math.max(0, priceIndex - 300), priceIndex)
          .split("\n");
        const potentialName = potentialNameLines
          .filter(
            (line) =>
              line.trim().length > 3 &&
              line.trim().length < 150 &&
              !line.includes("$") &&
              !line.toLowerCase().includes("price")
          )
          .pop()
          ?.trim();

        if (potentialName) {
          extractedName = potentialName;
          confidenceScore += 0.1; // Less confidence than a header
        }
      }
    }

    // SKU/Model Number Extraction - look for common identifier patterns
    const skuRegex =
      /(?:SKU|Item\s?#|Model\s?#|MPN|P\/N|Product\sID|Part\sNumber|Catalog\s?Number)[:\s]*([a-zA-Z0-9][a-zA-Z0-9\s\/-]{2,48}[a-zA-Z0-9])/gi;
    const skuMatch = skuRegex.exec(block);
    if (skuMatch && skuMatch[1]) {
      extractedSku = skuMatch[1].trim();
      confidenceScore += 0.3;
    }

    // Description Extraction - look for substantial text near the product info
    if (extractedName) {
      // Try to find a good description paragraph after the name
      const nameIndex = block.indexOf(extractedName);
      if (nameIndex !== -1) {
        let descStartIndex = nameIndex + extractedName.length;

        // If we have a price, start description after it
        if (bestPriceMatch) {
          const priceIndex = block.indexOf(bestPriceMatch.match);
          if (priceIndex > nameIndex) {
            descStartIndex = priceIndex + bestPriceMatch.match.length;
          }
        }

        // Extract potential description text
        const textAfterNamePrice = block.substring(descStartIndex).trim();
        const paragraphs = textAfterNamePrice.split(/\n\n+/);

        // Look for a good paragraph (not too short, not too long)
        for (const para of paragraphs) {
          const cleanedPara = para.trim();
          if (
            cleanedPara.length > 30 &&
            cleanedPara.length < 1000 &&
            !cleanedPara.startsWith("#") &&
            !cleanedPara.includes("copyright") &&
            !cleanedPara.includes("all rights reserved")
          ) {
            extractedDescription = cleanedPara;
            confidenceScore += 0.1;
            break;
          }
        }
      }
    }

    // Only add if we have at least a name and a price with decent confidence
    if (extractedName && extractedPrice && confidenceScore >= 0.5) {
      // Format price with currency symbol
      const formattedPrice = bestPriceMatch
        ? `${bestPriceMatch.currency}${extractedPrice.toFixed(2)}`
        : `$${extractedPrice.toFixed(2)}`;

      options.push({
        name: extractedName,
        price: formattedPrice,
        sku: extractedSku,
        description: extractedDescription,
        sourceUrl: sourceUrl,
        confidenceScore: parseFloat(confidenceScore.toFixed(2)),
        rawExtractedFields: {
          nameMatch: extractedName,
          priceMatch: bestPriceMatch?.match,
          skuMatch: extractedSku,
          blockSnippet: block.substring(0, Math.min(500, block.length)), // For debugging
        },
      });
    }
  }

  // Deduplicate based on name and price
  const uniqueOptions = options.filter(
    (option, index, self) =>
      index ===
      self.findIndex((o) => o.name === option.name && o.price === option.price)
  );

  // Sort by confidence score (highest first)
  uniqueOptions.sort((a, b) => b.confidenceScore - a.confidenceScore);

  logger.info(
    `[parseGenericMarkdownForProducts] Parsed ${uniqueOptions.length} unique options from URL: ${sourceUrl}`
  );
  return uniqueOptions;
}

module.exports = {
  // Original price lookup functions
  initiatePriceLookup,
  fetchMaterialDetailsFromAIList,
  updateQuoteItemWithOptions,

  // New AI material scraping functions
  initiateAiMaterialScraping,
  updateQuoteItemLookupStatus,
  handleMaterialOptionSelection,
  updateQuoteItemWithMaterialOptions,

  // MCP integration functions (DISABLED - replaced with Crawl4AI)
  // executeMcpTool,
  // handleMcpCallback,
  // storeMcpRequestMetadata,
  // getMcpRequestMetadata,
  // processAndStoreMcpResults,

  // Parsers
  parseBraveSearchResults,
  parseFirecrawlSearchResults,
  parseFirecrawlScrapeResults,
  parseGenericResults,
  parseGenericMarkdownForProducts,
};
