/**
 * enhancedFingerprint.js
 * Advanced utility for comprehensive browser fingerprint spoofing
 * Implements sophisticated techniques to modify browser properties and prevent detection
 */

const logger = require("./logger");

/**
 * JavaScript to be injected for comprehensive fingerprint protection
 * @private
 */
const enhancedFingerprintScript = `
() => {
  try {
    // ==========================================
    // 1. Comprehensive Navigator Modifications
    // ==========================================
    const originalNavigator = navigator.__proto__;
    const navigatorOverrides = {
      // Basic properties
      webdriver: false,
      automation: false,
      userAgent: navigator.userAgent.replace(/HeadlessChrome/gi, 'Chrome'),
      
      // Hardware properties
      hardwareConcurrency: Math.min(8, Math.max(4, navigator.hardwareConcurrency || 4)),
      deviceMemory: 8,
      
      // Language properties
      languages: ['en-US', 'en'],
      language: 'en-US',
      
      // Platform properties
      platform: 'Win32',
      oscpu: 'Windows NT 10.0; Win64; x64',
      
      // Plugins and MIME types
      plugins: Array(3).fill().map(() => ({
        name: ['Widevine Content Decryption Module', 'Chrome PDF Plugin', 'Chrome PDF Viewer'][Math.floor(Math.random() * 3)],
        description: 'Enables Widevine licenses for playback of HTML audio/video content.',
        filename: 'widevinecdm.dll',
        length: 1
      })),
      mimeTypes: Array(3).fill().map(() => ({
        type: ['application/pdf', 'application/x-google-chrome-pdf', 'application/x-nacl'][Math.floor(Math.random() * 3)],
        description: 'Portable Document Format',
        suffixes: 'pdf'
      }))
    };
    
    // Create a proxy for Navigator
    const navigatorProxy = new Proxy(originalNavigator, {
      get: function(target, prop) {
        if (prop in navigatorOverrides) {
          return navigatorOverrides[prop];
        }
        return target[prop];
      }
    });
    
    // Apply navigator proxy
    Object.defineProperty(window, 'navigator', {
      value: navigatorProxy,
      writable: false,
      configurable: false
    });
    
    // ==========================================
    // 2. Canvas Fingerprinting Protection
    // ==========================================
    
    // Override toDataURL to add noise
    const originalToDataURL = HTMLCanvasElement.prototype.toDataURL;
    HTMLCanvasElement.prototype.toDataURL = function(type) {
      // Only modify actual image data, not system dialogs
      if (this.width > 16 && this.height > 16) {
        // Add slight noise to canvas data
        const context = this.getContext('2d');
        if (context) {
          const imageData = context.getImageData(0, 0, this.width, this.height);
          const pixels = imageData.data;
          
          // Apply noise to only 0.5% of pixels to maintain visual appearance
          for (let i = 0; i < pixels.length; i += 4) {
            if (Math.random() < 0.005) {
              const offset = Math.floor(Math.random() * 10) - 5;
              pixels[i] = Math.max(0, Math.min(255, pixels[i] + offset));     // Red
              pixels[i+1] = Math.max(0, Math.min(255, pixels[i+1] + offset)); // Green
              pixels[i+2] = Math.max(0, Math.min(255, pixels[i+2] + offset)); // Blue
            }
          }
          
          context.putImageData(imageData, 0, 0);
        }
      }
      
      return originalToDataURL.apply(this, arguments);
    };
    
    // Override getImageData as well
    const originalGetImageData = CanvasRenderingContext2D.prototype.getImageData;
    CanvasRenderingContext2D.prototype.getImageData = function() {
      const imageData = originalGetImageData.apply(this, arguments);
      
      // Add noise to data for fingerprinting functions
      if (imageData.width > 16 && imageData.height > 16) {
        const pixels = imageData.data;
        
        // Apply very slight noise
        for (let i = 0; i < pixels.length; i += 4) {
          if (Math.random() < 0.003) {
            const offset = Math.floor(Math.random() * 4) - 2; // Smaller offset for getImageData
            pixels[i] = Math.max(0, Math.min(255, pixels[i] + offset));
            pixels[i+1] = Math.max(0, Math.min(255, pixels[i+1] + offset));
            pixels[i+2] = Math.max(0, Math.min(255, pixels[i+2] + offset));
          }
        }
      }
      
      return imageData;
    };
    
    // ==========================================
    // 3. WebGL Fingerprinting Protection
    // ==========================================
    if (window.WebGLRenderingContext) {
      const getParameterProxyHandler = {
        apply: function(target, thisArg, args) {
          const param = args[0];
          
          // UNMASKED_VENDOR_WEBGL
          if (param === 37445) {
            return 'Intel Inc.';
          }
          
          // UNMASKED_RENDERER_WEBGL
          if (param === 37446) {
            return 'Intel Iris OpenGL Engine';
          }
          
          // Normal parameters
          const result = target.apply(thisArg, args);
          return result;
        }
      };
      
      // Apply proxy to getParameter
      WebGLRenderingContext.prototype.getParameter = 
        new Proxy(WebGLRenderingContext.prototype.getParameter, getParameterProxyHandler);
        
      // Also apply to WebGL2RenderingContext if it exists
      if (window.WebGL2RenderingContext) {
        WebGL2RenderingContext.prototype.getParameter = 
          new Proxy(WebGL2RenderingContext.prototype.getParameter, getParameterProxyHandler);
      }
    }
    
    // ==========================================
    // 4. Audio Context Fingerprinting Protection
    // ==========================================
    if (window.AudioContext || window.webkitAudioContext) {
      const AudioContextClass = window.AudioContext || window.webkitAudioContext;
      const originalGetChannelData = AudioBuffer.prototype.getChannelData;
      
      // Add small variations to audio data
      AudioBuffer.prototype.getChannelData = function(channel) {
        const originalData = originalGetChannelData.call(this, channel);
        
        // Don't modify actual audio playback, only fingerprinting attempts
        // This is a heuristic - small buffers are often used for fingerprinting
        if (this.length < 1000) {
          const newData = originalData.slice(0);
          
          // Modify a small percentage of samples
          for (let i = 0; i < newData.length; i++) {
            if (Math.random() < 0.001) {
              newData[i] += (Math.random() * 0.0001) - 0.00005;
            }
          }
          
          return newData;
        }
        
        return originalData;
      };
    }
    
    // ==========================================
    // 5. Font Fingerprinting Protection
    // ==========================================
    
    // Override font measurement functions with small offsets
    if (document.fonts && document.fonts.check) {
      const originalCheck = document.fonts.check;
      document.fonts.check = function() {
        const result = originalCheck.apply(this, arguments);
        // Small percentage of common fonts report differently
        if (arguments[0] && 
            typeof arguments[0] === 'string' && 
            /^(Arial|Times|Courier|Helvetica|Verdana)/.test(arguments[0])) {
          if (Math.random() < 0.05) {
            return !result;
          }
        }
        return result;
      };
    }
    
    // ==========================================
    // 6. Battery API Protection
    // ==========================================
    if (navigator.getBattery) {
      navigator.getBattery = function() {
        return Promise.resolve({
          charging: true,
          chargingTime: 0,
          dischargingTime: Infinity,
          level: 1.0,
          addEventListener: function() {},
          removeEventListener: function() {}
        });
      };
    }
    
    // ==========================================
    // 7. Screen and Window Property Normalization
    // ==========================================
    
    // Normalize window screen properties to common values
    if (window.screen) {
      // Make screen dimensions appear more common
      Object.defineProperties(window.screen, {
        width: { value: 1920 },
        height: { value: 1080 },
        availWidth: { value: 1920 },
        availHeight: { value: 1040 }, // Accounts for taskbar
        colorDepth: { value: 24 },
        pixelDepth: { value: 24 }
      });
    }
    
    return { success: true, message: 'Enhanced fingerprint protection applied' };
  } catch (error) {
    return { 
      success: false, 
      message: 'Error applying enhanced fingerprint protection: ' + error.toString() 
    };
  }
}`;

/**
 * Apply enhanced fingerprint protection to a browser page
 * @param {Object} mcpClient - MCP client instance
 * @param {string} pageId - MCP page ID
 * @returns {Promise<boolean>} - Success status
 */
async function applyEnhancedFingerprint(mcpClient, pageId) {
  if (!mcpClient || !pageId) {
    logger.warn(
      `[EnhancedFingerprint] Missing parameters: mcpClient=${!!mcpClient}, pageId=${pageId}`
    );
    return false;
  }

  try {
    logger.debug(
      `[EnhancedFingerprint] Applying enhanced fingerprint protection to page ${pageId}`
    );

    const result = await mcpClient.callMcpTool("evaluate", {
      script: enhancedFingerprintScript,
      pageId: pageId,
    });

    if (result && result.success) {
      logger.debug(
        `[EnhancedFingerprint] Applied to page ${pageId}: ${
          result.message || "Success"
        }`
      );
      return true;
    } else {
      logger.warn(
        `[EnhancedFingerprint] Failed: ${
          result ? result.message : "Unknown error"
        }`
      );
      return false;
    }
  } catch (error) {
    logger.error(`[EnhancedFingerprint] Error: ${error.message}`, {
      stack: error.stack,
    });
    return false;
  }
}

module.exports = {
  applyEnhancedFingerprint,
};
