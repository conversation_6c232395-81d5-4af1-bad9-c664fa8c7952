import React, { useState, useEffect, useRef } from "react";
import {
  Container,
  Typography,
  Box,
  Paper,
  Grid,
  TextField,
  Button,
  Avatar,
  Divider,
  CircularProgress,
  Alert,
  Snackbar,
  Card,
  CardContent,
  List,
  ListItem,
  ListItemText,
  Tabs,
  Tab,
  IconButton,
  FormControl,
  InputLabel,
  FormHelperText,
  Select,
  MenuItem,
  Chip,
} from "@mui/material";
import {
  Save as SaveIcon,
  Home as HomeIcon,
  Description as DescriptionIcon,
  Person as PersonIcon,
  Edit as EditIcon,
  Settings as SettingsIcon,
  Photo as PhotoIcon,
} from "@mui/icons-material";
import { useSelector, useDispatch } from "react-redux";
import { deepPurple } from "@mui/material/colors";
import {
  updateUserProfile,
  setUserFromLocalStorage,
} from "../slices/authSlice";
import AddressAutocomplete from "../components/AddressAutocomplete";

// Tab Panel Component
function TabPanel(props) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`profile-tabpanel-${index}`}
      aria-labelledby={`profile-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ py: 3 }}>{children}</Box>}
    </div>
  );
}

// Profile Component
const Profile = () => {
  const { userInfo } = useSelector((state) => state.auth);
  const dispatch = useDispatch();

  // Ref to track timeouts for cleanup
  const timeoutsRef = useRef([]);

  const [loading, setLoading] = useState(false);
  const [saveLoading, setSaveLoading] = useState(false);
  const [success, setSuccess] = useState(false);
  const [error, setError] = useState(null);
  const [tabValue, setTabValue] = useState(0);

  // Profile states
  const [profileData, setProfileData] = useState({
    firstName: "",
    lastName: "",
    email: "",
    phone: "",
    position: "",
    address: "",
    city: "",
    state: "",
    zipCode: "",
    company: "",
    bio: "",
    contractorType: "",
    services: [],
    servicesInput: "",
    addressFormat: "",
    role: "",
  });

  // Security states
  const [securityData, setSecurityData] = useState({
    currentPassword: "",
    newPassword: "",
    confirmPassword: "",
  });

  // Mock user activity data
  const userActivity = [
    {
      id: 1,
      action: "Created Invoice",
      target: "INV-2025-0101",
      date: "2025-03-15 10:30 AM",
    },
    {
      id: 2,
      action: "Updated Job",
      target: "JOB-2025-142",
      date: "2025-03-14 02:15 PM",
    },
    {
      id: 3,
      action: "Added Customer",
      target: "Johnson Residence",
      date: "2025-03-12 09:45 AM",
    },
    {
      id: 4,
      action: "Completed Job",
      target: "JOB-2025-140",
      date: "2025-03-10 04:30 PM",
    },
  ];

  const [addressSearchTerm, setAddressSearchTerm] = useState("");
  const [newService, setNewService] = useState("");
  const [formErrors, setFormErrors] = useState({});

  // Helper function to create timeout with cleanup tracking
  const createTimeout = (callback, delay) => {
    const timeoutId = setTimeout(callback, delay);
    timeoutsRef.current.push(timeoutId);
    return timeoutId;
  };

  // Cleanup function to clear all timeouts
  const clearAllTimeouts = () => {
    timeoutsRef.current.forEach((timeoutId) => clearTimeout(timeoutId));
    timeoutsRef.current = [];
  };

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      clearAllTimeouts();
    };
  }, []);

  // Force reload user data from localStorage when component mounts
  useEffect(() => {
    dispatch(setUserFromLocalStorage());
  }, [dispatch]);

  useEffect(() => {
    setLoading(true);

    const timeoutId = createTimeout(() => {
      if (userInfo) {
        // Initialize address fields based on backend structure
        let addressStr = "";
        let city = "";
        let state = "";
        let zipCode = "";

        // Check if address is an object (from backend) or string (from old data)
        if (userInfo.address && typeof userInfo.address === "object") {
          addressStr = userInfo.address.street || "";
          city = userInfo.address.city || "";
          state = userInfo.address.state || "";
          zipCode = userInfo.address.zipCode || "";
        } else {
          // Handle legacy format where address might be a string
          addressStr = userInfo.address || "";
          city = userInfo.city || "";
          state = userInfo.state || "";
          zipCode = userInfo.zipCode || "";
        }

        setProfileData({
          firstName: userInfo.firstName || "",
          lastName: userInfo.lastName || "",
          email: userInfo.email || "",
          phone: userInfo.phone || "",
          position: userInfo.position || "",
          address: addressStr,
          city: city,
          state: state,
          zipCode: zipCode,
          company: userInfo.company || "",
          bio: userInfo.bio || "",
          contractorType: userInfo.contractorType || "",
          services: userInfo.services || [],
          addressFormat: userInfo.addressFormat || "",
          role: userInfo.role || "Technicians",
        });

        // Set the combined address for the autocomplete component if available
        const fullAddress = [addressStr, city, state, zipCode]
          .filter(Boolean)
          .join(", ");

        setAddressSearchTerm(fullAddress);
      }
      setLoading(false);
    }, 800);

    // Cleanup function to cancel timeout if component unmounts
    return () => clearTimeout(timeoutId);
  }, [userInfo]);

  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
  };

  const handleProfileChange = (e) => {
    const { name, value } = e.target;
    setProfileData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleSecurityChange = (e) => {
    const { name, value } = e.target;
    setSecurityData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleSaveProfile = () => {
    // Validate the form fields
    const errors = {};

    if (!profileData.firstName) errors.firstName = "First name is required";
    if (!profileData.lastName) errors.lastName = "Last name is required";
    if (!profileData.email) errors.email = "Email is required";
    if (!profileData.phone) errors.phone = "Phone number is required";
    if (!profileData.city) errors.city = "City is required";
    if (!profileData.state) errors.state = "State is required";
    if (!profileData.zipCode) errors.zipCode = "Zip Code is required";

    // Check if there are any validation errors
    if (Object.keys(errors).length > 0) {
      setFormErrors(errors);
      return;
    }

    setSaveLoading(true);

    // Format data to match the backend schema
    const userData = {
      firstName: profileData.firstName,
      lastName: profileData.lastName,
      email: profileData.email,
      phone: profileData.phone,
      // Structure the address as an object to match the backend model
      address: {
        street: profileData.address,
        city: profileData.city,
        state: profileData.state,
        zipCode: profileData.zipCode,
        country: "US", // Default country
      },
      contractorType: profileData.contractorType,
      services: profileData.services,
      role: profileData.role, // Include role in update data
      company: profileData.company, // Add company field
      position: profileData.position, // Add position field
    };

    if (profileData.bio) {
      userData.bio = profileData.bio;
    }

    dispatch(updateUserProfile(userData))
      .unwrap()
      .then(() => {
        setSaveLoading(false);
        setSuccess(true);
        // Clear any form errors after successful save
        setFormErrors({});

        createTimeout(() => {
          setSuccess(false);
        }, 5000);
      })
      .catch((err) => {
        setSaveLoading(false);
        setError(err.message || "Failed to update profile");

        createTimeout(() => {
          setError(null);
        }, 5000);
      });
  };

  const handleChangePassword = () => {
    if (securityData.newPassword !== securityData.confirmPassword) {
      setError("New password and confirm password do not match");
      return;
    }

    setSaveLoading(true);

    createTimeout(() => {
      setSaveLoading(false);
      setSuccess(true);
      setSecurityData({
        currentPassword: "",
        newPassword: "",
        confirmPassword: "",
      });

      createTimeout(() => {
        setSuccess(false);
      }, 5000);
    }, 1500);
  };

  const handleAddService = () => {
    const newServices = [...profileData.services, newService.trim()];
    setProfileData((prev) => ({ ...prev, services: newServices }));
    setNewService("");
  };

  const handleRemoveService = (service) => {
    const newServices = profileData.services.filter((s) => s !== service);
    setProfileData((prev) => ({ ...prev, services: newServices }));
  };

  const handleCloseSnackbar = () => {
    setSuccess(false);
    setError(null);
  };

  const getUserInitials = () => {
    return `${profileData.firstName.charAt(0)}${profileData.lastName.charAt(
      0
    )}`;
  };

  return (
    <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
      {/* Profile Header */}
      <Paper sx={{ p: 3, mb: 4 }}>
        <Grid container spacing={3} alignItems="center">
          <Grid item>
            <Box sx={{ position: "relative" }}>
              <Avatar
                sx={{
                  width: 100,
                  height: 100,
                  bgcolor: deepPurple[500],
                  fontSize: "2rem",
                }}
              >
                {getUserInitials()}
              </Avatar>
              <IconButton
                sx={{
                  position: "absolute",
                  bottom: 0,
                  right: 0,
                  bgcolor: "background.paper",
                  border: "1px solid",
                  borderColor: "divider",
                  "&:hover": { bgcolor: "action.hover" },
                }}
                size="small"
              >
                <PhotoIcon fontSize="small" />
              </IconButton>
            </Box>
          </Grid>
          <Grid item xs>
            <Typography variant="h4">
              {loading ? (
                <CircularProgress
                  size={24}
                  sx={{ verticalAlign: "middle", mr: 1 }}
                />
              ) : (
                `${profileData.firstName} ${profileData.lastName}`
              )}
            </Typography>
            <Typography variant="subtitle1" color="text.secondary">
              {profileData.position} · {profileData.company}
            </Typography>
          </Grid>
        </Grid>
      </Paper>

      {/* Profile Content */}
      <Grid container spacing={4}>
        {/* Left Column - Profile Navigation */}
        <Grid item xs={12} md={3}>
          <Paper sx={{ mb: { xs: 3, md: 0 } }}>
            <Tabs
              orientation="vertical"
              value={tabValue}
              onChange={handleTabChange}
              sx={{
                borderRight: 1,
                borderColor: "divider",
                "& .MuiTab-root": {
                  alignItems: "flex-start",
                  textAlign: "left",
                  pl: 3,
                },
              }}
            >
              <Tab
                icon={<PersonIcon />}
                iconPosition="start"
                label="Personal Info"
                id="profile-tab-0"
              />
              <Tab
                icon={<SettingsIcon />}
                iconPosition="start"
                label="Security"
                id="profile-tab-1"
              />
              <Tab
                icon={<DescriptionIcon />}
                iconPosition="start"
                label="Notifications"
                id="profile-tab-2"
              />
              <Tab
                icon={<HomeIcon />}
                iconPosition="start"
                label="Preferences"
                id="profile-tab-3"
              />
            </Tabs>
          </Paper>

          {/* User Activity Card */}
          <Card sx={{ mt: 3, display: { xs: "none", md: "block" } }}>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Recent Activity
              </Typography>
              <List dense>
                {userActivity.map((activity) => (
                  <ListItem key={activity.id} sx={{ px: 0 }}>
                    <ListItemText
                      primary={activity.action}
                      secondary={`${activity.target} • ${activity.date}`}
                    />
                  </ListItem>
                ))}
              </List>
            </CardContent>
          </Card>
        </Grid>

        {/* Right Column - Profile Content */}
        <Grid item xs={12} md={9}>
          <Paper sx={{ p: 3 }}>
            {/* Personal Info Tab */}
            <TabPanel value={tabValue} index={0}>
              <Typography variant="h6" gutterBottom>
                Personal Information
              </Typography>
              <Divider sx={{ mb: 3 }} />

              {loading ? (
                <Box display="flex" justifyContent="center" my={3}>
                  <CircularProgress />
                </Box>
              ) : (
                <Grid container spacing={3}>
                  <Grid item xs={12} sm={6}>
                    <TextField
                      fullWidth
                      label="First Name"
                      name="firstName"
                      value={profileData.firstName}
                      onChange={handleProfileChange}
                      error={!!formErrors.firstName}
                      helperText={formErrors.firstName}
                    />
                  </Grid>

                  <Grid item xs={12} sm={6}>
                    <TextField
                      fullWidth
                      label="Last Name"
                      name="lastName"
                      value={profileData.lastName}
                      onChange={handleProfileChange}
                      error={!!formErrors.lastName}
                      helperText={formErrors.lastName}
                    />
                  </Grid>

                  <Grid item xs={12} sm={6}>
                    <TextField
                      fullWidth
                      label="Email Address"
                      name="email"
                      value={profileData.email}
                      onChange={handleProfileChange}
                      error={!!formErrors.email}
                      helperText={formErrors.email}
                      InputProps={{
                        startAdornment: (
                          <EditIcon color="action" sx={{ mr: 1 }} />
                        ),
                      }}
                    />
                  </Grid>

                  <Grid item xs={12} sm={6}>
                    <TextField
                      fullWidth
                      label="Phone Number"
                      name="phone"
                      value={profileData.phone}
                      onChange={handleProfileChange}
                      error={!!formErrors.phone}
                      helperText={formErrors.phone}
                      InputProps={{
                        startAdornment: (
                          <EditIcon color="action" sx={{ mr: 1 }} />
                        ),
                      }}
                    />
                  </Grid>

                  <Grid item xs={12} sm={6}>
                    <TextField
                      fullWidth
                      label="Position / Job Title"
                      name="position"
                      value={profileData.position}
                      onChange={handleProfileChange}
                    />
                  </Grid>

                  <Grid item xs={12} sm={6}>
                    <FormControl fullWidth>
                      <InputLabel id="role-label">Role</InputLabel>
                      <Select
                        labelId="role-label"
                        id="role"
                        name="role"
                        value={profileData.role || "Technicians"}
                        onChange={handleProfileChange}
                        disabled={userInfo?.role !== "Administrators"} // Only admins can change roles
                        label="Role"
                      >
                        <MenuItem value="Administrators">
                          Administrator
                        </MenuItem>
                        <MenuItem value="Managers">Manager</MenuItem>
                        <MenuItem value="Supervisors">Supervisor</MenuItem>
                        <MenuItem value="Technicians">Technician</MenuItem>
                      </Select>
                      <FormHelperText>
                        {userInfo?.role === "Administrators"
                          ? "As an Administrator, you can change user roles"
                          : "Only Administrators can change roles"}
                      </FormHelperText>
                    </FormControl>

                    {/* Debug info - for development only */}
                    {process.env.NODE_ENV === "development" && (
                      <Box mt={1}>
                        <Typography variant="caption" color="text.secondary">
                          Current user role: {userInfo?.role}, Selected role:{" "}
                          {profileData.role}
                        </Typography>
                      </Box>
                    )}
                  </Grid>

                  <Grid item xs={12}>
                    <TextField
                      fullWidth
                      label="Company"
                      name="company"
                      value={profileData.company}
                      onChange={handleProfileChange}
                      InputProps={{
                        startAdornment: (
                          <EditIcon color="action" sx={{ mr: 1 }} />
                        ),
                      }}
                    />
                  </Grid>

                  <Grid item xs={12}>
                    <Typography variant="h6" gutterBottom sx={{ mt: 2 }}>
                      Address
                    </Typography>
                    <Divider sx={{ mb: 3 }} />
                  </Grid>

                  <Grid item xs={12}>
                    <AddressAutocomplete
                      value={addressSearchTerm}
                      onChange={(value) => setAddressSearchTerm(value)}
                      onAddressSelected={(addressDetails) => {
                        // Update each address field in profileData
                        setProfileData({
                          ...profileData,
                          address: addressDetails.street,
                          city: addressDetails.city,
                          state: addressDetails.state,
                          zipCode: addressDetails.zipCode,
                        });
                      }}
                      label="Street Address"
                      textFieldProps={{ sx: { mb: 3 } }}
                    />
                  </Grid>

                  <Grid item xs={12} sm={6}>
                    <TextField
                      fullWidth
                      label="City"
                      name="city"
                      value={profileData.city}
                      onChange={handleProfileChange}
                      error={!!formErrors.city}
                      helperText={formErrors.city}
                    />
                  </Grid>

                  <Grid item xs={12} sm={3}>
                    <TextField
                      fullWidth
                      label="State"
                      name="state"
                      value={profileData.state}
                      onChange={handleProfileChange}
                      error={!!formErrors.state}
                      helperText={formErrors.state}
                    />
                  </Grid>

                  <Grid item xs={12} sm={3}>
                    <TextField
                      fullWidth
                      label="Zip Code"
                      name="zipCode"
                      value={profileData.zipCode}
                      onChange={handleProfileChange}
                      error={!!formErrors.zipCode}
                      helperText={formErrors.zipCode}
                    />
                  </Grid>

                  <Grid item xs={12}>
                    <Typography variant="h6" gutterBottom sx={{ mt: 2 }}>
                      Bio
                    </Typography>
                    <Divider sx={{ mb: 3 }} />

                    <TextField
                      fullWidth
                      multiline
                      rows={4}
                      label="Professional Bio"
                      name="bio"
                      value={profileData.bio}
                      onChange={handleProfileChange}
                      helperText="A brief professional description visible to your team"
                    />
                  </Grid>

                  <Grid item xs={12}>
                    <Box display="flex" justifyContent="flex-end" mt={2}>
                      <Button
                        variant="contained"
                        color="primary"
                        startIcon={
                          saveLoading ? (
                            <CircularProgress size={20} color="inherit" />
                          ) : (
                            <SaveIcon />
                          )
                        }
                        onClick={handleSaveProfile}
                        disabled={saveLoading}
                      >
                        {saveLoading ? "Saving..." : "Save Changes"}
                      </Button>
                    </Box>
                  </Grid>
                </Grid>
              )}
            </TabPanel>

            {/* Security Tab */}
            <TabPanel value={tabValue} index={1}>
              <Typography variant="h6" gutterBottom>
                Security Settings
              </Typography>
              <Divider sx={{ mb: 3 }} />

              <Box sx={{ maxWidth: 500, mx: "auto" }}>
                <Typography variant="h6" gutterBottom>
                  Change Password
                </Typography>

                <Grid container spacing={3}>
                  <Grid item xs={12}>
                    <TextField
                      fullWidth
                      type="password"
                      label="Current Password"
                      name="currentPassword"
                      value={securityData.currentPassword}
                      onChange={handleSecurityChange}
                    />
                  </Grid>

                  <Grid item xs={12}>
                    <TextField
                      fullWidth
                      type="password"
                      label="New Password"
                      name="newPassword"
                      value={securityData.newPassword}
                      onChange={handleSecurityChange}
                    />
                  </Grid>

                  <Grid item xs={12}>
                    <TextField
                      fullWidth
                      type="password"
                      label="Confirm New Password"
                      name="confirmPassword"
                      value={securityData.confirmPassword}
                      onChange={handleSecurityChange}
                      error={
                        securityData.newPassword !==
                          securityData.confirmPassword &&
                        securityData.confirmPassword !== ""
                      }
                      helperText={
                        securityData.newPassword !==
                          securityData.confirmPassword &&
                        securityData.confirmPassword !== ""
                          ? "Passwords don't match"
                          : ""
                      }
                    />
                  </Grid>

                  <Grid item xs={12}>
                    <Box display="flex" justifyContent="flex-end" mt={2}>
                      <Button
                        variant="contained"
                        color="primary"
                        startIcon={
                          saveLoading ? (
                            <CircularProgress size={20} color="inherit" />
                          ) : (
                            <SaveIcon />
                          )
                        }
                        onClick={handleChangePassword}
                        disabled={
                          saveLoading ||
                          !securityData.currentPassword ||
                          !securityData.newPassword ||
                          !securityData.confirmPassword ||
                          securityData.newPassword !==
                            securityData.confirmPassword
                        }
                      >
                        {saveLoading ? "Updating..." : "Update Password"}
                      </Button>
                    </Box>
                  </Grid>
                </Grid>

                <Box mt={4}>
                  <Typography variant="h6" gutterBottom>
                    Two-Factor Authentication
                  </Typography>
                  <Alert severity="info" sx={{ mb: 2 }}>
                    Two-factor authentication adds an extra layer of security to
                    your account
                  </Alert>
                  <Button variant="outlined" color="primary">
                    Enable Two-Factor Authentication
                  </Button>
                </Box>

                <Box mt={4}>
                  <Typography variant="h6" gutterBottom>
                    Login Sessions
                  </Typography>
                  <Alert severity="success" sx={{ mb: 2 }}>
                    You're currently logged in from 1 device
                  </Alert>
                  <Button variant="outlined" color="error">
                    Sign out from all devices
                  </Button>
                </Box>
              </Box>
            </TabPanel>

            {/* Notifications Tab */}
            <TabPanel value={tabValue} index={2}>
              <Typography variant="h6" gutterBottom>
                Notification Settings
              </Typography>
              <Divider sx={{ mb: 3 }} />

              <Alert severity="info" sx={{ mb: 3 }}>
                Notification settings are under development. Check back soon!
              </Alert>
            </TabPanel>

            {/* Permissions Tab (formerly Preferences) */}
            <TabPanel value={tabValue} index={3}>
              <Typography variant="h6" gutterBottom>
                User Permissions & Preferences
              </Typography>
              <Divider sx={{ mb: 3 }} />

              {/* User Role and Permissions Section */}
              <Box sx={{ maxWidth: 700, mx: "auto", mb: 4 }}>
                <Typography variant="h6" gutterBottom>
                  Role & Permissions
                </Typography>
                <Alert severity="info" sx={{ mb: 3 }}>
                  Your role is:{" "}
                  <strong>{userInfo?.role || "Not assigned"}</strong>. This
                  determines what actions you can perform in the system.
                </Alert>

                <Typography variant="subtitle1" gutterBottom>
                  Your Permissions:
                </Typography>
                <Box sx={{ display: "flex", flexWrap: "wrap", gap: 1, mb: 3 }}>
                  {userInfo?.permissions && userInfo.permissions.length > 0 ? (
                    userInfo.permissions.map((permission, index) => (
                      <Chip
                        key={index}
                        label={permission}
                        color="primary"
                        variant="outlined"
                        size="small"
                      />
                    ))
                  ) : (
                    <Typography variant="body2" color="text.secondary">
                      No specific permissions assigned
                    </Typography>
                  )}
                </Box>
              </Box>

              <Box sx={{ maxWidth: 700, mx: "auto" }}>
                <Grid container spacing={3}>
                  <Grid item xs={12}>
                    <Typography variant="h6" gutterBottom>
                      Business Type Settings
                    </Typography>
                    <Alert severity="info" sx={{ mb: 3 }}>
                      Your contractor type selection determines what services
                      the AI will suggest to you. Select the option that best
                      describes your business.
                    </Alert>

                    <FormControl fullWidth sx={{ mb: 3 }}>
                      <InputLabel id="contractor-type-label">
                        Contractor Type
                      </InputLabel>
                      <Select
                        labelId="contractor-type-label"
                        id="contractor-type"
                        name="contractorType"
                        value={profileData.contractorType || "Other"}
                        onChange={handleProfileChange}
                        label="Contractor Type"
                      >
                        <MenuItem value="HVAC">HVAC</MenuItem>
                        <MenuItem value="Plumbing">Plumbing</MenuItem>
                        <MenuItem value="Electrical">Electrical</MenuItem>
                        <MenuItem value="Landscaping">Landscaping</MenuItem>
                        <MenuItem value="Cleaning">Cleaning Services</MenuItem>
                        <MenuItem value="General Contractor">
                          General Contractor
                        </MenuItem>
                        <MenuItem value="Other">Other</MenuItem>
                      </Select>
                    </FormControl>

                    {profileData.contractorType === "Other" && (
                      <TextField
                        fullWidth
                        label="Services Offered"
                        name="servicesInput"
                        helperText="Enter services you offer, separated by commas"
                        value={profileData.servicesInput || ""}
                        onChange={handleProfileChange}
                        sx={{ mb: 3 }}
                      />
                    )}

                    <Box sx={{ mt: 2, mb: 4 }}>
                      <Typography variant="subtitle1" gutterBottom>
                        Services Tags:
                      </Typography>
                      <Box sx={{ display: "flex", flexWrap: "wrap", gap: 1 }}>
                        {(profileData.services || []).map((service, index) => (
                          <Chip
                            key={index}
                            label={service}
                            onDelete={() => handleRemoveService(service)}
                            color="primary"
                            variant="outlined"
                          />
                        ))}
                        {profileData.services?.length === 0 && (
                          <Typography variant="body2" color="text.secondary">
                            No services added yet
                          </Typography>
                        )}
                      </Box>
                      <Box sx={{ display: "flex", mt: 2 }}>
                        <TextField
                          size="small"
                          label="Add Service"
                          value={newService}
                          onChange={(e) => setNewService(e.target.value)}
                          sx={{ flexGrow: 1, mr: 1 }}
                        />
                        <Button
                          variant="contained"
                          onClick={handleAddService}
                          disabled={!newService.trim()}
                        >
                          Add
                        </Button>
                      </Box>
                    </Box>

                    <Typography variant="h6" gutterBottom sx={{ mt: 4 }}>
                      Address Settings
                    </Typography>
                    <Divider sx={{ mb: 3 }} />

                    <Alert severity="info" sx={{ mb: 3 }}>
                      Set your default address format and location preferences.
                    </Alert>

                    <FormControl fullWidth sx={{ mb: 3 }}>
                      <InputLabel id="location-format-label">
                        Address Format
                      </InputLabel>
                      <Select
                        labelId="location-format-label"
                        id="location-format"
                        name="addressFormat"
                        value={profileData.addressFormat || "US"}
                        onChange={handleProfileChange}
                        label="Address Format"
                      >
                        <MenuItem value="US">United States</MenuItem>
                        <MenuItem value="CA">Canada</MenuItem>
                        <MenuItem value="UK">United Kingdom</MenuItem>
                        <MenuItem value="AU">Australia</MenuItem>
                        <MenuItem value="International">International</MenuItem>
                      </Select>
                    </FormControl>

                    <AddressAutocomplete
                      value={addressSearchTerm}
                      onChange={(value) => setAddressSearchTerm(value)}
                      onAddressSelected={(addressDetails) => {
                        setProfileData({
                          ...profileData,
                          address: addressDetails,
                        });
                      }}
                      label="Default Address"
                      textFieldProps={{ sx: { mb: 3 } }}
                    />
                  </Grid>

                  <Grid item xs={12}>
                    <Box display="flex" justifyContent="flex-end" mt={2}>
                      <Button
                        variant="contained"
                        color="primary"
                        startIcon={
                          saveLoading ? (
                            <CircularProgress size={20} color="inherit" />
                          ) : (
                            <SaveIcon />
                          )
                        }
                        onClick={handleSaveProfile}
                        disabled={saveLoading}
                      >
                        {saveLoading ? "Saving..." : "Save Preferences"}
                      </Button>
                    </Box>
                  </Grid>
                </Grid>
              </Box>
            </TabPanel>
          </Paper>
        </Grid>
      </Grid>

      {/* Success/Error Snackbar */}
      <Snackbar
        open={success || error !== null}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: "bottom", horizontal: "right" }}
      >
        <Alert
          onClose={handleCloseSnackbar}
          severity={error ? "error" : "success"}
          sx={{ width: "100%" }}
        >
          {error ? error : "Changes saved successfully!"}
        </Alert>
      </Snackbar>
    </Container>
  );
};

export default Profile;
