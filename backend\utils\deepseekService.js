const { OpenAI } = require("openai");
const { withRetry, circuitBreakers } = require("./retryUtils");
const logger = require("./logger"); // Import logger
const {
  AiServiceUnavailableError,
  AiRateLimitError,
  AiInvalidResponseError,
  AiInvalidRequestError,
  AiContextLengthError,
  AiTimeoutError,
  AiContentPolicyError,
  isRetryableError, // Import the helper function
} = require("./aiErrors");

// Initialize the Deepseek API client - optional since it's a fallback service
if (!process.env.DEEPSEEK_API_KEY) {
  logger.warn(
    "DEEPSEEK_API_KEY environment variable not set - Deepseek fallback will be unavailable"
  );
}

const openai = process.env.DEEPSEEK_API_KEY
  ? new OpenAI({
      baseURL: "https://api.deepseek.com",
      apiKey: process.env.DEEPSEEK_API_KEY,
      timeout: parseInt(process.env.DEEPSEEK_TIMEOUT || "30000"), // Configurable timeout, default 30s
    })
  : null;

// Model configurations from environment variables
const MODELS = {
  FALLBACK2: {
    name: process.env.AI_MODEL_FALLBACK2 || "deepseek-chat",
    requestsPerMinute: parseInt(process.env.AI_FALLBACK2_RATE_LIMIT || "5"),
    lastRequest: 0,
    requestCount: 0,
  },
};

// Track API health metrics
const healthMetrics = {
  totalRequests: 0,
  successfulRequests: 0,
  failedRequests: 0,
  retryAttempts: 0,
  lastRequestTime: null,
  lastErrorTime: null,
  lastErrorMessage: null,
};

/**
 * Get a response from Deepseek API
 */
async function getDeepseekResponse(systemPrompt, userPrompt, options = {}) {
  // Update health metrics
  healthMetrics.totalRequests++;
  healthMetrics.lastRequestTime = Date.now();

  // Use circuit breaker to prevent cascading failures
  // Reset circuit breaker if it's been more than 1 minute since last failure
  const circuitBreakerState = circuitBreakers.deepseek.getState();
  if (
    circuitBreakerState.lastFailureTime &&
    Date.now() - circuitBreakerState.lastFailureTime > 60000
  ) {
    circuitBreakers.deepseek.reset();
  }

  return await circuitBreakers.deepseek.execute(async () => {
    try {
      // Use retry with exponential backoff
      const result = await withRetry(
        () =>
          executeWithModel(MODELS.FALLBACK2, systemPrompt, userPrompt, options),
        {
          maxRetries: 2,
          baseDelay: 500,
          maxDelay: 3000,
          shouldRetry: (error) => isRetryableError(error),
        }
      );

      healthMetrics.successfulRequests++;
      return result;
    } catch (error) {
      healthMetrics.failedRequests++;
      healthMetrics.lastErrorTime = Date.now();
      const mappedError = mapDeepseekError(error); // Map to custom error type
      healthMetrics.lastErrorMessage = mappedError.message;
      throw mappedError; // Throw the mapped error
    }
  });
}

/**
 * Execute request with specific model
 */
async function executeWithModel(
  modelConfig,
  systemPrompt,
  userPrompt,
  options = {}
) {
  try {
    // Check if Deepseek API is available
    if (!openai) {
      throw new AiServiceUnavailableError(
        "Deepseek API not configured - missing DEEPSEEK_API_KEY"
      );
    }

    // Removed basic rate limit check logic.
    // Relying on the API call to fail with a 429 status,
    // which is mapped to AiRateLimitError by mapDeepseekError,
    // and then handled by the withRetry logic in getDeepseekResponse.

    // Update model stats (can still be useful for monitoring, though not for limiting)
    modelConfig.lastRequest = Date.now();
    // modelConfig.requestCount++; // Count is less relevant without the check

    const messages = [
      { role: "system", content: systemPrompt },
      { role: "user", content: userPrompt },
    ];

    const completion = await openai.chat.completions.create({
      model: modelConfig.name,
      messages,
      temperature: options.temperature || 0.7,
      max_tokens: options.maxTokens || 500,
      top_p: options.topP || 0.95,
    });

    const response = completion.choices[0]?.message?.content;

    // Validate response
    if (!response || response.trim() === "") {
      logger.warn("Empty response from Deepseek, attempting retry"); // Use logger
      throw new AiInvalidResponseError("Empty response from AI");
    }

    return response;
  } catch (error) {
    throw mapDeepseekError(error);
  }
}

/**
 * Get a structured JSON response from Deepseek API
 */
async function getDeepseekJsonResponse(systemPrompt, userInput, options = {}) {
  const expectsArray =
    systemPrompt.includes("JSON ARRAY") || systemPrompt.includes("array of");

  const promptWithJsonInstructions = `${systemPrompt}

VERY IMPORTANT - FOLLOW THESE JSON FORMATTING RULES EXACTLY:
1. Respond with a valid JSON ${expectsArray ? "array" : "object"} only
2. No markdown, backticks, or other formatting
3. Begin with "${expectsArray ? "[" : "{"}" and end with "${
    expectsArray ? "]" : "}"
  }"
4. No explanatory text before or after
5. All strings in double quotes
6. No trailing commas
7. Proper escaping for special characters

User input: ${
    typeof userInput === "string" ? userInput : JSON.stringify(userInput)
  }`;

  try {
    const response = await getDeepseekResponse(promptWithJsonInstructions, "", {
      ...options,
      temperature: 0.1, // Lower temperature for more predictable JSON
    });

    // Parse and validate JSON
    let json;
    try {
      // Handle cases where response might be wrapped in markdown
      logger.debug(
        "Raw Deepseek Response (first 100):",
        response.substring(0, 100)
      ); // Log snippet

      // Attempt to extract JSON more robustly
      let jsonStr = response.trim();

      // Remove potential markdown fences
      if (jsonStr.startsWith("```json")) {
        jsonStr = jsonStr.substring(7);
        if (jsonStr.endsWith("```")) {
          jsonStr = jsonStr.substring(0, jsonStr.length - 3);
        }
        jsonStr = jsonStr.trim();
      } else if (jsonStr.startsWith("```")) {
        jsonStr = jsonStr.substring(3);
        if (jsonStr.endsWith("```")) {
          jsonStr = jsonStr.substring(0, jsonStr.length - 3);
        }
        jsonStr = jsonStr.trim();
      }

      // Find the first opening bracket/brace and the last closing one
      const firstBrace = jsonStr.indexOf("{");
      const firstBracket = jsonStr.indexOf("[");
      let startIdx = -1;

      if (firstBrace === -1 && firstBracket === -1) {
        logger.error(
          "Deepseek response contained no JSON start character ({ or [)"
        );
        throw new AiInvalidResponseError(
          "No JSON object or array found in Deepseek response"
        );
      } else if (firstBrace === -1) {
        startIdx = firstBracket;
      } else if (firstBracket === -1) {
        startIdx = firstBrace;
      } else {
        startIdx = Math.min(firstBrace, firstBracket);
      }

      const lastBrace = jsonStr.lastIndexOf("}");
      const lastBracket = jsonStr.lastIndexOf("]");
      let endIdx = Math.max(lastBrace, lastBracket);

      if (startIdx === -1 || endIdx === -1 || endIdx < startIdx) {
        logger.error(
          "Deepseek response JSON boundaries invalid (start/end mismatch)"
        );
        throw new AiInvalidResponseError(
          "Invalid JSON boundaries in Deepseek response"
        );
      }

      // Extract the potential JSON string
      jsonStr = jsonStr.substring(startIdx, endIdx + 1);

      // Basic check for empty object/array before parsing
      if (jsonStr === "{}" || jsonStr === "[]") {
        logger.warn(
          "Deepseek response extracted to an empty JSON object/array."
        );
        // Depending on requirements, might allow empty or throw error. Let's allow for now.
      }

      json = JSON.parse(jsonStr); // Attempt parsing the extracted string

      // Validate minimum response length
      if (JSON.stringify(json).length < 20) {
        throw new AiInvalidResponseError("Response too short");
      }
    } catch (e) {
      logger.error("Failed to parse Deepseek JSON:", e); // Use logger
      throw new AiInvalidResponseError("Invalid JSON response");
    }

    // Validate required fields if specified
    if (options.requiredFields) {
      for (const field of options.requiredFields) {
        if (!(field in json)) {
          throw new AiInvalidResponseError(`Missing required field: ${field}`);
        }
      }
    }

    return json;
  } catch (error) {
    logger.error("Deepseek JSON error:", {
      message: error.message,
      stack: error.stack,
    }); // Use logger
    // Pass options to fallback generator
    return getFallbackJsonResponse(
      userInput,
      options,
      error.message,
      expectsArray
    );
  }
}

/**
 * Map Deepseek API errors to our custom error types
 */
function mapDeepseekError(error) {
  if (error.status === 429) {
    return new AiRateLimitError("Deepseek rate limit exceeded");
  }
  if (error.status >= 500) {
    return new AiServiceUnavailableError("Deepseek service unavailable");
  }
  if (error.message?.includes("context length")) {
    return new AiContextLengthError("Context length exceeded");
  }
  if (error.status >= 400 && error.status < 500) {
    return new AiInvalidRequestError("Invalid request to Deepseek");
  }
  return error;
}

/**
 * Generate fallback JSON response
 */
function getFallbackJsonResponse(
  userInput,
  options = {},
  errorMessage = null,
  isArrayResponse = false
) {
  const errorMsg = errorMessage || "Deepseek service unavailable";

  // Base fallback structure
  let fallback = {
    error: errorMsg,
    message: "Fallback response - please try again later",
    timestamp: new Date().toISOString(),
    fallback: true, // Indicate this is a fallback
  };

  // Add required fields with sensible defaults, similar to aiService.js
  if (options?.requiredFields?.length) {
    options.requiredFields.forEach((field) => {
      if (!fallback[field]) {
        // Basic type guessing for defaults
        fallback[field] = field.toLowerCase().includes("date")
          ? new Date().toISOString()
          : field.toLowerCase().includes("array") ||
            field.toLowerCase().includes("list")
          ? []
          : field.toLowerCase().includes("count") ||
            field.toLowerCase().includes("number") ||
            field.toLowerCase().includes("quantity")
          ? 0
          : ""; // Default to empty string
      }
    });
  }

  // Ensure array format if expected
  if (isArrayResponse) {
    // If the base fallback isn't already an array, wrap it
    // (This might need adjustment based on expected array item structure)
    return [fallback];
  }

  return fallback;
}

/**
 * Get health metrics
 */
function getHealthMetrics() {
  return {
    ...healthMetrics,
    successRate:
      healthMetrics.totalRequests > 0
        ? (
            (healthMetrics.successfulRequests / healthMetrics.totalRequests) *
            100
          ).toFixed(2) + "%"
        : "N/A",
    model: {
      name: MODELS.FALLBACK2.name,
      rateLimit: MODELS.FALLBACK2.requestsPerMinute,
      currentCount: MODELS.FALLBACK2.requestCount,
      lastRequest: MODELS.FALLBACK2.lastRequest,
    },
  };
}

module.exports = {
  getDeepseekResponse,
  getDeepseekJsonResponse,
  getHealthMetrics,
};
