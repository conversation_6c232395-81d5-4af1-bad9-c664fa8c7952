import React from "react";
import {
  Box,
  Card,
  CardContent,
  CardActions,
  Typography,
  Button,
  CardActionArea,
} from "@mui/material";

/**
 * QuickActionCard component for dashboard quick actions
 *
 * @param {Object} props - Component props
 * @param {string} props.title - Action title
 * @param {string} props.description - Action description
 * @param {React.ComponentType} props.icon - Material UI Icon component
 * @param {string} props.action - Action button text
 * @param {Function} props.onClick - Function to execute on card click
 * @param {string} [props.color] - Optional color for the card (primary, secondary, etc.)
 * @returns {JSX.Element} QuickActionCard component
 */
const QuickActionCard = ({
  title,
  description,
  icon: Icon,
  action,
  onClick,
  color = "secondary",
}) => (
  <Card
    sx={{
      height: "100%",
      display: "flex",
      flexDirection: "column",
      borderTop: 3,
      borderColor: `${color}.main`,
    }}
  >
    <CardActionArea
      onClick={onClick}
      sx={{
        flexGrow: 1,
        display: "flex",
        flexDirection: "column",
        alignItems: "flex-start",
      }}
    >
      <CardContent sx={{ width: "100%" }}>
        <Box sx={{ display: "flex", alignItems: "center", mb: 2 }}>
          <Icon sx={{ mr: 1, color: `${color}.main` }} />
          <Typography variant="h6" component="div">
            {title}
          </Typography>
        </Box>
        <Typography variant="body2" color="text.secondary">
          {description}
        </Typography>
      </CardContent>
    </CardActionArea>
    <CardActions>
      <Button size="small" color={color} onClick={onClick}>
        {action}
      </Button>
    </CardActions>
  </Card>
);

export default QuickActionCard;
