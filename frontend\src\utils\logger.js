// frontend/src/utils/logger.js
/**
 * Basic Frontend Logger
 * Wraps console methods for consistent logging.
 * Includes a conditional debug method.
 */
const logger = {
  log: (...args) => {
    console.log(...args);
  },
  info: (...args) => {
    console.info(...args);
  },
  warn: (...args) => {
    console.warn(...args);
  },
  error: (...args) => {
    console.error(...args);
  },
  // Conditionally enable debug logging in development
  debug: (...args) => {
    // Use process.env.NODE_ENV which is set by Create React App
    if (process.env.NODE_ENV === "development") {
      console.debug(...args);
    }
  },
};

export default logger;
