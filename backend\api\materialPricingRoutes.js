const express = require("express");
const router = express.Router();
const {
  getMaterialPriceBySku,
  searchMaterialPrice,
} = require("../controllers/materialPricingController");
const { protect } = require("../middleware/authMiddleware");

// All routes are protected
router.use(protect);

// Route for getting price by SKU
router.get("/sku/:sku", getMaterialPriceBySku);

// Route for searching price by description
router.get("/search/:query", searchMaterialPrice);

module.exports = router;
