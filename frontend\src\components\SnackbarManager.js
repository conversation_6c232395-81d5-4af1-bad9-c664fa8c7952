import React, { useEffect } from "react";
import { useSelector, useDispatch } from "react-redux";
import { Snackbar, Alert } from "@mui/material";
import { removeSnackbar } from "../slices/snackbarSlice";

/**
 * Global notification manager component
 * Displays snackbar notifications from anywhere in the application
 */
const SnackbarManager = () => {
  const dispatch = useDispatch();
  const { notifications } = useSelector((state) => state.snackbar);

  useEffect(() => {
    // Clean up any stale notifications on component mount
    return () => {
      notifications.forEach((notification) =>
        dispatch(removeSnackbar(notification.key))
      );
    };
  }, [dispatch, notifications]);

  const handleClose = (event, reason, key) => {
    if (reason === "clickaway") {
      return;
    }
    dispatch(removeSnackbar(key));
  };

  return (
    <>
      {notifications.map((notification) => (
        <Snackbar
          key={notification.key}
          open={true}
          autoHideDuration={notification.autoHideDuration}
          onClose={(event, reason) =>
            handleClose(event, reason, notification.key)
          }
          anchorOrigin={{ vertical: "bottom", horizontal: "right" }}
        >
          <Alert
            onClose={(event) => handleClose(event, "close", notification.key)}
            severity={notification.variant}
            variant="filled"
            sx={{ width: "100%" }}
          >
            {notification.message}
          </Alert>
        </Snackbar>
      ))}
    </>
  );
};

export default SnackbarManager;
