/**
 * MaterialSource.js
 * Model for material sources (suppliers)
 */

const mongoose = require("mongoose");

/**
 * Schema for material sources such as Home Depot, Grainger, etc.
 */
const materialSourceSchema = new mongoose.Schema(
  {
    name: {
      type: String,
      required: true,
      trim: true,
    },
    type: {
      type: String,
      required: true,
      enum: ["HOME_DEPOT", "PLATT", "GRAYBAR", "CUSTOM"], // Replaced GRAINGER with PLATT
      default: "CUSTOM",
    },
    baseUrl: {
      type: String,
      required: true,
      trim: true,
    },
    enabled: {
      type: Boolean,
      default: true,
    },
    credentials: {
      username: String,
      password: String,
      apiKey: String,
      refreshToken: String,
      accessToken: String,
      tokenExpiresAt: Date,
    },
    rateLimit: {
      requestsPerMinute: {
        type: Number,
        default: 10,
      },
      cooldownPeriod: {
        type: Number,
        default: 0, // ms to wait after hitting rate limit
      },
    },
    proxySettings: {
      useProxy: {
        type: Boolean,
        default: false,
      },
      proxyType: {
        type: String,
        enum: ["ROTATING", "STATIC", "RESIDENTIAL"],
        default: "ROTATING",
      },
      proxyUrl: String,
      proxyAuth: {
        username: String,
        password: String,
      },
      proxyList: [String], // List of proxies to rotate through
    },
    scrapeConfig: {
      selectors: {
        type: Map,
        of: String,
      },
      allowedDomains: [String],
      excludePatterns: [String],
      sessionDuration: {
        type: Number,
        default: 3600, // 1 hour
      },
      userAgents: [String],
    },
    lastScrapedAt: Date,
    scrapeStatus: {
      type: String,
      enum: ["IDLE", "RUNNING", "ERROR", "RATE_LIMITED"],
      default: "IDLE",
    },
    company: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Company",
    },
  },
  { timestamps: true }
);

// Add any pre-save hooks or methods here as needed
materialSourceSchema.pre("save", function (next) {
  // If credentials are provided, ensure they are properly encrypted
  // This is just a placeholder - you would implement proper encryption
  next();
});

// Method to check if credentials are valid
materialSourceSchema.methods.hasValidCredentials = function () {
  if (this.type === "CUSTOM") {
    return true; // Custom sources don't require credentials
  }

  // Each source type might have different credential requirements
  switch (this.type) {
    case "HOME_DEPOT":
      // For now, Home Depot scraping doesn't require authentication
      return true;
    case "PLATT": // Updated case
      // Assuming Platt might need credentials, adjust if not needed
      return !!(
        this.credentials &&
        this.credentials.username &&
        this.credentials.password
      );
    case "GRAYBAR":
      return !!(
        this.credentials &&
        this.credentials.username &&
        this.credentials.password
      );
    default:
      return false;
  }
};

// Database indexes for performance optimization
materialSourceSchema.index({ name: 1 }); // Source name lookup
materialSourceSchema.index({ type: 1, enabled: 1 }); // Source type filtering
materialSourceSchema.index({ enabled: 1, scrapeStatus: 1 }); // Active sources
materialSourceSchema.index({ company: 1 }); // Company-specific sources
materialSourceSchema.index({ lastScrapedAt: 1 }); // Scraping schedule
materialSourceSchema.index({ scrapeStatus: 1, lastScrapedAt: 1 }); // Scraping monitoring
materialSourceSchema.index({ "rateLimit.requestsPerMinute": 1 }); // Rate limit management
materialSourceSchema.index({ createdAt: -1 }); // Recent sources

const MaterialSource = mongoose.model("MaterialSource", materialSourceSchema);

module.exports = MaterialSource;
