{"version": "0.2.0", "configurations": [{"name": "Debug Backend Server", "type": "node", "request": "launch", "program": "${workspaceFolder}\\backend\\server.js", "cwd": "${workspaceFolder}\\backend", "env": {"NODE_ENV": "development", "LOG_LEVEL": "debug"}, "console": "integratedTerminal", "restart": true, "runtimeExecutable": "node", "skipFiles": ["<node_internals>/**"], "preLaunchTask": "Start Database Only"}, {"name": "Debug Backend Tests", "type": "node", "request": "launch", "program": "${workspaceFolder}\\backend\\node_modules\\jest\\bin\\jest.js", "args": ["--runInBand", "--no-cache", "--no-coverage"], "cwd": "${workspaceFolder}\\backend", "env": {"NODE_ENV": "test"}, "console": "integratedTerminal", "internalConsoleOptions": "neverOpen", "skipFiles": ["<node_internals>/**"]}]}