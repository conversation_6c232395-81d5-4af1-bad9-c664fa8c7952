/**
 * Streaming AI Comprehensive Tests
 * Tests for Server-Sent Events, session management, and streaming AI fixes
 */

const { test, expect } = require('@playwright/test');

test.describe('Streaming AI Comprehensive Tests', () => {
  let authToken;
  let page;
  
  const testUser = {
    email: '<EMAIL>',
    password: 'password123'
  };

  test.beforeAll(async ({ request }) => {
    // Login to get auth token
    const response = await request.post('http://localhost:5000/api/users/login', {
      data: testUser
    });
    
    expect(response.status()).toBe(200);
    const responseData = await response.json();
    authToken = responseData.token;
  });

  test.beforeEach(async ({ browser }) => {
    page = await browser.newPage();
    // Set auth token in localStorage
    await page.goto('http://localhost:3000');
    await page.evaluate((token) => {
      localStorage.setItem('token', token);
    }, authToken);
  });

  test.afterEach(async () => {
    await page.close();
  });

  test.describe('SSE Connection Management', () => {
    test('should handle session creation and connection', async ({ request }) => {
      // Create streaming session
      const sessionResponse = await request.post('http://localhost:5000/api/streaming/create-session', {
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json'
        },
        data: {
          inputType: 'materials',
          textInputData: [
            { name: 'Test Material 1', quantity: 1, description: 'Test description' },
            { name: 'Test Material 2', quantity: 2, description: 'Another test' }
          ],
          context: {
            jobType: 'electrical',
            customerDescription: 'Test customer project',
            projectOverview: 'Comprehensive electrical work'
          }
        }
      });

      expect(sessionResponse.status()).toBe(200);
      const sessionData = await sessionResponse.json();
      expect(sessionData.success).toBe(true);
      expect(sessionData.sessionId).toBeDefined();
      expect(sessionData.sessionId).toMatch(/^[a-f0-9-]{36}$/);
    });

    test('should handle invalid session IDs gracefully', async ({ request }) => {
      const invalidSessionId = 'invalid-session-id';
      
      const response = await request.get(`http://localhost:5000/api/streaming/connect/${invalidSessionId}`, {
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Accept': 'text/event-stream'
        }
      });

      expect(response.status()).toBe(404);
      const responseData = await response.json();
      expect(responseData.error).toContain('Session not found');
    });

    test('should handle session expiration', async ({ request }) => {
      // Create a session
      const sessionResponse = await request.post('http://localhost:5000/api/streaming/create-session', {
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json'
        },
        data: {
          inputType: 'overview',
          textInputData: 'Test project overview for session expiration test',
          context: { jobType: 'electrical' }
        }
      });

      const sessionData = await sessionResponse.json();
      const sessionId = sessionData.sessionId;

      // Wait for session to potentially expire (if configured with short timeout)
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Try to connect to potentially expired session
      const connectResponse = await request.get(`http://localhost:5000/api/streaming/connect/${sessionId}`, {
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Accept': 'text/event-stream'
        }
      });

      // Should either connect successfully or return session expired error
      if (connectResponse.status() === 410) {
        const responseData = await connectResponse.json();
        expect(responseData.error).toContain('expired');
      } else {
        expect(connectResponse.status()).toBe(200);
      }
    });
  });

  test.describe('SSE Message Handling', () => {
    test('should receive heartbeat messages', async ({ request }) => {
      // Create session
      const sessionResponse = await request.post('http://localhost:5000/api/streaming/create-session', {
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json'
        },
        data: {
          inputType: 'materials',
          textInputData: [{ name: 'Heartbeat Test Material', quantity: 1 }],
          context: { jobType: 'electrical' }
        }
      });

      const sessionData = await sessionResponse.json();
      const sessionId = sessionData.sessionId;

      // Connect to SSE stream
      const streamResponse = await request.get(`http://localhost:5000/api/streaming/connect/${sessionId}`, {
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Accept': 'text/event-stream'
        }
      });

      expect(streamResponse.status()).toBe(200);
      expect(streamResponse.headers()['content-type']).toContain('text/event-stream');
    });

    test('should handle message types correctly', async () => {
      await page.goto('http://localhost:3000/create-quote');
      
      // Mock SSE messages with proper type fields
      await page.route('**/api/streaming/connect/**', async (route) => {
        const mockSSEResponse = [
          'event: heartbeat',
          'data: {"type":"heartbeat","timestamp":"' + new Date().toISOString() + '"}',
          '',
          'event: connected',
          'data: {"type":"connected","message":"Connection established"}',
          '',
          'event: progress',
          'data: {"type":"progress","phase":"analyzing","message":"Analyzing materials..."}',
          '',
          'event: complete',
          'data: {"type":"complete","result":{"success":true,"quote":"Generated quote"}}',
          ''
        ].join('\n');

        await route.fulfill({
          status: 200,
          headers: {
            'Content-Type': 'text/event-stream',
            'Cache-Control': 'no-cache',
            'Connection': 'keep-alive'
          },
          body: mockSSEResponse
        });
      });

      // Fill form and start streaming
      await page.fill('[data-testid="customer-name"]', 'SSE Test Customer');
      await page.fill('[data-testid="project-overview"]', 'SSE message type test');
      await page.check('[data-testid="enable-streaming"]');
      await page.click('[data-testid="generate-streaming-quote"]');

      // Should handle different message types
      await expect(page.locator('[data-testid="streaming-status"]')).toContainText('Connection established');
      await expect(page.locator('[data-testid="streaming-progress"]')).toContainText('Analyzing materials');
    });
  });

  test.describe('Error Recovery and Reconnection', () => {
    test('should handle connection drops and reconnect', async () => {
      await page.goto('http://localhost:3000/create-quote');
      
      let connectionAttempts = 0;
      
      // Mock intermittent connection failures
      await page.route('**/api/streaming/connect/**', async (route) => {
        connectionAttempts++;
        
        if (connectionAttempts === 1) {
          // First attempt fails
          await route.abort('failed');
        } else {
          // Second attempt succeeds
          const mockSSEResponse = [
            'event: connected',
            'data: {"type":"connected","message":"Reconnected successfully"}',
            '',
            'event: complete',
            'data: {"type":"complete","result":{"success":true,"quote":"Generated after reconnection"}}',
            ''
          ].join('\n');

          await route.fulfill({
            status: 200,
            headers: {
              'Content-Type': 'text/event-stream',
              'Cache-Control': 'no-cache',
              'Connection': 'keep-alive'
            },
            body: mockSSEResponse
          });
        }
      });

      // Fill form and start streaming
      await page.fill('[data-testid="customer-name"]', 'Reconnection Test');
      await page.fill('[data-testid="project-overview"]', 'Test reconnection handling');
      await page.check('[data-testid="enable-streaming"]');
      await page.click('[data-testid="generate-streaming-quote"]');

      // Should eventually succeed after reconnection
      await expect(page.locator('[data-testid="streaming-status"]')).toContainText('Reconnected successfully', { timeout: 30000 });
    });

    test('should fall back to traditional mode after max retries', async () => {
      await page.goto('http://localhost:3000/create-quote');
      
      // Mock all streaming requests to fail
      await page.route('**/api/streaming/**', async (route) => {
        await route.abort('failed');
      });
      
      // Mock traditional AI endpoint to succeed
      await page.route('**/api/ai/generate-quote', async (route) => {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            success: true,
            quote: 'Generated via traditional mode fallback'
          })
        });
      });

      // Fill form and start streaming
      await page.fill('[data-testid="customer-name"]', 'Fallback Test');
      await page.fill('[data-testid="project-overview"]', 'Test fallback to traditional mode');
      await page.check('[data-testid="enable-streaming"]');
      await page.click('[data-testid="generate-streaming-quote"]');

      // Should show fallback message
      await expect(page.locator('.snackbar')).toContainText('Switching to traditional mode', { timeout: 30000 });
      
      // Should complete with traditional mode
      await expect(page.locator('[data-testid="quote-result"]')).toContainText('Generated via traditional mode fallback');
    });
  });

  test.describe('Session Statistics and Monitoring', () => {
    test('should track session statistics correctly', async ({ request }) => {
      // Create session
      const sessionResponse = await request.post('http://localhost:5000/api/streaming/create-session', {
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json'
        },
        data: {
          inputType: 'materials',
          textInputData: [{ name: 'Stats Test Material', quantity: 1 }],
          context: { jobType: 'electrical' }
        }
      });

      const sessionData = await sessionResponse.json();
      const sessionId = sessionData.sessionId;

      // Get session stats
      const statsResponse = await request.get(`http://localhost:5000/api/streaming/session-stats/${sessionId}`, {
        headers: {
          'Authorization': `Bearer ${authToken}`
        }
      });

      expect(statsResponse.status()).toBe(200);
      const statsData = await statsResponse.json();
      expect(statsData.sessionId).toBe(sessionId);
      expect(statsData.status).toBeDefined();
      expect(statsData.createdAt).toBeDefined();
    });

    test('should handle concurrent session stats requests', async ({ request }) => {
      // Create session
      const sessionResponse = await request.post('http://localhost:5000/api/streaming/create-session', {
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json'
        },
        data: {
          inputType: 'overview',
          textInputData: 'Concurrent stats test project',
          context: { jobType: 'electrical' }
        }
      });

      const sessionData = await sessionResponse.json();
      const sessionId = sessionData.sessionId;

      // Make multiple concurrent stats requests
      const statsRequests = Array(10).fill().map(() => 
        request.get(`http://localhost:5000/api/streaming/session-stats/${sessionId}`, {
          headers: {
            'Authorization': `Bearer ${authToken}`
          }
        })
      );

      const responses = await Promise.all(statsRequests);
      
      // All requests should succeed
      responses.forEach(response => {
        expect(response.status()).toBe(200);
      });
    });
  });

  test.describe('Clarification Handling in Streaming', () => {
    test('should handle clarification requests in streaming mode', async () => {
      await page.goto('http://localhost:3000/create-quote');
      
      // Mock streaming response with clarification request
      await page.route('**/api/streaming/connect/**', async (route) => {
        const mockSSEResponse = [
          'event: connected',
          'data: {"type":"connected","message":"Connection established"}',
          '',
          'event: clarification_required',
          'data: {"type":"clarification_required","questions":[{"id":"q1","question":"What is the building size?","explanation":"We need to know the square footage"}]}',
          ''
        ].join('\n');

        await route.fulfill({
          status: 200,
          headers: {
            'Content-Type': 'text/event-stream',
            'Cache-Control': 'no-cache',
            'Connection': 'keep-alive'
          },
          body: mockSSEResponse
        });
      });

      // Fill form and start streaming
      await page.fill('[data-testid="customer-name"]', 'Clarification Test');
      await page.fill('[data-testid="project-overview"]', 'Test clarification in streaming');
      await page.check('[data-testid="enable-streaming"]');
      await page.click('[data-testid="generate-streaming-quote"]');

      // Should show clarification dialog
      await expect(page.locator('[data-testid="clarification-dialog"]')).toBeVisible();
      await expect(page.locator('.clarification-dialog')).toContainText('What is the building size?');
    });

    test('should handle empty clarification questions in streaming', async () => {
      await page.goto('http://localhost:3000/create-quote');
      
      // Mock streaming response with empty clarification
      await page.route('**/api/streaming/connect/**', async (route) => {
        const mockSSEResponse = [
          'event: connected',
          'data: {"type":"connected","message":"Connection established"}',
          '',
          'event: clarification_required',
          'data: {"type":"clarification_required","questions":[]}',
          ''
        ].join('\n');

        await route.fulfill({
          status: 200,
          headers: {
            'Content-Type': 'text/event-stream',
            'Cache-Control': 'no-cache',
            'Connection': 'keep-alive'
          },
          body: mockSSEResponse
        });
      });

      // Fill form and start streaming
      await page.fill('[data-testid="customer-name"]', 'Empty Clarification Test');
      await page.fill('[data-testid="project-overview"]', 'Test empty clarification');
      await page.check('[data-testid="enable-streaming"]');
      await page.click('[data-testid="generate-streaming-quote"]');

      // Should show clarification dialog with appropriate message
      await expect(page.locator('[data-testid="clarification-dialog"]')).toBeVisible();
      await expect(page.locator('.clarification-dialog')).toContainText('No clarification questions are available');
      await expect(page.locator('.clarification-dialog')).toContainText('AI streaming service will continue processing');
    });
  });
});