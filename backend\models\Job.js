const mongoose = require("mongoose");

const jobSchema = new mongoose.Schema(
  {
    title: {
      type: String,
      required: true,
      trim: true,
    },
    customer: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Customer",
      required: true,
    },
    description: {
      type: String,
      trim: true,
    },
    status: {
      type: String,
      enum: ["Scheduled", "In Progress", "Completed", "Cancelled"],
      default: "Scheduled",
    },
    priority: {
      type: String,
      enum: ["Low", "Medium", "High", "Urgent"],
      default: "Medium",
    },
    scheduledDate: {
      type: Date,
      required: true,
    },
    estimatedDuration: {
      type: Number, // in minutes
      required: true,
    },
    location: {
      address: {
        type: String,
        required: true,
      },
      city: String,
      state: String,
      zipCode: String,
      country: String,
      coordinates: {
        lat: Number,
        lng: Number,
      },
    },
    assignedTechnicians: [
      {
        type: mongoose.Schema.Types.ObjectId,
        ref: "User",
      },
    ],
    tasks: [
      {
        description: {
          type: String,
          required: true,
        },
        completed: {
          type: Boolean,
          default: false,
        },
        completedAt: Date,
        completedBy: {
          type: mongoose.Schema.Types.ObjectId,
          ref: "User",
        },
      },
    ],
    equipmentUsed: [
      {
        item: {
          type: mongoose.Schema.Types.ObjectId,
          ref: "Material",
        },
        quantity: Number,
      },
    ],
    notes: [
      {
        content: {
          type: String,
          required: true,
        },
        createdBy: {
          type: mongoose.Schema.Types.ObjectId,
          ref: "User",
        },
        createdAt: {
          type: Date,
          default: Date.now,
        },
      },
    ],
    attachments: [
      {
        filename: String,
        url: String, // Relative path, e.g., /uploads/jobs/<jobId>/<filename>
        mimeType: String, // Added: e.g., 'image/jpeg', 'image/png'
        tag: String, // Added: e.g., 'before', 'after', 'quote_context', 'general'
        uploadedBy: {
          type: mongoose.Schema.Types.ObjectId,
          ref: "User",
        },
        uploadedAt: {
          type: Date,
          default: Date.now,
        },
      },
    ],
    signatures: {
      customer: {
        signature: String,
        signedBy: String,
        signedAt: Date,
      },
      technician: {
        signature: String,
        signedBy: {
          type: mongoose.Schema.Types.ObjectId,
          ref: "User",
        },
        signedAt: Date,
      },
    },
    invoice: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Invoice",
    },
    metadata: {
      createdBy: {
        type: mongoose.Schema.Types.ObjectId,
        ref: "User",
        required: true,
      },
      lastModifiedBy: {
        type: mongoose.Schema.Types.ObjectId,
        ref: "User",
        required: true,
      },
    },
    // New AI-related fields
    aiInsights: {
      complexity: {
        type: String,
        enum: ["Low", "Medium", "High"],
        default: "Medium",
      },
      recommendedSkills: [String],
      specialConsiderations: [String],
      assignmentReasoning: String,
      priorityScore: {
        type: Number,
        min: 1,
        max: 100,
        default: 50,
      },
      nextSteps: [String],
      lastAnalyzed: Date,
    },
    riskAssessment: {
      risks: [
        {
          factor: String,
          severity: {
            type: String,
            enum: ["Low", "Medium", "High", "Critical"],
          },
          probability: {
            type: String,
            enum: ["Low", "Medium", "High"],
          },
          mitigation: String,
        },
      ],
      overallRiskLevel: {
        type: String,
        enum: ["Low", "Medium", "High", "Critical"],
        default: "Low",
      },
      lastAssessed: Date,
    },
    progressPrediction: {
      estimatedCompletion: Date,
      confidenceScore: {
        type: Number,
        min: 0,
        max: 100,
      },
      potentialDelays: [String],
      completionRate: Number, // tasks completed per hour
      lastUpdated: Date,
    },
    similarityVectors: {
      keywords: [String],
      jobType: String,
      complexity: Number,
      equipmentRequired: [String],
      locationCharacteristics: [String],
    },
  },
  {
    timestamps: true,
  }
);

// Pre-save middleware to update lastModifiedBy
jobSchema.pre("save", function (next) {
  this.metadata.lastModifiedBy = this.metadata.createdBy;
  next();
});

// Method to check if job can be scheduled
jobSchema.methods.canBeScheduled = async function () {
  // Check for technician availability
  const overlappingJobs = await this.model("Job").find({
    assignedTechnicians: { $in: this.assignedTechnicians },
    scheduledDate: {
      $lt: new Date(
        this.scheduledDate.getTime() + this.estimatedDuration * 60000
      ),
      $gt: this.scheduledDate,
    },
    status: { $nin: ["Completed", "Cancelled"] },
  });

  return overlappingJobs.length === 0;
};

// Virtual for job duration in hours
jobSchema.virtual("durationHours").get(function () {
  return this.estimatedDuration / 60;
});

// Virtual for full address
jobSchema.virtual("fullAddress").get(function () {
  const location = this.location;
  return [
    location.address,
    location.city,
    location.state,
    location.zipCode,
    location.country,
  ]
    .filter(Boolean)
    .join(", ");
});

// Method to generate invoice
jobSchema.methods.generateInvoice = async function () {
  if (this.status !== "Completed") {
    throw new Error("Cannot generate invoice for incomplete job");
  }

  if (this.invoice) {
    throw new Error("Invoice already exists for this job");
  }

  const Invoice = this.model("Invoice");
  const invoice = new Invoice({
    customer: this.customer,
    job: this._id,
    items: [], // To be filled based on business logic
    status: "Draft",
  });

  await invoice.save();
  this.invoice = invoice._id;
  await this.save();

  return invoice;
};

// Method to calculate similarity score with another job
jobSchema.methods.calculateSimilarityScore = function (otherJob) {
  // Simple implementation based on matching criteria
  let score = 0;
  let matchingFactors = [];

  // Check similarity in job title and description (simple keyword matching)
  const keywords = this.similarityVectors.keywords || [];
  const otherKeywords = otherJob.similarityVectors.keywords || [];

  const matchingKeywords = keywords.filter((kw) => otherKeywords.includes(kw));
  if (matchingKeywords.length > 0) {
    score += 20 * (matchingKeywords.length / keywords.length);
    matchingFactors.push("Similar keywords");
  }

  // Check similarity in job type
  if (this.similarityVectors.jobType === otherJob.similarityVectors.jobType) {
    score += 20;
    matchingFactors.push("Same job type");
  }

  // Check complexity
  const complexityDiff = Math.abs(
    (this.similarityVectors.complexity || 0) -
      (otherJob.similarityVectors.complexity || 0)
  );
  if (complexityDiff < 0.3) {
    score += 20 * (1 - complexityDiff);
    matchingFactors.push("Similar complexity");
  }

  // Check equipment requirements
  const equipment = this.similarityVectors.equipmentRequired || [];
  const otherEquipment = otherJob.similarityVectors.equipmentRequired || [];

  const matchingEquipment = equipment.filter((eq) =>
    otherEquipment.includes(eq)
  );
  if (matchingEquipment.length > 0) {
    score += 20 * (matchingEquipment.length / equipment.length);
    matchingFactors.push("Similar equipment requirements");
  }

  // Check location characteristics
  const locationFeatures = this.similarityVectors.locationCharacteristics || [];
  const otherLocationFeatures =
    otherJob.similarityVectors.locationCharacteristics || [];

  const matchingLocationFeatures = locationFeatures.filter((lf) =>
    otherLocationFeatures.includes(lf)
  );
  if (matchingLocationFeatures.length > 0) {
    score += 20 * (matchingLocationFeatures.length / locationFeatures.length);
    matchingFactors.push("Similar location type");
  }

  return {
    similarityScore: Math.min(100, score),
    matchingFactors,
  };
};

// Database indexes for performance optimization
jobSchema.index({ customer: 1, status: 1 }); // Customer jobs by status
jobSchema.index({ scheduledDate: 1, status: 1 }); // Scheduled jobs
jobSchema.index({ assignedTechnicians: 1, scheduledDate: 1 }); // Technician schedules
jobSchema.index({ status: 1, priority: 1 }); // Job queue optimization
jobSchema.index({ "location.city": 1, "location.state": 1 }); // Location-based queries
jobSchema.index({ createdAt: -1 }); // Recent jobs
jobSchema.index({ "metadata.createdBy": 1, createdAt: -1 }); // User's jobs
jobSchema.index({ "aiInsights.priorityScore": -1 }); // AI priority sorting
jobSchema.index({ "aiInsights.complexity": 1, status: 1 }); // Complexity filtering
jobSchema.index({ "riskAssessment.overallRiskLevel": 1 }); // Risk-based queries
jobSchema.index({
  "similarityVectors.jobType": 1,
  "similarityVectors.complexity": 1,
}); // Similarity matching

const Job = mongoose.model("Job", jobSchema);

module.exports = Job;
