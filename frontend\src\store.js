import { configureStore } from "@reduxjs/toolkit";
import authReducer from "./slices/authSlice";
import customerReducer from "./slices/customerSlice";
import jobReducer from "./slices/jobSlice";
import invoiceReducer from "./slices/invoiceSlice";
import inventoryReducer from "./slices/materialSlice";
import calendarReducer from "./slices/calendarSlice";
import dashboardReducer from "./slices/dashboardSlice";
import userReducer from "./slices/userSlice";
import technicianReducer from "./slices/technicianSlice";
import snackbarReducer from "./slices/snackbarSlice";
import quoteReducer from "./slices/quoteSlice";

const store = configureStore({
  reducer: {
    auth: authReducer,
    customers: customerReducer,
    jobs: jobReducer,
    invoices: invoiceReducer,
    materials: inventoryReducer,
    calendar: calendarReducer,
    quotes: quoteReducer, // Add quote reducer
    dashboard: dashboardReducer,
    users: userReducer,
    technicians: technicianReducer,
    snackbar: snackbarReducer,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: false,
    }),
});

export default store;
