/**
 * BaseCrawl4AIScraper.js
 * Base class for all Crawl4AI-powered material scrapers
 * This is a standalone class that uses Crawl4AI for web scraping
 * without any MCP dependencies.
 */

const logger = require("../../utils/logger");
const crawl4aiService = require("./crawl4ai-service");
const ApiError = require("../../utils/ApiError");
const priceCache = require("../../utils/priceCache");

/**
 * Base class for all Crawl4AI-powered material scrapers
 */
class BaseCrawl4AIScraper {
  /**
   * Constructor
   * @param {Object} source - Material source document
   */
  constructor(source) {
    // Basic scraper properties (from original BaseScraper)
    this.source = source;
    this.sourceId = source._id.toString();
    this.name = source.name;
    this.baseUrl = source.baseUrl;
    this.enabled = source.enabled;
    this.initialized = false;

    // 🚀 CRITICAL FIX: Enhanced configuration with proper content filtering
    this.crawl4aiConfig = {
      stealthMode: true,
      headless: true,
      timeout: source.scrapeConfig?.timeout || parseInt(process.env.CRAWL4AI_DEFAULT_TIMEOUT || "45000", 10), // Increase default to 45s to reduce timeouts on dynamic pages
      maxRetries: source.scrapeConfig?.maxRetries || 2, // Reduced retries to fail faster
      userAgent:
        source.scrapeConfig?.userAgent ||
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
      // Enhanced content filtering configuration
      contentFilter: {
        threshold: 0.3, // Lower threshold for better content retention
        thresholdType: "fixed",
        minWordThreshold: 10 // Minimum words per content block
      },
      // Performance optimizations
      word_count_threshold: 10,
      remove_overlay_elements: true,
      bypass_cache: false, // Enable caching for better performance
      verbose: false // Reduce logging noise
    };

    // Default extraction schema (to be overridden by child classes)
    this.extractionSchema = null;

    // Cache settings specific to Crawl4AI
    this.cacheKeyPrefix = `crawl4ai:${this.name.toLowerCase()}`;
  }

  /**
   * Initialize the scraper
   * @returns {Promise<void>}
   */
  async initialize() {
    if (this.initialized) {
      return;
    }

    logger.info(`Initializing ${this.name} scraper with Crawl4AI`);

    try {
      // Initialize Crawl4AI service
      await crawl4aiService.initialize();

      // Call provider-specific initialization
      await this._initializeProvider();

      this.initialized = true;
      logger.info(`Scraper for ${this.name} initialized successfully`);
    } catch (error) {
      logger.error(
        `Failed to initialize ${this.name} scraper: ${error.message}`,
        { stack: error.stack }
      );
      throw error;
    }
  }

  /**
   * Search for materials by description using Crawl4AI
   * @param {string} query - Search query
   * @param {Object} options - Search options
   * @returns {Promise<Array>} - Array of material objects
   */
  async searchByDescription(query, options = {}) {
    if (!this.initialized) {
      await this.initialize();
    }

    if (!this.enabled) {
      throw new Error(`Scraper for ${this.name} is disabled`);
    }

    // Check cache if not explicitly skipped
    if (!options.skipCache) {
      const sanitizedQuery = query.replace(/\s+/g, "_");
      const cacheKey = `${this.cacheKeyPrefix}:search:${sanitizedQuery}`;
      logger.debug(
        `[Cache Base] Using sanitized search cache key: ${cacheKey}`
      );
      const cachedResults = await priceCache.get(cacheKey);
      if (Array.isArray(cachedResults)) {
        logger.info(
          `[Cache Base] Found cached results for "${query}" from ${this.name}`
        );
        return cachedResults.map((result) => ({ ...result, cached: true }));
      }
    }

    try {
      logger.info(`[${this.name} Scraper] Searching for "${query}"`);

      // Delegate to provider-specific search implementation
      const results = await this._searchByDescription(query, options);

      // Cache results if available and cache not explicitly skipped
      if (results && results.length > 0 && !options.skipCache) {
        const sanitizedQuery = query.replace(/\s+/g, "_");
        const cacheKey = `${this.cacheKeyPrefix}:search:${sanitizedQuery}`;
        await priceCache.setJson(cacheKey, results, this.cacheTTL);
        logger.debug(
          `[Cache Base] Cached results for search "${query}" in ${this.name}`
        );
      }

      return results;
    } catch (error) {
      logger.error(
        `[${this.name} Scraper] Error searching for "${query}": ${error.message}`,
        { stack: error.stack }
      );

      // Determine if this is a CAPTCHA issue
      if (
        error.message.includes("CAPTCHA") ||
        error.message.includes("cloudflare") ||
        error.message.includes("bot detection")
      ) {
        throw new ApiError("CAPTCHA detected", 403, "CAPTCHA_DETECTED");
      }

      throw error;
    }
  }

  /**
   * Provider-specific search method (to be implemented by child classes)
   * @param {string} query - Search query
   * @param {Object} options - Search options
   * @returns {Promise<Array>} - Array of material objects
   * @protected
   */
  async _searchByDescription(query, options = {}) {
    throw new Error("_searchByDescription must be implemented by child class");
  }

  /**
   * Helper method to perform a crawl operation using Crawl4AI
   * @param {string} url - URL to crawl
   * @param {Object} options - Additional options to merge with default config
   * @returns {Promise<Object>} - Crawl result
   * @protected
   */
  async _performCrawl(url, options = {}) {
    const crawlOptions = {
      ...this.crawl4aiConfig,
      ...options,
      extractionSchema: options.extractionSchema || this.extractionSchema,
    };

    logger.debug(`[${this.name} Scraper] Performing crawl for URL: ${url}`);
    return crawl4aiService.crawl(url, crawlOptions);
  }

  /**
   * Helper method to transform extracted product data to standardized format
   * @param {Object} product - Raw product data from extraction
   * @returns {Object} - Standardized product object
   * @protected
   */
  _transformProduct(product) {
    // Log to AI generation file if AI process is active, otherwise normal debug log
    if (logger.isAiGenerationActive) {
      logger.aiGenerationDebug(
        `[BaseCrawl4AIScraper] Raw product data for transformation: ${JSON.stringify(
          product
        )}`
      );
    } else {
      logger.debug(
        `[BaseCrawl4AIScraper] Raw product data for transformation: ${JSON.stringify(
          product
        )}`
      );
    }

    // Try to parse price from multiple sources
    let parsedPrice =
      this._parsePrice(product.price) || this._parsePrice(product.priceRaw);
    let perUnitPrice = null;

    let packSize = null;
    let unit = "each"; // Default unit
    let description = product.description || "";

    // Extract pack size from explicit field or product title/details
    if (product.packSize) {
      packSize = product.packSize;
    } else if (product.title) {
      // Extract pack size from title e.g., "(25-Pack)", "(5-Pack)", etc.
      const packMatch = product.title.match(/\((\d+)[\s-]pack\)/i);
      if (packMatch && packMatch[1]) {
        packSize = packMatch[1];
        unit = "pack"; // Update unit to 'pack' if we detect a pack
      }
    }

    // Parse complex price formats like "$6.38(26¢/unit)"
    if (product.priceRaw) {
      // Try to identify unit from price string (e.g., '$2.43/ft', '$6.94/unit', '$1.39/ft')
      const unitMatch = product.priceRaw.match(
        /\([^\)]*?\$?[\d.,]+[¢$]?\/([a-z]+)\)/i
      );
      if (unitMatch && unitMatch[1]) {
        // Convert to standardized units
        const detectedUnit = unitMatch[1].toLowerCase();
        if (detectedUnit === "ft" || detectedUnit === "foot") {
          unit = "feet";
        } else if (detectedUnit !== "unit") {
          unit = detectedUnit; // Use whatever unit was detected (e.g., 'each', 'bag', etc.)
        }
      }

      const perUnitMatch = product.priceRaw.match(
        /\((\$?[\d.,]+)[¢$]?\/([a-zA-Z]+)\)/i
      ); // Support multi-char units like 'each'
      if (perUnitMatch && perUnitMatch[1]) {
        const rawPerUnitPriceString = perUnitMatch[1]; // e.g., "26" (from "26¢"), "$0.26", "1.39". This is cleaned for _parsePrice.
        let tempPerUnitPrice = this._parsePrice(rawPerUnitPriceString);

        if (tempPerUnitPrice !== null) {
          logger.debug(
            `[BaseCrawl4AIScraper] Processing perUnitPrice: rawPerUnitPriceString='${rawPerUnitPriceString}', initial tempPerUnitPrice: ${tempPerUnitPrice}, perUnitMatch[0]='${perUnitMatch[0]}'`
          );
          // Check for '¢' in the full matched per-unit string (e.g., "(26¢/unit)") as perUnitMatch[1] is just the number.
          if (perUnitMatch[0] && perUnitMatch[0].includes("¢")) {
            logger.debug(
              `[BaseCrawl4AIScraper] Cent conversion triggered for perUnitMatch[0]='${perUnitMatch[0]}'. tempPerUnitPrice BEFORE division: ${tempPerUnitPrice}`
            );
            tempPerUnitPrice = tempPerUnitPrice / 100;
            logger.debug(
              `[BaseCrawl4AIScraper] Cent conversion: tempPerUnitPrice AFTER division: ${tempPerUnitPrice}`
            );
          }
          // Ensure perUnitPrice is a number with up to 2 decimal places for currency.
          perUnitPrice = parseFloat(tempPerUnitPrice.toFixed(2));
          logger.debug(
            `[BaseCrawl4AIScraper] Cent conversion: final perUnitPrice set to: ${perUnitPrice}`
          );
        }

        // If no explicit pack size but we have per-unit price, try to infer pack size
        // Ensure perUnitPrice is positive to avoid division by zero or negative pack sizes.
        if (
          !packSize &&
          parsedPrice &&
          perUnitPrice > 0 &&
          perUnitMatch[2] &&
          perUnitMatch[2].toLowerCase() === "unit"
        ) {
          const estimatedPackSize = Math.round(parsedPrice / perUnitPrice);
          if (estimatedPackSize > 1 && isFinite(estimatedPackSize)) {
            packSize = String(estimatedPackSize);
            unit = "pack";
          }
        }
      }
    } else if (
      parsedPrice !== null &&
      typeof product.priceRaw === "string" &&
      product.priceRaw.includes("¢") &&
      !product.priceRaw.match(/\//)
    ) {
      // This handles cases where priceRaw is a simple cent string like "26¢" (not "26¢/unit")
      // and was not handled by the per-unit logic.
      // product.price at this point would be the numeric value from _parsePrice (e.g., 26 for "26¢").
      product.price = parseFloat((parsedPrice / 100).toFixed(2));
    }

    if (product.price !== undefined && parsedPrice === null) {
      logger.warn(
        `[BaseCrawl4AIScraper] Failed to parse price string: "${
          product.price
        }" for product: "${
          product.title || "Unknown Product"
        }". Resulted in null.`
      );
    }

    // Extract model number, SKU, or product ID
    const modelNumber =
      product.modelNumber ||
      (product.sku && product.sku.includes("Model") ? product.sku : null);
    const sku = product.sku || product.id || null;

    // If URL contains a Home Depot product ID, extract it as the SKU if not already set
    const skuFromUrl = product.url && product.url.match(/\/p\/[^/]+\/(\d+)/);
    const extractedSku = skuFromUrl ? skuFromUrl[1] : null;

    // Also try extracting SKU from other URL patterns
    const altSkuFromUrl = product.url && product.url.match(/\/(\d{9,})/);
    const altExtractedSku = altSkuFromUrl ? altSkuFromUrl[1] : null;

    // Clean up review count (remove non-numeric characters)
    let reviewCount = null;
    if (product.reviewCount) {
      const reviewMatch = product.reviewCount.match(/(\d+)/);
      reviewCount = reviewMatch ? reviewMatch[1] : null;
    }

    // Extract dimensions from title if available
    let dimensions = null;
    if (product.title) {
      const dimensionMatch = product.title.match(
        /(\d+(?:\.\d+)?\s*(?:in|inch|inches|ft|foot|feet|mm|cm|m)\s*x\s*\d+(?:\.\d+)?\s*(?:in|inch|inches|ft|foot|feet|mm|cm|m))/i
      );
      if (dimensionMatch) {
        dimensions = dimensionMatch[1];
      }
    }

    // Enhance description if empty but we have other details
    if (!description && product.title) {
      description = `${product.title}`;
      if (dimensions) {
        description += `. Dimensions: ${dimensions}`;
      }
      if (packSize) {
        description += `. ${packSize}-pack`;
      }
    }

    // Extract brand from title if not explicitly provided
    let brand = product.brand || "Unknown Brand";
    if (brand === "Unknown Brand" && product.title) {
      // Try to extract brand from title (first word is often the brand)
      const potentialBrand = product.title.split(" ")[0];
      if (
        potentialBrand &&
        potentialBrand.length > 2 &&
        !/^\d/.test(potentialBrand)
      ) {
        brand = potentialBrand;
      }
    }

    // Calculate product URL if it's a relative path
    let productUrl = product.url || null;
    if (productUrl && !productUrl.startsWith("http")) {
      productUrl = `https://www.homedepot.com${productUrl}`;
    }

    // Determine final price and total price for pack
    let finalPrice = parsedPrice;
    let totalPriceForPack = null;

    if (perUnitPrice && typeof perUnitPrice === "number" && perUnitPrice > 0) {
      finalPrice = perUnitPrice;
      totalPriceForPack = parsedPrice; // The original parsedPrice was the total for the pack
    } else if (
      parsedPrice &&
      packSize &&
      packSize > 1 &&
      unit &&
      unit !== "unit"
    ) {
      // If perUnitPrice wasn't explicitly found but we have a packSize and a non-generic unit,
      // it's likely parsedPrice is the total. We keep finalPrice as parsedPrice (total)
      // and don't set a separate totalPriceForPack unless perUnitPrice was specifically extracted.
      // This avoids misinterpreting a total price as a unit price if perUnitPrice extraction failed.
    }

    const transformedProduct = {
      // Original fields
      title: product.title || "Unknown Product",
      price: finalPrice, // Prioritize unit price if available
      totalPrice: totalPriceForPack, // Store total package price if unit price was used for 'price'
      perUnitPrice: perUnitPrice,
      sku: sku || extractedSku || altExtractedSku || null,
      modelNumber: modelNumber,
      brand: brand,
      imageUrl: product.imageUrl || product.image || null,
      url: productUrl,
      description: description,
      packSize: packSize,
      rating: product.rating || null,
      reviewCount: reviewCount,
      source: this.source.name,
      sourceId: this.sourceId,
      unit: unit,
      dimensions: dimensions,

      // Include frontend-expected field names
      name: product.title || "Unknown Product", // Frontend expects 'name', not 'title'
      productName: product.title || "Unknown Product", // Another common field name
      productUrl: productUrl, // Frontend may expect this format
      productDescription: description, // Frontend may expect this format
      rawData: product, // Store the original data for reference
    };

    // Log to AI generation file if AI process is active, otherwise normal debug log
    if (logger.isAiGenerationActive) {
      logger.aiGenerationDebug(
        `[BaseCrawl4AIScraper] Transformed product data for "${
          transformedProduct.title
        }": ${JSON.stringify(transformedProduct, null, 2)}`
      );
    } else {
      logger.debug(
        `[BaseCrawl4AIScraper] Transformed product data for "${
          transformedProduct.title
        }": ${JSON.stringify(transformedProduct, null, 2)}`
      );
    }

    return transformedProduct;
  }

  /**
   * Helper method to parse price from various formats
   * @param {string|number} priceString - Price in string or number format
   * @returns {number|null} - Parsed price as number or null if invalid
   * @protected
   */
  _parsePrice(priceString) {
    if (typeof priceString === "number") {
      return priceString;
    }

    if (!priceString) {
      return null;
    }

    // Handle currency symbols and formats like "$123.45" or "123,45 €"
    const match = String(priceString).match(
      /(?:[$€£¥])?([0-9]+(?:[.,][0-9]+)?)/
    );
    if (match) {
      // Replace comma with dot for decimal separator if needed
      const normalized = match[1].replace(",", ".");
      return parseFloat(normalized);
    }

    return null;
  }
}

module.exports = BaseCrawl4AIScraper;
