/**
 * materialOptionService.js
 * Service layer for material options-related API calls
 */
import axios from "axios";
import logger from "../utils/logger";

const API_BASE_URL = process.env.REACT_APP_API_BASE_URL || "/api";

/**
 * Get material options for a quote item
 * @param {string} quoteId - The ID of the quote
 * @param {string} itemId - The ID of the item
 * @param {function} getToken - Function to get the auth token
 * @returns {Promise<object>} - The material options data
 */
export const getMaterialOptions = async (quoteId, itemId, getToken) => {
  if (!quoteId || !itemId) {
    logger.error("getMaterialOptions: quoteId and itemId are required");
    throw new Error(
      "Quote ID and Item ID are required to get material options."
    );
  }

  try {
    const token = getToken();
    if (!token) {
      logger.error("getMaterialOptions: No auth token available");
      throw new Error("Authentication token not found.");
    }

    const config = {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    };

    const { data } = await axios.get(
      `${API_BASE_URL}/ai/quote/${quoteId}/item/${itemId}/material-options`,
      config
    );

    if (data && data.success) {
      return data;
    } else {
      logger.error(
        "getMaterialOptions: API call did not return success or data",
        data
      );
      throw new Error(
        data?.message ||
          "Failed to get material options due to unexpected API response."
      );
    }
  } catch (error) {
    const errorMessage = error.response?.data?.message || error.message;
    logger.error(
      `getMaterialOptions: Error getting material options for quote ${quoteId}, item ${itemId}:`,
      errorMessage,
      error.response?.data
    );
    throw new Error(errorMessage);
  }
};

/**
 * Initiate AI material scraping for a quote item
 * @param {string} quoteId - The ID of the quote
 * @param {string} itemId - The ID of the item
 * @param {object} options - Scraping options (searchQuery, toolId, limit, url)
 * @param {function} getToken - Function to get the auth token
 * @returns {Promise<object>} - The response data with requestId
 */
export const initiateAiMaterialScraping = async (
  quoteId,
  itemId,
  options,
  getToken
) => {
  if (!quoteId || !itemId) {
    logger.error("initiateAiMaterialScraping: quoteId and itemId are required");
    throw new Error(
      "Quote ID and Item ID are required to initiate material scraping."
    );
  }

  try {
    const token = getToken();
    if (!token) {
      logger.error("initiateAiMaterialScraping: No auth token available");
      throw new Error("Authentication token not found.");
    }

    const config = {
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
    };

    const { data } = await axios.post(
      `${API_BASE_URL}/ai/quote/${quoteId}/item/${itemId}/scrape-materials`,
      options,
      config
    );

    if (data && data.success) {
      return data;
    } else {
      logger.error(
        "initiateAiMaterialScraping: API call did not return success or data",
        data
      );
      throw new Error(
        data?.message ||
          "Failed to initiate material scraping due to unexpected API response."
      );
    }
  } catch (error) {
    const errorMessage = error.response?.data?.message || error.message;
    logger.error(
      `initiateAiMaterialScraping: Error initiating material scraping for quote ${quoteId}, item ${itemId}:`,
      errorMessage,
      error.response?.data
    );
    throw new Error(errorMessage);
  }
};

/**
 * Select a material option for a quote item
 * @param {string} quoteId - The ID of the quote
 * @param {string} itemId - The ID of the item
 * @param {number} optionIndex - The index of the selected option
 * @param {function} getToken - Function to get the auth token
 * @returns {Promise<object>} - The response data
 */
export const selectMaterialOption = async (
  quoteId,
  itemId,
  optionIndex,
  getToken
) => {
  if (!quoteId || !itemId || optionIndex === undefined) {
    logger.error(
      "selectMaterialOption: quoteId, itemId, and optionIndex are required"
    );
    throw new Error(
      "Quote ID, Item ID, and Option Index are required to select a material option."
    );
  }

  try {
    const token = getToken();
    if (!token) {
      logger.error("selectMaterialOption: No auth token available");
      throw new Error("Authentication token not found.");
    }

    const config = {
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
    };

    const { data } = await axios.post(
      `${API_BASE_URL}/ai/quote/${quoteId}/item/${itemId}/select-option`,
      { optionIndex },
      config
    );

    if (data && data.success) {
      return data;
    } else {
      logger.error(
        "selectMaterialOption: API call did not return success or data",
        data
      );
      throw new Error(
        data?.message ||
          "Failed to select material option due to unexpected API response."
      );
    }
  } catch (error) {
    const errorMessage = error.response?.data?.message || error.message;
    logger.error(
      `selectMaterialOption: Error selecting material option for quote ${quoteId}, item ${itemId}, option ${optionIndex}:`,
      errorMessage,
      error.response?.data
    );
    throw new Error(errorMessage);
  }
};
