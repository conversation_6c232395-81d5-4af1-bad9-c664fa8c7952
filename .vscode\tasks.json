{"version": "2.0.0", "tasks": [{"label": "Start Backend Dev Server", "type": "shell", "command": "pnpm", "args": ["dev"], "options": {"cwd": "${workspaceFolder}\\backend"}, "group": {"kind": "build", "isDefault": true}, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new", "showReuseMessage": true, "clear": false}, "problemMatcher": [], "runOptions": {"runOn": "folderOpen"}}, {"label": "Start Frontend Dev Server", "type": "shell", "command": "pnpm", "args": ["start"], "options": {"cwd": "${workspaceFolder}\\frontend"}, "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new", "showReuseMessage": true, "clear": false}, "problemMatcher": []}, {"label": "Start Database Only", "type": "shell", "command": "docker-compose", "args": ["up", "-d", "mongodb"], "options": {"cwd": "${workspaceFolder}"}, "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new", "showReuseMessage": true, "clear": false}, "problemMatcher": []}, {"label": "Stop All Services", "type": "shell", "command": "docker-compose", "args": ["down"], "options": {"cwd": "${workspaceFolder}"}, "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new", "showReuseMessage": true, "clear": false}, "problemMatcher": []}, {"label": "Run Tests", "type": "shell", "command": "pnpm", "args": ["test"], "options": {"cwd": "${workspaceFolder}\\backend"}, "group": {"kind": "test", "isDefault": true}, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new", "showReuseMessage": true, "clear": false}, "problemMatcher": []}]}