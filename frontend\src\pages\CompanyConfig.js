import React, { useState, useEffect, useRef } from "react";
import {
  Container,
  Typography,
  Box,
  Paper,
  Grid,
  TextField,
  Button,
  CircularProgress,
  Alert,
  Snackbar,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Divider,
  Chip,
} from "@mui/material";
import { Save as SaveIcon } from "@mui/icons-material";
import { useSelector } from "react-redux";
import axios from "axios";
import AddressAutocomplete from "../components/AddressAutocomplete";

const CompanyConfig = () => {
  const { userInfo } = useSelector((state) => state.auth);

  const [loading, setLoading] = useState(false);
  const [saveLoading, setSaveLoading] = useState(false);
  const [success, setSuccess] = useState(false);
  const [error, setError] = useState(null);
  const [companyData, setCompanyData] = useState({
    name: "",
    email: "",
    phone: "",
    address: {
      street: "",
      city: "",
      state: "",
      zipCode: "",
      country: "US",
    },
    website: "",
    logo: "",
    taxId: "",
    businessType: "Other",
    primaryServices: [],
  });
  const [addressSearchTerm, setAddressSearchTerm] = useState("");
  const [newService, setNewService] = useState("");
  const [formErrors, setFormErrors] = useState({});

  // Refs to store timeout IDs for cleanup
  const successTimeoutRef = useRef(null);
  const errorTimeoutRef = useRef(null);

  // Fetch company data when component mounts
  useEffect(() => {
    const controller = new AbortController();
    const signal = controller.signal;

    // Define function inside the effect to avoid the dependency array issue
    async function loadCompanyData() {
      setLoading(true);
      try {
        const config = {
          headers: {
            Authorization: `Bearer ${userInfo.token}`,
          },
          signal, // Pass the abort signal to Axios
        };

        const { data } = await axios.get("/api/company", config);

        // Check if the component is still mounted before setting state
        // Axios throws an error if aborted, so this check might be redundant
        // if error handling correctly identifies AbortError.
        // However, it's a good defensive measure.
        if (!signal.aborted) {
          if (data) {
            setCompanyData(data);

            // Set the combined address for the autocomplete component
            const fullAddress = [
              data.address?.street,
              data.address?.city,
              data.address?.state,
              data.address?.zipCode,
            ]
              .filter(Boolean)
              .join(", ");

            setAddressSearchTerm(fullAddress);
          }
        }
      } catch (err) {
        if (axios.isCancel(err)) {
          console.log("Company data fetch cancelled:", err.message);
        } else if (!signal.aborted) {
          const message =
            err.response?.data?.message || "Failed to load company data";
          setError(message);

          // If company doesn't exist yet, don't show error
          if (err.response?.status === 404) {
            setError(null);
          }
        }
      } finally {
        if (!signal.aborted) {
          setLoading(false);
        }
      }
    }

    loadCompanyData();

    // Cleanup function to abort the request if the component unmounts
    return () => {
      controller.abort();
    };
  }, [userInfo]);

  // Cleanup timeouts on component unmount to prevent memory leaks
  useEffect(() => {
    return () => {
      if (successTimeoutRef.current) {
        clearTimeout(successTimeoutRef.current);
      }
      if (errorTimeoutRef.current) {
        clearTimeout(errorTimeoutRef.current);
      }
    };
  }, []);

  const handleChange = (e) => {
    const { name, value } = e.target;

    // Handle nested address fields
    if (name.startsWith("address.")) {
      const addressField = name.split(".")[1];
      setCompanyData((prev) => ({
        ...prev,
        address: {
          ...prev.address,
          [addressField]: value,
        },
      }));
    } else {
      setCompanyData((prev) => ({
        ...prev,
        [name]: value,
      }));
    }
  };

  const handleAddressSelected = (addressDetails) => {
    setCompanyData((prev) => ({
      ...prev,
      address: {
        street: addressDetails.street || "",
        city: addressDetails.city || "",
        state: addressDetails.state || "",
        zipCode: addressDetails.zipCode || "",
        country: "US",
      },
    }));
  };

  const handleAddService = () => {
    if (!newService.trim()) return;

    setCompanyData((prev) => ({
      ...prev,
      primaryServices: [...(prev.primaryServices || []), newService.trim()],
    }));

    setNewService("");
  };

  const handleRemoveService = (service) => {
    setCompanyData((prev) => ({
      ...prev,
      primaryServices: prev.primaryServices.filter((s) => s !== service),
    }));
  };

  const validateForm = () => {
    const errors = {};

    if (!companyData.name) errors.name = "Company name is required";
    if (!companyData.email) errors.email = "Email is required";
    if (!companyData.phone) errors.phone = "Phone number is required";
    if (!companyData.address?.street)
      errors["address.street"] = "Street address is required";
    if (!companyData.address?.city) errors["address.city"] = "City is required";
    if (!companyData.address?.state)
      errors["address.state"] = "State is required";
    if (!companyData.address?.zipCode)
      errors["address.zipCode"] = "Zip code is required";

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setSaveLoading(true);

    try {
      const config = {
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${userInfo.token}`,
        },
      };

      // Check if we're creating or updating
      const endpoint = "/api/company";
      const method = companyData._id ? "put" : "post";

      let response;
      if (method === "put") {
        response = await axios.put(endpoint, companyData, config);
      } else {
        response = await axios.post(endpoint, companyData, config);
      }

      setSuccess(true);

      // If it was a create, update the company data with the response
      if (!companyData._id && response.data) {
        setCompanyData(response.data);
      }

      // Clear any form errors
      setFormErrors({});

      // Clear any existing success timeout
      if (successTimeoutRef.current) {
        clearTimeout(successTimeoutRef.current);
      }

      // Set new timeout with ref for cleanup
      successTimeoutRef.current = setTimeout(() => {
        setSuccess(false);
        successTimeoutRef.current = null;
      }, 5000);
    } catch (err) {
      const message =
        err.response?.data?.message || "Failed to save company data";
      setError(message);

      // Clear any existing error timeout
      if (errorTimeoutRef.current) {
        clearTimeout(errorTimeoutRef.current);
      }

      // Set new timeout with ref for cleanup
      errorTimeoutRef.current = setTimeout(() => {
        setError(null);
        errorTimeoutRef.current = null;
      }, 5000);
    } finally {
      setSaveLoading(false);
    }
  };

  const handleCloseSnackbar = () => {
    // Clear any pending timeouts
    if (successTimeoutRef.current) {
      clearTimeout(successTimeoutRef.current);
      successTimeoutRef.current = null;
    }
    if (errorTimeoutRef.current) {
      clearTimeout(errorTimeoutRef.current);
      errorTimeoutRef.current = null;
    }

    setSuccess(false);
    setError(null);
  };

  return (
    <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
      <Paper sx={{ p: 3, mb: 4 }}>
        <Typography variant="h4" gutterBottom>
          Company Configuration
        </Typography>
        <Typography variant="subtitle1" color="text.secondary" gutterBottom>
          Manage your company details and settings. These settings will be used
          throughout the system.
        </Typography>
        <Alert severity="info" sx={{ mt: 2, mb: 3 }}>
          As an administrator, you can configure the company information that
          will be visible to all users and used in documents like invoices.
        </Alert>
      </Paper>

      <Paper sx={{ p: 3 }}>
        {loading ? (
          <Box display="flex" justifyContent="center" my={3}>
            <CircularProgress />
          </Box>
        ) : (
          <form onSubmit={handleSubmit}>
            <Box sx={{ mb: 4 }}>
              <Typography variant="h6" gutterBottom>
                Basic Information
              </Typography>
              <Divider sx={{ mb: 3 }} />

              <Grid container spacing={3}>
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="Company Name"
                    name="name"
                    value={companyData.name}
                    onChange={handleChange}
                    error={!!formErrors.name}
                    helperText={formErrors.name}
                  />
                </Grid>

                <Grid item xs={12} md={6}>
                  <FormControl fullWidth>
                    <InputLabel id="business-type-label">
                      Business Type
                    </InputLabel>
                    <Select
                      labelId="business-type-label"
                      id="business-type"
                      name="businessType"
                      value={companyData.businessType || "Other"}
                      onChange={handleChange}
                      label="Business Type"
                    >
                      <MenuItem value="HVAC">HVAC</MenuItem>
                      <MenuItem value="Plumbing">Plumbing</MenuItem>
                      <MenuItem value="Electrical">Electrical</MenuItem>
                      <MenuItem value="Landscaping">Landscaping</MenuItem>
                      <MenuItem value="Cleaning">Cleaning Services</MenuItem>
                      <MenuItem value="General Contractor">
                        General Contractor
                      </MenuItem>
                      <MenuItem value="Other">Other</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>

                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="Email Address"
                    name="email"
                    value={companyData.email}
                    onChange={handleChange}
                    error={!!formErrors.email}
                    helperText={formErrors.email}
                  />
                </Grid>

                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="Phone Number"
                    name="phone"
                    value={companyData.phone}
                    onChange={handleChange}
                    error={!!formErrors.phone}
                    helperText={formErrors.phone}
                  />
                </Grid>

                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="Website"
                    name="website"
                    value={companyData.website || ""}
                    onChange={handleChange}
                  />
                </Grid>

                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="Tax ID / EIN"
                    name="taxId"
                    value={companyData.taxId || ""}
                    onChange={handleChange}
                  />
                </Grid>
              </Grid>
            </Box>

            <Box sx={{ mb: 4 }}>
              <Typography variant="h6" gutterBottom>
                Address Information
              </Typography>
              <Divider sx={{ mb: 3 }} />

              <Grid container spacing={3}>
                <Grid item xs={12}>
                  <AddressAutocomplete
                    value={addressSearchTerm}
                    onChange={(value) => setAddressSearchTerm(value)}
                    onAddressSelected={handleAddressSelected}
                    label="Street Address"
                  />
                </Grid>

                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="Street"
                    name="address.street"
                    value={companyData.address?.street || ""}
                    onChange={handleChange}
                    error={!!formErrors["address.street"]}
                    helperText={formErrors["address.street"]}
                  />
                </Grid>

                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="City"
                    name="address.city"
                    value={companyData.address?.city || ""}
                    onChange={handleChange}
                    error={!!formErrors["address.city"]}
                    helperText={formErrors["address.city"]}
                  />
                </Grid>

                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="State"
                    name="address.state"
                    value={companyData.address?.state || ""}
                    onChange={handleChange}
                    error={!!formErrors["address.state"]}
                    helperText={formErrors["address.state"]}
                  />
                </Grid>

                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="Zip Code"
                    name="address.zipCode"
                    value={companyData.address?.zipCode || ""}
                    onChange={handleChange}
                    error={!!formErrors["address.zipCode"]}
                    helperText={formErrors["address.zipCode"]}
                  />
                </Grid>
              </Grid>
            </Box>

            <Box sx={{ mb: 4 }}>
              <Typography variant="h6" gutterBottom>
                Services
              </Typography>
              <Divider sx={{ mb: 3 }} />

              <Grid container spacing={3}>
                <Grid item xs={12}>
                  <Box sx={{ display: "flex", alignItems: "center", mb: 2 }}>
                    <TextField
                      sx={{ flexGrow: 1, mr: 2 }}
                      label="Add Service"
                      value={newService}
                      onChange={(e) => setNewService(e.target.value)}
                    />
                    <Button
                      variant="contained"
                      onClick={handleAddService}
                      disabled={!newService.trim()}
                    >
                      Add
                    </Button>
                  </Box>

                  <Box sx={{ display: "flex", flexWrap: "wrap", gap: 1 }}>
                    {(companyData.primaryServices || []).map(
                      (service, index) => (
                        <Chip
                          key={index}
                          label={service}
                          onDelete={() => handleRemoveService(service)}
                          color="primary"
                          variant="outlined"
                        />
                      )
                    )}
                    {companyData.primaryServices?.length === 0 && (
                      <Typography variant="body2" color="text.secondary">
                        No services added yet
                      </Typography>
                    )}
                  </Box>
                </Grid>
              </Grid>
            </Box>

            <Box display="flex" justifyContent="flex-end">
              <Button
                type="submit"
                variant="contained"
                color="primary"
                startIcon={
                  saveLoading ? (
                    <CircularProgress size={20} color="inherit" />
                  ) : (
                    <SaveIcon />
                  )
                }
                disabled={saveLoading}
              >
                {saveLoading ? "Saving..." : "Save Company Settings"}
              </Button>
            </Box>
          </form>
        )}
      </Paper>

      <Snackbar
        open={success || error !== null}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: "bottom", horizontal: "right" }}
      >
        <Alert
          onClose={handleCloseSnackbar}
          severity={error ? "error" : "success"}
          sx={{ width: "100%" }}
        >
          {error ? error : "Company settings saved successfully!"}
        </Alert>
      </Snackbar>
    </Container>
  );
};

export default CompanyConfig;
