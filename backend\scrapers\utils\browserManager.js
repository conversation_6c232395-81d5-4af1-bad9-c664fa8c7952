// /**
//  * browserManager.js
//  * Utility for managing headless browser instances
//  *
//  * NOTE: This file's functionality is being replaced by the Puppeteer MCP server.
//  * The original code is commented out below for reference.
//  */

// // Import playwright-extra and stealth plugin
// const playwrightExtra = require('playwright-extra');
// // const stealthPlugin = require('playwright-extra-plugin-stealth'); // Incompatible with playwright-extra
// // We still need the original chromium for type hints or if playwright-extra doesn't export it directly
// const { chromium } = require('playwright');
// const logger = require('../../utils/logger');

// // Apply the stealth plugin - Removed as it's incompatible
// // playwrightExtra.chromium.use(stealthPlugin());

// /**
//  * Maximum number of concurrent browser instances
//  * @type {number}
//  */
// const MAX_CONCURRENT_BROWSERS = 5;

// /**
//  * Active browser instances
//  * @type {Map}
//  */
// const activeBrowsers = new Map();

// /**
//  * Launch a headless browser instance
//  * @param {Object} options - Browser options
//  * @returns {Promise<Object>} - Browser instance, context, and page
//  */
// async function launch(options = {}) {
//   const {
//     userAgent = null,
//     proxyUrl = null,
//     timeout = 30000,
//     width = 1280,
//     height = 800,
//     blockImages = true,
//     blockStylesheets = false,
//     blockFonts = true
//   } = options;

//   // Check if we have too many active browsers
//   if (activeBrowsers.size >= MAX_CONCURRENT_BROWSERS) {
//     logger.warn(`Maximum concurrent browsers (${MAX_CONCURRENT_BROWSERS}) reached, waiting for one to close...`);

//     // Wait for a browser to become available
//     await new Promise(resolve => {
//       const checkInterval = setInterval(() => {
//         if (activeBrowsers.size < MAX_CONCURRENT_BROWSERS) {
//           clearInterval(checkInterval);
//           resolve();
//         }
//       }, 1000);
//     });
//   }

//   // Launch browser using playwright-extra with stealth plugin applied
//   const browser = await playwrightExtra.chromium.launch({
//     headless: true,
//     timeout: timeout
//     // Note: Proxy is handled in context options below
//   });

//   // Create browser context with provided options
//   const contextOptions = {
//     viewport: { width, height },
//     userAgent: userAgent,
//     bypassCSP: true,
//     acceptDownloads: false,
//     javaScriptEnabled: true,
//     extraHTTPHeaders: {
//       'Accept-Language': 'en-US,en;q=0.9',
//       'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',
//       'Cache-Control': 'no-cache',
//       'Pragma': 'no-cache'
//     }
//   };
//   // Add proxy if provided
//   if (proxyUrl) {
//     contextOptions.proxy = { server: proxyUrl };
//   }

//   const context = await browser.newContext(contextOptions);

//   // Create page
//   const page = await context.newPage();

//   // Set default timeout
//   page.setDefaultTimeout(timeout);

//   // Setup request interception if needed
//   if (blockImages || blockStylesheets || blockFonts) {
//     await context.route('**/*', route => {
//       const resourceType = route.request().resourceType();

//       if (
//         (blockImages && resourceType === 'image') ||
//         (blockStylesheets && resourceType === 'stylesheet') ||
//         (blockFonts && resourceType === 'font')
//       ) {
//         return route.abort();
//       }

//       return route.continue();
//     });
//   }

//   // Store browser instance
//   const browserId = `browser_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`;
//   activeBrowsers.set(browserId, { browser, context, page });

//   logger.debug(`Browser launched [${browserId}], active browsers: ${activeBrowsers.size}`);

//   return {
//     browser,
//     context,
//     page,
//     id: browserId
//   };
// }

// /**
//  * Close a browser instance
//  * @param {Object} browser - Browser instance
//  * @returns {Promise<void>}
//  */
// async function close(browser) {
//   if (!browser) return;

//   try {
//     // Find browser by ID or object reference
//     let browserId = null;

//     if (browser.id) {
//       // If we have the browser ID
//       browserId = browser.id;
//     } else {
//       // Find by browser object reference
//       for (const [id, instance] of activeBrowsers.entries()) {
//         if (instance.browser === browser ||
//             (browser.browser && instance.browser === browser.browser)) {
//           browserId = id;
//           break;
//         }
//       }
//     }

//     if (browserId && activeBrowsers.has(browserId)) {
//       const instance = activeBrowsers.get(browserId);

//       // Close browser contexts and browser
//       await instance.context.close();
//       await instance.browser.close();

//       // Remove from active browsers
//       activeBrowsers.delete(browserId);

//       logger.debug(`Browser closed [${browserId}], active browsers: ${activeBrowsers.size}`);
//     } else {
//       // If not found in active browsers, just try to close it
//       if (browser.context) await browser.context.close();
//       if (browser.browser) await browser.browser.close();

//       logger.debug('Untracked browser closed');
//     }
//   } catch (error) {
//     logger.error('Error closing browser:', error);
//   }
// }

// /**
//  * Close all active browser instances
//  * @returns {Promise<void>}
//  */
// async function closeAll() {
//   logger.info(`Closing all browsers (${activeBrowsers.size} active)`);

//   const closePromises = [];
//   for (const [id, instance] of activeBrowsers.entries()) {
//     closePromises.push(
//       instance.browser.close()
//         .catch(error => logger.error(`Error closing browser ${id}:`, error))
//         .finally(() => activeBrowsers.delete(id))
//     );
//   }

//   await Promise.all(closePromises);
//   logger.info('All browsers closed');
// }

// /**
//  * Get the count of active browser instances
//  * @returns {number} - Active browser count
//  */
// function getActiveBrowserCount() {
//   return activeBrowsers.size;
// }

// // Handle process exit to close all browsers
// process.on('exit', () => {
//   for (const [id, instance] of activeBrowsers.entries()) {
//     try {
//       instance.browser.close();
//     } catch (error) {
//       // Ignore errors on process exit
//     }
//   }
// });

// // Also handle other termination signals
// ['SIGINT', 'SIGTERM', 'SIGHUP'].forEach(signal => {
//   process.on(signal, async () => {
//     await closeAll();
//     process.exit(0);
//   });
// });

// // Export empty functions for now to avoid breaking imports
// module.exports = {
//   launch: async () => { throw new Error('browserManager.launch is deprecated. Use MCP server.'); },
//   close: async () => { /* No-op */ },
//   closeAll: async () => { /* No-op */ },
//   getActiveBrowserCount: () => 0
// };
