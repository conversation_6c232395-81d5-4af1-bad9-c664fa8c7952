// Load environment variables FIRST - before any other operations
require("dotenv").config();

const express = require("express");
const cors = require("cors");
const morgan = require("morgan");
const path = require("path");
const connectDB = require("./config/database");
const logger = require("./utils/logger");

// Log the configured level immediately
console.log(
  `[Server Start] LOG_LEVEL environment variable: ${process.env.LOG_LEVEL}`
);
console.log(`[Server Start] Logger configured level: ${logger.level}`);
const scraperService = require("./scrapers/ScraperService"); // Import ScraperService
const changeStreamService = require("./services/changeStreamService"); // Import ChangeStreamService
const priceLookupPoller = require("./services/priceLookupPoller"); // Import PriceLookupPoller as fallback

// Create Express app
const app = express();

// Import middleware (needed for setup inside IIFE)
const requestId = require("./middleware/requestId");
const {
  standardLimiter,
  authLimiter,
  aiLimiter,
} = require("./middleware/rateLimiter");
const errorHandler = require("./middleware/errorHandler");
const mapRoutes = require("./api/mapRoutes"); // Moved import here

// Set port
const PORT = process.env.PORT || 5000;

// Function to clean AI generation log file
const cleanAIGenerationLog = () => {
  const fs = require("fs");
  const path = require("path");

  try {
    const logFilePath = path.join(__dirname, "..", "ai-generation.log");
    // Clear the file by writing empty string (creates file if doesn't exist)
    fs.writeFileSync(logFilePath, "", "utf8");
    logger.info("AI generation log file cleaned successfully");
  } catch (error) {
    logger.warn("Failed to clean AI generation log file:", error.message);
  }
};

// Use an async IIFE for top-level await functionality
(async () => {
  logger.info("Starting async server setup...");

  // Clean AI generation log file at startup
  cleanAIGenerationLog();

  let serverInstance;
  try {
    // 1. Connect to Database
    logger.info("Connecting to Database...");
    const conn = await connectDB();
    logger.info("Database connection established.");

    // 2. Initialize Change Streams (must be after DB connection)
    logger.info("Initializing Change Stream Service...");
    try {
      await changeStreamService.initializeChangeStreams(conn); // Pass the main Mongoose instance // conn is returned by connectDB()
      logger.info("Change Stream Service initialized successfully.");

      // Add a brief delay and then check if we should start the poller as fallback
      setTimeout(() => {
        if (!priceLookupPoller.isRunning) {
          logger.info(
            "Starting Price Lookup Poller as additional fallback mechanism..."
          );
          priceLookupPoller.start();
        }
      }, 5000); // Wait 5 seconds after Change Stream setup
    } catch (changeStreamError) {
      logger.error(
        "Failed to initialize Change Stream Service:",
        changeStreamError
      );
      logger.warn("Starting Price Lookup Poller as fallback...");
      // Start the poller as a fallback if change streams fail
      priceLookupPoller.start();
    }

    // 3. Initialize Scraper Service
    logger.info("Initializing Scraper Service...");
    await scraperService.initialize();
    logger.info("Scraper Service initialized successfully."); // More specific success log

    // 4. Run basic material source validation only (skip comprehensive Crawl4AI diagnostics)
    logger.info("Running basic material source validation...");
    try {
      // Check material sources first
      const materialSourceValidation =
        await scraperService.validateMaterialSources();
      // Log material source validation results in a structured way to prevent JSON serialization issues
      logger.info(
        `Material Source Validation completed: ${materialSourceValidation.totalSources} sources, ${materialSourceValidation.validSources.length} valid`
      );

      if (materialSourceValidation.issues.length > 0) {
        logger.warn("⚠️  Material Source Issues Found:");
        materialSourceValidation.issues.forEach((issue) => {
          logger.warn(`   - ${issue}`);
        });
      }

      // Skip comprehensive Crawl4AI health check to avoid hanging
      logger.info("✅ Skipping comprehensive Crawl4AI health check for faster startup");
      
    } catch (diagError) {
      logger.error("Failed to run material source validation:", diagError);
    }

    // --- Configure Express App AFTER initializations ---
    logger.info("Configuring Express middleware and routes...");
    app.use(express.json());
    app.use(express.urlencoded({ extended: true }));

    // Configure CORS with SSE support
    const corsOptions = {
      origin: function (origin, callback) {
        // Allow requests with no origin (like mobile apps or curl requests)
        if (!origin) return callback(null, true);

        // In development, allow localhost origins
        if (process.env.NODE_ENV === "development") {
          const allowedOrigins = [
            "http://localhost:3000",
            "http://localhost:3001",
            "http://127.0.0.1:3000",
            "http://127.0.0.1:3001",
          ];
          if (allowedOrigins.includes(origin)) {
            return callback(null, true);
          }
        }

        // In production, you might want to restrict to specific domains
        callback(null, true);
      },
      credentials: true,
      methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
      allowedHeaders: ["Content-Type", "Authorization", "Cache-Control"],
      exposedHeaders: ["Content-Type"],
    };

    app.use(cors(corsOptions));
    app.use(requestId);
    if (process.env.NODE_ENV === "development") {
      app.use(morgan("combined")); // More verbose HTTP request logging
    }
    // Import moved to top level
    app.use("/api/users", authLimiter, require("./api/userRoutes"));
    app.use("/api/customers", standardLimiter, require("./api/customerRoutes"));
    app.use("/api/jobs", standardLimiter, require("./api/jobRoutes"));
    app.use("/api/invoices", standardLimiter, require("./api/invoiceRoutes"));
    app.use("/api/materials", standardLimiter, require("./api/materialRoutes"));
    app.use(
      "/api/material-pricing",
      standardLimiter,
      require("./api/materialPricingRoutes")
    );
    app.use("/api/calendar", standardLimiter, require("./api/calendarRoutes"));
    app.use(
      "/api/dashboard",
      standardLimiter,
      require("./api/dashboardRoutes")
    );
    app.use("/api/ai", aiLimiter, require("./api/aiRoutes"));
    app.use(
      "/api/technicians",
      standardLimiter,
      require("./api/technicianRoutes")
    );
    app.use("/api/company", standardLimiter, require("./api/companyRoutes"));
    app.use("/api/quotes", standardLimiter, require("./api/quoteRoutes")); // Renamed route path and file
    app.use("/api/maps", standardLimiter, mapRoutes);
    app.use(
      "/api/material-status",
      standardLimiter,
      require("./api/materialStatusRoutes")
    ); // Material status monitoring
    app.use(
      "/api/streaming",
      standardLimiter,
      require("./api/streamingRoutes")
    ); // Streaming AI service for real-time updates
    // Serve uploaded files statically
    logger.info(
      `Configuring static file serving for /uploads path: ${path.join(
        __dirname,
        "uploads"
      )}`
    );
    app.use("/uploads", express.static(path.join(__dirname, "uploads")));

    if (process.env.NODE_ENV === "production") {
      app.use(express.static(path.join(__dirname, "../frontend/build")));
      app.get("*", (req, res) => {
        res.sendFile(
          path.resolve(__dirname, "../frontend/build", "index.html")
        );
      });
    }
    app.use(errorHandler);
    logger.info("Express middleware and routes configured.");
    // --- End Express App Configuration ---

    // 5. Start the Express server AFTER configuration
    serverInstance = app.listen(PORT, () => {
      logger.info(
        `Server running in ${process.env.NODE_ENV} mode on port ${PORT}`
      );
    });

    // Set up unhandledRejection listener *after* serverInstance is potentially assigned
    process.on("unhandledRejection", (err) => {
      logger.error(`Unhandled Rejection: ${err.message}\n${err.stack}`);
      logger.error("Shutting down server due to unhandled promise rejection");
      if (serverInstance) {
        // Stop poller if running
        priceLookupPoller.stop();
        changeStreamService.closeChangeStreams().finally(() => {
          serverInstance.close(() => process.exit(1));
        });
      } else {
        process.exit(1);
      }
    });
  } catch (error) {
    // Catch errors during the async startup sequence
    logger.error("!!! CRITICAL STARTUP ERROR !!!"); // Make error more prominent
    logger.error(`Failed during server startup sequence: ${error.message}`);
    logger.error("Stack Trace:", error.stack); // Log the full stack trace
    // Add a small delay to ensure logs are flushed before exiting
    await new Promise((resolve) => setTimeout(resolve, 100));
    process.exit(1); // Exit if essential initialization fails
  }
  logger.info("Async server setup finished successfully."); // Log successful completion

  // Graceful shutdown handler
  process.on("SIGTERM", async () => {
    logger.info("SIGTERM signal received: closing HTTP server");
    priceLookupPoller.stop();
    await changeStreamService.closeChangeStreams();
    if (serverInstance) {
      serverInstance.close(() => {
        logger.info("HTTP server closed");
        process.exit(0);
      });
    }
  });

  process.on("SIGINT", async () => {
    logger.info("SIGINT signal received: closing HTTP server");
    priceLookupPoller.stop();
    await changeStreamService.closeChangeStreams();
    if (serverInstance) {
      serverInstance.close(() => {
        logger.info("HTTP server closed");
        process.exit(0);
      });
    }
  });
})(); // Immediately invoke the async function

module.exports = app; // Export app for testing

// Export server instance for cleanup in tests (commented out as it's tricky with async)
// module.exports.server = serverInstance;
