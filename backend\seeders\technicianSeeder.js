const mongoose = require("mongoose");
const dotenv = require("dotenv");
const Technician = require("../models/Technician");
const connectDB = require("../config/database");

// Load environment variables
dotenv.config();

// Sample technician data
const technicianData = [
  {
    firstName: "<PERSON>",
    lastName: "<PERSON>",
    email: "<EMAIL>",
    phone: "************",
    contractorType: "HVAC",
    hourlyRate: 75,
    availabilityStatus: "available",
    skills: [
      "Air conditioning",
      "Heating",
      "Ventilation",
      "System installation",
    ],
    services: ["Installation", "Repair", "Maintenance", "Inspection"],
  },
  {
    firstName: "<PERSON>",
    lastName: "<PERSON>",
    email: "<EMAIL>",
    phone: "************",
    contractorType: "Plumbing",
    hourlyRate: 65,
    availabilityStatus: "available",
    skills: [
      "Pipe fitting",
      "Drainage",
      "Fixture installation",
      "Water heaters",
    ],
    services: ["Installation", "Repair", "Emergency Service"],
  },
  {
    firstName: "<PERSON>",
    lastName: "<PERSON>",
    email: "micha<PERSON>.rodrig<PERSON><PERSON>@example.com",
    phone: "************",
    contractorType: "Electrical",
    hourlyRate: 80,
    availabilityStatus: "busy",
    skills: ["Wiring", "Panel upgrades", "Lighting systems", "Troubleshooting"],
    services: ["Installation", "Repair", "Consultation"],
  },
  {
    firstName: "Jessica",
    lastName: "Chen",
    email: "<EMAIL>",
    phone: "************",
    contractorType: "General Contractor",
    hourlyRate: 70,
    availabilityStatus: "available",
    skills: ["Carpentry", "Drywall", "Flooring", "Project management"],
    services: ["Renovation", "Repair", "Consultation"],
  },
];

// Connect to database
connectDB();

// Seed function
const seedTechnicians = async () => {
  try {
    // Clear existing technicians
    await Technician.deleteMany({});
    console.log("Deleted existing technicians");

    // Insert new technicians
    const createdTechnicians = await Technician.insertMany(technicianData);
    console.log(`Created ${createdTechnicians.length} technicians`);

    // Exit with success
    process.exit(0);
  } catch (error) {
    console.error(`Error: ${error.message}`);
    process.exit(1);
  }
};

// Run the seeder
seedTechnicians();
