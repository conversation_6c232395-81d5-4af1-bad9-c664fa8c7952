/**
 * PlattCrawl4AIScraper.js
 * Crawl4AI-based scraper for Platt Electric Supply
 * Uses advanced anti-bot detection bypassing and AI vision for robust scraping
 */

const BaseCrawl4AIScraper = require("./BaseCrawl4AIScraper");
const logger = require("../../utils/logger");
const geminiService = require("../../utils/geminiService");
const crawl4aiService = require("./crawl4ai-service");
const fs = require("fs").promises;
const path = require("path");
const ApiError = require("../../utils/ApiError");

/**
 * URL detection regex for Platt products
 */
const PLATT_PRODUCT_URL_REGEX =
  /^https?:\/\/(?:www\.)?platt\.com\/p\/([^\/]+)\/([^\/]+)\/([^\/]+)\/([^\/]+)/i;

/**
 * Crawl4AI scraper for Platt Electric Supply website
 */
class PlattCrawl4AIScraper extends BaseCrawl4AIScraper {
  /**
   * Constructor
   * @param {Object} source - Material source document
   */
  constructor(source) {
    super(source);

    // Platt-specific configuration with enhanced Cloudflare bypass
    this.config = {
      ...this.config,
      // Enhanced anti-bot bypassing for Platt's Cloudflare protection
      bypassBlocks: true,
      waitForImages: true,
      simulateUser: true,
      screenshotWaitFor: 5, // Increased wait time
      stealthMode: true,
      // Enhanced headers to appear more legitimate
      headers: {
        Accept:
          "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
        "Accept-Language": "en-US,en;q=0.9",
        "Accept-Encoding": "gzip, deflate, br",
        "Cache-Control": "no-cache",
        Pragma: "no-cache",
        "Upgrade-Insecure-Requests": "1",
        "Sec-Fetch-Dest": "document",
        "Sec-Fetch-Mode": "navigate",
        "Sec-Fetch-Site": "none",
        "Sec-Fetch-User": "?1",
        "sec-ch-ua": '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"',
        "sec-ch-ua-mobile": "?0",
        "sec-ch-ua-platform": '"Windows"',
      },
      // Enhanced wait conditions for dynamic content
      waitFor: [
        "css:.product-title, .product-name, .item-name",
        "css:.product-price, .item-price, .price-value",
        { type: "timeout", value: 3000 },
      ],
      // JavaScript execution for better content extraction
      js_code: [
        // Remove popups and overlays that might interfere
        "document.querySelectorAll('.modal, .overlay, .popup').forEach(el => el.remove());",
        // Scroll to load lazy-loaded content
        "window.scrollTo(0, document.body.scrollHeight / 2);",
        // Wait for dynamic content
        "await new Promise(resolve => setTimeout(resolve, 1000));",
      ].join("\n"),
    };

    // Enhanced selectors for Platt
    this.selectors = {
      // Search page selectors
      searchResults:
        ".product-list .product-item, .search-results .result-item, .items-grid .item",
      searchResultTitle:
        ".product-name, .item-title, .result-title, h3 a, h4 a",
      searchResultPrice: ".product-price, .item-price, .result-price, .price",
      searchResultImage:
        ".product-image img, .item-image img, .result-image img",
      searchResultSku: ".product-sku, .item-number, .sku, .catalog-number",
      searchResultLink:
        "a.product-link, a.item-link, .product-name a, .item-title a",
      searchResultAvailability:
        ".availability, .stock-status, .in-stock, .out-of-stock",

      // Product page selectors
      productTitle:
        'h1.product-title, h1.product-name, h1.item-name, h1[itemprop="name"]',
      productPrice:
        '.product-price .price, .price-now, .selling-price, [itemprop="price"]',
      productSku: ".product-sku, .item-number, .catalog-number, .sku-value",
      productImage: ".product-image img, .main-image img, .gallery-main img",
      productAvailability:
        ".availability-status, .stock-info, .inventory-message",
      productDescription:
        ".product-description, .product-details, .description-content",
      productBrand:
        '.product-brand, .manufacturer, .brand-name, [itemprop="brand"]',
      productSpecifications: ".specifications, .product-specs, .tech-specs",

      // Override with source config if provided
      ...(source.scrapeConfig?.selectors || {}),
    };

    // Track anti-bot challenges with circuit breaker pattern
    this.scrapingChallenges = {
      cloudflareDetected: false,
      captchaDetected: false,
      loginRequired: false,
      rateLimited: false,
      blockedCount: 0,
      lastBlockTime: null,
      consecutiveFailures: 0,
      circuitBreakerOpen: false,
      circuitBreakerOpenTime: null,
      // Make breaker behavior configurable via env
      circuitBreakerFailureThreshold: parseInt(process.env.PLATT_CIRCUIT_BREAKER_FAILURE_THRESHOLD || "5", 10),
      circuitBreakerTimeout: parseInt(process.env.PLATT_CIRCUIT_BREAKER_TIMEOUT_MS || String(3 * 60 * 1000), 10), // default 3 minutes
    };
  }

  /**
   * Initialize the scraper
   * @returns {Promise<void>}
   */
  async _initializeProvider() {
    logger.info(
      "[Platt Crawl4AI] Initializing Platt scraper with enhanced anti-bot measures"
    );

    // Update base URL if needed
    if (!this.baseUrl || !this.baseUrl.includes("platt.com")) {
      this.baseUrl = "https://www.platt.com";
    }

    // Test connectivity with timeout protection
    try {
      // Use Promise.race to timeout the test connection
      const testPromise = crawl4aiService.crawl(this.baseUrl, {
        ...this.config,
        returnFormat: "markdown",
        waitFor: [{ type: "timeout", value: 5000 }], // 5 second timeout
      });

      const timeoutPromise = new Promise((_, reject) =>
        setTimeout(() => reject(new Error("Platt connection test timeout")), 20000)
      );

      const testResponse = await Promise.race([testPromise, timeoutPromise]);

      if (testResponse.success) {
        logger.info("[Platt Crawl4AI] Successfully connected to Platt website");
        this._analyzeAntiBot(testResponse);
      } else {
        logger.warn(
          "[Platt Crawl4AI] Initial connection test returned unsuccessful response"
        );
      }
    } catch (error) {
      if (error.message === "Platt connection test timeout") {
        logger.warn("[Platt Crawl4AI] Connection test timed out - proceeding with degraded mode");
      } else {
        logger.error("[Platt Crawl4AI] Error during initialization test:", error);
      }
    }
  }

  /**
   * Analyze response for anti-bot measures
   * @param {Object} response - Crawl4AI response
   * @private
   */
  _analyzeAntiBot(response) {
    const content = response.markdown || response.html || "";
    const url = response.url || "";

    // Enhanced Cloudflare detection
    if (
      content.includes("Checking if the site connection is secure") ||
      content.includes("Just a moment...") ||
      content.includes("Ray ID:") ||
      content.includes("cf-browser-verification") ||
      content.includes("__cf_chl_opt") ||
      content.includes("challenge-platform") ||
      content.includes("Enable JavaScript and cookies to continue") ||
      url.includes("__cf_chl_tk=") ||
      response.status === 403
    ) {
      this.scrapingChallenges.cloudflareDetected = true;
      logger.warn("[Platt Crawl4AI] Cloudflare protection detected", {
        url: url,
        hasJavaScriptChallenge: content.includes("__cf_chl_opt"),
        status: response.status
      });
    }

    // Enhanced CAPTCHA detection
    if (
      content.includes("captcha") ||
      content.includes("CAPTCHA") ||
      content.includes("challenge-form") ||
      content.includes("hCaptcha") ||
      content.includes("recaptcha") ||
      content.includes("I'm not a robot")
    ) {
      this.scrapingChallenges.captchaDetected = true;
      logger.warn("[Platt Crawl4AI] CAPTCHA challenge detected");
    }

    // Check for login requirement
    if (
      content.includes("login") ||
      content.includes("sign in") ||
      content.includes("member pricing") ||
      content.includes("Please log in") ||
      content.includes("authentication required")
    ) {
      this.scrapingChallenges.loginRequired = true;
      logger.info("[Platt Crawl4AI] Login may be required for full access");
    }

    // Check for rate limiting
    if (
      content.includes("Too many requests") ||
      content.includes("Rate limit exceeded") ||
      response.status === 429
    ) {
      this.scrapingChallenges.rateLimited = true;
      logger.warn("[Platt Crawl4AI] Rate limiting detected");
    }
  }

  /**
   * Check if circuit breaker should allow requests
   * @returns {boolean} - True if requests are allowed
   * @private
   */
  _isCircuitBreakerOpen() {
    if (!this.scrapingChallenges.circuitBreakerOpen) {
      return false;
    }

    // Check if timeout has passed
    const now = Date.now();
    const timeSinceOpen = now - this.scrapingChallenges.circuitBreakerOpenTime;

    if (timeSinceOpen >= this.scrapingChallenges.circuitBreakerTimeout) {
      // Reset circuit breaker to half-open state
      this.scrapingChallenges.circuitBreakerOpen = false;
      this.scrapingChallenges.consecutiveFailures = 0;
      logger.info("[Platt Crawl4AI] Circuit breaker reset to half-open state");
      return false;
    }

    return true;
  }

  /**
   * Record a successful operation
   * @private
   */
  _recordSuccess() {
    this.scrapingChallenges.consecutiveFailures = 0;
    this.scrapingChallenges.circuitBreakerOpen = false;
  }

  /**
   * Record a failed operation and potentially open circuit breaker
   * @private
   */
  _recordFailure() {
    this.scrapingChallenges.consecutiveFailures++;

    // Open circuit breaker after configurable number of consecutive failures
    if (this.scrapingChallenges.consecutiveFailures >= this.scrapingChallenges.circuitBreakerFailureThreshold) {
      this.scrapingChallenges.circuitBreakerOpen = true;
      this.scrapingChallenges.circuitBreakerOpenTime = Date.now();
      logger.warn("[Platt Crawl4AI] Circuit breaker opened due to consecutive failures");
    }
  }

  /**
   * Search for materials by description
   * @param {string} query - Search query
   * @param {Object} options - Search options
   * @returns {Promise<Array>} - Array of material objects
   * @protected
   */
  async _searchByDescription(query, options = {}) {
    // Check circuit breaker
    if (this._isCircuitBreakerOpen()) {
      const error = new ApiError(503, "Platt scraper circuit breaker is open - too many recent failures");
      logger.warn("[Platt Crawl4AI] Circuit breaker is open, rejecting request");
      throw error;
    }

    const searchUrl = `${this.baseUrl}/search?q=${encodeURIComponent(query)}`;
    logger.info(`[Platt Crawl4AI] Searching for: "${query}"`);

    try {
      // First attempt with enhanced Crawl4AI configuration
      const response = await crawl4aiService.crawl(searchUrl, {
        ...this.config,
        returnFormat: "all",
        extractSchemas: true,
        screenshot: true, // Capture screenshot for AI analysis
        screenshotWaitFor: 5, // Increased wait time
        contentFilter: {
          threshold: 0.3,
          thresholdType: "fixed",
          minWordThreshold: 10
        },
        word_count_threshold: 10,
        remove_overlay_elements: true,
        verbose: false, // Reduce logging noise
        // Use timeout from options if provided, otherwise use default
        timeout: options.timeout || parseInt(process.env.CRAWL4AI_DEFAULT_TIMEOUT || "45000", 10),
        // Pass through any other options from the caller
        ...options
      });

      if (!response.success) {
        this._recordFailure();
        throw new ApiError(500, "Failed to fetch search results from Platt");
      }

      this._analyzeAntiBot(response);

      // If blocked, try AI vision approach
      if (
        this.scrapingChallenges.cloudflareDetected ||
        this.scrapingChallenges.captchaDetected
      ) {
        logger.info(
          "[Platt Crawl4AI] Attempting AI vision fallback due to blocking"
        );
        const aiResults = await this._searchWithAIVision(query, response);
        if (aiResults && aiResults.length > 0) {
          this._recordSuccess();
          return aiResults;
        } else {
          this._recordFailure();
          throw new ApiError(503, "Platt scraper blocked by anti-bot measures");
        }
      }

      // Extract products from response
      const products = await this._extractSearchResults(response, query);

      if (products.length === 0) {
        logger.warn(
          "[Platt Crawl4AI] No products found with standard extraction, trying AI vision"
        );
        const aiResults = await this._searchWithAIVision(query, response);
        if (aiResults && aiResults.length > 0) {
          this._recordSuccess();
          return aiResults;
        } else {
          this._recordFailure();
          return []; // Return empty array instead of throwing
        }
      }

      this._recordSuccess();
      return products;
    } catch (error) {
      logger.error(`[Platt Crawl4AI] Search error for "${query}":`, error);
      this.scrapingChallenges.blockedCount++;
      this.scrapingChallenges.lastBlockTime = new Date();
      this._recordFailure();
      throw error;
    }
  }

  /**
   * Extract search results from Crawl4AI response
   * @param {Object} response - Crawl4AI response
   * @param {string} query - Original search query
   * @returns {Promise<Array>} - Array of products
   * @private
   */
  async _extractSearchResults(response, query) {
    const products = [];

    // Try structured data extraction first
    if (response.extractedData?.products) {
      for (const product of response.extractedData.products) {
        products.push({
          name: product.name || product.title,
          price: this._parsePrice(product.price),
          sku: product.sku ? `PLATT-${product.sku}` : null,
          url: product.url,
          imageUrl: product.image,
          source: "PLATT",
          availability: product.availability || "Unknown",
          brand: product.brand,
          method: "crawl4ai_structured",
        });
      }
    }

    // If no structured data, try CSS extraction
    if (products.length === 0 && response.cssExtractionRules) {
      const cssProducts = this._extractFromCSS(response.cssExtractionRules);
      products.push(...cssProducts);
    }

    // If still no products, try regex extraction on markdown
    if (products.length === 0 && response.markdown) {
      const regexProducts = this._extractFromMarkdown(response.markdown);
      products.push(...regexProducts);
    }

    // Filter and validate products
    return products.filter((p) => p.name && (p.price || p.sku));
  }

  /**
   * Use AI vision to extract product information from screenshot
   * @param {string} query - Search query
   * @param {Object} response - Crawl4AI response with screenshot
   * @returns {Promise<Array>} - Array of products
   * @private
   */
  async _searchWithAIVision(query, response) {
    if (!response.screenshot) {
      logger.warn(
        "[Platt Crawl4AI] No screenshot available for AI vision analysis"
      );
      return [];
    }

    try {
      // Save screenshot temporarily
      const tempDir = path.join(__dirname, "../../uploads/ai-context/temp");
      await fs.mkdir(tempDir, { recursive: true });
      const screenshotPath = path.join(
        tempDir,
        `platt_search_${Date.now()}.png`
      );

      // Decode base64 screenshot
      const screenshotBuffer = Buffer.from(response.screenshot, "base64");
      await fs.writeFile(screenshotPath, screenshotBuffer);

      // Use Gemini Vision to analyze the screenshot
      const prompt = `Analyze this screenshot from Platt Electric Supply search results for "${query}".
Extract all visible products with the following information:
- Product name/title
- Price (numeric value only)
- SKU or item number
- Availability status
- Brand/manufacturer if visible

Return the data as a JSON array with objects containing: name, price, sku, availability, brand.
If you see a Cloudflare or CAPTCHA challenge instead of products, return an empty array.`;

      const aiResult = await geminiService.analyzeImageForProducts(
        screenshotPath,
        prompt
      );

      // Clean up temp file
      await fs.unlink(screenshotPath).catch(() => {});

      if (!aiResult.success || !aiResult.products) {
        logger.warn(
          "[Platt Crawl4AI] AI vision analysis failed or returned no products"
        );
        return [];
      }

      // Format AI-extracted products
      return aiResult.products.map((p) => ({
        name: p.name,
        price: this._parsePrice(p.price),
        sku: p.sku ? `PLATT-${p.sku}` : null,
        url: null, // Can't extract URLs from image
        imageUrl: null,
        source: "PLATT",
        availability: p.availability || "Unknown",
        brand: p.brand,
        method: "ai_vision_extraction",
      }));
    } catch (error) {
      logger.error("[Platt Crawl4AI] AI vision extraction error:", error);
      return [];
    }
  }

  /**
   * Public method to get material by URL
   * @param {string} url - Product URL
   * @param {Object} options - Options
   * @returns {Promise<Object>} - Material object
   */
  async getByUrl(url, options = {}) {
    return this._getByUrl(url, options);
  }

  /**
   * Get material by URL (private implementation)
   * @param {string} url - Product URL
   * @param {Object} options - Options
   * @returns {Promise<Object>} - Material object
   * @protected
   */
  async _getByUrl(url, options = {}) {
    logger.info(`[Platt Crawl4AI] Fetching product from URL: ${url}`);

    try {
      const response = await crawl4aiService.crawl(url, {
        ...this.config,
        returnFormat: "all",
        extractSchemas: true,
        screenshot: true,
      });

      if (!response.success) {
        throw new ApiError(500, "Failed to fetch product page from Platt");
      }

      this._analyzeAntiBot(response);

      // Extract product details
      let product = await this._extractProductDetails(response, url);

      // If extraction failed, try AI vision
      if (!product || !product.price) {
        logger.info(
          "[Platt Crawl4AI] Attempting AI vision for product details"
        );
        product = await this._getProductWithAIVision(url, response);
      }

      return product;
    } catch (error) {
      logger.error(`[Platt Crawl4AI] Error fetching product from URL:`, error);
      throw error;
    }
  }

  /**
   * Extract product details from Crawl4AI response
   * @param {Object} response - Crawl4AI response
   * @param {string} url - Product URL
   * @returns {Promise<Object>} - Product object
   * @private
   */
  async _extractProductDetails(response, url) {
    // Try structured data first
    if (response.extractedData?.product) {
      const p = response.extractedData.product;
      return {
        name: p.name || p.title,
        price: this._parsePrice(p.price),
        sku: p.sku ? `PLATT-${p.sku}` : this._extractSkuFromUrl(url),
        url: url,
        imageUrl: p.image || p.images?.[0],
        source: "PLATT",
        availability: p.availability || "Unknown",
        brand: p.brand || p.manufacturer,
        description: p.description,
        specifications: p.specifications,
        method: "crawl4ai_structured",
      };
    }

    // Try CSS extraction
    const cssProduct = this._extractProductFromCSS(response);
    if (cssProduct) {
      return { ...cssProduct, url, source: "PLATT" };
    }

    // Try markdown extraction
    if (response.markdown) {
      const mdProduct = this._extractProductFromMarkdown(
        response.markdown,
        url
      );
      if (mdProduct) {
        return { ...mdProduct, url, source: "PLATT" };
      }
    }

    return null;
  }

  /**
   * Helper methods for extraction
   */

  _parsePrice(priceStr) {
    if (!priceStr) return null;
    const match = priceStr.toString().match(/[\d,]+\.?\d*/);
    return match ? parseFloat(match[0].replace(/,/g, "")) : null;
  }

  _extractSkuFromUrl(url) {
    const match = url.match(/\/p\/([^\/]+)/);
    return match ? `PLATT-${match[1]}` : null;
  }

  _extractFromCSS(cssData) {
    // Implementation depends on CSS extraction rules structure
    return [];
  }

  _extractFromMarkdown(markdown) {
    const products = [];
    // Basic regex patterns for common product listing formats
    const productPattern = /#{2,3}\s*(.+?)\n[\s\S]*?\$\s*([\d,]+\.?\d*)/g;
    let match;

    while ((match = productPattern.exec(markdown)) !== null) {
      products.push({
        name: match[1].trim(),
        price: this._parsePrice(match[2]),
        sku: null,
        source: "PLATT",
        method: "markdown_regex",
      });
    }

    return products;
  }

  _extractProductFromCSS(response) {
    // Placeholder for CSS-based extraction
    return null;
  }

  _extractProductFromMarkdown(markdown, url) {
    // Extract product details from markdown
    const product = {
      sku: this._extractSkuFromUrl(url),
      method: "markdown_extraction",
    };

    // Extract title
    const titleMatch = markdown.match(/^#\s+(.+)$/m);
    if (titleMatch) product.name = titleMatch[1].trim();

    // Extract price
    const priceMatch = markdown.match(/\$\s*([\d,]+\.?\d*)/);
    if (priceMatch) product.price = this._parsePrice(priceMatch[1]);

    // Extract brand
    const brandMatch = markdown.match(/(?:Brand|Manufacturer):\s*(.+)/i);
    if (brandMatch) product.brand = brandMatch[1].trim();

    return product.name ? product : null;
  }

  async _getProductWithAIVision(url, response) {
    // Similar to _searchWithAIVision but for product pages
    if (!response.screenshot) return null;

    try {
      const tempDir = path.join(__dirname, "../../uploads/ai-context/temp");
      await fs.mkdir(tempDir, { recursive: true });
      const screenshotPath = path.join(
        tempDir,
        `platt_product_${Date.now()}.png`
      );

      const screenshotBuffer = Buffer.from(response.screenshot, "base64");
      await fs.writeFile(screenshotPath, screenshotBuffer);

      const prompt = `Analyze this Platt Electric Supply product page screenshot. Extract:
- Product name/title
- Price (numeric value)
- SKU/catalog number
- Brand/manufacturer
- Availability
- Key specifications

Return as JSON with fields: name, price, sku, brand, availability, specifications`;

      const aiResult = await geminiService.analyzeImageForProducts(
        screenshotPath,
        prompt
      );

      await fs.unlink(screenshotPath).catch(() => {});

      if (!aiResult.success || !aiResult.product) return null;

      const p = aiResult.product;
      return {
        name: p.name,
        price: this._parsePrice(p.price),
        sku: p.sku ? `PLATT-${p.sku}` : this._extractSkuFromUrl(url),
        url: url,
        source: "PLATT",
        availability: p.availability || "Unknown",
        brand: p.brand,
        specifications: p.specifications,
        method: "ai_vision_product",
      };
    } catch (error) {
      logger.error("[Platt Crawl4AI] Product AI vision error:", error);
      return null;
    }
  }

  /**
   * Get diagnostic information
   * @returns {Object} - Diagnostic info
   */
  getDiagnostics() {
    const status =
      this.scrapingChallenges.blockedCount > 3
        ? "BLOCKED"
        : this.scrapingChallenges.cloudflareDetected
        ? "CHALLENGED"
        : "OPERATIONAL";

    return {
      status,
      source: "PLATT",
      method: "crawl4ai",
      challenges: this.scrapingChallenges,
      lastAttempt: this.scrapingChallenges.lastBlockTime,
      recommendations: this._getRecommendations(),
    };
  }

  _getRecommendations() {
    const recs = [];

    if (this.scrapingChallenges.cloudflareDetected) {
      recs.push("Consider implementing proxy rotation");
      recs.push("Reduce scraping frequency");
    }

    if (this.scrapingChallenges.loginRequired) {
      recs.push("Implement authenticated scraping for better access");
    }

    if (this.scrapingChallenges.blockedCount > 5) {
      recs.push("Temporarily pause Platt scraping");
      recs.push("Contact Platt for API access");
    }

    return recs;
  }

  /**
   * Heuristic to determine if the scraper is likely blocked
   * @returns {boolean}
   */
  isLikelyBlocked() {
    const s = this.scrapingChallenges;
    return (
      s.circuitBreakerOpen ||
      s.cloudflareDetected ||
      s.captchaDetected ||
      s.rateLimited ||
      s.blockedCount >= 5
    );
  }
}

module.exports = PlattCrawl4AIScraper;
