const ApiError = require("../utils/ApiError");
const logger = require("../utils/logger");

/**
 * Permission checker middleware factory
 * Returns middleware function that checks if user has specific permission
 * @param {string} permission - Required permission string (e.g., 'invoices:create')
 * @returns {Function} Express middleware
 */
exports.checkPermissions = (permission) => {
  return (req, res, next) => {
    try {
      const user = req.user;

      if (!user) {
        return next(ApiError.unauthorized("User must be authenticated"));
      }

      // Administrators have all permissions
      if (user.role === "Administrators") {
        logger.info(
          `Permission granted to ${user.email} (${user.role}) for ${permission} [Administrator override]`
        );
        return next();
      }

      // Check if user has the permission directly in their permissions array
      if (user.permissions && user.permissions.includes(permission)) {
        logger.info(
          `Permission granted to ${user.email} (${user.role}) for ${permission} [Direct permission]`
        );
        return next();
      }

      // Map roles to permissions for backwards compatibility and default permissions
      const rolePermissions = {
        Managers: [
          "users:read",
          "users:update",
          "jobs:create",
          "jobs:read",
          "jobs:update",
          "jobs:delete",
          "jobs:assign",
          "customers:create",
          "customers:read",
          "customers:update",
          "customers:delete",
          "invoices:create",
          "invoices:read",
          "invoices:update",
          "invoices:delete",
          "invoices:void",
          "inventory:read",
          "inventory:update",
        ],
        Supervisors: [
          "users:read",
          "users:update",
          "jobs:read",
          "jobs:update",
          "jobs:assign",
          "customers:read",
          "customers:update",
          "invoices:read",
          "invoices:update",
          "inventory:read",
        ],
        Technicians: [
          "jobs:read",
          "jobs:update",
          "customers:read",
          "invoices:read",
          "inventory:read",
        ],
      };

      // Check if user's role has the required permission in the default mapping
      const defaultRolePermissions = rolePermissions[user.role] || [];

      if (defaultRolePermissions.includes(permission)) {
        logger.info(
          `Permission granted to ${user.email} (${user.role}) for ${permission} [Role-based permission]`
        );
        return next();
      }

      // Special case handling for specific permissions
      if (handleSpecialCasePermissions(permission, user, req)) {
        logger.info(
          `Permission granted to ${user.email} (${user.role}) for ${permission} [Special case]`
        );
        return next();
      }

      // Permission denied - log this event
      logger.warn(
        `Permission denied for ${user.email} (${user.role}): ${permission}`
      );
      return next(ApiError.forbidden(`Missing permission: ${permission}`));
    } catch (error) {
      next(error);
    }
  };
};

/**
 * Handle special case permissions that require additional context
 * @param {string} permission - The permission being checked
 * @param {object} user - The user object
 * @param {object} req - The request object
 * @returns {boolean} - Whether the special case permission is granted
 */
function handleSpecialCasePermissions(permission, user, req) {
  // Special case for invoice updates by Technicians
  if (permission === "invoices:update" && user.role === "Technicians") {
    // Technicians can update only draft invoices
    const invoice = req.invoice;
    if (invoice && invoice.status === "DRAFT") {
      return true;
    }
  }

  // Special case for job assignments by Supervisors
  // They can only assign to their supervised technicians
  if (permission === "jobs:assign" && user.role === "Supervisors") {
    // Implementation would depend on how supervisors and technicians are related
    // This is a placeholder for that logic
    return false;
  }

  return false;
}

/**
 * Permission middleware for invoice management
 * Checks if user has the right permissions to perform invoice operations
 */
exports.canManageInvoices = (req, res, next) => {
  try {
    const user = req.user;

    // Check if user has the necessary role for invoice management
    if (!user) {
      return next(ApiError.unauthorized("User must be authenticated"));
    }

    // Administrative roles can always manage invoices
    if (["Administrators", "Managers"].includes(user.role)) {
      logger.info(`Invoice management granted to ${user.email} (${user.role})`);
      return next();
    }

    // Supervisors can view and update invoices but not delete/void
    if (user.role === "Supervisors") {
      const method = req.method;

      // Supervisors can GET invoices
      if (method === "GET") {
        logger.info(`Invoice read granted to supervisor ${user.email}`);
        return next();
      }

      // Supervisors can update invoices
      if (
        (method === "PUT" || method === "PATCH") &&
        !req.path.includes("/void")
      ) {
        logger.info(`Invoice update granted to supervisor ${user.email}`);
        return next();
      }
    }

    // Technicians can only view invoices
    if (user.role === "Technicians") {
      const method = req.method;

      // Technicians can GET invoices
      if (method === "GET") {
        logger.info(`Invoice read granted to technician ${user.email}`);
        return next();
      }
    }

    // If we get here, the user doesn't have permission
    logger.warn(
      `Invoice operation denied for ${user.email} (${user.role}): ${req.method} ${req.path}`
    );
    return next(
      ApiError.forbidden("Insufficient permissions for this invoice operation")
    );
  } catch (error) {
    next(error);
  }
};

/**
 * Checks if the user has administrative privileges
 */
exports.isAdmin = (req, res, next) => {
  try {
    const user = req.user;

    if (!user) {
      return next(ApiError.unauthorized("User must be authenticated"));
    }

    if (user.role !== "Administrators") {
      logger.warn(
        `Administrative access denied for ${user.email} (${user.role})`
      );
      return next(ApiError.forbidden("Administrator access required"));
    }

    logger.info(`Administrative access granted to ${user.email}`);

    next();
  } catch (error) {
    next(error);
  }
};

/**
 * Checks if the user is the owner or has administrative rights
 */
exports.isOwnerOrAdmin = (req, res, next) => {
  try {
    const user = req.user;
    const resourceCreator = req.resource?.createdBy;

    if (!user) {
      return next(ApiError.unauthorized("User must be authenticated"));
    }

    // Administrators can access everything
    if (user.role === "Administrators") {
      logger.info(
        `Owner/admin access granted to ${user.email} (Administrator)`
      );
      return next();
    }

    // Owner check
    if (resourceCreator && resourceCreator.toString() === user._id.toString()) {
      logger.info(`Owner access granted to ${user.email} for resource`);
      return next();
    }

    logger.warn(`Owner/admin access denied for ${user.email} (${user.role})`);
    return next(
      ApiError.forbidden("You do not have permission to access this resource")
    );
  } catch (error) {
    next(error);
  }
};

/**
 * Middleware to load the invoice into the request for permission checks
 */
exports.loadInvoiceForPermission = async (req, res, next) => {
  try {
    // Only apply to requests with invoice ID
    if (!req.params.id) {
      return next();
    }

    const Invoice = require("../models/Invoice");
    const invoice = await Invoice.findById(req.params.id);

    if (!invoice) {
      return next();
    }

    req.invoice = invoice;
    next();
  } catch (error) {
    next(error);
  }
};
