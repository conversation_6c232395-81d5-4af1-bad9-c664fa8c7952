# Workiz Backend Environment Variables

# Server Configuration
NODE_ENV=development
PORT=5000

# Database Configuration
MONGODB_URI=mongodb://localhost:27017/workiz_clone

# JWT Authentication
# IMPORTANT: Use strong, unique secrets and rotate them periodically
# Generate with: require('crypto').randomBytes(64).toString('hex')
JWT_SECRET=your_development_jwt_secret_here
JWT_SECRET_PRODUCTION=your_production_jwt_secret_here
JWT_EXPIRE=30d
JWT_COOKIE_EXPIRE=30
JWT_REFRESH_SECRET=your_refresh_token_secret_here
JWT_REFRESH_EXPIRE=7d

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=

# AI Service Configuration
GEMINI_API_KEY=your_gemini_api_key_here
# Model Order: 1.5 Pro -> 1.5 Flash -> Exp -> 2.0 Lite
AI_MODEL_PRIMARY=models/gemini-2.0-flash                # Primary (Stable, JSON Mode)
AI_MODEL_FALLBACK_1=models/gemini-1.5-flash           # Fallback 1 (Stable, JSON Mode)
AI_MODEL_FALLBACK_2=gemini-2.5-pro-exp-03-25          # Fallback 2 (Experimental)
AI_MODEL_FALLBACK_3=models/gemini-2.0-flash-lite      # Fallback 3 (Stable)
AI_PRIMARY_RATE_LIMIT=2                               # From gemini.md (Free Tier for 1.5 Pro)
AI_FALLBACK_1_RATE_LIMIT=15                           # From gemini.md (Free Tier for 1.5 Flash)
AI_FALLBACK_2_RATE_LIMIT=5                            # From gemini.md (Free Tier for Exp)
AI_FALLBACK_3_RATE_LIMIT=30                           # From gemini.md (Free Tier for 2.0 Flash Lite)

GEMINI_TIMEOUT=60000 # Timeout for Gemini API calls in milliseconds (default: 60000)

# Deepseek AI Configuration (Used as final fallback)
AI_MODEL_DEEPSEEK=deepseek-chat # Model name for Deepseek (Consistent Naming)
AI_DEEPSEEK_RATE_LIMIT=20 # Example rate limit (RPM) (Consistent Naming)
DEEPSEEK_API_KEY=your_deepseek_api_key_here
DEEPSEEK_TIMEOUT=30000 # Timeout in milliseconds (default: 30000)
HUGGINGFACE_INFERENCE_API_URL=https://api-inference.huggingface.co/models/t5-base # Optional: Override default model for query refinement

# Google Custom Search Configuration
GOOGLE_CUSTOM_SEARCH_API_KEY=your_google_search_api_key_here
GOOGLE_CUSTOM_SEARCH_CX=your_google_search_cx_id_here
# Comma-separated list of domains to prioritize in search results (e.g., homedepot.com,lowes.com)
GOOGLE_SEARCH_PREFERRED_DOMAINS=homedepot.com,lowes.com,platt.com,supplyhouse.com
# Email Configuration
SMTP_HOST=smtp.example.com
SMTP_PORT=587
SMTP_USER=your_smtp_username
SMTP_PASSWORD=your_smtp_password
FROM_EMAIL=<EMAIL>
FROM_NAME=Workiz

# Logging Configuration
LOG_LEVEL=info

# Security Configuration
CORS_ORIGIN=http://localhost:3000

# Feature Flags
ENABLE_AI_FEATURES=true
ENABLE_ADVANCED_ANALYTICS=true

# Crawl4AI / Scraper timeouts and breaker (tune to reduce timeouts and noisy errors)
CRAWL4AI_DEFAULT_TIMEOUT=45000               # Default timeout for crawl operations (ms)
PLATT_CIRCUIT_BREAKER_FAILURE_THRESHOLD=5    # Failures before opening breaker
PLATT_CIRCUIT_BREAKER_TIMEOUT_MS=180000      # Breaker open duration (ms)
