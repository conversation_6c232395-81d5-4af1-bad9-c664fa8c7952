const express = require("express");
const router = express.Router();
const {
  createCustomer,
  getCustomers,
  getCustomerById,
  getCustomerWithJobs,
  getCustomerWithInvoices,
  updateCustomer,
  deleteCustomer,
} = require("../controllers/customerController");
const {
  uploadCustomerImages,
  getCustomerImages,
  deleteCustomerImage,
  updateCustomerImage,
} = require("../controllers/customerImageController");
const { protect } = require("../middleware/authMiddleware");
const upload = require("../middleware/uploadMiddleware");

// All routes are protected
router.use(protect);

router.route("/").post(createCustomer).get(getCustomers);

router
  .route("/:id")
  .get(getCustomerById)
  .put(updateCustomer)
  .delete(deleteCustomer);

router.get("/:id/jobs", getCustomerWithJobs);
router.get("/:id/invoices", getCustomerWithInvoices);

// Customer images routes
router
  .route("/:id/images")
  .get(getCustomerImages)
  .post(upload.array("customerImages", 10), uploadCustomerImages);

router
  .route("/:id/images/:imageId")
  .put(updateCustomerImage)
  .delete(deleteCustomerImage);

module.exports = router;
