/**
 * Materials Textbox Validation Tests
 * Tests the specific fix for materials textbox validation logic
 * Validates the fix where hasI<PERSON><PERSON> was incorrectly prioritized over materials text
 */

const { test, expect } = require('@playwright/test');

test.describe('Materials Textbox Validation Fix', () => {
  let authToken;
  let page;
  
  const testUser = {
    email: '<EMAIL>',
    password: 'password123'
  };

  test.beforeAll(async ({ request }) => {
    // Login to get auth token
    const response = await request.post('http://localhost:5000/api/users/login', {
      data: testUser
    });
    
    expect(response.status()).toBe(200);
    const responseData = await response.json();
    authToken = responseData.token;
  });

  test.beforeEach(async ({ browser }) => {
    page = await browser.newPage();
    // Set auth token in localStorage
    await page.goto('http://localhost:3000');
    await page.evaluate((token) => {
      localStorage.setItem('token', token);
    }, authToken);
  });

  test.afterEach(async () => {
    await page.close();
  });

  test.describe('Materials Textbox Priority Fix', () => {
    test('should process materials textbox when no valid items exist', async () => {
      // Navigate to quote creation page
      await page.goto('http://localhost:3000/create-quote');
      
      // Wait for form to load
      await expect(page.locator('[data-testid="materials-textbox"]')).toBeVisible();
      
      // Fill materials textbox with realistic electrical materials
      const materialsText = `200 amp electrical panel upgrade
12 AWG copper wire - 500 feet
20 amp GFCI outlets - 8 units
15 amp standard outlets - 12 units
2-pole 20 amp circuit breakers - 4 units
1-pole 15 amp circuit breakers - 6 units
Electrical conduit 3/4 inch - 100 feet
Wire nuts assorted sizes
Electrical boxes - 20 units
Ground fault circuit interrupter`;
      
      await page.fill('[data-testid="materials-textbox"]', materialsText);
      
      // Fill other required fields
      await page.fill('[data-testid="customer-name"]', 'Materials Test Customer');
      await page.fill('[data-testid="project-overview"]', 'Electrical panel upgrade project');
      
      // Click Generate from Materials button
      await page.click('[data-testid="generate-from-materials"]');
      
      // Should NOT show the old validation error
      await expect(page.locator('.error-message')).not.toContainText(
        'Please ensure your materials list contains items with valid names',
        { timeout: 5000 }
      );
      
      // Should show AI processing or clarification dialog
      await expect(
        page.locator('[data-testid="ai-progress"], [data-testid="clarification-dialog"]')
      ).toBeVisible({ timeout: 10000 });
    });

    test('should handle empty materials textbox gracefully', async () => {
      await page.goto('http://localhost:3000/create-quote');
      
      // Leave materials textbox empty
      await page.fill('[data-testid="customer-name"]', 'Empty Materials Test');
      await page.fill('[data-testid="project-overview"]', 'Test project');
      
      // Try to generate from materials
      await page.click('[data-testid="generate-from-materials"]');
      
      // Should show appropriate error for empty materials
      await expect(page.locator('.error-message')).toContainText(
        'Please provide either materials list or materials description'
      );
    });

    test('should prioritize valid items over materials textbox when items exist', async () => {
      await page.goto('http://localhost:3000/create-quote');
      
      // Add a valid material item
      await page.click('[data-testid="add-material"]');
      await page.fill('[data-testid="material-name-0"]', 'Valid Material Item');
      await page.fill('[data-testid="material-quantity-0"]', '1');
      
      // Also fill materials textbox
      await page.fill('[data-testid="materials-textbox"]', 'Materials textbox content');
      
      // Fill other required fields
      await page.fill('[data-testid="customer-name"]', 'Priority Test Customer');
      await page.fill('[data-testid="project-overview"]', 'Priority test project');
      
      // Generate quote
      await page.click('[data-testid="generate-ai-quote"]');
      
      // Should process successfully (prioritizing valid items)
      await expect(
        page.locator('[data-testid="ai-progress"], [data-testid="clarification-dialog"]')
      ).toBeVisible({ timeout: 10000 });
    });

    test('should handle mixed valid and invalid items correctly', async () => {
      await page.goto('http://localhost:3000/create-quote');
      
      // Add one valid and one invalid material item
      await page.click('[data-testid="add-material"]');
      await page.fill('[data-testid="material-name-0"]', 'Valid Material');
      await page.fill('[data-testid="material-quantity-0"]', '1');
      
      await page.click('[data-testid="add-material"]');
      // Leave second material name empty (invalid)
      await page.fill('[data-testid="material-quantity-1"]', '1');
      
      // Fill other required fields
      await page.fill('[data-testid="customer-name"]', 'Mixed Materials Test');
      await page.fill('[data-testid="project-overview"]', 'Mixed materials test project');
      
      // Generate quote
      await page.click('[data-testid="generate-ai-quote"]');
      
      // Should process successfully (using the valid item)
      await expect(
        page.locator('[data-testid="ai-progress"], [data-testid="clarification-dialog"]')
      ).toBeVisible({ timeout: 10000 });
    });

    test('should fall back to materials textbox when all items are invalid', async () => {
      await page.goto('http://localhost:3000/create-quote');
      
      // Add invalid material items (empty names)
      await page.click('[data-testid="add-material"]');
      await page.fill('[data-testid="material-quantity-0"]', '1');
      
      await page.click('[data-testid="add-material"]');
      await page.fill('[data-testid="material-quantity-1"]', '2');
      
      // Fill materials textbox as fallback
      await page.fill('[data-testid="materials-textbox"]', 'Fallback materials from textbox\nElectrical panel\nWiring materials');
      
      // Fill other required fields
      await page.fill('[data-testid="customer-name"]', 'Fallback Test Customer');
      await page.fill('[data-testid="project-overview"]', 'Fallback test project');
      
      // Generate from materials
      await page.click('[data-testid="generate-from-materials"]');
      
      // Should process successfully using textbox content
      await expect(
        page.locator('[data-testid="ai-progress"], [data-testid="clarification-dialog"]')
      ).toBeVisible({ timeout: 10000 });
    });
  });

  test.describe('Console Monitoring', () => {
    test('should log correct validation flow in console', async () => {
      const consoleLogs = [];
      page.on('console', msg => {
        if (msg.type() === 'log' && msg.text().includes('validation')) {
          consoleLogs.push(msg.text());
        }
      });
      
      await page.goto('http://localhost:3000/create-quote');
      
      // Fill materials textbox
      await page.fill('[data-testid="materials-textbox"]', 'Test materials for console validation');
      await page.fill('[data-testid="customer-name"]', 'Console Test Customer');
      await page.fill('[data-testid="project-overview"]', 'Console test project');
      
      // Generate from materials
      await page.click('[data-testid="generate-from-materials"]');
      
      // Wait for processing
      await page.waitForTimeout(3000);
      
      // Check that validation logs show correct flow
      const hasValidationLogs = consoleLogs.some(log => 
        log.includes('hasItems') || log.includes('hasMaterialsText')
      );
      
      expect(hasValidationLogs).toBe(true);
    });
  });

  test.describe('Regression Prevention', () => {
    test('should not regress to old validation error', async () => {
      await page.goto('http://localhost:3000/create-quote');
      
      // Simulate the exact scenario that was failing before the fix
      // (empty item from form initialization + materials textbox content)
      
      // Fill materials textbox
      await page.fill('[data-testid="materials-textbox"]', 'Regression test materials\nElectrical components');
      
      // Fill required fields
      await page.fill('[data-testid="customer-name"]', 'Regression Test Customer');
      await page.fill('[data-testid="project-overview"]', 'Regression prevention test');
      
      // Generate from materials
      await page.click('[data-testid="generate-from-materials"]');
      
      // Should NOT show the specific old error message
      await expect(page.locator('.error-message')).not.toContainText(
        'Please ensure your materials list contains items with valid names',
        { timeout: 5000 }
      );
      
      // Should proceed with AI processing
      await expect(
        page.locator('[data-testid="ai-progress"], [data-testid="clarification-dialog"]')
      ).toBeVisible({ timeout: 10000 });
    });
  });
});