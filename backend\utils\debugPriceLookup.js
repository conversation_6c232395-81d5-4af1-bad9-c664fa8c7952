/**
 * Debug utility to manually trigger price lookups
 * Useful for testing when change streams aren't working
 */

const logger = require("./logger");
const Quote = require("../models/Quote");
const priceLookupService = require("../services/priceLookupService");
const mongoose = require("mongoose");

/**
 * Manually trigger price lookup for a specific quote
 * @param {string} quoteId - Quote ID
 * @returns {Promise<Object>} - Result summary
 */
async function triggerPriceLookupForQuote(quoteId) {
  try {
    logger.info(
      `[DebugPriceLookup] Manually triggering price lookup for quote ${quoteId}`
    );

    const quote = await Quote.findById(quoteId);
    if (!quote) {
      throw new Error(`Quote ${quoteId} not found`);
    }

    let itemsProcessed = 0;
    let itemsWithPending = 0;
    let errors = [];

    for (const item of quote.items) {
      if (!item.lookup_results || !Array.isArray(item.lookup_results)) {
        continue;
      }

      const lookupEntry = item.lookup_results.find(
        (lr) =>
          lr.status === "pending_internal_price_lookup" &&
          lr.mcp_request &&
          lr.mcp_request.internal_query_details
      );

      if (lookupEntry) {
        itemsWithPending++;

        try {
          logger.info(
            `[DebugPriceLookup] Processing item ${item._id} with query: ${lookupEntry.mcp_request.internal_query_details}`
          );

          const originalToolParams = {
            description: lookupEntry.mcp_request.internal_query_details,
            userId: quote.createdBy,
            companyId: quote.company,
            quantity: item.quantity || 1,
          };

          await priceLookupService.initiatePriceLookup(
            quote._id,
            item._id,
            originalToolParams
          );

          itemsProcessed++;
        } catch (error) {
          logger.error(
            `[DebugPriceLookup] Error processing item ${item._id}:`,
            error
          );
          errors.push({ itemId: item._id.toString(), error: error.message });
        }
      }
    }

    const result = {
      quoteId: quoteId,
      totalItems: quote.items.length,
      itemsWithPending,
      itemsProcessed,
      errors,
    };

    logger.info(`[DebugPriceLookup] Completed. Result:`, result);
    return result;
  } catch (error) {
    logger.error(`[DebugPriceLookup] Error:`, error);
    throw error;
  }
}

/**
 * Check status of all pending price lookups
 * @returns {Promise<Object>} - Status summary
 */
async function checkPendingLookups() {
  try {
    const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000);

    const quotes = await Quote.find({
      createdAt: { $gte: oneHourAgo },
      "items.lookup_results": {
        $elemMatch: {
          status: "pending_internal_price_lookup",
        },
      },
    }).limit(20);

    const summary = {
      quotesWithPending: quotes.length,
      quotes: [],
    };

    for (const quote of quotes) {
      let pendingCount = 0;

      for (const item of quote.items) {
        if (item.lookup_results && Array.isArray(item.lookup_results)) {
          const hasPending = item.lookup_results.some(
            (lr) => lr.status === "pending_internal_price_lookup"
          );
          if (hasPending) pendingCount++;
        }
      }

      summary.quotes.push({
        quoteId: quote._id.toString(),
        quoteName: quote.name,
        createdAt: quote.createdAt,
        totalItems: quote.items.length,
        itemsWithPendingLookup: pendingCount,
      });
    }

    return summary;
  } catch (error) {
    logger.error(`[DebugPriceLookup] Error checking pending lookups:`, error);
    throw error;
  }
}

/**
 * Force process all pending lookups
 * @param {number} limit - Maximum number of quotes to process
 * @returns {Promise<Object>} - Processing summary
 */
async function forceProcessAllPending(limit = 10) {
  try {
    logger.info(
      `[DebugPriceLookup] Force processing all pending lookups (limit: ${limit})`
    );

    const pendingSummary = await checkPendingLookups();
    const quotesToProcess = pendingSummary.quotes.slice(0, limit);

    const results = {
      totalProcessed: 0,
      successCount: 0,
      errorCount: 0,
      quotes: [],
    };

    for (const quoteInfo of quotesToProcess) {
      try {
        const result = await triggerPriceLookupForQuote(quoteInfo.quoteId);
        results.quotes.push(result);
        results.totalProcessed += result.itemsProcessed;
        results.successCount += result.itemsProcessed;
      } catch (error) {
        results.errorCount++;
        results.quotes.push({
          quoteId: quoteInfo.quoteId,
          error: error.message,
        });
      }
    }

    logger.info(`[DebugPriceLookup] Force processing complete:`, results);
    return results;
  } catch (error) {
    logger.error(`[DebugPriceLookup] Error in force processing:`, error);
    throw error;
  }
}

module.exports = {
  triggerPriceLookupForQuote,
  checkPendingLookups,
  forceProcessAllPending,
};
