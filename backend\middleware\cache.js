const Redis = require("ioredis");
const logger = require("../utils/logger");

// Initialize Redis client only if not in test environment and Redis is not disabled
let redisClient = null;
if (process.env.NODE_ENV !== "test" && process.env.REDIS_DISABLED !== "true") {
  try {
    redisClient = process.env.REDIS_URL
      ? new Redis(process.env.REDIS_URL)
      : new Redis({
          host: process.env.REDIS_HOST || "workiz-redis",
          port: process.env.REDIS_PORT || 6379,
          ...(process.env.REDIS_PASSWORD && { password: process.env.REDIS_PASSWORD }), // Only include password if set
          retryDelayOnFailover: 100,
          lazyConnect: true,
          connectTimeout: 5000,
          commandTimeout: 5000,
        });
  } catch (error) {
    logger.warn(
      "Redis connection failed, continuing without cache:",
      error.message
    );
    redisClient = null;
  }
}

if (!redisClient) {
  // Provide a basic mock object when Redis is not available
  const mockFn = process.env.NODE_ENV === "test" ? jest.fn() : () => {};
  redisClient = {
    get:
      process.env.NODE_ENV === "test"
        ? jest.fn().mockResolvedValue(null)
        : async () => null,
    set:
      process.env.NODE_ENV === "test"
        ? jest.fn().mockResolvedValue("OK")
        : async () => "OK",
    del:
      process.env.NODE_ENV === "test"
        ? jest.fn().mockResolvedValue(1)
        : async () => 1,
    keys:
      process.env.NODE_ENV === "test"
        ? jest.fn().mockResolvedValue([])
        : async () => [],
    ping:
      process.env.NODE_ENV === "test"
        ? jest.fn().mockResolvedValue("PONG")
        : async () => "PONG",
    on: mockFn,
    quit:
      process.env.NODE_ENV === "test"
        ? jest.fn().mockResolvedValue("OK")
        : async () => "OK",
    disconnect: mockFn,
    pipeline:
      process.env.NODE_ENV === "test"
        ? jest.fn().mockReturnThis()
        : () => redisClient,
    exec:
      process.env.NODE_ENV === "test"
        ? jest.fn().mockResolvedValue([])
        : async () => [],
    call:
      process.env.NODE_ENV === "test"
        ? jest.fn().mockResolvedValue(null)
        : async () => null,
  };
}

if (redisClient && process.env.NODE_ENV !== "test") {
  // Add check before attaching listeners
  redisClient.on("error", (err) => {
    logger.error(`Redis error: ${err.message}`);
  });
}

if (redisClient && process.env.NODE_ENV !== "test") {
  // Add check before attaching listeners
  redisClient.on("connect", () => {
    logger.info("Connected to Redis server");
  });
}

// For compatibility with different Redis packages
const redis = redisClient;

// Cache middleware
const cacheMiddleware = (ttl = 300) => {
  return async (req, res, next) => {
    // Skip cache for non-GET requests
    if (req.method !== "GET") {
      return next();
    }

    try {
      // Create a unique cache key based on the URL and query parameters
      const cacheKey = `api:${req.originalUrl || req.url}`;

      // Try to get cached response
      const cachedResponse = await redis.get(cacheKey);

      if (cachedResponse) {
        const parsedResponse = JSON.parse(cachedResponse);
        return res.status(200).json(parsedResponse);
      }

      // Store the original JSON response
      const originalJson = res.json;

      // Override res.json method to cache the response before sending
      res.json = function (body) {
        // Only cache successful responses
        if (res.statusCode >= 200 && res.statusCode < 300) {
          redis.set(cacheKey, JSON.stringify(body), "EX", ttl);
        }

        // Call the original json method
        return originalJson.call(this, body);
      };

      next();
    } catch (error) {
      logger.error(`Cache middleware error: ${error.message}`);
      next();
    }
  };
};

// Manually clear cache for specific keys
const clearCache = async (pattern) => {
  try {
    const keys = await redis.keys(`api:${pattern}*`);
    if (keys.length > 0) {
      await redis.del(keys);
      logger.info(
        `Cleared cache for pattern: ${pattern}, keys: ${keys.length}`
      );
    }
  } catch (error) {
    logger.error(`Error clearing cache: ${error.message}`);
  }
};

// Clear all cache
const clearAllCache = async () => {
  try {
    await redis.flushdb();
    logger.info("Cleared all cache");
  } catch (error) {
    logger.error(`Error clearing all cache: ${error.message}`);
  }
};

// Health check for Redis
const healthCheck = async () => {
  try {
    const pong = await redis.ping();
    return pong === "PONG";
  } catch (error) {
    logger.error(`Redis health check failed: ${error.message}`);
    return false;
  }
};

module.exports = {
  redis,
  redisClient,
  cacheMiddleware,
  clearCache,
  clearAllCache,
  healthCheck,
};
