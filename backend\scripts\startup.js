#!/usr/bin/env node

/**
 * Startup script that ensures MongoDB replica set is initialized
 * before starting the main application
 */

require('dotenv').config();
const { exec } = require('child_process');
const { setTimeout } = require('timers/promises');
const logger = require('../utils/logger');

async function waitForMongoDB() {
  console.log('Waiting for MongoDB to be ready...');

  let attempts = 0;
  const maxAttempts = 30; // 30 attempts * 2 seconds = 60 seconds total

  while (attempts < maxAttempts) {
    try {
      // Use mongosh ping command to check if MongoDB is ready
      await new Promise((resolve, reject) => {
        exec('docker exec workiz-mongo-rs mongosh --eval "db.runCommand(\'ping\')"',
          (error, stdout, stderr) => {
            if (error) {
              reject(error);
            } else {
              resolve(stdout);
            }
          }
        );
      });

      console.log('MongoDB is ready');
      return true;

    } catch (error) {
      attempts++;
      if (attempts >= maxAttempts) {
        console.error('MongoDB not ready after maximum attempts');
        return false;
      }

      console.log(`Waiting for MongoDB... (attempt ${attempts}/${maxAttempts})`);
      await setTimeout(2000); // Wait 2 seconds between attempts
    }
  }
}

async function initializeReplicaSet() {
  try {
    console.log('Checking if replica set needs initialization...');
    
    // Check if replica set is already initialized
    const checkResult = await new Promise((resolve, reject) => {
      exec('docker exec workiz-mongo-rs mongosh --eval "rs.status()"', 
        (error, stdout, stderr) => {
          if (error) {
            // If rs.status fails, it might mean replica set is not initialized
            if (stderr.includes('no replset config') || 
                stderr.includes('NotYetInitialized')) {
              resolve({ needsInitialization: true });
            } else {
              reject(error);
            }
          } else {
            resolve({ needsInitialization: false, status: stdout });
          }
        }
      );
    });
    
    if (checkResult.needsInitialization) {
      console.log('Initializing replica set...');
      
      const initResult = await new Promise((resolve, reject) => {
        exec(`docker exec workiz-mongo-rs mongosh --eval "rs.initiate({_id: 'rs0', members: [{_id: 0, host: 'workiz-mongo-rs:27017'}]})"`, 
          (error, stdout, stderr) => {
            if (error) {
              // Check if it's already initialized error
              if (stderr.includes('already initialized') || stdout.includes('already initialized')) {
                resolve({ alreadyInitialized: true });
              } else {
                reject(error);
              }
            } else {
              resolve({ success: true, output: stdout });
            }
          }
        );
      });
      
      if (initResult.alreadyInitialized) {
        console.log('Replica set already initialized');
      } else if (initResult.success) {
        console.log('Replica set initialized successfully');
        
        // Wait a bit for replica set to stabilize
        console.log('Waiting for replica set to become ready...');
        await setTimeout(5000);
        
        // Verify the initialization
        const statusResult = await new Promise((resolve, reject) => {
          exec('docker exec workiz-mongo-rs mongosh --eval "rs.status()"', 
            (error, stdout, stderr) => {
              if (error) {
                reject(error);
              } else {
                resolve(stdout);
              }
            }
          );
        });
        
        console.log('Replica set status verified');
      }
    } else {
      console.log('Replica set already initialized');
    }
    
    return true;
    
  } catch (error) {
    console.error('Error initializing replica set:', error.message);
    return false;
  }
}

async function main() {
  console.log('=== Workiz Startup Script ===');
  
  // Wait for MongoDB to be ready
  const mongoReady = await waitForMongoDB();
  if (!mongoReady) {
    console.error('MongoDB failed to become ready. Exiting.');
    process.exit(1);
  }
  
  // Initialize replica set if needed
  const replicaSetInitialized = await initializeReplicaSet();
  if (!replicaSetInitialized) {
    console.error('Failed to initialize replica set. Exiting.');
    process.exit(1);
  }
  
  console.log('Startup completed successfully. Starting main application...');
  
  // Start the main application
  const { spawn } = require('child_process');
  const app = spawn('node', ['server.js'], { stdio: 'inherit' });
  
  app.on('close', (code) => {
    console.log(`Application exited with code ${code}`);
    process.exit(code);
  });
  
  app.on('error', (error) => {
    console.error('Failed to start application:', error);
    process.exit(1);
  });
}

// Run if this script is executed directly
if (require.main === module) {
  main().catch(error => {
    console.error('Startup script failed:', error);
    process.exit(1);
  });
}

module.exports = { waitForMongoDB, initializeReplicaSet };