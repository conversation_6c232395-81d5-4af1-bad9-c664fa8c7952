const Invoice = require("../models/Invoice");
const Job = require("../models/Job");
const mongoose = require("mongoose");
const PDFDocument = require("pdfkit");
const { format } = require("date-fns");
const logger = require("../utils/logger");
const { OpenAI } = require("openai");
const ApiError = require("../utils/ApiError");
const { generateInvoiceNumber } = require("../utils/invoiceUtils");
const { generateFollowupStrategy } = require("./aiController");
const { sendInvoiceEmail } = require("../services/emailService");
const { validateInvoiceData } = require("../validators/invoiceValidator");

const openai = new OpenAI(process.env.OPENAI_API_KEY);
const Quote = require("../models/Quote"); // Updated import

// Helper functions for PDF generation
const formatCurrency = (amount) => {
  if (typeof amount !== "number") {
    return "$0.00";
  }
  return `$${amount.toFixed(2)}`;
};

const formatDate = (date) => {
  if (!date) return "N/A";
  try {
    const d = new Date(date);
    if (isNaN(d.getTime())) return "Invalid Date";
    return format(d, "MM/dd/yyyy"); // Simpler format for PDF
  } catch (e) {
    return "Invalid Date";
  }
};

/**
 * Create a new invoice
 * @route POST /api/invoices
 */
exports.createInvoice = async (req, res, next) => {
  try {
    const { jobId, ...invoiceData } = req.body;

    // Job field should already be present due to middleware transformation,
    // but if not, we'll add it here as a fallback
    const validatedData = await validateInvoiceData(req.body);

    // Get job details
    let job = await Job.findById(jobId).populate("customer");
    if (!job) {
      console.error(`Job not found with ID: ${jobId}`);

      // Try using the job ID from the validated data instead
      let altJobId = validatedData.job;

      if (altJobId && altJobId !== jobId) {
        console.log(`Trying alternative job ID: ${altJobId}`);
        job = await Job.findById(altJobId).populate("customer");
      }

      if (!job) {
        // If we have a customer ID but no job, create a "virtual" job reference
        if (validatedData.customer) {
          const customer = await require("../models/Customer").findById(
            validatedData.customer
          );
          if (customer) {
            console.log(
              `Creating virtual job reference for customer: ${customer._id}`
            );

            // Create a valid MongoDB ObjectId for a "virtual" job
            // This is required because the Invoice model requires a valid ObjectId for the job field
            const virtualJobId = new mongoose.Types.ObjectId();
            console.log(`Generated virtual job ID: ${virtualJobId}`);

            job = {
              _id: virtualJobId,
              customer: customer,
              title: "Invoice without job reference",
              virtual: true, // Mark this as a virtual job
            };
          } else {
            throw new ApiError(404, "Neither job nor customer found");
          }
        } else {
          throw new ApiError(404, "Job not found and no customer ID provided");
        }
      }
    }

    // Generate invoice number
    const invoiceNumber = await generateInvoiceNumber();

    // Analyze payment risk using AI (mock implementation)
    const aiInsights = {
      paymentRiskScore: 15,
      riskFactors: ["Large commercial client", "Established payment history"],
      suggestedFollowUp: "Send payment reminder 5 days before due date",
      lastUpdated: new Date(),
    };

    // Prepare the invoice data
    const invoiceCreateData = {
      ...validatedData,
      number: invoiceNumber,
      customer: job.customer._id,
      aiInsights,
      metadata: {
        createdBy: req.user._id,
        lastModifiedBy: req.user._id,
      },
    };

    // Ensure the job ID is set properly for MongoDB
    const jobId_str = job._id.toString();
    invoiceCreateData.job = jobId_str;
    console.log(`Using job ID for invoice: ${jobId_str}`);

    // Create invoice
    console.log("Creating invoice with job and customer:", {
      job: invoiceCreateData.job,
      customer: invoiceCreateData.customer,
    });

    const invoice = await Invoice.create(invoiceCreateData);
    console.log(`Invoice created successfully with ID: ${invoice._id}`);

    // Update job with invoice reference only if it's a real job (not a virtual one)
    if (
      job &&
      !job.virtual &&
      typeof job.constructor === "function" &&
      job.constructor.name === "model"
    ) {
      try {
        console.log(`Updating job ${job._id} with invoice reference`);
        await Job.findByIdAndUpdate(job._id, {
          $push: { invoices: invoice._id },
          $set: { "metadata.lastModifiedBy": req.user._id },
        });
      } catch (jobUpdateError) {
        console.error(
          "Failed to update job with invoice reference:",
          jobUpdateError
        );
        // Continue execution despite this error - the invoice is already created
      }
    } else {
      console.log("Skipping job update as this is a virtual job reference");
    }

    // Send invoice email if not in draft status
    if (invoice.status !== "DRAFT") {
      await sendInvoiceEmail(invoice, job.customer);
    }

    // Transform the invoice object with all fields needed by the frontend
    const returnInvoice = invoice.toObject();

    // Ensure all required frontend fields exist with proper defaults
    returnInvoice.balance = returnInvoice.balance || returnInvoice.total || 0;
    returnInvoice.issueDate = returnInvoice.issueDate || new Date();
    returnInvoice.dueDate =
      returnInvoice.dueDate || new Date(Date.now() + 30 * 24 * 60 * 60 * 1000); // Default 30 days

    // Add customer.name for frontend compatibility
    if (returnInvoice.customer) {
      // Handle both mongoose models and plain objects
      const customerObj = job.customer.toObject
        ? job.customer.toObject()
        : job.customer;
      returnInvoice.customer = {
        ...customerObj,
        name:
          customerObj.businessName || customerObj.contactPerson || "Unknown",
      };
    }

    // Add job title for frontend compatibility
    if (returnInvoice.job) {
      returnInvoice.job = {
        _id: job._id,
        title: job.title || "N/A",
        status: job.status || "UNKNOWN",
      };
    }

    console.log("Returning invoice to client with required fields:", {
      id: returnInvoice._id,
      number: returnInvoice.number,
      customer: returnInvoice.customer?.name,
      job: returnInvoice.job?.title,
      issueDate: returnInvoice.issueDate,
      dueDate: returnInvoice.dueDate,
      total: returnInvoice.total,
      balance: returnInvoice.balance,
    });
    res.status(201).json(returnInvoice);
  } catch (error) {
    console.error("Invoice creation error:", error);
    next(error);
  }
};

/**
 * Get all invoices with filtering and pagination
 * @route GET /api/invoices
 */
exports.getInvoices = async (req, res, next) => {
  try {
    const {
      page = 1,
      limit = 10,
      status,
      customer,
      startDate,
      endDate,
      minAmount,
      maxAmount,
      sortBy = "createdAt",
      sortOrder = "desc",
    } = req.query;

    // Build query
    const query = {};
    if (status) query.status = status;
    if (customer) query.customer = customer;
    if (startDate || endDate) {
      query.issueDate = {};
      if (startDate) query.issueDate.$gte = new Date(startDate);
      if (endDate) query.issueDate.$lte = new Date(endDate);
    }
    if (minAmount || maxAmount) {
      query.total = {};
      if (minAmount) query.total.$gte = Number(minAmount);
      if (maxAmount) query.total.$lte = Number(maxAmount);
    }

    // Execute query with pagination
    let invoices = await Invoice.find(query)
      .populate("customer", "businessName contactPerson email")
      .populate("job", "title status")
      .sort({ [sortBy]: sortOrder === "desc" ? -1 : 1 })
      .skip((page - 1) * limit)
      .limit(limit);

    // Transform the populated data to match what the frontend expects
    invoices = invoices.map((invoice) => {
      const plainInvoice = invoice.toObject();

      // Add name property to customer
      if (plainInvoice.customer) {
        plainInvoice.customer.name =
          plainInvoice.customer.businessName ||
          plainInvoice.customer.contactPerson ||
          "Unknown";
      }

      return plainInvoice;
    });

    // Get total count
    const total = await Invoice.countDocuments(query);

    res.json({
      invoices,
      currentPage: Number(page),
      totalPages: Math.ceil(total / limit),
      total,
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Get invoice by ID
 * @route GET /api/invoices/:id
 */
exports.getInvoice = async (req, res, next) => {
  try {
    const invoice = await Invoice.findById(req.params.id)
      .populate("customer") // Populate all customer fields
      .populate("job", "title status")
      .populate("metadata.createdBy", "firstName lastName")
      .populate("metadata.lastModifiedBy", "firstName lastName");

    if (!invoice) {
      throw new ApiError(404, "Invoice not found");
    }

    // Update AI insights if needed
    if (shouldUpdateInsights(invoice)) {
      invoice.aiInsights = {
        paymentRiskScore: 15,
        riskFactors: ["Large commercial client", "Established payment history"],
        suggestedFollowUp: "Send payment reminder 5 days before due date",
        lastUpdated: new Date(),
      };
      await invoice.save();
    }

    res.json(invoice);
  } catch (error) {
    next(error);
  }
};

/**
 * Update invoice
 * @route PUT /api/invoices/:id
 */
exports.updateInvoice = async (req, res, next) => {
  try {
    const invoice = await Invoice.findById(req.params.id);
    if (!invoice) {
      throw new ApiError(404, "Invoice not found");
    }

    // Validate update data
    const validatedData = await validateInvoiceData(req.body, true);

    // Update invoice
    Object.assign(invoice, validatedData);
    invoice.metadata.lastModifiedBy = req.user._id;

    // Add to history
    invoice.history.push({
      action: "UPDATED",
      user: req.user._id,
      date: new Date(),
      details: { changes: req.body },
    });

    await invoice.save();
    res.json(invoice);
  } catch (error) {
    next(error);
  }
};

/**
 * Record payment for invoice
 * @route POST /api/invoices/:id/payments
 */
exports.recordPayment = async (req, res, next) => {
  try {
    const invoice = await Invoice.findById(req.params.id);
    if (!invoice) {
      throw new ApiError(404, "Invoice not found");
    }

    await invoice.recordPayment({
      ...req.body,
      metadata: {
        ...req.body.metadata,
        recordedBy: req.user._id,
      },
    });

    // Generate follow-up strategy if partially paid
    if (invoice.status === "PARTIAL") {
      const followUpStrategy = await generateFollowupStrategy(invoice);
      invoice.aiInsights.suggestedFollowUp = followUpStrategy;
      await invoice.save();
    }

    res.json(invoice);
  } catch (error) {
    next(error);
  }
};

/**
 * Void invoice
 * @route POST /api/invoices/:id/void
 */
exports.voidInvoice = async (req, res, next) => {
  try {
    const invoice = await Invoice.findById(req.params.id);
    if (!invoice) {
      throw new ApiError(404, "Invoice not found");
    }

    await invoice.void(req.body.reason, req.user._id);
    res.json(invoice);
  } catch (error) {
    next(error);
  }
};

/**
 * Generate PDF for an invoice
 * @route GET /api/invoices/:id/pdf
 */
exports.generateInvoicePdf = async (req, res, next) => {
  try {
    const { id } = req.params;
    if (!mongoose.Types.ObjectId.isValid(id))
      return next(new ApiError(400, "Invalid invoice ID"));

    const invoice = await Invoice.findById(id)
      .populate(
        "customer",
        "name businessName address email phone contactPerson"
      ) // Populate necessary fields
      .populate("job", "title address")
      .populate("metadata.createdBy", "firstName lastName");

    if (!invoice) return next(new ApiError(404, "Invoice not found"));

    // Optional: Add permission check if needed
    // if (invoice.company.toString() !== req.user.company.toString()) return next(new ApiError(403, 'Access denied'));

    const doc = new PDFDocument({ margin: 50 });

    // Set headers for PDF download
    res.setHeader("Content-Type", "application/pdf");
    res.setHeader(
      "Content-Disposition",
      `attachment; filename="invoice_${invoice.number}.pdf"`
    );

    // Pipe PDF to response
    doc.pipe(res);

    // --- PDF Content ---
    const writeLine = (text, options) => doc.text(text, options).moveDown(0.5);

    // Header
    doc.fontSize(20).text(`Invoice ${invoice.number}`, { align: "center" });
    doc.moveDown();

    // Invoice Info
    doc.fontSize(10);
    writeLine(`Invoice Number: ${invoice.number}`);
    writeLine(`Date Issued: ${formatDate(invoice.issueDate)}`);
    writeLine(`Due Date: ${formatDate(invoice.dueDate)}`);
    writeLine(`Status: ${invoice.status}`);
    writeLine(
      `Created By: ${invoice.metadata?.createdBy?.firstName || ""} ${
        invoice.metadata?.createdBy?.lastName || ""
      }`.trim() || "N/A"
    );
    doc.moveDown();

    // Customer / Job Info
    const customerName =
      invoice.customer?.businessName || invoice.customer?.name || "N/A";
    doc
      .fontSize(12)
      .text("Bill To:", { continued: true })
      .font("Helvetica-Bold")
      .text(` ${customerName}`);
    if (invoice.customer?.address) {
      doc
        .font("Helvetica")
        .fontSize(10)
        .text(
          `${invoice.customer.address.street || ""}, ${
            invoice.customer.address.city || ""
          }, ${invoice.customer.address.state || ""} ${
            invoice.customer.address.zip || ""
          }`
        );
    }
    doc.moveDown(0.5);
    doc
      .fontSize(12)
      .text("Job:", { continued: true })
      .font("Helvetica-Bold")
      .text(` ${invoice.job?.title || "N/A"}`);
    if (invoice.job?.address) {
      doc
        .font("Helvetica")
        .fontSize(10)
        .text(
          `${invoice.job.address.street || ""}, ${
            invoice.job.address.city || ""
          }, ${invoice.job.address.state || ""} ${
            invoice.job.address.zip || ""
          }`
        );
    }
    doc.moveDown();

    // Items Table
    doc.fontSize(12).text("Items:", { underline: true });
    doc.moveDown(0.5);

    const tableTop = doc.y;
    const itemColWidths = [250, 50, 80, 80]; // Adjust widths: Description, Qty, Unit Price, Amount
    const itemHeaders = ["Description", "Qty", "Unit Price", "Amount"];
    const startX = doc.page.margins.left;
    const endX = doc.page.width - doc.page.margins.right;

    // Draw Table Header
    doc.font("Helvetica-Bold").fontSize(10);
    let currentX = startX;
    itemHeaders.forEach((header, i) => {
      doc.text(header, currentX, tableTop, {
        width: itemColWidths[i],
        align: i > 0 ? "right" : "left",
      });
      currentX += itemColWidths[i] + 10; // Add padding
    });
    doc.moveDown(0.5);
    const headerBottom = doc.y;
    doc
      .moveTo(startX, headerBottom)
      .lineTo(endX, headerBottom)
      .strokeColor("#aaaaaa")
      .stroke();
    doc.moveDown(0.5);

    // Draw Table Rows
    doc.font("Helvetica").fontSize(9);
    (invoice.items || []).forEach((item) => {
      const rowTop = doc.y;
      currentX = startX;
      const itemTotal = (item.quantity || 0) * (item.unitPrice || 0);

      const descHeight = doc.heightOfString(item.description || "", {
        width: itemColWidths[0],
      });
      const rowHeight = Math.max(descHeight, 15); // Minimum row height

      doc.text(item.description || "", currentX, rowTop, {
        width: itemColWidths[0],
        align: "left",
      });
      currentX += itemColWidths[0] + 10;
      doc.text((item.quantity || 0).toString(), currentX, rowTop, {
        width: itemColWidths[1],
        align: "right",
      });
      currentX += itemColWidths[1] + 10;
      doc.text(formatCurrency(item.unitPrice), currentX, rowTop, {
        width: itemColWidths[2],
        align: "right",
      });
      currentX += itemColWidths[2] + 10;
      doc.text(formatCurrency(itemTotal), currentX, rowTop, {
        width: itemColWidths[3],
        align: "right",
      });

      doc.y = rowTop + rowHeight + 5; // Add padding

      const rowBottom = doc.y;
      doc
        .moveTo(startX, rowBottom)
        .lineTo(endX, rowBottom)
        .strokeColor("#dddddd")
        .stroke();
      doc.moveDown(0.5);
    });

    // Summary Section
    const summaryX = doc.page.width - doc.page.margins.right - 150; // Position for summary
    const summaryY = doc.y > 650 ? 50 : doc.y + 20; // Move summary to next page if too low
    if (doc.y > 650) doc.addPage();
    doc.fontSize(10);
    doc.text(
      `Subtotal: ${formatCurrency(invoice.subtotal)}`,
      summaryX,
      summaryY,
      { align: "right", width: 150 }
    );
    if (invoice.discountTotal > 0) {
      doc.text(
        `Discount: -${formatCurrency(invoice.discountTotal)}`,
        summaryX,
        doc.y,
        { align: "right", width: 150 }
      );
    }
    doc.text(`Tax: ${formatCurrency(invoice.taxTotal)}`, summaryX, doc.y, {
      align: "right",
      width: 150,
    });
    doc.moveDown(0.5);
    doc.font("Helvetica-Bold").fontSize(12);
    doc.text(`Total: ${formatCurrency(invoice.total)}`, summaryX, doc.y, {
      align: "right",
      width: 150,
    });
    doc.font("Helvetica").fontSize(10); // Reset font
    doc.text(
      `Amount Paid: ${formatCurrency(invoice.amountPaid)}`,
      summaryX,
      doc.y,
      { align: "right", width: 150 }
    );
    doc.font("Helvetica-Bold").fontSize(12);
    doc.text(
      `Balance Due: ${formatCurrency(invoice.balance)}`,
      summaryX,
      doc.y,
      { align: "right", width: 150 }
    );

    // Notes & Terms
    if (invoice.notes?.customer) {
      doc.moveDown(2);
      doc.font("Helvetica").fontSize(10).text("Notes:", { underline: true });
      doc.text(invoice.notes.customer);
    }
    if (invoice.terms) {
      doc.moveDown(2);
      doc.font("Helvetica").fontSize(10).text("Terms:", { underline: true });
      doc.text(invoice.terms);
    }

    // --- End PDF Content ---

    // Finalize the PDF and end the stream
    doc.end();
  } catch (error) {
    logger.error(`Error generating PDF for invoice ${req.params.id}:`, error);
    next(new ApiError(500, "Failed to generate PDF invoice"));
  }
};

/**
 * Get payments for an invoice
 * @route GET /api/invoices/:id/payments
 */
exports.getInvoicePayments = async (req, res, next) => {
  try {
    const invoice = await Invoice.findById(req.params.id).select("payments"); // Select only the payments field

    if (!invoice) {
      throw new ApiError(404, "Invoice not found");
    }

    // Optional: Add permission check if needed

    res.json({ success: true, data: invoice.payments });
  } catch (error) {
    next(error);
  }
};

/**
 * Delete invoice by ID
 * @route DELETE /api/invoices/:id
 */
exports.deleteInvoice = async (req, res, next) => {
  try {
    const invoice = await Invoice.findById(req.params.id);

    if (!invoice) {
      // Use return next() for consistency and to stop execution
      return next(new ApiError(404, "Invoice not found"));
    }

    // Optional: Add logic here to check permissions or invoice status before deleting
    // For example, prevent deletion of PAID invoices?
    // if (invoice.status === 'PAID') {
    //   return next(new ApiError(400, 'Cannot delete a paid invoice.'));
    // }

    await invoice.remove(); // Mongoose remove method

    // Optional: Remove invoice reference from the associated Job
    // Consider if this should be handled via middleware or model hooks instead
    // if (invoice.job) {
    //   await Job.findByIdAndUpdate(invoice.job, { $pull: { invoices: invoice._id } });
    // }

    res
      .status(200)
      .json({ success: true, message: "Invoice deleted successfully" }); // Use 200 OK for successful delete
  } catch (error) {
    logger.error(`Error deleting invoice ${req.params.id}:`, error); // Add logging
    next(error); // Pass error to centralized handler
  }
};

/**
 * Generate PDF for an invoice
 * @route GET /api/invoices/:id/pdf
 */
exports.generateInvoicePdf = async (req, res, next) => {
  try {
    const { id } = req.params;
    if (!mongoose.Types.ObjectId.isValid(id))
      return next(new ApiError(400, "Invalid invoice ID"));

    const invoice = await Invoice.findById(id)
      .populate(
        "customer",
        "name businessName address email phone contactPerson"
      ) // Populate necessary fields
      .populate("job", "title address")
      .populate("metadata.createdBy", "firstName lastName");

    if (!invoice) return next(new ApiError(404, "Invoice not found"));

    // Optional: Add permission check if needed
    // if (invoice.company.toString() !== req.user.company.toString()) return next(new ApiError(403, 'Access denied'));

    const doc = new PDFDocument({ margin: 50 });

    // Set headers for PDF download
    res.setHeader("Content-Type", "application/pdf");
    res.setHeader(
      "Content-Disposition",
      `attachment; filename="invoice_${invoice.number}.pdf"`
    );

    // Pipe PDF to response
    doc.pipe(res);

    // --- PDF Content ---
    const writeLine = (text, options) => doc.text(text, options).moveDown(0.5);

    // Header
    doc.fontSize(20).text(`Invoice ${invoice.number}`, { align: "center" });
    doc.moveDown();

    // Invoice Info
    doc.fontSize(10);
    writeLine(`Invoice Number: ${invoice.number}`);
    writeLine(`Date Issued: ${formatDate(invoice.issueDate)}`);
    writeLine(`Due Date: ${formatDate(invoice.dueDate)}`);
    writeLine(`Status: ${invoice.status}`);
    writeLine(
      `Created By: ${invoice.metadata?.createdBy?.firstName || ""} ${
        invoice.metadata?.createdBy?.lastName || ""
      }`.trim() || "N/A"
    );
    doc.moveDown();

    // Customer / Job Info
    const customerName =
      invoice.customer?.businessName || invoice.customer?.name || "N/A";
    doc
      .fontSize(12)
      .text("Bill To:", { continued: true })
      .font("Helvetica-Bold")
      .text(` ${customerName}`);
    if (invoice.customer?.address) {
      doc
        .font("Helvetica")
        .fontSize(10)
        .text(
          `${invoice.customer.address.street || ""}, ${
            invoice.customer.address.city || ""
          }, ${invoice.customer.address.state || ""} ${
            invoice.customer.address.zip || ""
          }`
        );
    }
    doc.moveDown(0.5);
    doc
      .fontSize(12)
      .text("Job:", { continued: true })
      .font("Helvetica-Bold")
      .text(` ${invoice.job?.title || "N/A"}`);
    if (invoice.job?.address) {
      doc
        .font("Helvetica")
        .fontSize(10)
        .text(
          `${invoice.job.address.street || ""}, ${
            invoice.job.address.city || ""
          }, ${invoice.job.address.state || ""} ${
            invoice.job.address.zip || ""
          }`
        );
    }
    doc.moveDown();

    // Items Table
    doc.fontSize(12).text("Items:", { underline: true });
    doc.moveDown(0.5);

    const tableTop = doc.y;
    const itemColWidths = [250, 50, 80, 80]; // Adjust widths: Description, Qty, Unit Price, Amount
    const itemHeaders = ["Description", "Qty", "Unit Price", "Amount"];
    const startX = doc.page.margins.left;
    const endX = doc.page.width - doc.page.margins.right;

    // Draw Table Header
    doc.font("Helvetica-Bold").fontSize(10);
    let currentX = startX;
    itemHeaders.forEach((header, i) => {
      doc.text(header, currentX, tableTop, {
        width: itemColWidths[i],
        align: i > 0 ? "right" : "left",
      });
      currentX += itemColWidths[i] + 10; // Add padding
    });
    doc.moveDown(0.5);
    const headerBottom = doc.y;
    doc
      .moveTo(startX, headerBottom)
      .lineTo(endX, headerBottom)
      .strokeColor("#aaaaaa")
      .stroke();
    doc.moveDown(0.5);

    // Draw Table Rows
    doc.font("Helvetica").fontSize(9);
    (invoice.items || []).forEach((item) => {
      const rowTop = doc.y;
      currentX = startX;
      const itemTotal = (item.quantity || 0) * (item.unitPrice || 0);

      const descHeight = doc.heightOfString(item.description || "", {
        width: itemColWidths[0],
      });
      const rowHeight = Math.max(descHeight, 15); // Minimum row height

      doc.text(item.description || "", currentX, rowTop, {
        width: itemColWidths[0],
        align: "left",
      });
      currentX += itemColWidths[0] + 10;
      doc.text((item.quantity || 0).toString(), currentX, rowTop, {
        width: itemColWidths[1],
        align: "right",
      });
      currentX += itemColWidths[1] + 10;
      doc.text(formatCurrency(item.unitPrice), currentX, rowTop, {
        width: itemColWidths[2],
        align: "right",
      });
      currentX += itemColWidths[2] + 10;
      doc.text(formatCurrency(itemTotal), currentX, rowTop, {
        width: itemColWidths[3],
        align: "right",
      });

      doc.y = rowTop + rowHeight + 5; // Add padding

      const rowBottom = doc.y;
      doc
        .moveTo(startX, rowBottom)
        .lineTo(endX, rowBottom)
        .strokeColor("#dddddd")
        .stroke();
      doc.moveDown(0.5);
    });

    // Summary Section
    const summaryX = doc.page.width - doc.page.margins.right - 150; // Position for summary
    const summaryY = doc.y > 650 ? 50 : doc.y + 20; // Move summary to next page if too low
    if (doc.y > 650) doc.addPage();
    doc.fontSize(10);
    doc.text(
      `Subtotal: ${formatCurrency(invoice.subtotal)}`,
      summaryX,
      summaryY,
      { align: "right", width: 150 }
    );
    if (invoice.discountTotal > 0) {
      doc.text(
        `Discount: -${formatCurrency(invoice.discountTotal)}`,
        summaryX,
        doc.y,
        { align: "right", width: 150 }
      );
    }
    doc.text(`Tax: ${formatCurrency(invoice.taxTotal)}`, summaryX, doc.y, {
      align: "right",
      width: 150,
    });
    doc.moveDown(0.5);
    doc.font("Helvetica-Bold").fontSize(12);
    doc.text(`Total: ${formatCurrency(invoice.total)}`, summaryX, doc.y, {
      align: "right",
      width: 150,
    });
    doc.font("Helvetica").fontSize(10); // Reset font
    doc.text(
      `Amount Paid: ${formatCurrency(invoice.amountPaid)}`,
      summaryX,
      doc.y,
      { align: "right", width: 150 }
    );
    doc.font("Helvetica-Bold").fontSize(12);
    doc.text(
      `Balance Due: ${formatCurrency(invoice.balance)}`,
      summaryX,
      doc.y,
      { align: "right", width: 150 }
    );

    // Notes & Terms
    if (invoice.notes?.customer) {
      doc.moveDown(2);
      doc.font("Helvetica").fontSize(10).text("Notes:", { underline: true });
      doc.text(invoice.notes.customer);
    }
    if (invoice.terms) {
      doc.moveDown(2);
      doc.font("Helvetica").fontSize(10).text("Terms:", { underline: true });
      doc.text(invoice.terms);
    }

    // --- End PDF Content ---

    // Finalize the PDF and end the stream
    doc.end();
  } catch (error) {
    logger.error(`Error generating PDF for invoice ${req.params.id}:`, error);
    next(new ApiError(500, "Failed to generate PDF invoice"));
  }
};

/**
 * Get invoice analytics
 * @route GET /api/invoices/analytics
 */
exports.getAnalytics = async (req, res, next) => {
  try {
    const { startDate, endDate } = req.query;

    const dateRange = {};
    if (startDate) dateRange.$gte = new Date(startDate);
    if (endDate) dateRange.$lte = new Date(endDate);

    const [totalStats, statusStats, dailyStats] = await Promise.all([
      // Total statistics
      Invoice.aggregate([
        { $match: { issueDate: dateRange } },
        {
          $group: {
            _id: null,
            totalAmount: { $sum: "$total" },
            totalPaid: { $sum: "$amountPaid" },
            count: { $sum: 1 },
            averageAmount: { $avg: "$total" },
          },
        },
      ]),

      // Status breakdown
      Invoice.aggregate([
        { $match: { issueDate: dateRange } },
        {
          $group: {
            _id: "$status",
            count: { $sum: 1 },
            amount: { $sum: "$total" },
          },
        },
      ]),

      // Daily statistics
      Invoice.aggregate([
        { $match: { issueDate: dateRange } },
        {
          $group: {
            _id: { $dateToString: { format: "%Y-%m-%d", date: "$issueDate" } },
            amount: { $sum: "$total" },
            count: { $sum: 1 },
          },
        },
        { $sort: { _id: 1 } },
      ]),
    ]);

    res.json({
      totalStats: totalStats[0],
      statusStats,
      dailyStats,
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Create invoice from an existing quote
 * @route POST /api/invoices/from-quote/:quoteId
 */
exports.createInvoiceFromQuote = async (req, res, next) => {
  try {
    const { quoteId } = req.params;
    const userId = req.user._id;

    // Validate quoteId
    if (!mongoose.Types.ObjectId.isValid(quoteId)) {
      throw new ApiError(400, "Invalid Quote ID");
    }

    // Find the quote
    const quote = await Quote.findById(quoteId); // Use renamed model
    if (!quote) {
      throw new ApiError(404, "Quote not found");
    }

    // Check if quote belongs to the user's company (or implement proper authorization)
    // if (quote.company.toString() !== req.user.company.toString()) {
    //   throw new ApiError(403, 'Unauthorized to convert this quote');
    // }

    // Check if quote is in an approved status (or suitable status for conversion)
    if (quote.status !== "APPROVED") {
      throw new ApiError(
        400,
        "Quote must be approved before converting to invoice"
      );
    }

    // Use the static method on the Invoice model
    const invoice = await Invoice.createFromQuote(quoteId, userId);

    res.status(201).json(invoice);
  } catch (error) {
    console.error("Error creating invoice from quote:", error);
    next(error);
  }
};
/**
 * Internal method for creating an invoice (used by batch route)
 * This is a helper method that doesn't directly handle HTTP requests
 */
exports.createInvoiceInternal = async (invoiceData, user) => {
  const { jobId, ...restData } = invoiceData;

  try {
    // Add job field for validation
    const dataToValidate = {
      ...restData,
      job: jobId,
    };

    // Validate invoice data
    const validatedData = await validateInvoiceData(dataToValidate);

    // Get job details
    let job = await Job.findById(jobId).populate("customer");
    if (!job) {
      console.error(`Job not found with ID: ${jobId}`);

      // Try using the job ID from the validated data
      let altJobId = validatedData.job;

      if (altJobId && altJobId !== jobId) {
        console.log(`Trying alternative job ID: ${altJobId}`);
        job = await Job.findById(altJobId).populate("customer");
      }

      if (!job) {
        // If we have a customer ID but no job, create a "virtual" job reference
        if (validatedData.customer) {
          const customer = await require("../models/Customer").findById(
            validatedData.customer
          );
          if (customer) {
            console.log(
              `Creating virtual job reference for customer: ${customer._id}`
            );

            // Create a valid MongoDB ObjectId for a "virtual" job
            const virtualJobId = new mongoose.Types.ObjectId();
            console.log(`Generated virtual job ID: ${virtualJobId}`);

            job = {
              _id: virtualJobId,
              customer: customer,
              title: "Invoice without job reference",
              virtual: true, // Mark this as a virtual job
            };
          } else {
            throw new ApiError(404, "Neither job nor customer found");
          }
        } else {
          throw new ApiError(404, "Job not found and no customer ID provided");
        }
      }
    }

    // Generate invoice number
    const invoiceNumber = await generateInvoiceNumber();

    // Analyze payment risk using AI
    const aiInsights = {
      paymentRiskScore: 15,
      riskFactors: ["Large commercial client", "Established payment history"],
      suggestedFollowUp: "Send payment reminder 5 days before due date",
      lastUpdated: new Date(),
    };

    // Prepare the invoice data
    const invoiceCreateData = {
      ...validatedData,
      number: invoiceNumber,
      customer: job.customer._id,
      aiInsights,
      metadata: {
        createdBy: user._id,
        lastModifiedBy: user._id,
      },
    };

    // Ensure the job ID is set properly for MongoDB
    const jobId_str = job._id.toString();
    invoiceCreateData.job = jobId_str;
    console.log(`Using job ID for internal invoice: ${jobId_str}`);

    // Create invoice
    console.log("Creating internal invoice with job and customer:", {
      job: invoiceCreateData.job,
      customer: invoiceCreateData.customer,
    });

    const invoice = await Invoice.create(invoiceCreateData);
    console.log(
      `Internal invoice created successfully with ID: ${invoice._id}`
    );

    // Update job with invoice reference only if it's a real job (not a virtual one)
    if (
      job &&
      !job.virtual &&
      typeof job.constructor === "function" &&
      job.constructor.name === "model"
    ) {
      try {
        console.log(`Updating job ${job._id} with invoice reference`);
        await Job.findByIdAndUpdate(job._id, {
          $push: { invoices: invoice._id },
          $set: { "metadata.lastModifiedBy": user._id },
        });
      } catch (jobUpdateError) {
        console.error(
          "Failed to update job with invoice reference:",
          jobUpdateError
        );
        // Continue execution despite this error - the invoice is already created
      }
    } else {
      console.log("Skipping job update as this is a virtual job reference");
    }

    // Send invoice email if not in draft status
    if (invoice.status !== "DRAFT") {
      await sendInvoiceEmail(invoice, job.customer);
    }

    return invoice;
  } catch (error) {
    console.error("Internal invoice creation error:", error);
    throw error;
  }
};

// Helper function to determine if AI insights need updating
function shouldUpdateInsights(invoice) {
  // Update if insights are older than 24 hours or don't exist
  // Added safe navigation and getTime() for potential null/undefined lastUpdated
  return (
    !invoice.aiInsights ||
    Date.now() - (invoice.aiInsights.lastUpdated?.getTime() || 0) >
      24 * 60 * 60 * 1000
  );
}

// Export all functions including the new ones
module.exports = {
  createInvoice: exports.createInvoice,
  getInvoices: exports.getInvoices,
  getInvoice: exports.getInvoice,
  updateInvoice: exports.updateInvoice,
  deleteInvoice: exports.deleteInvoice, // Added export
  recordPayment: exports.recordPayment,
  getInvoicePayments: exports.getInvoicePayments, // Added export
  voidInvoice: exports.voidInvoice,
  getAnalytics: exports.getAnalytics,
  createInvoiceFromQuote: exports.createInvoiceFromQuote,
  createInvoiceInternal: exports.createInvoiceInternal,
  generateInvoicePdf: exports.generateInvoicePdf, // Added export
  // Assuming analyzeInvoice and generateFollowUpStrategy are internal or called by other exported functions
};
