import React, { useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import {
  Box,
  Container,
  Grid,
  Paper,
  Typography,
  CircularProgress,
  Alert,
} from "@mui/material";
import CalendarTodayIcon from "@mui/icons-material/CalendarToday";
import PeopleIcon from "@mui/icons-material/People";
import AssignmentIcon from "@mui/icons-material/Assignment";
import ReceiptIcon from "@mui/icons-material/Receipt";
import WarningIcon from "@mui/icons-material/Warning";

// Import dashboard components
import MetricCard from "../components/dashboard/MetricCard";
import QuickActionCard from "../components/dashboard/QuickActionCard";

// Import dashboard action
import {
  getDashboardMetrics,
  resetDashboardState,
} from "../slices/dashboardSlice";

const Dashboard = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch();

  // Get dashboard metrics from Redux store
  const { metrics, loading, error } = useSelector((state) => state.dashboard);

  useEffect(() => {
    dispatch(resetDashboardState());
    dispatch(getDashboardMetrics());
  }, [dispatch]);

  return (
    <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
      <Typography variant="h4" component="h1" gutterBottom>
        Dashboard
      </Typography>

      {/* Error alert */}
      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {/* Metrics Section */}
      {loading ? (
        <Box sx={{ display: "flex", justifyContent: "center", my: 5 }}>
          <CircularProgress />
        </Box>
      ) : (
        <Grid container spacing={3} sx={{ mb: 4 }}>
          <Grid item xs={12} sm={6} md={4}>
            <MetricCard
              title="Open Jobs"
              value={Number(metrics.openJobs || 0)}
              icon={AssignmentIcon}
              color="primary"
            />
          </Grid>
          <Grid item xs={12} sm={6} md={4}>
            <MetricCard
              title="Total Customers"
              value={Number(metrics.totalCustomers || 0)}
              icon={PeopleIcon}
              color="secondary"
            />
          </Grid>
          <Grid item xs={12} sm={6} md={4}>
            <MetricCard
              title="Outstanding Invoices"
              value={`$${Number(
                metrics.outstandingInvoices || 0
              ).toLocaleString()}`}
              icon={ReceiptIcon}
              color="error"
            />
          </Grid>
          <Grid item xs={12} sm={6} md={6}>
            <MetricCard
              title="Upcoming Events (7 days)"
              value={Number(metrics.upcomingEvents || 0)}
              icon={CalendarTodayIcon}
              color="info"
            />
          </Grid>
          <Grid item xs={12} sm={6} md={6}>
            <MetricCard
              title="Low Stock Items"
              value={Number(metrics.lowStockItems || 0)}
              icon={WarningIcon}
              color="warning"
            />
          </Grid>
        </Grid>
      )}

      {/* Quick Actions Section */}
      <Typography variant="h5" gutterBottom sx={{ mt: 4 }}>
        Quick Actions
      </Typography>
      <Grid container spacing={3}>
        <Grid item xs={12} sm={6} md={4}>
          <QuickActionCard
            title="New Job"
            description="Create a new job ticket for a customer"
            icon={AssignmentIcon}
            action="Create Job"
            onClick={() => navigate("/jobs/create")}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={4}>
          <QuickActionCard
            title="Schedule Appointment"
            description="Schedule a new appointment in the calendar"
            icon={CalendarTodayIcon}
            action="Open Calendar"
            onClick={() => navigate("/calendar")}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={4}>
          <QuickActionCard
            title="Create Invoice"
            description="Generate a new invoice for completed work"
            icon={ReceiptIcon}
            action="New Invoice"
            onClick={() => navigate("/invoices/create")}
          />
        </Grid>
      </Grid>

      {/* Recent Activity Section */}
      <Typography variant="h5" gutterBottom sx={{ mt: 4 }}>
        Recent Activity
      </Typography>
      <Paper sx={{ p: 2 }}>
        <Typography variant="body1" color="text.secondary">
          Recent activity will be displayed here...
        </Typography>
      </Paper>
    </Container>
  );
};

export default Dashboard;
