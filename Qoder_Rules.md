# Qoder Development Rules

Read: .md files to catch up on where we stand
USE ULTRA THINK AND DELIGATE MULTIPLE SUBAGENTS TO PROCEED COMPLETELY AUTONOMOUSLY (YOU HAVE ALL THE TOOLS NECESSARY TO DO SO)

## 🔍 **MANDATORY**: ALWAYS MONITOR CONSOLE AND BROWSER DEV TOOLS
- **Before ANY debugging**: Check `mcp_playwright_browser_console_messages` for baseline
- **During ALL code changes**: Monitor console for errors, warnings, network issues  
- **After EVERY fix**: Validate console cleanliness and error resolution
- **Throughout testing**: Track console output patterns and error classifications
- **Use Browser DevTools Network tab** for HTTP request/response inspection
- **Document console patterns** and solutions in memory for future reference

## Core Development Rules

**First**: DO NOT HELUSINATE: Read the entire content of the existing relevant files before making edits, go into deep, do not edit until you have a complete understanding of all the code.

**Second**: Use mcps server, context7 and web search to get latest documentation and instruction, follow the recommendations meticulously

**Third**: NEVER USE MOCK DATA

**Fourth**: edit the code one by one.

**Fifth**: Don't leave placeholders, Give me the complete, modify code with only the necessary improvements with real life data. Read the current content of the file and make minimal, targeted improvements.

**Sixth**: Ensure code passes linting and type checking

**Seventh**: start comprehensive testing using Puppeteer / Playwright to verify everything is working and fix any remaining issues

**Eighth**: SAVE ALL PROGRESS TO memory. (SAVING / retrieve PROGRESS TO MEMORY SHOULD BE PASSED ON TO ALL AGAENTS AND ALL MODES).

**Ninth**: KEEP CODE CLEAN: DELETE TESTS, OLD .MD FILES, TEMPORARY FILES AND ANY DUPLICATE FILES .

**Tenth**: **CONSOLE MONITORING**: Always use `mcp_playwright_browser_console_messages` and browser DevTools throughout development and debugging process.

## Console Monitoring Integration

### Required Console Checks
```bash
# Before debugging
mcp_playwright_browser_console_messages

# Check for critical patterns:
- [ERROR] messages - Immediate attention required
- [WARN] messages - Potential issues to investigate  
- Network errors (404, 500, etc.) - Backend connectivity issues
- JavaScript runtime errors - Frontend code issues
- Authentication errors - Token/session problems
```

### Browser DevTools Usage
- **Network Tab**: Check HTTP requests, response codes, payload inspection
- **Console Tab**: Monitor JavaScript errors, warnings, and debug logs
- **Application Tab**: Verify localStorage, sessionStorage, service worker states
- **Performance Tab**: Track loading times and memory usage

### Documentation Requirement
Always document console findings and patterns in memory for future debugging sessions.