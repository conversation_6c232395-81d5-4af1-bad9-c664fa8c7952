import React, { useState } from "react";
import {
  Box,
  TextField,
  IconButton,
  Chip,
  Paper,
  Typography,
  CircularProgress,
  Tooltip,
  Collapse,
  Alert, // Import Alert
} from "@mui/material";
import {
  Search as SearchIcon,
  Psychology as AiIcon,
  Close as CloseIcon,
  ExpandMore as ExpandMoreIcon,
  ExpandLess as ExpandLessIcon,
  ErrorOutline as ErrorIcon, // Import ErrorIcon
} from "@mui/icons-material";
import aiService from "../utils/aiService";
import logger from "../utils/logger"; // Assuming logger exists

// Basic sanitization helper (similar to backend)
function sanitizeForPrompt(input) {
  if (typeof input !== "string") return input;
  return input.replace(/[`[\]{}<>]/g, "");
}

/**
 * AI-powered customer filtering component
 * Provides natural language search and smart categorization
 */
const AiCustomerFilter = ({ customers, onFilteredResultsChange }) => {
  const [query, setQuery] = useState("");
  const [isProcessing, setIsProcessing] = useState(false);
  const [showAdvanced, setShowAdvanced] = useState(false);
  const [activeSuggestions, setActiveSuggestions] = useState([]);
  const [filterError, setFilterError] = useState(null); // State for errors

  // Smart filter suggestions - these could be dynamically generated in the future
  const smartSuggestions = [
    {
      id: 1,
      label: "Recent customers",
      description: "Added in the last 30 days",
    },
    {
      id: 2,
      label: "High value",
      description: "Customers with high total spend",
    },
    { id: 3, label: "Commercial", description: "Business customers" },
    { id: 4, label: "Residential", description: "Individual homeowners" },
    { id: 5, label: "Regular", description: "Recurring customers" },
    {
      id: 6,
      label: "New leads",
      description: "Customers with no completed jobs",
    },
  ];

  // Handle natural language search using AI service
  const handleSearch = async () => {
    const trimmedQuery = query.trim();
    if (!trimmedQuery) {
      onFilteredResultsChange(null); // Pass null to indicate no filtering
      setFilterError(null); // Clear error on empty query
      return;
    }

    setIsProcessing(true);
    setFilterError(null); // Clear previous errors
    const sanitizedQuery = sanitizeForPrompt(trimmedQuery); // Sanitize query

    try {
      // Use the real AI service to filter customers
      const results = await aiService.filterCustomersByAI(sanitizedQuery); // Pass sanitized query
      onFilteredResultsChange(results); // Pass results (customer list from backend)
    } catch (error) {
      const errorMsg =
        error.response?.data?.message || error.message || "AI filter failed";
      logger.error("Error during AI customer filtering:", {
        message: errorMsg,
        query: sanitizedQuery,
      });
      setFilterError(errorMsg); // Set error state
      onFilteredResultsChange(null); // Indicate filtering failed / show all
    } finally {
      setIsProcessing(false);
    }
  };

  // Handle when Enter key is pressed
  const handleKeyPress = (e) => {
    if (e.key === "Enter") {
      handleSearch();
    }
  };

  // Handle suggestion click
  const handleSuggestionClick = (suggestion) => {
    const isActive = activeSuggestions.some((s) => s.id === suggestion.id);
    let newActiveSuggestions;
    if (isActive) {
      newActiveSuggestions = activeSuggestions.filter(
        (s) => s.id !== suggestion.id
      );
    } else {
      newActiveSuggestions = [...activeSuggestions, suggestion];
    }
    setActiveSuggestions(newActiveSuggestions);
    applySmartFilters(newActiveSuggestions); // Apply filters when suggestions change
  };

  // Apply smart filters based on active suggestions
  const applySmartFilters = async (activeFilters) => {
    if (activeFilters.length === 0) {
      onFilteredResultsChange(null); // Clear filter
      setFilterError(null);
      return;
    }

    setIsProcessing(true);
    setFilterError(null); // Clear previous errors

    try {
      // Convert filter suggestions to a natural language query
      const filterQuery = activeFilters
        .map((filter) => filter.label)
        .join(" and ");

      const sanitizedFilterQuery = sanitizeForPrompt(filterQuery); // Sanitize generated query

      // Use the AI service to filter customers based on the generated query
      const results = await aiService.filterCustomersByAI(sanitizedFilterQuery); // Pass sanitized query
      onFilteredResultsChange(results); // Pass results
    } catch (error) {
      const errorMsg =
        error.response?.data?.message ||
        error.message ||
        "AI smart filter failed";
      logger.error("Error applying smart filters:", {
        message: errorMsg,
        activeFilters,
      });
      setFilterError(errorMsg); // Set error state
      onFilteredResultsChange(null); // Indicate filtering failed / show all
    } finally {
      setIsProcessing(false);
    }
  };

  // Clear all filters
  const clearFilters = () => {
    setQuery("");
    setActiveSuggestions([]);
    setFilterError(null);
    onFilteredResultsChange(null); // Clear filtering in parent
  };

  return (
    <Paper
      elevation={2}
      sx={{
        p: 2,
        mb: 3,
        position: "relative",
        border: "1px solid #e0e0e0",
        borderRadius: 2,
      }}
    >
      <Box sx={{ display: "flex", alignItems: "center", mb: 1 }}>
        <AiIcon color="primary" sx={{ mr: 1 }} />
        <Typography variant="h6" component="h2">
          AI Customer Filter
        </Typography>
      </Box>

      <Box sx={{ display: "flex", alignItems: "center", mb: 2 }}>
        <TextField
          fullWidth
          variant="outlined"
          placeholder="Use natural language to search (e.g., 'find all commercial customers in New York')"
          value={query}
          onChange={(e) => setQuery(e.target.value)}
          onKeyPress={handleKeyPress}
          InputProps={{
            startAdornment: <SearchIcon color="action" sx={{ mr: 1 }} />,
            endAdornment: isProcessing ? (
              <CircularProgress size={24} color="primary" />
            ) : query ? (
              <IconButton size="small" onClick={clearFilters}>
                <CloseIcon />
              </IconButton>
            ) : null,
          }}
          disabled={isProcessing}
        />
        <IconButton
          color="primary"
          onClick={handleSearch}
          disabled={isProcessing || !query.trim()}
          sx={{ ml: 1 }}
        >
          <SearchIcon />
        </IconButton>
      </Box>

      {/* Active filters */}
      {activeSuggestions.length > 0 && (
        <Box sx={{ display: "flex", flexWrap: "wrap", gap: 1, mb: 2 }}>
          {activeSuggestions.map((suggestion) => (
            <Chip
              key={suggestion.id}
              label={suggestion.label}
              onDelete={() => handleSuggestionClick(suggestion)} // Allows removing by clicking 'x'
              color="primary"
            />
          ))}
          {activeSuggestions.length > 0 && (
            <Chip
              label="Clear All"
              onClick={clearFilters}
              variant="outlined"
              color="secondary"
              size="small" // Make clear button less prominent
              clickable
            />
          )}
        </Box>
      )}

      {/* Smart filter suggestions */}
      <Box sx={{ mb: 1 }}>
        <Box
          sx={{
            display: "flex",
            alignItems: "center",
            cursor: "pointer",
          }}
          onClick={() => setShowAdvanced(!showAdvanced)}
        >
          <Typography variant="subtitle2" color="primary">
            Smart Filters
          </Typography>
          {showAdvanced ? (
            <ExpandLessIcon fontSize="small" />
          ) : (
            <ExpandMoreIcon fontSize="small" />
          )}
        </Box>

        <Collapse in={showAdvanced}>
          <Box sx={{ display: "flex", flexWrap: "wrap", gap: 1, mt: 2 }}>
            {smartSuggestions.map((suggestion) => (
              <Tooltip key={suggestion.id} title={suggestion.description} arrow>
                <Chip
                  label={suggestion.label}
                  onClick={() => handleSuggestionClick(suggestion)}
                  color={
                    activeSuggestions.some((s) => s.id === suggestion.id)
                      ? "primary"
                      : "default"
                  }
                  variant={
                    activeSuggestions.some((s) => s.id === suggestion.id)
                      ? "filled"
                      : "outlined"
                  }
                  clickable
                />
              </Tooltip>
            ))}
          </Box>
        </Collapse>
      </Box>

      {/* Display Error if any */}
      {filterError && (
        <Alert
          severity="error"
          icon={<ErrorIcon fontSize="inherit" />}
          sx={{ mt: 2 }}
        >
          {filterError}
        </Alert>
      )}

      {/* Removed Results summary - Parent component handles display */}
    </Paper>
  );
};

export default AiCustomerFilter;
