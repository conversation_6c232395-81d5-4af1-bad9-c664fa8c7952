module.exports = {
  testEnvironment: "node",
  testMatch: ["**/__tests__/**/*.js", "**/?(*.)+(spec|test).js"],
  collectCoverage: true,
  coverageDirectory: "coverage",
  collectCoverageFrom: [
    "controllers/**/*.js",
    "models/**/*.js",
    "middleware/**/*.js",
    "!**/node_modules/**",
    "!**/vendor/**",
  ],
  coverageReporters: ["text", "lcov", "clover", "html"],
  verbose: true,
  testPathIgnorePatterns: ["/node_modules/"],
  setupFilesAfterEnv: ["./tests/setupTests.js"],
  watchPlugins: [
    // 'jest-watch-typeahead/filename', // Removed problematic plugin
    // 'jest-watch-typeahead/testname'  // Removed problematic plugin
  ],
};
