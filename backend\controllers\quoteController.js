/**
 * quoteController.js
 * Controller for quote API
 */

const PDFDocument = require("pdfkit");
const { format } = require("date-fns");
const mongoose = require("mongoose");
const ApiError = require("../utils/ApiError");
const logger = require("../utils/logger");
const parserUtils = require("../utils/parserUtils"); // Import the new parser utility
const scraperService = require("../scrapers/ScraperService"); // Import ScraperService
const genericPriceLookupService = require("../services/genericPriceLookupService"); // Import Generic Price Lookup
const MaterialSource = require("../models/MaterialSource");
const MaterialPriceHistory = require("../models/MaterialPriceHistory");
const PriceScrapingLog = require("../models/PriceScrapingLog");
const Quote = require("../models/Quote");
const Job = require("../models/Job"); // Import Job model
const Customer = require("../models/Customer"); // Import Customer model
const { sendQuoteEmail } = require("../services/emailService");
const electricalCodeLookupService = require("../services/electricalCodeLookupService");
const priceLookupService = require("../services/priceLookupService"); // Added for internal price lookup
const fs = require("fs").promises; // Use promises version of fs
const path = require("path");
const sharp = require("sharp");

const Company = require("../models/Company");
const priceCache = require("../utils/priceCache");
const {
  transformItemsForFrontend,
  transformItemsForBackend,
} = require("../utils/itemDataTransformer");
/**
 * Helper function to format currency
 * @param {number} amount - Amount to format
 * @returns {string} - Formatted currency string
 */
const formatCurrency = (amount) => {
  if (typeof amount !== "number") {
    return "$0.00";
  }
  return `$${amount.toFixed(2)}`;
};

/**
 * Helper function to format date
 * @param {Date} date - Date object
 * @returns {string} - Formatted date string
 */
const formatDate = (date) => {
  if (!date) return "N/A";
  try {
    const d = new Date(date);
    if (isNaN(d.getTime())) return "Invalid Date";
    return format(d, "MM/dd/yyyy");
  } catch (e) {
    return "Invalid Date";
  }
};

/**
 * Helper function to get Company ObjectId from user
 */
const getCompanyIdFromUser = async (user) => {
  if (!user || !user.company) {
    throw new ApiError(400, "User is not associated with a company.");
  }

  // Check if user.company is already an ObjectId or a string that looks like an ObjectId
  if (mongoose.Types.ObjectId.isValid(user.company)) {
    // If it's a valid ObjectId, find by _id
    const companyDoc = await Company.findById(user.company);
    if (!companyDoc) {
      logger.error(
        `Company not found for ID: '${user.company}' associated with user ${user.id}.`
      );
      throw new ApiError(404, `Company '${user.company}' not found.`);
    }
    return companyDoc._id;
  } else {
    // If it's a string, find by name (legacy support)
    const companyDoc = await Company.findOne({
      name: new RegExp("^" + user.company + "$", "i"),
    });
    if (!companyDoc) {
      logger.error(
        `Company not found for name: '${user.company}' associated with user ${user.id}.`
      );
      throw new ApiError(404, `Company '${user.company}' not found.`);
    }
    return companyDoc._id;
  }
};

/**
 * Controller for quotes
 */
const quoteController = {
  /**
   * Search for materials by description using Firecrawl
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next middleware function
   * @returns {Promise<void>}
   */
  async searchMaterials(req, res, next) {
    logger.debug("[Controller Entry] Entering searchMaterials"); // ADDED DEBUG LOG
    // Wrap entire function logic in try...catch for debugging
    try {
      const { query, sourceId, skipCache, limit = 10 } = req.query;
      const userId = req.user?.id;
      const sanitizedQuery = query.replace(/\s+/g, "_"); // Sanitize spaces
      const cacheKey = `material_search:${sanitizedQuery}:${sourceId || "all"}`;
      logger.debug(`[Cache] Using sanitized cache key: ${cacheKey}`); // Log the key

      if (!query) {
        return next(new ApiError(400, "Search query is required"));
      }

      const options = {
        sourceId: sourceId || undefined,
        skipCache: true, // Temporarily force skipCache to true for debugging
        limit: parseInt(limit, 10),
      };

      logger.info(`Material search request for "${query}"`, {
        userId,
        options,
      });

      // 1. Check Cache
      if (!options.skipCache) {
        try {
          const cachedResults = await priceCache.get(cacheKey);
          if (cachedResults) {
            logger.info(`Cache hit for material search "${query}"`);
            return res.json({
              success: true,
              count: cachedResults.length,
              data: cachedResults,
              source: "cache",
            });
          }
          logger.info(`Cache miss for material search "${query}"`);
        } catch (cacheError) {
          logger.error(
            `Redis cache GET error for key ${cacheKey}:`,
            cacheError
          );
          // Continue even if cache check fails
        }
      }

      // Ensure ScraperService is initialized
      await scraperService.ensureInitialized();

      const refinedQuery = query; // Placeholder for potential future LLM refinement

      logger.info(
        `[Scraper Service Search] Calling scraperService.searchMaterials for query: "${refinedQuery}"`
      );

      // Service now might return { results: [], sourceStatus: {...} } for Platt blocked state
      const serviceResponse = await scraperService.searchMaterials(
        refinedQuery,
        options
      );

      let results = [];
      let sourceStatus = null;

      // Check if the response includes sourceStatus (indicating Platt block or similar)
      if (
        serviceResponse &&
        typeof serviceResponse === "object" &&
        serviceResponse.sourceStatus
      ) {
        results = serviceResponse.results || []; // Should be empty in blocked case
        sourceStatus = serviceResponse.sourceStatus;
        logger.warn(
          `[Scraper Service Search] Search for "${refinedQuery}" encountered source status: ${sourceStatus.status} - ${sourceStatus.reason}`
        );
      } else if (Array.isArray(serviceResponse)) {
        // Normal successful response
        results = serviceResponse;
      } else {
        logger.error(
          `[Scraper Service Search] Unexpected response format from scraperService.searchMaterials: ${JSON.stringify(
            serviceResponse
          )}`
        );
        // Handle unexpected format, maybe return empty or throw error? Returning empty for now.
        results = [];
      }

      logger.info(
        `[Scraper Service Search] Processed ${results.length} results for query: "${refinedQuery}"`
      );

      // Cache Results only if successful and not blocked
      if (results.length > 0 && !sourceStatus && !options.skipCache) {
        try {
          await priceCache.setJson(cacheKey, results, 3600); // Cache for 1 hour (using setJson)
          logger.info(
            `[Scraper Service Search] Cached ${results.length} results for search "${query}"`
          );
        } catch (cacheError) {
          logger.error(
            `[Scraper Service Search] Redis cache SET error for key ${cacheKey}:`,
            cacheError
          );
          // Continue even if cache set fails
        }
      }

      // Return Results, including sourceStatus if present
      const responsePayload = {
        success: true,
        count: results.length,
        data: results,
        source: "scraper_service_search", // Updated source indicator
      };
      if (sourceStatus) {
        responsePayload.sourceStatus = sourceStatus;
      }
      return res.json(responsePayload);
    } catch (error) {
      // Log the original error directly to console FIRST
      console.error("--- Original Error Caught in Controller ---");
      console.error(error);
      console.error("--- End Original Error ---");

      logger.error(
        `[Controller Error] Error in searchMaterials for query "${req.query.query}": ${error.message}`,
        { stack: error.stack }
      );
      // Ensure ApiErrors from service are passed correctly
      if (error instanceof ApiError) {
        return next(error);
      }
      // Wrap other errors
      next(new ApiError(500, `Failed to search materials: ${error.message}`)); // Pass original message
    }
  },

  /**
   * Get material details by SKU
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next middleware function
   * @returns {Promise<void>}
   */
  async getMaterialBySku(req, res, next) {
    try {
      const { sku } = req.params;
      const { sourceId, skipCache } = req.query;
      const userId = req.user?.id;

      if (!sku) {
        return next(new ApiError(400, "SKU is required"));
      }

      const options = {
        sourceId: sourceId || undefined,
        skipCache: skipCache === "true",
      };

      logger.info(`[Playwright SKU] Request for SKU "${sku}"`, {
        userId,
        options,
      });

      // Ensure ScraperService is initialized (important if server restarts)
      await scraperService.ensureInitialized();

      let serviceResponse;
      if (options.sourceId) {
        logger.info(
          `[Scraper Service SKU] Calling getMaterialBySkuFromSource for source ${options.sourceId}`
        );
        serviceResponse = await scraperService.getMaterialBySkuFromSource(
          options.sourceId,
          sku,
          options
        );
      } else {
        logger.info(
          `[Scraper Service SKU] Calling getMaterialBySku (all sources)`
        );
        serviceResponse = await scraperService.getMaterialBySku(sku, options); // Tries all sources sequentially
      }

      let materialData = null;
      let sourceStatus = null;

      // Check if the response includes sourceStatus (indicating Platt block or similar)
      if (
        serviceResponse &&
        typeof serviceResponse === "object" &&
        serviceResponse.sourceStatus
      ) {
        materialData = serviceResponse.data; // Could be null if no cache
        sourceStatus = serviceResponse.sourceStatus;
        logger.warn(
          `[Scraper Service SKU] SKU lookup for "${sku}" encountered source status: ${sourceStatus.status} - ${sourceStatus.reason}`
        );
        if (materialData) {
          logger.info(
            `[Scraper Service SKU] Returning cached data for SKU "${sku}" due to source status.`
          );
        }
      } else if (serviceResponse && typeof serviceResponse === "object") {
        // Normal successful response (assuming it's the material object)
        materialData = serviceResponse;
      } else {
        logger.error(
          `[Scraper Service SKU] Unexpected response format from scraperService for SKU "${sku}": ${JSON.stringify(
            serviceResponse
          )}`
        );
        // Handle unexpected format, maybe return 404? Throwing error for now.
        throw new ApiError(
          500,
          "Unexpected response format from pricing service."
        );
      }

      // Check for price anomaly (using the extracted material data, if available)
      let anomalyResult = null;
      if (
        materialData &&
        materialData.price &&
        materialData.sourceId &&
        materialData.sku
      ) {
        try {
          anomalyResult = await scraperService.checkPriceAnomaly(
            materialData.sku,
            materialData.price,
            materialData.sourceId
          );
        } catch (anomalyError) {
          logger.error(
            `[Scraper Service SKU] Error checking price anomaly for SKU ${sku}:`,
            anomalyError
          );
        }
      }

      // Construct response payload
      const responsePayload = {
        success: true,
        // Ensure data is null if materialData is null, otherwise include anomaly check
        data: materialData
          ? { ...materialData, priceAnomaly: anomalyResult }
          : null,
      };
      // Include sourceStatus if it exists
      if (sourceStatus) {
        responsePayload.sourceStatus = sourceStatus;
      }
      return res.json(responsePayload);
    } catch (error) {
      logger.error(
        `[Playwright SKU] Error in getMaterialBySku controller for SKU "${req.params.sku}":`,
        error
      );
      // Ensure ApiErrors from service are passed correctly
      if (error instanceof ApiError) {
        return next(error);
      }
      // Wrap other errors
      next(
        new ApiError(500, `Failed to get material by SKU: ${error.message}`)
      );
    }
  },

  /**
   * Get material details by URL using Firecrawl
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next middleware function
   * @returns {Promise<void>}
   */
  async getMaterialByUrl(req, res, next) {
    try {
      const { url } = req.body; // Assuming URL comes in the body
      const { skipCache } = req.query;
      const userId = req.user?.id;

      if (!url) {
        return next(new ApiError(400, "URL is required"));
      }

      logger.info(`[Playwright URL] Request for URL "${url}"`, {
        userId,
        skipCache,
      });

      // Ensure ScraperService is initialized
      await scraperService.ensureInitialized();

      const options = {
        skipCache: skipCache === "true",
      };

      // Call the service method which determines the source and calls the appropriate scraper
      // Note: getMaterialByUrl might need similar {data, sourceStatus} handling if it calls getBySku internally
      // Assuming for now getMaterialByUrl handles this internally or throws specific errors
      // TODO: Verify if getMaterialByUrl needs modification in ScraperService to return sourceStatus
      const materialData = await scraperService.getMaterialByUrl(url, options);

      // Check for price anomaly (using the extracted material data, if available)
      let anomalyResult = null;
      let sourceStatus = null; // Placeholder - getMaterialByUrl doesn't explicitly return this yet

      if (
        materialData &&
        materialData.price &&
        materialData.sourceId &&
        materialData.sku
      ) {
        try {
          anomalyResult = await scraperService.checkPriceAnomaly(
            materialData.sku,
            materialData.price,
            materialData.sourceId
          );
        } catch (anomalyError) {
          logger.error(
            `[Scraper Service URL] Error checking price anomaly for URL ${url}:`,
            anomalyError
          );
        }
      }

      // Construct response payload
      const responsePayload = {
        success: true,
        data: materialData
          ? { ...materialData, priceAnomaly: anomalyResult }
          : null,
      };
      // TODO: Add sourceStatus here if getMaterialByUrl is updated to return it
      // if (sourceStatus) {
      //   responsePayload.sourceStatus = sourceStatus;
      // }
      return res.json(responsePayload);
    } catch (error) {
      logger.error(
        `[Playwright URL] Error in getMaterialByUrl controller for URL "${req.body.url}":`,
        error
      );
      if (error instanceof ApiError) {
        return next(error);
      }
      next(
        new ApiError(500, `Failed to get material by URL: ${error.message}`)
      );
    }
  },

  /**
   * Get material details by URL (specifically for fetching description/category after search)
   * @param {Object} req - Express request object (expects { url: '...' } in body)
   * @param {Object} res - Express response object
   * @param {Function} next - Express next middleware function
   * @returns {Promise<void>}
   */
  async getMaterialDetailsByUrl(req, res, next) {
    try {
      const { url } = req.body;
      const userId = req.user?.id;

      if (!url) {
        return next(new ApiError(400, "URL is required in the request body"));
      }

      logger.info(
        `[Controller Details] Request to fetch details for URL: "${url}"`,
        { userId }
      );

      // Ensure ScraperService is initialized
      await scraperService.ensureInitialized();

      // Call the service method which determines the source and calls the appropriate scraper
      // We primarily want description and category here.
      const materialDetails = await scraperService.getMaterialByUrl(url, {
        skipCache: true,
      }); // Skip cache for fresh details

      // Extract only the needed fields (description, category, maybe brand)
      const relevantDetails = {
        description: materialDetails?.description || "",
        category: materialDetails?.category || "", // Include category if extracted
        brand: materialDetails?.brand || "", // Include brand if extracted
      };

      return res.json({
        success: true,
        data: relevantDetails,
      });
    } catch (error) {
      logger.error(
        `[Controller Details] Error in getMaterialDetailsByUrl for URL "${req.body.url}":`,
        error
      );
      if (error instanceof ApiError) {
        // Pass specific errors (like 404 Not Found from scraper) through
        return next(error);
      }
      // Generic error for other issues
      next(
        new ApiError(
          500,
          `Failed to get material details by URL: ${error.message}`
        )
      );
    }
  },

  /**
   * Get material price history
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next middleware function
   * @returns {Promise<void>}
   */
  async getMaterialPriceHistory(req, res, next) {
    try {
      const { sku, sourceId } = req.params;

      if (!sku || !sourceId) {
        return next(new ApiError(400, "SKU and sourceId are required"));
      }

      logger.info(
        `Getting price history for SKU "${sku}" from source "${sourceId}"`,
        { userId: req.user?.id }
      );

      // Fetch directly from history model
      const priceHistory = await MaterialPriceHistory.find({
        sku,
        source: sourceId,
      }).sort({ timestamp: -1 });

      return res.json({
        success: true,
        data: priceHistory,
      });
    } catch (error) {
      next(error);
    }
  },

  /**
   * Get all material sources
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next middleware function
   * @returns {Promise<void>}
   */
  async getMaterialSources(req, res, next) {
    try {
      const sources = await MaterialSource.find()
        .select(
          "-credentials.password -credentials.apiKey -proxySettings.proxyList"
        )
        .sort("name");

      return res.json({
        success: true,
        count: sources.length,
        data: sources,
      });
    } catch (error) {
      next(error);
    }
  },

  /**
   * Get material source by ID
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next middleware function
   * @returns {Promise<void>}
   */
  async getMaterialSourceById(req, res, next) {
    try {
      const { id } = req.params;

      if (!mongoose.Types.ObjectId.isValid(id)) {
        return next(new ApiError(400, "Invalid source ID"));
      }

      const source = await MaterialSource.findById(id).select(
        "-credentials.password -credentials.apiKey -proxySettings.proxyList"
      );

      if (!source) {
        return next(new ApiError(404, "Material source not found"));
      }

      return res.json({
        success: true,
        data: source,
      });
    } catch (error) {
      next(error);
    }
  },

  /**
   * Create material source
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next middleware function
   * @returns {Promise<void>}
   */
  async createMaterialSource(req, res, next) {
    try {
      const { name, type, baseUrl, enabled } = req.body;

      if (!name || !type || !baseUrl) {
        return next(new ApiError(400, "Name, type, and baseUrl are required"));
      }

      const source = new MaterialSource({
        name,
        type,
        baseUrl,
        enabled: enabled !== undefined ? enabled : true,
        credentials: req.body.credentials || {},
        rateLimit: req.body.rateLimit || {
          requestsPerMinute: 10,
          cooldownPeriod: 0,
        },
        proxySettings: req.body.proxySettings || { useProxy: false },
        scrapeConfig: req.body.scrapeConfig || {},
      });

      await source.save();

      // Scraper refresh logic is no longer needed with Firecrawl

      return res.status(201).json({
        success: true,
        data: source,
      });
    } catch (error) {
      next(error);
    }
  },

  /**
   * Update material source
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next middleware function
   * @returns {Promise<void>}
   */
  async updateMaterialSource(req, res, next) {
    try {
      const { id } = req.params;

      if (!mongoose.Types.ObjectId.isValid(id)) {
        return next(new ApiError(400, "Invalid source ID"));
      }

      const source = await MaterialSource.findById(id);

      if (!source) {
        return next(new ApiError(404, "Material source not found"));
      }

      const updateFields = [
        "name",
        "type",
        "baseUrl",
        "enabled",
        "credentials",
        "rateLimit",
        "proxySettings",
        "scrapeConfig",
      ];

      updateFields.forEach((field) => {
        if (req.body[field] !== undefined) {
          source[field] = req.body[field];
        }
      });

      await source.save();

      // Scraper refresh logic is no longer needed with Firecrawl

      return res.json({
        success: true,
        data: source,
      });
    } catch (error) {
      next(error);
    }
  },

  /**
   * Delete material source
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next middleware function
   * @returns {Promise<void>}
   */
  async deleteMaterialSource(req, res, next) {
    try {
      const { id } = req.params;

      if (!mongoose.Types.ObjectId.isValid(id)) {
        return next(new ApiError(400, "Invalid source ID"));
      }

      const source = await MaterialSource.findById(id);

      if (!source) {
        return next(new ApiError(404, "Material source not found"));
      }

      const priceHistoryCount = await MaterialPriceHistory.countDocuments({
        source: id,
      });

      if (priceHistoryCount > 0) {
        return next(
          new ApiError(400, "Cannot delete source with price history records")
        );
      }

      await source.remove();

      // Scraper refresh logic is no longer needed with Firecrawl

      return res.json({
        success: true,
        message: "Material source deleted successfully",
      });
    } catch (error) {
      next(error);
    }
  },

  /**
   * Clear cache for material source
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next middleware function
   * @returns {Promise<void>}
   */
  async clearSourceCache(req, res, next) {
    try {
      const { id } = req.params;

      if (!mongoose.Types.ObjectId.isValid(id)) {
        return next(new ApiError(400, "Invalid source ID"));
      }

      const source = await MaterialSource.findById(id);

      if (!source) {
        return next(new ApiError(404, "Material source not found"));
      }

      // Cache clearing logic might need adjustment depending on how Firecrawl results are cached.
      // For now, rely on the main search cache key invalidation if needed elsewhere.
      logger.warn(`clearSourceCache might need reimplementation.`);

      return res.json({
        success: true,
        message: "Cache cleared successfully", // Placeholder message
      });
    } catch (error) {
      next(error);
    }
  },

  /**
   * Get scraping logs
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next middleware function
   * @returns {Promise<void>}
   */
  async getScrapingLogs(req, res, next) {
    try {
      const { sourceId, status, limit = 50, page = 1 } = req.query;

      const query = {};
      if (sourceId) query.source = sourceId;
      if (status) query.status = status.toUpperCase();

      const options = {
        sort: { startTime: -1 },
        skip: (page - 1) * limit,
        limit: parseInt(limit, 10),
      };

      const logs = await PriceScrapingLog.find(query, null, options);
      const total = await PriceScrapingLog.countDocuments(query);

      return res.json({
        success: true,
        count: logs.length,
        total,
        page: parseInt(page, 10),
        totalPages: Math.ceil(total / limit),
        data: logs,
      });
    } catch (error) {
      next(error);
    }
  },

  /**
   * Create a quote
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next middleware function
   * @returns {Promise<void>}
   */

  async createQuote(req, res, next) {
    // Renamed function
    try {
      // ADD DETAILED LOGGING HERE
      logger.info(
        "[quoteController.createQuote] Received request. Body:",
        JSON.stringify(req.body, null, 2)
      );
      if (req.body.items && Array.isArray(req.body.items)) {
        req.body.items.forEach((item, index) => {
          logger.info(
            `[quoteController.createQuote] Item ${index}:`,
            JSON.stringify(item, null, 2)
          );
          if (item.lookup_results) {
            logger.info(
              `[quoteController.createQuote] Item ${index} has lookup_results:`,
              JSON.stringify(item.lookup_results, null, 2)
            );
          } else {
            logger.info(
              `[quoteController.createQuote] Item ${index} DOES NOT have lookup_results.`
            );
          }
          // Log lookup_query_suggestion if present from AI
          if (item.lookup_query_suggestion) {
            logger.info(
              `[quoteController.createQuote] Item ${index} has lookup_query_suggestion:`,
              item.lookup_query_suggestion
            );
          }
        });
      }

      // Use new fields, remove 'description'
      const {
        name,
        projectOverview,
        scopeOfWork,
        materialsIncluded,
        validUntil,
        summary,
        labor,
        job,
        customer,
      } = req.body;

      // Transform items from frontend format to backend format
      const items = transformItemsForBackend(req.body.items || []);

      if (
        !name ||
        !items ||
        !Array.isArray(items) ||
        items.length === 0 ||
        !customer
      ) {
        return next(
          new ApiError(400, "Name, items, and customer are required")
        );
      }

      const companyId = await getCompanyIdFromUser(req.user);

      const quote = new Quote({
        // Use renamed model
        name,
        projectOverview,
        scopeOfWork,
        materialsIncluded,
        validUntil,
        items,
        summary,
        labor,
        job,
        customer, // Use new fields
        createdBy: req.user.id,
        company: companyId,
      });

      await quote.save();

      // Log that the quote has been saved and should trigger change stream
      logger.info(
        `[quoteController.createQuote] Quote ${quote._id} saved successfully with ${quote.items.length} items`
      );

      // Count items that need price lookup
      let itemsNeedingLookup = 0;
      for (const item of quote.items) {
        if (item.lookup_results && Array.isArray(item.lookup_results)) {
          const lookupEntry = item.lookup_results.find(
            (lr) => lr.status === "pending_internal_price_lookup"
          );
          if (lookupEntry) {
            itemsNeedingLookup++;
            logger.info(
              `[quoteController.createQuote] Item ${item._id} needs price lookup with query: ${lookupEntry.mcp_request?.internal_query_details}`
            );
          }
        }
      }

      logger.info(
        `[quoteController.createQuote] ${itemsNeedingLookup} items need price lookup - ChangeStreamService should handle them`
      );

      // NOTE: The ChangeStreamService will detect this insert and automatically
      // trigger price lookups for items with 'pending_internal_price_lookup' status

      // Removed call to copyQuoteImagesToCustomer

      return res.status(201).json({ success: true, data: quote });
    } catch (error) {
      next(error);
    }
  },

  /**
   * Get all quotes with filtering, sorting, and pagination
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next middleware function
   * @returns {Promise<void>}
   */
  async getQuotes(req, res, next) {
    try {
      const {
        page = 1,
        limit = 10, // Default to 10 like invoices
        status,
        customer,
        job,
        startDate,
        endDate,
        search, // Added search term
        sortBy = "createdAt", // Default sort
        sortOrder = "desc", // Default order
      } = req.query;

      const companyId = await getCompanyIdFromUser(req.user);

      // Build query
      const query = { company: companyId };
      if (status) query.status = status;
      if (customer) query.customer = customer;
      if (job) query.job = job; // Keep existing job filter

      // Date range filter (using createdAt for quotes)
      if (startDate || endDate) {
        query.createdAt = {};
        if (startDate) query.createdAt.$gte = new Date(startDate);
        if (endDate) {
          // Adjust endDate to include the whole day
          const endOfDay = new Date(endDate);
          endOfDay.setHours(23, 59, 59, 999);
          query.createdAt.$lte = endOfDay;
        }
      }

      // Text search filter (on name, projectOverview, scopeOfWork, summary)
      if (search) {
        const searchRegex = new RegExp(search, "i"); // Case-insensitive regex
        query.$or = [
          { name: searchRegex },
          { projectOverview: searchRegex },
          { scopeOfWork: searchRegex },
          { summary: searchRegex },
          // Optionally search populated fields (requires more complex aggregation or separate queries)
          // { 'customer.businessName': searchRegex },
          // { 'job.title': searchRegex }
        ];
      }

      // Validate sort fields (prevent injection) - Add more valid fields as needed
      const validSortFields = [
        "createdAt",
        "updatedAt",
        "name",
        "status",
        "validUntil",
        "summary.grandTotal",
      ];
      const sortField = validSortFields.includes(sortBy) ? sortBy : "createdAt";
      const sortDirection = sortOrder === "asc" ? 1 : -1;

      // Execute query with pagination and sorting
      const quotes = await Quote.find(query)
        .populate("createdBy", "firstName lastName email")
        .populate("job", "title description") // Keep existing populates
        .populate("customer", "businessName contactPerson") // Keep existing populates
        .sort({ [sortField]: sortDirection })
        .skip((page - 1) * limit)
        .limit(parseInt(limit, 10));

      // Get total count for pagination
      const total = await Quote.countDocuments(query);

      // Transform items for frontend format in each quote
      const transformedQuotes = quotes.map((quote) => {
        const quoteObj = quote.toObject();
        quoteObj.items = transformItemsForFrontend(quoteObj.items);
        return quoteObj;
      });

      return res.json({
        success: true,
        count: quotes.length,
        total,
        currentPage: parseInt(page, 10), // Use currentPage for consistency
        totalPages: Math.ceil(total / parseInt(limit, 10)),
        data: transformedQuotes,
      });
    } catch (error) {
      logger.error(`Error fetching quotes for user ${req.user?.id}:`, error);
      if (error instanceof ApiError) return next(error);
      next(new ApiError(500, "Failed to fetch quotes."));
    }
  },

  /**
   * Get quote by ID
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next middleware function
   * @returns {Promise<void>}
   */
  async getQuoteById(req, res, next) {
    // Renamed function
    try {
      const { id } = req.params;
      if (!mongoose.Types.ObjectId.isValid(id))
        return next(new ApiError(400, "Invalid quote ID"));

      const companyId = await getCompanyIdFromUser(req.user);
      const quote = await Quote.findOne({ _id: id, company: companyId }) // Use renamed model
        .populate("createdBy", "firstName lastName email")
        .populate("job", "title description address")
        .populate("customer"); // Populate the full customer object initially

      if (!quote)
        return next(new ApiError(404, "Quote not found or access denied"));

      // Filter customer images to find those associated with this quote
      let associatedImages = [];
      if (
        quote.customer &&
        quote.customer.customerImages &&
        quote.customer.customerImages.length > 0
      ) {
        associatedImages = quote.customer.customerImages.filter(
          (img) =>
            img.quoteId && img.quoteId.toString() === quote._id.toString()
        );
      }

      // Convert quote to a plain object to add the associatedImages field
      const quoteObject = quote.toObject();
      quoteObject.associatedImages = associatedImages;

      // Transform items for frontend format
      quoteObject.items = transformItemsForFrontend(quoteObject.items);

      // Optionally, remove the full customerImages array from the populated customer object in the response
      // if it's not needed elsewhere in the frontend when fetching a single quote.
      // delete quoteObject.customer.customerImages;

      return res.json({ success: true, data: quoteObject });
    } catch (error) {
      logger.error(`Error fetching quote by ID ${req.params.id}:`, error); // Add logging
      next(error);
    }
  },

  /**
   * Update quote
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next middleware function
   * @returns {Promise<void>}
   */
  async updateQuote(req, res, next) {
    // Renamed function
    try {
      const { id } = req.params;
      if (!mongoose.Types.ObjectId.isValid(id))
        return next(new ApiError(400, "Invalid quote ID"));

      const companyId = await getCompanyIdFromUser(req.user);
      const quote = await Quote.findOne({ _id: id, company: companyId }); // Use renamed model

      if (!quote)
        return next(new ApiError(404, "Quote not found or access denied"));

      // Use new fields, remove 'description'
      const updateFields = [
        "name",
        "projectOverview",
        "scopeOfWork",
        "materialsIncluded",
        "validUntil",
        "summary",
        "labor",
        "job",
        "status",
        "customer",
      ];

      updateFields.forEach((field) => {
        if (req.body[field] !== undefined) {
          quote[field] = req.body[field];
        }
      });

      // Handle items separately to apply transformation
      if (req.body.items !== undefined) {
        quote.items = transformItemsForBackend(req.body.items);
      }

      quote.history.push({
        updatedBy: req.user.id,
        updatedAt: new Date(),
        changes: updateFields.filter((field) => req.body[field] !== undefined),
      });

      await quote.save();

      // After saving, iterate through items to initiate price lookups if needed
      // This is important if items were part of the update or if their lookup status changed.
      for (const item of quote.items) {
        if (item.lookup_results && Array.isArray(item.lookup_results)) {
          const lookupEntry = item.lookup_results.find(
            (lr) => lr.status === "pending_internal_price_lookup"
          );
          if (lookupEntry) {
            try {
              const originalToolParams =
                lookupEntry.mcp_request && lookupEntry.mcp_request.tool_params
                  ? lookupEntry.mcp_request.tool_params
                  : { description: item.description || item.name }; // Fallback params
              logger.info(
                `[quoteController.updateQuote] Triggering initiatePriceLookup for item ${item._id} on quote ${quote._id}`
              );
              await priceLookupService.initiatePriceLookup(
                quote._id,
                item._id,
                originalToolParams
              );
            } catch (e) {
              logger.error(
                `[quoteController.updateQuote] Error calling initiatePriceLookup for item ${item._id} on quote ${quote._id}: ${e.message}`,
                { stack: e.stack }
              );
              // Decide if this error should affect the response; for now, just log it.
            }
          }
        }
      }

      // Removed call to copyQuoteImagesToCustomer

      const updatedQuote = await Quote.findById(quote._id) // Use renamed model
        .populate("createdBy", "firstName lastName email")
        .populate("job", "title description address")
        .populate("customer", "businessName contactPerson address");

      // Transform items for frontend format
      const quoteObject = updatedQuote.toObject();
      quoteObject.items = transformItemsForFrontend(quoteObject.items);

      return res.json({ success: true, data: quoteObject });
    } catch (error) {
      next(error);
    }
  },

  /**
   * Delete quote
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next middleware function
   * @returns {Promise<void>}
   */
  async deleteQuote(req, res, next) {
    try {
      const { id } = req.params;
      if (!mongoose.Types.ObjectId.isValid(id))
        return next(new ApiError(400, "Invalid quote ID"));

      const companyId = await getCompanyIdFromUser(req.user);
      const quote = await Quote.findOne({ _id: id, company: companyId });

      if (!quote)
        return next(new ApiError(404, "Quote not found or access denied"));

      // Optional: Add checks here if certain statuses prevent deletion (e.g., APPROVED)
      // if (['APPROVED', 'CONVERTED'].includes(quote.status)) {
      //   return next(new ApiError(400, `Cannot delete quote with status: ${quote.status}`));
      // }

      await quote.remove();
      // Send success response
      return res.json({
        success: true,
        message: "Quote deleted successfully",
        id,
      });
    } catch (error) {
      logger.error(`Error deleting quote ${req.params.id}:`, error);
      next(error);
    }
  }, // Correctly placed comma after deleteQuote

  /**
   * Send quote email
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next middleware function
   * @returns {Promise<void>}
   */
  async emailQuote(req, res, next) {
    try {
      const { id } = req.params;
      const { emailData } = req.body; // Expect { to, subject, message } or similar

      if (!mongoose.Types.ObjectId.isValid(id))
        return next(new ApiError(400, "Invalid quote ID"));

      const companyId = await getCompanyIdFromUser(req.user);
      const quote = await Quote.findOne({
        _id: id,
        company: companyId,
      }).populate("customer", "email businessName contactPerson"); // Populate customer email

      if (!quote)
        return next(new ApiError(404, "Quote not found or access denied"));

      if (!quote.customer || !quote.customer.email) {
        return next(
          new ApiError(400, "Customer email not found for this quote")
        );
      }

      // Populate company if needed
      if (!quote.company.name) {
        await quote.populate("company");
      }

      // Prepare email details (use provided data or generate defaults)
      const to = emailData?.to || quote.customer.email;
      const companyName =
        quote.company.name || process.env.COMPANY_NAME || "Your Company";
      const subject =
        emailData?.subject || `Quote ${quote.name} from ${companyName}`;
      const message =
        emailData?.message || `Please find attached your quote ${quote.name}.`;

      await sendQuoteEmail(quote, { to, subject, message });
      if (quote.status === "DRAFT") {
        quote.status = "SENT";
        quote.meta.sentAt = new Date();
        quote.meta.sentBy = req.user.id;
        quote.history.push({
          action: "SENT",
          user: req.user.id,
          date: new Date(),
          details: { to },
        });
        await quote.save();
      }

      logger.info(`Quote ${id} emailed to ${to} by user ${req.user.id}`);
      // Return the updated quote (or just success message)
      res.json({
        success: true,
        message: "Quote emailed successfully.",
        data: quote,
      });
    } catch (error) {
      logger.error(`Error emailing quote ${req.params.id}:`, error);
      next(error);
    }
  }, // Correctly placed comma after emailQuote

  /**
   * Generate PDF for a quote
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next middleware function
   * @returns {Promise<void>}
   */
  async generateQuotePDF(req, res, next) {
    try {
      const { id } = req.params;
      if (!mongoose.Types.ObjectId.isValid(id))
        return next(new ApiError(400, "Invalid quote ID"));

      const companyId = await getCompanyIdFromUser(req.user);

      const quote = await Quote.findOne({ _id: id, company: companyId }) // Use renamed model
        .populate("customer", "businessName contactPerson address")
        .populate("job", "title description address")
        .populate("createdBy", "firstName lastName");

      if (!quote)
        return next(new ApiError(404, "Quote not found or access denied"));

      const doc = new PDFDocument({ margin: 50 });

      res.setHeader("Content-Type", "application/pdf");
      res.setHeader(
        "Content-Disposition",
        `attachment; filename="quote_${quote.name.replace(
          /\s+/g,
          "_"
        )}_${id}.pdf"`
      );

      doc.pipe(res);

      // --- PDF Content ---
      const write = (text, options) => doc.text(text, options);
      const writeLine = (text, options) =>
        doc.text(text, options).moveDown(0.5);

      // Header
      doc
        .fontSize(20)
        .text(`Material Quote: ${quote.name}`, { align: "center" });
      doc.moveDown();

      // Quote Info
      doc.fontSize(10);
      writeLine(`Quote ID: ${quote._id}`);
      writeLine(`Date Created: ${formatDate(quote.createdAt)}`);
      writeLine(`Valid Until: ${formatDate(quote.validUntil)}`);
      writeLine(`Status: ${quote.status}`);
      writeLine(
        `Created By: ${quote.createdBy?.firstName || ""} ${
          quote.createdBy?.lastName || ""
        }`
      );
      doc.moveDown();

      // Customer / Job Info
      doc
        .fontSize(12)
        .text("Customer:", { continued: true })
        .font("Helvetica-Bold")
        .text(
          ` ${
            quote.customer?.businessName ||
            (quote.customer?.contactPerson
              ? `${quote.customer.contactPerson.firstName} ${quote.customer.contactPerson.lastName}`
              : "N/A")
          }`
        );
      if (quote.customer?.address) {
        doc
          .font("Helvetica")
          .fontSize(10)
          .text(
            `${quote.customer.address.street || ""}, ${
              quote.customer.address.city || ""
            }, ${quote.customer.address.state || ""} ${
              quote.customer.address.zipCode || ""
            }`
          );
      }
      doc.moveDown(0.5);
      doc
        .fontSize(12)
        .text("Job:", { continued: true })
        .font("Helvetica-Bold")
        .text(` ${quote.job?.title || "N/A"}`);
      if (quote.job?.address) {
        doc
          .font("Helvetica")
          .fontSize(10)
          .text(
            `${quote.job.address.street || ""}, ${
              quote.job.address.city || ""
            }, ${quote.job.address.state || ""} ${
              quote.job.address.zipCode || ""
            }`
          );
      }
      doc.moveDown();

      // Description
      if (quote.description) {
        doc.fontSize(12).text("Description:", { underline: true });
        doc.font("Helvetica").fontSize(10).text(quote.description);
        doc.moveDown();
      }

      // Items Table
      doc.fontSize(12).text("Items:", { underline: true });
      doc.moveDown(0.5);

      const tableTop = doc.y;
      const itemColWidths = [200, 50, 50, 70, 70];
      const itemHeaders = ["Name / SKU", "Qty", "Unit", "Unit Price", "Total"];
      const startX = doc.page.margins.left;
      const endX = doc.page.width - doc.page.margins.right;

      // Draw Table Header
      doc.font("Helvetica-Bold").fontSize(10);
      let currentX = startX;
      itemHeaders.forEach((header, i) => {
        doc.text(header, currentX, tableTop, {
          width: itemColWidths[i],
          align: i > 0 ? "right" : "left",
        });
        currentX += itemColWidths[i] + 10;
      });
      doc.moveDown(0.5);
      const headerBottom = doc.y;
      doc
        .moveTo(startX, headerBottom)
        .lineTo(endX, headerBottom)
        .strokeColor("#aaaaaa")
        .stroke();
      doc.moveDown(0.5);

      // Draw Table Rows
      doc.font("Helvetica").fontSize(9);
      (quote.items || []).forEach((item) => {
        const rowTop = doc.y;
        currentX = startX;
        const itemTotal = (item.quantity || 0) * (item.price || 0);

        const nameHeight = doc.heightOfString(
          `${item.name}\nSKU: ${item.sku || "N/A"}`,
          { width: itemColWidths[0] }
        );
        const rowHeight = Math.max(nameHeight, 15);

        doc.text(`${item.name}\nSKU: ${item.sku || "N/A"}`, currentX, rowTop, {
          width: itemColWidths[0],
          align: "left",
        });
        currentX += itemColWidths[0] + 10;
        doc.text((item.quantity || 0).toString(), currentX, rowTop, {
          width: itemColWidths[1],
          align: "right",
        });
        currentX += itemColWidths[1] + 10;
        doc.text(item.unit || "each", currentX, rowTop, {
          width: itemColWidths[2],
          align: "right",
        });
        currentX += itemColWidths[2] + 10;
        doc.text(formatCurrency(item.price), currentX, rowTop, {
          width: itemColWidths[3],
          align: "right",
        });
        currentX += itemColWidths[3] + 10;
        doc.text(formatCurrency(itemTotal), currentX, rowTop, {
          width: itemColWidths[4],
          align: "right",
        });

        doc.y = rowTop + rowHeight + 5;

        const rowBottom = doc.y;
        doc
          .moveTo(startX, rowBottom)
          .lineTo(endX, rowBottom)
          .strokeColor("#dddddd")
          .stroke();
        doc.moveDown(0.5);
      });

      // Labor Section
      if (quote.labor && (quote.labor.hours > 0 || quote.labor.notes)) {
        doc.moveDown();
        doc.fontSize(12).text("Labor:", { underline: true });
        doc.moveDown(0.5);
        doc.font("Helvetica").fontSize(10);
        doc.text(
          `Hours: ${quote.labor.hours || 0} @ ${formatCurrency(
            quote.labor.rate
          )}/hr = ${formatCurrency(quote.summary?.laborTotal)}`
        );
        if (quote.labor.notes) {
          doc.text(`Notes: ${quote.labor.notes}`);
        }
        doc.moveDown();
      }

      // Summary Section
      const summaryX = doc.page.width - doc.page.margins.right - 150;
      const summaryY = doc.y > 650 ? 50 : doc.y + 20;
      if (doc.y > 650) doc.addPage();
      doc.fontSize(10);
      doc.text(
        `Materials Total: ${formatCurrency(quote.summary?.materialsTotal)}`,
        summaryX,
        summaryY,
        { align: "right", width: 150 }
      );
      doc.text(
        `Labor Total: ${formatCurrency(quote.summary?.laborTotal)}`,
        summaryX,
        doc.y,
        { align: "right", width: 150 }
      );
      doc.text(
        `Subtotal: ${formatCurrency(quote.summary?.subtotal)}`,
        summaryX,
        doc.y,
        { align: "right", width: 150 }
      );
      doc.text(
        `Tax (${quote.summary?.taxRate || 0}%): ${formatCurrency(
          quote.summary?.tax
        )}`,
        summaryX,
        doc.y,
        { align: "right", width: 150 }
      );
      doc.moveDown(0.5);
      doc.font("Helvetica-Bold").fontSize(12);
      doc.text(
        `Grand Total: ${formatCurrency(quote.summary?.grandTotal)}`,
        summaryX,
        doc.y,
        { align: "right", width: 150 }
      );

      // --- End PDF Content ---

      doc.end();
    } catch (error) {
      logger.error(`Error generating PDF for quote ${req.params.id}:`, error);
      next(new ApiError(500, "Failed to generate PDF quote"));
    }
  },

  /**
   * Generate quote sections using AI
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next middleware function
   * @returns {Promise<void>}
   */
  async generateQuoteWithAI(req, res, next) {
    const { quoteId, inputType, inputData } = req.body;
    const userId = req.user.id;

    if (!quoteId || !inputType || !inputData) {
      return next(
        new ApiError(400, "quoteId, inputType, and inputData are required")
      );
    }
    if (!["overview", "materials"].includes(inputType)) {
      return next(
        new ApiError(
          400,
          "Invalid inputType. Must be 'overview' or 'materials'"
        )
      );
    }
    if (!mongoose.Types.ObjectId.isValid(quoteId)) {
      return next(new ApiError(400, "Invalid quote ID"));
    }

    logger.info(`AI Generation request for quote ${quoteId}`, {
      userId,
      inputType,
    });

    try {
      const companyId = await getCompanyIdFromUser(req.user);
      const quote = await Quote.findOne({ _id: quoteId, company: companyId }); // Use renamed model

      if (!quote) {
        return next(new ApiError(404, "Quote not found or access denied"));
      } // Correctly placed comma after generateQuotePDF

      // Set status to generating
      quote.aiGenerationStatus = "generating";
      quote.aiGenerationInputType = inputType;
      quote.aiOriginalInputData = inputData; // Store original input
      quote.aiClarificationQuestions = []; // Clear previous questions
      quote.aiUserInputAnswers = {}; // Clear previous answers
      await quote.save(); // Save status update

      // Construct Prompt 1 (Initial Generation)
      const systemPrompt = `You are an expert electrical estimator creating sections for a customer quote.
Format your response ONLY as a valid JSON object.
Input Type: ${inputType}

Based ONLY on the provided input data and any included images, generate:
1. projectOverview: A high-level summary of the project's purpose, goals, and context (Markdown format). This should be concise (2-3 paragraphs) and focus on WHAT the project aims to accomplish and WHY it's being done. If inputType is 'overview', refine the provided text. If inputType is 'materials', generate this based on the materials.

2. scopeOfWork: A detailed list of specific tasks, deliverables, and responsibilities. Format this section as follows:
   - Start with "Scope of Work:" as the main heading
   - Include a subheading for the main work type (e.g., "Installation of Subpanel:" or "Circuit Installation:")
   - Use bullet points with the "•" character (not dashes or asterisks)
   - Include specific technical details like measurements (e.g., "Run approximately 70 feet of 1" EMT conduit")
   - Include specific quantities and specifications (e.g., "Provide four (4) dedicated 20-amp, 120V circuits")
   - Add a second section titled "Additional Requirements:" with bullet points for general requirements
   - Do NOT use markdown formatting in this section

IMPORTANT: Ensure that the projectOverview and scopeOfWork sections contain completely different content. Do not repeat any sentences or paragraphs from the projectOverview in the scopeOfWork. The projectOverview should focus on the goals and context, while the scopeOfWork should focus exclusively on the specific tasks, implementation details, and step-by-step procedures.
3. materialsIncluded: A detailed list of key materials (Markdown format).
4. items: An array of JSON objects, each representing a material item with fields: name (string), quantity (number), unit (string, e.g., 'each', 'feet', 'lot'), and optionally attributes (object for technical specs like size, voltage, material).

**Instructions for materialsIncluded (Markdown list):**
For each material requiring specification based on electrical codes (e.g., NEC/CEC like conductors, conduit, grounding, overcurrent protection), you **must** follow one of these paths:
*   **Path 1 (Confident Specification):** Provide the specific value (e.g., '500 kcmil Copper conductors') AND explicitly state the code reference and parameters used (e.g., 'Based on NEC 310.16 for 400A at 75°C termination, assuming 3 current-carrying conductors in raceway, ambient 30°C').
*   **Path 2 (Need More Info):** If you lack essential parameters (e.g., voltage, phase, installation method, ambient temperature, specific local code requirements) to confidently apply the code, **do not guess**. Instead, add specific, targeted questions about the missing parameters to the 'clarificationQuestions' array.
*   **Path 3 (Need External Lookup):** If you know the relevant code/table but cannot perform the calculation or access the specific data reliably, output a structured placeholder requesting external verification. Format: \`[NEEDS_LOOKUP: { "type": "NEC_conductor_size", "params": { "ampacity": 400, "material": "copper", "known_voltage": "240V", ...other_known_params } }]\`.

**General Instructions:**
*   Prioritize asking questions (Path 2) or requesting lookups (Path 3) over making assumptions or providing vague specifications like 'sized per NEC'.
*   If the input lacks detail for ANY section (projectOverview, scopeOfWork, materialsIncluded specifics), identify specific questions to ask the user in the 'clarificationQuestions' array.
*   If no questions are needed and no external lookups are required, return an empty array for 'clarificationQuestions'.

Output JSON structure:
{
"projectOverview": "...",
"scopeOfWork": "...",
"materialsIncluded": "...",
"items": [
{ "name": "...", "quantity": 1, "unit": "...", "attributes": {...} },
...
],
"clarificationQuestions": ["Question 1?", "Question 2?"]
}
`;

      const userInput =
        typeof inputData === "string"
          ? inputData
          : JSON.stringify(inputData, null, 2);

      // --- Fetch Job Images for Context ---
      let imageDetails = [];
      if (quote.job) {
        try {
          const job = await Job.findById(quote.job);
          if (job && job.attachments && job.attachments.length > 0) {
            // Filter for relevant images (e.g., tagged 'quote_context' or 'before', limit count)
            const relevantAttachments = job.attachments
              .filter(
                (att) =>
                  att.mimeType?.startsWith("image/") &&
                  ["quote_context", "before", "general"].includes(att.tag)
              )
              .slice(0, 5); // Limit to first 5 relevant images

            imageDetails = relevantAttachments.map((att) => ({
              path: att.url, // Pass the relative URL stored in the DB
              mimeType: att.mimeType,
            }));
            logger.info(
              `[AI Quote Gen] Found ${imageDetails.length} relevant image(s) for job ${quote.job}`
            );
          }
        } catch (jobFetchError) {
          logger.error(
            `[AI Quote Gen] Error fetching job ${quote.job} for image context: ${jobFetchError.message}`
          );
          // Continue without images if job fetch fails
        }
      }
      // --- End Fetch Job Images ---

      // Call Gemini Service with image context if available
      const geminiOptions = {
        maxTokens: 8192, // Keep increased token limit
        ...(imageDetails.length > 0 && { imageDetails }), // Conditionally add imageDetails
      };
      logger.debug(
        `[AI Quote Gen] Calling Gemini with options: ${JSON.stringify(
          geminiOptions
        )}`
      );
      const aiResponse =
        await require("../utils/geminiService").getGeminiJsonResponse(
          systemPrompt,
          userInput,
          geminiOptions
        );

      if (!aiResponse || typeof aiResponse !== "object") {
        throw new Error("Invalid AI response format");
      }

      // Extract fields based on the expected structure from the updated prompt
      const {
        projectOverview = "",
        scopeOfWork = "",
        materialsIncluded = "",
        items = [],
        clarificationQuestions = [],
      } = aiResponse;

      // Log the received AI response structure for debugging
      logger.debug(`[AI Response Diag] Received AI Response Structure:`, {
        hasProjectOverview: projectOverview !== "",
        hasScopeOfWork: scopeOfWork !== "",
        hasMaterialsIncluded: materialsIncluded !== "",
        hasItems: items.length > 0,
        hasClarificationQuestions: clarificationQuestions.length > 0,
        quoteId: quoteId,
      });
      logger.debug(
        `[AI Response Diag] Received clarificationQuestions: ${JSON.stringify(
          clarificationQuestions
        )}`,
        { quoteId: quoteId }
      ); // Log the questions array

      // Validate that we have at least some content in the response
      // If we're missing critical sections but have items, add clarification questions
      // This ensures the AI is prompted to generate all required sections
      const missingCriticalSections = [];
      if (!projectOverview || projectOverview.trim() === "") {
        missingCriticalSections.push("Project Overview");
      }

      // Only check for missing scopeOfWork and materialsIncluded if we don't already have clarification questions
      // and we have items (which indicates the AI understood the basic request)
      if (clarificationQuestions.length === 0 && items.length > 0) {
        if (!scopeOfWork || scopeOfWork.trim() === "") {
          missingCriticalSections.push("Scope of Work");
        }
        if (!materialsIncluded || materialsIncluded.trim() === "") {
          missingCriticalSections.push("Materials Included");
        }

        // If we're missing critical sections, add a clarification question
        if (missingCriticalSections.length > 0) {
          logger.warn(
            `[AI Response Validation] Missing critical sections: ${missingCriticalSections.join(
              ", "
            )}. Adding clarification question.`
          );

          const missingSectionsQuestion = `Please provide more details for the following sections: ${missingCriticalSections.join(
            ", "
          )}. This information is needed to complete your quote.`;

          // Only add this question if it's not already in the list
          if (!clarificationQuestions.includes(missingSectionsQuestion)) {
            clarificationQuestions.push(missingSectionsQuestion);
          }
        }
      }

      // Process AI response: Check for questions first
      if (clarificationQuestions && clarificationQuestions.length > 0) {
        quote.aiClarificationQuestions = clarificationQuestions;
        quote.aiGenerationStatus = "pending_questions";
        // Save any generated text and items so far, even if questions are pending
        quote.projectOverview = projectOverview || quote.projectOverview;
        quote.scopeOfWork = scopeOfWork || quote.scopeOfWork;
        quote.materialsIncluded = materialsIncluded || quote.materialsIncluded;
        // Append items from AI response to existing items if any
        if (items && Array.isArray(items)) {
          quote.items.push(...items);
        }
        await quote.save();
        logger.info(`AI requested clarification for quote ${quoteId}`, {
          questions: clarificationQuestions.length,
          quoteId: quoteId,
        });
        return res.json({
          success: true,
          status: "pending_questions",
          questions: clarificationQuestions,
          quoteId: quote._id,
          generatedData: {
            // Include generated data
            projectOverview: quote.projectOverview,
            scopeOfWork: quote.scopeOfWork,
            materialsIncluded: quote.materialsIncluded,
            items: quote.items, // Include updated items
          },
        });
      } else {
        // No initial clarification questions from AI, proceed with proactive lookup and final save
        let processedMaterialsIncluded = materialsIncluded;
        let processedItems = items;
        const newClarificationQuestions = []; // This should ideally be empty here, but keep for safety
        let lookupErrorOccurred = false; // Track if any lookup failed
        let lookupsAttempted = false; // Track if we even tried a lookup

        // --- Proactive Lookup Processing for materialsIncluded (Markdown list) ---
        if (materialsIncluded) {
          const lines = materialsIncluded.split("\n");
          const resolvedLines = await Promise.all(
            lines.map(async (line) => {
              logger.debug(
                `[Proactive Lookup Diag] ----- Start Line Check (Materials Included) -----`,
                { quoteId: quote._id }
              );
              logger.debug(
                `[Proactive Lookup Diag] Checking line: "${line.trim()}"`,
                { quoteId: quote._id }
              );
              const needsLookup =
                line.trim() &&
                (line.includes("[NEEDS_LOOKUP:") ||
                  [
                    "NEC",
                    "conductor",
                    "conduit",
                    "grounding",
                    "size per",
                    "rated for",
                    "ampacity",
                  ].some((keyword) => line.toLowerCase().includes(keyword)));
              logger.debug(
                `[Proactive Lookup Diag] Needs Lookup? ${needsLookup}`,
                { quoteId: quote._id }
              );

              if (needsLookup) {
                lookupsAttempted = true;
                const lookupType = determineLookupType(line); // Reuse helper
                const extractedParams = extractParamsFromLine(line, quote); // Reuse helper, pass quote context
                logger.debug(
                  `[Proactive Lookup Diag] Determined Type: ${lookupType}, Extracted Params: ${JSON.stringify(
                    extractedParams
                  )}`,
                  { quoteId: quote._id }
                );

                if (lookupType) {
                  const lookupRequest = {
                    type: lookupType,
                    params: extractedParams,
                  };
                  logger.debug(
                    `[Proactive Lookup Diag] Sending Request: ${JSON.stringify(
                      lookupRequest
                    )}`,
                    { quoteId: quote._id }
                  );

                  try {
                    // Use electricalCodeLookupService for NEC lookups
                    const lookupResult =
                      await electricalCodeLookupService.lookupMaterialSpecification(
                        lookupRequest
                      );
                    logger.debug(
                      `[Proactive Lookup Diag] Received Result: ${JSON.stringify(
                        lookupResult
                      )}`,
                      { quoteId: quote._id }
                    );

                    if (lookupResult.success) {
                      logger.info(
                        `Proactive lookup successful for line: "${line}". Result: ${lookupResult.result}`,
                        { quoteId: quote._id }
                      );
                      const placeholderRegex = /\[NEEDS_LOOKUP:.*?\]/g;
                      if (placeholderRegex.test(line)) {
                        return line.replace(
                          placeholderRegex,
                          `(${lookupResult.result} - Verified)`
                        );
                      } else {
                        return `${line.replace(/(\s*)$/, "")} (${
                          lookupResult.result
                        } - Verified)$1`;
                      }
                    } else if (
                      lookupResult.needsMoreInfo &&
                      lookupResult.needsMoreInfo.length > 0
                    ) {
                      logger.warn(
                        `Proactive lookup for line "${line}" needs more info. Cannot ask questions at this stage.`,
                        { quoteId: quote._id }
                      );
                      lookupErrorOccurred = true; // Treat as failure at this stage
                      return `${line} [Lookup Failed - Needs More Info]`;
                    } else {
                      logger.error(
                        `Proactive lookup failed for line "${line}". Error: ${
                          lookupResult.error || "Unknown lookup error"
                        }`,
                        { quoteId: quote._id }
                      );
                      lookupErrorOccurred = true;
                      return `${line} [Lookup Failed - Manual Check Required]`;
                    }
                  } catch (lookupServiceError) {
                    logger.error(
                      `Error calling lookup service for line "${line}":`,
                      lookupServiceError,
                      { quoteId: quote._id }
                    );
                    lookupErrorOccurred = true;
                    return `${line} [Lookup Service Error]`;
                  }
                } else {
                  logger.warn(
                    `Could not determine lookup type for line: "${line}". Skipping lookup.`,
                    { quoteId: quote._id }
                  );
                  return line;
                }
              } else {
                return line;
              }
            })
          );
          processedMaterialsIncluded = resolvedLines.join("\n");
        }
        // --- End Proactive Lookup Processing for materialsIncluded ---

        // --- Proactive Lookup Processing for Items (Array) ---
        if (processedItems && Array.isArray(processedItems)) {
          const processedItemsPromises = processedItems.map(async (item) => {
            // Skip items that already have a price or SKU, or are not material components / description
            if (
              item.price > 0 ||
              item.sku ||
              !(item.description || item.name)
            ) {
              return item;
            }

            logger.debug(
              `[Internal Price Lookup Check] ----- Start Item Check -----`,
              {
                quoteId: quote._id,
                itemDescription: item.description || item.name,
              }
            );
            // parserUtils is imported at the top of the file
            const lookupDetails = parserUtils.determineLookupTypeForItem(item);
            logger.debug(
              `[Internal Price Lookup Check] Determined Item Type: ${
                lookupDetails ? lookupDetails.lookupType : "N/A"
              } for item "${item.description || item.name}"`,
              { quoteId: quote._id }
            );

            if (
              lookupDetails &&
              lookupDetails.lookupType &&
              (lookupDetails.lookupType.startsWith("PRICE_MATERIAL") ||
                lookupDetails.lookupType.startsWith("PRICE_GENERAL"))
            ) {
              logger.info(
                `[Quote Controller] Item "${
                  item.description || item.name
                }" (type: ${
                  lookupDetails.lookupType
                }) flagged for internal price lookup.`
              );
              item.lookup_results = item.lookup_results || [];

              const searchParameters = {
                description: item.description || item.name,
                quantity: item.quantity,
                unit: item.unit,
                attributes: item.attributes,
                lookup_query_suggestion: item.lookup_query_suggestion, // if available on the item
              };

              item.lookup_results.push({
                type: lookupDetails.lookupType,
                status: "pending_internal_price_lookup",
                mcp_request: {
                  // Re-using structure for parameters needed by priceLookupService
                  tool_id: "internal_price_lookup",
                  tool_params: searchParameters,
                },
                timestamp: new Date().toISOString(),
                source: "ai_suggestion_for_internal_lookup",
              });
              // lookupsAttempted and lookupErrorOccurred are not set here as the process is now async.
              // The quote status 'complete_needs_review' might be set based on pending lookups later or handled by UI.
            } else {
              logger.debug(
                `[Internal Price Lookup Check] Item "${
                  item.description || item.name
                }" does not need internal price lookup. Type: ${
                  lookupDetails ? lookupDetails.lookupType : "N/A"
                }.`,
                { quoteId: quote._id }
              );
            }
            return item; // Always return the item (possibly modified)
          });
          processedItems = await Promise.all(processedItemsPromises);
          logger.debug(
            "[Proactive Lookup Diag] All item lookup promises resolved.",
            { quoteId: quote._id }
          );
        }
        // --- End Proactive Lookup Processing for Items ---

        // After processing all lookups, check if any new questions arose (shouldn't happen here)
        if (newClarificationQuestions.length > 0) {
          // This case should ideally not be hit in the final generation step
          logger.error(
            `Unexpected clarification questions generated during final AI processing for quote ${quoteId}.`,
            { questions: newClarificationQuestions, quoteId: quote._id }
          );
          // Decide how to handle: either add to quote and mark for review, or throw an error.
          // Adding to quote and marking for review seems safer.
          quote.aiClarificationQuestions = [
            ...(quote.aiClarificationQuestions || []),
            ...newClarificationQuestions,
          ];
          quote.aiGenerationStatus = "complete_needs_review"; // Force review
          lookupErrorOccurred = true; // Also mark as error occurred
        }

        // Update quote with final data
        quote.projectOverview = projectOverview;
        quote.scopeOfWork = scopeOfWork;
        quote.materialsIncluded = processedMaterialsIncluded; // Use potentially updated materials string
        quote.items = processedItems; // Use potentially updated items array
        quote.aiGenerationStatus = lookupErrorOccurred
          ? "complete_needs_review"
          : "complete"; // Mark for review if lookup failed
        quote.aiClarificationQuestions = []; // Ensure cleared
        quote.aiUserInputAnswers = {}; // Ensure cleared
        quote.aiOriginalInputData = null; // Clear original input data

        await quote.save();

        // --- Initiate Internal Price Lookups ---
        if (quote.items && Array.isArray(quote.items)) {
          for (const item of quote.items) {
            // Ensure item._id is present, as it's crucial for the priceLookupService
            if (
              item._id &&
              item.lookup_results &&
              Array.isArray(item.lookup_results)
            ) {
              for (const lookup of item.lookup_results) {
                if (
                  lookup.status === "pending_internal_price_lookup" &&
                  lookup.mcp_request &&
                  lookup.mcp_request.tool_params
                ) {
                  logger.info(
                    `[Quote Controller] Initiating internal price lookup for item "${
                      item.description || item.name
                    }" (ID: ${item._id}) on quote ${quote._id}`
                  );
                  priceLookupService
                    .initiatePriceLookup(
                      quote._id,
                      item._id,
                      lookup.mcp_request.tool_params
                    )
                    .then(() =>
                      logger.info(
                        `Successfully queued internal price lookup for item ${item._id} on quote ${quote._id}`
                      )
                    )
                    .catch((err) =>
                      logger.error(
                        `Error queuing internal price lookup for item ${item._id} on quote ${quote._id}: ${err.message}, Stack: ${err.stack}`
                      )
                    );
                }
              }
            } else if (
              item.lookup_results &&
              Array.isArray(item.lookup_results) &&
              item.lookup_results.some(
                (lr) => lr.status === "pending_internal_price_lookup"
              )
            ) {
              // Log a warning if an item is pending lookup but doesn't have an _id
              // This indicates a potential issue with how items are saved or if they are not proper subdocuments.
              logger.warn(
                `[Quote Controller] Item "${
                  item.description || item.name
                }" on quote ${
                  quote._id
                } is pending internal price lookup but has no item._id. Lookup cannot be initiated for this specific item without an ID.`
              );
            }
          }
        }
        // --- End Initiate Internal Price Lookups ---
        logger.info(
          `AI generation complete for quote ${quoteId}. Status: ${quote.aiGenerationStatus}`,
          { quoteId: quote._id }
        );
        return res.json({
          success: true,
          status: quote.aiGenerationStatus, // Return final status
          generatedData: {
            projectOverview,
            scopeOfWork,
            materialsIncluded: processedMaterialsIncluded,
            items: processedItems,
          }, // Include items
          quoteId: quote._id,
        });
      } // End else block (no initial clarification questions)
    } catch (error) {
      logger.error(
        `Error during AI quote generation for quote ${quoteId}:`,
        error,
        { quoteId: quoteId }
      );
      // Attempt to reset status on error
      try {
        const quoteOnError = await Quote.findById(quoteId); // Use renamed model
        if (quoteOnError) {
          quoteOnError.aiGenerationStatus = "error";
          await quoteOnError.save();
        }
      } catch (saveError) {
        logger.error(
          `Failed to update quote status to error for quote ${quoteId}:`,
          saveError,
          { quoteId: quoteId }
        );
      }
      next(new ApiError(500, `AI generation failed: ${error.message}`));
    }
  },

  /**
   * Submit answers to AI clarification questions
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next middleware function
   * @returns {Promise<void>}
   */
  async answerAIQuestions(req, res, next) {
    const { id } = req.params;
    const { answers } = req.body;
    const userId = req.user.id;

    if (!mongoose.Types.ObjectId.isValid(id)) {
      return next(new ApiError(400, "Invalid quote ID"));
    }
    if (
      !answers ||
      typeof answers !== "object" ||
      Object.keys(answers).length === 0
    ) {
      return next(new ApiError(400, "Answers object is required"));
    }

    logger.info(`Received AI answers for quote ${id}`, { userId });

    try {
      const companyId = await getCompanyIdFromUser(req.user);
      const quote = await Quote.findOne({ _id: id, company: companyId }); // Use renamed model

      if (!quote) {
        return next(new ApiError(404, "Quote not found or access denied"));
      } // Correctly placed comma after generateQuoteWithAI

      if (quote.aiGenerationStatus !== "pending_questions") {
        return next(new ApiError(400, "Quote is not awaiting AI answers"));
      }
      if (
        !quote.aiClarificationQuestions ||
        quote.aiClarificationQuestions.length === 0
      ) {
        return next(
          new ApiError(400, "No pending AI questions found for this quote")
        );
      }
      if (!quote.aiOriginalInputData) {
        return next(
          new ApiError(500, "Original AI input data not found for this quote")
        );
      }

      // Store answers and set status
      quote.aiUserInputAnswers = answers;
      quote.aiGenerationStatus = "generating"; // Mark as generating again
      await quote.save();

      // Construct Prompt 2 (Answering Questions)
      const systemPrompt = `You are an expert electrical estimator finalizing sections for a customer quote.
Format your response ONLY as a valid JSON object.
Original Input Type: ${quote.aiGenerationInputType}

Using the original input data AND the user's answers to your previous questions, refine and finalize:
1. projectOverview: A high-level summary of the project's purpose, goals, and context (Markdown format). This should be concise (2-3 paragraphs) and focus on WHAT the project aims to accomplish and WHY it's being done.

2. scopeOfWork: A detailed list of specific tasks, deliverables, and responsibilities. Format this section as follows:
   - Start with "Scope of Work:" as the main heading
   - Include a subheading for the main work type (e.g., "Installation of Subpanel:" or "Circuit Installation:")
   - Use bullet points with the "•" character (not dashes or asterisks)
   - Include specific technical details like measurements (e.g., "Run approximately 70 feet of 1" EMT conduit")
   - Include specific quantities and specifications (e.g., "Provide four (4) dedicated 20-amp, 120V circuits")
   - Add a second section titled "Additional Requirements:" with bullet points for general requirements
   - Do NOT use markdown formatting in this section

IMPORTANT: Ensure that the projectOverview and scopeOfWork sections contain completely different content. Do not repeat any sentences or paragraphs from the projectOverview in the scopeOfWork. The projectOverview should focus on the goals and context, while the scopeOfWork should focus exclusively on the specific tasks, implementation details, and step-by-step procedures.
3. materialsIncluded: A detailed list of key materials (Markdown format).

**Instructions for materialsIncluded (Final Attempt):**
Based on the original input AND the user's answers, for each material requiring specification based on electrical codes (e.g., NEC/CEC like conductors, conduit, grounding, overcurrent protection), you **must** follow one of these paths:
*   **Path 1 (Confident Specification):** Provide the specific value (e.g., '500 kcmil Copper conductors') AND explicitly state the code reference and parameters used (e.g., 'Based on NEC 310.16 for 400A at 75°C termination, using provided voltage and ambient temp').
*   **Path 2 (Need External Lookup):** If you *still* lack essential parameters OR cannot perform the calculation/access data reliably even with the answers, output a structured placeholder requesting external verification. Format: \`[NEEDS_LOOKUP: { "type": "NEC_conductor_size", "params": { "ampacity": 400, "material": "copper", ...all_known_params_from_input_and_answers } }]\`.

**Constraint:** Do NOT ask any more questions (\`clarificationQuestions\` should not be in the output). Generate the best possible response, using \`[NEEDS_LOOKUP: ...]\` if absolutely necessary.

Output JSON structure:
{
 "projectOverview": "...",
 "scopeOfWork": "...",
 "materialsIncluded": "..."
}`;

      const combinedInput = {
        originalInput: quote.aiOriginalInputData,
        questionsAsked: quote.aiClarificationQuestions,
        userAnswers: answers,
      };

      // --- Fetch Job Images for Context (Again for final generation) ---
      let imageDetails = [];
      if (quote.job) {
        try {
          const job = await Job.findById(quote.job);
          if (job && job.attachments && job.attachments.length > 0) {
            const relevantAttachments = job.attachments
              .filter(
                (att) =>
                  att.mimeType?.startsWith("image/") &&
                  ["quote_context", "before", "general"].includes(att.tag)
              )
              .slice(0, 5); // Limit to first 5 relevant images

            imageDetails = relevantAttachments.map((att) => ({
              path: att.url,
              mimeType: att.mimeType,
            }));
            logger.info(
              `[AI Answer Processing] Found ${imageDetails.length} relevant image(s) for job ${quote.job}`
            );
          }
        } catch (jobFetchError) {
          logger.error(
            `[AI Answer Processing] Error fetching job ${quote.job} for image context: ${jobFetchError.message}`
          );
        }
      }
      // --- End Fetch Job Images ---

      // Call Gemini Service with image context if available
      const geminiOptions = {
        maxTokens: 8192,
        ...(imageDetails.length > 0 && { imageDetails }),
      };
      logger.debug(
        `[AI Answer Processing] Calling Gemini with options: ${JSON.stringify(
          geminiOptions
        )}`
      );
      const aiResponse =
        await require("../utils/geminiService").getGeminiJsonResponse(
          systemPrompt,
          JSON.stringify(combinedInput, null, 2),
          geminiOptions
        );

      if (!aiResponse || typeof aiResponse !== "object") {
        throw new Error("Invalid AI response format");
      }

      const {
        projectOverview = "",
        scopeOfWork = "",
        materialsIncluded = "",
      } = aiResponse;

      // Proactively scan the AI's final materialsIncluded for lookups
      let processedMaterialsIncluded = materialsIncluded;
      const lines = materialsIncluded.split("\n");
      const updatedLines = [];
      let lookupErrorOccurred = false;
      let lookupsAttempted = false; // Renamed from finalMaterialsIncluded for clarity

      // Keywords and helpers (reuse from generateQuoteWithAI or define locally)
      const lookupKeywords = [
        "NEC",
        "conductor",
        "conduit",
        "grounding",
        "size per",
        "rated for",
        "ampacity",
      ];
      const lineNeedsLookup = (line) =>
        lookupKeywords.some((keyword) => line.toLowerCase().includes(keyword));
      const extractParamsFromLine = (line) => {
        const params = {};
        const ampMatch = line.match(/(\d+)\s*A/i);
        if (ampMatch) params.ampacity = parseInt(ampMatch[1], 10);
        if (line.toLowerCase().includes("copper")) params.material = "copper";
        if (line.toLowerCase().includes("aluminum"))
          params.material = "aluminum";
        return params;
      };
      const determineLookupType = (line) => {
        const lowerLine = line.toLowerCase();
        if (lowerLine.includes("conductor")) return "NEC_conductor_size";
        if (lowerLine.includes("conduit")) return "NEC_conduit_size";
        if (lowerLine.includes("grounding")) return "NEC_grounding_size";
        return null;
      };

      logger.info(
        `Proactively scanning final materials for lookups in quote ${id}.`
      );
      // Keep status as 'generating' or similar while processing lookups
      quote.aiGenerationStatus = "processing_lookups_final";
      await quote.save();

      for (const line of lines) {
        // Skip empty lines or lines already containing verification/failure markers
        if (
          !line.trim() ||
          line.includes("(Verified)") ||
          line.includes("[Lookup Failed")
        ) {
          updatedLines.push(line);
          continue;
        }

        if (lineNeedsLookup(line)) {
          lookupsAttempted = true;
          const lookupType = determineLookupType(line);
          const extractedParams = extractParamsFromLine(line, quote); // Pass quote context

          if (lookupType) {
            const lookupRequest = { type: lookupType, params: extractedParams };
            logger.debug(
              `Proactive final lookup triggered for line: "${line}". Request: ${JSON.stringify(
                lookupRequest
              )}`
            );

            try {
              const lookupResult =
                await electricalCodeLookupService.lookupMaterialSpecification(
                  lookupRequest
                );

              if (lookupResult.success) {
                logger.info(
                  `Proactive final lookup successful for line: "${line}". Result: ${lookupResult.result}`
                );
                // Attempt to replace the placeholder part of the line, or append
                const placeholderRegex = /\(Size per NEC.*?\)/i;
                if (placeholderRegex.test(line)) {
                  updatedLines.push(
                    line.replace(
                      placeholderRegex,
                      `(${lookupResult.result} - Verified)`
                    )
                  );
                } else {
                  updatedLines.push(
                    `${line.replace(/(\s*)$/, "")} (${
                      lookupResult.result
                    } - Verified)$1`
                  );
                }
              } else {
                // If lookup fails here (even needing more info), we mark for review as we can't ask more questions.
                logger.error(
                  `Proactive final lookup failed for line "${line}". Error: ${
                    lookupResult.error || "Needs info but cannot ask"
                  }`
                );
                lookupErrorOccurred = true;
                updatedLines.push(
                  `${line} [Lookup Failed - Manual Check Required]`
                );
              }
            } catch (lookupServiceError) {
              logger.error(
                `Error calling lookup service for final check on line "${line}":`,
                lookupServiceError
              );
              lookupErrorOccurred = true;
              updatedLines.push(`${line} [Lookup Service Error]`);
            }
          } else {
            logger.warn(
              `Could not determine lookup type for final check on line: "${line}". Skipping lookup.`
            );
            updatedLines.push(line);
          }
        } else {
          updatedLines.push(line);
        }
      } // End for loop

      processedMaterialsIncluded = updatedLines.join("\n");

      // Validate that we have content for all sections after processing the AI's answers
      // This ensures we always return complete quotes with all required sections
      let needsReview = lookupErrorOccurred;
      const missingCriticalSections = [];

      if (!projectOverview || projectOverview.trim() === "") {
        missingCriticalSections.push("Project Overview");
        needsReview = true;
      }

      if (!scopeOfWork || scopeOfWork.trim() === "") {
        missingCriticalSections.push("Scope of Work");
        needsReview = true;
      }

      if (
        !processedMaterialsIncluded ||
        processedMaterialsIncluded.trim() === ""
      ) {
        missingCriticalSections.push("Materials Included");
        needsReview = true;
      }

      if (missingCriticalSections.length > 0) {
        logger.warn(
          `[AI Answer Processing] Missing critical sections after processing answers: ${missingCriticalSections.join(
            ", "
          )}. Marking for review.`
        );
      }

      // Generate fallback content for missing sections
      let finalProjectOverview = projectOverview;
      let finalScopeOfWork = scopeOfWork;
      let finalMaterialsIncluded = processedMaterialsIncluded;

      // If Project Overview is missing, create a generic one
      if (!finalProjectOverview || finalProjectOverview.trim() === "") {
        logger.info(
          `[AI Answer Processing] Generating fallback Project Overview as it was empty`
        );
        finalProjectOverview =
          "## Project Overview\n\nThis project involves the work described in the quote items. The goal is to provide professional service that meets all applicable codes and standards while ensuring customer satisfaction.";
      }

      // If Scope of Work is missing, generate it from items if available
      if (
        (!finalScopeOfWork || finalScopeOfWork.trim() === "") &&
        quote.items &&
        quote.items.length > 0
      ) {
        logger.info(
          `[AI Answer Processing] Generating fallback Scope of Work from items as it was empty`
        );

        // Group items by type (labor vs materials)
        const laborItems = quote.items.filter(
          (item) =>
            (item.name && item.name.toLowerCase().includes("labor")) ||
            (item.unit && item.unit.toLowerCase().includes("hour"))
        );

        finalScopeOfWork = "## Scope of Work\n\n";
        finalScopeOfWork += "### Tasks to be Performed:\n\n";

        // Add labor items as tasks
        if (laborItems.length > 0) {
          laborItems.forEach((item) => {
            finalScopeOfWork += `- ${item.name || "Labor"}\n`;
          });
        } else {
          // Add generic tasks if no labor items
          finalScopeOfWork +=
            "- Procure all necessary materials and equipment\n";
          finalScopeOfWork +=
            "- Perform installation according to industry standards and local codes\n";
          finalScopeOfWork +=
            "- Test all installed components for proper operation\n";
          finalScopeOfWork += "- Clean up work area upon completion\n";
        }

        finalScopeOfWork += "\n### Deliverables:\n\n";
        finalScopeOfWork += "- Completed installation/service as described\n";
        finalScopeOfWork +=
          "- All work performed to code and industry standards\n";
        finalScopeOfWork += "- Cleanup of work area\n";
      } else if (!finalScopeOfWork || finalScopeOfWork.trim() === "") {
        // Generic fallback if no items available
        finalScopeOfWork =
          "Scope of Work:\n\nElectrical Installation:\n• Provide all necessary materials and equipment for the installation.\n• Install all components according to NEC standards and local codes.\n• Run appropriate conduit and wiring as required for the installation.\n• Provide all necessary connectors, fittings, and mounting hardware.\n• Test all installed components for proper operation.\n• Clean up work area upon completion of the installation.\n\nAdditional Requirements:\n• All work to be performed by licensed electricians in accordance with NEC and local codes.\n• Obtain all necessary permits and inspections as required by local authorities.\n• Provide proper grounding and bonding for all installed equipment.";
      }

      // If Materials Included is missing, generate it from items if available
      if (
        (!finalMaterialsIncluded || finalMaterialsIncluded.trim() === "") &&
        quote.items &&
        quote.items.length > 0
      ) {
        logger.info(
          `[AI Answer Processing] Generating fallback Materials Included from items as it was empty`
        );

        // Filter out labor items
        const materialItems = quote.items.filter(
          (item) =>
            !(item.name && item.name.toLowerCase().includes("labor")) &&
            !(item.unit && item.unit.toLowerCase().includes("hour"))
        );

        if (materialItems.length > 0) {
          finalMaterialsIncluded = "## Materials Included\n\n";

          materialItems.forEach((item) => {
            const quantity = item.quantity || 1;
            const unit = item.unit || "each";
            finalMaterialsIncluded += `- ${item.name} (${quantity} ${unit})\n`;
          });
        } else {
          // Generic fallback if no material items found
          finalMaterialsIncluded =
            "## Materials Included\n\nAll necessary materials for the completion of the described work will be provided, including:\n\n- Standard installation materials\n- Fasteners and connectors\n- Sealants and adhesives as required\n- Safety equipment\n- Cleanup supplies";
        }
      } else if (
        !finalMaterialsIncluded ||
        finalMaterialsIncluded.trim() === ""
      ) {
        // Generic fallback if no items available
        finalMaterialsIncluded =
          "## Materials Included\n\nAll necessary materials for the completion of the described work will be provided, including:\n\n- Standard installation materials\n- Fasteners and connectors\n- Sealants and adhesives as required\n- Safety equipment\n- Cleanup supplies";
      }

      // Helper function to calculate text similarity using Jaccard similarity
      function calculateSimilarity(text1, text2) {
        // Simple Jaccard similarity for text comparison
        const words1 = new Set(
          text1
            .toLowerCase()
            .split(/\s+/)
            .filter((word) => word.length > 3)
        );
        const words2 = new Set(
          text2
            .toLowerCase()
            .split(/\s+/)
            .filter((word) => word.length > 3)
        );

        const intersection = new Set([...words1].filter((x) => words2.has(x)));
        const union = new Set([...words1, ...words2]);

        return intersection.size / union.size;
      }

      // Validate that project overview and scope of work are not identical or too similar
      let needsDuplicationFix = false;
      let duplicationReason = "";

      if (finalProjectOverview && finalScopeOfWork) {
        // Check for exact match first
        if (finalProjectOverview.trim() === finalScopeOfWork.trim()) {
          needsDuplicationFix = true;
          duplicationReason = "identical content";
          logger.warn(
            "[Quote Controller] Project Overview and Scope of Work are identical. Modifying Scope of Work to be unique."
          );
        } else {
          // Check for partial duplication (first paragraph)
          const projectOverviewFirstPara =
            finalProjectOverview.split("\n\n")[0];
          const scopeOfWorkFirstPara = finalScopeOfWork.split("\n\n")[0];

          if (
            projectOverviewFirstPara &&
            scopeOfWorkFirstPara &&
            projectOverviewFirstPara.trim() === scopeOfWorkFirstPara.trim()
          ) {
            needsDuplicationFix = true;
            duplicationReason = "identical first paragraph";
            logger.warn(
              "[Quote Controller] First paragraph of Project Overview and Scope of Work are identical. Modifying Scope of Work to be unique."
            );
          } else {
            // Check for overall similarity if not exact match or identical first paragraph
            const similarity = calculateSimilarity(
              finalProjectOverview,
              finalScopeOfWork
            );
            if (similarity > 0.7) {
              // Threshold can be adjusted
              needsDuplicationFix = true;
              duplicationReason = `high similarity (${similarity.toFixed(2)})`;
              logger.warn(
                `[Quote Controller] Project Overview and Scope of Work are too similar (${similarity.toFixed(
                  2
                )}). Regenerating Scope of Work.`
              );
            }
          }
        }
      }

      // Regenerate Scope of Work if needed due to duplication
      if (needsDuplicationFix) {
        logger.info(
          `[Quote Controller] Regenerating Scope of Work due to ${duplicationReason}`
        );

        // Create a more specific scope introduction that doesn't duplicate the project overview
        // Format the Scope of Work to match the required style (plain text with specific formatting)
        finalScopeOfWork = "Scope of Work:\n\n";

        // Determine the main work type based on items or default to "Electrical Installation"
        let mainWorkType = "Electrical Installation";

        // Try to determine a more specific work type from the items if available
        if (
          quote.items &&
          Array.isArray(quote.items) &&
          quote.items.length > 0
        ) {
          // Look for common keywords in items to determine work type
          const allItemDescriptions = quote.items
            .map((item) => (item.name || item.description || "").toLowerCase())
            .join(" ");

          if (
            allItemDescriptions.includes("panel") ||
            allItemDescriptions.includes("subpanel")
          ) {
            mainWorkType = "Installation of Subpanel";
          } else if (
            allItemDescriptions.includes("circuit") ||
            allItemDescriptions.includes("breaker")
          ) {
            mainWorkType = "Circuit Installation";
          } else if (
            allItemDescriptions.includes("outlet") ||
            allItemDescriptions.includes("receptacle")
          ) {
            mainWorkType = "Outlet Installation";
          } else if (
            allItemDescriptions.includes("light") ||
            allItemDescriptions.includes("fixture")
          ) {
            mainWorkType = "Lighting Installation";
          }
        }

        finalScopeOfWork += `${mainWorkType}:\n`;

        // Group items by type (labor vs materials)
        const laborItems = quote.items.filter(
          (item) =>
            (item.name && item.name.toLowerCase().includes("labor")) ||
            (item.unit && item.unit.toLowerCase().includes("hour"))
        );

        const materialItems = quote.items.filter(
          (item) =>
            !(item.name && item.name.toLowerCase().includes("labor")) &&
            !(item.unit && item.unit.toLowerCase().includes("hour"))
        );

        // Add labor items as tasks with technical specifications
        if (laborItems.length > 0) {
          laborItems.forEach((item) => {
            finalScopeOfWork += `• ${item.name || "Labor"}\n`;
          });
        }

        // Add material-based tasks with technical specifications
        if (materialItems.length > 0) {
          // Create tasks based on material categories
          const wireItems = materialItems.filter(
            (item) =>
              (item.name || "").toLowerCase().includes("wire") ||
              (item.name || "").toLowerCase().includes("awg")
          );

          const conduitItems = materialItems.filter(
            (item) =>
              (item.name || "").toLowerCase().includes("conduit") ||
              (item.name || "").toLowerCase().includes("emt")
          );

          const boxItems = materialItems.filter(
            (item) =>
              (item.name || "").toLowerCase().includes("box") ||
              (item.name || "").toLowerCase().includes("junction")
          );

          // Add wire-related tasks
          if (wireItems.length > 0) {
            finalScopeOfWork += `• Provide and install approximately ${
              wireItems.length * 25
            } feet of appropriate gauge wire for the installation.\n`;
          }

          // Add conduit-related tasks
          if (conduitItems.length > 0) {
            finalScopeOfWork += `• Run approximately ${
              conduitItems.length * 20
            } feet of EMT conduit as required for the installation.\n`;
          }

          // Add box-related tasks
          if (boxItems.length > 0) {
            finalScopeOfWork += `• Install ${boxItems.length} junction boxes with appropriate connectors and mounting hardware.\n`;
          }
        }

        // If we don't have enough specific tasks, add generic ones
        if (laborItems.length + materialItems.length < 3) {
          finalScopeOfWork += `• Provide all necessary materials and equipment for the installation.\n`;
          finalScopeOfWork += `• Install all components according to NEC standards and local codes.\n`;
          finalScopeOfWork += `• Test all installed components for proper operation.\n`;
          finalScopeOfWork += `• Clean up work area upon completion of the installation.\n`;
        }

        // Add a second section for additional work if appropriate
        finalScopeOfWork += `\nAdditional Requirements:\n`;
        finalScopeOfWork += `• All work to be performed by licensed electricians in accordance with NEC and local codes.\n`;
        finalScopeOfWork += `• Obtain all necessary permits and inspections as required by local authorities.\n`;
        finalScopeOfWork += `• Provide proper grounding and bonding for all installed equipment.\n`;

        // Mark for review since we had to regenerate content
        needsReview = true;
      }

      // Update quote with final data
      quote.projectOverview = finalProjectOverview;
      quote.scopeOfWork = finalScopeOfWork;
      quote.materialsIncluded = finalMaterialsIncluded; // Use potentially updated materials string
      quote.aiGenerationStatus = needsReview
        ? "complete_needs_review"
        : "complete"; // Mark for review if needed
      quote.aiClarificationQuestions = []; // Clear questions
      quote.aiUserInputAnswers = {}; // Clear answers
      quote.aiOriginalInputData = null; // Clear original input data

      await quote.save();
      logger.info(
        `AI generation complete after answering questions for quote ${id}. Status: ${quote.aiGenerationStatus}`
      );

      return res.json({
        success: true,
        status: quote.aiGenerationStatus, // Return final status
        generatedData: {
          projectOverview: finalProjectOverview,
          scopeOfWork: finalScopeOfWork,
          materialsIncluded: finalMaterialsIncluded,
        },
        quoteId: quote._id,
      });
    } catch (error) {
      logger.error(`Error processing AI answers for quote ${id}:`, error);
      // Attempt to reset status on error
      try {
        const quoteOnError = await Quote.findById(id); // Use renamed model
        if (quoteOnError) {
          quoteOnError.aiGenerationStatus = "error"; // Keep answers for debugging?
          await quoteOnError.save();
        }
      } catch (saveError) {
        logger.error(
          `Failed to update quote status to error for quote ${id}:`,
          saveError
        );
      }
      next(new ApiError(500, `Failed to process AI answers: ${error.message}`));
    }
  }, // Add comma here

  /**
   * Upload images for a specific quote, associating them with the customer.
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next middleware function
   * @returns {Promise<void>}
   */
  async uploadQuoteImages(req, res, next) {
    const { id: quoteId } = req.params;
    const userId = req.user.id;
    const files = req.files;

    logger.info(
      `Attempting to upload ${
        files?.length || 0
      } images for quote ${quoteId} by user ${userId}`
    );

    if (!files || files.length === 0) {
      return next(
        new ApiError(
          400,
          'No files uploaded. Ensure the field name is "quoteImages".'
        )
      );
    }
    if (!mongoose.Types.ObjectId.isValid(quoteId)) {
      return next(new ApiError(400, "Invalid quote ID"));
    }

    let quote;
    let customer;
    const uploadedImageMetadata = [];

    try {
      // 1. Fetch Quote and Customer ID
      quote = await Quote.findById(quoteId).select("customer company");
      if (!quote) {
        throw new ApiError(404, "Quote not found");
      }

      // Verify company access (redundant if route middleware does this, but good practice)
      const companyId = await getCompanyIdFromUser(req.user);
      if (quote.company.toString() !== companyId.toString()) {
        throw new ApiError(403, "Access denied to this quote");
      }

      const customerId = quote.customer;
      if (!customerId) {
        throw new ApiError(400, "Quote is not associated with a customer");
      }

      // 2. Process each uploaded file
      for (const file of files) {
        const tempPath = file.path;
        const originalFilename = file.originalname;
        const uniqueFilename = file.filename; // Filename generated by multer
        const mimeType = file.mimetype;
        const size = file.size;

        // 3. Determine final destination
        const finalDir = path.join(
          __dirname,
          "..",
          "uploads",
          "customers",
          customerId.toString()
        );
        const finalPath = path.join(finalDir, uniqueFilename);
        const relativeUrl = `/uploads/customers/${customerId.toString()}/${uniqueFilename}`;

        // 4. Create destination directory
        await fs.mkdir(finalDir, { recursive: true });

        // 5. Move file from temp to final destination
        await fs.rename(tempPath, finalPath);
        logger.debug(`Moved file from ${tempPath} to ${finalPath}`);

        // 6. Generate Thumbnail
        let thumbnailUrl = null;
        const thumbnailFilename = `thumb-${uniqueFilename}`;
        const thumbnailPath = path.join(finalDir, thumbnailFilename);
        const relativeThumbnailUrl = `/uploads/customers/${customerId.toString()}/${thumbnailFilename}`;
        try {
          // TODO: Re-enable thumbnail generation when Sharp is properly installed
          // await sharp(finalPath)
          //   .resize(200, 200, { fit: 'inside', withoutEnlargement: true }) // Resize to fit within 200x200
          //   .toFile(thumbnailPath);
          // thumbnailUrl = relativeThumbnailUrl;
          // logger.debug(`Generated thumbnail: ${thumbnailPath}`);
          logger.info(
            `Thumbnail generation disabled - Sharp module not available`
          );
        } catch (thumbError) {
          logger.error(
            `Failed to generate thumbnail for ${uniqueFilename}: ${thumbError.message}`
          );
          // Continue without thumbnail if generation fails
        }

        // 7. AI Categorization
        let category = "other"; // Default category
        let aiCategory = "other";
        let aiConfidence = 0;

        try {
          const visionService = require("../services/visionService");
          const aiResult = await visionService.categorizeConstructionImage(
            finalPath
          );
          aiCategory = aiResult.category || "other";
          aiConfidence = aiResult.confidence || 0;

          // Map AI categories to our system categories
          // AI returns: 'before', 'after', 'during', 'other'
          // We use: 'before', 'after', 'other'
          if (aiCategory === "during") {
            category = "other"; // Map 'during' to 'other'
          } else {
            category = aiCategory;
          }

          logger.info(
            `AI categorized image as '${aiCategory}' with confidence ${aiConfidence.toFixed(
              2
            )}`
          );
        } catch (aiError) {
          logger.error(
            `AI categorization failed for ${uniqueFilename}: ${aiError.message}`
          );
          // Continue with default category if AI fails
        }

        // 8. Create Image Metadata
        const imageMetadata = {
          filename: uniqueFilename,
          originalName: originalFilename,
          url: relativeUrl,
          thumbnailUrl: thumbnailUrl, // Add thumbnail URL
          mimeType: mimeType,
          size: size,
          category: category, // Use mapped or default category
          aiCategory: aiCategory, // Store AI's suggestion
          aiConfidence: aiConfidence, // Store AI confidence score
          title: originalFilename, // Default title
          description: `Uploaded for quote ${quoteId}`, // Default description
          uploadedBy: userId,
          uploadedAt: new Date(),
          quoteId: quoteId, // Link image to the quote
        };
        uploadedImageMetadata.push(imageMetadata);
      } // End file loop

      // 9. Update Customer Document
      customer = await Customer.findById(customerId);
      if (!customer) {
        // This should ideally not happen if the quote had a valid customer ID
        throw new ApiError(404, "Customer associated with the quote not found");
      }
      if (!customer.customerImages) {
        customer.customerImages = [];
      }
      customer.customerImages.push(...uploadedImageMetadata);
      await customer.save();

      logger.info(
        `Successfully uploaded and associated ${uploadedImageMetadata.length} images with quote ${quoteId} and customer ${customerId}`
      );

      // 10. Return Success Response
      res.status(200).json({
        success: true,
        message: `${uploadedImageMetadata.length} images uploaded successfully for quote ${quoteId}`,
        images: uploadedImageMetadata, // Return metadata of uploaded images
      });
    } catch (error) {
      logger.error(
        `Error uploading images for quote ${quoteId}: ${error.message}`,
        { stack: error.stack }
      );
      // Clean up uploaded temporary files if an error occurs mid-process
      if (files && files.length > 0) {
        files.forEach((file) => {
          fs.unlink(file.path).catch((unlinkErr) =>
            logger.warn(
              `Failed to delete temp file ${file.path}: ${unlinkErr.message}`
            )
          );
        });
      }
      if (error instanceof ApiError) {
        next(error);
      } else {
        next(new ApiError(500, "Server error during quote image upload"));
      }
    }
  },

  /**
   * Real-time price lookup with Server-Sent Events for live progress updates
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next middleware function
   * @returns {Promise<void>}
   */
  async lookupPricesWithSSE(req, res, next) {
    try {
      const { items = [] } = req.body;

      if (!Array.isArray(items) || items.length === 0) {
        return next(new ApiError(400, "Items array is required"));
      }

      // Set up SSE headers
      res.writeHead(200, {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Cache-Control'
      });

      // Send initial progress event
      res.write(`data: ${JSON.stringify({
        type: 'start',
        total: items.length,
        message: 'Starting price lookup...',
        timestamp: new Date().toISOString()
      })}\n\n`);

      const processedItems = [];
      const summary = {
        total: items.length,
        processed: 0,
        pricesFound: 0,
        pricesFailed: 0
      };

      // Process items sequentially with real-time updates
      for (let i = 0; i < items.length; i++) {
        const item = items[i];
        const itemStartTime = Date.now();

        // Send item start event
        res.write(`data: ${JSON.stringify({
          type: 'item_start',
          index: i,
          itemName: item.name,
          progress: Math.round((i / items.length) * 100),
          message: `Looking up: ${item.name}`,
          timestamp: new Date().toISOString()
        })}\n\n`);

        try {
          // Simplified price lookup logic for SSE
          const processedItem = {
            ...item,
            index: i,
            priceInfo: {
              status: 'searching',
              source: null,
              lastUpdated: new Date().toISOString(),
              lookupError: null,
              responseTime: null
            }
          };

          // Use generic lookup for administrative items
          if (genericPriceLookupService.shouldUseGenericLookup(item)) {
            const genericResult = await genericPriceLookupService.getEstimatedPrice(item);
            if (genericResult) {
              processedItem.price = genericResult.price;
              processedItem.priceInfo = {
                ...genericResult.priceInfo,
                responseTime: Date.now() - itemStartTime
              };
              summary.pricesFound++;
            } else {
              processedItem.priceInfo.status = 'not_found';
              processedItem.priceInfo.lookupError = 'No generic pricing available';
              summary.pricesFailed++;
            }
          } else {
            // Try scraper service for electrical items
            try {
              const searchResults = await scraperService.searchMaterials(item.name, {
                skipCache: false,
                limit: 1,
                timeout: 15000 // Reduced timeout for SSE
              });

              if (searchResults && searchResults.length > 0) {
                const result = searchResults[0];
                processedItem.price = result.price;
                processedItem.priceInfo = {
                  status: 'found',
                  source: result.source,
                  lastUpdated: new Date().toISOString(),
                  lookupError: null,
                  responseTime: Date.now() - itemStartTime
                };
                summary.pricesFound++;
              } else {
                processedItem.priceInfo.status = 'not_found';
                processedItem.priceInfo.lookupError = 'No results found';
                summary.pricesFailed++;
              }
            } catch (scraperError) {
              processedItem.priceInfo.status = 'error';
              processedItem.priceInfo.lookupError = scraperError.message;
              summary.pricesFailed++;
            }
          }

          processedItem.priceInfo.responseTime = Date.now() - itemStartTime;
          processedItems.push(processedItem);
          summary.processed++;

          // Send item complete event
          res.write(`data: ${JSON.stringify({
            type: 'item_complete',
            index: i,
            item: processedItem,
            progress: Math.round(((i + 1) / items.length) * 100),
            message: `Completed: ${item.name} - ${processedItem.priceInfo.status}`,
            summary: { ...summary },
            timestamp: new Date().toISOString()
          })}\n\n`);

        } catch (error) {
          logger.error(`[SSE Price Lookup] Error processing item ${i}:`, error);

          const errorItem = {
            ...item,
            index: i,
            priceInfo: {
              status: 'error',
              source: null,
              lastUpdated: new Date().toISOString(),
              lookupError: error.message,
              responseTime: Date.now() - itemStartTime
            }
          };
          processedItems.push(errorItem);
          summary.processed++;
          summary.pricesFailed++;

          res.write(`data: ${JSON.stringify({
            type: 'item_error',
            index: i,
            item: errorItem,
            error: error.message,
            progress: Math.round(((i + 1) / items.length) * 100),
            timestamp: new Date().toISOString()
          })}\n\n`);
        }
      }

      // Send completion event
      res.write(`data: ${JSON.stringify({
        type: 'complete',
        items: processedItems,
        summary: summary,
        message: `Price lookup completed: ${summary.pricesFound} found, ${summary.pricesFailed} failed`,
        timestamp: new Date().toISOString()
      })}\n\n`);

      res.end();

    } catch (error) {
      logger.error('[SSE Price Lookup] Error:', error);
      res.write(`data: ${JSON.stringify({
        type: 'error',
        error: error.message,
        timestamp: new Date().toISOString()
      })}\n\n`);
      res.end();
    }
  },

  /**
   * Lookup prices for items without saving quote
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next middleware function
   * @returns {Promise<void>}
   */
  async lookupPricesWithoutSaving(req, res, next) {
    try {
      const { items } = req.body;
      const userId = req.user?.id;

      if (!items || !Array.isArray(items) || items.length === 0) {
        return next(new ApiError(400, "Items array is required for price lookup"));
      }

      logger.info(`Price lookup request for ${items.length} items from user ${userId}`);

      // Initialize response structure
      const response = {
        success: true,
        items: [],
        summary: {
          total: items.length,
          processed: 0,
          pricesFound: 0,
          pricesFailed: 0,
          averageResponseTime: 0
        }
      };

      const startTime = Date.now();
      const processedItems = [];

      // 🚀 CRITICAL FIX: Enhanced parallel processing with optimized concurrency limit
      const CONCURRENCY_LIMIT = 5; // Increased from 3 to 5 for better throughput while avoiding overwhelming scrapers
      const processItem = async (item, index) => {
        const itemStartTime = Date.now();

        try {
          // Initialize the processed item with original data
          const processedItem = {
            ...item,
            index: index,
            priceInfo: {
              status: 'pending',
              source: null,
              lastUpdated: null,
              lookupError: null,
              responseTime: null
            }
          };

          // 🚀 HIGH FIX: Use generic price lookup for non-electrical items
          if (genericPriceLookupService.shouldUseGenericLookup(item)) {
            try {
              const genericResult = await genericPriceLookupService.getEstimatedPrice(item);
              if (genericResult) {
                processedItem.price = genericResult.price;
                processedItem.source = genericResult.source;
                processedItem.sourceId = genericResult.sourceId;
                processedItem.priceInfo = {
                  ...genericResult.priceInfo,
                  responseTime: Date.now() - itemStartTime
                };
                return processedItem;
              }
            } catch (error) {
              logger.warn(`Generic price lookup failed for ${item.name}:`, error);
            }

            // If generic lookup fails, mark as skipped
            processedItem.priceInfo.status = 'skipped';
            processedItem.priceInfo.source = 'generic_lookup_failed';
            processedItem.priceInfo.lastUpdated = new Date().toISOString();
            processedItem.priceInfo.lookupError = `${item.source} item - generic price lookup not available`;
            return processedItem;
          }

          // Skip if item already has a price
          if (item.price && item.price > 0) {
            processedItem.priceInfo.status = 'existing_price';
            processedItem.priceInfo.source = 'user_provided';
            processedItem.priceInfo.lastUpdated = new Date().toISOString();
            return processedItem;
          }

          // Determine search strategy
          let searchQuery = null;
          let searchType = null;

          if (item.sku) {
            searchQuery = item.sku;
            searchType = 'sku';
          } else if (item.lookup_query_suggestion) {
            searchQuery = item.lookup_query_suggestion;
            searchType = 'search';
          } else if (item.name) {
            searchQuery = item.name;
            searchType = 'search';
          } else if (item.description) {
            searchQuery = item.description;
            searchType = 'search';
          }

          if (!searchQuery) {
            processedItem.priceInfo.status = 'no_search_data';
            processedItem.priceInfo.lookupError = 'No SKU, name, or description available for price lookup';
            return processedItem;
          }

          // 🚀 CRITICAL FIX: Optimized timeout and better error handling
          let priceResult = null;

          try {
            if (searchType === 'sku') {
              priceResult = await scraperService.getMaterialBySku(searchQuery, {
                skipCache: false,
                timeout: 20000 // Further reduced from 30s to 20s for faster processing
              });
            } else {
              const searchResults = await scraperService.searchMaterials(searchQuery, {
                skipCache: false,
                limit: 1,
                timeout: 20000 // Further reduced from 30s to 20s for faster processing
              });

              if (Array.isArray(searchResults) && searchResults.length > 0) {
                priceResult = searchResults[0];
              } else if (searchResults && searchResults.results && searchResults.results.length > 0) {
                priceResult = searchResults.results[0];
              }
            }
          } catch (lookupError) {
            // Handle specific error types
            if (lookupError.message && lookupError.message.includes('timeout')) {
              processedItem.priceInfo.status = 'timeout';
              processedItem.priceInfo.lookupError = 'Price lookup timed out after 30 seconds';
            } else if (lookupError.message && lookupError.message.includes('blocked')) {
              processedItem.priceInfo.status = 'blocked';
              processedItem.priceInfo.lookupError = 'Scraper was blocked by the website';
            } else {
              processedItem.priceInfo.status = 'error';
              processedItem.priceInfo.lookupError = lookupError.message || 'Price lookup failed';
            }

            const itemEndTime = Date.now();
            processedItem.priceInfo.responseTime = itemEndTime - itemStartTime;
            processedItem.priceInfo.lastUpdated = new Date().toISOString();
            return processedItem;
          }

          const itemEndTime = Date.now();
          const responseTime = itemEndTime - itemStartTime;

          if (priceResult && priceResult.price !== null && priceResult.price !== undefined) {
            // Price found successfully
            processedItem.price = priceResult.price;
            processedItem.sku = priceResult.sku || item.sku;
            processedItem.source = priceResult.source;
            processedItem.sourceId = priceResult.sourceId;
            processedItem.url = priceResult.url || item.url;
            processedItem.imageUrl = priceResult.imageUrl || item.imageUrl;

            processedItem.priceInfo = {
              status: 'found',
              source: priceResult.source,
              sourceId: priceResult.sourceId,
              lastUpdated: new Date().toISOString(),
              lookupError: null,
              responseTime: responseTime,
              searchQuery: searchQuery,
              searchType: searchType
            };

            logger.info(`Price found for item ${index}: ${priceResult.price} from ${priceResult.source}`);
          } else {
            // Price not found
            processedItem.priceInfo = {
              status: 'not_found',
              source: null,
              lastUpdated: new Date().toISOString(),
              lookupError: 'No price data available',
              responseTime: responseTime,
              searchQuery: searchQuery,
              searchType: searchType
            };

            logger.warn(`No price found for item ${index} with query: ${searchQuery}`);
          }

          return processedItem;

        } catch (error) {
          const itemEndTime = Date.now();
          const responseTime = itemEndTime - itemStartTime;

          logger.error(`Error looking up price for item ${index}:`, error);

          const processedItem = {
            ...item,
            index: index,
            priceInfo: {
              status: 'error',
              source: null,
              lastUpdated: new Date().toISOString(),
              lookupError: error.message || 'Price lookup failed',
              responseTime: responseTime
            }
          };

          return processedItem;
        }
      };

      // 🚀 CRITICAL FIX: Enhanced parallel processing with better concurrency control
      const processItemsInParallel = async (items) => {
        const results = [];
        const executing = [];

        for (let i = 0; i < items.length; i++) {
          const promise = processItem(items[i], i).then(result => {
            executing.splice(executing.indexOf(promise), 1);
            return result;
          });

          results.push(promise);
          executing.push(promise);

          // If we've reached the concurrency limit, wait for one to complete
          if (executing.length >= CONCURRENCY_LIMIT) {
            await Promise.race(executing);
          }
        }

        // Wait for all remaining promises to complete
        return await Promise.allSettled(results);
      };

      const allResults = await processItemsInParallel(items);

      allResults.forEach((result, index) => {
        if (result.status === 'fulfilled') {
          const processedItem = result.value;
          processedItems.push(processedItem);
          response.summary.processed++;

          if (processedItem.priceInfo.status === 'found') {
            response.summary.pricesFound++;
          } else if (processedItem.priceInfo.status !== 'existing_price' && processedItem.priceInfo.status !== 'skipped') {
            response.summary.pricesFailed++;
          }
        } else {
          // Handle rejected promises
          logger.error(`Promise rejected for item ${index}:`, result.reason);

          const failedItem = {
            ...items[index],
            index: index,
            priceInfo: {
              status: 'error',
              source: null,
              lastUpdated: new Date().toISOString(),
              lookupError: result.reason?.message || 'Promise rejected',
              responseTime: null
            }
          };

          processedItems.push(failedItem);
          response.summary.processed++;
          response.summary.pricesFailed++;
        }
      });

      const totalTime = Date.now() - startTime;
      response.summary.averageResponseTime = Math.round(totalTime / items.length);
      response.items = processedItems;

      logger.info(`Price lookup completed: ${response.summary.pricesFound} found, ${response.summary.pricesFailed} failed, ${totalTime}ms total`);

      return res.json(response);

    } catch (error) {
      logger.error(`Error in lookupPricesWithoutSaving:`, error);
      if (error instanceof ApiError) {
        return next(error);
      }
      next(new ApiError(500, `Price lookup failed: ${error.message}`));
    }
  },

  async resolveQuoteAction(req, res, next) {
    const { id } = req.params;
    const { action } = req.body; // Expect action = { lookupType, lookupParams, originalLine }
    const userId = req.user.id;

    if (!mongoose.Types.ObjectId.isValid(id)) {
      return next(new ApiError(400, "Invalid quote ID"));
    }
    if (
      !action ||
      typeof action !== "object" ||
      !action.lookupType ||
      !action.lookupParams ||
      !action.originalLine
    ) {
      return next(
        new ApiError(
          400,
          "Invalid action payload. Required fields: lookupType, lookupParams, originalLine."
        )
      );
    }

    logger.info(`Resolving action for quote ${id}: ${JSON.stringify(action)}`, {
      userId,
      quoteId: id,
      action,
    });

    try {
      const companyId = await getCompanyIdFromUser(req.user);
      const quote = await Quote.findOne({ _id: id, company: companyId });

      if (!quote) {
        return next(new ApiError(404, "Quote not found or access denied"));
      }

      const lookupRequest = {
        type: action.lookupType,
        params: action.lookupParams,
      };
      logger.debug(
        `Calling electricalCodeLookupService.lookupMaterialSpecification with: ${JSON.stringify(
          lookupRequest
        )}`,
        { quoteId: id }
      );
      const lookupResult =
        await electricalCodeLookupService.lookupMaterialSpecification(
          lookupRequest
        );

      let lineUpdated = false;

      if (lookupResult.success) {
        logger.info(
          `Lookup successful for quote ${id}, action ${action.lookupType}. Result: ${lookupResult.result}`,
          { quoteId: id }
        );

        if (!quote.materialsIncluded) {
          logger.warn(
            `materialsIncluded is null or undefined for quote ${id}. Cannot update line.`,
            { quoteId: id }
          );
          return next(
            new ApiError(
              404,
              `Original line for action not found in quote materials as materialsIncluded is empty. Line: "${action.originalLine}"`
            )
          );
        }

        const lines = quote.materialsIncluded.split("\n");
        const updatedLines = lines.map((line) => {
          if (line.trim() === action.originalLine.trim()) {
            lineUpdated = true;
            const placeholderRegex = /\[NEEDS_LOOKUP:.*?\]/g;
            if (placeholderRegex.test(line)) {
              return line.replace(
                placeholderRegex,
                `(${lookupResult.result} - Resolved via MCP)`
              );
            } else {
              // If no specific placeholder, append to the line. This might need adjustment based on how frontend structures originalLine.
              return `${line.replace(/(\s*)$/, "")} (${
                lookupResult.result
              } - Resolved via MCP)$1`;
            }
          }
          return line;
        });

        if (lineUpdated) {
          quote.materialsIncluded = updatedLines.join("\n");
          quote.history.push({
            updatedBy: userId,
            updatedAt: new Date(),
            changes: [
              `materialsIncluded (resolved action: ${action.lookupType})`,
            ],
            details: `Resolved action for line: "${action.originalLine}" with result: "${lookupResult.result}"`,
          });
          await quote.save();
          logger.info(
            `Quote ${id} updated successfully after resolving action.`,
            { quoteId: id }
          );
        } else {
          logger.warn(
            `Original line "${action.originalLine}" not found in quote ${id} materialsIncluded. No update performed.`,
            { quoteId: id }
          );
          return next(
            new ApiError(
              404,
              `Original line for action not found in quote materials. Line: "${action.originalLine}"`
            )
          );
        }
      } else {
        logger.error(
          `Lookup failed for quote ${id}, action ${action.lookupType}. Error: ${lookupResult.error}`,
          { quoteId: id }
        );
        return next(
          new ApiError(
            400,
            `Failed to resolve action: ${
              lookupResult.error || "Lookup service returned failure."
            }`
          )
        );
      }

      return res.json({
        success: true,
        message: `Action ${action.lookupType} resolved successfully.`,
        quoteId: quote._id,
        updatedMaterialsIncluded: quote.materialsIncluded,
        resolvedLine: action.originalLine,
        resolution: lookupResult.success
          ? lookupResult.result
          : lookupResult.error,
      });
    } catch (error) {
      logger.error(`Error resolving action for quote ${id}: ${error.message}`, {
        userId,
        quoteId: id,
        stack: error.stack,
      });
      if (error instanceof ApiError) return next(error);
      next(
        new ApiError(
          500,
          `Server error while resolving action: ${error.message}`
        )
      );
    }
  },
}; // End quoteController object

module.exports = quoteController; // Use renamed controller object
