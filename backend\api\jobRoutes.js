const express = require("express");
const router = express.Router();
const {
  createJob,
  getJob<PERSON>,
  getJob<PERSON>yId,
  updateJob,
  deleteJob,
  updateJob<PERSON>tatus,
  addJobTask,
  updateTaskStatus,
  assignTechnicians,
  analyzeJobRisks,
  getSimilarJobs,
  predictJobProgress,
  getJobHistory,
  uploadJobAttachment, // Added for file uploads
} = require("../controllers/jobController");
const { protect } = require("../middleware/authMiddleware");
const upload = require("../middleware/uploadMiddleware"); // Import upload middleware

// All routes are protected
router.use(protect);

router.route("/").post(createJob).get(getJobs);

router.route("/:id").get(getJobById).put(updateJob).delete(deleteJob);

// Status and task routes
router.patch("/:id/status", updateJobStatus);
router.post("/:id/tasks", addJobTask);
router.patch("/:id/tasks/:taskId", updateTaskStatus);

// AI-specific routes
router.post("/assign", assignTechnicians);
router.post("/analyze-risks", analyzeJobRisks);
router.get("/:id/similar", getSimilarJobs);
router.get("/:id/predict-progress", predictJobProgress);
router.get("/:id/history", getJobHistory);

// Route for uploading job attachments (images)
// Uses multer middleware: 'jobImages' is the field name expected in FormData, 10 is max files
router.post("/:id/upload", upload.array("jobImages", 10), uploadJobAttachment);

module.exports = router;
