const aiService = require("../services/aiService");
const path = require("path"); // Import path module
const geminiService = require("../utils/geminiService");
const Customer = require("../models/Customer");
const Job = require("../models/Job");
const { suggestMaterials } = require("../services/aiService");
const Invoice = require("../models/Invoice");
const mongoose = require("mongoose");
const { z } = require("zod"); // Import Zod
const { withRetry, circuitBreakers } = require("../utils/retryUtils");
const {
  isRetryableError,
  getRetryDelay,
  AiServiceError,
} = require("../utils/aiErrors");
const ApiError = require("../utils/ApiError");
const logger = require("../utils/logger");
const electricalCodeLookupService = require("../services/electricalCodeLookupService"); // Import the lookup service
const { determineLookupTypeForItem } = require("../utils/parserUtils"); // Import the new type determination function
const { transformItemsForFrontend } = require("../utils/itemDataTransformer"); // Import data transformer
const fs = require("fs");
const mime = require("mime-types");

// --- Zod Schemas for Request Body Validation ---

const suggestMaterialsSchema = z.object({
  description: z.string().min(1, "Job description is required"),
  jobType: z.string().optional(),
});

const generateMaterialDescriptionSchema = z.object({
  name: z.string().min(1, "Material name is required"),
});

const clarifyDraftQuoteSchema = z.object({
  draftQuoteData: z.object({
    inputType: z.enum(["overview", "materials"]),
    inputData: z.union([z.string(), z.object({})]),
    aiQuestions: z.array(z.union([z.string(), z.object({})])),
    formData: z.object({}).optional(), // Allow additional form context
  }),
  answers: z
    .object({})
    .refine((obj) => Object.keys(obj).length > 0, {
      message: "At least one answer is required",
    }),
});

const generateQuoteContentSchema = z.object({
  inputType: z.enum(["overview", "materials"]),
  inputData: z.union([z.string(), z.object({})]), // Allow string or object
});

const analyzeJobSchema = z
  .object({
    jobData: z
      .object({
        description: z.string().optional(),
        title: z.string().optional(),
        tasks: z.array(z.any()).optional(), // Define task structure if known
      })
      .optional(),
    // Allow legacy format as fallback
    description: z.string().optional(),
    title: z.string().optional(),
    tasks: z.array(z.any()).optional(),
  })
  .refine((data) => data.jobData?.description || data.description, {
    message: "Either jobData.description or description is required",
    path: ["description"], // Path for error message
  });

const enhanceDescriptionSchema = z.object({
  description: z.string().min(1, "Description is required"),
  title: z.string().optional(),
  jobType: z.string().optional(),
});

const analyzeCompleteJobSchema = z.object({
  description: z
    .string()
    .min(5, "A more detailed job description is required (min 5 chars)"),
  title: z.string().optional(),
});

// Basic schema, assuming customer/jobHistory objects are validated elsewhere or trusted
const analyzePaymentRiskSchema = z.object({
  customer: z.object({}).passthrough(), // Allow any customer object structure for now
  amount: z.number().positive("Amount must be a positive number"),
  jobHistory: z.array(z.object({}).passthrough()).optional(), // Allow any job history structure
});

const generateFollowupStrategySchema = z.object({
  invoice: z.object({}).passthrough(), // Allow any invoice structure for now
});

const getFollowUpSuggestionsSchema = z.object({
  interaction: z
    .object({
      type: z.string().optional(),
      date: z.string().datetime().optional(),
      notes: z.string().optional(),
    })
    .optional(),
});

const filterCustomersSchema = z.object({
  query: z.string().min(1, "Query string is required"),
});

// --- Helper Functions ---

/**
 * Basic sanitization for strings included in AI prompts.
 */
function sanitizeForPrompt(input) {
  if (typeof input !== "string") return input;
  // Remove potential instruction prefixes and characters often used in injection
  let sanitized = input.replace(
    /^(ignore|disregard|forget|override|system|user|assistant):?\s*/i,
    ""
  );
  // Remove backticks, brackets, braces, angle brackets, and potentially harmful control characters
  sanitized = sanitized.replace(/[`[\]{}<>\x00-\x1F\x7F-\x9F]/g, "");
  return sanitized.trim();
}

/**
 * Get the most common value in an array
 */
function getMostCommonValue(arr) {
  if (!arr || !arr.length) return null;
  const frequency = {};
  let maxFreq = 0;
  let mostCommon;
  arr.forEach((value) => {
    if (value) {
      frequency[value] = (frequency[value] || 0) + 1;
      if (frequency[value] > maxFreq) {
        maxFreq = frequency[value];
        mostCommon = value;
      }
    }
  });
  return mostCommon || null;
}

/**
 * Generate a default follow-up template
 */
function generateDefaultTemplate(customer, interaction) {
  const name = customer.contactPerson?.firstName || "Customer"; // Handle missing contactPerson
  switch (
    interaction?.type // Handle missing interaction
  ) {
    case "quote":
      return `Hello ${name},\n\nI wanted to follow up on the quote we provided recently. Would you like to discuss any questions or proceed with the service?\n\nBest regards,\n[Your Name]`;
    case "service":
      return `Hello ${name},\n\nI hope you're satisfied with the recent service we provided. I'd love to hear your feedback and if there's anything else we can assist with.\n\nBest regards,\n[Your Name]`;
    case "complaint":
      return `Hello ${name},\n\nI wanted to follow up regarding your recent concern. Has the issue been resolved to your satisfaction? If not, please let me know how we can make things right.\n\nBest regards,\n[Your Name]`;
    default:
      return `Hello ${name},\n\nJust checking in to see how everything is going. Please let us know if you need any assistance or have questions about our services.\n\nBest regards,\n[Your Name]`;
  }
}

// --- Helper Functions for Quote Content Lookup (Adapted from quoteController) ---

const lookupKeywords = [
  "NEC",
  "conductor",
  "conduit",
  "grounding",
  "size per",
  "rated for",
  "ampacity",
];
const placeholderPattern = /\(Size per NEC.*?\)/i;

const lineNeedsLookup = (line) => {
  const lowerLine = line.toLowerCase();
  return (
    placeholderPattern.test(line) ||
    lookupKeywords.some((keyword) => lowerLine.includes(keyword))
  );
};

// Simplified context extraction for this controller - might need refinement
const determineLookupType = (line) => {
  const lowerLine = line.toLowerCase();
  // Prioritize more specific terms first
  if (lowerLine.includes("grounding electrode conductor"))
    return "NEC_grounding_electrode_conductor_size";
  if (lowerLine.includes("bonding jumper")) return "NEC_bonding_jumper_size";
  if (lowerLine.includes("service entrance conduit")) return "NEC_conduit_size";
  if (lowerLine.includes("conduit")) return "NEC_conduit_size"; // Check conduit before general conductor
  if (lowerLine.includes("service entrance conductor"))
    return "NEC_conductor_size";
  if (lowerLine.includes("feeder conductor")) return "NEC_conductor_size";
  if (lowerLine.includes("breaker") || lowerLine.includes("circuit breaker"))
    return "NEC_overcurrent_protection_size"; // Added previously
  if (lowerLine.includes("fuse")) return "NEC_overcurrent_protection_size"; // Added previously
  if (lowerLine.includes("ocpd")) return "NEC_overcurrent_protection_size"; // Added previously
  if (lowerLine.includes("conductor")) return "NEC_conductor_size"; // General conductor check last
  if (lowerLine.includes("ground rod")) return null; // Explicitly ignore ground rods for sizing lookup
  if (lowerLine.includes("breaker") || lowerLine.includes("circuit breaker"))
    return "NEC_overcurrent_protection_size";
  if (lowerLine.includes("fuse")) return "NEC_overcurrent_protection_size";
  if (lowerLine.includes("ocpd")) return "NEC_overcurrent_protection_size";
  logger.warn(
    `[AI Lookup Type] Could not determine specific lookup type for: "${line}"`
  );
  return null;
};

const extractParamsFromLine = (line, context = {}) => {
  const params = {};
  const lowerLine = line.toLowerCase();
  logger.debug(`[AI Param Extraction] Processing line: "${line}"`);

  // Ampacity (more flexible regex)
  const ampRegex = /(\d+)\s*A(mp(s)?)?(?!WG)/i;
  const ampMatch = line.match(ampRegex);
  if (ampMatch) {
    params.ampacity = parseInt(ampMatch[1], 10);
    logger.debug(`[AI Param Extraction] Ampacity found: ${params.ampacity}`);
  } else {
    logger.debug(
      `[AI Param Extraction] Ampacity regex (${ampRegex}) did not match.`
    );
  }

  // Material
  if (lowerLine.includes("copper") || lowerLine.includes(" cu ")) {
    params.material = "copper";
    logger.debug(`[AI Param Extraction] Material found: copper`);
  } else if (lowerLine.includes("aluminum") || lowerLine.includes(" al ")) {
    params.material = "aluminum";
    logger.debug(`[AI Param Extraction] Material found: aluminum`);
  } else {
    logger.debug(
      `[AI Param Extraction] Material (copper/aluminum) not explicitly mentioned.`
    );
  }
  // Defaulting logic is handled later based on lookup type

  // Temperature Rating
  const tempRegex = /(75|90)\s*°?C/i;
  const tempMatch = line.match(tempRegex);
  if (tempMatch) {
    params.temperature_rating = parseInt(tempMatch[1], 10);
    logger.debug(
      `[AI Param Extraction] Temperature Rating found: ${params.temperature_rating}C`
    );
  } else {
    logger.debug(
      `[AI Param Extraction] Temperature Rating regex (${tempRegex}) did not match.`
    );
  }

  // Conductor Details (for conduit fill)
  // Regex made more flexible: allows optional insulation type, hyphens in type, and '.' in size.
  const conductorDetailsRegex =
    /(\d+)\s*x\s*([\d./]+)\s*(kcmil|awg)\s*([\w-]+)?/i;
  const conductorMatch = line.match(conductorDetailsRegex);
  if (conductorMatch) {
    params.conductor_details = conductorMatch[0].trim(); // Trim result
    logger.debug(
      `[AI Param Extraction] Conductor Details found: ${params.conductor_details}`
    );
  } else {
    logger.debug(
      `[AI Param Extraction] Conductor Details regex (${conductorDetailsRegex}) did not match.`
    );
  }

  // Voltage (from context)
  const projectOverviewLower =
    context.inputData?.projectOverview?.toLowerCase();
  if (projectOverviewLower) {
    if (projectOverviewLower.includes("480v")) {
      params.known_voltage = "480V";
      logger.debug(`[AI Param Extraction] Voltage found in context: 480V`);
    } else if (projectOverviewLower.includes("208v")) {
      params.known_voltage = "208V";
      logger.debug(`[AI Param Extraction] Voltage found in context: 208V`);
    } else if (projectOverviewLower.includes("240v")) {
      params.known_voltage = "240V";
      logger.debug(`[AI Param Extraction] Voltage found in context: 240V`);
    } else {
      logger.debug(
        `[AI Param Extraction] Specific voltage (480/208/240V) not found in project overview context.`
      );
    }
  } else {
    logger.debug(
      `[AI Param Extraction] Project overview not available in context for voltage check.`
    );
  }

  // --- Service Conductor Size Extraction (for GEC lookups) ---
  // This requires the calling function to determine the type first and potentially pass the full materials list in context
  const currentLineLookupType = determineLookupType(line); // Assuming determineLookupType is accessible or logic is duplicated
  // Pass the full AI response in context for this check
  if (
    currentLineLookupType === "NEC_grounding_electrode_conductor_size" &&
    context.aiResponse?.materialsIncluded
  ) {
    logger.debug(
      `[AI Param Extraction] Current line is for GEC. Searching context for Service Entrance Conductor size...`
    );
    const materialsLines = context.aiResponse.materialsIncluded.split("\n");
    const serviceLine = materialsLines.find((l) =>
      l.toLowerCase().includes("service entrance conductor")
    );
    if (serviceLine) {
      logger.debug(
        `[AI Param Extraction] Found potential service line: "${serviceLine}"`
      );
      // Attempt to extract size like "4/0 AWG Copper" or "500 kcmil Aluminum"
      const serviceSizeRegex = /([\d/]+\s*(kcmil|awg))\s*(copper|aluminum)?/i;
      const serviceMatch = serviceLine.match(serviceSizeRegex);
      if (serviceMatch) {
        params.service_conductor_size = serviceMatch[0].trim();
        // Ensure material is included if matched
        if (
          !params.service_conductor_size.toLowerCase().includes("copper") &&
          !params.service_conductor_size.toLowerCase().includes("aluminum") &&
          serviceMatch[3]
        ) {
          params.service_conductor_size += ` ${serviceMatch[3]}`;
        }
        logger.info(
          `[AI Param Extraction] Extracted Service Conductor Size for GEC context: ${params.service_conductor_size}`
        );
      } else {
        logger.warn(
          `[AI Param Extraction] Found service line, but could not extract size using regex (${serviceSizeRegex}).`
        );
      }
    } else {
      logger.warn(
        `[AI Param Extraction] Could not find 'service entrance conductor' line in context materials for GEC lookup.`
      );
    }
  }
  // --- End Service Conductor Size Extraction ---

  logger.debug(
    `[AI Param Extraction] Final Extracted Params for line "${line}": ${JSON.stringify(
      params
    )}`
  );
  return params;
};

// ---------------------------------------------------------------------------

// --- Controller Functions ---

/**
 * Suggest materials for a job based on description
 */
async function suggestMaterialsForJob(req, res, next) {
  try {
    const validationResult = suggestMaterialsSchema.safeParse(req.body);
    if (!validationResult.success) {
      throw new ApiError(400, "Invalid input", validationResult.error.errors);
    }
    const { description, jobType } = validationResult.data;

    const sanitizedDescription = sanitizeForPrompt(description);
    const suggestions = await aiService.suggestMaterials(
      sanitizedDescription,
      jobType
    );
    res.json({ suggestions });
  } catch (error) {
    logger.error("Error suggesting materials:", error);
    next(error);
  }
}

/**
 * Generate AI description for a material
 */
async function generateMaterialDescription(req, res, next) {
  try {
    const validationResult = generateMaterialDescriptionSchema.safeParse(
      req.body
    );
    if (!validationResult.success) {
      throw new ApiError(400, "Invalid input", validationResult.error.errors);
    }
    const { name } = validationResult.data;

    const sanitizedName = sanitizeForPrompt(name);
    const description = await aiService.generateDescriptionForMaterial(
      sanitizedName
    );
    res.json({ success: true, description });
  } catch (error) {
    logger.error("Error generating material description:", error);
    next(error);
  }
}

/**
 * Generate quote content without requiring a saved quote
 */
async function generateQuoteContent(req, res, next) {
  try {
    // Start comprehensive AI generation logging
    logger.startAiGenerationLogging();

    logger.aiGeneration("=== AI Quote Generation Started ===", {
      inputType: req.body?.inputType,
      hasFiles: !!(req.files && req.files.length > 0),
      fileCount: req.files ? req.files.length : 0,
    });

    logger.aiGenerationDebug(
      "[AI Controller] generateQuoteContent called. req.files:",
      req.files
    );
    logger.aiGenerationDebug("[AI Controller] req.body:", req.body); // Log the body received from multer

    // Extract data directly from req.body (populated by multer for multipart/form-data)
    const { inputType, inputData: rawInputData } = req.body;

    // Basic validation for required fields from FormData
    if (!inputType || !rawInputData) {
      logger.error(
        "[AI Controller] Missing inputType or inputData in FormData body.",
        { body: req.body }
      );
      throw new ApiError(
        400,
        "Missing required fields: inputType or inputData."
      );
    }

    // Handle inputData parsing for materials type - support both JSON arrays and raw text
    let inputData;
    if (inputType === "materials" && typeof rawInputData === "string") {
      // Try to parse as JSON first (for structured data from form items)
      try {
        inputData = JSON.parse(rawInputData);
        logger.aiGenerationDebug(
          "[AI Controller] Successfully parsed materials inputData as JSON array"
        );
      } catch (parseError) {
        // If JSON parsing fails, treat as raw text input (from materials textbox)
        inputData = rawInputData.trim();
        logger.aiGenerationDebug(
          "[AI Controller] Using materials inputData as raw text string",
          {
            textLength: inputData.length,
            preview: inputData.substring(0, 100),
          }
        );

        // Validate that we have meaningful text content
        if (!inputData || inputData.length < 3) {
          throw new ApiError(
            400,
            "Materials input must contain at least 3 characters of meaningful content."
          );
        }
      }
    } else {
      inputData = rawInputData; // Use as is for 'overview' or if already an object somehow
    }
    logger.aiGenerationDebug(
      `[AI Controller] Input type: ${inputType}, Parsed inputData (first 200 chars): ${
        typeof inputData === "string"
          ? inputData.substring(0, 200)
          : JSON.stringify(inputData).substring(0, 200)
      }`
    );

    // 🚀 STREAMING MODE DETECTION - Check for X-Streaming-Mode header
    const isStreamingMode = req.headers["x-streaming-mode"] === "true";
    logger.aiGeneration(
      `[AI Controller] Streaming mode detected: ${isStreamingMode}`,
      {
        streamingHeader: req.headers["x-streaming-mode"],
        userAgent: req.headers["user-agent"]?.substring(0, 50),
      }
    );

    // If streaming mode is requested, delegate to streaming service
    if (isStreamingMode) {
      logger.aiGeneration(
        "[AI Controller] Delegating to streaming service for real-time generation"
      );

      try {
        // Import streaming dependencies
        const { v4: uuidv4 } = require("uuid");
        const streamingAiService = require("../services/streamingAiService");

        // Convert FormData to streaming-compatible format
        const formDataMap = new Map();

        // Add form fields
        formDataMap.set("inputType", inputType);
        formDataMap.set("inputData", rawInputData);

        // Add other body fields if present
        Object.entries(req.body).forEach(([key, value]) => {
          if (key !== "inputType" && key !== "inputData") {
            formDataMap.set(key, value);
          }
        });

        // Add file references for streaming (files will be processed by streaming service)
        if (req.files && req.files.length > 0) {
          formDataMap.set("files", req.files);
          formDataMap.set("fileCount", req.files.length);
        }

        // Add user context for session tracking
        formDataMap.set("userId", req.user?.id);
        formDataMap.set("userAgent", req.headers["user-agent"]);
        formDataMap.set("ipAddress", req.ip);

        // Generate unique session ID for this streaming request
        const sessionId = uuidv4();

        logger.aiGeneration(
          `[AI Controller] Creating streaming session ${sessionId}`,
          {
            sessionId,
            userId: req.user?.id,
            hasFiles: !!(req.files && req.files.length > 0),
            fileCount: req.files ? req.files.length : 0,
          }
        );

        // Create streaming session
        const createdSessionId = streamingAiService.createSession(
          sessionId,
          formDataMap
        );

        // Verify the session was actually created
        if (createdSessionId !== sessionId) {
          throw new Error(
            `Session creation mismatch: requested ${sessionId}, got ${createdSessionId}`
          );
        }

        logger.aiGeneration(
          `[AI Controller] Streaming session ${sessionId} created successfully`,
          {
            sessionId,
            userId: req.user?.id,
            hasFiles: !!(req.files && req.files.length > 0),
          }
        );

        // Wait longer for session to be fully initialized before returning
        await new Promise((resolve) => setTimeout(resolve, 500));

        // Verify session was created successfully with enhanced retry logic
        let verifySession = streamingAiService.getSession(sessionId);
        let retryCount = 0;
        const maxRetries = 5;

        while (!verifySession && retryCount < maxRetries) {
          logger.warn(
            `[AI Controller] Session ${sessionId} not found, retry ${
              retryCount + 1
            }/${maxRetries}`
          );
          // Exponential backoff: 100ms, 200ms, 400ms, 800ms, 1600ms
          const delay = 100 * Math.pow(2, retryCount);
          await new Promise((resolve) => setTimeout(resolve, delay));
          verifySession = streamingAiService.getSession(sessionId);
          retryCount++;
        }

        if (!verifySession) {
          throw new Error(
            `Session creation failed - session ${sessionId} not found after ${maxRetries} retries`
          );
        }

        // Additional verification that session is in registry
        const sessionStats = streamingAiService.getSessionStats();
        if (!sessionStats || sessionStats.activeSessions === 0) {
          logger.error(
            `[AI Controller] Session registry appears empty after creating ${sessionId}`
          );
          throw new Error("Session registry validation failed");
        }
        logger.aiGeneration(
          `[AI Controller] Session ${sessionId} verified and ready for SSE connection`,
          {
            sessionId,
            sessionExists: !!verifySession,
            activeSessions: sessionStats.activeSessions,
            sessionStatus: verifySession?.status,
          }
        );

        // Return session details for frontend to connect to SSE endpoint
        return res.status(200).json({
          success: true,
          sessionId: sessionId,
          sseUrl: `/api/streaming/quote-generation/${sessionId}`,
          message:
            "Streaming session created. Connect to SSE endpoint for real-time updates.",
          mode: "streaming",
          status: "ready",
          expiresAt: new Date(Date.now() + 30 * 60 * 1000).toISOString(), // 30 minutes
          timestamp: new Date().toISOString(),
        });
      } catch (streamingError) {
        logger.error(
          "[AI Controller] Streaming session creation failed, falling back to traditional mode:",
          {
            error: streamingError.message,
            stack: streamingError.stack?.substring(0, 500),
          }
        );

        // Fallback to traditional mode processing
        logger.aiGeneration(
          "[AI Controller] Continuing with traditional AI generation due to streaming error"
        );
        // Continue to traditional processing below...
      }
    }

    // Enhanced System Prompt for Detailed Analysis with Improved Image Processing - Now with instructions to avoid image references
    const systemPrompt = `You are a highly experienced electrical estimator tasked with creating a detailed quote based on provided information, which may include text descriptions and images.

IMPORTANT: DO NOT reference any image file names, image numbers, or use phrases like "as shown in image 1" or "as per image IMG-20250527-WA0034" in your final quote content. The quote must be professional and client-ready without direct references to submitted images.

Perform a step-by-step analysis (chain-of-thought) considering all inputs:

1.  **Analyze Images in Extreme Detail:**
    * Carefully examine any provided images pixel by pixel.
    * Identify ALL visible equipment with extreme specificity (exact panel brand/type, meter type, subpanels, etc.)
    * Document EVERY visible detail about wiring condition (wire gauge, insulation type, color, visible damage)
    * Note ALL visible hazards (rust, water damage, exposed wires, burn marks, etc.)
    * Measure corrosion levels on a scale of 1-5 and explain your rating
    * Estimate age of equipment based on visible characteristics and explain your reasoning
    * Read and transcribe ALL visible labels, markings, and text in the image (breaker labels, warning stickers, amperage ratings)
    * Identify specific NEC code violations visible in the image, if any
    * Note the physical environment surrounding the equipment (indoor/outdoor, wall material, space constraints)
    * Identify any unusual or non-standard installations that require special attention

2.  **Analyze Text:** Correlate your detailed image findings with the text description (\${inputType}). Extract key requirements, customer requests, and project goals.

3.  **Synthesize & Recommend:** Based on the combined analysis, determine the most likely and appropriate electrical upgrade or service needed. Propose specific solutions with exact specifications (e.g., 'Replace existing 200A Federal Pacific panel with a new 400A Siemens G4040MB1200 load center and multi-gang meter base'). Include specific brand recommendations when possible.

4.  **Itemize Deliverables:** Based on your recommendation, create a comprehensive list of ALL specific materials, components, and distinct labor activities required. 
    
    CRITICAL DISTINCTION - You MUST properly categorize items:
    
    **CRITICAL: For EVERY item, you MUST include a "category" field that classifies the item as one of:**
    - "electrical_material" - Physical electrical components that can be purchased
    - "labor" - Time-based work activities and services  
    - "non_electrical_material" - Construction materials like wood, drywall, paint
    - "administrative" - Permits, fees, coordination, documentation
    
    **CRITICAL CATEGORIZATION RULES - READ CAREFULLY:**
    - ALL physical electrical components MUST be categorized as "electrical_material" - this is CRITICAL for price lookups
    - Examples of electrical materials: wires, cables, conduits, panels, breakers, outlets, switches, fixtures, devices, fittings, connectors, etc.
    - Work activities, installation tasks, and time-based services MUST be categorized as "labor"
    - Examples of labor: installation, removal, testing, commissioning, project management, etc.
    - Construction materials (wood, drywall, paint) MUST be categorized as "non_electrical_material"
    - Permits, fees, and administrative tasks MUST be categorized as "administrative"
    
    **COMMON MISTAKE TO AVOID:** Do NOT categorize physical electrical components like panels, breakers, wires, outlets, etc. as "labor" - these are materials that need price lookups.
    
    **HOW TO CLASSIFY ELECTRICAL ITEMS:** If an item is something you can purchase at Home Depot or Platt Electric, it's an "electrical_material". If it's work performed by an electrician that takes time, it's "labor".
    
    **MATERIALS (Physical Electrical Components) - THESE GET PRICE LOOKUPS:**
    - These are EXAMPLES ONLY - use your understanding to classify ANY item appropriately:
      * Wire/Cable: "4/0 AWG THHN Copper Wire", "12 AWG Romex Cable", "500 kcmil Aluminum SER Cable"
      * Conduit: "3/4 inch EMT Conduit", "2 inch PVC Schedule 40 Conduit", "1 inch Flexible Metal Conduit"
      * Panels/Breakers: "200A Main Breaker Panel", "20A Single Pole Circuit Breaker", "100A Sub Panel"
      * Devices: "20A GFCI Outlet", "3-Way Light Switch", "LED Dimmer Switch"
      * Fittings: "3/4 inch EMT Connector", "2 inch PVC Elbow", "Ground Rod Clamp"
      * And ANY other electrical items: photocells, emergency lights, exit signs, smoke detectors, etc.
    
    **LABOR (Work Activities) - THESE DO NOT GET PRICE LOOKUPS:**
    - Include all time-based work activities
    - Examples: "Installation of new panel (8 hours)", "Demolition of existing wiring (4 hours)", "Testing and commissioning (2 hours)", "Project management (1 hour)"
    - Always use "hours" or "hrs" as the unit
    - Never suggest price lookups for labor
    
    **IMPORTANT:** If an item can be purchased from an electrical supplier, it's a material. If it's work performed by a technician, it's labor.
    
    For each item:
    *   Provide a clear, detailed 'description' with brand recommendations when applicable
    *   Estimate the precise 'quantity' and specify the 'unit' (e.g., feet, pcs, hours for labor)
    *   For MATERIAL items: Detail ALL relevant 'attributes' such as material type (Copper, Aluminum), ampacity (100A), size (2 AWG), specific type (THHN, SER), conduit type (EMT, PVC), conduit size (3/4 inch), voltage rating, temperature rating, etc. If an attribute is not applicable, omit it.
    *   For LABOR items: Use "hours" or "hrs" as the unit. Do NOT suggest price lookups for labor items.
    *   'lookup_query_suggestion' field:
        - For MATERIALS: Suggest a specific query for fetching current pricing (e.g., "price of 100ft 2 AWG THHN copper wire")
        - For LABOR: Either omit this field or set to null/empty string - labor rates are not looked up automatically

5.  **Generate Detailed Scope of Work:** Based on the comprehensive analysis of the project overview (provided as user input), image details, and your recommended solution, construct a 'detailed_scope_of_work'. This section must:\n    *   Be written in clear, professional language suitable for a client quote.\n    *   NEVER include references to specific image filenames, image numbers, or phrases like "as shown in image X" in your content.\n    *   Use Markdown for formatting (e.g., H2 for main title '## Scope of Work', H3 for subsections like '### Key Tasks', '### Deliverables', '### Exclusions (if any)', bullet points for lists).\n    *   Elaborate significantly on the tasks and deliverables involved in executing the recommended solution. Do not be generic; be specific.\n    *   Directly reference and expand upon details found in the original project overview and your image analysis findings to create a tailored scope, but without mentioning the images themselves.\n    *   Clearly outline the sequence of major work phases if applicable (e.g., Demolition, Installation, Testing, Commissioning).\n    *   Ensure content is distinct from the 'overall_summary'. The 'detailed_scope_of_work' is the granular 'what and how' of the work to be performed.\n\n6.  **Summarize and Assess Confidence:** Provide a detailed 'overall_summary' of the project, including your recommendation, key justifications, and observations that informed your decisions. Remember: do NOT reference image filenames or numbers in this summary. Also, provide an 'ai_confidence_score' (a numerical value between 0.0 and 1.0) reflecting your confidence in the accuracy and completeness of the generated quote based on the input provided.

**Output Format:**
Respond ONLY with a valid JSON object matching this structure. Ensure all string values are properly escaped for JSON. CRITICAL: Do not include ANY references to image filenames, image numbers (e.g., "image 1"), or phrases like "as seen in the provided image" anywhere in the JSON output - this is a client-facing professional document.
{
  "items": [
    {
      "description": "Description of the material, component, or labor activity",
      "category": "electrical_material | labor | non_electrical_material | administrative",
      "quantity": 1,
      "unit": "e.g., feet, pcs, hours",
      "attributes": {
        "material": "e.g., Copper, Aluminum",
        "ampacity": "e.g., 100A",
        "size": "e.g., 2 AWG, 4/0",
        "type": "e.g., THHN, SER",
        "conduit_type": "e.g., EMT, PVC",
        "conduit_size": "e.g., 3/4 inch"
        // Only include applicable attributes
      },
      "lookup_query_suggestion": "Suggested query for price/code lookup for this item"
    }
    // Add more items as necessary
  ],
  "detailed_scope_of_work": "A comprehensive, Markdown-formatted scope of work derived from the project overview and AI analysis, detailing tasks, deliverables, and phases. Example: ## Scope of Work\nThis project involves the complete overhaul of...\n### Key Tasks\n- Task 1\n- Task 2\n### Deliverables\n- Deliverable A",
  "overall_summary": "Detailed overview including analysis findings, justification for recommendation, and proposed solution.",
  "ai_confidence_score": 0.85 // Example confidence score
}
Do not include your chain-of-thought analysis in the final JSON output. Stick strictly to the JSON structure provided.`;

    const sanitizedInputData =
      typeof inputData === "string" ? sanitizeForPrompt(inputData) : inputData;
    // Ensure complex objects are stringified for the prompt if not already strings
    const userInput =
      typeof sanitizedInputData === "string"
        ? sanitizedInputData
        : JSON.stringify(sanitizedInputData, null, 2);
    logger.aiGenerationDebug(
      `[AI Controller] Final userInput for Gemini (first 300 chars): ${userInput.substring(
        0,
        300
      )}`
    );

    // Log summary request information (reduced verbosity)
    logger.aiGenerationDebug(
      `[AI Controller] Request summary - URL: ${req.originalUrl}, Files: ${
        req.files ? req.files.length : 0
      }, Content-Type: ${req.headers["content-type"] || "unknown"}`
    );
    // Full headers only in extreme debug mode
    if (process.env.LOG_LEVEL === "silly") {
      logger.aiGenerationDebug(
        "[AI Controller] Full request headers:",
        req.headers
      );
    }

    // Prepare options for Gemini service, including image details if present
    const geminiOptions = {};
    if (req.files && req.files.length > 0) {
      logger.info(
        `[AI Controller] Found ${req.files.length} files in the request.`
      );

      // Log details about each file
      req.files.forEach((file, index) => {
        logger.aiGenerationDebug(`[AI Controller] File ${index + 1} details:`, {
          fieldname: file.fieldname,
          originalname: file.originalname,
          encoding: file.encoding,
          mimetype: file.mimetype,
          size: file.size,
          path: file.path,
        });
      });

      // Enhanced image processing to extract more information
      const fs = require("fs");
      const path = require("path");
      const enhancedImageDetails = [];

      for (const file of req.files) {
        try {
          // Read the image file
          const imageBuffer = fs.readFileSync(file.path);

          // Basic image details
          const imageDetail = {
            path: file.path,
            mimeType: file.mimetype,
            originalName: file.originalname,
            size: file.size,
          };

          // Add to enhanced image details
          enhancedImageDetails.push(imageDetail);

          logger.aiGenerationDebug(
            `[AI Controller] Enhanced image details for ${file.originalname}:`,
            imageDetail
          );
        } catch (imageError) {
          logger.error(
            `[AI Controller] Error processing image ${file.originalname}:`,
            imageError
          );
          // Still add the basic details even if enhanced processing fails
          enhancedImageDetails.push({
            path: file.path,
            mimeType: file.mimetype,
          });
        }
      }

      geminiOptions.imageDetails = enhancedImageDetails;
      logger.info(
        `[AI Controller] Passing ${geminiOptions.imageDetails.length} enhanced image(s) to Gemini service.`
      );
      logger.aiGenerationDebug(
        `[AI Controller] Gemini imageDetails summary - Count: ${
          geminiOptions.imageDetails.length
        }, Total size: ${geminiOptions.imageDetails.reduce(
          (sum, img) => sum + (img.size || 0),
          0
        )} bytes`
      );
    } else {
      logger.info(
        "[AI Controller] No images provided or found in request for AI processing."
      );
      logger.aiGenerationDebug("[AI Controller] req.files value:", req.files);
    }

    logger.aiGeneration("Calling Gemini AI Service for quote generation", {
      inputType,
      systemPromptLength: systemPrompt.length,
      userInputLength:
        typeof userInput === "string"
          ? userInput.length
          : JSON.stringify(userInput).length,
      imageCount: req.files ? req.files.length : 0,
      hasImageDetails: !!(
        geminiOptions.imageDetails && geminiOptions.imageDetails.length > 0
      ),
    });

    logger.info(
      `[AI Controller] Attempting to call geminiService.getGeminiJsonResponse for inputType: ${inputType}. Image count: ${
        req.files ? req.files.length : 0
      }.`
    );
    const aiResponse = await geminiService.getGeminiJsonResponse(
      systemPrompt,
      userInput,
      geminiOptions
    ); // Pass options

    if (!aiResponse || typeof aiResponse !== "object") {
      logger.warn(
        `[AI Controller] Invalid AI response format received (not an object).`
      );
      throw new ApiError(
        502,
        "AI service returned an invalid, non-object format."
      );
    }
    // Directly use parsedAiResponse, assuming it's the structured JSON from getGeminiJsonResponse
    const parsedAiResponse = aiResponse;
    logger.aiGeneration("AI Response received and parsed successfully", {
      hasItems: !!(
        parsedAiResponse.items && Array.isArray(parsedAiResponse.items)
      ),
      itemCount: parsedAiResponse.items ? parsedAiResponse.items.length : 0,
      hasOverallSummary: !!parsedAiResponse.overall_summary,
      hasDetailedScope: !!parsedAiResponse.detailed_scope_of_work,
      aiConfidenceScore: parsedAiResponse.ai_confidence_score,
    });

    logger.info(
      "[AI Controller] Successfully received structured AI response."
    );
    logger.aiGenerationDebug(
      `[AI Controller] Parsed AI Response: ${JSON.stringify(
        parsedAiResponse,
        null,
        2
      )}`
    );

    // Initialize items needing price lookup (declare outside conditional to avoid scope issues)
    let itemsNeedingPriceLookup = [];

    // --- Process items for additional lookups ---
    if (
      parsedAiResponse &&
      parsedAiResponse.items &&
      Array.isArray(parsedAiResponse.items)
    ) {
      logger.info(
        `[AI Controller] Processing ${parsedAiResponse.items.length} items for price lookups...`
      );

      // Log all items for debugging
      logger.info(
        `[AI Controller] Raw AI items: ${JSON.stringify(
          parsedAiResponse.items,
          null,
          2
        )}`
      );

      const processedItems = await Promise.all(
        parsedAiResponse.items.map(async (item) => {
          item.lookup_results = item.lookup_results || []; // Ensure lookup_results array exists

          // Use AI-provided category for intelligent classification
          const category = item.category || "unknown";
          logger.info(
            `[AI Controller] Item "${item.description}": AI Category: ${category}, Suggestion: "${item.lookup_query_suggestion}"`
          );

          // Enhanced debugging to see exactly how items are being categorized
          logger.debug(
            `[AI Controller] FULL ITEM DETAILS for "${
              item.description
            }": ${JSON.stringify(item, null, 2)}`
          );

          // Log category decision making
          if (category === "electrical_material") {
            logger.info(
              `[AI Controller] ✅ CORRECTLY categorized as ELECTRICAL MATERIAL: "${item.description}"`
            );
          } else if (category === "labor") {
            logger.info(
              `[AI Controller] ⚠️  Categorized as LABOR: "${item.description}"`
            );
          } else {
            // Use info level for known categories that are not electrical_material or labor
            logger.info(
              `[AI Controller] ℹ️  Categorized as "${category}": "${item.description}"`
            );
          }

          // Determine if we need price lookup based on AI category
          const skipPriceLookup = category !== "electrical_material";
          let lookupType = null;
          let crawl4aiRequest = null;

          // For electrical materials, determine specific lookup type
          if (
            category === "electrical_material" &&
            item.lookup_query_suggestion
          ) {
            // Check if it's an NEC lookup based on description and attributes
            const lookupDetails = determineLookupTypeForItem(item);
            lookupType = lookupDetails
              ? lookupDetails.lookupType
              : "PRICE_MATERIAL_COMPONENT";
            crawl4aiRequest = lookupDetails
              ? lookupDetails.crawl4aiRequest
              : {
                  method: "searchByDescription",
                  query: item.lookup_query_suggestion,
                };
          }

          // Handle items based on AI category
          if (skipPriceLookup) {
            logger.info(
              `[AI Controller] Item "${item.description}" will SKIP price lookup. Category: ${category}`
            );
            if (category === "labor") {
              logger.info(
                `[AI Controller] Item "${item.description}" categorized as LABOR by AI - skipping price lookup`
              );
              item.lookup_results.push({
                type: "LABOR_ITEM",
                status: "labor_item_no_price_lookup",
                timestamp: new Date().toISOString(),
                reason: "Labor items do not require price lookups",
                source: "ai_category_classification",
              });
              item.priceStatus = "not_applicable";
              item.source = "Labor";
            } else if (category === "non_electrical_material") {
              logger.info(
                `[AI Controller] Item "${item.description}" categorized as NON-ELECTRICAL material by AI - including in output without price lookup`
              );
              item.lookup_results.push({
                type: "NON_ELECTRICAL_MATERIAL",
                status: "non_electrical_material_no_price",
                timestamp: new Date().toISOString(),
                reason:
                  "Non-electrical construction materials included for informational purposes",
                source: "ai_category_classification",
              });
              item.priceStatus = "not_applicable";
              item.source = "Non-Electrical";
              // Do NOT mark for removal - include in output
              // item._shouldRemove = true;
            } else if (category === "administrative") {
              logger.info(
                `[AI Controller] Item "${item.description}" categorized as ADMINISTRATIVE by AI - including in output without price lookup`
              );
              item.lookup_results.push({
                type: "ADMINISTRATIVE_ITEM",
                status: "administrative_item_no_price",
                timestamp: new Date().toISOString(),
                reason: "Administrative items included for informational purposes",
                source: "ai_category_classification",
              });
              item.priceStatus = "not_applicable";
              item.source = "Administrative";
              // Do NOT mark for removal - include in output
              // item._shouldRemove = true;
            } else if (category === "unknown" || !category) {
              logger.warn(
                `[AI Controller] Item "${item.description}" has unknown/missing category - marking as invalid`
              );
              item.lookup_results.push({
                type: "INVALID_ITEM",
                status: "invalid_item_skip",
                timestamp: new Date().toISOString(),
                reason: "Item category could not be determined by AI",
                source: "ai_category_classification",
              });
              item.priceStatus = "invalid";
              item.source = "Unknown";
              // Mark for removal
              item._shouldRemove = true;
            } else {
              // Unexpected category
              logger.warn(
                `[AI Controller] Item "${item.description}" has unexpected category: ${category}`
              );
              item.lookup_results.push({
                type: "UNEXPECTED_CATEGORY",
                status: "unexpected_category_skip",
                timestamp: new Date().toISOString(),
                reason: `Unexpected item category: ${category}`,
                source: "ai_category_classification",
              });
              item.priceStatus = "invalid";
              item.source =
                category.charAt(0).toUpperCase() + category.slice(1); // Capitalize first letter
            }
          } else if (lookupType && lookupType.startsWith("NEC_")) {
            logger.info(
              `[AI Controller] Performing NEC lookup for item: "${item.description}", Type: ${lookupType}`
            );
            try {
              const necResult =
                await electricalCodeLookupService.lookupMaterialSpecification({
                  type: lookupType,
                  params: item.attributes,
                });
              logger.debug(
                `[AI Controller] NEC lookup success for item "${item.description}":`,
                necResult
              );
              item.lookup_results.push({
                type: "NEC_LOOKUP_EXECUTED",
                status: "success",
                data: necResult,
                timestamp: new Date().toISOString(),
                original_item_type: lookupType,
              });
            } catch (necError) {
              logger.error(
                `[AI Controller] NEC lookup error for item "${item.description}": ${necError.message}`
              );
              item.lookup_results.push({
                type: "NEC_LOOKUP_EXECUTED",
                status: "error",
                error: necError.message,
                timestamp: new Date().toISOString(),
                original_item_type: lookupType,
              });
            }
          } else if (
            lookupType &&
            crawl4aiRequest &&
            item.lookup_query_suggestion
          ) {
            // Determine if this is a critical item that should get immediate price lookup
            const isCriticalItem =
              (lookupType === "PRICE_MATERIAL_COMPONENT" ||
                lookupType === "PRICE_GENERAL") &&
              (item.description.toLowerCase().includes("panel") ||
                item.description.toLowerCase().includes("meter") ||
                item.description.toLowerCase().includes("disconnect") ||
                item.description.toLowerCase().includes("breaker") ||
                item.description.toLowerCase().includes("conductor") ||
                item.description.toLowerCase().includes("wire"));

            // Item is suitable for price lookup (handled via normal deferred flow)

            // Item is suitable for further lookup
            let newStatus = "pending_external_research"; // Default for general research actions
            let logMessage = `[AI Controller] Item "${item.description}" (type: ${lookupType}) flagged for external research.`;

            if (
              lookupType === "PRICE_MATERIAL_COMPONENT" ||
              lookupType === "PRICE_GENERAL"
            ) {
              newStatus = "pending_internal_price_lookup";
              logMessage = `[AI Controller] Item "${item.description}" (type: ${lookupType}) flagged for INTERNAL backend price lookup.`;
            }

            logger.info(logMessage);

            // Fix: Move variable declaration and initialization before its first use
            let final_crawl4ai_request;

            if (
              newStatus === "pending_internal_price_lookup" ||
              newStatus === "pending_internal_price_lookup_secondary"
            ) {
              final_crawl4ai_request = {
                internal_query_details:
                  (item.lookup_query_suggestion
                    ? item.lookup_query_suggestion
                    : null) ||
                  item.description ||
                  "",
              };
              // Ensure we log what the internal lookup service will actually use
              logger.debug(
                `[AI Controller] Item "${item.description}" (type: ${lookupType}) - internal lookup query details: "${final_crawl4ai_request.internal_query_details}"`
              );
            } else {
              final_crawl4ai_request = crawl4aiRequest; // This is the original crawl4aiRequest for Crawl4AI tools
            }

            // Enhanced debugging for items being sent to Crawl4AI
            logger.info(
              `[AI Controller] 🚀 SENDING TO CRAWL4AI: "${
                item.description
              }" with query: "${
                final_crawl4ai_request?.internal_query_details || "N/A"
              }"`
            );

            item.lookup_results.push({
              type: lookupType,
              status: newStatus,
              request_data: final_crawl4ai_request, // Conditionally set (renamed from mcp_request)
              timestamp: new Date().toISOString(),
              source: "ai_suggestion_for_lookup",
            });
            // For external research, log the request. For internal, the specific detail log above is sufficient.
            if (
              newStatus !== "pending_internal_price_lookup" &&
              final_crawl4ai_request
            ) {
              logger.debug(
                `[AI Controller] External research request for item "${item.description}":`,
                final_crawl4ai_request
              );
            } else if (
              !final_crawl4ai_request &&
              newStatus === "pending_external_research"
            ) {
              // External research items without crawl4ai requests are flagged for manual research
              logger.info(
                `[AI Controller] Item "${item.description}" flagged for manual external research - no automated lookup available`
              );
            } else if (
              !final_crawl4ai_request &&
              newStatus === "pending_internal_price_lookup"
            ) {
              // This case should ideally not happen if item.description always exists, but good to log
              logger.warn(
                `[AI Controller] Internal lookup for item "${
                  item.description
                }" has no query details. Original suggestion: "${
                  item.lookup_query_suggestion
                    ? item.lookup_query_suggestion
                    : "N/A"
                }", Description: "${item.description || "N/A"}"`
              );
            }
          } else {
            // No specific NEC lookup and no actionable Crawl4AI request
            let reason =
              "Could not determine lookup type or no actionable Crawl4AI request.";
            if (lookupType && !item.lookup_query_suggestion) {
              reason = `Lookup type ${lookupType} identified, but no lookup_query_suggestion was provided by AI.`;
            } else if (
              lookupType &&
              !crawl4aiRequest &&
              item.lookup_query_suggestion
            ) {
              reason = `Lookup type ${lookupType} identified with suggestion, but crawl4aiRequest was not formulated by parserUtils.`;
            } else if (!lookupType && item.lookup_query_suggestion) {
              reason =
                "Lookup query suggestion present, but item type could not be determined by parserUtils.";
            } else if (!lookupType && !item.lookup_query_suggestion) {
              reason =
                "Neither lookup type nor query suggestion could be determined.";
            }

            logger.warn(
              `[AI Controller] Item "${
                item.description
              }" - No specific lookup action taken. Type: ${
                lookupType || "N/A"
              }, Suggestion: "${
                item.lookup_query_suggestion || "N/A"
              }", Reason: ${reason}`
            );
            item.lookup_results.push({
              type: lookupType || "UNKNOWN",
              status: "skipped_no_action",
              timestamp: new Date().toISOString(),
              reason: reason,
              query_suggestion: item.lookup_query_suggestion || null,
            });
          }
          return item;
        })
      );

      // Filter out items marked for removal (non-electrical materials and invalid items)
      const validItems = processedItems.filter((item) => !item._shouldRemove);

      if (processedItems.length !== validItems.length) {
        logger.info(
          `[AI Controller] Filtered out ${
            processedItems.length - validItems.length
          } non-electrical/invalid items from ${
            processedItems.length
          } total items`
        );
      }

      // Ensure each item has required fields for database/frontend compatibility
      parsedAiResponse.items = validItems.map((item) => {
        // Remove the temporary _shouldRemove flag
        delete item._shouldRemove;

        // Ensure item has a name field (extract from description if not present)
        if (!item.name && item.description) {
          // Extract a reasonable name from the description (first 100 chars or until comma/dash)
          const descParts = item.description.split(/[,\-]/);
          let baseName = descParts[0].trim();
          // If the first part is still too long, try to find a better truncation point
          if (baseName.length > 100) {
            // Look for a natural break point like a space
            const lastSpace = baseName.substring(0, 95).lastIndexOf(" ");
            if (lastSpace > 50) { // Only use if it's not too early
              baseName = baseName.substring(0, lastSpace) + "...";
            } else {
              baseName = baseName.substring(0, 95) + "...";
            }
          }
          item.name = baseName;
        }

        // Ensure item has a price field (default to 0 since prices are fetched later)
        if (item.price === undefined || item.price === null) {
          item.price = 0;
        }

        // Ensure other required fields have defaults
        item.quantity = item.quantity || 1;
        item.unit = item.unit || "each";
        item.currency = item.currency || "USD";

        return item;
      });

      logger.info(
        "[AI Controller] Finished processing items for additional lookups."
      );
      logger.aiGenerationDebug(
        `[AI Controller] AI Response with Lookups: ${JSON.stringify(
          parsedAiResponse,
          null,
          2
        )}`
      );

      // Note: Price lookups are triggered automatically when quotes are saved
      // via ChangeStreams (if replica set is available) or polling mechanism.
      // This ensures price lookups only happen for persisted quotes with valid IDs.
      itemsNeedingPriceLookup = parsedAiResponse.items.filter(
        (item) =>
          item.lookup_results &&
          item.lookup_results.some(
            (lr) => lr.status === "pending_internal_price_lookup"
          )
      );

      // Enhanced debugging for price lookup items
      logger.info(
        `[AI Controller] 🔍 Items needing price lookup: ${itemsNeedingPriceLookup.length}`
      );
      itemsNeedingPriceLookup.forEach((item, index) => {
        logger.info(
          `[AI Controller] 🔍 Price lookup item ${index + 1}: "${
            item.description
          }" (Category: ${item.category || "N/A"})`
        );
      });

      if (itemsNeedingPriceLookup.length > 0) {
        logger.info(
          `[AI Controller] 📋 ${itemsNeedingPriceLookup.length} items marked for deferred price lookup. Lookups will be triggered when quote is saved.`
        );
      } else {
        logger.info("[AI Controller] No items need deferred price lookup");
      }
    } else {
      logger.warn(
        "[AI Controller] No items found in parsed AI response to process for lookups, or response structure is not as expected."
      );
      // Ensure parsedAiResponse has at least an empty items array if it's missing, to avoid downstream errors.
      if (parsedAiResponse && !parsedAiResponse.items) {
        parsedAiResponse.items = [];
      }
    }

    // Generate scopeOfWork and materialsIncluded from items if they're empty
    // This ensures that even if the AI doesn't generate these sections, we still have meaningful content
    let generatedScopeOfWork =
      parsedAiResponse.detailed_scope_of_work ||
      parsedAiResponse.scopeOfWork ||
      "";
    let generatedMaterialsIncluded = parsedAiResponse.materialsIncluded || "";

    // If scopeOfWork is empty but we have items, generate it from the items
    if (
      (!generatedScopeOfWork || generatedScopeOfWork.trim() === "") &&
      parsedAiResponse.items &&
      Array.isArray(parsedAiResponse.items) &&
      parsedAiResponse.items.length > 0
    ) {
      logger.info(
        "[AI Controller] Generating scopeOfWork from items as it was empty"
      );

      // Group items by type (labor vs materials)
      const laborItems = parsedAiResponse.items.filter(
        (item) =>
          (item.description &&
            item.description.toLowerCase().includes("labor")) ||
          (item.unit && item.unit.toLowerCase().includes("hour"))
      );

      const materialItems = parsedAiResponse.items.filter(
        (item) => !laborItems.includes(item)
      );

      // Generate scope of work with tasks based on the items
      generatedScopeOfWork = "## Scope of Work\n\n";

      // Instead of duplicating the project overview, create a more specific scope introduction
      // that doesn't repeat the exact same text as the project overview
      generatedScopeOfWork +=
        "This scope of work outlines the specific tasks and deliverables for the project.\n\n";

      generatedScopeOfWork += "### Tasks to be Performed:\n\n";

      // Add labor items as tasks
      if (laborItems.length > 0) {
        laborItems.forEach((item) => {
          generatedScopeOfWork += `- ${item.description}\n`;
        });
      }

      // Add generic tasks based on material items if no labor items
      if (laborItems.length === 0 && materialItems.length > 0) {
        generatedScopeOfWork +=
          "- Procure all necessary materials and equipment\n";
        generatedScopeOfWork +=
          "- Perform installation according to industry standards and local codes\n";
        generatedScopeOfWork +=
          "- Test all installed components for proper operation\n";
        generatedScopeOfWork += "- Clean up work area upon completion\n";
      }

      generatedScopeOfWork += "\n### Deliverables:\n\n";
      generatedScopeOfWork += "- Completed installation/service as described\n";
      generatedScopeOfWork +=
        "- All work performed to code and industry standards\n";
      generatedScopeOfWork += "- Cleanup of work area\n";

      logger.aiGenerationDebug(
        "[AI Controller] Generated scopeOfWork:",
        generatedScopeOfWork
      );
    }

    // If materialsIncluded is empty but we have items, generate it from the items
    if (
      (!generatedMaterialsIncluded ||
        generatedMaterialsIncluded.trim() === "") &&
      parsedAiResponse.items &&
      Array.isArray(parsedAiResponse.items) &&
      parsedAiResponse.items.length > 0
    ) {
      logger.info(
        "[AI Controller] Generating materialsIncluded from items as it was empty"
      );

      // Filter out labor items
      const materialItems = parsedAiResponse.items.filter(
        (item) =>
          !(
            item.description && item.description.toLowerCase().includes("labor")
          ) && !(item.unit && item.unit.toLowerCase().includes("hour"))
      );

      if (materialItems.length > 0) {
        generatedMaterialsIncluded = "## Materials Included\n\n";

        materialItems.forEach((item) => {
          const quantity = item.quantity || 1;
          const unit = item.unit || "each";
          generatedMaterialsIncluded += `- ${item.description} (${quantity} ${unit})\n`;

          // Add attributes if available
          if (item.attributes && Object.keys(item.attributes).length > 0) {
            Object.entries(item.attributes).forEach(([key, value]) => {
              if (value) {
                generatedMaterialsIncluded += `  - ${key.replace(
                  /_/g,
                  " "
                )}: ${value}\n`;
              }
            });
          }
        });
      }

      logger.aiGenerationDebug(
        "[AI Controller] Generated materialsIncluded: " + generatedMaterialsIncluded
      );
    }

    // Transform AI response to the structure expected by quoteController/frontend
    // Note: According to Google Gemini documentation, the model may sometimes focus on certain parts of the prompt
    // and not generate all requested sections. This transformation ensures we have complete data regardless.
    //
    // IMPORTANT: We need to ensure that Project Overview and Scope of Work sections are not identical.
    // This is a common issue with AI-generated content where the model might duplicate content across sections.
    // The code below implements validation and fallback mechanisms to ensure each section is unique and meaningful.

    // Get the project overview from the AI response
    let finalProjectOverview =
      parsedAiResponse.overall_summary ||
      parsedAiResponse.projectOverview ||
      "";

    // Get the scope of work (either generated or from AI response)
    let finalScopeOfWork =
      generatedScopeOfWork || parsedAiResponse.scopeOfWork || "";

    // Get the materials included (either generated or from AI response)
    let finalMaterialsIncluded =
      generatedMaterialsIncluded || parsedAiResponse.materialsIncluded || "";

    // Helper function to calculate text similarity using Jaccard similarity
    function calculateSimilarity(text1, text2) {
      // Simple Jaccard similarity for text comparison
      const words1 = new Set(
        text1
          .toLowerCase()
          .split(/\s+/)
          .filter((word) => word.length > 3)
      );
      const words2 = new Set(
        text2
          .toLowerCase()
          .split(/\s+/)
          .filter((word) => word.length > 3)
      );

      const intersection = new Set([...words1].filter((x) => words2.has(x)));
      const union = new Set([...words1, ...words2]);

      return intersection.size / union.size;
    }

    // Validate that project overview and scope of work are not identical or too similar
    // This is a critical check to prevent duplication of content between sections
    // The AI model sometimes generates the same content for both sections, which creates a poor user experience
    let needsRegeneration = false;
    let regenerationReason = "";

    if (finalProjectOverview && finalScopeOfWork) {
      // Check for exact match first
      if (finalProjectOverview.trim() === finalScopeOfWork.trim()) {
        needsRegeneration = true;
        regenerationReason = "identical content";
        logger.warn(
          "[AI Controller] Project Overview and Scope of Work are identical. Modifying Scope of Work to be unique."
        );
        logger.aiGenerationDebug(
          "[AI Controller] Identical content detected: " +
            finalProjectOverview.substring(0, 100) +
            "..."
        );
      } else {
        // Check for partial duplication (first paragraph)
        const projectOverviewFirstPara = finalProjectOverview.split("\n\n")[0];
        const scopeOfWorkFirstPara = finalScopeOfWork.split("\n\n")[0];

        if (
          projectOverviewFirstPara &&
          scopeOfWorkFirstPara &&
          projectOverviewFirstPara.trim() === scopeOfWorkFirstPara.trim()
        ) {
          needsRegeneration = true;
          regenerationReason = "identical first paragraph";
          logger.warn(
            "[AI Controller] First paragraph of Project Overview and Scope of Work are identical. Modifying Scope of Work to be unique."
          );
          logger.aiGenerationDebug(
            "[AI Controller] Identical first paragraph detected: " +
              projectOverviewFirstPara.substring(0, 100) +
              "..."
          );
        } else {
          // Check for overall similarity if not exact match or identical first paragraph
          const similarity = calculateSimilarity(
            finalProjectOverview,
            finalScopeOfWork
          );
          if (similarity > 0.7) {
            // Threshold can be adjusted
            needsRegeneration = true;
            regenerationReason = `high similarity (${similarity.toFixed(2)})`;
            logger.warn(
              `[AI Controller] Project Overview and Scope of Work are too similar (${similarity.toFixed(
                2
              )}). Regenerating Scope of Work.`
            );
          }
        }
      }
    }

    // Regenerate Scope of Work if needed
    if (needsRegeneration) {
      logger.info(
        `[AI Controller] Regenerating Scope of Work due to ${regenerationReason}`
      );

      // Create a more specific scope introduction that doesn't duplicate the project overview
      // Format the Scope of Work to match the required style (plain text with specific formatting)
      finalScopeOfWork = "Scope of Work:\n\n";

      // Determine the main work type based on items or default to "Electrical Installation"
      let mainWorkType = "Electrical Installation";

      // Try to determine a more specific work type from the items if available
      if (
        parsedAiResponse.items &&
        Array.isArray(parsedAiResponse.items) &&
        parsedAiResponse.items.length > 0
      ) {
        // Look for common keywords in items to determine work type
        const allItemDescriptions = parsedAiResponse.items
          .map((item) => (item.description || item.name || "").toLowerCase())
          .join(" ");

        if (
          allItemDescriptions.includes("panel") ||
          allItemDescriptions.includes("subpanel")
        ) {
          mainWorkType = "Installation of Subpanel";
        } else if (
          allItemDescriptions.includes("circuit") ||
          allItemDescriptions.includes("breaker")
        ) {
          mainWorkType = "Circuit Installation";
        } else if (
          allItemDescriptions.includes("outlet") ||
          allItemDescriptions.includes("receptacle")
        ) {
          mainWorkType = "Outlet Installation";
        } else if (
          allItemDescriptions.includes("light") ||
          allItemDescriptions.includes("fixture")
        ) {
          mainWorkType = "Lighting Installation";
        }
      }

      finalScopeOfWork += `${mainWorkType}:\n`;

      // Add specific tasks with technical details
      // Add generic tasks if we have items
      if (
        parsedAiResponse.items &&
        Array.isArray(parsedAiResponse.items) &&
        parsedAiResponse.items.length > 0
      ) {
        // Group items by type (labor vs materials)
        const laborItems = parsedAiResponse.items.filter(
          (item) =>
            (item.description &&
              item.description.toLowerCase().includes("labor")) ||
            (item.unit && item.unit.toLowerCase().includes("hour"))
        );

        const materialItems = parsedAiResponse.items.filter(
          (item) =>
            !(
              item.description &&
              item.description.toLowerCase().includes("labor")
            ) && !(item.unit && item.unit.toLowerCase().includes("hour"))
        );

        // Add labor items as tasks with technical specifications
        if (laborItems.length > 0) {
          laborItems.forEach((item) => {
            finalScopeOfWork += `• ${item.description}\n`;
          });
        }

        // Add material-based tasks with technical specifications
        if (materialItems.length > 0) {
          // Create tasks based on material categories
          const wireItems = materialItems.filter(
            (item) =>
              (item.description || "").toLowerCase().includes("wire") ||
              (item.description || "").toLowerCase().includes("awg")
          );

          const conduitItems = materialItems.filter(
            (item) =>
              (item.description || "").toLowerCase().includes("conduit") ||
              (item.description || "").toLowerCase().includes("emt")
          );

          const boxItems = materialItems.filter(
            (item) =>
              (item.description || "").toLowerCase().includes("box") ||
              (item.description || "").toLowerCase().includes("junction")
          );

          // Add wire-related tasks
          if (wireItems.length > 0) {
            finalScopeOfWork += `• Provide and install approximately ${
              wireItems.length * 25
            } feet of appropriate gauge wire for the installation.\n`;
          }

          // Add conduit-related tasks
          if (conduitItems.length > 0) {
            finalScopeOfWork += `• Run approximately ${
              conduitItems.length * 20
            } feet of EMT conduit as required for the installation.\n`;
          }

          // Add box-related tasks
          if (boxItems.length > 0) {
            finalScopeOfWork += `• Install ${boxItems.length} junction boxes with appropriate connectors and mounting hardware.\n`;
          }
        }

        // If we don't have enough specific tasks, add generic ones
        if (laborItems.length + materialItems.length < 3) {
          finalScopeOfWork += `• Provide all necessary materials and equipment for the installation.\n`;
          finalScopeOfWork += `• Install all components according to NEC standards and local codes.\n`;
          finalScopeOfWork += `• Test all installed components for proper operation.\n`;
          finalScopeOfWork += `• Clean up work area upon completion of the installation.\n`;
        }
      } else {
        // No items available, add generic tasks with technical specifications
        finalScopeOfWork += `• Provide all necessary materials and equipment for the installation.\n`;
        finalScopeOfWork += `• Install all components according to NEC standards and local codes.\n`;
        finalScopeOfWork += `• Run appropriate conduit and wiring as required for the installation.\n`;
        finalScopeOfWork += `• Provide all necessary connectors, fittings, and mounting hardware.\n`;
        finalScopeOfWork += `• Test all installed components for proper operation.\n`;
        finalScopeOfWork += `• Clean up work area upon completion of the installation.\n`;
      }

      // Add a second section for additional work if appropriate
      finalScopeOfWork += `\nAdditional Requirements:\n`;
      finalScopeOfWork += `• All work to be performed by licensed electricians in accordance with NEC and local codes.\n`;
      finalScopeOfWork += `• Obtain all necessary permits and inspections as required by local authorities.\n`;
      finalScopeOfWork += `• Provide proper grounding and bonding for all installed equipment.\n`;
    }

    // Ensure project overview is not empty
    if (!finalProjectOverview || finalProjectOverview.trim() === "") {
      logger.info(
        "[AI Controller] Generating fallback Project Overview as it was empty"
      );
      finalProjectOverview =
        "Based on the project requirements, this quote provides a comprehensive solution that includes all necessary materials, labor, and services to complete the work according to industry standards and local codes.";
    }

    // Determine status based on questions and pending price lookups
    let responseStatus = "complete";
    if (parsedAiResponse.questions && parsedAiResponse.questions.length > 0) {
      responseStatus = "pending_questions";
    } else if (itemsNeedingPriceLookup.length > 0) {
      responseStatus = "pending_price_lookups";
    }

    const finalResponse = {
      success: true,
      status: responseStatus,
      quoteId: parsedAiResponse.quoteId || null, // Pass through if present
      generatedData: {
        projectOverview: finalProjectOverview,
        scopeOfWork: finalScopeOfWork,
        materialsIncluded: finalMaterialsIncluded,
        items: transformItemsForFrontend(parsedAiResponse.items || []), // Transform items for frontend
      },
      questions: parsedAiResponse.questions || [],
      ai_confidence_score: parsedAiResponse.ai_confidence_score,
      // Include pricing status information for frontend
      pricingStatus: {
        totalItems: parsedAiResponse.items ? parsedAiResponse.items.length : 0,
        itemsWithPricing: 0, // No immediate pricing anymore
        itemsPendingLookup: itemsNeedingPriceLookup.length,
        message:
          itemsNeedingPriceLookup.length > 0
            ? `${itemsNeedingPriceLookup.length} items will have pricing updated automatically after quote is saved`
            : "All items processed",
      },
      // Include original top-level fields from AI if they exist, for debugging or future use
      ...(parsedAiResponse.projectOverview && {
        originalProjectOverview: parsedAiResponse.projectOverview,
      }),
      ...(parsedAiResponse.scopeOfWork && {
        originalScopeOfWork: parsedAiResponse.scopeOfWork,
      }),
      ...(parsedAiResponse.materialsIncluded && {
        originalMaterialsIncluded: parsedAiResponse.materialsIncluded,
      }),
    };

    logger.aiGeneration("=== AI Quote Generation Completed Successfully ===", {
      status: finalResponse.status,
      hasQuestions: !!(
        finalResponse.questions && finalResponse.questions.length > 0
      ),
      questionCount: finalResponse.questions
        ? finalResponse.questions.length
        : 0,
      itemCount: finalResponse.generatedData?.items
        ? finalResponse.generatedData.items.length
        : 0,
      itemsPendingPriceLookup: finalResponse.pricingStatus.itemsPendingLookup,
      aiConfidenceScore: finalResponse.ai_confidence_score,
      processingTimeMs: Date.now() - (req.startTime || Date.now()),
    });

    // Log only a summary of the final response to avoid massive logs
    logger.aiGenerationDebug(
      `[AI Controller] Final response summary - Status: ${
        finalResponse.status
      }, Items: ${
        finalResponse.generatedData?.items?.length || 0
      }, Questions: ${finalResponse.questions?.length || 0}, Confidence: ${
        finalResponse.ai_confidence_score
      }`
    );
    // Full response only in extreme debug mode
    if (process.env.LOG_LEVEL === "silly") {
      logger.aiGenerationDebug(
        `[AI Controller] Full final response: ${JSON.stringify(
          finalResponse,
          null,
          2
        )}`
      );
    }

    // Stop comprehensive AI generation logging
    logger.stopAiGenerationLogging();

    res.status(200).json(finalResponse);
  } catch (error) {
    logger.aiGeneration("=== AI Quote Generation Failed ===", {
      error: error.message,
      errorType: error.constructor.name,
      processingTimeMs: Date.now() - (req.startTime || Date.now()),
    });

    logger.error(`Error during AI quote content generation:`, {
      message: error.message,
      stack: error.stack,
    });

    // Stop comprehensive AI generation logging even on error
    logger.stopAiGenerationLogging();

    next(error);
  }
}

/**
 * Clarify AI questions for draft quote without requiring saved quote
 * This endpoint allows users to get AI clarification on unsaved quote data
 */
async function clarifyDraftQuote(req, res, next) {
  try {
    console.log(
      "🎯 [DRAFT CLARIFICATION] Starting draft quote clarification process"
    );

    // Validate request body
    const validationResult = clarifyDraftQuoteSchema.safeParse(req.body);
    if (!validationResult.success) {
      console.error(
        "❌ [DRAFT CLARIFICATION] Validation failed:",
        validationResult.error.errors
      );
      throw new ApiError(
        400,
        "Invalid input data for draft quote clarification",
        validationResult.error.errors
      );
    }

    const { draftQuoteData, answers } = validationResult.data;
    const userId = req.user?.id;

    console.log("📋 [DRAFT CLARIFICATION] Processing request:", {
      userId,
      inputType: draftQuoteData.inputType,
      questionCount: draftQuoteData.aiQuestions?.length || 0,
      answerCount: Object.keys(answers).length,
      hasFormData: !!draftQuoteData.formData,
    });

    // Check for uploaded images in the original request
    let imageInfo = "";
    if (req.files && req.files.length > 0) {
      imageInfo = `\n\nIMAGES PROVIDED: ${req.files.length} image(s) uploaded. These images show electrical equipment and conditions that should be considered in your response. Analyze these images carefully for:
- Equipment brands, types, and conditions
- Visible wiring conditions and issues
- Safety hazards and code violations
- Electrical panel details and labeling
- Installation environment and constraints

Incorporate your detailed image analysis into the final content without directly referencing the images in client-facing content.`;
    }

    // Enhanced system prompt for clarification processing
    const systemPrompt = `You are an expert electrical estimator processing user clarification answers for a quote.

Original Input Type: ${draftQuoteData.inputType}
Original Input Data: ${
      typeof draftQuoteData.inputData === "string"
        ? draftQuoteData.inputData
        : JSON.stringify(draftQuoteData.inputData)
    }${imageInfo}

Previous AI Questions: ${JSON.stringify(draftQuoteData.aiQuestions)}
User Answers: ${JSON.stringify(answers)}

Using the original input data, the uploaded images (if any), AND the user's clarification answers, generate a complete and detailed quote with the following sections. Format your response ONLY as a valid JSON object:

{
  "projectOverview": "A high-level summary of the project's purpose, goals, and context (Markdown format). This should be concise (2-3 paragraphs) and focus on WHAT the project aims to accomplish and WHY it's being done.",
  "scopeOfWork": "A detailed breakdown of all work to be performed (Markdown format). Include specific tasks, methods, safety considerations, and deliverables. Be comprehensive and professional.",
  "materialsIncluded": "A detailed list of all materials, equipment, and components (Markdown format). Include specifications, quantities, and brief descriptions. Organize by category if applicable.",
  "items": [
    {
      "name": "Item name",
      "description": "Detailed description of the work item",
      "quantity": 1,
      "unit": "each",
      "unitPrice": 0,
      "totalPrice": 0,
      "category": "electrical_material" | "labor" | "equipment" | "other",
      "lookup_query_suggestion": "Suggested search term for price lookup (only for electrical_material category)"
    }
  ]
}

IMPORTANT:
- Incorporate ALL clarification answers into the final content
- If images were provided, analyze them thoroughly and incorporate relevant details
- Be specific and detailed based on the clarification provided
- Ensure professional quality suitable for client presentation
- For electrical materials, provide realistic specifications based on clarification
- Do not include pricing information - focus on accurate specifications and descriptions`;

    console.log(
      "🤖 [DRAFT CLARIFICATION] Calling AI service for clarification processing"
    );

    // Call Gemini service to process clarification
    const geminiOptions = {
      maxTokens: 8192,
      temperature: 0.3,
    };

    // Add image details if available
    if (req.files && req.files.length > 0) {
      const fs = require("fs");
      const images = [];

      for (const file of req.files) {
        try {
          // Read the image file
          const imageBuffer = fs.readFileSync(file.path);

          images.push({
            inlineData: {
              data: imageBuffer.toString("base64"),
              mimeType: file.mimetype,
            },
          });

          console.log(
            "📸 [DRAFT CLARIFICATION] Prepared image for Gemini:",
            file.originalname
          );
        } catch (error) {
          console.error(
            "❌ [DRAFT CLARIFICATION] Error preparing image:",
            file.originalname,
            error
          );
        }
      }

      if (images.length > 0) {
        geminiOptions.imageDetails = images;
        console.log(
          "🖼️ [DRAFT CLARIFICATION] Including",
          images.length,
          "images in clarification request"
        );
      }
    }

    // Create combined input for AI processing
    const combinedInput = {
      originalInput: draftQuoteData.inputData,
      questionsAsked: draftQuoteData.aiQuestions,
      userAnswers: answers,
      formContext: draftQuoteData.formData || {},
    };

    const aiResponse = await circuitBreakers.gemini.execute(async () => {
      return await withRetry(
        async () =>
          geminiService.getGeminiResponse(
            systemPrompt,
            JSON.stringify(combinedInput),
            geminiOptions
          ),
        {
          maxRetries: 3,
          baseDelay: 1000,
          maxDelay: 5000,
          shouldRetry: isRetryableError,
          getRetryDelay: getRetryDelay,
        }
      );
    });

    console.log(
      "✅ [DRAFT CLARIFICATION] AI response received, parsing result"
    );

    // Parse AI response
    let parsedResponse;
    try {
      // Clean and parse the AI response
      const cleanedResponse = aiResponse
        .trim()
        .replace(/```json\n?|```\n?/g, "");
      parsedResponse = JSON.parse(cleanedResponse);

      console.log("📊 [DRAFT CLARIFICATION] Parsed response structure:", {
        hasProjectOverview: !!parsedResponse.projectOverview,
        hasScopeOfWork: !!parsedResponse.scopeOfWork,
        hasMaterialsIncluded: !!parsedResponse.materialsIncluded,
        itemCount: parsedResponse.items?.length || 0,
      });
    } catch (parseError) {
      console.error(
        "❌ [DRAFT CLARIFICATION] Failed to parse AI response:",
        parseError.message
      );
      console.error(
        "❌ [DRAFT CLARIFICATION] Raw response:",
        aiResponse.substring(0, 500)
      );
      throw new ApiError(500, "Failed to parse AI clarification response");
    }

    // Validate and enhance the parsed response
    const enhancedResponse = {
      projectOverview: parsedResponse.projectOverview || "",
      scopeOfWork: parsedResponse.scopeOfWork || "",
      materialsIncluded: parsedResponse.materialsIncluded || "",
      items: Array.isArray(parsedResponse.items) ? parsedResponse.items : [],
    };

    // Process items for consistent structure
    const processedItems = enhancedResponse.items.map((item, index) => ({
      name: item.name || `Item ${index + 1}`,
      description: item.description || "No description provided",
      quantity: typeof item.quantity === "number" ? item.quantity : 1,
      unit: item.unit || "each",
      unitPrice: 0, // Draft quotes don't include pricing
      totalPrice: 0, // Draft quotes don't include pricing
      category: item.category || "other",
      lookup_query_suggestion: item.lookup_query_suggestion || "",
    }));

    console.log(
      "🎉 [DRAFT CLARIFICATION] Clarification processing completed successfully"
    );

    // Return the enhanced quote data
    const finalResponse = {
      success: true,
      status: "complete",
      message: "Draft quote clarification processed successfully",
      generatedData: {
        projectOverview: enhancedResponse.projectOverview,
        scopeOfWork: enhancedResponse.scopeOfWork,
        materialsIncluded: enhancedResponse.materialsIncluded,
        items: processedItems,
      },
      clarificationApplied: true,
      answersProcessed: Object.keys(answers).length,
      timestamp: new Date().toISOString(),
    };

    res.status(200).json(finalResponse);
  } catch (error) {
    console.error(
      "❌ [DRAFT CLARIFICATION] Error during draft quote clarification:",
      {
        message: error.message,
        userId: req.user?.id,
        stack: error.stack?.substring(0, 500),
      }
    );

    logger.error("Error during draft quote clarification:", {
      message: error.message,
      stack: error.stack,
      userId: req.user?.id,
    });

    next(error);
  }
}

/**
 * Generate AI business insights for a customer
 */
const generateBusinessDescription = async (req, res, next) => {
  try {
    const customerId = req.params.id;
    if (!mongoose.Types.ObjectId.isValid(customerId)) {
      throw new ApiError(400, "Invalid customer ID format");
    }

    const customer = await Customer.findById(customerId)
      .populate("jobs")
      .populate("invoices");
    if (!customer) throw new ApiError(404, "Customer not found");

    const Company = require("../models/Company");
    const company = await Company.getPrimaryCompany();
    const businessType = company?.businessType || "Other";
    const user = req.user;
    const contractorType = user?.contractorType || "General Contractor";

    const systemPrompt = `You are a business analyst specializing in ${businessType} field service businesses. Based on the customer data provided, generate a concise, professional description of this CUSTOMER. The description should be in the third person, approximately 2-3 sentences long, and capture essential characteristics about the customer (not about your company). IMPORTANT INDUSTRY CONTEXT: - YOUR company business type: ${businessType} - YOUR contractor type: ${contractorType} CRITICAL: The customer is NOT in the ${businessType} industry - YOU are. Do NOT describe the customer as an ${businessType} company. Instead, describe the customer from the perspective of a ${businessType} service provider. Focus on the customer's needs, history, and characteristics that would be relevant to a ${businessType} service provider. RESPOND WITH ONLY THE DESCRIPTION TEXT, NO FORMATTING OR EXTRA TEXT. DO NOT include any markdown, line breaks, or special characters in your response. The text should be plain and ready for display in a UI component.`;

    const customerContext = {
      /* ... context object ... */
    }; // Keep context object as before

    const description = await circuitBreakers.gemini.execute(async () => {
      return await withRetry(
        async () =>
          geminiService.getGeminiResponse(
            systemPrompt,
            JSON.stringify(customerContext),
            { temperature: 0.4, maxTokens: 4000 }
          ), // Increased for complex JSON responses
        {
          maxRetries: 2,
          baseDelay: 500,
          maxDelay: 2000,
          shouldRetry: isRetryableError,
          getRetryDelay: getRetryDelay,
        }
      );
    });

    let cleanedDescription = description.trim();
    try {
      // Attempt to parse as JSON and extract if it matches known unwanted structures
      const parsed = JSON.parse(cleanedDescription);
      if (parsed && typeof parsed === "object" && parsed.customer_description) {
        // Handle {"customer_description": "..."}
        cleanedDescription = parsed.customer_description;
        logger.debug(
          "Extracted description from JSON object structure in generateBusinessDescription"
        );
      } else if (
        Array.isArray(parsed) &&
        parsed.length > 0 &&
        typeof parsed[0] === "object" &&
        parsed[0].description
      ) {
        // Handle ["description": "..."] - Check if it's an array, get first element, check for 'description' key
        cleanedDescription = parsed[0].description;
        logger.debug(
          "Extracted description from JSON structure in generateBusinessDescription"
        );
      }
    } catch (e) {
      // Parsing failed, assume it's not the JSON structure we're looking for
      // Proceed with existing markdown/whitespace cleaning
      cleanedDescription = cleanedDescription
        .replace(/\*\*/g, "")
        .replace(/\*/g, "")
        .replace(/\n\n/g, " ")
        .replace(/\n/g, " ")
        .replace(/^"(.*)"$/g, "$1");
    }
    // Final whitespace cleanup
    cleanedDescription = cleanedDescription.replace(/\s+/g, " ").trim();
    return res.json({ description: cleanedDescription });
  } catch (error) {
    logger.error("Error generating business description:", {
      message: error.message,
      stack: error.stack,
      customerId: req.params.id,
    });
    next(error);
  }
};

/**
 * Generate customer insights using AI
 */
const generateCustomerInsights = async (req, res, next) => {
  try {
    const customerId = req.params.id;
    if (!mongoose.Types.ObjectId.isValid(customerId)) {
      throw new ApiError(400, "Invalid customer ID format");
    }

    const customer = await Customer.findById(customerId)
      .populate({
        path: "jobs",
        options: { sort: { createdAt: -1 }, limit: 10 },
      })
      .populate({
        path: "invoices",
        options: { sort: { issueDate: -1 }, limit: 10 },
      });
    if (!customer) throw new ApiError(404, "Customer not found");

    const Company = require("../models/Company");
    const company = await Company.getPrimaryCompany();
    const businessType = company?.businessType || "Other";
    const user = req.user;
    const contractorType = user?.contractorType || "General Contractor";

    const systemPrompt = `You are a business intelligence specialist for a field service company in the ${businessType} industry. Based on the customer data provided, generate 3-5 business insights about this customer. Each insight should be actionable and help the service provider better understand and serve the customer. IMPORTANT INDUSTRY CONTEXT: - Company business type: ${businessType} - User contractor type: ${contractorType} Your insights should be specifically relevant to the ${businessType} industry and ${contractorType} specialization. Consider industry best practices, common customer patterns, and specialized needs for this field. RESPOND WITH A JSON ARRAY OF STRINGS, each string being one insight. Format as: ["Insight 1", "Insight 2", "Insight 3"]`;

    const customerContext = {
      /* ... context object ... */
    }; // Keep context object as before

    const insightsResponse = await circuitBreakers.gemini.execute(async () => {
      return await withRetry(
        async () =>
          geminiService.getGeminiJsonResponse(systemPrompt, customerContext, {
            temperature: 0.3,
            maxTokens: 4000,
          }), // Increased for complex JSON responses
        {
          maxRetries: 2,
          baseDelay: 500,
          maxDelay: 2000,
          shouldRetry: isRetryableError,
          getRetryDelay: getRetryDelay,
        }
      );
    });

    const insights = Array.isArray(insightsResponse) ? insightsResponse : [];
    return res.json({ insights });
  } catch (error) {
    logger.error("Error generating customer insights:", {
      message: error.message,
      stack: error.stack,
      customerId: req.params.id,
    });
    next(error);
  }
};

/**
 * Suggest services for a customer
 */
const suggestServices = async (req, res, next) => {
  try {
    const customerId = req.params.id;
    if (!mongoose.Types.ObjectId.isValid(customerId)) {
      throw new ApiError(400, "Invalid customer ID format");
    }
    const user = req.user;
    if (!user) throw new ApiError(401, "Authentication required");

    const customer = await Customer.findById(customerId).populate({
      path: "jobs",
      options: { limit: 10 },
    });
    if (!customer) throw new ApiError(404, "Customer not found");

    const Company = require("../models/Company");
    const company = await Company.getPrimaryCompany();
    const businessType = company?.businessType || "Other";

    const systemPrompt = `You are a professional service suggestion assistant for a field service company. Based on the customer data provided, suggest relevant services that YOUR company, which specializes in ${businessType}, could offer this customer. Focus ONLY on services directly related to ${businessType}. Do NOT suggest services outside of the ${businessType} field (e.g., if the business type is Electrical, do not suggest Plumbing or HVAC unless it's directly related electrical work for those systems). Provide a response as a JSON array of objects, where each object has "service" (string, name of the service) and "description" (string, brief explanation). Format: [{"service": "...", "description": "..."}, ...]`;
    const customerContext = {
      /* ... context object ... */
    }; // Keep context object as before

    const servicesResponse = await circuitBreakers.gemini.execute(async () => {
      return await withRetry(
        async () =>
          geminiService.getGeminiJsonResponse(systemPrompt, customerContext, {
            temperature: 0.4,
            maxTokens: 4000,
          }), // Increased for complex JSON responses
        {
          maxRetries: 2,
          baseDelay: 500,
          maxDelay: 2000,
          shouldRetry: isRetryableError,
          getRetryDelay: getRetryDelay,
        }
      );
    });

    const services = Array.isArray(servicesResponse) ? servicesResponse : [];
    return res.json({ services });
  } catch (error) {
    logger.error("Error in suggestServices:", {
      message: error.message,
      stack: error.stack,
      customerId: req.params.id,
    });
    next(error);
  }
};

/**
 * Generate follow-up suggestions for a customer
 */
const getFollowUpSuggestions = async (req, res, next) => {
  try {
    const validationResult = getFollowUpSuggestionsSchema.safeParse(req.body);
    if (!validationResult.success) {
      throw new ApiError(400, "Invalid input", validationResult.error.errors);
    }
    const { interaction } = validationResult.data;

    const customerId = req.params.id;
    if (!mongoose.Types.ObjectId.isValid(customerId)) {
      throw new ApiError(400, "Invalid customer ID format");
    }

    const customer = await Customer.findById(customerId);
    if (!customer) throw new ApiError(404, "Customer not found");

    const Company = require("../models/Company");
    const company = await Company.getPrimaryCompany();
    const businessType = company?.businessType || "Other";
    const user = req.user;
    const contractorType = user?.contractorType || "General Contractor";

    const systemPrompt = `You are a customer relationship specialist for a ${businessType} company (contractor type: ${contractorType}). Based on the provided customer data and last interaction details, generate a follow-up suggestion.
RESPOND ONLY WITH A VALID JSON OBJECT containing the following keys:
- "suggestedFollowUpDate": (string) An ISO 8601 date string (e.g., "YYYY-MM-DD") for the suggested follow-up. THIS FIELD IS MANDATORY.
- "messageTemplate": (string) A concise message template for the follow-up.
- "channels": (array of strings) Suggested communication channels (e.g., ["email", "phone"]).
- "priority": (string) The priority level ('high', 'normal', 'low').
CRITICAL: Ensure the entire response is ONLY the JSON object, starting with '{' and ending with '}'. Do not include any other text, explanations, or markdown formatting. The 'suggestedFollowUpDate' field MUST be present.`;

    const contextData = {
      /* ... context object, using sanitized interaction.notes ... */
      customer: {
        /* ... */
      },
      interaction: {
        type: interaction?.type || "general",
        date: interaction?.date || new Date().toISOString(),
        notes: sanitizeForPrompt(interaction?.notes || ""),
      },
      industryContext: {
        /* ... */
      },
    };

    let followUpResponse;
    try {
      followUpResponse = await circuitBreakers.gemini.execute(async () => {
        return await withRetry(
          async () =>
            geminiService.getGeminiJsonResponse(systemPrompt, contextData, {
              temperature: 0.3,
              maxTokens: 4000,
              topP: 0.7,
            }),
          {
            maxRetries: 2,
            baseDelay: 500,
            maxDelay: 2000,
            shouldRetry: isRetryableError,
            getRetryDelay: getRetryDelay,
          }
        );
      });

      // Validate the core structure first
      if (!followUpResponse || typeof followUpResponse !== "object") {
        logger.error(
          "Received completely invalid follow-up response from AI (not an object or null).",
          { response: followUpResponse }
        );
        throw new ApiError(502, "AI service returned an unusable response.");
      }

      // Check for mandatory date, but allow fallback if template exists
      if (!followUpResponse.suggestedFollowUpDate) {
        logger.warn("AI follow-up response missing suggestedFollowUpDate.", {
          response: followUpResponse,
        });
        // Allow proceeding if at least a message template is present
        if (!followUpResponse.messageTemplate) {
          logger.error(
            "AI follow-up response missing both suggestedFollowUpDate and messageTemplate."
          );
          throw new ApiError(
            502,
            "AI service failed to provide key follow-up details."
          );
        }
      }

      // Construct the response object safely
      const validatedResponse = {
        suggestedFollowUpDate: followUpResponse?.suggestedFollowUpDate || null, // Use optional chaining
        messageTemplate:
          followUpResponse?.messageTemplate ||
          generateDefaultTemplate(customer, interaction), // Use optional chaining
        channels:
          Array.isArray(followUpResponse?.channels) &&
          followUpResponse.channels.length > 0
            ? followUpResponse.channels
            : ["email", "phone"], // Use optional chaining
        priority:
          followUpResponse?.priority ||
          (interaction?.type === "complaint" ? "high" : "normal"), // Use optional chaining
      };

      // Validate length AFTER constructing the object safely
      if (
        validatedResponse.messageTemplate &&
        validatedResponse.messageTemplate.length > 500
      ) {
        validatedResponse.messageTemplate =
          validatedResponse.messageTemplate.substring(0, 497) + "...";
      }

      return res.json(validatedResponse);
    } catch (aiError) {
      // Catch errors from geminiService call or validation checks above
      logger.error(
        "Error during AI follow-up suggestion generation or validation:",
        {
          message: aiError.message,
          stack: aiError.stack,
          customerId: customerId,
          originalError: aiError.originalError, // Include original error if available from AiServiceError types
        }
      );
      // Pass a generic but informative error to the centralized handler
      // The centralized handler will determine the final status code (e.g., 502 Bad Gateway)
      throw new ApiError(
        502,
        `Failed to generate follow-up suggestion: ${aiError.message}`
      );
    }
  } catch (error) {
    logger.error("Error generating follow-up suggestions:", {
      message: error.message,
      stack: error.stack,
      customerId: req.params.id,
    });
    next(error);
  }
};

/**
 * Use AI to filter customers based on natural language criteria
 */
const filterCustomersByAI = async (req, res, next) => {
  try {
    const validationResult = filterCustomersSchema.safeParse(req.body);
    if (!validationResult.success) {
      throw new ApiError(400, "Invalid input", validationResult.error.errors);
    }
    const { query } = validationResult.data;

    const sanitizedQuery = sanitizeForPrompt(query);

    const customers = await Customer.find(
      {},
      { businessName: 1, contactPerson: 1, address: 1, tags: 1 }
    );

    const Company = require("../models/Company");
    const company = await Company.getPrimaryCompany();
    const businessType = company?.businessType || "Other";
    const user = req.user;
    const contractorType = user?.contractorType || "General Contractor";

    const systemPrompt = `You are a data analyst... Filter this list based on the query: "${sanitizedQuery}"... RESPOND WITH A JSON ARRAY...`; // Keep prompt as before

    const filteredIdsResponse = await circuitBreakers.gemini.execute(
      async () => {
        return await withRetry(
          async () =>
            geminiService.getGeminiJsonResponse(
              systemPrompt,
              {
                query: sanitizedQuery,
                customers: customers.map((c) => ({
                  id: c._id.toString(),
                  name: c.businessName,
                  tags: c.tags,
                })), // Send only relevant fields
              },
              { temperature: 0.1, maxTokens: 4000 }
            ),
          {
            maxRetries: 1,
            baseDelay: 500,
            maxDelay: 2000,
            shouldRetry: isRetryableError,
            getRetryDelay: getRetryDelay,
          }
        );
      }
    );

    const filteredIds = Array.isArray(filteredIdsResponse)
      ? filteredIdsResponse
      : [];
    const filteredCustomers = customers.filter((customer) =>
      filteredIds.includes(customer._id.toString())
    );

    return res.json({
      customers: filteredCustomers,
      matchCount: filteredCustomers.length,
      criteria: query,
    });
  } catch (error) {
    logger.error("Error filtering customers by AI:", {
      message: error.message,
      stack: error.stack,
      query,
    });
    next(error); // Pass error to centralized handler
  }
};

// --- NEW Controller Functions (Moved from aiRoutes.js) ---

/**
 * Analyze job data for AI suggestions
 */
const analyzeJob = async (req, res, next) => {
  try {
    // Validate request body
    const validationResult = analyzeJobSchema.safeParse(req.body);
    if (!validationResult.success) {
      throw new ApiError(400, "Invalid input", validationResult.error.errors);
    }
    // Use validated data, preferring jobData if present
    const jobInput = validationResult.data.jobData || {
      description: validationResult.data.description,
      title: validationResult.data.title,
      tasks: validationResult.data.tasks,
    };

    let analysis = {
      /* ... default analysis structure ... */
    };

    if (jobInput && jobInput.description) {
      try {
        const enhancementResult = await aiService.enhanceJobDescription(
          sanitizeForPrompt(jobInput.description),
          sanitizeForPrompt(jobInput.title || ""),
          jobInput.type || ""
        );
        analysis.enhancedDescription = enhancementResult.enhancedDescription;
        analysis.technicalTerms = enhancementResult.technicalTerms || [];
        analysis.chainOfThought = enhancementResult.chainOfThought || {};
      } catch (enhancementError) {
        logger.error(
          "Error enhancing job description within analyzeJob:",
          enhancementError
        );
        analysis.enhancedDescription = jobInput.description;
        analysis.technicalTerms = [];
        analysis.chainOfThought = {
          error: "Enhancement failed, using original description",
        };
      }
    } else {
      analysis.enhancedDescription = "No description provided";
      analysis.technicalTerms = [];
      analysis.chainOfThought = { error: "No job description provided" };
    }
    return res.json(analysis);
  } catch (error) {
    logger.error("AI analysis error in controller:", error);
    next(error);
  }
};

/**
 * Use AI to enhance a job description
 */
const enhanceDescription = async (req, res, next) => {
  try {
    const validationResult = enhanceDescriptionSchema.safeParse(req.body);
    if (!validationResult.success) {
      throw new ApiError(400, "Invalid input", validationResult.error.errors);
    }
    const { description, title, jobType } = validationResult.data;

    const enhancementResult = await aiService.enhanceJobDescription(
      sanitizeForPrompt(description),
      sanitizeForPrompt(title || ""),
      jobType || ""
    );
    return res.json(enhancementResult);
  } catch (error) {
    logger.error("Error enhancing description in controller:", error);
    next(error); // Pass all errors to central handler
  }
};

/**
 * Use AI to analyze job description and suggest title and duration
 */
const analyzeCompleteJob = async (req, res, next) => {
  try {
    const validationResult = analyzeCompleteJobSchema.safeParse(req.body);
    if (!validationResult.success) {
      throw new ApiError(400, "Invalid input", validationResult.error.errors);
    }
    const { description, title } = validationResult.data;

    const analysisResult = await aiService.analyzeJobDescription(
      sanitizeForPrompt(description),
      sanitizeForPrompt(title || "")
    );
    return res.json(analysisResult);
  } catch (error) {
    logger.error("AI job analysis error in controller:", error);
    next(error);
  }
};

/**
 * Analyze payment risk for customer
 */
const analyzePaymentRisk = async (req, res, next) => {
  try {
    const validationResult = analyzePaymentRiskSchema.safeParse(req.body);
    if (!validationResult.success) {
      throw new ApiError(400, "Invalid input", validationResult.error.errors);
    }
    const { customer, amount, jobHistory } = validationResult.data;

    // Deeper sanitization/validation might be needed for customer/jobHistory if they contain user-modifiable fields used in prompts
    const analysis = await aiService.analyzePaymentRisk({
      customer,
      amount,
      jobHistory: jobHistory || [],
    });
    res.json(analysis);
  } catch (error) {
    logger.error("Payment Risk Analysis Error in controller:", error);
    next(error);
  }
};

/**
 * Generate follow-up strategy for invoice
 */
const generateFollowupStrategy = async (req, res, next) => {
  try {
    const validationResult = generateFollowupStrategySchema.safeParse(req.body);
    if (!validationResult.success) {
      throw new ApiError(400, "Invalid input", validationResult.error.errors);
    }
    const { invoice } = validationResult.data;

    // Assuming invoice data is internally generated or validated elsewhere.
    const strategy = await aiService.generateFollowUpStrategy(invoice);
    res.json(strategy);
  } catch (error) {
    logger.error("Follow-up Strategy Error in controller:", error);
    next(error);
  }
};

/**
 * Analyze a customer image using AI
 * @param {Object} req - Request object with image URL
 * @param {Object} res - Response object
 * @param {Function} next - Next middleware function
 */
const analyzeCustomerImage = async (req, res, next) => {
  try {
    const { imageUrl, imageId, customerId } = req.body;

    if (!imageUrl) {
      throw new ApiError(400, "Image URL is required");
    }

    logger.info(`Analyzing customer image: ${imageUrl}`);

    // Construct the full path to the image file
    const imagePath = path.join(__dirname, "..", imageUrl);

    // Check if file exists
    if (!fs.existsSync(imagePath)) {
      throw new ApiError(404, "Image file not found");
    }

    // Read the image file
    const imageBuffer = fs.readFileSync(imagePath);

    // Get image MIME type
    const mimeType = mime.lookup(imagePath) || "image/jpeg";

    // Prepare image details for Gemini
    const imageDetails = [
      {
        path: imageUrl, // Relative path for geminiService
        mimeType,
      },
    ];

    // Extract EXIF data from image if available
    let exifData = null;
    try {
      // Use the jpeg-exif library to extract EXIF data
      const exifLib = require("jpeg-exif");
      exifData = exifLib.fromBuffer(imageBuffer);
      logger.debug(
        `EXIF data extracted from image: ${JSON.stringify(exifData)}`
      );
    } catch (exifError) {
      logger.warn(
        `Could not extract EXIF data from image: ${exifError.message}`
      );
    }

    // Extract location data if available in EXIF
    let locationData = null;
    if (exifData && exifData.GPSInfo) {
      try {
        const gpsInfo = exifData.GPSInfo;
        if (gpsInfo.GPSLatitude && gpsInfo.GPSLongitude) {
          // Convert GPS coordinates from EXIF format to decimal degrees
          const latRef = gpsInfo.GPSLatitudeRef || "N";
          const lngRef = gpsInfo.GPSLongitudeRef || "E";

          // Parse latitude and longitude arrays
          const lat = gpsInfo.GPSLatitude;
          const lng = gpsInfo.GPSLongitude;

          if (
            Array.isArray(lat) &&
            Array.isArray(lng) &&
            lat.length === 3 &&
            lng.length === 3
          ) {
            // Convert from degrees, minutes, seconds to decimal degrees
            const latDecimal =
              (lat[0] + lat[1] / 60 + lat[2] / 3600) *
              (latRef === "N" ? 1 : -1);
            const lngDecimal =
              (lng[0] + lng[1] / 60 + lng[2] / 3600) *
              (lngRef === "E" ? 1 : -1);

            locationData = {
              latitude: latDecimal,
              longitude: lngDecimal,
              formatted: `${Math.abs(latDecimal).toFixed(
                6
              )}° ${latRef}, ${Math.abs(lngDecimal).toFixed(6)}° ${lngRef}`,
            };

            logger.info(
              `Location data extracted from image: ${locationData.formatted}`
            );
          }
        }
      } catch (gpsError) {
        logger.warn(`Error parsing GPS data from EXIF: ${gpsError.message}`);
      }
    }

    // Define enhanced system prompt for image analysis
    const systemPrompt = `You are an expert electrical contractor assistant analyzing customer images.
    Analyze the provided image and extract the following information:

    1. TITLE: Generate a concise, descriptive title for this image (5-10 words) that captures its main subject
    2. DESCRIPTION: Write a detailed description of what's shown in the image (2-3 sentences)
    3. CATEGORY: Determine the most appropriate category for this image from the following options:
       - 'before': Shows equipment/wiring before work was done (old, damaged, outdated)
       - 'after': Shows completed work (new installations, repairs, upgrades)
       - 'inspection': Shows an inspection process or findings
       - 'diagram': Contains electrical diagrams, plans or schematics
       - 'equipment': Shows specific electrical equipment or components
       - 'damage': Shows damage to electrical systems
       - 'hazard': Shows safety hazards or code violations
       - 'invoice': Contains invoice or receipt information
       - 'other': Doesn't fit any of the above categories
    4. COMPONENTS: Identify all visible electrical components, equipment, or systems
    5. CONCERNS: List any safety concerns, code violations, or issues that need attention
    6. LOCATION: If visible in the image, identify the location type (e.g., residential kitchen, commercial panel room)
    7. METADATA: Extract any visible text that indicates dates, times, addresses, or other metadata

    Respond with a JSON object containing these fields:
    - title: A concise, descriptive title for the image
    - description: A detailed description of the image content
    - category: One of the categories listed above
    - components: Array of electrical components visible
    - concerns: Array of potential safety concerns or code violations (empty array if none)
    - location: Location type if identifiable (null if not)
    - metadata: Object containing any extracted metadata like dates, addresses, etc.
    - confidence: A number from 0-1 indicating your confidence in this analysis`;

    // Call Gemini with image
    const geminiOptions = {
      imageDetails,
      maxTokens: 1024,
      temperature: 0.2,
    };

    const aiResponse =
      await require("../utils/geminiService").getGeminiJsonResponse(
        systemPrompt,
        "Please analyze this image.",
        geminiOptions
      );

    if (!aiResponse || typeof aiResponse !== "object") {
      throw new ApiError(500, "Invalid AI response format");
    }

    // Add EXIF location data to the AI response if available
    if (locationData) {
      if (!aiResponse.metadata) {
        aiResponse.metadata = {};
      }
      aiResponse.metadata.gpsLocation = locationData;
    }

    // Map the enhanced category to the standard category for backward compatibility
    let standardCategory = "other";
    if (
      aiResponse.category === "before" ||
      aiResponse.category === "damage" ||
      aiResponse.category === "hazard"
    ) {
      standardCategory = "before";
    } else if (aiResponse.category === "after") {
      standardCategory = "after";
    }

    // If customerId and imageId are provided, update the image with AI analysis
    if (customerId && imageId) {
      try {
        const customer = await Customer.findById(customerId);
        if (customer && customer.customerImages) {
          const imageIndex = customer.customerImages.findIndex(
            (img) => img._id.toString() === imageId
          );
          if (imageIndex !== -1) {
            // Update image with AI analysis
            customer.customerImages[imageIndex].title =
              aiResponse.title || customer.customerImages[imageIndex].title;
            customer.customerImages[imageIndex].description =
              aiResponse.description;
            // Don't override the user-selected category unless it's 'other' and we have a better suggestion
            if (customer.customerImages[imageIndex].category === "other") {
              // Map AI category to standard category if possible
              const categoryMapping = {
                before: "before",
                after: "after",
                inspection: "other",
                diagram: "other",
                equipment: "other",
                damage: "before",
                hazard: "before",
                invoice: "other",
              };
              // Use the mapping or default to 'other'
              customer.customerImages[imageIndex].category =
                categoryMapping[aiResponse.category] || "other";
            }
            customer.customerImages[imageIndex].aiCategory =
              aiResponse.category;
            customer.customerImages[imageIndex].aiComponents =
              aiResponse.components;
            customer.customerImages[imageIndex].aiConcerns =
              aiResponse.concerns;
            customer.customerImages[imageIndex].aiConfidence =
              aiResponse.confidence;
            customer.customerImages[imageIndex].aiLocation =
              aiResponse.location;
            customer.customerImages[imageIndex].aiMetadata =
              aiResponse.metadata;
            customer.customerImages[imageIndex].aiAnalyzedAt = new Date();

            // Save the customer document
            await customer.save();
            logger.info(
              `Updated customer image ${imageId} with enhanced AI analysis`
            );
            // Return the updated image object
            const updatedImage = customer.customerImages[imageIndex];
            logger.info(`Returning updated image data for ${imageId}`);
            return res.json({
              success: true,
              updatedImage: updatedImage, // Send the updated image back
              analysis: aiResponse, // Also include the original analysis for debugging
            });
          } else {
            logger.warn(
              `Image index ${imageIndex} not found after attempting update for image ${imageId}`
            );
            // Fallback: return original analysis if update failed to find index
            return res.json({
              success: true,
              analysis: aiResponse,
              error: "Image not found in database",
              imageId: imageId,
            });
          }
        } else {
          logger.warn(
            `Customer or customerImages not found for update operation. Customer ID: ${customerId}`
          );
          // Fallback: return original analysis if customer/images not found
          return res.json({
            success: true,
            analysis: aiResponse,
            error: "Customer or customer images not found",
            customerId: customerId,
          });
        }
      } catch (updateError) {
        logger.error(
          `Error updating customer image with AI analysis: ${updateError.message}`
        );
        // Fallback: return original analysis if update fails
        return res.json({
          success: false,
          analysis: aiResponse,
          error: "Database update error",
          updateError: updateError.message,
          customerId: customerId,
          imageId: imageId,
        });
      }
    } else {
      // If no customerId/imageId provided, just return the analysis
      return res.json({
        success: true,
        analysis: aiResponse,
      });
    }
  } catch (error) {
    logger.error(`Error analyzing customer image: ${error.message}`, {
      stack: error.stack,
    });
    next(error);
  }
};

// --- AI Material Scraping Functions ---

/**
 * Initiate AI material scraping for a quote item
 * @param {Object} req - Request with quoteId, itemId, and search options
 * @param {Object} res - Response object
 * @param {Function} next - Next middleware function
 */
const initiateAiMaterialScraping = async (req, res, next) => {
  try {
    const { quoteId, itemId } = req.params;
    const { searchQuery, toolId, limit, url } = req.body;

    logger.info(
      `[aiController] Initiating AI material scraping for quote ${quoteId}, item ${itemId}`
    );

    // Validate inputs
    if (!quoteId || !mongoose.Types.ObjectId.isValid(quoteId)) {
      throw new ApiError(400, "Valid quoteId is required");
    }

    if (!itemId) {
      throw new ApiError(400, "Valid itemId is required");
    }

    // Import the service dynamically to avoid circular dependencies
    const priceLookupService = require("../services/priceLookupService");

    // Initiate the material scraping
    const requestId = await priceLookupService.initiateAiMaterialScraping(
      quoteId,
      itemId,
      {
        searchQuery,
        toolId: toolId || "mcp4_brave_web_search", // Default to Brave search if not specified
        limit: limit || 10,
        url,
        userId: req.user ? req.user._id : null,
      }
    );

    res.status(200).json({
      success: true,
      message: "AI material scraping initiated successfully",
      requestId,
      quoteId,
      itemId,
    });
  } catch (error) {
    logger.error(
      `[aiController] Error initiating AI material scraping: ${error.message}`,
      { stack: error.stack }
    );
    next(error);
  }
};

/**
 * Handle user selection of a material option
 * @param {Object} req - Request with quoteId, itemId, and optionIndex
 * @param {Object} res - Response object
 * @param {Function} next - Next middleware function
 */
const selectMaterialOption = async (req, res, next) => {
  try {
    const { quoteId, itemId } = req.params;
    const { optionIndex } = req.body;

    logger.info(
      `[aiController] Handling material option selection for quote ${quoteId}, item ${itemId}, option ${optionIndex}`
    );

    // Validate inputs
    if (!quoteId || !mongoose.Types.ObjectId.isValid(quoteId)) {
      throw new ApiError(400, "Valid quoteId is required");
    }

    if (!itemId) {
      throw new ApiError(400, "Valid itemId is required");
    }

    if (optionIndex === undefined || optionIndex === null) {
      throw new ApiError(400, "Option index is required");
    }

    // Import the service dynamically to avoid circular dependencies
    const priceLookupService = require("../services/priceLookupService");

    // Handle the material option selection
    await priceLookupService.handleMaterialOptionSelection(
      quoteId,
      itemId,
      optionIndex,
      req.user ? req.user._id : null
    );

    res.status(200).json({
      success: true,
      message: "Material option selected successfully",
      quoteId,
      itemId,
      optionIndex,
    });
  } catch (error) {
    logger.error(
      `[aiController] Error selecting material option: ${error.message}`,
      { stack: error.stack }
    );
    next(error);
  }
};

/**
 * Get the status of AI material options for a quote item
 * @param {Object} req - Request with quoteId and itemId
 * @param {Object} res - Response object
 * @param {Function} next - Next middleware function
 */
const getMaterialOptions = async (req, res, next) => {
  try {
    const { quoteId, itemId } = req.params;

    logger.info(
      `[aiController] Getting material options for quote ${quoteId}, item ${itemId}`
    );

    // Validate inputs
    if (!quoteId || !mongoose.Types.ObjectId.isValid(quoteId)) {
      throw new ApiError(400, "Valid quoteId is required");
    }

    if (!itemId) {
      throw new ApiError(400, "Valid itemId is required");
    }

    // Get the quote document with the specific item
    const Quote = require("../models/Quote");
    const quote = await Quote.findById(quoteId);

    if (!quote) {
      throw new ApiError(404, "Quote not found");
    }

    const item = quote.items.id(itemId);

    if (!item) {
      throw new ApiError(404, "Item not found in quote");
    }

    // Return the material options, lookup status, and selected option if any
    res.status(200).json({
      success: true,
      quoteId,
      itemId,
      lookup_status: item.lookup_status || "not_initiated",
      material_options: item.material_options || [],
      selected_option: item.selected_option || null,
      lookup_results: item.lookup_results || [],
      lookup_query_suggestion: item.lookup_query_suggestion || "",
    });
  } catch (error) {
    logger.error(
      `[aiController] Error getting material options: ${error.message}`,
      { stack: error.stack }
    );
    next(error);
  }
};

// Explicitly assigning functions to module.exports
module.exports = {
  generateBusinessDescription,
  generateCustomerInsights,
  suggestServices,
  getFollowUpSuggestions,
  filterCustomersByAI,
  suggestMaterialsForJob,
  generateMaterialDescription,
  generateQuoteContent,
  clarifyDraftQuote, // NEW: Draft quote clarification
  // Add new functions
  analyzeJob,
  enhanceDescription,
  analyzeCompleteJob,
  analyzePaymentRisk,
  generateFollowupStrategy,
  analyzeCustomerImage,
  // AI Material Scraping functions
  initiateAiMaterialScraping,
  selectMaterialOption,
  getMaterialOptions,
};
