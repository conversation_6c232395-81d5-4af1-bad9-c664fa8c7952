/**
 * Streaming Routes for Real-time AI Quote Generation
 *
 * Implements Server-Sent Events (SSE) for progressive quote generation
 * Based on ai-system.md recommendation to reduce perceived latency from 140s to 1-3s
 */

const express = require("express");
const router = express.Router();
const { v4: uuidv4 } = require("uuid");
const streamingAiService = require("../services/streamingAiService");
const { protect } = require("../middleware/auth");
const logger = require("../utils/logger");

// Session configuration
const SESSION_CONFIG = {
  maxAge: 30 * 60 * 1000, // 30 minutes
  maxIdleTime: 10 * 60 * 1000, // 10 minutes
  heartbeatInterval: 15000, // 15 seconds
  gracePeriod: 30000, // 30 seconds
};

/**
 * @route   GET /api/streaming/session/:sessionId/status
 * @desc    Get session status for verification
 * @access  Private
 */
router.get("/session/:sessionId/status", protect, async (req, res) => {
  try {
    const { sessionId } = req.params;

    logger.debug(`[StreamingRoutes] Session status check for ${sessionId}`);

    const sessionStats = streamingAiService.getSessionStats();

    logger.debug(`[StreamingRoutes] Current session stats:`, {
      activeSessions: sessionStats.activeSessions,
      totalSessions: sessionStats.totalSessions,
    });

    const session = streamingAiService.getSession(sessionId);
    if (!session) {
      logger.warn(
        `[StreamingRoutes] Session ${sessionId} not found during status check`,
        {
          sessionId,
          activeSessions: sessionStats.activeSessions,
          timestamp: new Date().toISOString(),
        }
      );

      return res.status(404).json({
        success: false,
        error: "Session not found or expired",
        code: "session_not_found",
        sessionId: sessionId,
        activeSessions: sessionStats.activeSessions,
        timestamp: new Date().toISOString(),
      });
    }

    const sessionStatus = streamingAiService.getSessionStatus(sessionId);

    logger.debug(
      `[StreamingRoutes] Session ${sessionId} found with status: ${sessionStatus?.status}`
    );

    res.json({
      success: true,
      sessionId,
      status: sessionStatus?.status || "unknown",
      progress: sessionStatus?.progress || 0,
      ready: true,
      activeSessions: sessionStats.activeSessions,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    logger.error(
      `[StreamingRoutes] Error checking session status for ${req.params.sessionId}:`,
      error
    );
    res.status(500).json({
      success: false,
      error: "Internal server error",
      message: error.message,
    });
  }
});

/**
 * 🆕 PHASE 4: POST /api/streaming/session/:sessionId/answer-clarification
 * @desc    Submit answers to AI clarification questions in streaming mode
 * @access  Private
 */
router.post(
  "/session/:sessionId/answer-clarification",
  protect,
  async (req, res) => {
    try {
      const { sessionId } = req.params;
      const { answers } = req.body;

      logger.info(
        `[StreamingRoutes] Processing clarification answers for session ${sessionId}`,
        {
          userId: req.user?.id,
          answersCount: answers ? Object.keys(answers).length : 0,
        }
      );

      // Validate input
      if (
        !answers ||
        typeof answers !== "object" ||
        Object.keys(answers).length === 0
      ) {
        return res.status(400).json({
          success: false,
          error: "Answers object is required and must not be empty",
          code: "invalid_answers",
        });
      }

      // Check if session exists
      const session = streamingAiService.getSession(sessionId);

      if (!session) {
        logger.warn(
          `[StreamingRoutes] Session ${sessionId} not found for clarification answers`
        );
        return res.status(404).json({
          success: false,
          error: "Session not found or expired",
          code: "session_not_found",
          sessionId: sessionId,
        });
      }

      // Check if session is awaiting clarification
      if (!session.awaitingClarification) {
        logger.warn(
          `[StreamingRoutes] Session ${sessionId} is not awaiting clarification`
        );
        return res.status(400).json({
          success: false,
          error: "Session is not awaiting clarification",
          code: "not_awaiting_clarification",
          sessionStatus: session.status,
        });
      }

      // Validate that we have answers for the questions
      const questions = session.clarificationQuestions || [];
      const providedAnswers = Object.keys(answers);

      logger.debug(`[StreamingRoutes] Clarification validation:`, {
        questionsCount: questions.length,
        answersCount: providedAnswers.length,
        questionIds: questions.map((_, idx) => `question_${idx}`),
        answerIds: providedAnswers,
      });

      // Check that all questions have answers (flexible key matching)
      const missingAnswers = [];
      for (let i = 0; i < questions.length; i++) {
        const questionKey = `question_${i}`;
        if (
          !answers[questionKey] ||
          typeof answers[questionKey] !== "string" ||
          answers[questionKey].trim() === ""
        ) {
          missingAnswers.push(questionKey);
        }
      }

      if (missingAnswers.length > 0) {
        logger.warn(
          `[StreamingRoutes] Missing answers for session ${sessionId}:`,
          missingAnswers
        );
        return res.status(400).json({
          success: false,
          error: "Please answer all questions before continuing",
          code: "incomplete_answers",
          missingAnswers: missingAnswers,
        });
      }

      // Process the clarification answers (this will resume streaming)
      await streamingAiService.processClarificationAnswers(sessionId, answers);

      logger.info(
        `[StreamingRoutes] Successfully processed clarification answers for session ${sessionId}`
      );

      // Return success response
      res.json({
        success: true,
        message:
          "Clarification answers processed successfully. Streaming will resume automatically.",
        sessionId: sessionId,
        answersProcessed: Object.keys(answers).length,
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      logger.error(
        `[StreamingRoutes] Error processing clarification answers for ${req.params.sessionId}:`,
        error
      );
      res.status(500).json({
        success: false,
        error: "Failed to process clarification answers",
        message: error.message,
        code: "processing_error",
      });
    }
  }
);

/**
 * SSE endpoint for streaming quote generation
 * GET /api/streaming/quote-generation/:sessionId
 */
router.get("/quote-generation/:sessionId", protect, async (req, res) => {
  const { sessionId } = req.params;

  logger.info(
    `[StreamingRoutes] Starting SSE connection for session ${sessionId}`,
    {
      userId: req.user?.id,
      ip: req.ip,
    }
  );

  // Enhanced session validation with recovery attempt
  let session = streamingAiService.getSession(sessionId);
  if (!session) {
    logger.warn(
      `[StreamingRoutes] Session ${sessionId} not found - attempting recovery`,
      {
        sessionId,
        userId: req.user?.id,
        userAgent: req.headers["user-agent"],
        timestamp: new Date().toISOString(),
      }
    );

    // Try session recovery
    const recoveryResult = streamingAiService.recoverSession(sessionId);

    if (recoveryResult.found) {
      logger.info(
        `[StreamingRoutes] Session ${sessionId} recovered successfully`,
        recoveryResult.details
      );
      session = recoveryResult.session;
    } else {
      logger.error(
        `[StreamingRoutes] Session ${sessionId} recovery failed`,
        recoveryResult.details
      );

      // Enhanced error response with recovery details
      const sessionStats = streamingAiService.getSessionStats
        ? streamingAiService.getSessionStats()
        : {};

      res.writeHead(404, {
        "Content-Type": "application/json",
        "X-Session-Status": "not_found",
        "X-Active-Sessions": sessionStats.activeSessions || 0,
        "X-Retry-After": "60",
        "X-Recovery-Status": recoveryResult.status,
      });
      res.end(
        JSON.stringify({
          error: "Session not found or expired",
          code: "session_not_found",
          sessionId: sessionId,
          suggestion:
            "Please start a new quote generation session. If this issue persists, try refreshing the page.",
          timestamp: new Date().toISOString(),
          retryAfter: 60,
          recoveryAttempted: true,
          recoveryDetails: recoveryResult.details,
          supportInfo: {
            activeSessions: sessionStats.activeSessions || 0,
            maxSessions: 50,
            canRetry: true,
            possibleCauses: recoveryResult.details.possibleCauses || [],
          },
        })
      );
      return;
    }
  }

  const now = Date.now();
  const sessionAge = now - session.startTime;
  const lastActivity = session.lastActivity || session.startTime;
  const idleTime = now - lastActivity;

  // Check session expiration with detailed error information
  if (
    sessionAge > SESSION_CONFIG.maxAge ||
    idleTime > SESSION_CONFIG.maxIdleTime
  ) {
    const expirationType =
      sessionAge > SESSION_CONFIG.maxAge ? "max_age_exceeded" : "idle_timeout";
    const remainingTime =
      expirationType === "max_age_exceeded"
        ? 0
        : Math.max(0, SESSION_CONFIG.maxIdleTime - idleTime);

    logger.warn(
      `[StreamingRoutes] Session ${sessionId} expired (age: ${sessionAge}ms, idle: ${idleTime}ms, type: ${expirationType})`
    );
    streamingAiService.cleanupSession(sessionId);

    res.writeHead(410, {
      "Content-Type": "application/json",
      "X-Session-Status": "expired",
      "X-Expiration-Type": expirationType,
    });
    res.end(
      JSON.stringify({
        error: "Session expired",
        code: "session_expired",
        reason: expirationType,
        details: {
          sessionAge: Math.round(sessionAge / 1000), // in seconds
          idleTime: Math.round(idleTime / 1000), // in seconds
          maxAge: Math.round(SESSION_CONFIG.maxAge / 1000),
          maxIdle: Math.round(SESSION_CONFIG.maxIdleTime / 1000),
          remainingTime: Math.round(remainingTime / 1000),
        },
        suggestion:
          expirationType === "max_age_exceeded"
            ? "Your session has reached the maximum duration limit. Please start a new quote generation."
            : "Your session has been idle for too long. Please start a new quote generation to continue.",
        timestamp: new Date().toISOString(),
        canRetry: true,
      })
    );
    return;
  }

  // Set enhanced SSE headers according to MDN spec
  res.writeHead(200, {
    "Content-Type": "text/event-stream",
    "Cache-Control": "no-cache, no-store, must-revalidate",
    Connection: "keep-alive",
    "X-Accel-Buffering": "no", // Disable Nginx buffering
    "Access-Control-Allow-Origin":
      req.headers.origin || "http://localhost:3000",
    "Access-Control-Allow-Credentials": "true",
    "Access-Control-Allow-Methods": "GET, POST, OPTIONS",
    "Access-Control-Allow-Headers":
      "Content-Type, Authorization, Last-Event-ID, Cache-Control",
    "Access-Control-Expose-Headers": "X-Session-ID",
    "X-Session-ID": sessionId,
  });

  // Send initial comment to establish connection
  res.write(`: SSE connection established\n\n`);

  // Send initial connection event with proper format
  res.write(`event: connected\n`);
  res.write(
    `data: ${JSON.stringify({
      type: "connection_established",
      sessionId: sessionId,
      timestamp: new Date().toISOString(),
      message: "Streaming connection established",
      serverTime: now,
      sessionAge: sessionAge,
      expiresAt: new Date(
        session.startTime + SESSION_CONFIG.maxAge
      ).toISOString(),
    })}\n\n`
  );

  // Flush immediately to ensure client receives initial data
  if (res.flush) res.flush();

  // Set up robust heartbeat with error handling
  let heartbeatInterval;
  let heartbeatCount = 0;

  const startHeartbeat = () => {
    heartbeatInterval = setInterval(() => {
      if (res.destroyed || res.finished || !res.writable) {
        logger.debug(
          `[StreamingRoutes] Stopping heartbeat for session ${sessionId} - connection closed`
        );
        clearInterval(heartbeatInterval);
        return;
      }

      try {
        heartbeatCount++;
        res.write(
          `: keepalive ${new Date().toISOString()} #${heartbeatCount}\n\n`
        );
        if (res.flush) res.flush();
      } catch (error) {
        logger.warn(
          `[StreamingRoutes] Heartbeat failed for session ${sessionId}:`,
          error.message
        );
        clearInterval(heartbeatInterval);
        streamingAiService.handleConnectionError &&
          streamingAiService.handleConnectionError(sessionId, error);
      }
    }, SESSION_CONFIG.heartbeatInterval);
  };

  const cleanup = () => {
    if (heartbeatInterval) {
      clearInterval(heartbeatInterval);
      heartbeatInterval = null;
    }
    streamingAiService.detachSSEResponse(sessionId);
  };

  // Store the SSE response with the session for sending updates
  streamingAiService.attachSSEResponse(sessionId, res);

  // Check if this session needs to start streaming
  const sessionStatus = streamingAiService.getSessionStatus(sessionId);
  if (
    sessionStatus &&
    sessionStatus.status === "initialized" &&
    !sessionStatus.streamingStarted
  ) {
    // Mark that streaming is starting to prevent duplicate starts
    sessionStatus.streamingStarted = true;

    // Start the streaming process now that SSE is connected
    logger.info(
      `[StreamingRoutes] Starting streaming for session ${sessionId} after SSE connection`
    );
    streamingAiService
      .startStreamingQuoteGeneration(sessionId)
      .catch((error) => {
        logger.error(
          `[StreamingRoutes] Streaming generation failed for ${sessionId}:`,
          error
        );
      });
  }

  // Handler for streaming chunks
  const onChunk = (data) => {
    try {
      res.write(`event: chunk\n`);
      res.write(`data: ${JSON.stringify(data)}\n\n`);
      if (res.flush) res.flush();

      // Log progress for debugging
      if (data.type === "progress") {
        logger.debug(
          `[StreamingRoutes] Progress update ${sessionId}: ${data.progress}% - ${data.message}`
        );
      }
    } catch (error) {
      logger.error(
        `[StreamingRoutes] Error sending chunk for ${sessionId}:`,
        error
      );
    }
  };

  // Handler for completion
  const onComplete = (data) => {
    try {
      res.write(`event: complete\n`);
      res.write(`data: ${JSON.stringify(data)}\n\n`);
      res.write(`event: close\n`);
      res.write(`data: ${JSON.stringify({
        type: "stream_completed",
        message: "Stream completed",
      })}

`);

      logger.info(
        `[StreamingRoutes] Session ${sessionId} completed successfully`
      );

      // Close connection after a brief delay
      setTimeout(() => {
        cleanup();
        res.end();
        streamingAiService.cleanupSession(sessionId);
      }, 100);
    } catch (error) {
      logger.error(
        `[StreamingRoutes] Error sending completion for ${sessionId}:`,
        error
      );
      res.end();
    }
  };

  // Handler for errors
  const onError = (error) => {
    try {
      res.write(`event: error\n`);
      res.write(
        `data: ${JSON.stringify({
          type: "error",
          error: error.message,
          timestamp: new Date().toISOString(),
        })}\n\n`
      );

      logger.error(`[StreamingRoutes] Session ${sessionId} error:`, error);
      cleanup();
      res.end();
      streamingAiService.cleanupSession(sessionId);
    } catch (writeError) {
      logger.error(
        `[StreamingRoutes] Error sending error response for ${sessionId}:`,
        writeError
      );
      res.end();
    }
  };

  // Enhanced client disconnect handling
  req.on("close", () => {
    logger.info(
      `[StreamingRoutes] Client disconnected from session ${sessionId} (heartbeats sent: ${heartbeatCount})`
    );
    cleanup();
  });

  req.on("aborted", () => {
    logger.info(
      `[StreamingRoutes] Client aborted connection for session ${sessionId}`
    );
    cleanup();
  });

  // Handle response errors
  res.on("error", (error) => {
    logger.error(
      `[StreamingRoutes] SSE response error for session ${sessionId}:`,
      error
    );
    cleanup();
    streamingAiService.handleConnectionError &&
      streamingAiService.handleConnectionError(sessionId, error);
  });

  res.on("close", () => {
    logger.debug(
      `[StreamingRoutes] SSE response closed for session ${sessionId}`
    );
    cleanup();
  });

  // Start heartbeat
  startHeartbeat();

  // Check if session exists and has form data
  const currentSessionStatus = streamingAiService.getSessionStatus(sessionId);
  if (!currentSessionStatus) {
    logger.error(`[StreamingRoutes] Session ${sessionId} not found`);

    // Send error event with session not found message
    res.write(`event: error\n`);
    res.write(
      `data: ${JSON.stringify({
        type: "error",
        error: "Session not found. Please start quote generation first.",
        timestamp: new Date().toISOString(),
        sessionExpired: true,
      })}\n\n`
    );

    // Send close event to signal client to stop reconnecting
    res.write(`event: close\n`);
    res.write(
      `data: ${JSON.stringify({
        type: "session_expired",
        message: "Session expired or not found",
        timestamp: new Date().toISOString(),
      })}\n\n`
    );

    // Close connection immediately
    setTimeout(() => {
      cleanup();
      res.end();
    }, 50);
    return;
  }

  // Log successful SSE connection establishment
  logger.info(
    `[StreamingRoutes] SSE connection established for session ${sessionId}`,
    {
      status: currentSessionStatus.status,
      progress: currentSessionStatus.progress,
    }
  );

  // The actual streaming will be handled by the POST endpoint that starts the process
  // This endpoint just maintains the SSE connection
});

/**
 * Start streaming quote generation
 * POST /api/streaming/start-quote-generation
 */
router.post("/start-quote-generation", protect, async (req, res) => {
  try {
    // Validate request body
    if (!req.body || Object.keys(req.body).length === 0) {
      return res.status(400).json({
        success: false,
        error: "Request body is required",
      });
    }

    // Check service capacity
    const stats = streamingAiService.getSessionStats
      ? streamingAiService.getSessionStats()
      : { activeSessions: 0 };
    const maxSessions = 50; // Default limit
    if (stats.activeSessions >= maxSessions) {
      logger.warn(
        `[StreamingRoutes] Session creation rejected - capacity limit reached (${stats.activeSessions}/${maxSessions})`
      );
      return res.status(503).json({
        success: false,
        error: "Service at capacity. Please try again later.",
        retryAfter: 60,
      });
    }

    const sessionId = uuidv4();
    const formData = req.body;

    logger.info(
      `[StreamingRoutes] Starting quote generation session ${sessionId}`,
      {
        userId: req.user?.id,
        hasProjectOverview: !!formData.projectOverview,
        hasImages: !!formData.images,
        activeSessions: stats.activeSessions + 1,
      }
    );

    // Validate input
    if (!formData.projectOverview && !formData.description) {
      return res.status(400).json({
        success: false,
        error: "Project overview or description is required",
      });
    }

    // Validate job type if provided
    const validJobTypes = [
      "electrical",
      "plumbing",
      "hvac",
      "general",
      "roofing",
      "flooring",
      "painting",
    ];
    if (formData.jobType && !validJobTypes.includes(formData.jobType)) {
      return res.status(400).json({
        success: false,
        error: "Invalid job type. Must be one of: " + validJobTypes.join(", "),
      });
    }

    // Validate required fields for complete form data
    if (
      formData.jobType !== undefined &&
      (!formData.jobType || !formData.jobType.trim())
    ) {
      return res.status(400).json({
        success: false,
        error: "Job type validation failed: cannot be empty",
      });
    }

    // Convert to FormData-like object for compatibility
    const formDataMap = new Map();
    Object.entries(formData).forEach(([key, value]) => {
      formDataMap.set(key, value);
    });
    formDataMap.get = formDataMap.get.bind(formDataMap);

    // Initialize session in streaming service
    const sessionInfo = {
      sessionId,
      startTime: Date.now(),
      status: "initialized",
      progress: 0,
      formData: formDataMap,
      userId: req.user?.id,
    };

    // Store session info in streaming service
    streamingAiService.createSession(sessionId, formDataMap);

    // Don't start streaming here - wait for SSE connection
    // The streaming will start when the client connects to the SSE endpoint
    logger.info(
      `[StreamingRoutes] Session ${sessionId} created, waiting for SSE connection`
    );

    // Return session info immediately
    res.json({
      success: true,
      sessionId: sessionId,
      sseUrl: `/api/streaming/quote-generation/${sessionId}`,
      message:
        "Quote generation started. Connect to SSE endpoint for real-time updates.",
      expiresAt: new Date(Date.now() + SESSION_CONFIG.maxAge).toISOString(),
      stats: {
        activeSessions: stats.activeSessions + 1,
        maxSessions: maxSessions,
      },
    });
  } catch (error) {
    logger.error("[StreamingRoutes] Failed to start quote generation:", error);

    if (error.message.includes("Session limit reached")) {
      return res.status(503).json({
        success: false,
        error: error.message,
        retryAfter: 60,
      });
    }

    res.status(500).json({
      success: false,
      message: "Failed to start quote generation",
      error: error.message,
    });
  }
});

/**
 * Get session status
 * GET /api/streaming/session/:sessionId/status
 */
router.get("/session/:sessionId/status", protect, (req, res) => {
  const { sessionId } = req.params;

  try {
    const status = streamingAiService.getSessionStatus(sessionId);

    if (!status) {
      return res.status(404).json({
        success: false,
        message: "Session not found or expired",
      });
    }

    const now = Date.now();
    const sessionAge = now - status.startTime;
    const lastActivity = status.lastActivity || status.startTime;
    const idleTime = now - lastActivity;

    // Check if session is expired
    if (
      sessionAge > SESSION_CONFIG.maxAge ||
      idleTime > SESSION_CONFIG.maxIdleTime
    ) {
      streamingAiService.cleanupSession(sessionId);
      return res.status(410).json({
        success: false,
        message: "Session expired",
        reason:
          sessionAge > SESSION_CONFIG.maxAge
            ? "max_age_exceeded"
            : "idle_timeout",
      });
    }

    res.json({
      success: true,
      sessionId: sessionId,
      status: status.status,
      progress: status.progress,
      startTime: status.startTime,
      currentTime: now,
      age: sessionAge,
      idleTime: idleTime,
      expiresAt: new Date(
        status.startTime + SESSION_CONFIG.maxAge
      ).toISOString(),
      idleExpiresAt: new Date(
        lastActivity + SESSION_CONFIG.maxIdleTime
      ).toISOString(),
    });
  } catch (error) {
    logger.error(
      `[StreamingRoutes] Failed to get session status for ${sessionId}:`,
      error
    );
    res.status(500).json({
      success: false,
      message: "Failed to get session status",
      error: error.message,
    });
  }
});

/**
 * Stop streaming quote generation
 * POST /api/streaming/stop-quote-generation/:sessionId
 */
router.post("/stop-quote-generation/:sessionId", protect, async (req, res) => {
  const { sessionId } = req.params;

  try {
    logger.info(`[StreamingRoutes] Stopping session ${sessionId}`, {
      userId: req.user?.id,
    });

    // Clean up the session
    streamingAiService.cleanupSession(sessionId);

    res.json({
      success: true,
      message: "Session stopped successfully",
    });
  } catch (error) {
    logger.error(
      `[StreamingRoutes] Failed to stop session ${sessionId}:`,
      error
    );
    res.status(500).json({
      success: false,
      message: "Failed to stop session",
      error: error.message,
    });
  }
});

/**
 * Get session info
 * GET /api/streaming/session/:sessionId/info
 */
router.get("/session/:sessionId/info", protect, (req, res) => {
  const { sessionId } = req.params;

  try {
    const sessionInfo = streamingAiService.getSessionInfo(sessionId);

    if (!sessionInfo) {
      return res.status(404).json({
        success: false,
        message: "Session not found",
      });
    }

    res.json({
      success: true,
      sessionId: sessionId,
      status: sessionInfo.status,
      progress: sessionInfo.progress,
      startTime: sessionInfo.startTime,
      formData: sessionInfo.formData
        ? {
            jobType: sessionInfo.formData.get
              ? sessionInfo.formData.get("jobType")
              : sessionInfo.formData.jobType,
            description: sessionInfo.formData.get
              ? sessionInfo.formData.get("description")
              : sessionInfo.formData.description,
            location: sessionInfo.formData.get
              ? sessionInfo.formData.get("location")
              : sessionInfo.formData.location,
          }
        : null,
    });
  } catch (error) {
    logger.error(
      `[StreamingRoutes] Failed to get session info for ${sessionId}:`,
      error
    );
    res.status(500).json({
      success: false,
      message: "Failed to get session info",
      error: error.message,
    });
  }
});

/**
 * Health check for streaming service
 * GET /api/streaming/health
 */
router.get("/health", (req, res) => {
  const activeSessions = streamingAiService.streamingSessions?.size || 0;
  const sessionStats = streamingAiService.getSessionStats();

  res.json({
    success: true,
    service: "streaming-ai",
    status: "healthy",
    activeSessions: activeSessions,
    stats: sessionStats,
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    memory: {
      used: Math.round(process.memoryUsage().heapUsed / 1024 / 1024),
      total: Math.round(process.memoryUsage().heapTotal / 1024 / 1024),
      rss: Math.round(process.memoryUsage().rss / 1024 / 1024),
    },
    features: {
      sse: true,
      streaming: true,
      confidence_scoring: true,
      progressive_updates: true,
      session_recovery: true,
    },
  });
});

/**
 * Get comprehensive session diagnostics
 * GET /api/streaming/session/:sessionId/diagnostics
 */
router.get("/session/:sessionId/diagnostics", protect, (req, res) => {
  const { sessionId } = req.params;

  try {
    // Get session with recovery attempt
    const recoveryResult = streamingAiService.recoverSession(sessionId);
    const sessionStats = streamingAiService.getSessionStats();

    // Detailed diagnostics
    const diagnostics = {
      sessionId,
      timestamp: new Date().toISOString(),
      recovery: recoveryResult,
      systemStats: {
        activeSessions: sessionStats.activeSessions,
        totalSessions: sessionStats.totalSessions,
        peakSessions: sessionStats.peakSessions,
        sessionsCreated: sessionStats.sessionsCreated,
        sessionsCleaned: sessionStats.sessionsCleaned,
        sessionsExpired: sessionStats.sessionsExpired,
        lastCleanupTime: new Date(sessionStats.lastCleanupTime).toISOString(),
      },
      serverInfo: {
        uptime: Math.round(process.uptime()),
        memoryUsage: {
          heapUsed: Math.round(process.memoryUsage().heapUsed / 1024 / 1024),
          heapTotal: Math.round(process.memoryUsage().heapTotal / 1024 / 1024),
          rss: Math.round(process.memoryUsage().rss / 1024 / 1024),
        },
        nodeVersion: process.version,
        platform: process.platform,
      },
      recommendations: [],
    };

    // Add recommendations based on diagnostics
    if (recoveryResult.found && recoveryResult.session) {
      diagnostics.recommendations.push("Session is active and healthy");
    } else if (
      recoveryResult.details?.possibleCauses?.includes("high_capacity")
    ) {
      diagnostics.recommendations.push(
        "Service is at high capacity - try again later"
      );
    } else if (
      recoveryResult.details?.possibleCauses?.includes("recent_cleanup")
    ) {
      diagnostics.recommendations.push(
        "Session was recently cleaned up - start a new session"
      );
    } else {
      diagnostics.recommendations.push(
        "Session not found - start a new session"
      );
    }

    if (sessionStats.activeSessions > 40) {
      diagnostics.recommendations.push(
        "High session count detected - expect slower performance"
      );
    }

    res.json({
      success: true,
      diagnostics,
    });
  } catch (error) {
    logger.error(
      `[StreamingRoutes] Diagnostics error for session ${sessionId}:`,
      error
    );
    res.status(500).json({
      success: false,
      error: "Failed to generate diagnostics",
      message: error.message,
    });
  }
});

/**
 * Get real-time session monitoring data
 * GET /api/streaming/monitor
 */
router.get("/monitor", protect, (req, res) => {
  try {
    const sessionStats = streamingAiService.getSessionStats();
    const healthMetrics = streamingAiService.getSessionHealthMetrics();

    const monitoringData = {
      timestamp: new Date().toISOString(),
      status: healthMetrics.status,
      healthScore: healthMetrics.healthScore,
      summary: {
        activeSessions: sessionStats.activeSessions,
        maxSessions: 50,
        capacityUtilization: `${Math.round(
          (sessionStats.activeSessions / 50) * 100
        )}%`,
        averageSessionAge: `${Math.round(sessionStats.averageAge / 1000)}s`,
        healthyConnections: sessionStats.healthyConnections,
        protectedSessions: sessionStats.protectedSessions,
      },
      sessionBreakdown: {
        byStatus: sessionStats.sessionsByStatus,
        byAge: sessionStats.sessionsByAge,
        lifetime: {
          created: sessionStats.sessionsCreated,
          cleaned: sessionStats.sessionsCleaned,
          expired: sessionStats.sessionsExpired,
          peak: sessionStats.peakSessions,
        },
      },
      performance: {
        serverUptime: Math.round(process.uptime()),
        memoryUsage: {
          heapUsed: Math.round(process.memoryUsage().heapUsed / 1024 / 1024),
          heapTotal: Math.round(process.memoryUsage().heapTotal / 1024 / 1024),
          rss: Math.round(process.memoryUsage().rss / 1024 / 1024),
        },
        lastCleanup: new Date(sessionStats.lastCleanupTime).toISOString(),
        timeSinceCleanup: Math.round(
          (Date.now() - sessionStats.lastCleanupTime) / 1000
        ),
      },
      alerts: healthMetrics.recommendations.filter(
        (r) => r.priority === "high" || r.priority === "critical"
      ),
      recommendations: healthMetrics.recommendations,
    };

    res.json({
      success: true,
      monitoring: monitoringData,
    });
  } catch (error) {
    logger.error("[StreamingRoutes] Monitoring endpoint error:", error);
    res.status(500).json({
      success: false,
      error: "Failed to retrieve monitoring data",
      message: error.message,
    });
  }
});

module.exports = router;
