import React from "react";
import {
  <PERSON>,
  Paper,
  Typography,
  Di<PERSON>r,
  <PERSON>,
  <PERSON>ton,
  CircularProgress,
  Alert,
} from "@mui/material";
import {
  Timeline,
  TimelineItem,
  TimelineSeparator,
  TimelineConnector,
  TimelineContent,
  TimelineDot,
  TimelineOppositeContent,
} from "@mui/lab";
import {
  Event as EventIcon,
  Person as PersonIcon,
  Edit as EditIcon,
  Assignment as AssignmentIcon,
  CheckCircle as CheckCircleIcon,
  Schedule as ScheduleIcon,
  Notes as NotesIcon,
  Info as InfoIcon,
  Warning as WarningIcon,
  Flag as FlagIcon,
  Build as BuildIcon,
  History as HistoryIcon,
  Refresh as RefreshIcon,
} from "@mui/icons-material";

const JobHistoryTimeline = ({ jobId, history, loading, error, onRefresh }) => {
  // Get icon for history event type
  const getEventIcon = (actionType) => {
    switch (actionType) {
      case "status_change":
        return <FlagIcon />;
      case "technician_assigned":
        return <PersonIcon />;
      case "technician_removed":
        return <PersonIcon color="error" />;
      case "note_added":
        return <NotesIcon />;
      case "task_added":
        return <AssignmentIcon />;
      case "task_completed":
        return <CheckCircleIcon color="success" />;
      case "task_updated":
        return <EditIcon />;
      case "job_updated":
        return <EditIcon />;
      case "ai_analysis":
        return <InfoIcon color="primary" />;
      case "risk_assessment":
        return <WarningIcon color="warning" />;
      case "scheduled":
        return <ScheduleIcon />;
      case "equipment_assigned":
        return <BuildIcon />;
      default:
        return <EventIcon />;
    }
  };

  // Get color for timeline dot based on event type
  const getDotColor = (actionType) => {
    switch (actionType) {
      case "status_change":
        return "primary";
      case "technician_assigned":
        return "success";
      case "technician_removed":
        return "error";
      case "note_added":
        return "info";
      case "task_added":
      case "task_completed":
      case "task_updated":
        return "secondary";
      case "job_updated":
        return "primary";
      case "ai_analysis":
        return "info";
      case "risk_assessment":
        return "warning";
      case "scheduled":
        return "primary";
      case "equipment_assigned":
        return "secondary";
      default:
        return "grey";
    }
  };

  // Format date with time
  const formatDateTime = (dateString) => {
    if (!dateString) return "Unknown date";

    const date = new Date(dateString);
    return date.toLocaleString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  // Get a nicely formatted description for the event
  const getEventDescription = (event) => {
    if (event.description) return event.description;

    switch (event.actionType) {
      case "status_change":
        return `Status changed to ${event.newValue || "Unknown"}`;
      case "technician_assigned":
        return `Technician ${
          event.technician?.name || event.userId || "Unknown"
        } assigned to job`;
      case "technician_removed":
        return `Technician ${
          event.technician?.name || event.userId || "Unknown"
        } removed from job`;
      case "note_added":
        return "Note added to job";
      case "task_added":
        return `Task added: ${event.task?.description || "Unknown task"}`;
      case "task_completed":
        return `Task completed: ${event.task?.description || "Unknown task"}`;
      case "task_updated":
        return `Task updated: ${event.task?.description || "Unknown task"}`;
      case "job_updated":
        return `Job details updated${
          event.updatedFields ? `: ${event.updatedFields.join(", ")}` : ""
        }`;
      case "ai_analysis":
        return "AI Analysis performed";
      case "risk_assessment":
        return `Risk assessment: ${
          event.riskLevel || "Unknown"
        } risk identified`;
      case "scheduled":
        return `Job scheduled for ${
          event.scheduledDate
            ? formatDateTime(event.scheduledDate)
            : "Unknown date"
        }`;
      case "equipment_assigned":
        return `Equipment assigned: ${
          event.equipment?.name || "Unknown equipment"
        }`;
      default:
        return event.action || "Unknown event";
    }
  };

  // Get secondary details for the event
  const getEventDetails = (event) => {
    const details = [];

    if (event.userId) {
      details.push(`By: ${event.userDisplayName || event.userId}`);
    }

    if (event.notes) {
      details.push(event.notes);
    }

    if (event.additionalDetails) {
      details.push(event.additionalDetails);
    }

    return details.join(" • ");
  };

  return (
    <Paper elevation={0} sx={{ p: 2, height: "100%" }}>
      <Box
        display="flex"
        justifyContent="space-between"
        alignItems="center"
        mb={2}
      >
        <Typography variant="h6" fontWeight="bold">
          Job History Timeline
        </Typography>
        <Button
          startIcon={<RefreshIcon />}
          size="small"
          onClick={onRefresh}
          disabled={loading}
        >
          Refresh
        </Button>
      </Box>

      <Divider sx={{ mb: 2 }} />

      {loading && (
        <Box display="flex" justifyContent="center" p={3}>
          <CircularProgress size={40} />
        </Box>
      )}

      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      {!loading && !error && (!history || history.length === 0) && (
        <Box textAlign="center" p={3}>
          <HistoryIcon color="disabled" sx={{ fontSize: 60, mb: 2 }} />
          <Typography color="textSecondary">
            No history records available
          </Typography>
        </Box>
      )}

      {!loading && !error && history && history.length > 0 && (
        <Timeline position="alternate" sx={{ p: 0 }}>
          {history.map((event, index) => (
            <TimelineItem key={index}>
              <TimelineOppositeContent color="text.secondary">
                {formatDateTime(event.timestamp)}
              </TimelineOppositeContent>

              <TimelineSeparator>
                <TimelineDot color={getDotColor(event.actionType)}>
                  {getEventIcon(event.actionType)}
                </TimelineDot>
                {index < history.length - 1 && <TimelineConnector />}
              </TimelineSeparator>

              <TimelineContent>
                <Box
                  sx={{
                    p: 2,
                    bgcolor: "background.paper",
                    borderRadius: 1,
                    boxShadow: 1,
                  }}
                >
                  <Typography variant="body1" fontWeight="medium">
                    {getEventDescription(event)}
                  </Typography>

                  {getEventDetails(event) && (
                    <Typography variant="body2" color="text.secondary">
                      {getEventDetails(event)}
                    </Typography>
                  )}

                  {event.actionType === "status_change" && (
                    <Chip
                      size="small"
                      label={event.newValue}
                      color={
                        event.newValue === "Completed"
                          ? "success"
                          : event.newValue === "In Progress"
                          ? "primary"
                          : event.newValue === "Delayed"
                          ? "warning"
                          : event.newValue === "Cancelled"
                          ? "error"
                          : "default"
                      }
                      sx={{ mt: 1 }}
                    />
                  )}
                </Box>
              </TimelineContent>
            </TimelineItem>
          ))}
        </Timeline>
      )}
    </Paper>
  );
};

export default JobHistoryTimeline;
