const mongoose = require("mongoose");

const notificationSchema = new mongoose.Schema({
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "User",
    required: true,
  },
  title: {
    type: String,
    required: true,
    trim: true,
  },
  message: {
    type: String,
    required: true,
    trim: true,
  },
  type: {
    type: String,
    enum: [
      "info",
      "success",
      "warning",
      "error",
      "job",
      "invoice",
      "customer",
      "system",
    ],
    default: "info",
  },
  relatedTo: {
    model: {
      type: String,
      enum: [
        "Job",
        "Customer",
        "Invoice",
        "User",
        "Material",
        "CalendarEvent",
        "Other",
      ],
    },
    id: {
      type: mongoose.Schema.Types.ObjectId,
      refPath: "relatedTo.model",
    },
  },
  isRead: {
    type: Boolean,
    default: false,
  },
  createdAt: {
    type: Date,
    default: Date.now,
    index: true,
  },
});

// Database indexes for performance optimization
notificationSchema.index({ user: 1, isRead: 1 }); // User notification status
notificationSchema.index({ user: 1, createdAt: -1 }); // User recent notifications
notificationSchema.index({ type: 1, createdAt: -1 }); // Notification type queries
notificationSchema.index({ "relatedTo.model": 1, "relatedTo.id": 1 }); // Related entity lookup
notificationSchema.index({ isRead: 1, createdAt: 1 }); // Cleanup old read notifications
notificationSchema.index({ createdAt: -1 }); // Recent notifications across all users
notificationSchema.index({ user: 1, type: 1, isRead: 1 }); // User type-specific notifications

// Static method to create a notification
notificationSchema.statics.createNotification = async function (data) {
  const notification = new this(data);
  await notification.save();
  return notification;
};

// Static method to mark notifications as read
notificationSchema.statics.markAsRead = function (userId, notificationIds) {
  return this.updateMany(
    {
      user: userId,
      _id: { $in: notificationIds },
    },
    {
      $set: { isRead: true },
    }
  );
};

// Static method to mark all notifications as read for a user
notificationSchema.statics.markAllAsRead = function (userId) {
  return this.updateMany(
    { user: userId, isRead: false },
    { $set: { isRead: true } }
  );
};

// Static method to get unread notifications count
notificationSchema.statics.getUnreadCount = function (userId) {
  return this.countDocuments({ user: userId, isRead: false });
};

// Static method to get recent notifications
notificationSchema.statics.getRecent = function (userId, limit = 10) {
  return this.find({ user: userId })
    .sort({ createdAt: -1 })
    .limit(limit)
    .populate("relatedTo.id");
};

// Static method to delete old notifications
notificationSchema.statics.deleteOld = function (days = 30) {
  const cutoffDate = new Date();
  cutoffDate.setDate(cutoffDate.getDate() - days);

  return this.deleteMany({
    createdAt: { $lt: cutoffDate },
    isRead: true,
  });
};

const Notification = mongoose.model("Notification", notificationSchema);

module.exports = Notification;
