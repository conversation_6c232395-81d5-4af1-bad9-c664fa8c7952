.image-gallery {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.image-gallery-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.image-gallery-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 16px;
}

.image-card {
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  overflow: visible; /* Allow menu to be visible */
  transition: box-shadow 0.2s;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.image-card:hover {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.image-thumbnail {
  width: 100%;
  height: 150px;
  object-fit: cover;
  display: block;
}

.image-info {
  padding: 12px;
}

.image-title {
  font-weight: 500;
  margin-bottom: 4px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.image-meta {
  display: flex;
  justify-content: space-between;
  font-size: 0.8rem;
  color: #666;
}

.image-category {
  display: inline-block;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 0.7rem;
  text-transform: lowercase;
  height: 20px;
  min-height: 20px;
}

.category-before {
  background-color: #e3f2fd;
  color: #1976d2;
}

.category-after {
  background-color: #e8f5e9;
  color: #2e7d32;
}

.category-other {
  background-color: #f5f5f5;
  color: #616161;
}

/* AI Category Styles */
.ai-inspection {
  background-color: #fff8e1;
  color: #ff8f00;
}

.ai-diagram {
  background-color: #e8eaf6;
  color: #3f51b5;
}

.ai-equipment {
  background-color: #e0f7fa;
  color: #00acc1;
}

.ai-damage {
  background-color: #ffebee;
  color: #c62828;
}

.ai-hazard {
  background-color: #fbe9e7;
  color: #d84315;
}

.ai-invoice {
  background-color: #f3e5f5;
  color: #7b1fa2;
}

.upload-dropzone {
  border: 2px dashed #ccc;
  border-radius: 8px;
  padding: 20px;
  text-align: center;
  cursor: pointer;
  margin-bottom: 20px;
  transition: border-color 0.2s;
}

.upload-dropzone:hover {
  border-color: #2196f3;
}

.image-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  max-width: 90%;
  max-height: 90%;
  position: relative;
}

.modal-image {
  max-width: 100%;
  max-height: 90vh;
  display: block;
  border-radius: 4px;
}

.modal-close {
  position: absolute;
  top: -40px;
  right: 0;
  color: white;
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
}

.modal-info {
  background-color: white;
  padding: 16px;
  border-radius: 0 0 4px 4px;
}

.empty-gallery {
  text-align: center;
  padding: 40px 20px;
  background-color: #f5f5f5;
  border-radius: 8px;
}

/* Ensure menu button is always clickable */
.image-menu-button {
  position: relative;
  z-index: 10;
}

.image-menu-button:hover {
  background-color: rgba(0, 0, 0, 0.08);
}

.ai-analysis-section {
  background-color: rgba(25, 118, 210, 0.05);
  border-radius: 4px;
  padding: 8px;
  margin-top: 8px;
}

.ai-component-chip {
  margin: 2px;
  background-color: #e3f2fd;
  color: #1976d2;
  font-size: 0.7rem;
}

.ai-concern-item {
  color: #f44336;
  margin-top: 4px;
  font-size: 0.75rem;
}

/* Custom styles for Allotment component */
:root {
  --focus-border: #1976d2;
  --separator-border: rgba(0, 0, 0, 0.2);
}

.split-view-sash-dragging {
  cursor: ns-resize;
}

.sash-hover {
  background-color: var(--focus-border) !important;
}

.sash {
  transition: background-color 0.1s ease;
}

/* Fullscreen mode styles */
.fullscreen-image-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1300;
  background-color: #000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.fullscreen-details-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1300;
  background-color: #fff;
  overflow: auto;
  padding: 20px;
}

/* Zoom styles */
.zoom-controls {
  position: absolute;
  bottom: 16px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  background-color: rgba(0, 0, 0, 0.5);
  border-radius: 8px;
  padding: 4px 8px;
  z-index: 10;
  opacity: 0.7;
  transition: opacity 0.2s ease;
}

.zoom-controls:hover {
  opacity: 1;
}

.zoom-image-container {
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
}

.zoom-image-container.draggable {
  cursor: grab;
}

.zoom-image-container.dragging {
  cursor: grabbing;
}

.zoom-image {
  transition: transform 0.1s ease;
}

.zoom-image.dragging {
  transition: none;
}
