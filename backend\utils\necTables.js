/**
 * Internal storage for simplified NEC table data for common lookups.
 * This provides a fast and reliable offline fallback.
 * Data should be verified against the relevant NEC version.
 */

const logger = require("./logger");

// Simplified NEC Table 310.16 Ampacities (Example - verify against actual NEC version)
// Based on Copper conductors at 75°C termination rating, in raceway/cable.
const necTable310_16_Copper_75C = {
  // AWG: Ampacity
  14: 15,
  12: 20,
  10: 30,
  8: 50, // Note: NEC 240.4(D) often limits #8 to 40A OCPD
  6: 65,
  4: 85,
  3: 100,
  2: 115,
  1: 130,
  "1/0": 150,
  "2/0": 175,
  "3/0": 200,
  "4/0": 230,
  // kcmil: Ampacity
  250: 255,
  300: 285,
  350: 310,
  400: 335,
  500: 380,
  600: 420,
  700: 460, // Approximation, check exact table
  750: 475,
  800: 490, // Approximation
  900: 520, // Approximation
  1000: 545,
  // Add more as needed
};

// Simplified NEC Table 250.66 Grounding Electrode Conductor (GEC) Sizing (Example)
// Based on Copper Service Entrance Conductors
const necTable250_66_GEC_CopperService = {
  // Service Conductor Size (AWG/kcmil): Required GEC Size (AWG Copper)
  "2_or_smaller": "8",
  "1_or_1/0": "6",
  "2/0_or_3/0": "4",
  "over_3/0_thru_350_kcmil": "2",
  over_350_kcmil_thru_600_kcmil: "1/0",
  over_600_kcmil_thru_1100_kcmil: "2/0",
  over_1100_kcmil: "3/0",
};
// Simplified NEC Table 250.66 GEC Sizing - Aluminum Service Entrance Conductors
const necTable250_66_GEC_AluminumService = {
  "1/0_or_smaller": "6", // Adjusted based on typical AL equivalents
  "2/0_or_3/0": "4",
  "4/0_or_250_kcmil": "2",
  over_250_kcmil_thru_500_kcmil: "1/0",
  over_500_kcmil_thru_900_kcmil: "2/0",
  over_900_kcmil_thru_1750_kcmil: "3/0",
  over_1750_kcmil: "3/0", // Max size typically listed
};

// Helper function to find the smallest conductor size meeting ampacity requirement
function findMinConductorSize(ampacity, material = "copper", tempRating = 75) {
  if (material.toLowerCase() !== "copper" || tempRating !== 75) {
    logger.warn(
      `[necTables] Internal lookup currently only supports Copper @ 75C. Requested: ${material} @ ${tempRating}C`
    );
    return null; // Only support copper 75C for this example
  }
  if (typeof ampacity !== "number" || isNaN(ampacity) || ampacity <= 0) {
    logger.warn(
      `[necTables] Invalid ampacity provided for internal lookup: ${ampacity}`
    );
    return null;
  }

  const table = necTable310_16_Copper_75C;
  const sizes = Object.keys(table);

  // Sort sizes correctly (AWG descending, then kcmil ascending)
  const sortedSizes = sizes.sort((a, b) => {
    const aIsKcmil =
      a.includes("kcmil") || (!a.includes("/") && !isNaN(parseInt(a)));
    const bIsKcmil =
      b.includes("kcmil") || (!b.includes("/") && !isNaN(parseInt(b)));
    const aIsAWG = a.includes("AWG") || a.includes("/");
    const bIsAWG = b.includes("AWG") || b.includes("/");

    if (aIsAWG && bIsAWG) {
      const aVal = a.includes("/") ? -parseInt(a.split("/")[0]) : parseInt(a); // Treat 1/0 as -1, 2/0 as -2 etc.
      const bVal = b.includes("/") ? -parseInt(b.split("/")[0]) : parseInt(b);
      return bVal - aVal; // Sort AWG descending (e.g., 2 before 4)
    }
    if (aIsKcmil && bIsKcmil) {
      return parseInt(a) - parseInt(b); // Sort kcmil ascending
    }
    if (aIsAWG && bIsKcmil) return -1; // AWG comes before kcmil
    if (aIsKcmil && bIsAWG) return 1; // kcmil comes after AWG
    return 0;
  });

  for (const size of sortedSizes) {
    if (table[size] >= ampacity) {
      logger.debug(
        `[necTables] Found internal match for ${ampacity}A: ${size} AWG/kcmil Copper @ 75C`
      );
      return size.includes("kcmil") ? size : `${size} AWG`; // Add AWG suffix if not kcmil
    }
  }

  logger.warn(
    `[necTables] Ampacity ${ampacity}A exceeds internal table limits.`
  );
  return null; // Ampacity exceeds table limits
}

// Helper function to find GEC size based on service conductor size and material
function findGECSize(serviceConductorSizeStr, serviceMaterial = "copper") {
  const material = serviceMaterial.toLowerCase();
  if (material !== "copper" && material !== "aluminum") {
    logger.warn(
      `[necTables] Invalid service material provided for internal GEC lookup: ${serviceMaterial}`
    );
    return null;
  }
  if (!serviceConductorSizeStr || typeof serviceConductorSizeStr !== "string") {
    logger.warn(
      `[necTables] Invalid service conductor size string provided for internal GEC lookup: ${serviceConductorSizeStr}`
    );
    return null;
  }

  // Improved parsing for size and unit, handling parallel runs (e.g., "Parallel 3/0 AWG", "2 x 500 kcmil")
  let totalKcmil = 0;
  let isAWG = false;
  let largestAWG = 1000; // Start with a large AWG number

  // Check for parallel runs first
  const parallelMatch = serviceConductorSizeStr.match(
    /(?:parallel|(\d+)\s*x)\s*([\d/]+)\s*(kcmil|awg)/i
  );

  if (parallelMatch) {
    const count = parallelMatch[1] ? parseInt(parallelMatch[1], 10) : 2; // Default to 2 if "parallel" is used without a number
    const size = parallelMatch[2];
    const unit = parallelMatch[3].toLowerCase();

    if (unit === "kcmil") {
      totalKcmil = count * parseInt(size);
      isAWG = false;
    } else {
      // AWG
      isAWG = true;
      // For AWG parallel runs, use the equivalent area based on Chapter 9, Table 8, then multiply.
      // This is complex, for simplification, we'll base GEC on the size of ONE conductor of the parallel set,
      // as per common interpretation, but note this might need refinement based on specific AHJ rules.
      // We still need the single conductor size parsed correctly.
      if (size.includes("/")) {
        // Handle X/0 sizes
        const numerator = parseInt(size.split("/")[0]);
        largestAWG = -numerator; // e.g., 4/0 -> -4
      } else {
        largestAWG = parseInt(size);
      }
      // Convert AWG to approximate kcmil for comparison IF NEEDED by lookup logic below
      // This conversion is rough and only for range checking
      if (largestAWG <= -1)
        totalKcmil =
          count *
          (105.6 *
            Math.pow(10, (1 - largestAWG) / 10)); // Approx kcmil for /0 sizes
      else if (largestAWG <= 6)
        totalKcmil =
          count *
          (105.6 *
            Math.pow(10, (6 - largestAWG) / 10)); // Approx kcmil for 1-6 AWG
      else totalKcmil = count * 1; // Smaller AWG, treat as minimal kcmil for range check
      logger.debug(
        `[necTables] Parsed parallel AWG (${count}x${size}) - using single conductor size ${size} AWG for GEC lookup.`
      );
    }
  } else {
    // Single conductor run
    const singleMatch = serviceConductorSizeStr.match(
      /([\d/]+)\s*(kcmil|awg)/i
    );
    if (singleMatch) {
      const size = singleMatch[1];
      const unit = singleMatch[2].toLowerCase();
      if (unit === "kcmil") {
        totalKcmil = parseInt(size);
        isAWG = false;
      } else {
        // AWG
        isAWG = true;
        if (size.includes("/")) {
          largestAWG = -parseInt(size.split("/")[0]);
        } else {
          largestAWG = parseInt(size);
        }
        // Approx kcmil conversion for range check
        if (largestAWG <= -1)
          totalKcmil = 105.6 * Math.pow(10, (1 - largestAWG) / 10);
        else if (largestAWG <= 6)
          totalKcmil = 105.6 * Math.pow(10, (6 - largestAWG) / 10);
        else totalKcmil = 1;
      }
    } else {
      logger.warn(
        `[necTables] Could not parse service conductor size for GEC lookup: ${serviceConductorSizeStr}`
      );
      return null;
    }
  }

  const table =
    material === "copper"
      ? necTable250_66_GEC_CopperService
      : necTable250_66_GEC_AluminumService;
  let key = null;

  // Determine the key for the lookup table based on parsed size
  if (material === "copper") {
    if (isAWG) {
      if (largestAWG >= 2) key = "2_or_smaller"; // e.g., #2, #4, #6, #8
      else if (largestAWG >= 0) key = "1_or_1/0"; // #1, 1/0
      else if (largestAWG >= -3) key = "2/0_or_3/0"; // 2/0, 3/0
      else key = "over_3/0_thru_350_kcmil"; // 4/0 AWG falls here
    } else {
      // kcmil
      if (totalKcmil <= 350) key = "over_3/0_thru_350_kcmil";
      else if (totalKcmil <= 600) key = "over_350_kcmil_thru_600_kcmil";
      else if (totalKcmil <= 1100) key = "over_600_kcmil_thru_1100_kcmil";
      else key = "over_1100_kcmil";
    }
  } else {
    // Aluminum
    if (isAWG) {
      if (largestAWG >= 0) key = "1/0_or_smaller"; // #1/0, #1, #2 etc.
      else if (largestAWG >= -3) key = "2/0_or_3/0"; // 2/0, 3/0
      else key = "4/0_or_250_kcmil"; // 4/0 AWG falls here
    } else {
      // kcmil
      if (totalKcmil <= 250) key = "4/0_or_250_kcmil";
      else if (totalKcmil <= 500) key = "over_250_kcmil_thru_500_kcmil";
      else if (totalKcmil <= 900) key = "over_500_kcmil_thru_900_kcmil";
      else if (totalKcmil <= 1750) key = "over_900_kcmil_thru_1750_kcmil";
      else key = "over_1750_kcmil";
    }
  }

  if (key && table[key]) {
    const gecSize = table[key];
    // GEC is typically Copper, specify unless table implies otherwise
    const gecMaterial = "Copper";
    logger.debug(
      `[necTables] Found internal GEC match for service size ${serviceConductorSizeStr} (${material}): #${gecSize} AWG ${gecMaterial}`
    );
    return `#${gecSize} AWG ${gecMaterial}`;
  }

  logger.warn(
    `[necTables] Could not determine internal GEC size for service size: ${serviceConductorSizeStr} (${material})`
  );
  return null;
}

// Basic lookup for typical service entrance conduit size
function findServiceEntranceConduitSize(ampacity, serviceConductorSizeStr) {
  if (typeof ampacity !== "number" || isNaN(ampacity) || ampacity <= 0) {
    logger.warn(
      `[necTables] Invalid ampacity provided for internal conduit lookup: ${ampacity}`
    );
    return null;
  }
  // This is highly simplified and depends heavily on conductor size/type
  // Add more specific rules based on common practices or conductor info if available
  if (ampacity <= 100) return '1-1/4" or 1-1/2"'; // Common range
  if (ampacity <= 200) return '2" or 2-1/2"'; // Common range
  if (ampacity <= 400) {
    // Could refine based on serviceConductorSizeStr if parsed
    return '3" or 3-1/2"'; // Common range for 400A
  }
  if (ampacity <= 600) return '4" or Parallel Conduits';

  logger.warn(
    `[necTables] No simple internal rule for conduit size at ${ampacity}A.`
  );
  return null; // No simple rule found
}

module.exports = {
  findMinConductorSize,
  findGECSize,
  findServiceEntranceConduitSize,
  // Add other table lookup functions here
};
