const { execSync } = require('child_process');
const path = require('path');

console.log('Testing Python environment detection...');

// Test the Python detection logic from crawl4ai-service.js
function testPythonDetection() {
  const pythonWrapperPath = path.join(__dirname, 'backend', 'scrapers', 'crawl4ai', 'python', 'crawl4ai_wrapper.py');
  
  console.log('Testing Python executable detection...');
  
  // Test candidate paths
  const candidates = [
    'python3',
    'python',
    '/usr/bin/python3',
    '/usr/bin/python',
    '/usr/local/bin/python3',
    '/usr/local/bin/python'
  ];
  
  let pythonExecutable = null;
  let pythonVersion = null;
  
  for (const candidate of candidates) {
    try {
      console.log(`Testing candidate: ${candidate}`);
      const versionOutput = execSync(`${candidate} --version`, { 
        encoding: 'utf8',
        timeout: 5000 
      }).trim();
      
      if (versionOutput.startsWith('Python 3')) {
        pythonExecutable = candidate;
        pythonVersion = versionOutput;
        console.log(`✅ Found Python: ${candidate} - ${versionOutput}`);
        break;
      }
    } catch (error) {
      console.log(`❌ ${candidate}: ${error.message}`);
    }
  }
  
  if (pythonExecutable) {
    console.log('\n✅ Python environment detection: SUCCESS');
    console.log(`Executable: ${pythonExecutable}`);
    console.log(`Version: ${pythonVersion}`);
    
    // Test the Python wrapper script
    console.log('\nTesting Python wrapper script...');
    try {
      const testInput = JSON.stringify({
        url: 'https://httpbin.org/json',
        options: { timeout: 10000 }
      });
      
      const result = execSync(`echo '${testInput}' | ${pythonExecutable} ${pythonWrapperPath}`, {
        encoding: 'utf8',
        timeout: 30000
      });
      
      const parsedResult = JSON.parse(result);
      console.log('✅ Python wrapper test: SUCCESS');
      console.log(`Result: ${parsedResult.success ? 'Success' : 'Failed'}`);
      if (parsedResult.success) {
        console.log(`Content length: ${parsedResult.markdown ? parsedResult.markdown.length : 0} characters`);
      } else {
        console.log(`Error: ${parsedResult.error}`);
      }
      
    } catch (error) {
      console.log('❌ Python wrapper test: FAILED');
      console.log(`Error: ${error.message}`);
      if (error.stdout) console.log(`Stdout: ${error.stdout}`);
      if (error.stderr) console.log(`Stderr: ${error.stderr}`);
    }
    
  } else {
    console.log('❌ Python environment detection: FAILED - No suitable Python found');
  }
}

testPythonDetection();