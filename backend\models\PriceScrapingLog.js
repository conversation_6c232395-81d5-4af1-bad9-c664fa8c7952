/**
 * PriceScrapingLog.js
 * Model for logging price scraping operations
 */

const mongoose = require("mongoose");

/**
 * Schema for tracking price scraping operations
 */
const priceScrapingLogSchema = new mongoose.Schema(
  {
    // Original source reference - optional for MCP requests which don't have a specific source
    source: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "MaterialSource",
      index: true,
    },

    // New fields for MCP AI material scraping
    requestId: {
      type: String,
      trim: true,
      unique: true,
      sparse: true,
      index: true,
    },
    quoteId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Quote",
      index: true,
    },
    itemId: {
      type: String,
      trim: true,
    },
    toolId: {
      type: String,
      trim: true,
    },
    toolParams: {
      type: String, // Stored as JSON string
    },
    result: {
      type: String, // Stored as JSON string, limited to prevent overflow
    },
    query: {
      type: String,
      trim: true,
    },
    url: {
      type: String,
      trim: true,
    },
    sku: {
      type: String,
      trim: true,
    },
    status: {
      // Overall status
      type: String,
      required: true,
      enum: [
        "PENDING",
        "SUCCESS",
        "ERROR",
        "PARTIAL",
        "BLOCKED",
        "RATE_LIMITED",
      ], // Added PENDING
      default: "PENDING", // Default to PENDING on creation
    },
    methodUsed: {
      // Which method succeeded or was last attempted
      type: String,
      enum: ["crawl4ai", "firecrawl", "playwright", "cache", "unknown"], // Crawl4AI is now the primary method
      default: "unknown",
    },
    errorCode: {
      // Specific code for monitoring/debugging
      type: String,
      // Examples: SUCCESS, FIRECRAWL_FAIL, PLAYWRIGHT_FAIL, PARSE_FAIL, SELECTOR_FAIL, CAPTCHA, LOGIN_REQ, TIMEOUT, NO_RESULTS, UNKNOWN
    },
    error: {
      message: String,
      code: String, // For HTTP status or specific internal codes
      stack: String,
    },
    results: {
      found: Number,
      processed: Number,
      updated: Number,
      new: Number,
      unchanged: Number,
      failed: Number,
    },
    duration: {
      type: Number, // in ms
      default: 0,
    },
    userAgent: String,
    ip: String,
    proxyUsed: String,
    attempts: {
      type: Number,
      default: 1,
    },
    startTime: {
      type: Date,
      default: Date.now,
    },
    endTime: Date,
    initiatedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
    },
    company: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Company",
    },
  },
  { timestamps: true }
);

// Database indexes for performance optimization
priceScrapingLogSchema.index({ createdAt: -1 }); // Recent logs
priceScrapingLogSchema.index({ source: 1, status: 1 }); // Source status queries
priceScrapingLogSchema.index({ source: 1, createdAt: -1 }); // Source recent logs
priceScrapingLogSchema.index({ errorCode: 1 }); // Error analysis
priceScrapingLogSchema.index({ methodUsed: 1 }); // Method performance
priceScrapingLogSchema.index({ requestId: 1 }); // MCP request tracking
priceScrapingLogSchema.index({ quoteId: 1, status: 1 }); // Quote scraping status
priceScrapingLogSchema.index({ status: 1, startTime: -1 }); // Status-based queries
priceScrapingLogSchema.index({ initiatedBy: 1, createdAt: -1 }); // User activity tracking
priceScrapingLogSchema.index({ company: 1, createdAt: -1 }); // Company scraping history
priceScrapingLogSchema.index({ sku: 1 }); // SKU lookup
priceScrapingLogSchema.index({ url: 1 }); // URL tracking
priceScrapingLogSchema.index({ duration: 1 }); // Performance analysis

/**
 * Complete the scrape log with results
 * @param {Object} results - Results of the scrape
 * @param {Object} options - Additional options
 * @returns {Promise<Object>} - The updated log
 */
priceScrapingLogSchema.methods.complete = async function (
  results,
  options = {}
) {
  // Destructure options with defaults
  const {
    status = "SUCCESS",
    error = null,
    methodUsed = this.methodUsed || "unknown", // Keep existing if already set
    // Corrected logic for errorCode: prioritize options, then error object, then default based on error presence
    errorCode = options.errorCode ||
      (error && error.errorCode) ||
      (error ? "UNKNOWN_ERROR" : "SUCCESS"),
  } = options;

  this.status = status;
  this.results = results;
  this.endTime = new Date();
  this.duration = this.endTime - this.startTime;
  this.methodUsed = methodUsed; // Store the method used

  // Ensure this.errorCode is set based on the final determined errorCode from options or error object
  this.errorCode =
    options.errorCode ||
    (error && error.errorCode) ||
    (error ? "UNKNOWN_ERROR" : "SUCCESS");

  if (error) {
    this.error = {
      message: error.message || String(error),
      // Use error.code (e.g. HTTP status) or error.statusCode if available, otherwise this.errorCode
      code: error.code || error.statusCode || this.errorCode || "UNKNOWN",
      stack: error.stack,
    };
    // Ensure overall status reflects error if an error object is passed, unless status is PARTIAL
    if (this.status === "SUCCESS" && status !== "PARTIAL") {
      this.status = "ERROR";
    }
  } else {
    // Clear error field if completing successfully without an error object
    if (status === "SUCCESS") {
      this.error = undefined;
    }
  }

  return this.save();
};

/**
 * Mark the scrape as failed
 * @param {Error} error - Error object
 * @param {Object} options - Additional options
 * @returns {Promise<Object>} - The updated log
 */
priceScrapingLogSchema.methods.fail = async function (error, options = {}) {
  // Destructure options, providing a default error code if none is specified
  const {
    status = "ERROR", // Default status for fail is ERROR
    methodUsed = this.methodUsed || "unknown",
    // Prioritize errorCode from options, then from the error object itself, then a generic failure code
    errorCode = options.errorCode ||
      (error && typeof error === "object" && error.errorCode) ||
      "UNKNOWN_FAILURE",
  } = options;

  // Ensure error is an Error object
  const errorObj =
    error instanceof Error
      ? error
      : new Error(String(error || "Unknown failure"));
  // If the original error had an errorCode, preserve it on the errorObj if not already present
  if (
    error &&
    typeof error === "object" &&
    error.errorCode &&
    !errorObj.errorCode
  ) {
    errorObj.errorCode = error.errorCode;
  }
  // Preserve original error.code (e.g. HTTP status) or statusCode
  if (
    error &&
    typeof error === "object" &&
    (error.code || error.statusCode) &&
    !errorObj.code
  ) {
    errorObj.code = error.code || error.statusCode;
  }

  return this.complete(this.results || {}, {
    status, // This will usually be 'ERROR', 'BLOCKED', 'RATE_LIMITED' etc.
    error: errorObj,
    methodUsed,
    errorCode, // Pass the specific error code, which might have come from options or the error itself
  });
};

/**
 * Static method to start a new scrape log
 * @param {Object} source - Material source document or ID
 * @param {Object} options - Scrape options
 * @returns {Promise<Object>} - The created log
 */
priceScrapingLogSchema.statics.startScrape = async function (
  source,
  options = {}
) {
  const {
    query,
    url,
    sku,
    userAgent,
    ip,
    proxyUsed,
    initiatedBy,
    company,
    methodUsed = "unknown", // Add methodUsed to options
  } = options;

  const log = new this({
    source: typeof source === "object" ? source._id : source,
    query,
    url,
    sku,
    userAgent,
    ip,
    proxyUsed,
    startTime: new Date(),
    initiatedBy,
    company,
    methodUsed, // Store initial method if known
    status: "PENDING", // Start in a pending state
    results: {
      found: 0,
      processed: 0,
      updated: 0,
      new: 0,
      unchanged: 0,
      failed: 0,
    },
  });

  return log.save();
};

/**
 * Static method to get recent scrape logs for a source
 * @param {Object} source - Material source document or ID
 * @param {Number} limit - Maximum number of logs to return
 * @returns {Promise<Array>} - Array of logs
 */
priceScrapingLogSchema.statics.getRecentLogs = async function (
  source,
  limit = 10
) {
  return this.find({
    source: typeof source === "object" ? source._id : source,
  })
    .sort({ startTime: -1 })
    .limit(limit);
};

/**
 * Static method to get status summary for a source
 * @param {Object} source - Material source document or ID
 * @param {Number} days - Number of days to include
 * @returns {Promise<Object>} - Summary of scraping status
 */
priceScrapingLogSchema.statics.getStatusSummary = async function (
  source,
  days = 7
) {
  const sourceId = typeof source === "object" ? source._id : source;
  const cutoffDate = new Date();
  cutoffDate.setDate(cutoffDate.getDate() - days);

  const logs = await this.find({
    source: sourceId,
    startTime: { $gte: cutoffDate },
  }).sort({ startTime: -1 });

  const results = {
    total: logs.length,
    success: 0,
    error: 0, // Generic error count
    blocked: 0,
    rateLimited: 0,
    partial: 0,
    pending: 0, // Add pending count
    byErrorCode: {}, // Add breakdown by specific error code
    byMethod: {}, // Add breakdown by method used
    averageDuration: 0, // in ms
    totalItemsProcessed: 0,
  };

  let totalDuration = 0;

  logs.forEach((log) => {
    switch (log.status) {
      case "SUCCESS":
        results.success++;
        break;
      case "ERROR":
        results.error++;
        break;
      case "BLOCKED":
        results.blocked++;
        break;
      case "RATE_LIMITED":
        results.rateLimited++;
        break;
      case "PARTIAL":
        results.partial++;
        break;
      case "PENDING": // Count pending logs
        results.pending++;
        break;
    }

    // Count by specific error code
    if (log.errorCode) {
      results.byErrorCode[log.errorCode] =
        (results.byErrorCode[log.errorCode] || 0) + 1;
    } else if (
      log.status === "ERROR" ||
      log.status === "BLOCKED" ||
      log.status === "RATE_LIMITED" ||
      log.status === "PARTIAL"
    ) {
      // If errorCode is missing for a failed/blocked/partial log, categorize it as UNKNOWN_CODE_IN_LOG
      results.byErrorCode["UNKNOWN_CODE_IN_LOG"] =
        (results.byErrorCode["UNKNOWN_CODE_IN_LOG"] || 0) + 1;
    }

    // Count by method used
    if (log.methodUsed) {
      results.byMethod[log.methodUsed] =
        (results.byMethod[log.methodUsed] || 0) + 1;
    } else {
      results.byMethod["UNKNOWN_METHOD_IN_LOG"] =
        (results.byMethod["UNKNOWN_METHOD_IN_LOG"] || 0) + 1;
    }

    if (log.duration && log.status !== "PENDING") {
      // Only average completed logs
      totalDuration += log.duration;
    }

    if (log.results && log.results.processed) {
      results.totalItemsProcessed += log.results.processed;
    }
  });

  const completedLogsCount = logs.length - results.pending;
  if (completedLogsCount > 0) {
    results.averageDuration = totalDuration / completedLogsCount;
  }

  return results;
};

const PriceScrapingLog = mongoose.model(
  "PriceScrapingLog",
  priceScrapingLogSchema
);

module.exports = PriceScrapingLog;
