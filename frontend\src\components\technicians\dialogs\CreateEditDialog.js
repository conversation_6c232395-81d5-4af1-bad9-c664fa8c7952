import React, { useState } from "react";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Grid,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Typography,
  Box,
  Chip,
  Paper,
  Autocomplete,
} from "@mui/material";
import SmartToyIcon from "@mui/icons-material/SmartToy";

/**
 * Dialog component for creating or editing a technician
 */
const CreateEditDialog = ({
  open,
  onClose,
  isCreateMode,
  formData,
  onFormChange,
  onSubmit,
  validationError,
}) => {
  // Predefined options for skills and services
  const skillOptions = [
    "Electrical Wiring",
    "Plumbing",
    "HVAC",
    "Carpentry",
    "Painting",
    "Flooring",
    "Masonry",
    "Roofing",
  ];
  const serviceOptions = [
    "Installation",
    "Repair",
    "Maintenance",
    "Inspection",
    "Consultation",
    "Emergency Service",
  ];

  // State for AI suggestions
  const [aiLoading, setAiLoading] = useState(false);
  const [aiSkillSuggestions, setAiSkillSuggestions] = useState([]);
  const [aiServiceSuggestions, setAiServiceSuggestions] = useState([]);

  const handleSkillChange = (event, newValue) => {
    onFormChange({
      target: {
        name: "skills",
        value: newValue,
      },
    });
  };

  const handleServiceChange = (event, newValue) => {
    onFormChange({
      target: {
        name: "services",
        value: newValue,
      },
    });
  };

  // Function to generate AI suggestions based on contractor type
  const generateAiSuggestions = () => {
    if (!formData.contractorType) return;

    setAiLoading(true);

    // Simulate AI response - in a real implementation, this would call the backend AI service
    setTimeout(() => {
      // Generate suggestions based on contractor type
      let suggestedSkills = [];
      let suggestedServices = [];

      switch (formData.contractorType) {
        case "HVAC":
          suggestedSkills = [
            "HVAC Installation",
            "Refrigerant Handling",
            "Ductwork",
            "Thermostat Programming",
            "Air Quality Testing",
          ];
          suggestedServices = [
            "HVAC Maintenance",
            "AC Repair",
            "Heating System Installation",
            "Duct Cleaning",
          ];
          break;
        case "Plumbing":
          suggestedSkills = [
            "Pipe Fitting",
            "Drain Cleaning",
            "Fixture Installation",
            "Water Heater Repair",
            "Leak Detection",
          ];
          suggestedServices = [
            "Plumbing Repair",
            "Drain Unclogging",
            "Water Heater Installation",
            "Pipe Replacement",
          ];
          break;
        case "Electrical":
          suggestedSkills = [
            "Circuit Installation",
            "Panel Upgrades",
            "Lighting Systems",
            "Electrical Troubleshooting",
            "Code Compliance",
          ];
          suggestedServices = [
            "Electrical Repair",
            "Lighting Installation",
            "Electrical Inspection",
            "Wiring Upgrade",
          ];
          break;
        default:
          suggestedSkills = [
            "Customer Service",
            "Project Management",
            "Safety Procedures",
            "Quality Control",
          ];
          suggestedServices = ["Consultation", "Maintenance", "Inspection"];
      }

      setAiSkillSuggestions(suggestedSkills);
      setAiServiceSuggestions(suggestedServices);
      setAiLoading(false);
    }, 1000);
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle>
        {isCreateMode ? "Create New Technician" : "Edit Technician"}
      </DialogTitle>
      <DialogContent dividers>
        <Grid container spacing={2}>
          <Grid item xs={12} sm={6}>
            <TextField
              name="firstName"
              label="First Name"
              value={formData.firstName}
              onChange={onFormChange}
              fullWidth
              margin="normal"
              required
              error={validationError && !formData.firstName}
              helperText={
                validationError && !formData.firstName
                  ? "First name is required"
                  : ""
              }
            />
          </Grid>
          <Grid item xs={12} sm={6}>
            <TextField
              name="lastName"
              label="Last Name"
              value={formData.lastName}
              onChange={onFormChange}
              fullWidth
              margin="normal"
              required
              error={validationError && !formData.lastName}
              helperText={
                validationError && !formData.lastName
                  ? "Last name is required"
                  : ""
              }
            />
          </Grid>
          <Grid item xs={12} sm={6}>
            <TextField
              name="email"
              label="Email Address"
              value={formData.email}
              onChange={onFormChange}
              fullWidth
              margin="normal"
              required
              type="email"
              error={validationError && !formData.email}
              helperText={
                validationError && !formData.email ? "Email is required" : ""
              }
            />
          </Grid>
          <Grid item xs={12} sm={6}>
            <TextField
              name="phone"
              label="Phone Number"
              value={formData.phone}
              onChange={onFormChange}
              fullWidth
              margin="normal"
            />
          </Grid>
          <Grid item xs={12} sm={6}>
            <FormControl fullWidth margin="normal">
              <InputLabel id="contractor-type-label">
                Contractor Type
              </InputLabel>
              <Select
                labelId="contractor-type-label"
                name="contractorType"
                value={formData.contractorType}
                label="Contractor Type"
                onChange={onFormChange}
              >
                <MenuItem value="HVAC">HVAC</MenuItem>
                <MenuItem value="Plumbing">Plumbing</MenuItem>
                <MenuItem value="Electrical">Electrical</MenuItem>
                <MenuItem value="Landscaping">Landscaping</MenuItem>
                <MenuItem value="Cleaning">Cleaning</MenuItem>
                <MenuItem value="General Contractor">
                  General Contractor
                </MenuItem>
                <MenuItem value="Other">Other</MenuItem>
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} sm={6}>
            <TextField
              name="hourlyRate"
              label="Hourly Rate ($)"
              value={formData.hourlyRate}
              onChange={onFormChange}
              fullWidth
              margin="normal"
              type="number"
              inputProps={{ min: 0 }}
            />
          </Grid>
          <Grid item xs={12}>
            <Box display="flex" alignItems="center">
              <Typography variant="subtitle1" sx={{ mt: 2 }}>
                Skills
              </Typography>
              <Button
                size="small"
                startIcon={<SmartToyIcon />}
                color="secondary"
                sx={{ ml: 2, mt: 2 }}
                onClick={generateAiSuggestions}
                disabled={aiLoading || !formData.contractorType}
              >
                AI Suggest
              </Button>
            </Box>
            <Paper
              elevation={0}
              sx={{ p: 2, border: "1px solid #e0e0e0", mt: 1 }}
            >
              <Autocomplete
                multiple
                freeSolo
                options={[...skillOptions, ...aiSkillSuggestions]}
                value={formData.skills}
                onChange={handleSkillChange}
                renderTags={(value, getTagProps) =>
                  value.map((option, index) => (
                    <Chip
                      label={option}
                      {...getTagProps({ index })}
                      color="primary"
                    />
                  ))
                }
                renderInput={(params) => (
                  <TextField
                    {...params}
                    variant="outlined"
                    placeholder="Add skills..."
                    helperText="Select from the list or type to add custom skills"
                  />
                )}
              />
              {aiSkillSuggestions.length > 0 && (
                <Box mt={2}>
                  <Typography variant="caption" color="text.secondary">
                    AI Suggested Skills:
                  </Typography>
                  <Box display="flex" flexWrap="wrap" gap={1} mt={1}>
                    {aiSkillSuggestions.map((skill) => (
                      <Chip
                        key={skill}
                        label={skill}
                        size="small"
                        color={
                          formData.skills.includes(skill)
                            ? "primary"
                            : "default"
                        }
                        variant="outlined"
                        onClick={() => {
                          if (!formData.skills.includes(skill)) {
                            handleSkillChange(null, [
                              ...formData.skills,
                              skill,
                            ]);
                          }
                        }}
                        sx={{ m: 0.5 }}
                      />
                    ))}
                  </Box>
                </Box>
              )}
            </Paper>
          </Grid>
          <Grid item xs={12}>
            <Typography variant="subtitle1" sx={{ mt: 2 }}>
              Services Offered
            </Typography>
            <Paper
              elevation={0}
              sx={{ p: 2, border: "1px solid #e0e0e0", mt: 1 }}
            >
              <Autocomplete
                multiple
                freeSolo
                options={[...serviceOptions, ...aiServiceSuggestions]}
                value={formData.services}
                onChange={handleServiceChange}
                renderTags={(value, getTagProps) =>
                  value.map((option, index) => (
                    <Chip
                      label={option}
                      {...getTagProps({ index })}
                      color="secondary"
                    />
                  ))
                }
                renderInput={(params) => (
                  <TextField
                    {...params}
                    variant="outlined"
                    placeholder="Add services..."
                    helperText="Select from the list or type to add custom services"
                  />
                )}
              />
              {aiServiceSuggestions.length > 0 && (
                <Box mt={2}>
                  <Typography variant="caption" color="text.secondary">
                    AI Suggested Services:
                  </Typography>
                  <Box display="flex" flexWrap="wrap" gap={1} mt={1}>
                    {aiServiceSuggestions.map((service) => (
                      <Chip
                        key={service}
                        label={service}
                        size="small"
                        color={
                          formData.services.includes(service)
                            ? "secondary"
                            : "default"
                        }
                        variant="outlined"
                        onClick={() => {
                          if (!formData.services.includes(service)) {
                            handleServiceChange(null, [
                              ...formData.services,
                              service,
                            ]);
                          }
                        }}
                        sx={{ m: 0.5 }}
                      />
                    ))}
                  </Box>
                </Box>
              )}
            </Paper>
          </Grid>
        </Grid>
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose}>Cancel</Button>
        <Button variant="contained" color="primary" onClick={onSubmit}>
          {isCreateMode ? "Create Technician" : "Save Changes"}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default CreateEditDialog;
