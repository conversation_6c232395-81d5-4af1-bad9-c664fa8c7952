const Joi = require("joi");
const ApiError = require("../utils/ApiError");
const { getTaxRate } = require("../utils/invoiceUtils");

/**
 * Line item validation schema
 */
const lineItemSchema = Joi.object({
  description: Joi.string().required().trim().min(3).max(200).messages({
    "string.empty": "Item description is required",
    "string.min": "Item description must be at least 3 characters",
    "string.max": "Item description cannot exceed 200 characters",
  }),

  quantity: Joi.number().required().positive().precision(2).messages({
    "number.base": "Quantity must be a number",
    "number.positive": "Quantity must be greater than 0",
  }),

  unitPrice: Joi.number().required().positive().precision(2).messages({
    "number.base": "Unit price must be a number",
    "number.positive": "Unit price must be greater than 0",
  }),

  type: Joi.string()
    .valid("SERVICE", "PARTS", "LABOR", "MATERIALS")
    .required()
    .messages({
      "any.only": "Invalid item type",
    }),

  taxRate: Joi.number()
    .min(0)
    .max(100)
    .default((parent) => getTaxRate(parent.type) * 100)
    .messages({
      "number.min": "Tax rate cannot be negative",
      "number.max": "Tax rate cannot exceed 100%",
    }),

  tax: Joi.number().min(0).max(100).messages({
    "number.min": "Tax rate cannot be negative",
    "number.max": "Tax rate cannot exceed 100%",
  }),

  discountRate: Joi.number().min(0).max(100).default(0).messages({
    "number.min": "Discount rate cannot be negative",
    "number.max": "Discount rate cannot exceed 100%",
  }),
});

/**
 * Payment validation schema
 */
const paymentSchema = Joi.object({
  amount: Joi.number().required().positive().precision(2).messages({
    "number.base": "Payment amount must be a number",
    "number.positive": "Payment amount must be greater than 0",
  }),

  method: Joi.string()
    .required()
    .valid("CREDIT_CARD", "BANK_TRANSFER", "CHECK", "CASH", "OTHER")
    .messages({
      "any.only": "Invalid payment method",
    }),

  date: Joi.date().default(Date.now).messages({
    "date.base": "Invalid payment date",
  }),

  transactionId: Joi.string().allow("", null).messages({
    "string.base": "Invalid transaction ID",
  }),

  notes: Joi.string().allow("", null).max(500).messages({
    "string.max": "Notes cannot exceed 500 characters",
  }),
});

/**
 * Invoice validation schema
 */
const invoiceSchema = Joi.object({
  job: Joi.string()
    .pattern(/^[0-9a-fA-F]{24}$/)
    .messages({
      "string.pattern.base": "Invalid job ID format",
    }),

  customer: Joi.string()
    .pattern(/^[0-9a-fA-F]{24}$/)
    .messages({
      "string.pattern.base": "Invalid customer ID format",
    }),

  status: Joi.string()
    .valid("DRAFT", "SENT", "VIEWED", "PARTIAL", "PAID", "OVERDUE", "VOID")
    .default("DRAFT")
    .messages({
      "any.only": "Invalid invoice status",
    }),

  issueDate: Joi.date().default(Date.now).messages({
    "date.base": "Invalid issue date",
  }),

  dueDate: Joi.date().min(Joi.ref("issueDate")).messages({
    "date.min": "Due date cannot be earlier than issue date",
  }),

  items: Joi.array().min(1).items(lineItemSchema).required().messages({
    "array.min": "At least one item is required",
    "array.base": "Items must be an array",
  }),

  notes: Joi.object({
    internal: Joi.string().allow("", null).max(1000).messages({
      "string.max": "Internal notes cannot exceed 1000 characters",
    }),
    customer: Joi.string().allow("", null).max(1000).messages({
      "string.max": "Customer notes cannot exceed 1000 characters",
    }),
  }),

  terms: Joi.string().allow("", null).max(2000).messages({
    "string.max": "Terms cannot exceed 2000 characters",
  }),

  attachments: Joi.array().items(
    Joi.object({
      name: Joi.string().required(),
      type: Joi.string().required(),
      url: Joi.string().uri().required(),
      uploadedAt: Joi.date().default(Date.now),
      uploadedBy: Joi.string().pattern(/^[0-9a-fA-F]{24}$/),
    })
  ),
}).options({ stripUnknown: true });

/**
 * Validate invoice data
 * @param {Object} data - Invoice data to validate
 * @param {boolean} isUpdate - Whether this is an update operation
 */
async function validateInvoiceData(data, isUpdate = false) {
  console.log(
    "validateInvoiceData called with:",
    JSON.stringify(data, null, 2)
  );
  try {
    // Set validation options
    const options = {
      abortEarly: false,
      convert: true,
    };

    // Remove required validation for optional fields during update
    const schema = isUpdate
      ? invoiceSchema.fork(["items", "job", "customer"], (schema) =>
          schema.optional()
        )
      : invoiceSchema;

    const validatedData = await schema.validateAsync(data, options);

    // Calculate totals
    if (validatedData.items) {
      validatedData.items = validatedData.items.map((item) => ({
        ...item,
        total: item.quantity * item.unitPrice,
      }));

      validatedData.subtotal = validatedData.items.reduce(
        (sum, item) => sum + item.total,
        0
      );

      validatedData.taxTotal = validatedData.items.reduce((sum, item) => {
        // Use taxRate if available, otherwise use tax field
        const effectiveTaxRate =
          item.taxRate !== undefined ? item.taxRate : item.tax || 0;
        return sum + (item.total * effectiveTaxRate) / 100;
      }, 0);

      validatedData.discountTotal = validatedData.items.reduce(
        (sum, item) => sum + (item.total * item.discountRate) / 100,
        0
      );

      validatedData.total =
        validatedData.subtotal +
        validatedData.taxTotal -
        validatedData.discountTotal;

      validatedData.balance = validatedData.total;
    }

    return validatedData;
  } catch (error) {
    if (error.isJoi) {
      throw new ApiError(400, "Validation Error", error.details);
    }
    throw error;
  }
}

/**
 * Validate payment data
 */
async function validatePaymentData(data) {
  try {
    const validatedData = await paymentSchema.validateAsync(data, {
      abortEarly: false,
    });
    return validatedData;
  } catch (error) {
    if (error.isJoi) {
      throw new ApiError(400, "Invalid payment data", error.details);
    }
    throw error;
  }
}

module.exports = {
  validateInvoiceData,
  validatePaymentData,
  invoiceSchema,
  paymentSchema,
  lineItemSchema,
};
