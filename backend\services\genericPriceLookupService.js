/**
 * Generic Price Lookup Service
 * Handles price lookups for non-electrical items like permits, administrative fees, etc.
 * Uses web search to find approximate pricing information.
 */

const logger = require("../utils/logger");
const ApiError = require("../utils/ApiError");

class GenericPriceLookupService {
  constructor() {
    this.name = "GenericPriceLookupService";
    this.supportedItemTypes = [
      'permit', 'fee', 'inspection', 'administrative', 'labor', 'consultation'
    ];
  }

  /**
   * Determines if an item should use generic price lookup
   * @param {Object} item - The item to check
   * @returns {boolean} - True if item should use generic lookup
   */
  shouldUseGenericLookup(item) {
    if (!item) return false;

    // Check item source
    if (item.source === 'Administrative' || item.source === 'Labor') {
      return true;
    }

    // Check item name/description for keywords
    const searchText = `${item.name || ''} ${item.description || ''}`.toLowerCase();
    
    return this.supportedItemTypes.some(type => 
      searchText.includes(type)
    );
  }

  /**
   * Get estimated price for administrative/permit items with web search fallback
   * @param {Object} item - The item to price
   * @param {Object} options - Lookup options
   * @returns {Promise<Object>} - Price result
   */
  async getEstimatedPrice(item, options = {}) {
    try {
      logger.info(`[GenericPriceLookup] Getting estimated price for: ${item.name}`);

      // First try industry average estimates
      const priceEstimate = this.getPermitPriceEstimate(item);

      if (priceEstimate) {
        logger.info(`[GenericPriceLookup] Using industry average for ${item.name}: $${priceEstimate.price}`);
        return {
          price: priceEstimate.price,
          source: 'estimated',
          sourceId: 'generic_lookup',
          productName: item.name,
          currency: 'USD',
          timestamp: new Date().toISOString(),
          priceInfo: {
            status: 'estimated',
            source: 'generic_lookup',
            lastUpdated: new Date().toISOString(),
            lookupError: null,
            estimationMethod: priceEstimate.method,
            confidence: priceEstimate.confidence
          }
        };
      }

      // If no industry average available, try web search fallback
      logger.info(`[GenericPriceLookup] No industry average found for ${item.name}, trying web search fallback`);
      const webSearchResults = await this.searchWebForPricing(item.name, options);

      if (webSearchResults && webSearchResults.length > 0) {
        // Use the highest confidence result
        const bestResult = webSearchResults.reduce((best, current) => {
          const confidenceOrder = { 'high': 3, 'medium': 2, 'low': 1 };
          return confidenceOrder[current.confidence] > confidenceOrder[best.confidence] ? current : best;
        });

        logger.info(`[GenericPriceLookup] Using web search result for ${item.name}: $${bestResult.price} (confidence: ${bestResult.confidence})`);
        return {
          price: bestResult.price,
          source: 'web_search',
          sourceId: 'web_search_lookup',
          productName: item.name,
          currency: 'USD',
          timestamp: new Date().toISOString(),
          priceInfo: {
            status: 'found',
            source: 'web_search',
            lastUpdated: new Date().toISOString(),
            lookupError: null,
            estimationMethod: bestResult.method,
            confidence: bestResult.confidence,
            sourceUrl: bestResult.sourceUrl,
            extractedFrom: bestResult.extractedFrom
          }
        };
      }

      // If both methods fail, return null
      logger.warn(`[GenericPriceLookup] No pricing found for ${item.name} using any method`);
      return null;

    } catch (error) {
      logger.error(`[GenericPriceLookup] Error getting price for ${item.name}:`, error);
      throw new ApiError(500, `Generic price lookup failed: ${error.message}`);
    }
  }

  /**
   * Get price estimates for common permit/administrative items
   * @param {Object} item - The item to estimate
   * @returns {Object|null} - Price estimate with method and confidence
   */
  getPermitPriceEstimate(item) {
    const itemText = `${item.name || ''} ${item.description || ''}`.toLowerCase();

    // Electrical permit estimates (varies by location)
    if (itemText.includes('electrical permit')) {
      return {
        price: 150, // Average electrical permit fee
        method: 'industry_average',
        confidence: 'medium'
      };
    }

    // Building permit estimates
    if (itemText.includes('building permit')) {
      return {
        price: 200,
        method: 'industry_average', 
        confidence: 'medium'
      };
    }

    // Inspection fees
    if (itemText.includes('inspection')) {
      return {
        price: 100,
        method: 'industry_average',
        confidence: 'medium'
      };
    }

    // Utility coordination fees
    if (itemText.includes('utility') && (itemText.includes('coordination') || itemText.includes('disconnect'))) {
      return {
        price: 75,
        method: 'industry_average',
        confidence: 'low'
      };
    }

    // Administrative processing fees
    if (itemText.includes('processing') || itemText.includes('administrative')) {
      return {
        price: 50,
        method: 'industry_average',
        confidence: 'low'
      };
    }

    // Labor estimates (per hour)
    if (itemText.includes('labor') || itemText.includes('hour')) {
      return {
        price: 85, // Average electrician hourly rate
        method: 'industry_average',
        confidence: 'medium'
      };
    }

    return null;
  }

  /**
   * Search web for pricing information using web search
   * @param {string} query - Search query
   * @param {Object} options - Search options
   * @returns {Promise<Array>} - Search results with pricing
   */
  async searchWebForPricing(query, options = {}) {
    try {
      logger.info(`[GenericPriceLookup] Searching web for pricing: ${query}`);

      // Use web search to find pricing information
      const webSearch = require('../utils/webSearchService');
      const searchQuery = `${query} price cost estimate`;

      const searchResults = await webSearch.searchForPricing(searchQuery, {
        numResults: 5,
        ...options
      });

      if (searchResults && searchResults.length > 0) {
        logger.info(`[GenericPriceLookup] Found ${searchResults.length} web search results for: ${query}`);
        return searchResults;
      }

      return [];
    } catch (error) {
      logger.error(`[GenericPriceLookup] Web search failed for ${query}:`, error);
      return [];
    }
  }

  /**
   * Get service diagnostics
   * @returns {Object} - Service status and capabilities
   */
  getDiagnostics() {
    return {
      status: 'OK',
      name: this.name,
      supportedTypes: this.supportedItemTypes,
      capabilities: [
        'permit_estimation',
        'labor_estimation', 
        'administrative_fee_estimation'
      ],
      lastUpdated: new Date().toISOString()
    };
  }
}

module.exports = new GenericPriceLookupService();
