import React from "react";
import {
  Typo<PERSON>,
  List,
  ListItem,
  ListItemAvatar,
  Avatar,
  ListItemText,
  Button,
  Divider,
  Paper,
  Box,
} from "@mui/material";
import { Check as SelectIcon, Link as LinkIcon } from "@mui/icons-material";

// Helper to format currency (consider moving to a utils file if used elsewhere)
const formatCurrency = (amount) => {
  // Handle different data types and edge cases
  if (amount === null || amount === undefined) {
    return "$--.--"; // Indicate missing price
  }

  // Convert string to number if needed
  if (typeof amount === "string") {
    const parsed = parseFloat(amount.replace(/[^0-9.-]+/g, ""));
    if (isNaN(parsed)) {
      return "$--.--"; // Indicate invalid price
    }
    amount = parsed;
  }

  if (typeof amount !== "number" || isNaN(amount)) {
    return "$--.--"; // Indicate missing price
  }

  // Show prices of 0 as actual $0.00 instead of missing
  return `$${amount.toFixed(2)}`;
};

const MaterialSearchResultsDisplay = ({ results = [], onSelect }) => {
  if (!results || results.length === 0) {
    return (
      <Paper elevation={2} sx={{ mt: 3, p: 2 }}>
        <Typography variant="h6" gutterBottom sx={{ mb: 2 }}>
          Select a Material Match
        </Typography>
        <Typography
          variant="body1"
          sx={{ textAlign: "center", color: "text.secondary" }}
        >
          No materials found or still loading...
        </Typography>
      </Paper>
    );
  }

  return (
    <Paper elevation={2} sx={{ mt: 3, p: 2 }}>
      <Typography variant="h6" gutterBottom sx={{ mb: 2 }}>
        Select a Material Match ({results.length})
      </Typography>
      <List sx={{ maxHeight: 400, overflow: "auto" }}>
        {results.map((item, index) => (
          <React.Fragment key={item.id || item.sku || `material-${index}`}>
            {" "}
            {/* Added item.id and more robust key */}
            <ListItem alignItems="flex-start" sx={{ gap: 2, py: 1.5 }}>
              {" "}
              {/* Added some padding */}
              <ListItemAvatar>
                <Avatar
                  variant="rounded"
                  src={item.imageUrl || item.image}
                  alt={item.name || item.title || "Material Image"}
                  sx={{
                    width: 70,
                    height: 70,
                    mr: 1.5,
                    border: "1px solid lightgray",
                  }} /* Slightly larger image */
                >
                  {!(item.imageUrl || item.image) &&
                    (item.name || item.title
                      ? (item.name || item.title).charAt(0).toUpperCase()
                      : "?")}
                </Avatar>
              </ListItemAvatar>
              <ListItemText
                primary={
                  <Typography
                    variant="subtitle1"
                    component="div"
                    sx={{ fontWeight: "bold", mb: 0.5, lineHeight: 1.3 }}
                  >
                    {item.name || item.title || "Product Title Not Available"}
                  </Typography>
                }
                secondaryTypographyProps={{ component: "div" }} // Ensure secondary is a div for block elements
                secondary={
                  <>
                    <Typography
                      component="div"
                      variant="body2"
                      color="text.secondary"
                      display="block"
                      sx={{ mb: 0.25 }}
                    >
                      Model#: {item.modelNumber || "N/A"}
                    </Typography>
                    {item.averageRating &&
                    typeof item.averageRating === "number" &&
                    item.averageRating > 0 &&
                    typeof item.reviewCount === "number" ? (
                      <Typography
                        component="div"
                        variant="body2"
                        color="text.secondary"
                        display="block"
                        sx={{ mb: 0.25 }}
                      >
                        Rating: {item.averageRating.toFixed(1)} (
                        {item.reviewCount} reviews)
                      </Typography>
                    ) : (
                      <Typography
                        component="div"
                        variant="body2"
                        color="text.secondary"
                        display="block"
                        sx={{ mb: 0.25 }}
                      >
                        Rating: Not Rated
                      </Typography>
                    )}
                    <Typography
                      component="div"
                      variant="body2"
                      color="text.secondary"
                      display="block"
                      sx={{ mb: 0.25 }}
                    >
                      SKU: {item.sku || "N/A"}
                    </Typography>
                    <Typography
                      component="div"
                      variant="body2"
                      color="text.secondary"
                      display="block"
                      sx={{ mb: 0.25 }}
                    >
                      Brand: {item.brand || "N/A"}
                    </Typography>
                    <Typography
                      component="div"
                      variant="body2"
                      color="text.secondary"
                      display="block"
                      sx={{ mb: 0.25 }}
                    >
                      Source:{" "}
                      {item.source || item.sourceName || "Unknown Source"}
                    </Typography>
                    <Typography
                      component="div"
                      variant="body2"
                      color="text.secondary"
                      display="block"
                      sx={{ mb: 0.25 }}
                    >
                      Pack Size:{" "}
                      {item.packQuantity && item.packQuantity > 0
                        ? item.packQuantity > 1
                          ? `${item.packQuantity}-Pack`
                          : "Single Item"
                        : "N/A"}
                    </Typography>
                    <Typography
                      component="div"
                      variant="body1"
                      color="text.primary"
                      sx={{ fontWeight: "bold", mt: 0.75 }}
                    >
                      Price:{" "}
                      {formatCurrency(
                        item.price ||
                          item.unitPrice ||
                          item.cost ||
                          item.finalPrice
                      )}
                      {item.packQuantity > 1 &&
                        item.unitCostPrice &&
                        item.unitCostPrice !==
                          (item.price || item.unitPrice) && (
                          <Typography
                            component="span"
                            variant="caption"
                            color="text.secondary"
                            sx={{ ml: 0.5 }}
                          >
                            ({formatCurrency(item.unitCostPrice)}/unit)
                          </Typography>
                        )}
                    </Typography>
                  </>
                }
              />
              <Box
                sx={{
                  display: "flex",
                  flexDirection: "column",
                  gap: 1,
                  alignSelf: "center",
                  minWidth: 80,
                }}
              >
                <Button
                  variant="outlined"
                  size="small"
                  startIcon={<SelectIcon />}
                  onClick={() => onSelect(item)}
                  fullWidth
                >
                  Select
                </Button>
                {(item.url || item.productUrl) && (
                  <Button
                    variant="text"
                    size="small"
                    startIcon={<LinkIcon />}
                    href={item.url || item.productUrl}
                    target="_blank"
                    rel="noopener noreferrer"
                    fullWidth
                  >
                    View
                  </Button>
                )}
              </Box>
            </ListItem>
            {index < results.length - 1 && (
              <Divider variant="inset" component="li" />
            )}
          </React.Fragment>
        ))}
      </List>
    </Paper>
  );
};

export default MaterialSearchResultsDisplay;
