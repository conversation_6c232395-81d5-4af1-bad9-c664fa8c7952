import React, { useState, useEffect, useMemo } from "react";
import { useDispatch } from "react-redux";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Button,
  Grid,
  Box,
  CircularProgress,
  Alert,
  Typography,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  OutlinedInput,
} from "@mui/material";
import { useSnackbar } from "notistack";
import { createCustomer } from "../../slices/customerSlice";
import AddressAutocomplete from "../AddressAutocomplete"; // Assuming this is reusable
import { useGoogleMaps } from "../GoogleMapsProvider"; // Needed for AddressAutocomplete

const CreateCustomerModal = ({ open, onClose, onSuccess }) => {
  const dispatch = useDispatch();
  const { enqueueSnackbar } = useSnackbar();
  const { isLoaded } = useGoogleMaps(); // For AddressAutocomplete

  const initialCustomerData = useMemo(
    () => ({
      businessName: "",
      contactPerson: { firstName: "", lastName: "" },
      email: "",
      phone: "",
      alternatePhone: "", // Added alternate phone
      address: {
        street: "",
        city: "",
        state: "",
        zipCode: "",
        fullAddress: "",
      },
      notes: "", // Added notes
      tags: [],
      source: "", // Added source
    }),
    []
  );

  const [customerData, setCustomerData] = useState(initialCustomerData);
  const [addressSearchTerm, setAddressSearchTerm] = useState("");
  const [formErrors, setFormErrors] = useState({});
  const [loading, setLoading] = useState(false);
  const [apiError, setApiError] = useState(null);
  const [newTag, setNewTag] = useState("");
  const availableTags = ["Residential", "Commercial", "VIP", "Regular", "New"]; // Match CreateCustomer page

  // Reset form when modal opens/closes
  useEffect(() => {
    if (open) {
      setCustomerData(initialCustomerData);
      setAddressSearchTerm("");
      setFormErrors({});
      setApiError(null);
      setLoading(false);
      setNewTag("");
    }
  }, [open, initialCustomerData]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setApiError(null); // Clear API error on change
    if (name.includes(".")) {
      const [parent, child] = name.split(".");
      setCustomerData((prev) => ({
        ...prev,
        [parent]: { ...prev[parent], [child]: value },
      }));
    } else {
      setCustomerData((prev) => ({ ...prev, [name]: value }));
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
    // eslint-disable-next-line react-hooks/exhaustive-deps
  };

  const handleTagChange = (event) => {
    const {
      target: { value },
    } = event;
    setCustomerData((prev) => ({
      ...prev,
      tags: typeof value === "string" ? value.split(",") : value,
    }));
  };

  const handleNewTagAdd = () => {
    if (newTag && !customerData.tags.includes(newTag)) {
      setCustomerData((prev) => ({
        ...prev,
        tags: [...prev.tags, newTag],
      }));
      setNewTag("");
    }
  };

  const handleAddressSelected = (addressDetails) => {
    setCustomerData((prev) => ({
      ...prev,
      address: {
        street: addressDetails.street,
        city: addressDetails.city,
        state: addressDetails.state,
        zipCode: addressDetails.zipCode,
        fullAddress: addressDetails.fullAddress,
      },
    }));
    setAddressSearchTerm(addressDetails.fullAddress); // Update search term display
  };

  const validateForm = () => {
    const errors = {};
    if (
      !customerData.businessName &&
      !customerData.contactPerson.firstName &&
      !customerData.contactPerson.lastName
    ) {
      errors.name = "Either Business Name or First/Last Name is required";
    }
    if (!customerData.email) errors.email = "Email is required";
    else if (!/\S+@\S+\.\S+/.test(customerData.email))
      errors.email = "Email is invalid";
    if (!customerData.phone) errors.phone = "Phone number is required";
    // Add more specific validations as needed (e.g., phone format)
    if (!customerData.address.street)
      errors.street = "Street address is required";
    if (!customerData.address.city) errors.city = "City is required";
    if (!customerData.address.state) errors.state = "State is required";
    if (!customerData.address.zipCode) errors.zipCode = "ZIP code is required";

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setApiError(null);
    if (!validateForm()) {
      return;
    }

    setLoading(true);
    try {
      const resultAction = await dispatch(
        createCustomer(customerData)
      ).unwrap();
      enqueueSnackbar("Customer created successfully", { variant: "success" });
      onSuccess(resultAction); // Pass the newly created customer data back
      onClose(); // Close the modal
    } catch (err) {
      console.error("Failed to create customer:", err);
      setApiError(
        err.message || "An unexpected error occurred creating customer."
      );
      enqueueSnackbar("Failed to create customer. Please try again.", {
        variant: "error",
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle>Create New Customer</DialogTitle>
      <DialogContent>
        {apiError && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {apiError}
          </Alert>
        )}
        <Box component="form" onSubmit={handleSubmit} noValidate>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            {/* Replicate form fields from CreateCustomer.js */}
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Business Name"
                name="businessName"
                value={customerData.businessName}
                onChange={handleChange}
                error={!!formErrors.name || !!formErrors.businessName}
                helperText={formErrors.name || formErrors.businessName}
                disabled={loading}
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Contact First Name"
                name="contactPerson.firstName"
                value={customerData.contactPerson.firstName}
                onChange={handleChange}
                error={!!formErrors.name || !!formErrors.contactPersonFirstName}
                helperText={
                  formErrors.name || formErrors.contactPersonFirstName
                }
                disabled={loading}
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Contact Last Name"
                name="contactPerson.lastName"
                value={customerData.contactPerson.lastName}
                onChange={handleChange}
                error={!!formErrors.name || !!formErrors.contactPersonLastName}
                helperText={formErrors.name || formErrors.contactPersonLastName}
                disabled={loading}
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <TextField
                required
                fullWidth
                label="Email"
                name="email"
                type="email"
                value={customerData.email}
                onChange={handleChange}
                error={!!formErrors.email}
                helperText={formErrors.email}
                disabled={loading}
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <TextField
                required
                fullWidth
                label="Phone"
                name="phone"
                value={customerData.phone}
                onChange={handleChange}
                error={!!formErrors.phone}
                helperText={formErrors.phone}
                disabled={loading}
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Alternate Phone"
                name="alternatePhone"
                value={customerData.alternatePhone}
                onChange={handleChange}
                disabled={loading}
              />
            </Grid>

            {/* Address Section */}
            <Grid item xs={12} sx={{ mt: 1 }}>
              <Typography variant="subtitle1" gutterBottom>
                Address
              </Typography>
            </Grid>
            <Grid item xs={12}>
              <AddressAutocomplete
                value={addressSearchTerm}
                onChange={(value) => setAddressSearchTerm(value)}
                onAddressSelected={handleAddressSelected}
                label="Search Address"
                textFieldProps={{
                  fullWidth: true,
                  helperText: "Start typing to get address suggestions",
                  disabled: !isLoaded || loading,
                }}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                required
                fullWidth
                label="Street"
                name="address.street"
                value={customerData.address.street}
                onChange={handleChange}
                error={!!formErrors.street}
                helperText={formErrors.street}
                disabled={loading}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                required
                fullWidth
                label="City"
                name="address.city"
                value={customerData.address.city}
                onChange={handleChange}
                error={!!formErrors.city}
                helperText={formErrors.city}
                disabled={loading}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                required
                fullWidth
                label="State"
                name="address.state"
                value={customerData.address.state}
                onChange={handleChange}
                error={!!formErrors.state}
                helperText={formErrors.state}
                disabled={loading}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                required
                fullWidth
                label="Zip Code"
                name="address.zipCode"
                value={customerData.address.zipCode}
                onChange={handleChange}
                error={!!formErrors.zipCode}
                helperText={formErrors.zipCode}
                disabled={loading}
              />
            </Grid>

            {/* Tags Section */}
            <Grid item xs={12} sx={{ mt: 1 }}>
              <Typography variant="subtitle1" gutterBottom>
                Tags & Notes
              </Typography>
            </Grid>
            <Grid item xs={12}>
              <FormControl fullWidth disabled={loading}>
                <InputLabel id="tags-label-modal">Tags</InputLabel>
                <Select
                  labelId="tags-label-modal"
                  multiple
                  value={customerData.tags}
                  onChange={handleTagChange}
                  input={<OutlinedInput label="Tags" />}
                  renderValue={(selected) => (
                    <Box sx={{ display: "flex", flexWrap: "wrap", gap: 0.5 }}>
                      {selected.map((value) => (
                        <Chip key={value} label={value} size="small" />
                      ))}
                    </Box>
                  )}
                >
                  {availableTags.map((tag) => (
                    <MenuItem key={tag} value={tag}>
                      {tag}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12}>
              <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                <TextField
                  fullWidth
                  label="Add Custom Tag"
                  value={newTag}
                  onChange={(e) => setNewTag(e.target.value)}
                  size="small"
                  disabled={loading}
                />
                <Button
                  variant="outlined"
                  onClick={handleNewTagAdd}
                  disabled={!newTag || loading}
                  size="small"
                >
                  Add
                </Button>
              </Box>
            </Grid>

            {/* Notes and Source */}
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Notes"
                name="notes"
                multiline
                rows={3}
                value={customerData.notes}
                onChange={handleChange}
                disabled={loading}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Source (e.g., Referral, Website)"
                name="source"
                value={customerData.source}
                onChange={handleChange}
                disabled={loading}
              />
            </Grid>
          </Grid>
        </Box>
      </DialogContent>
      <DialogActions sx={{ p: 2 }}>
        <Button onClick={onClose} disabled={loading}>
          Cancel
        </Button>
        <Button variant="contained" onClick={handleSubmit} disabled={loading}>
          {loading ? <CircularProgress size={24} /> : "Save Customer"}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default CreateCustomerModal;
