/**
 * Retry utility functions for handling transient failures
 */

/**
 * Executes a function with exponential backoff retry logic
 * @param {Function} fn - The async function to execute
 * @param {Object} options - Retry options
 * @param {number} options.maxRetries - Maximum number of retries (default: 3)
 * @param {number} options.baseDelay - Base delay in ms (default: 300)
 * @param {number} options.maxDelay - Maximum delay in ms (default: 5000)
 * @param {Function} options.shouldRetry - Function to determine if retry should be attempted (default: retry on any error)
 * @param {Function} options.onRetry - Function called before each retry attempt
 * @param {Function} options.validateResponse - Function to validate the response (optional)
 * @returns {Promise<any>} - The result of the function call
 */
const { getRetryDelay } = require("../utils/aiErrors");
const logger = require("./logger"); // Assuming logger.js is in the same directory or path is correct

async function withRetry(fn, options = {}) {
  const {
    maxRetries = 3,
    maxDelay = 5000,
    shouldRetry = () => true,
    onRetry = () => {},
    validateResponse = null,
  } = options;

  let lastError;

  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      // Execute the function
      const result = await fn();

      // Validate the response if a validator is provided
      if (typeof validateResponse === "function") {
        try {
          validateResponse(result, options); // Pass options if validator needs them
        } catch (validationError) {
          // Treat validation error like any other error for retry logic
          throw validationError;
        }
      }

      // Return the result if execution and validation were successful
      return result;
    } catch (error) {
      lastError = error;

      // Check if we should retry
      if (attempt >= maxRetries || !shouldRetry(error, attempt)) {
        throw error;
      }

      // Calculate delay using the centralized getRetryDelay function
      const delay = getRetryDelay(error, attempt);

      // Log retry attempt using logger
      // The onRetry callback (e.g., in geminiService) typically provides more context like model name.
      // This log can be a more generic one from withRetry itself.
      logger.info(
        `[withRetry] Attempt ${attempt + 1}/${maxRetries} after ${Math.round(
          delay
        )}ms due to: ${error.message}`
      );

      // Call onRetry callback
      onRetry(error, attempt, delay);

      // Wait before next attempt
      // Using the delay from getRetryDelay. If cappedDelay was used above, use it here.
      await new Promise((resolve) => setTimeout(resolve, delay));
    }
  }

  // This should never be reached due to the throw in the catch block
  throw lastError;
}

/**
 * Circuit breaker implementation to prevent cascading failures
 */
class CircuitBreaker {
  constructor(options = {}) {
    this.options = {
      failureThreshold: options.failureThreshold || 5,
      resetTimeout: options.resetTimeout || 30000, // 30 seconds
      monitorInterval: options.monitorInterval || 5000, // 5 seconds
      ...options,
    };

    this.state = "CLOSED"; // CLOSED, OPEN, HALF_OPEN
    this.failureCount = 0;
    this.lastFailureTime = null;
    this.successCount = 0;

    // Only start monitoring in non-test environments
    if (process.env.NODE_ENV !== "test") {
      this.startMonitoring();
    }
  }

  startMonitoring() {
    this.monitorInterval = setInterval(() => {
      if (
        this.state === "OPEN" &&
        Date.now() - this.lastFailureTime >= this.options.resetTimeout
      ) {
        // Transition to HALF_OPEN after reset timeout
        this.state = "HALF_OPEN";
        this.successCount = 0;
        console.log("Circuit breaker state changed to HALF_OPEN");
      }
    }, this.options.monitorInterval);
  }

  stop() {
    if (this.monitorInterval) {
      clearInterval(this.monitorInterval);
    }
  }

  /**
   * Execute a function with circuit breaker protection
   * @param {Function} fn - The async function to execute
   * @returns {Promise<any>} - The result of the function call
   */
  async execute(fn) {
    if (this.state === "OPEN") {
      throw new Error("Circuit breaker is OPEN - service unavailable");
    }

    try {
      const result = await fn();

      // On success in HALF_OPEN state, transition back to CLOSED
      if (this.state === "HALF_OPEN") {
        this.successCount++;
        if (this.successCount >= 2) {
          // Require 2 consecutive successes
          this.state = "CLOSED";
          this.failureCount = 0;
          console.log("Circuit breaker state changed to CLOSED");
        }
      }

      return result;
    } catch (error) {
      this.recordFailure();
      throw error;
    }
  }

  recordFailure() {
    this.lastFailureTime = Date.now();

    if (this.state === "HALF_OPEN") {
      // Immediate transition back to OPEN on failure in HALF_OPEN
      this.state = "OPEN";
      console.log("Circuit breaker state changed back to OPEN");
      return;
    }

    if (this.state === "CLOSED") {
      this.failureCount++;
      if (this.failureCount >= this.options.failureThreshold) {
        this.state = "OPEN";
        console.log("Circuit breaker state changed to OPEN");
      }
    }
  }

  reset() {
    this.state = "CLOSED";
    this.failureCount = 0;
    this.lastFailureTime = null;
    this.successCount = 0;
    console.log("Circuit breaker reset to CLOSED state");
  }

  getState() {
    return {
      state: this.state,
      failureCount: this.failureCount,
      lastFailureTime: this.lastFailureTime,
      successCount: this.successCount,
    };
  }
}

// Create circuit breakers for different services
const circuitBreakers = {
  gemini: new CircuitBreaker({ failureThreshold: 5, resetTimeout: 60000 }), // Main breaker for general Gemini calls
  geminiParsing: new CircuitBreaker({
    failureThreshold: 3,
    resetTimeout: 45000,
  }), // Isolated breaker for LLM parsing step
  geminiGrounding: new CircuitBreaker({
    failureThreshold: 4,
    resetTimeout: 60000,
  }), // Breaker for grounding calls
  deepseek: new CircuitBreaker({ failureThreshold: 5, resetTimeout: 60000 }), // Main breaker for Deepseek calls
  // Add more circuit breakers for other services as needed
};

// Cleanup function to stop all circuit breakers
function cleanup() {
  Object.values(circuitBreakers).forEach((breaker) => {
    if (breaker && typeof breaker.stop === "function") {
      breaker.stop();
    }
  });
}

module.exports = {
  withRetry,
  CircuitBreaker,
  circuitBreakers, // Export the instances map
  cleanup, // Export cleanup function
};
