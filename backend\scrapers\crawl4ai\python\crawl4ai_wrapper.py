#!/usr/bin/env python3
"""
crawl4ai_wrapper.py - Bridge between Node.js and Crawl4AI.

This script serves as an interface between the Workiz backend (JavaScript)
and the Crawl4AI Python library. It handles:
- Receiving JSON configuration via stdin
- Running the appropriate Crawl4AI functions
- Returning results as JSON via stdout
"""

import io
import asyncio
import json
import logging
import os
import sys
import traceback
from datetime import datetime
from typing import Dict, Any, Optional, Type

# Configure sys.stdout and sys.stderr for UTF-8.
# JSON output to Node.js goes via sys.stdout.
# Script's own error/debug logs go via sys.stderr.
sys.stdout = io.TextIOWrapper(
    sys.stdout.buffer, encoding='utf-8', errors='replace', line_buffering=True
)
sys.stderr = io.TextIOWrapper(
    sys.stderr.buffer, encoding='utf-8', errors='replace', line_buffering=True
)

# Set up logging.
project_root_path = os.path.dirname(
    os.path.dirname(
        os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    )
)
log_dir = os.path.join(project_root_path, "logs")
os.makedirs(log_dir, exist_ok=True)
log_file_name = f"crawl4ai_{datetime.now().strftime('%Y%m%d')}.log"
log_file_path = os.path.join(log_dir, log_file_name)

logging.basicConfig(
    level=logging.WARNING,  # Reduced from INFO to WARNING for less noise
    format='%(asctime)s [%(levelname)s] [%(filename)s:%(lineno)d] %(message)s',
    handlers=[
        logging.FileHandler(log_file_path, encoding='utf-8'),
        logging.StreamHandler(sys.stderr)  # Logger output to UTF-8 stderr
    ]
)
logger = logging.getLogger("crawl4ai_wrapper")

# Placeholder for Crawl4AI components
AsyncWebCrawler: Optional[Type] = None
BrowserConfig: Optional[Type] = None
CrawlerRunConfig: Optional[Type] = None
JsonCssExtractionStrategy: Optional[Type] = None
PruningContentFilter: Optional[Type] = None
CrawlResult: Optional[Type] = None
Crawl4AI_IMPORTED = False

try:
    from crawl4ai import (  # type: ignore[import-untyped]
        AsyncWebCrawler as _AsyncWebCrawler,
        BrowserConfig as _BrowserConfig,
        CrawlerRunConfig as _CrawlerRunConfig,
        JsonCssExtractionStrategy as _JsonCssExtractionStrategy,
        PruningContentFilter as _PruningContentFilter,
        CrawlResult as _CrawlResult
    )

    AsyncWebCrawler = _AsyncWebCrawler
    BrowserConfig = _BrowserConfig
    CrawlerRunConfig = _CrawlerRunConfig
    JsonCssExtractionStrategy = _JsonCssExtractionStrategy
    PruningContentFilter = _PruningContentFilter
    CrawlResult = _CrawlResult

    # Crawl4AI components imported successfully (reduced logging)
    Crawl4AI_IMPORTED = True
except ImportError as e:
    logger.error(
        f"Failed to import Crawl4AI: {str(e)}. Ensure Crawl4AI is "
        "installed in the Python environment."
    )
    # Error reported as JSON by main() if Crawl4AI_IMPORTED is False.


async def run_crawler(url: str, options: Dict[str, Any]) -> Dict[str, Any]:
    # Starting crawl operation (reduced logging)

    if not Crawl4AI_IMPORTED or not BrowserConfig or \
       not CrawlerRunConfig or not AsyncWebCrawler:
        error_msg = "Crawl4AI components not imported. Cannot run crawler."
        logger.error(error_msg)
        return {'success': False, 'url': url, 'error': error_msg}

    json_output_stream = sys.stdout
    crawler_internal_stdout_capture = io.StringIO()
    original_script_stdout = sys.stdout  # Should be json_output_stream

    try:
        browser_config_args = {
            'headless': options.get('headless', True),
            'browser_type': 'chromium',  # Explicitly set browser type
            'verbose': options.get('verbose', False)
        }
        user_agent = options.get('userAgent')
        if user_agent:
            browser_config_args['user_agent'] = user_agent
        browser_conf = BrowserConfig(**browser_config_args)
        logger.debug(f"BrowserConfig created with args: {browser_config_args}")

        # Create crawler run configuration with all supported options
        run_config_args = {
            'page_timeout': options.get('timeout', 60000)
        }

        # Add screenshot support
        if options.get('screenshot'):
            run_config_args['screenshot'] = True
            # Handle different naming conventions for screenshot wait time
            screenshot_wait = options.get(
                'screenshotWaitFor',
                options.get('screenshot_wait_for', 3)
            )
            run_config_args['screenshot_wait_for'] = screenshot_wait
            # Screenshot enabled with reduced logging

        # Add PDF support
        if options.get('pdf'):
            run_config_args['pdf'] = True
            # PDF capture enabled

        # Add verbose mode
        if options.get('verbose'):
            run_config_args['verbose'] = True

        # Add wait_for conditions if provided
        wait_for = options.get('waitFor')
        if wait_for:
            run_config_args['wait_for'] = wait_for
            logger.debug(f"Wait conditions set: {wait_for}")

        # Add JavaScript code execution if provided
        js_code = options.get('js_code')
        if js_code:
            run_config_args['js_code'] = js_code
            logger.debug("JavaScript code execution configured")

        run_conf = CrawlerRunConfig(**run_config_args)
        logger.debug(f"CrawlerRunConfig created with args: {run_config_args}")

        # 🚀 CRITICAL FIX: Enhanced content filtering with proper error handling
        content_filter_opts = options.get('contentFilter')
        if content_filter_opts:
            try:
                # Check if PruningContentFilter is available and supported
                if PruningContentFilter and hasattr(run_conf, 'content_filter'):
                    # Apply PruningContentFilter with enhanced configuration
                    run_conf.content_filter = PruningContentFilter(
                        threshold=content_filter_opts.get('threshold', 0.3),  # Reduced threshold for better content retention
                        threshold_type=content_filter_opts.get('thresholdType', "fixed"),
                        min_word_threshold=content_filter_opts.get('minWordThreshold', 10)  # Increased minimum words
                    )
                    logger.debug("PruningContentFilter applied successfully")
                elif PruningContentFilter:
                    # Try alternative content filtering approaches
                    logger.info("Using alternative content filtering approach")
                    # Set word count threshold as alternative
                    if hasattr(run_conf, 'word_count_threshold'):
                        run_conf.word_count_threshold = content_filter_opts.get('minWordThreshold', 10)
                    if hasattr(run_conf, 'remove_overlay_elements'):
                        run_conf.remove_overlay_elements = True
                else:
                    logger.debug("PruningContentFilter not available, using built-in filtering")
                    # Use built-in filtering options instead
                    if hasattr(run_conf, 'word_count_threshold'):
                        run_conf.word_count_threshold = content_filter_opts.get('minWordThreshold', 10)
            except Exception as e:
                logger.warning(f"Content filter configuration failed: {str(e)}, proceeding without custom filtering")
        else:
            # Apply default content filtering to improve performance
            try:
                if hasattr(run_conf, 'word_count_threshold'):
                    run_conf.word_count_threshold = 10  # Default minimum word threshold
                if hasattr(run_conf, 'remove_overlay_elements'):
                    run_conf.remove_overlay_elements = True
                logger.debug("Applied default content filtering settings")
            except Exception as e:
                logger.debug(f"Default content filtering setup failed: {str(e)}")

        extraction_schema = options.get('extractionSchema')
        if extraction_schema and JsonCssExtractionStrategy and \
           hasattr(run_conf, 'extraction_strategy'):
            # Applying JsonCssExtractionStrategy
            run_conf.extraction_strategy = JsonCssExtractionStrategy(
                extraction_schema
            )
            # JsonCssExtractionStrategy applied
        elif extraction_schema:
            logger.warning(
                "JsonCssExtractionStrategy not applied (unavailable or not "
                "supported by CrawlerRunConfig)."
            )

        result_from_arun = None
        crawl_exception = None

        sys.stdout = crawler_internal_stdout_capture
        try:
            # Starting AsyncWebCrawler.arun
            # Use a more robust approach to handle browser lifecycle
            # Implement retry logic for transient errors
            # Increase default to 2 retries
            max_retries = options.get('maxRetries', 2)
            retry_count = 0

            while retry_count < max_retries:
                try:
                    # Create crawler instance outside context manager
                    # for better control
                    crawler = AsyncWebCrawler(config=browser_conf)
                    await crawler.start()  # Explicitly start the crawler
                    result_from_arun = await crawler.arun(
                        url=url, config=run_conf
                    )
                    await crawler.close()  # Explicitly close the crawler
                    break  # Success, exit retry loop
                except Exception as crawl_error:
                    # Ensure crawler is closed even if an error occurs
                    try:
                        if 'crawler' in locals():
                            await crawler.close()
                    except Exception as close_error:
                        logger.warning(
                            f"Error closing crawler after crawl error: "
                            f"{close_error}"
                        )

                    # Log additional details about the error
                    logger.error(
                        f"Detailed crawl error for {url}: "
                        f"{type(crawl_error).__name__}: {str(crawl_error)}"
                    )

                    retry_count += 1
                    error_str = str(crawl_error).lower()

                    # Check if this is a TargetClosedError or similar 
                    # browser closure error
                    is_target_closed = (
                        'target closed' in error_str or 
                        'browser has been closed' in error_str or 
                        'context has been closed' in error_str or
                        'page.*closed' in error_str or
                        'target.*closed' in error_str
                    )

                    # Check if it's a timeout error
                    is_timeout = (
                        'timeout' in error_str or 
                        'timed out' in error_str or 
                        'navigation.*timeout' in error_str
                    )

                    # If this is the last retry attempt or not a retryable 
                    # error, re-raise
                    if (retry_count >= max_retries or 
                            not (is_target_closed or is_timeout)):
                        if is_target_closed:
                            logger.warning(
                                f"Browser was closed during crawl for "
                                f"{url} "
                                f"(attempt {retry_count}/{max_retries}). "
                                f"This may be due to timeout or "
                                f"external interruption."
                            )
                            # Try to provide a more user-friendly error message
                            raise Exception(
                                f"Browser session was terminated "
                                f"during crawl. "
                                f"This can happen due to timeouts, "
                                f"website security "
                                f"measures, or external "
                                f"interruptions. URL: {url}"
                            )
                        elif is_timeout:
                            logger.warning(
                                f"Timeout occurred during crawl for "
                                f"{url} (attempt {retry_count}/{max_retries})."
                            )
                            raise Exception(
                                f"Crawl operation timed out. This can "
                                f"happen on "
                                f"slow websites or when the page "
                                f"takes too long "
                                f"to load. URL: {url}"
                            )
                        else:
                            # Re-raise the original error for other cases
                            raise crawl_error
                    else:
                        # Log retry attempt and continue
                        logger.warning(
                            f"Transient error occurred during crawl for "
                            f"{url} (attempt {retry_count}/{max_retries}). "
                            f"Retrying..."
                        )
                        # Exponential backoff before retry
                    await asyncio.sleep(2 ** retry_count)
            # AsyncWebCrawler.arun finished
        except Exception as ce:
            logger.error(
                f"Exception during AsyncWebCrawler.arun: {str(ce)}",
                exc_info=True
            )
            crawl_exception = ce
        finally:
            sys.stdout = original_script_stdout
            captured_output = crawler_internal_stdout_capture.getvalue()
            if captured_output:
                logger.debug(
                    f"Internal stdout from crawler for {url}:\n"
                    f"{captured_output.strip()}"
                )
            crawler_internal_stdout_capture.close()

        if crawl_exception:
            err_detail = traceback.format_exc()
            return {
                'success': False, 'url': url,
                'error': f"Crawl execution failed: {str(crawl_exception)}",
                'errorDetails': err_detail
            }

        if result_from_arun is None:
            logger.error(
                f"Crawl for {url} produced no result and no direct exception."
            )
            return {
                'success': False, 'url': url,
                'error': "Crawl failed: No result from arun."
            }

        actual_crawl_result: Optional[Any] = None  # Use Any for CrawlResult
        if CrawlResult and isinstance(result_from_arun, CrawlResult):
            actual_crawl_result = result_from_arun
        elif (hasattr(result_from_arun, '__iter__') and
                hasattr(result_from_arun, '__len__')):
            # Handle container-like objects (list, tuple, etc.)
            if len(result_from_arun) > 0:
                actual_crawl_result = result_from_arun[0]
            else:
                logger.error(f"Result container empty for {url}.")
                return {
                    'success': False, 'url': url,
                    'error': "Crawler returned an empty result container."
                }
        else:
            logger.error(
                f"Unexpected result type from crawler for {url}: "
                f"{type(result_from_arun)}"
            )
            return {
                'success': False, 'url': url,
                'error': f"Unexpected result type: {type(result_from_arun)}"
            }

        if actual_crawl_result is None:
            logger.error(f"actual_crawl_result is None for {url}.")
            return {
                'success': False, 'url': url,
                'error': "Failed to obtain a valid CrawlResult."
            }

        response: Dict[str, Any] = {
            'success': True, 'url': url,
            'markdown': None, 'html': None, 'title': None,
            'extractedContent': None,
            'screenshot': None,
            'pdf': None,
            'metadata': {
                'timestamp': datetime.now().isoformat(),
                'crawlDuration': None
            },
        }
        if hasattr(actual_crawl_result, 'markdown') and \
           actual_crawl_result.markdown is not None:
            response['markdown'] = str(actual_crawl_result.markdown)
        if hasattr(actual_crawl_result, 'html'):
            response['html'] = actual_crawl_result.html
        if hasattr(actual_crawl_result, 'metadata') and \
           actual_crawl_result.metadata:
            response['title'] = actual_crawl_result.metadata.get('title')
        if hasattr(actual_crawl_result, 'extracted_content') and \
           actual_crawl_result.extracted_content:
            response['extractedContent'] = \
                actual_crawl_result.extracted_content
        elif hasattr(actual_crawl_result, 'html') and actual_crawl_result.html:
            # If extraction failed or yielded no content, but we have raw HTML,
            # include it so the Node.js side can inspect or log it.
            # Use info level for test URLs and JSON endpoints to reduce noise
            if any(test_pattern in url.lower() for test_pattern in ['httpbin.org', 'example.com', '/api/', '.json']):
                # Content extraction empty for test/API URL (expected)
                pass
            else:
                logger.warning(
                    f"Extracted content was empty for {url}. "
                    f"Falling back to providing raw HTML."
                )
            response['html_fallback'] = actual_crawl_result.html
            response['extractedContent'] = None  # Ensure it's explicitly null

        # Add screenshot if available
        if hasattr(actual_crawl_result, 'screenshot') and \
           actual_crawl_result.screenshot:
            response['screenshot'] = actual_crawl_result.screenshot
            # Screenshot captured

        # Add PDF if available
        if hasattr(actual_crawl_result, 'pdf') and \
           actual_crawl_result.pdf:
            # Convert PDF bytes to base64 string for JSON serialization
            import base64
            response['pdf'] = base64.b64encode(
                actual_crawl_result.pdf
            ).decode('utf-8')
            # PDF captured

        if (hasattr(actual_crawl_result, 'dispatch_result') and
                actual_crawl_result.dispatch_result and
                hasattr(actual_crawl_result.dispatch_result, 'start_time') and
                hasattr(actual_crawl_result.dispatch_result, 'end_time')):
            start = actual_crawl_result.dispatch_result.start_time
            end = actual_crawl_result.dispatch_result.end_time
            if isinstance(start, datetime) and isinstance(end, datetime):
                response['metadata']['crawlDuration'] = \
                    (end - start).total_seconds()

        # Successfully processed crawl result
        return response

    except Exception as e:
        if sys.stdout != json_output_stream:
            sys.stdout = json_output_stream
        logger.error(
            f"General error in run_crawler for {url}: {str(e)}",
            exc_info=True
        )
        return {
            'success': False, 'url': url,
            'error': f"General run_crawler error: {str(e)}",
            'errorDetails': traceback.format_exc()
        }


def main():
    if not Crawl4AI_IMPORTED:
        print(json.dumps({
            "success": False,
            "error": (
                "Crawl4AI package could not be imported. Script cannot run. "
                "Please ensure Crawl4AI is installed in the Python "
                "environment accessible to this script."
            )
        }), flush=True)
        sys.exit(1)

    # Crawl4AI wrapper script started
    input_json_str = ""
    try:
        input_json_str = sys.stdin.read()
        if not input_json_str.strip():
            logger.error("No input received from stdin.")
            print(json.dumps({
                'success': False,
                'error': 'No input data received from stdin.'
            }), flush=True)
            sys.exit(1)

        data = json.loads(input_json_str)
        logger.debug("Successfully parsed JSON input from stdin.")
    except json.JSONDecodeError:
        err_msg = f"Invalid JSON input. Snippet: '{input_json_str[:200]}...'"
        logger.error(err_msg, exc_info=True)
        print(json.dumps({
            'success': False,
            'error': 'Invalid JSON input provided to script.'
        }), flush=True)
        sys.exit(1)
    except Exception as e_stdin:
        logger.error(
            f"Error reading or initially parsing stdin: {str(e_stdin)}",
            exc_info=True
        )
        print(json.dumps({
            'success': False,
            'error': f"Error processing stdin: {str(e_stdin)}",
            'errorDetails': traceback.format_exc()
        }), flush=True)
        sys.exit(1)

    url = data.get('url')
    options = data.get('options', {})

    if not url:
        logger.error("URL not provided in input JSON.")
        print(json.dumps({
            'success': False,
            'error': 'URL is required in input JSON.'
        }), flush=True)
        sys.exit(1)

    # Preparing to call run_crawler
    try:
        result = asyncio.run(run_crawler(url, options))
        # run_crawler completed, sending JSON to stdout
        print(json.dumps(result), flush=True)
    except Exception as e_main_async:
        logger.error(
            f"Critical error during asyncio.run for {url}: "
            f"{str(e_main_async)}",
            exc_info=True
        )
        print(json.dumps({
            'success': False,
            'error': f"Main execution/async error: {str(e_main_async)}",
            'errorDetails': traceback.format_exc()
        }),
              flush=True)
        sys.exit(1)
    # Crawl4AI wrapper script finished


if __name__ == "__main__":
    main()
