import React from "react";
import {
  Typo<PERSON>,
  Paper,
  Box,
  Chip,
  Divider,
  List,
  IconButton,
  Tooltip,
  CircularProgress,
  Button,
  Card,
  CardContent,
  Alert,
  Rating,
  Stack,
  ListItemIcon,
} from "@mui/material";
import {
  Assignment as AssignmentIcon,
  FindInPage as FindInPageIcon,
  OpenInNew as OpenInNewIcon,
  Star as StarIcon,
  Lightbulb as LightbulbIcon,
  Timeline as TimelineIcon,
  Warning as WarningIcon,
} from "@mui/icons-material";
import { useNavigate } from "react-router-dom";

const SimilarJobsPanel = ({
  jobId,
  loading,
  error,
  similarJobs,
  onRefreshClick,
}) => {
  const navigate = useNavigate();

  // Calculate similarity score percentage
  const getSimilarityPercentage = (score) => {
    return Math.round(score * 100);
  };

  // Get color based on similarity score
  const getSimilarityColor = (score) => {
    if (score >= 0.8) return "success.main";
    if (score >= 0.6) return "info.main";
    if (score >= 0.4) return "warning.main";
    return "text.secondary";
  };

  // Generate tooltip text with shared attributes
  const getSharedAttributesTooltip = (shared) => {
    if (!shared || shared.length === 0) return "No shared attributes";
    return `Shared attributes: ${shared.join(", ")}`;
  };

  // Handle navigation to similar job
  const handleViewJob = (id) => {
    navigate(`/jobs/${id}`);
  };

  return (
    <Paper elevation={0} sx={{ p: 2, height: "100%" }}>
      <Box
        display="flex"
        justifyContent="space-between"
        alignItems="center"
        mb={2}
      >
        <Typography variant="h6" fontWeight="bold">
          Similar Jobs
        </Typography>
        <Button
          startIcon={<FindInPageIcon />}
          size="small"
          onClick={onRefreshClick}
          disabled={loading}
        >
          Refresh
        </Button>
      </Box>

      <Divider sx={{ mb: 2 }} />

      {loading && (
        <Box display="flex" justifyContent="center" p={3}>
          <CircularProgress size={40} />
        </Box>
      )}

      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      {!loading && !error && similarJobs?.length === 0 && (
        <Box textAlign="center" p={3}>
          <FindInPageIcon color="disabled" sx={{ fontSize: 60, mb: 2 }} />
          <Typography color="textSecondary">No similar jobs found</Typography>
          <Button
            variant="outlined"
            size="small"
            sx={{ mt: 2 }}
            onClick={onRefreshClick}
          >
            Find Similar Jobs
          </Button>
        </Box>
      )}

      {!loading && !error && similarJobs?.length > 0 && (
        <List sx={{ p: 0 }}>
          {similarJobs.map((job) => (
            <Card
              key={job._id}
              sx={{ mb: 2, border: "1px solid", borderColor: "divider" }}
            >
              <CardContent sx={{ p: 2, "&:last-child": { pb: 2 } }}>
                <Box
                  display="flex"
                  justifyContent="space-between"
                  alignItems="center"
                  mb={1}
                >
                  <Box display="flex" alignItems="center">
                    <ListItemIcon sx={{ minWidth: 40 }}>
                      <AssignmentIcon color="primary" />
                    </ListItemIcon>
                    <Typography fontWeight="bold">{job.title}</Typography>
                  </Box>
                  <Tooltip title="View Job">
                    <IconButton
                      size="small"
                      onClick={() => handleViewJob(job._id)}
                    >
                      <OpenInNewIcon fontSize="small" />
                    </IconButton>
                  </Tooltip>
                </Box>

                <Box pl={5} mb={1}>
                  <Typography variant="body2" color="textSecondary" noWrap>
                    {job.description?.substring(0, 60)}
                    {job.description?.length > 60 ? "..." : ""}
                  </Typography>
                </Box>

                <Box pl={5} display="flex" alignItems="center" flexWrap="wrap">
                  <Tooltip
                    title={getSharedAttributesTooltip(job.sharedAttributes)}
                  >
                    <Chip
                      size="small"
                      label={`${getSimilarityPercentage(
                        job.similarityScore
                      )}% match`}
                      sx={{
                        mr: 1,
                        mb: 1,
                        color: "white",
                        bgcolor: getSimilarityColor(job.similarityScore),
                      }}
                    />
                  </Tooltip>

                  {job.status && (
                    <Chip
                      size="small"
                      label={job.status}
                      sx={{ mr: 1, mb: 1 }}
                    />
                  )}

                  {job.priority && (
                    <Box
                      sx={{
                        display: "flex",
                        alignItems: "center",
                        mr: 1,
                        mb: 1,
                      }}
                    >
                      <Rating
                        name={`priority-${job._id}`}
                        value={job.priority}
                        max={3}
                        readOnly
                        size="small"
                        icon={<StarIcon fontSize="inherit" />}
                        emptyIcon={<StarIcon fontSize="inherit" />}
                      />
                    </Box>
                  )}

                  {job.riskAssessment?.level && (
                    <Tooltip title={`Risk: ${job.riskAssessment.level}`}>
                      <Chip
                        size="small"
                        icon={<WarningIcon fontSize="small" />}
                        label={job.riskAssessment.level}
                        color={
                          job.riskAssessment.level === "High"
                            ? "error"
                            : job.riskAssessment.level === "Medium"
                            ? "warning"
                            : "default"
                        }
                        sx={{ mr: 1, mb: 1 }}
                      />
                    </Tooltip>
                  )}
                </Box>

                {job.insights && (
                  <Box pl={5} mt={1}>
                    <Stack direction="row" spacing={1} flexWrap="wrap">
                      {job.insights.complexity && (
                        <Tooltip title="Job Complexity">
                          <Chip
                            size="small"
                            icon={<TimelineIcon fontSize="small" />}
                            label={`Complexity: ${job.insights.complexity}`}
                            variant="outlined"
                            sx={{ mb: 1 }}
                          />
                        </Tooltip>
                      )}
                      {job.insights.keyInsight && (
                        <Tooltip title={job.insights.keyInsight}>
                          <Chip
                            size="small"
                            icon={<LightbulbIcon fontSize="small" />}
                            label="Key Insight"
                            variant="outlined"
                            sx={{ mb: 1 }}
                          />
                        </Tooltip>
                      )}
                    </Stack>
                  </Box>
                )}
              </CardContent>
            </Card>
          ))}
        </List>
      )}
    </Paper>
  );
};

export default SimilarJobsPanel;
