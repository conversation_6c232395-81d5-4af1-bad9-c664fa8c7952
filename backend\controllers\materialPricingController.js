const asyncHandler = require("express-async-handler");
const ScraperService = require("../scrapers/ScraperService");
const logger = require("../utils/logger");

/**
 * @desc    Get price for a material by SKU
 * @route   GET /api/material-pricing/sku/:sku
 * @access  Private
 */
const getMaterialPriceBySku = asyncHandler(async (req, res) => {
  const { sku } = req.params;
  const { sourceId, skipCache } = req.query;

  if (!sku) {
    res.status(400);
    throw new Error("SKU is required");
  }

  logger.info(`Material price lookup requested for SKU: ${sku}`, {
    sku,
    sourceId: sourceId || "default",
    skipCache: !!skipCache,
    userId: req.user._id,
  });

  try {
    // Use our ScraperService to fetch the price
    const priceData = await ScraperService.fetchPriceForMaterial(
      sku,
      sourceId || "HOME_DEPOT",
      { skipCache: skipCache === "true" }
    );

    if (priceData && priceData.price !== undefined) {
      logger.info(`Price found for SKU ${sku}: ${priceData.price}`, {
        sku,
        price: priceData.price,
        source: priceData.source,
      });

      return res.json({
        success: true,
        data: {
          sku,
          price: priceData.price,
          currency: priceData.currency || "USD",
          source: priceData.source,
          lastUpdated: priceData.timestamp || new Date().toISOString(),
        },
      });
    } else {
      logger.warn(`No price found for SKU ${sku}`, { sku });
      return res.status(200).json({
        success: true,
        data: {
          sku,
          price: null,
          currency: "USD",
          source: null,
          lastUpdated: new Date().toISOString(),
          priceFound: false,
        },
      });
    }
  } catch (error) {
    logger.error(`Error fetching price for SKU ${sku}: ${error.message}`, {
      sku,
      error: error.message,
      stack: error.stack,
    });

    res.status(500);
    throw new Error(`Failed to fetch price: ${error.message}`);
  }
});

/**
 * @desc    Get price for a material by description
 * @route   GET /api/material-pricing/search/:query
 * @access  Private
 */
const searchMaterialPrice = asyncHandler(async (req, res) => {
  const { query } = req.params;
  const { sourceId, skipCache } = req.query;

  if (!query) {
    res.status(400);
    throw new Error("Search query is required");
  }

  logger.info(`Material price search requested for: ${query}`, {
    query,
    sourceId: sourceId || "default",
    skipCache: !!skipCache,
    userId: req.user._id,
  });

  try {
    // Use our ScraperService to fetch the price by description
    const priceData = await ScraperService.fetchPriceForMaterial(
      query,
      sourceId || "HOME_DEPOT",
      { skipCache: skipCache === "true", searchByDescription: true }
    );

    if (priceData && priceData.price !== undefined) {
      logger.info(`Price found for query "${query}": ${priceData.price}`, {
        query,
        price: priceData.price,
        source: priceData.source,
      });

      return res.json({
        success: true,
        data: {
          query,
          price: priceData.price,
          currency: priceData.currency || "USD",
          source: priceData.source,
          lastUpdated: priceData.timestamp || new Date().toISOString(),
          productName: priceData.productName || query,
          productUrl: priceData.productUrl || null,
          imageUrl: priceData.imageUrl || null,
        },
      });
    } else {
      logger.warn(`No price found for query "${query}"`, { query });
      return res.status(200).json({
        success: true,
        data: {
          query,
          price: null,
          currency: "USD",
          source: null,
          lastUpdated: new Date().toISOString(),
          productName: query,
          productUrl: null,
          imageUrl: null,
          priceFound: false,
        },
      });
    }
  } catch (error) {
    logger.error(
      `Error searching price for query "${query}": ${error.message}`,
      {
        query,
        error: error.message,
        stack: error.stack,
      }
    );

    res.status(500);
    throw new Error(`Failed to search price: ${error.message}`);
  }
});

module.exports = {
  getMaterialPriceBySku,
  searchMaterialPrice,
};
