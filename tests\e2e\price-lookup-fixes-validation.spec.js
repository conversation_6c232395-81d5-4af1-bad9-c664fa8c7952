/**
 * Price Lookup Fixes Validation Tests
 * Comprehensive tests to validate immediate price lookup integration and Python environment fixes
 */

const { test, expect } = require('@playwright/test');

test.describe('Price Lookup Fixes Validation', () => {
  let authToken;
  let page;
  
  const testUser = {
    email: '<EMAIL>',
    password: 'password123'
  };

  test.beforeAll(async ({ request }) => {
    // Login to get auth token
    const response = await request.post('http://localhost:5000/api/users/login', {
      data: testUser
    });
    
    expect(response.status()).toBe(200);
    const responseData = await response.json();
    authToken = responseData.token;
  });

  test.beforeEach(async ({ browser }) => {
    page = await browser.newPage();
    // Set auth token in localStorage
    await page.goto('http://localhost:3000');
    await page.evaluate((token) => {
      localStorage.setItem('token', token);
    }, authToken);
  });

  test.afterEach(async () => {
    await page.close();
  });

  test.describe('Immediate Price Lookup Integration', () => {
    test('should fetch prices immediately during AI quote generation', async () => {
      await page.goto('http://localhost:3000/create-quote');
      
      // Fill in required form data
      await page.fill('input[name="name"]', 'Test Quote for Price Lookup');
      
      // Create a test customer first by clicking the add customer button
      await page.click('button:has-text("New")');
      await page.waitForSelector('[data-testid="create-customer-modal"]');
      await page.fill('input[name="businessName"]', 'Test Customer Business');
      await page.fill('input[name="contactPerson.firstName"]', 'John');
      await page.fill('input[name="contactPerson.lastName"]', 'Doe');
      await page.fill('input[name="email"]', '<EMAIL>');
      await page.click('button:has-text("Create Customer")');
      
      // Wait for customer to be created and selected
      await page.waitForTimeout(2000);
      
      // Fill project overview for AI generation
      await page.fill('textarea[name="projectOverview"]', 'Commercial electrical installation with 200A main panel, outlet installation, and lighting fixtures');
      
      // Add some test materials for price lookup
      await page.fill('textarea[name="materialsIncluded"]', '200A electrical panel, GFCI outlets, LED light fixtures, electrical wire 12 AWG');
      
      // Start AI generation from overview
      await page.click('button:has-text("Generate from Overview")');
      
      // Should show AI generation progress
      await expect(page.locator('.MuiLinearProgress-root')).toBeVisible();
      
      // Should show immediate price lookup progress indicator
      await expect(page.locator('text=💰 Fetching Material Prices')).toBeVisible({ timeout: 30000 });
      
      // Verify the price lookup progress shows item counts
      const progressText = await page.locator('text=✅').first();
      await expect(progressText).toBeVisible({ timeout: 60000 });
      
      // Wait for AI generation to complete
      await expect(page.locator('text=🎉 Quote generated successfully')).toBeVisible({ timeout: 120000 });
      
      // Verify that items have been populated
      const itemCards = page.locator('[data-testid="quote-item"]');
      await expect(itemCards.first()).toBeVisible();
      
      // Check if immediate pricing indicators are shown
      const pricingIndicators = page.locator('text=✨ Immediate Pricing Found During AI Generation!');
      const indicatorCount = await pricingIndicators.count();
      
      // At least some items should have immediate pricing
      expect(indicatorCount).toBeGreaterThan(0);
    });

    test('should show loading indicators until backend is completely finished', async () => {
      await page.goto('http://localhost:3000/create-quote');
      
      // Fill in minimal required data
      await page.fill('input[name="name"]', 'Loading Test Quote');
      
      // Create customer quickly
      await page.click('button:has-text("New")');
      await page.waitForSelector('[data-testid="create-customer-modal"]');
      await page.fill('input[name="businessName"]', 'Loading Test Customer');
      await page.fill('input[name="contactPerson.firstName"]', 'Test');
      await page.fill('input[name="contactPerson.lastName"]', 'User');
      await page.fill('input[name="email"]', '<EMAIL>');
      await page.click('button:has-text("Create Customer")');
      
      await page.waitForTimeout(1000);
      
      // Add materials for immediate price lookup
      await page.click('button:has-text("Add Item Manually")');
      await page.fill('input[name="items[0].name"]', '200A Electrical Panel');
      await page.fill('input[name="items[0].quantity"]', '1');
      await page.fill('input[name="items[0].price"]', '0');
      await page.fill('input[name="items[0].sku"]', 'TEST-PANEL-200A');
      
      // Mock the price lookup API to add delay
      await page.route('**/api/quotes/lookup-prices', async (route) => {
        // Add a delay to test loading indicators
        await new Promise(resolve => setTimeout(resolve, 3000));
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            success: true,
            items: [{
              name: '200A Electrical Panel',
              price: 299.99,
              priceInfo: {
                status: 'found',
                source: 'BACKEND_IMMEDIATE_LOOKUP',
                lastUpdated: new Date().toISOString()
              }
            }],
            summary: {
              total: 1,
              pricesFound: 1,
              pricesFailed: 0,
              averageResponseTime: 3000
            }
          })
        });
      });
      
      // Trigger price lookup
      await page.click('button[aria-label="refresh price"]');
      
      // Should show loading indicator immediately
      await expect(page.locator('.MuiCircularProgress-root')).toBeVisible();
      
      // Should show progress text
      await expect(page.locator('text=Searching for current market prices')).toBeVisible();
      
      // Loading should persist until backend responds
      await page.waitForTimeout(1000);
      await expect(page.locator('.MuiCircularProgress-root')).toBeVisible();
      
      // After backend responds, loading should disappear
      await expect(page.locator('.MuiCircularProgress-root')).not.toBeVisible({ timeout: 5000 });
    });

    test('should use usePriceLookup hook instead of custom fetchPricesForItems', async ({ request }) => {
      // Test the backend endpoint directly to ensure it works
      const testItems = [
        {
          name: 'GFCI Outlet',
          sku: 'TEST-GFCI-001',
          quantity: 5,
          description: 'Ground fault circuit interrupter outlet'
        },
        {
          name: 'LED Light Fixture',
          sku: 'TEST-LED-001', 
          quantity: 3,
          description: '4ft LED linear light fixture'
        }
      ];

      const response = await request.post('http://localhost:5000/api/quotes/lookup-prices', {
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json'
        },
        data: { items: testItems }
      });

      expect(response.status()).toBe(200);
      const responseData = await response.json();
      
      // Verify response structure
      expect(responseData.success).toBe(true);
      expect(responseData.items).toBeDefined();
      expect(responseData.summary).toBeDefined();
      expect(responseData.summary.total).toBe(2);
      
      // Verify items have proper structure
      responseData.items.forEach(item => {
        expect(item).toHaveProperty('name');
        expect(item).toHaveProperty('priceInfo');
        expect(item.priceInfo).toHaveProperty('status');
        expect(item.priceInfo).toHaveProperty('lastUpdated');
      });
    });
  });

  test.describe('Python Environment Detection Fixes', () => {
    test('should handle Python executable detection gracefully', async ({ request }) => {
      // Test the Crawl4AI health check endpoint
      const response = await request.get('http://localhost:5000/api/health/crawl4ai', {
        headers: {
          'Authorization': `Bearer ${authToken}`
        }
      });

      // Should not fail with ENOENT errors
      expect(response.status()).toBe(200);
      const healthData = await response.json();
      
      // Verify health check structure
      expect(healthData).toHaveProperty('overall');
      expect(healthData).toHaveProperty('pythonEnvironment');
      expect(healthData).toHaveProperty('timestamp');
      
      // Python environment should be properly detected
      if (healthData.pythonEnvironment) {
        expect(healthData.pythonEnvironment).toHaveProperty('available');
        expect(healthData.pythonEnvironment).toHaveProperty('path');
        
        // If Python is available, should have version info
        if (healthData.pythonEnvironment.available) {
          expect(healthData.pythonEnvironment).toHaveProperty('version');
          expect(healthData.pythonEnvironment.version).toMatch(/Python \d+\.\d+/);
        }
      }
    });

    test('should provide helpful installation guidance when Python is missing', async ({ request }) => {
      // Test the diagnostic endpoint
      const response = await request.get('http://localhost:5000/api/diagnostics/crawl4ai', {
        headers: {
          'Authorization': `Bearer ${authToken}`
        }
      });

      expect(response.status()).toBe(200);
      const diagnostics = await response.json();
      
      // Should provide environment information
      expect(diagnostics).toHaveProperty('service');
      expect(diagnostics).toHaveProperty('environment');
      expect(diagnostics.environment).toHaveProperty('platform');
      expect(diagnostics.environment).toHaveProperty('nodeVersion');
    });
  });

  test.describe('Crawl4AI Integration Optimizations', () => {
    test('should have reduced logging noise', async ({ request }) => {
      // Test a simple crawl operation
      const testUrl = 'https://httpbin.org/json';
      
      const response = await request.post('http://localhost:5000/api/scraping/test-crawl', {
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json'
        },
        data: {
          url: testUrl,
          options: { timeout: 30000 }
        }
      });

      // Should complete without excessive logging
      expect(response.status()).toBe(200);
      const result = await response.json();
      
      if (result.success) {
        expect(result).toHaveProperty('markdown');
        expect(result.metadata).toHaveProperty('processDuration');
      }
    });

    test('should handle version validation properly', async ({ request }) => {
      // Test that Python version validation works
      const response = await request.get('http://localhost:5000/api/health/python-version', {
        headers: {
          'Authorization': `Bearer ${authToken}`
        }
      });

      expect(response.status()).toBe(200);
      const versionData = await response.json();
      
      if (versionData.detected) {
        expect(versionData).toHaveProperty('version');
        expect(versionData).toHaveProperty('compatible');
        
        // If compatible, should be Python 3.8+
        if (versionData.compatible) {
          const versionMatch = versionData.version.match(/(\d+)\.(\d+)/);
          if (versionMatch) {
            const major = parseInt(versionMatch[1]);
            const minor = parseInt(versionMatch[2]);
            expect(major).toBeGreaterThanOrEqual(3);
            if (major === 3) {
              expect(minor).toBeGreaterThanOrEqual(8);
            }
          }
        }
      }
    });
  });

  test.describe('Form State Preservation', () => {
    test('should preserve form state during AI generation', async () => {
      await page.goto('http://localhost:3000/create-quote');
      
      // Fill form with specific data
      const quoteName = 'State Preservation Test Quote';
      await page.fill('input[name="name"]', quoteName);
      
      // Create customer
      await page.click('button:has-text("New")');
      await page.waitForSelector('[data-testid="create-customer-modal"]');
      await page.fill('input[name="businessName"]', 'State Test Customer');
      await page.fill('input[name="contactPerson.firstName"]', 'State');
      await page.fill('input[name="contactPerson.lastName"]', 'Test');
      await page.fill('input[name="email"]', '<EMAIL>');
      await page.click('button:has-text("Create Customer")');
      
      await page.waitForTimeout(1000);
      
      // Fill additional fields
      const projectOverview = 'This is a test project overview that should be preserved during AI generation';
      await page.fill('textarea[name="projectOverview"]', projectOverview);
      
      const materialsIncluded = 'Test materials list that should remain intact';
      await page.fill('textarea[name="materialsIncluded"]', materialsIncluded);
      
      // Start AI generation
      await page.click('button:has-text("Generate from Overview")');
      
      // During AI generation, form fields should retain their values
      await page.waitForTimeout(2000);
      
      // Verify form state is preserved
      const nameValue = await page.inputValue('input[name="name"]');
      expect(nameValue).toBe(quoteName);
      
      const overviewValue = await page.inputValue('textarea[name="projectOverview"]');
      expect(overviewValue).toBe(projectOverview);
      
      const materialsValue = await page.inputValue('textarea[name="materialsIncluded"]');
      expect(materialsValue).toBe(materialsIncluded);
      
      // Customer should remain selected
      const customerField = page.locator('input[name="customer"]');
      await expect(customerField).not.toBeEmpty();
    });

    test('should update draft quote with current form values', async () => {
      await page.goto('http://localhost:3000/create-quote');
      
      // Fill form and verify draft is updated
      await page.fill('input[name="name"]', 'Draft Update Test');
      
      // Check that changes trigger draft updates (this would be verified through API calls in a real test)
      // For now, we verify the form accepts the input correctly
      const nameValue = await page.inputValue('input[name="name"]');
      expect(nameValue).toBe('Draft Update Test');
    });
  });

  test.describe('Error Handling Improvements', () => {
    test('should show user-friendly error messages for common issues', async () => {
      await page.goto('http://localhost:3000/create-quote');
      
      // Mock a permission error
      await page.route('**/api/ai/**', async (route) => {
        await route.fulfill({
          status: 403,
          contentType: 'application/json',
          body: JSON.stringify({ 
            error: 'Access denied. Please ensure you have permission to use AI generation.' 
          })
        });
      });
      
      // Fill minimal form data
      await page.fill('input[name="name"]', 'Error Test Quote');
      await page.fill('textarea[name="projectOverview"]', 'Test project for error handling');
      
      // Try to generate quote
      await page.click('button:has-text("Generate from Overview")');
      
      // Should show user-friendly error message
      await expect(page.locator('.MuiAlert-message')).toContainText('Access denied');
    });

    test('should handle network timeouts gracefully', async () => {
      await page.goto('http://localhost:3000/create-quote');
      
      // Mock a timeout
      await page.route('**/api/ai/**', async (route) => {
        // Never respond to simulate timeout
        await new Promise(() => {}); // Infinite promise
      });
      
      // Fill form data
      await page.fill('input[name="name"]', 'Timeout Test Quote');
      await page.fill('textarea[name="projectOverview"]', 'Test project for timeout handling');
      
      // Start generation
      await page.click('button:has-text("Generate from Overview")');
      
      // Should show timeout handling after reasonable time
      await expect(page.locator('text=Network connection issue')).toBeVisible({ timeout: 10000 });
    });
  });

  test.describe('Integration with Existing Components', () => {
    test('should work with MaterialPriceStatusDisplay component', async () => {
      // This would require a saved quote to test the MaterialPriceStatusDisplay
      // For now, we verify the component doesn't break the page
      await page.goto('http://localhost:3000/create-quote');
      
      // Page should load without errors
      await expect(page.locator('h5:has-text("Create Quote")')).toBeVisible();
      
      // Form should be functional
      await page.fill('input[name="name"]', 'Component Integration Test');
      const nameValue = await page.inputValue('input[name="name"]');
      expect(nameValue).toBe('Component Integration Test');
    });

    test('should integrate with PriceLookupProgressIndicator', async () => {
      await page.goto('http://localhost:3000/create-quote');
      
      // Add an item to trigger price lookup
      await page.click('button:has-text("Add Item Manually")');
      await page.fill('input[name="items[0].name"]', 'Test Integration Item');
      await page.fill('input[name="items[0].sku"]', 'TEST-INTEG-001');
      
      // The PriceLookupProgressIndicator should be ready to show when triggered
      // (It's not visible until price lookup starts)
      const formElements = page.locator('form');
      await expect(formElements).toBeVisible();
    });
  });
});