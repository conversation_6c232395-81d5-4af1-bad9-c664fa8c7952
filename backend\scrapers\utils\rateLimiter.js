/**
 * rateLimiter.js
 * Provides rate limiting for web scraping operations
 */

/**
 * Simple token bucket rate limiter
 */
class TokenBucketRateLimiter {
  /**
   * Constructor
   * @param {Object} options - Rate limiter options
   */
  constructor(options = {}) {
    this.requestsPerMinute = options.requestsPerMinute || 10;
    this.cooldownPeriod = options.cooldownPeriod || 0;
    this.tokens = this.requestsPerMinute;
    this.lastRefill = Date.now();
    this.refillRate = this.requestsPerMinute / 60; // tokens per second
    this.waitingQueue = [];
    this.isRefilling = false;
    this.cooldownActive = false;
    this.tokenAcquired = false; // Track if a token has been acquired
  }

  /**
   * Acquire a token for a request
   * @returns {Promise<void>}
   */
  async acquire() {
    // If cooldown is active, wait for it to complete
    if (this.cooldownActive) {
      await new Promise((resolve) => {
        this.waitingQueue.push(resolve);
      });
      return this.acquire(); // Try again after cooldown
    }

    // Refill tokens based on elapsed time
    this._refillTokens();

    // If tokens are available, consume one
    if (this.tokens >= 1) {
      this.tokens -= 1;
      return;
    }

    // Otherwise, wait for next token to become available
    const timeUntilNextToken = (1 / this.refillRate) * 1000;

    await new Promise((resolve) => setTimeout(resolve, timeUntilNextToken));
    return this.acquire(); // Try again after waiting
  }

  /**
   * Refill tokens based on elapsed time
   * @private
   */
  _refillTokens() {
    const now = Date.now();
    const elapsedSeconds = (now - this.lastRefill) / 1000;

    // Calculate tokens to add
    const tokensToAdd = elapsedSeconds * this.refillRate;

    if (tokensToAdd > 0) {
      this.tokens = Math.min(this.requestsPerMinute, this.tokens + tokensToAdd);
      this.lastRefill = now;
    }
  }

  /**
   * Handle rate limit detection
   * @returns {Promise<void>}
   */
  async handleRateLimitDetected() {
    if (this.cooldownActive) {
      return; // Already in cooldown
    }

    console.warn(
      `Rate limit detected! Cooling down for ${this.cooldownPeriod}ms`
    );

    // Set cooldown status
    this.cooldownActive = true;
    this.tokens = 0;

    // Wait for cooldown period
    await new Promise((resolve) => setTimeout(resolve, this.cooldownPeriod));

    // Reset after cooldown
    this.cooldownActive = false;
    this.tokens = this.requestsPerMinute;
    this.lastRefill = Date.now();

    // Release waiting queue
    const waiters = [...this.waitingQueue];
    this.waitingQueue = [];
    waiters.forEach((resolve) => resolve());
  }

  /**
   * Get current token count
   * @returns {number} - Available tokens
   */
  getAvailableTokens() {
    this._refillTokens();
    return this.tokens;
  }

  /**
   * Release a previously acquired token
   * This is a no-op in this implementation as tokens are replenished automatically based on time
   * But we provide this method for compatibility with common rate limiter interfaces
   * @returns {void}
   */
  release() {
    // No specific action needed in this implementation
    // The token bucket automatically refills over time
    this.tokenAcquired = false;
  }
}

/**
 * Create a new rate limiter
 * @param {Object} options - Rate limiter options
 * @returns {TokenBucketRateLimiter} - Rate limiter instance
 */
function createLimiter(options = {}) {
  return new TokenBucketRateLimiter(options);
}

/**
 * Default rate limiter options by source type
 * @type {Object}
 */
const defaultRateLimits = {
  HOME_DEPOT: {
    requestsPerMinute: 10,
    cooldownPeriod: 60000, // 1 minute
  },
  PLATT: {
    // Renamed from GRAINGER
    requestsPerMinute: 10,
    cooldownPeriod: 120000, // 2 minutes
  },
  GRAYBAR: {
    requestsPerMinute: 5,
    cooldownPeriod: 300000, // 5 minutes
  },
  CUSTOM: {
    requestsPerMinute: 20,
    cooldownPeriod: 30000, // 30 seconds
  },
};

/**
 * Get default rate limit options for a source type
 * @param {string} sourceType - Material source type
 * @returns {Object} - Rate limit options
 */
function getDefaultRateLimit(sourceType) {
  return defaultRateLimits[sourceType] || defaultRateLimits.CUSTOM;
}

module.exports = {
  createLimiter,
  getDefaultRateLimit,
};
