# MongoDB Connection Fixes

## Problem Analysis

The application was experiencing repeated MongoDB connection failures with the following symptoms:

1. **ECONNREFUSED 127.0.0.1:27017** errors in logs
2. **ReplicaSetNoPrimary** errors with empty servers list
3. PriceLookupPoller service flooding logs with connection errors every 10 seconds
4. Application unable to connect to MongoDB replica set

## Root Causes Identified

### 1. Hostname Mismatch
- **Issue**: Application configured to connect to `localhost:27017`
- **Reality**: MongoDB runs in Docker container named `workiz-mongo-rs`
- **Fix**: Updated `.env` file to use `workiz-mongo-rs:27017`

### 2. Missing Replica Set Initialization
- **Issue**: MongoDB configured for replica set "rs0" but not initialized
- **Reality**: Replica sets require manual initialization with `rs.initiate()`
- **Fix**: Created automatic replica set initialization scripts

### 3. Lack of Connection Recovery
- **Issue**: PriceLookupPoller continued polling despite connection failures
- **Reality**: No connection health checking or recovery mechanisms
- **Fix**: Added connection health checks and exponential backoff retry logic

## Files Modified

### 1. Configuration Files
- **`/mnt/c/Projects/workiz/backend/.env`**: Updated `MONGODB_URI` from `localhost` to `workiz-mongo-rs`
- **`/mnt/c/Projects/workiz/backend/.env.example`**: Updated example configuration

### 2. Scripts Created
- **`/mnt/c/Projects/workiz/backend/scripts/initReplicaSet.js`**: Programmatic replica set initialization using Mongoose
- **`/mnt/c/Projects/workiz/backend/scripts/startup.js`**: Comprehensive startup script that:
  - Waits for MongoDB to be ready
  - Initializes replica set if needed
  - Starts main application

### 3. Service Enhanced
- **`/mnt/c/Projects/workiz/backend/services/priceLookupPoller.js`**: Added:
  - Connection health checking
  - Exponential backoff retry logic
  - Connection failure detection and recovery
  - Graceful degradation during connection issues

### 4. Package.json Updated
- **`/mnt/c/Projects/workiz/backend/package.json`**: Added new scripts:
  - `startup`: Runs the comprehensive startup sequence
  - `init:replica-set`: Manual replica set initialization
  - `check:replica-set`: Check replica set status

## Key Features Implemented

### Connection Recovery Logic
- **Health Checks**: Regular ping operations to verify MongoDB connection
- **Exponential Backoff**: Retry delays increase with each failure (5s, 10s, 20s, etc.)
- **Jitter**: Random delay variation to prevent thundering herd problems
- **Max Retries**: Automatic pause after 5 consecutive failures
- **Reconnection**: Full connection close/reopen on failure

### Startup Sequence
1. Wait for MongoDB container to be responsive (60s timeout)
2. Check if replica set is already initialized
3. Initialize replica set if needed with single-member configuration
4. Wait for replica set to stabilize
5. Start main application

### Error Handling
- **Specific Error Detection**: Identifies `MongooseServerSelectionError`, `ECONNREFUSED`, and connection-related errors
- **Graceful Degradation**: Poller pauses during connection issues instead of flooding logs
- **Informative Logging**: Clear status messages and error context

## Usage

### Normal Startup
```bash
npm start
# or
npm run startup
```

### Development Mode
```bash
npm run dev
```

### Manual Operations
```bash
# Initialize replica set manually
npm run init:replica-set

# Check replica set status
npm run check:replica-set
```

## Testing the Fixes

1. **Start fresh**: `docker-compose down -v` to remove volumes
2. **Start application**: `npm start`
3. **Verify**: Check logs for successful replica set initialization
4. **Test connection**: Use `npm run check:replica-set`
5. **Simulate failure**: Stop MongoDB container and observe recovery behavior

## Expected Behavior After Fixes

- ✅ No more `ECONNREFUSED 127.0.0.1:27017` errors
- ✅ Replica set properly initialized and primary elected
- ✅ PriceLookupPoller handles connection failures gracefully
- ✅ Application starts only after MongoDB is ready
- ✅ Connection recovery with exponential backoff during temporary issues

## Monitoring

Check application logs for:
- "Replica set initialized successfully"
- "MongoDB connection unhealthy" warnings
- "Connection restored successfully" messages
- Poller activity during normal operation

Use `npm run check:replica-set` to verify replica set status at any time.