const express = require("express");
const router = express.Router();
const {
  createInvoiceFromQuote,
  ...invoiceController
} = require("../controllers/invoiceController"); // Import createInvoiceFromQuote
const { protect: auth } = require("../middleware/auth");
const { validate } = require("../middleware/validate");
const {
  validateInvoiceData,
  validatePaymentData,
} = require("../validators/invoiceValidator");
const { cacheMiddleware, clearCache } = require("../middleware/cache");
const { rateLimiter } = require("../middleware/rateLimiter");
const { checkPermissions } = require("../middleware/permissions");
const ApiError = require("../utils/ApiError");
const Invoice = require("../models/Invoice");

/**
 * Invoice routes
 * @swagger
 * tags:
 *   name: Invoices
 *   description: Invoice management endpoints
 */

/**
 * @swagger
 * /api/invoices:
 *   post:
 *     summary: Create a new invoice
 *     tags: [Invoices]
 */

// Create invoice from quote
router.post("/from-quote/:quoteId", auth, createInvoiceFromQuote); // Use 'auth' alias and ensure function is imported
router.post(
  "/",
  rateLimiter({ windowMs: 60000, max: 10 }), // 10 requests per minute
  auth,
  checkPermissions("invoices:create"),
  // Custom middleware to transform the request body before validation
  (req, res, next) => {
    // If jobId exists, transform it to job for validation
    if (req.body.jobId) {
      req.body = {
        ...req.body,
        job: req.body.jobId,
      };
    }
    next();
  },
  validate(validateInvoiceData),
  invoiceController.createInvoice
);

/**
 * @swagger
 * /api/invoices:
 *   get:
 *     summary: Get all invoices with filtering and pagination
 *     tags: [Invoices]
 */
/**
 * @swagger
 * /api/invoices/batch:
 *   post:
 *     summary: Create multiple invoices
 *     tags: [Invoices]
 */
router.post(
  "/batch",
  rateLimiter({ windowMs: 300000, max: 20 }), // 20 requests per 5 minutes
  auth,
  checkPermissions("invoices:create"),
  async (req, res, next) => {
    try {
      const invoices = await Promise.all(
        req.body.invoices.map((invoiceData) => {
          // Extract jobId and prepare data for validation, similar to createInvoice
          const { jobId, ...restData } = invoiceData;
          const dataToValidate = {
            ...restData,
            job: jobId,
          };

          return validateInvoiceData(dataToValidate).then(() =>
            invoiceController.createInvoiceInternal(invoiceData, req.user)
          );
        })
      );
      res.status(201).json(invoices);
    } catch (error) {
      next(error);
    }
  }
);

/**
 * @swagger
 * /api/invoices/analytics:
 *   get:
 *     summary: Get invoice analytics
 *     tags: [Invoices]
 */
router.get(
  "/analytics",
  rateLimiter({ windowMs: 60000, max: 20 }), // 20 requests per minute
  auth,
  checkPermissions("invoices:analytics"),
  cacheMiddleware(600), // Cache for 10 minutes
  invoiceController.getAnalytics
);

router.get(
  "/",
  rateLimiter({ windowMs: 60000, max: 100 }), // 100 requests per minute
  auth,
  checkPermissions("invoices:read"),
  cacheMiddleware(300), // Cache for 5 minutes
  invoiceController.getInvoices
);

/**
 * AI-Enhanced Routes
 */

/**
 * @swagger
 * /api/invoices/{id}/analyze:
 *   get:
 *     summary: Get AI analysis for invoice
 *     tags: [Invoices]
 */
router.get(
  "/:id/analyze",
  rateLimiter({ windowMs: 60000, max: 10 }), // 10 requests per minute
  auth,
  checkPermissions("invoices:ai"),
  cacheMiddleware(1800), // Cache for 30 minutes
  async (req, res, next) => {
    try {
      const invoice = await Invoice.findById(req.params.id)
        .populate("customer")
        .populate("job");

      if (!invoice) {
        throw new ApiError(404, "Invoice not found");
      }

      const analysis = await invoiceController.analyzeInvoice(invoice);
      res.json(analysis);
    } catch (error) {
      next(error);
    }
  }
);

/**
 * @swagger
 * /api/invoices/{id}/suggest-followup:
 *   get:
 *     summary: Get AI-powered follow-up suggestions
 *     tags: [Invoices]
 */
router.get(
  "/:id/suggest-followup",
  rateLimiter({ windowMs: 60000, max: 10 }), // 10 requests per minute
  auth,
  checkPermissions("invoices:ai"),
  cacheMiddleware(3600), // Cache for 1 hour
  async (req, res, next) => {
    try {
      const invoice = await Invoice.findById(req.params.id).populate(
        "customer"
      );

      if (!invoice) {
        throw new ApiError(404, "Invoice not found");
      }

      const followUp = await invoiceController.generateFollowUpStrategy(
        invoice
      );
      res.json(followUp);
    } catch (error) {
      next(error);
    }
  }
);

/**
 * @swagger
 * /api/invoices/{id}:
 *   get:
 *     summary: Get invoice by ID
 *     tags: [Invoices]
 */
router.get(
  "/:id",
  rateLimiter({ windowMs: 60000, max: 50 }), // 50 requests per minute
  auth,
  checkPermissions("invoices:read"),
  cacheMiddleware(300),
  invoiceController.getInvoice
);

/**
 * @swagger
 * /api/invoices/{id}:
 *   put:
 *     summary: Update invoice by ID
 *     tags: [Invoices]
 */
router.put(
  "/:id",
  rateLimiter({ windowMs: 60000, max: 30 }), // Allow more updates than creates
  auth,
  checkPermissions("invoices:update"),
  validate(validateInvoiceData), // Assuming update uses same validation as create
  invoiceController.updateInvoice
);

/**
 * @swagger
 * /api/invoices/{id}:
 *   delete:
 *     summary: Delete invoice by ID
 *     tags: [Invoices]
 */
router.delete(
  "/:id",
  rateLimiter({ windowMs: 60000, max: 10 }),
  auth,
  checkPermissions("invoices:delete"),
  invoiceController.deleteInvoice // Need to implement this controller function
);

/**
 * @swagger
 * /api/invoices/{id}/pdf:
 *   get:
 *     summary: Generate PDF for invoice by ID
 *     tags: [Invoices]
 */
router.get(
  "/:id/pdf",
  rateLimiter({ windowMs: 60000, max: 10 }), // Limit PDF generation
  auth,
  checkPermissions("invoices:read"), // Or a specific pdf permission
  invoiceController.generateInvoicePdf // Need to implement this controller function
);

/**
 * @swagger
 * /api/invoices/{id}/payments:
 *   post:
 *     summary: Record a payment for an invoice
 *     tags: [Invoices]
 */
router.post(
  "/:id/payments",
  rateLimiter({ windowMs: 60000, max: 20 }),
  auth,
  checkPermissions("invoices:update"), // Or specific payment permission
  validate(validatePaymentData), // Need payment validation
  invoiceController.recordPayment
);

/**
 * @swagger
 * /api/invoices/{id}/payments:
 *   get:
 *     summary: Get payments for an invoice
 *     tags: [Invoices]
 */
router.get(
  "/:id/payments",
  rateLimiter({ windowMs: 60000, max: 50 }),
  auth,
  checkPermissions("invoices:read"),
  invoiceController.getInvoicePayments // Need to implement this controller function
);

/**
 * @swagger
 * /api/invoices/{id}/send:
 *   post:
 *     summary: Send invoice to customer
 *     tags: [Invoices]
 */
router.post(
  "/:id/send",
  rateLimiter({ windowMs: 60000, max: 5 }), // 5 requests per minute
  auth,
  checkPermissions("invoices:send"),
  async (req, res, next) => {
    try {
      const result = await invoiceController.sendInvoice(req.params.id);
      res.json(result);
    } catch (error) {
      next(error);
    }
  }
);

/**
 * @swagger
 * /api/invoices/{id}/remind:
 *   post:
 *     summary: Send payment reminder
 *     tags: [Invoices]
 */
router.post(
  "/:id/remind",
  rateLimiter({ windowMs: 3600000, max: 3 }), // 3 requests per hour
  auth,
  checkPermissions("invoices:send"),
  async (req, res, next) => {
    try {
      const result = await invoiceController.sendReminder(req.params.id);
      res.json(result);
    } catch (error) {
      next(error);
    }
  }
);

module.exports = router;
