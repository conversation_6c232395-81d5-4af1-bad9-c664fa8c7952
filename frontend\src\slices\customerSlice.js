import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import axios from "axios";
import { uploadQuoteImages } from "./quoteSlice"; // Import action from quoteSlice
import logger from "../utils/logger";

const initialState = {
  customers: [],
  customer: null,
  loading: false,
  error: null,
  success: false,
  totalPages: 1,
  currentPage: 1,
  imageUploading: false,
  imageError: null,
  imageSuccess: false,
};

// Get all customers with pagination
export const getCustomers = createAsyncThunk(
  "customers/getCustomers",
  async (
    { page = 1, limit = 10, search = "", tags = "" },
    { getState, rejectWithValue }
  ) => {
    try {
      const { auth } = getState();

      const config = {
        headers: {
          Authorization: `Bearer ${auth.userInfo.token}`,
        },
        params: {
          page,
          pageSize: limit,
          keyword: search,
          tags,
          _cacheBust: Date.now(), // Add cache-busting parameter
        },
      };

      const { data } = await axios.get("/api/customers", config);
      return data;
    } catch (error) {
      return rejectWithValue(
        error.response && error.response.data.message
          ? error.response.data.message
          : error.message
      );
    }
  }
);

// Get customer by ID
export const getCustomerById = createAsyncThunk(
  "customers/getCustomerById",
  async (id, { getState, rejectWithValue }) => {
    try {
      const { auth } = getState();

      const config = {
        headers: {
          Authorization: `Bearer ${auth.userInfo.token}`,
        },
      };

      const { data } = await axios.get(`/api/customers/${id}`, config);
      return data;
    } catch (error) {
      return rejectWithValue(
        error.response && error.response.data.message
          ? error.response.data.message
          : error.message
      );
    }
  }
);

// Create new customer
export const createCustomer = createAsyncThunk(
  "customers/createCustomer",
  async (customerData, { getState, rejectWithValue }) => {
    try {
      const { auth } = getState();

      const config = {
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${auth.userInfo.token}`,
        },
      };

      const { data } = await axios.post("/api/customers", customerData, config);
      return data;
    } catch (error) {
      return rejectWithValue(
        error.response && error.response.data.message
          ? error.response.data.message
          : error.message
      );
    }
  }
);

// Update customer
export const updateCustomer = createAsyncThunk(
  "customers/updateCustomer",
  async ({ id, customerData }, { getState, rejectWithValue }) => {
    try {
      const { auth } = getState();

      const config = {
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${auth.userInfo.token}`,
        },
      };

      const { data } = await axios.put(
        `/api/customers/${id}`,
        customerData,
        config
      );
      return data;
    } catch (error) {
      return rejectWithValue(
        error.response && error.response.data.message
          ? error.response.data.message
          : error.message
      );
    }
  }
);

// Delete customer
export const deleteCustomer = createAsyncThunk(
  "customers/deleteCustomer",
  async (id, { getState, rejectWithValue }) => {
    try {
      const { auth } = getState();

      const config = {
        headers: {
          Authorization: `Bearer ${auth.userInfo.token}`,
        },
      };

      await axios.delete(`/api/customers/${id}`, config);
      return id;
    } catch (error) {
      return rejectWithValue(
        error.response && error.response.data.message
          ? error.response.data.message
          : error.message
      );
    }
  }
);

// Upload customer images
export const uploadCustomerImages = createAsyncThunk(
  "customers/uploadImages",
  async ({ id, formData }, { getState, rejectWithValue }) => {
    try {
      const { auth } = getState();

      const config = {
        headers: {
          "Content-Type": "multipart/form-data",
          Authorization: `Bearer ${auth.userInfo.token}`,
        },
      };

      const { data } = await axios.post(
        `/api/customers/${id}/images`,
        formData,
        config
      );
      return data;
    } catch (error) {
      logger.error("Error uploading customer images:", error);
      return rejectWithValue(
        error.response && error.response.data.message
          ? error.response.data.message
          : error.message
      );
    }
  }
);

// Get customer images
export const getCustomerImages = createAsyncThunk(
  "customers/getImages",
  async (id, { getState, rejectWithValue }) => {
    try {
      const { auth } = getState();

      const config = {
        headers: {
          Authorization: `Bearer ${auth.userInfo.token}`,
        },
      };

      const { data } = await axios.get(`/api/customers/${id}/images`, config);
      return data;
    } catch (error) {
      logger.error("Error fetching customer images:", error);
      return rejectWithValue(
        error.response && error.response.data.message
          ? error.response.data.message
          : error.message
      );
    }
  }
);

// Delete customer image
export const deleteCustomerImage = createAsyncThunk(
  "customers/deleteImage",
  async ({ customerId, imageId }, { getState, rejectWithValue }) => {
    try {
      const { auth } = getState();

      const config = {
        headers: {
          Authorization: `Bearer ${auth.userInfo.token}`,
        },
      };

      await axios.delete(
        `/api/customers/${customerId}/images/${imageId}`,
        config
      );
      return { customerId, imageId };
    } catch (error) {
      logger.error("Error deleting customer image:", error);
      return rejectWithValue(
        error.response && error.response.data.message
          ? error.response.data.message
          : error.message
      );
    }
  }
);

// Update customer image metadata
export const updateCustomerImage = createAsyncThunk(
  "customers/updateImage",
  async ({ customerId, imageId, imageData }, { getState, rejectWithValue }) => {
    try {
      const { auth } = getState();

      const config = {
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${auth.userInfo.token}`,
        },
      };

      const { data } = await axios.put(
        `/api/customers/${customerId}/images/${imageId}`,
        imageData,
        config
      );
      return data;
    } catch (error) {
      logger.error("Error updating customer image:", error);
      return rejectWithValue(
        error.response && error.response.data.message
          ? error.response.data.message
          : error.message
      );
    }
  }
);

const customerSlice = createSlice({
  name: "customers",
  initialState,
  reducers: {
    resetCustomerState: (state) => {
      state.loading = false;
      state.error = null;
      state.success = false;
    },
    clearCustomer: (state) => {
      state.customer = null;
    },
    resetImageState: (state) => {
      state.imageUploading = false;
      state.imageError = null;
      state.imageSuccess = false;
    },
    // New reducer to update a single image in the customer's image array
    updateSingleCustomerImage: (state, action) => {
      const updatedImage = action.payload;
      if (
        state.customer &&
        state.customer.customerImages &&
        updatedImage?._id
      ) {
        const imageIndex = state.customer.customerImages.findIndex(
          (img) => img._id === updatedImage._id
        );
        if (imageIndex !== -1) {
          state.customer.customerImages[imageIndex] = updatedImage;
          logger.info(
            `Redux: Updated single image ${updatedImage._id} in customer state.`
          );
        } else {
          logger.warn(
            `Redux: Could not find image index for ID ${updatedImage._id} to update.`
          );
          // Optionally, add the image if not found? Or rely on full refresh? For now, just log.
        }
      } else {
        logger.warn(
          "Redux: Cannot update single image - customer, customerImages, or updatedImage._id missing.",
          {
            customerExists: !!state.customer,
            imagesExist: !!state.customer?.customerImages,
            payloadId: updatedImage?._id,
          }
        );
      }
    },
  },
  extraReducers: (builder) => {
    builder
      // Upload customer images reducers
      .addCase(uploadCustomerImages.pending, (state) => {
        state.imageUploading = true;
        state.imageError = null;
        state.imageSuccess = false;
      })
      .addCase(uploadCustomerImages.fulfilled, (state, action) => {
        state.imageUploading = false;
        state.imageSuccess = true;
        // If customer is loaded, update its images
        if (state.customer) {
          if (!state.customer.customerImages) {
            state.customer.customerImages = [];
          }
          // Log the response for debugging
          console.log("Upload response:", action.payload);

          // Make sure we have images in the response
          if (
            action.payload &&
            action.payload.images &&
            Array.isArray(action.payload.images)
          ) {
            state.customer.customerImages = [
              ...state.customer.customerImages,
              ...action.payload.images,
            ];
          } else {
            console.error(
              "Invalid image upload response format:",
              action.payload
            );
          }
        }
      })
      .addCase(uploadCustomerImages.rejected, (state, action) => {
        state.imageUploading = false;
        state.imageError = action.payload || "Failed to upload images";
      })

      // Get customer images reducers
      .addCase(getCustomerImages.pending, (state) => {
        state.imageUploading = true;
        state.imageError = null;
      })
      .addCase(getCustomerImages.fulfilled, (state, action) => {
        state.imageUploading = false;
        // If customer is loaded, update its images
        if (state.customer) {
          state.customer.customerImages = action.payload;
        }
      })
      .addCase(getCustomerImages.rejected, (state, action) => {
        state.imageUploading = false;
        state.imageError = action.payload || "Failed to fetch images";
      })

      // Delete customer image reducers
      .addCase(deleteCustomerImage.pending, (state) => {
        state.imageUploading = true;
        state.imageError = null;
      })
      .addCase(deleteCustomerImage.fulfilled, (state, action) => {
        state.imageUploading = false;
        state.imageSuccess = true;
        // If customer is loaded, remove the deleted image
        if (state.customer && state.customer.customerImages) {
          state.customer.customerImages = state.customer.customerImages.filter(
            (img) => img._id !== action.payload.imageId
          );
        }
      })
      .addCase(deleteCustomerImage.rejected, (state, action) => {
        state.imageUploading = false;
        state.imageError = action.payload || "Failed to delete image";
      })

      // Update customer image reducers
      .addCase(updateCustomerImage.pending, (state) => {
        state.imageUploading = true;
        state.imageError = null;
      })
      .addCase(updateCustomerImage.fulfilled, (state, action) => {
        state.imageUploading = false;
        state.imageSuccess = true;
        // If customer is loaded, update the modified image
        if (state.customer && state.customer.customerImages) {
          const imageIndex = state.customer.customerImages.findIndex(
            (img) => img._id === action.payload.image._id
          );
          if (imageIndex !== -1) {
            state.customer.customerImages[imageIndex] = action.payload.image;
          }
        }
      })
      .addCase(updateCustomerImage.rejected, (state, action) => {
        state.imageUploading = false;
        state.imageError = action.payload || "Failed to update image";
      })
      // Get customers
      .addCase(getCustomers.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getCustomers.fulfilled, (state, action) => {
        state.loading = false;
        state.customers = action.payload.customers;
        state.totalPages = action.payload.totalPages;
        state.currentPage = action.payload.currentPage;
      })
      .addCase(getCustomers.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      // Get customer by ID
      .addCase(getCustomerById.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getCustomerById.fulfilled, (state, action) => {
        state.loading = false;
        state.customer = action.payload;
      })
      .addCase(getCustomerById.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      // Create customer
      .addCase(createCustomer.pending, (state) => {
        state.loading = true;
        state.error = null;
        state.success = false;
      })
      .addCase(createCustomer.fulfilled, (state, action) => {
        state.loading = false;
        state.success = true;
        state.customers.push(action.payload);
      })
      .addCase(createCustomer.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      // Update customer
      .addCase(updateCustomer.pending, (state) => {
        state.loading = true;
        state.error = null;
        state.success = false;
      })
      .addCase(updateCustomer.fulfilled, (state, action) => {
        state.loading = false;
        state.success = true;
        state.customer = action.payload;
        state.customers = state.customers.map((customer) =>
          customer._id === action.payload._id ? action.payload : customer
        );
      })
      .addCase(updateCustomer.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      // Delete customer
      .addCase(deleteCustomer.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(deleteCustomer.fulfilled, (state, action) => {
        state.loading = false;
        state.success = true;
        state.customers = state.customers.filter(
          (customer) => customer._id !== action.payload
        );
      })
      .addCase(deleteCustomer.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      // Handle image upload from quote context
      .addCase(uploadQuoteImages.fulfilled, (state, action) => {
        // Check if the currently loaded customer matches the customer associated with the quote
        // We need the customerId in the payload for this check.
        // Assuming the payload is { quoteId, images: [...] } and we can get customerId from state.customer._id
        // A better approach might be to include customerId in the uploadQuoteImages payload.
        // For now, let's assume state.customer is the correct one if loaded.
        if (
          state.customer &&
          action.payload.images &&
          action.payload.images.length > 0
        ) {
          // Check if the customer ID matches (requires customerId in payload or fetching quote)
          // This logic might need refinement based on how customerId is passed.
          // Let's assume for now if state.customer exists, we update it.
          // A more robust check would involve comparing state.customer._id with the customerId
          // associated with action.payload.quoteId, which isn't directly available here.
          // Safest bet: Only update if the customer detail page is currently loaded.
          if (state.customer) {
            // Check if a customer is loaded in this slice's state
            if (!state.customer.customerImages) {
              state.customer.customerImages = [];
            }
            // Add only images not already present (by _id)
            const existingImageIds = new Set(
              state.customer.customerImages.map((img) => img._id)
            );
            const newImages = action.payload.images.filter(
              (img) => !existingImageIds.has(img._id)
            );
            state.customer.customerImages.push(...newImages);
            state.imageSuccess = true; // Indicate success
          }
        }
      });
    // Note: No pending/rejected cases needed here as those are handled by quoteSlice primarily.
  },
});

export const {
  resetCustomerState,
  clearCustomer,
  resetImageState,
  updateSingleCustomerImage,
} = customerSlice.actions; // Added resetImageState and updateSingleCustomerImage

export default customerSlice.reducer;
