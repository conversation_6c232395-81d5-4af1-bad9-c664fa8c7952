const express = require("express");
const router = express.Router();
const aiController = require("../controllers/aiController"); // Import the controller object
const { protect } = require("../middleware/auth");
const logger = require("../utils/logger");
const ApiError = require("../utils/ApiError"); // Import ApiError for consistency

// Suggest materials based on job description
router.post("/suggest-materials", protect, aiController.suggestMaterialsForJob);
router.post(
  "/generate-material-description",
  protect,
  aiController.generateMaterialDescription
);
const multer = require("multer");
const uploadMiddleware = require("../middleware/uploadMiddleware"); // Import upload middleware

// Create a special more permissive handler for quote content generation that accepts any field
// This resolves the "Unexpected field" error by allowing any field name for files
const uploadAny = multer({
  storage: uploadMiddleware.storage,
  fileFilter: uploadMiddleware.fileFilter,
  limits: uploadMiddleware.limits,
}).any();

router.post(
  "/generate-quote-content",
  protect,
  uploadAny,
  aiController.generateQuoteContent
); // Using any fields for file uploads

// NEW: Draft quote clarification endpoint - no quoteId required
router.post("/clarify-draft-quote", protect, aiController.clarifyDraftQuote);

/**
 * @route   POST /api/ai/analyze-job
 * @desc    Analyze job data for AI suggestions
 * @access  Private (Requires authentication)
 */
// Refactor: Delegate to controller, add protect middleware
router.post("/analyze-job", protect, (req, res, next) => {
  aiController.analyzeJob(req, res, next);
});

/**
 * @route   POST /api/ai/enhance-description
 * @desc    Use AI to enhance a job description
 * @access  Private (Requires authentication)
 */
// Refactor: Delegate to controller, add protect middleware
router.post("/enhance-description", protect, (req, res, next) => {
  aiController.enhanceDescription(req, res, next);
});

/**
 * @route   POST /api/ai/analyze-complete-job
 * @desc    Use AI to analyze job description and suggest title and duration
 * @access  Private (Requires authentication)
 */
// Refactor: Delegate to controller, add protect middleware
router.post("/analyze-complete-job", protect, (req, res, next) => {
  aiController.analyzeCompleteJob(req, res, next);
});

/**
 * @route   POST /api/ai/payment-risk
 * @desc    Analyze payment risk for customer
 * @access  Private
 */
// Refactor: Delegate to controller
router.post("/payment-risk", protect, (req, res, next) => {
  aiController.analyzePaymentRisk(req, res, next);
});

/**
 * @route   POST /api/ai/followup-strategy
 * @desc    Generate follow-up strategy for invoice
 * @access  Private
 */
// Refactor: Delegate to controller
router.post("/followup-strategy", protect, (req, res, next) => {
  aiController.generateFollowupStrategy(req, res, next);
});

/**
 * Customer AI routes (Already using controller and protect middleware)
 */
router.get(
  "/customer/:id/description",
  protect,
  aiController.generateBusinessDescription
);
router.get(
  "/customer/:id/insights",
  protect,
  aiController.generateCustomerInsights
);
router.get("/customer/:id/services", protect, aiController.suggestServices);
router.post(
  "/customer/:id/followup",
  protect,
  aiController.getFollowUpSuggestions
);
router.post("/customers/filter", protect, aiController.filterCustomersByAI);
router.post("/analyze-image", protect, aiController.analyzeCustomerImage);

/**
 * Material AI Scraping Routes
 * These routes handle AI-powered material scraping and selection
 */
// Initiate AI material scraping for a quote item
router.post(
  "/quote/:quoteId/item/:itemId/scrape-materials",
  protect,
  aiController.initiateAiMaterialScraping
);
// Get material options for a quote item
router.get(
  "/quote/:quoteId/item/:itemId/material-options",
  protect,
  aiController.getMaterialOptions
);
// Select a material option for a quote item
router.post(
  "/quote/:quoteId/item/:itemId/select-option",
  protect,
  aiController.selectMaterialOption
);

/**
 * @route   GET /api/ai/debug/price-scraping
 * @desc    Debug endpoint for price scraping system health
 * @access  Private
 */
router.get("/debug/price-scraping", protect, async (req, res) => {
  try {
    const results = {
      timestamp: new Date().toISOString(),
      scraperService: null,
      crawl4ai: null,
      materialSources: null,
      diagnostics: null,
      installationGuide: null,
    };

    // Test ScraperService
    const scraperService = require("../scrapers/ScraperService");
    results.scraperService = {
      initialized: scraperService.initialized,
      scraperCount: scraperService.scrapers.size,
      sourceTypes: Array.from(scraperService.sourceTypeMap.keys()),
    };

    // Test Crawl4AI
    const crawl4aiService = require("../scrapers/crawl4ai/crawl4ai-service");
    results.crawl4ai = await crawl4aiService.healthCheck();
    results.diagnostics = await crawl4aiService.getDiagnostics();
    results.installationGuide = await crawl4aiService.getInstallationGuide();

    // Test Material Sources
    results.materialSources = await scraperService.validateMaterialSources();

    res.json(results);
  } catch (error) {
    res.status(500).json({
      error: "Failed to run price scraping diagnostics",
      message: error.message,
      timestamp: new Date().toISOString(),
    });
  }
});

/**
 * @route   GET /api/ai/health
 * @desc    Get AI service health status
 * @access  Public
 */
router.get("/health", (req, res, next) => {
  try {
    const aiHealthService = require("../services/aiHealthService");
    const healthMetrics = aiHealthService.getAllHealthMetrics();

    res.json({
      status: healthMetrics.status,
      services: healthMetrics.services,
      aggregatedMetrics: healthMetrics.aggregatedMetrics,
      errors: healthMetrics.errors,
      timestamp: healthMetrics.timestamp,
    });
  } catch (error) {
    logger.error("Error getting AI health metrics:", error);
    next(error);
  }
});

/**
 * @route   GET /api/ai/health/:service
 * @desc    Get health status for a specific AI service
 * @access  Public
 */
router.get("/health/:service", (req, res, next) => {
  try {
    const aiHealthService = require("../services/aiHealthService");
    const { service } = req.params;
    const serviceHealth = aiHealthService.getServiceHealth(service);

    res.json({
      service,
      ...serviceHealth,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    logger.error(
      `Error getting health for service ${req.params.service}:`,
      error
    );
    next(error);
  }
});

/**
 * @route   GET /api/ai/stream/:sessionId
 * @desc    SSE endpoint for streaming AI quote generation (unified with /api/streaming/quote-generation/:sessionId)
 * @access  Private
 */
router.get("/stream/:sessionId", protect, async (req, res) => {
  try {
    const { sessionId } = req.params;

    logger.info(
      `[AI Routes] SSE connection request for session ${sessionId} via /api/ai/stream/`,
      {
        userId: req.user?.id,
        ip: req.ip,
        userAgent: req.headers["user-agent"]?.substring(0, 50),
      }
    );

    // Import streaming service
    const streamingAiService = require("../services/streamingAiService");

    // Log current session state for debugging
    const sessionStats = streamingAiService.getSessionStats();
    logger.info(
      `[AI Routes] Looking for session ${sessionId} among ${sessionStats.activeSessions} active sessions`
    );

    // Validate session existence with detailed debugging
    const session = streamingAiService.getSession(sessionId);
    if (!session) {
      // Log additional debugging info for 404 errors
      const currentTime = Date.now();
      logger.error(
        `[AI Routes] Session ${sessionId} not found for SSE connection`,
        {
          sessionId,
          userId: req.user?.id,
          activeSessions: sessionStats.activeSessions || 0,
          totalSessions: sessionStats.totalSessions || 0,
          requestTime: new Date(currentTime).toISOString(),
          serverUptime: process.uptime(),
          memoryUsage: process.memoryUsage(),
          timestamp: new Date().toISOString(),
        }
      );

      res.writeHead(404, {
        "Content-Type": "application/json",
        "X-Session-Status": "not_found",
        "X-Active-Sessions": sessionStats.activeSessions || 0,
        "X-Debug-Time": new Date().toISOString(),
      });
      res.end(
        JSON.stringify({
          error: "Session not found or expired",
          code: "session_not_found",
          sessionId: sessionId,
          suggestion: "Please start a new quote generation session.",
          activeSessions: sessionStats.activeSessions,
          debug: {
            requestTime: new Date().toISOString(),
            serverUptime: Math.round(process.uptime()),
            memoryUsage: {
              rss: Math.round(process.memoryUsage().rss / 1024 / 1024),
              heapUsed: Math.round(
                process.memoryUsage().heapUsed / 1024 / 1024
              ),
            },
          },
          timestamp: new Date().toISOString(),
        })
      );
      return;
    }

    logger.info(
      `[AI Routes] Establishing SSE connection for session ${sessionId}`
    );

    // Setup SSE headers
    res.writeHead(200, {
      "Content-Type": "text/event-stream",
      "Cache-Control": "no-cache",
      Connection: "keep-alive",
      "Access-Control-Allow-Origin": "*",
      "Access-Control-Allow-Headers": "Cache-Control",
      "X-Session-Id": sessionId,
    });

    // Send initial connection event
    res.write(`event: connected\n`);
    res.write(
      `data: ${JSON.stringify({
        type: "connected",
        sessionId,
        message: "SSE connection established",
        timestamp: new Date().toISOString(),
      })}\n\n`
    );

    // Setup heartbeat mechanism
    let heartbeatInterval = null;
    let heartbeatCount = 0;

    const startHeartbeat = () => {
      heartbeatInterval = setInterval(() => {
        try {
          heartbeatCount++;
          res.write(`event: heartbeat\n`);
          res.write(
            `data: ${JSON.stringify({
              timestamp: new Date().toISOString(),
              count: heartbeatCount,
            })}\n\n`
          );
          if (res.flush) res.flush();
        } catch (error) {
          logger.warn(
            `[AI Routes] Heartbeat failed for session ${sessionId}:`,
            error.message
          );
          clearInterval(heartbeatInterval);
          streamingAiService.handleConnectionError &&
            streamingAiService.handleConnectionError(sessionId, error);
        }
      }, 15000); // 15 second heartbeat
    };

    const cleanup = () => {
      if (heartbeatInterval) {
        clearInterval(heartbeatInterval);
        heartbeatInterval = null;
      }
      streamingAiService.detachSSEResponse(sessionId);
    };

    // Enhanced client disconnect handling
    req.on("close", () => {
      logger.info(
        `[AI Routes] Client disconnected from session ${sessionId} (heartbeats sent: ${heartbeatCount})`
      );
      cleanup();
    });

    req.on("aborted", () => {
      logger.info(
        `[AI Routes] Client aborted connection for session ${sessionId}`
      );
      cleanup();
    });

    // Handle response errors
    res.on("error", (error) => {
      logger.error(
        `[AI Routes] SSE response error for session ${sessionId}:`,
        error
      );
      cleanup();
      streamingAiService.handleConnectionError &&
        streamingAiService.handleConnectionError(sessionId, error);
    });

    res.on("close", () => {
      logger.debug(`[AI Routes] SSE response closed for session ${sessionId}`);
      cleanup();
    });

    // Start heartbeat
    startHeartbeat();

    // Store the SSE response with the session for sending updates
    streamingAiService.attachSSEResponse(sessionId, res);

    // Check if this session needs to start streaming
    const sessionStatus = streamingAiService.getSessionStatus(sessionId);
    if (
      sessionStatus &&
      sessionStatus.status === "initialized" &&
      !sessionStatus.streamingStarted
    ) {
      // Mark that streaming is starting to prevent duplicate starts
      sessionStatus.streamingStarted = true;

      // Start the streaming process now that SSE is connected
      logger.info(
        `[AI Routes] Starting streaming for session ${sessionId} after SSE connection`
      );
      streamingAiService
        .startStreamingQuoteGeneration(sessionId)
        .catch((error) => {
          logger.error(
            `[AI Routes] Streaming generation failed for ${sessionId}:`,
            error
          );
        });
    }
  } catch (error) {
    logger.error(
      `[AI Routes] Error setting up SSE for session ${req.params.sessionId}:`,
      error
    );
    res.status(500).json({
      error: "Failed to establish SSE connection",
      message: error.message,
    });
  }
});

/**
 * @route   POST /api/ai/health/reset
 * @desc    Reset AI service health metrics
 * @access  Private (Admin only)
 */
router.post("/health/reset", protect, (req, res, next) => {
  try {
    // Check if user is admin (add proper admin check based on your auth system)
    if (!req.user || req.user.role !== "admin") {
      return res.status(403).json({ error: "Admin access required" });
    }

    const aiHealthService = require("../services/aiHealthService");
    const result = aiHealthService.resetAllMetrics();

    res.json(result);
  } catch (error) {
    logger.error("Error resetting AI health metrics:", error);
    next(error);
  }
});

module.exports = router;
