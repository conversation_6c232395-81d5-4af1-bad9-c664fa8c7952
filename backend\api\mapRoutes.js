const express = require("express");
const { getPlaceAutocomplete } = require("../controllers/mapController"); // Destructure directly
const { protect } = require("../middleware/authMiddleware"); // Destructure the protect middleware

const router = express.Router();

// @route   GET /api/maps/autocomplete
// @desc    Get Google Places Autocomplete suggestions via backend
// @access  Private (requires authentication)
router.get(
  "/autocomplete",
  protect, // Use the specific protect middleware function
  getPlaceAutocomplete // Use the destructured function
);

module.exports = router;
