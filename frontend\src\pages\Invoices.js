import React, { useState, useEffect, useCallback } from "react";
import {
  Container,
  Typography,
  Box,
  Paper,
  Grid,
  Button,
  TextField,
  InputAdornment,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  Chip,
  IconButton,
  CircularProgress,
  Alert,
  Menu,
  MenuItem,
  Tooltip,
  Card,
  CardContent,
  FormControl,
  InputLabel,
  Select,
  Divider,
} from "@mui/material";
import {
  Search as SearchIcon,
  Add as AddIcon,
  FilterList as FilterListIcon,
  Receipt as ReceiptIcon,
  ArrowUpward as ArrowUpwardIcon,
  ArrowDownward as ArrowDownwardIcon,
  MoreVert as MoreVertIcon,
  Visibility as VisibilityIcon,
  Edit as EditIcon,
  Email as EmailIcon,
  Print as PrintIcon,
  AttachMoney as PaymentIcon,
  Delete as DeleteIcon,
} from "@mui/icons-material";
import { useNavigate } from "react-router-dom";
import { useSelector, useDispatch } from "react-redux";
import {
  getInvoices,
  deleteInvoice,
  sendInvoice,
  markInvoiceAsPaid,
  generateInvoicePdf,
} from "../slices/invoiceSlice";
import { enqueueSnackbar } from "../slices/snackbarSlice";

const Invoices = () => {
  const [searchTerm, setSearchTerm] = useState("");
  const [status, setStatus] = useState("all");
  const [dateRange, setDateRange] = useState("all");
  const [sortField, setSortField] = useState("issueDate");
  const [sortDirection, setSortDirection] = useState("desc");
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [anchorEl, setAnchorEl] = useState(null);
  const [selectedInvoiceId, setSelectedInvoiceId] = useState(null);

  // We need auth state for authorization in other functions, even if not directly used here
  useSelector((state) => state.auth);
  const { invoices, loading, error } = useSelector((state) => state.invoices);
  const dispatch = useDispatch();
  const navigate = useNavigate();

  // Calculate stats from real data
  const stats = {
    total: invoices?.length || 0,
    paid: invoices?.filter((inv) => inv.status === "PAID").length || 0,
    unpaid:
      invoices?.filter((inv) =>
        ["DRAFT", "SENT", "VIEWED"].includes(inv.status)
      ).length || 0,
    overdue: invoices?.filter((inv) => inv.status === "OVERDUE").length || 0,
    amountDue: invoices?.reduce((sum, inv) => sum + (inv.balance || 0), 0) || 0,
  };

  // Memoize the loadInvoices function to avoid dependency cycle
  const loadInvoices = useCallback(() => {
    const filters = {};

    // Apply status filter
    if (status !== "all") {
      // Convert UI status to API format
      const statusMap = {
        paid: "PAID",
        partially_paid: "PARTIAL",
        unpaid: "DRAFT",
        overdue: "OVERDUE",
      };
      filters.status = statusMap[status] || status.toUpperCase();
    }

    // Apply date range filter
    if (dateRange !== "all") {
      const today = new Date();

      switch (dateRange) {
        case "last30":
          const thirtyDaysAgo = new Date();
          thirtyDaysAgo.setDate(today.getDate() - 30);
          filters.startDate = thirtyDaysAgo.toISOString().split("T")[0];
          break;
        case "last90":
          const ninetyDaysAgo = new Date();
          ninetyDaysAgo.setDate(today.getDate() - 90);
          const thirtyDaysAgo2 = new Date();
          thirtyDaysAgo2.setDate(today.getDate() - 30);
          filters.startDate = ninetyDaysAgo.toISOString().split("T")[0];
          filters.endDate = thirtyDaysAgo2.toISOString().split("T")[0];
          break;
        case "older":
          const ninetyDaysAgo2 = new Date();
          ninetyDaysAgo2.setDate(today.getDate() - 90);
          filters.endDate = ninetyDaysAgo2.toISOString().split("T")[0];
          break;
        default:
          break;
      }
    }

    // Apply search term
    if (searchTerm.trim() !== "") {
      filters.search = searchTerm;
    }

    // Apply sorting
    let sortBy = sortField;
    if (sortField === "dateIssued") sortBy = "issueDate";
    if (sortField === "dateDue") sortBy = "dueDate";

    filters.sortBy = sortBy;
    filters.sortOrder = sortDirection;

    dispatch(
      getInvoices({
        page: page + 1, // API uses 1-based pagination
        limit: rowsPerPage,
        ...filters,
      })
    );
  }, [
    dispatch,
    page,
    rowsPerPage,
    searchTerm,
    status,
    dateRange,
    sortField,
    sortDirection,
  ]);

  useEffect(() => {
    loadInvoices();
  }, [page, rowsPerPage, loadInvoices]);

  // Handle search input change
  const handleSearchChange = (event) => {
    const searchValue = event.target.value.toLowerCase();
    setSearchTerm(searchValue);
  };

  // Handle status filter change
  const handleStatusChange = (event) => {
    const statusValue = event.target.value;
    setStatus(statusValue);
    setPage(0); // Reset to first page
  };

  // Handle date range filter change
  const handleDateRangeChange = (event) => {
    const rangeValue = event.target.value;
    setDateRange(rangeValue);
    setPage(0); // Reset to first page
  };

  // Apply filters when user clicks the search/filter button
  const applyFilters = () => {
    loadInvoices();
  };

  // Handle sort change
  const handleSort = (field) => {
    const newDirection =
      field === sortField && sortDirection === "asc" ? "desc" : "asc";
    setSortField(field);
    setSortDirection(newDirection);
    loadInvoices();
  };

  // Handle page change
  const handleChangePage = (event, newPage) => {
    setPage(newPage);
  };

  // Handle rows per page change
  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  // Format date
  const formatDate = (dateString) => {
    if (!dateString) return "N/A";
    const options = { year: "numeric", month: "short", day: "numeric" };
    return new Date(dateString).toLocaleDateString("en-US", options);
  };

  // Handle menu click
  const handleMenuClick = (event, invoiceId) => {
    setAnchorEl(event.currentTarget);
    setSelectedInvoiceId(invoiceId);
  };

  // Handle menu close
  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  // Handle view invoice
  const handleViewInvoice = () => {
    navigate(`/invoices/${selectedInvoiceId}`);
    handleMenuClose();
  };

  // Handle edit invoice
  const handleEditInvoice = () => {
    navigate(`/invoices/edit/${selectedInvoiceId}`);
    handleMenuClose();
  };

  // Handle email invoice
  const handleEmailInvoice = () => {
    const selectedInvoice = invoices.find(
      (inv) => inv._id === selectedInvoiceId
    );
    if (!selectedInvoice || !selectedInvoice.customer) {
      dispatch(
        enqueueSnackbar({
          message: "Customer information is missing",
          severity: "error",
        })
      );
      handleMenuClose();
      return;
    }

    const emailData = {
      to: selectedInvoice.customer.email,
      subject: `Invoice ${selectedInvoice.number}`,
      message: `Please find attached your invoice ${
        selectedInvoice.number
      } due on ${formatDate(selectedInvoice.dueDate)}.`,
    };

    dispatch(sendInvoice({ id: selectedInvoiceId, emailData }))
      .unwrap()
      .then(() => {
        dispatch(
          enqueueSnackbar({
            message: "Invoice sent successfully",
            severity: "success",
          })
        );
      })
      .catch((error) => {
        dispatch(
          enqueueSnackbar({
            message: `Error: ${error}`,
            severity: "error",
          })
        );
      });

    handleMenuClose();
  };

  // Handle print invoice
  const handlePrintInvoice = () => {
    dispatch(generateInvoicePdf(selectedInvoiceId))
      .unwrap()
      .then((pdfBlob) => {
        // Create a URL for the blob and open it in a new window
        const pdfUrl = URL.createObjectURL(pdfBlob);
        const newWindow = window.open(pdfUrl, "_blank");
        if (!newWindow) {
          dispatch(
            enqueueSnackbar({
              message: "Please allow popups to view the PDF",
              severity: "warning",
            })
          );
        }
      })
      .catch((error) => {
        dispatch(
          enqueueSnackbar({
            message: `Error generating PDF: ${error}`,
            severity: "error",
          })
        );
      });

    handleMenuClose();
  };

  // Handle record payment
  const handleRecordPayment = () => {
    // In a real app, this would open a payment dialog
    // For now, we'll just log it and show a notification
    const selectedInvoice = invoices.find(
      (inv) => inv._id === selectedInvoiceId
    );
    if (!selectedInvoice) {
      handleMenuClose();
      return;
    }

    const paymentData = {
      amount: selectedInvoice.balance,
      method: "CREDIT_CARD",
      date: new Date().toISOString(),
      notes: "Payment recorded via invoice list",
    };

    dispatch(markInvoiceAsPaid({ id: selectedInvoiceId, paymentData }))
      .unwrap()
      .then(() => {
        dispatch(
          enqueueSnackbar({
            message: "Payment recorded successfully",
            severity: "success",
          })
        );
        loadInvoices(); // Reload to update the list
      })
      .catch((error) => {
        dispatch(
          enqueueSnackbar({
            message: `Error: ${error}`,
            severity: "error",
          })
        );
      });

    handleMenuClose();
  };

  // Handle delete invoice
  const handleDeleteInvoice = () => {
    if (
      window.confirm(
        "Are you sure you want to delete this invoice? This cannot be undone."
      )
    ) {
      dispatch(deleteInvoice(selectedInvoiceId))
        .unwrap()
        .then(() => {
          dispatch(
            enqueueSnackbar({
              message: "Invoice deleted successfully",
              severity: "success",
            })
          );
          loadInvoices(); // Reload the list after deletion
        })
        .catch((error) => {
          dispatch(
            enqueueSnackbar({
              message: `Error: ${error}`,
              severity: "error",
            })
          );
        });
    }
    handleMenuClose();
  };

  // Get status chip based on status value
  const getStatusChip = (status) => {
    const statusMap = {
      PAID: { label: "Paid", color: "success" },
      PARTIAL: { label: "Partially Paid", color: "warning" },
      DRAFT: { label: "Draft", color: "default" },
      SENT: { label: "Sent", color: "primary" },
      VIEWED: { label: "Viewed", color: "info" },
      OVERDUE: { label: "Overdue", color: "error" },
      VOID: { label: "Void", color: "default" },
    };

    const statusConfig = statusMap[status] || {
      label: status,
      color: "default",
    };
    return (
      <Chip
        label={statusConfig.label}
        color={statusConfig.color}
        size="small"
      />
    );
  };

  return (
    <Container maxWidth="xl" sx={{ mt: 4, mb: 4 }}>
      {/* Invoices Overview Cards */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={4} lg={2.4}>
          <Card>
            <CardContent>
              <Typography color="text.secondary" gutterBottom>
                Total Invoices
              </Typography>
              <Box
                display="flex"
                justifyContent="space-between"
                alignItems="center"
              >
                <Typography variant="h4" component="div">
                  {stats.total}
                </Typography>
                <ReceiptIcon fontSize="large" color="primary" />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={4} lg={2.4}>
          <Card>
            <CardContent>
              <Typography color="text.secondary" gutterBottom>
                Paid
              </Typography>
              <Box
                display="flex"
                justifyContent="space-between"
                alignItems="center"
              >
                <Typography variant="h4" component="div">
                  {stats.paid}
                </Typography>
                <PaymentIcon fontSize="large" color="success" />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={4} lg={2.4}>
          <Card>
            <CardContent>
              <Typography color="text.secondary" gutterBottom>
                Unpaid
              </Typography>
              <Box
                display="flex"
                justifyContent="space-between"
                alignItems="center"
              >
                <Typography variant="h4" component="div">
                  {stats.unpaid}
                </Typography>
                <ReceiptIcon fontSize="large" color="primary" />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={4} lg={2.4}>
          <Card>
            <CardContent>
              <Typography color="text.secondary" gutterBottom>
                Overdue
              </Typography>
              <Box
                display="flex"
                justifyContent="space-between"
                alignItems="center"
              >
                <Typography variant="h4" component="div" color="error">
                  {stats.overdue}
                </Typography>
                <ReceiptIcon fontSize="large" color="error" />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={4} lg={2.4}>
          <Card>
            <CardContent>
              <Typography color="text.secondary" gutterBottom>
                Amount Due
              </Typography>
              <Box
                display="flex"
                justifyContent="space-between"
                alignItems="center"
              >
                <Typography variant="h4" component="div">
                  $
                  {stats.amountDue.toLocaleString(undefined, {
                    minimumFractionDigits: 2,
                    maximumFractionDigits: 2,
                  })}
                </Typography>
                <Typography variant="h4" color="primary">
                  $
                </Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      <Paper sx={{ p: 2, mb: 4 }}>
        <Box
          display="flex"
          justifyContent="space-between"
          alignItems="center"
          mb={3}
        >
          <Typography component="h1" variant="h5">
            Invoices
          </Typography>
          <Button
            variant="contained"
            color="primary"
            startIcon={<AddIcon />}
            onClick={() => navigate("/invoices/create")}
          >
            Create Invoice
          </Button>
        </Box>

        {/* Search and Filters */}
        <Grid container spacing={2} sx={{ mb: 3 }}>
          <Grid item xs={12} md={4}>
            <TextField
              fullWidth
              placeholder="Search by invoice #, customer, job..."
              value={searchTerm}
              onChange={handleSearchChange}
              onKeyPress={(e) => e.key === "Enter" && applyFilters()}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon />
                  </InputAdornment>
                ),
                endAdornment: (
                  <InputAdornment position="end">
                    <IconButton
                      size="small"
                      onClick={applyFilters}
                      aria-label="search"
                    >
                      <SearchIcon />
                    </IconButton>
                  </InputAdornment>
                ),
              }}
              variant="outlined"
              size="small"
            />
          </Grid>

          <Grid item xs={12} sm={6} md={2.5}>
            <FormControl fullWidth size="small">
              <InputLabel id="status-filter-label">Status</InputLabel>
              <Select
                labelId="status-filter-label"
                id="status-filter"
                value={status}
                label="Status"
                onChange={handleStatusChange}
              >
                <MenuItem value="all">All Statuses</MenuItem>
                <MenuItem value="paid">Paid</MenuItem>
                <MenuItem value="partially_paid">Partially Paid</MenuItem>
                <MenuItem value="unpaid">Unpaid</MenuItem>
                <MenuItem value="overdue">Overdue</MenuItem>
              </Select>
            </FormControl>
          </Grid>

          <Grid item xs={12} sm={6} md={2.5}>
            <FormControl fullWidth size="small">
              <InputLabel id="date-filter-label">Date Range</InputLabel>
              <Select
                labelId="date-filter-label"
                id="date-filter"
                value={dateRange}
                label="Date Range"
                onChange={handleDateRangeChange}
              >
                <MenuItem value="all">All Time</MenuItem>
                <MenuItem value="last30">Last 30 Days</MenuItem>
                <MenuItem value="last90">30-90 Days</MenuItem>
                <MenuItem value="older">Older than 90 Days</MenuItem>
              </Select>
            </FormControl>
          </Grid>

          <Grid
            item
            xs={12}
            md={3}
            sx={{ display: "flex", justifyContent: "flex-end" }}
          >
            <Button
              startIcon={<FilterListIcon />}
              onClick={() => {
                setSearchTerm("");
                setStatus("all");
                setDateRange("all");
                setPage(0);
                loadInvoices();
              }}
            >
              Clear Filters
            </Button>
          </Grid>
        </Grid>

        {/* Error Alert */}
        {error && (
          <Alert severity="error" sx={{ mb: 3 }}>
            {error}
          </Alert>
        )}

        {/* Invoices Table */}
        {loading ? (
          <Box display="flex" justifyContent="center" p={3}>
            <CircularProgress />
          </Box>
        ) : invoices?.length === 0 ? (
          <Alert severity="info" sx={{ mb: 3 }}>
            No invoices found matching your search criteria.
          </Alert>
        ) : (
          <>
            <TableContainer>
              <Table sx={{ minWidth: 650 }}>
                <TableHead>
                  <TableRow>
                    <TableCell>
                      <Box
                        sx={{
                          display: "flex",
                          alignItems: "center",
                          cursor: "pointer",
                        }}
                        onClick={() => handleSort("number")}
                      >
                        Invoice #
                        {sortField === "number" &&
                          (sortDirection === "asc" ? (
                            <ArrowUpwardIcon fontSize="small" />
                          ) : (
                            <ArrowDownwardIcon fontSize="small" />
                          ))}
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Box
                        sx={{
                          display: "flex",
                          alignItems: "center",
                          cursor: "pointer",
                        }}
                        onClick={() => handleSort("customer.name")}
                      >
                        Customer
                        {sortField === "customer.name" &&
                          (sortDirection === "asc" ? (
                            <ArrowUpwardIcon fontSize="small" />
                          ) : (
                            <ArrowDownwardIcon fontSize="small" />
                          ))}
                      </Box>
                    </TableCell>
                    <TableCell>Job</TableCell>
                    <TableCell>
                      <Box
                        sx={{
                          display: "flex",
                          alignItems: "center",
                          cursor: "pointer",
                        }}
                        onClick={() => handleSort("issueDate")}
                      >
                        Date
                        {sortField === "issueDate" &&
                          (sortDirection === "asc" ? (
                            <ArrowUpwardIcon fontSize="small" />
                          ) : (
                            <ArrowDownwardIcon fontSize="small" />
                          ))}
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Box
                        sx={{
                          display: "flex",
                          alignItems: "center",
                          cursor: "pointer",
                        }}
                        onClick={() => handleSort("dueDate")}
                      >
                        Due Date
                        {sortField === "dueDate" &&
                          (sortDirection === "asc" ? (
                            <ArrowUpwardIcon fontSize="small" />
                          ) : (
                            <ArrowDownwardIcon fontSize="small" />
                          ))}
                      </Box>
                    </TableCell>
                    <TableCell>Status</TableCell>
                    <TableCell align="right">
                      <Box
                        sx={{
                          display: "flex",
                          alignItems: "center",
                          justifyContent: "flex-end",
                          cursor: "pointer",
                        }}
                        onClick={() => handleSort("total")}
                      >
                        Total
                        {sortField === "total" &&
                          (sortDirection === "asc" ? (
                            <ArrowUpwardIcon fontSize="small" />
                          ) : (
                            <ArrowDownwardIcon fontSize="small" />
                          ))}
                      </Box>
                    </TableCell>
                    <TableCell align="right">
                      <Box
                        sx={{
                          display: "flex",
                          alignItems: "center",
                          justifyContent: "flex-end",
                          cursor: "pointer",
                        }}
                        onClick={() => handleSort("balance")}
                      >
                        Balance
                        {sortField === "balance" &&
                          (sortDirection === "asc" ? (
                            <ArrowUpwardIcon fontSize="small" />
                          ) : (
                            <ArrowDownwardIcon fontSize="small" />
                          ))}
                      </Box>
                    </TableCell>
                    <TableCell align="center">Actions</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {invoices.map((invoice) => (
                    <TableRow key={invoice._id} hover>
                      <TableCell component="th" scope="row">
                        <Typography
                          variant="body2"
                          sx={{
                            fontWeight: "medium",
                            cursor: "pointer",
                            "&:hover": { textDecoration: "underline" },
                          }}
                          onClick={() => navigate(`/invoices/${invoice._id}`)}
                        >
                          {invoice.number}
                        </Typography>
                      </TableCell>
                      <TableCell>{invoice.customer?.name || "N/A"}</TableCell>
                      <TableCell>{invoice.job?.title || "N/A"}</TableCell>
                      <TableCell>{formatDate(invoice.issueDate)}</TableCell>
                      <TableCell>{formatDate(invoice.dueDate)}</TableCell>
                      <TableCell>{getStatusChip(invoice.status)}</TableCell>
                      <TableCell align="right">
                        ${invoice.total?.toFixed(2) || "0.00"}
                      </TableCell>
                      <TableCell align="right">
                        <Typography
                          color={
                            invoice.balance > 0
                              ? invoice.status === "OVERDUE"
                                ? "error"
                                : "inherit"
                              : "success.main"
                          }
                          fontWeight={
                            invoice.status === "OVERDUE" ? "bold" : "regular"
                          }
                        >
                          ${invoice.balance?.toFixed(2) || "0.00"}
                        </Typography>
                      </TableCell>
                      <TableCell align="center">
                        <Tooltip title="Options">
                          <IconButton
                            size="small"
                            onClick={(event) =>
                              handleMenuClick(event, invoice._id)
                            }
                          >
                            <MoreVertIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
            <TablePagination
              rowsPerPageOptions={[5, 10, 25, 50]}
              component="div"
              count={invoices.length}
              rowsPerPage={rowsPerPage}
              page={page}
              onPageChange={handleChangePage}
              onRowsPerPageChange={handleChangeRowsPerPage}
            />

            {/* Actions Menu */}
            <Menu
              anchorEl={anchorEl}
              open={Boolean(anchorEl)}
              onClose={handleMenuClose}
            >
              <MenuItem onClick={handleViewInvoice}>
                <VisibilityIcon fontSize="small" sx={{ mr: 1 }} />
                View
              </MenuItem>
              <MenuItem onClick={handleEditInvoice}>
                <EditIcon fontSize="small" sx={{ mr: 1 }} />
                Edit
              </MenuItem>
              <MenuItem onClick={handleEmailInvoice}>
                <EmailIcon fontSize="small" sx={{ mr: 1 }} />
                Email
              </MenuItem>
              <MenuItem onClick={handlePrintInvoice}>
                <PrintIcon fontSize="small" sx={{ mr: 1 }} />
                Print
              </MenuItem>
              <MenuItem onClick={handleRecordPayment}>
                <PaymentIcon fontSize="small" sx={{ mr: 1 }} />
                Record Payment
              </MenuItem>
              <Divider />
              <MenuItem
                onClick={handleDeleteInvoice}
                sx={{ color: "error.main" }}
              >
                <DeleteIcon fontSize="small" sx={{ mr: 1 }} />
                Delete
              </MenuItem>
            </Menu>
          </>
        )}
      </Paper>
    </Container>
  );
};

export default Invoices;
