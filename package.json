{"name": "workiz", "version": "1.0.0", "description": "Field service management system with AI capabilities", "scripts": {"test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:e2e": "jest --config jest.e2e.config.js", "test:unit": "jest --testPathPattern=tests/unit", "test:integration": "jest --testPathPattern=tests/integration", "test:ai": "jest --testPathPattern=tests/jobs/jobAI.test.js", "test:ci": "jest --ci --coverage --reporters='default' --reporters='jest-junit'", "test:performance": "jest --testPathPattern=tests/performance", "perf": "node tests/performance/runPerformanceTests.js", "perf:report": "npm run perf && open-cli test-results/performance-report.md", "update:checklist": "node tests/utils/updateChecklist.js", "verify:ready": "npm run test:coverage && npm run test:performance && npm run test:ai && npm run update:checklist", "report:status": "npm run verify:ready && open-cli tests/status-report.md", "checklist": "./tests/utils/checklist-cli.js"}, "dependencies": {"@mui/icons-material": "^5.0.0", "@mui/material": "^5.0.0", "@reduxjs/toolkit": "^1.9.0", "allotment": "^1.20.3", "axios": "^1.11.0", "bcryptjs": "^2.4.3", "bull": "^4.11.3", "cheerio": "^1.0.0-rc.12", "date-fns": "^2.30.0", "dotenv": "^16.5.0", "express": "^4.17.1", "fuse.js": "^7.0.0", "image-hash": "^5.3.2", "jsonwebtoken": "^9.0.2", "mongoose": "^6.0.0", "openai": "^4.0.0", "playwright": "^1.41.2", "playwright-extra": "^4.3.6", "puppeteer-extra-plugin-stealth": "^2.11.2", "rate-limit-redis": "^4.2.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-redux": "^8.0.0", "react-router-dom": "^6.0.0", "redis": "^4.0.0"}, "bin": {"checklist": "./tests/utils/checklist-cli.js"}, "devDependencies": {"@babel/core": "^7.22.0", "@babel/preset-env": "^7.22.0", "@babel/preset-react": "^7.22.0", "@playwright/test": "^1.41.2", "@testing-library/jest-dom": "^5.16.0", "@testing-library/react": "^14.0.0", "@testing-library/user-event": "^14.0.0", "@types/commander": "^2.12.2", "autocannon": "^7.12.0", "babel-jest": "^29.0.0", "chalk": "^4.1.2", "clinic": "^9.1.0", "commander": "^11.0.0", "concurrently": "^8.0.0", "eslint": "^8.0.0", "eslint-plugin-jest": "^27.0.0", "eslint-plugin-playwright": "^0.18.0", "eslint-plugin-react": "^7.0.0", "form-data": "^4.0.2", "husky": "^8.0.0", "jest": "^29.0.0", "jest-environment-jsdom": "^29.0.0", "jest-junit": "^16.0.0", "k6": "^0.0.0", "lint-staged": "^13.0.0", "loadtest": "^8.0.0", "markdown-table": "^3.0.0", "mongodb-memory-server": "^8.0.0", "msw": "^2.8.4", "nodemon": "^2.0.0", "open-cli": "^7.0.0", "pixelmatch": "^5.3.0", "prettier": "^2.0.0", "supertest": "^6.0.0"}, "jest": {"testEnvironment": "node", "setupFilesAfterEnv": ["<rootDir>/tests/utils/setupTests.js"], "setupFiles": ["<rootDir>/tests/utils/setEnvVars.js"], "collectCoverageFrom": ["src/**/*.{js,jsx}", "!src/index.js", "!src/serviceWorker.js"], "coverageThreshold": {"global": {"statements": 80, "branches": 70, "functions": 80, "lines": 80}}}, "lint-staged": {"*.{js,jsx}": ["eslint --fix", "prettier --write", "jest --findRelatedTests"]}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "engines": {"node": ">=14.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/yourusername/workiz.git"}, "author": "Your Name", "license": "MIT"}