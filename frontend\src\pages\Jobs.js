import React, { useEffect, useState, useCallback } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import {
  Box,
  Typography,
  Button,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  Chip,
  IconButton,
  TextField,
  InputAdornment,
  CircularProgress,
  Alert,
  ToggleButtonGroup,
  ToggleButton,
  Tooltip,
  Card,
  CardContent,
  Grid,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Snackbar,
} from "@mui/material";
import {
  Add as AddIcon,
  Search as SearchIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Visibility as VisibilityIcon,
  Warning as WarningIcon,
  Schedule as ScheduleIcon,
  CheckCircle as CheckCircleIcon,
  Sort as SortIcon,
  Psychology as PsychologyIcon,
  AssignmentInd as AssignmentIndIcon,
  Timeline as TimelineIcon,
  FindInPage as FindInPageIcon,
} from "@mui/icons-material";
import {
  getJobs,
  deleteJob,
  resetJobState,
  sortJobsByPriority,
  filterHighRiskJobs,
  setJobFilters,
  assignTechnicians,
  analyzeJobRisks,
  predictJobProgress,
} from "../slices/jobSlice";

const Jobs = () => {
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [showHighRiskOnly, setShowHighRiskOnly] = useState(false);
  const [sortOrder, setSortOrder] = useState("priority");

  // AI-specific state
  const [selectedJobIds, setSelectedJobIds] = useState([]);
  const [assignDialogOpen, setAssignDialogOpen] = useState(false);
  const [riskAnalysisDialogOpen, setRiskAnalysisDialogOpen] = useState(false);
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState("");

  const dispatch = useDispatch();
  const navigate = useNavigate();

  const {
    jobs,
    loading,
    error,
    totalPages,
    assignmentResult,
    riskAnalysis,
    progressPrediction,
  } = useSelector((state) => state.jobs);

  const loadJobs = useCallback(() => {
    const filters = {
      status: statusFilter !== "all" ? statusFilter : undefined,
      search: searchTerm,
      highRiskOnly: showHighRiskOnly,
    };

    dispatch(setJobFilters(filters));
    dispatch(
      getJobs({
        page: page + 1,
        limit: rowsPerPage,
        filters,
      })
    );

    if (sortOrder === "priority") {
      dispatch(sortJobsByPriority());
    }
  }, [
    dispatch,
    page,
    rowsPerPage,
    searchTerm,
    statusFilter,
    showHighRiskOnly,
    sortOrder,
  ]);

  useEffect(() => {
    dispatch(resetJobState());
    loadJobs();
  }, [dispatch, loadJobs]);

  // Show success message when AI operations complete
  useEffect(() => {
    if (assignmentResult) {
      setSnackbarMessage("Technicians assigned successfully!");
      setSnackbarOpen(true);
    } else if (riskAnalysis) {
      setSnackbarMessage("Risk analysis completed!");
      setSnackbarOpen(true);
    } else if (progressPrediction) {
      setSnackbarMessage("Progress prediction completed!");
      setSnackbarOpen(true);
    }
  }, [assignmentResult, riskAnalysis, progressPrediction]);

  const handleChangePage = (event, newPage) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const handleSearch = (e) => {
    e.preventDefault();
    setPage(0);
    loadJobs();
  };

  const handleStatusFilterChange = (event, newStatus) => {
    if (newStatus !== null) {
      setStatusFilter(newStatus);
      setPage(0);
    }
  };

  const handleSortOrderChange = (event, newOrder) => {
    if (newOrder !== null) {
      setSortOrder(newOrder);
      if (newOrder === "priority") {
        dispatch(sortJobsByPriority());
      }
      // Add other sort options as needed
    }
  };

  const handleHighRiskToggle = () => {
    setShowHighRiskOnly(!showHighRiskOnly);
    if (!showHighRiskOnly) {
      dispatch(filterHighRiskJobs());
    }
    setPage(0);
  };

  const handleDeleteJob = (id) => {
    if (window.confirm("Are you sure you want to delete this job?")) {
      dispatch(deleteJob(id));
    }
  };

  // AI-specific handlers
  const handleJobSelection = (jobId) => {
    setSelectedJobIds((prev) =>
      prev.includes(jobId)
        ? prev.filter((id) => id !== jobId)
        : [...prev, jobId]
    );
  };

  const handleBulkAssignTechnicians = () => {
    if (selectedJobIds.length === 0) {
      setSnackbarMessage("Please select at least one job first");
      setSnackbarOpen(true);
      return;
    }
    setAssignDialogOpen(true);
  };

  const confirmAssignTechnicians = () => {
    dispatch(assignTechnicians({ jobIds: selectedJobIds }));
    setAssignDialogOpen(false);
    setSelectedJobIds([]);
  };

  const handleAnalyzeRisks = () => {
    if (selectedJobIds.length === 0) {
      setSnackbarMessage("Please select at least one job first");
      setSnackbarOpen(true);
      return;
    }
    setRiskAnalysisDialogOpen(true);
  };

  const confirmRiskAnalysis = () => {
    dispatch(analyzeJobRisks({ jobIds: selectedJobIds }));
    setRiskAnalysisDialogOpen(false);
    setSelectedJobIds([]);
  };

  const handlePredictProgress = (jobId) => {
    dispatch(predictJobProgress(jobId));
  };

  const handleFindSimilarJobs = (jobId) => {
    navigate(`/jobs/${jobId}/similar`);
  };

  const handleCloseSnackbar = () => {
    setSnackbarOpen(false);
  };

  const renderPriorityChip = (priority, score) => {
    // Use the AI-generated priority score from aiInsights if available
    const priorityScore = score || 50;
    const color =
      priorityScore >= 80
        ? "error"
        : priorityScore >= 50
        ? "warning"
        : "success";
    return (
      <Tooltip title={`Priority Score: ${priorityScore}`}>
        <Chip
          label={priority}
          color={color}
          size="small"
          sx={{ fontWeight: "bold" }}
        />
      </Tooltip>
    );
  };

  const renderStatusChip = (status) => {
    const statusColors = {
      Scheduled: "info",
      "In Progress": "warning",
      Completed: "success",
      Cancelled: "error",
    };

    return (
      <Chip
        label={status}
        color={statusColors[status]}
        size="small"
        icon={status === "Completed" ? <CheckCircleIcon /> : <ScheduleIcon />}
      />
    );
  };

  const renderRiskIndicators = (job) => {
    // Use the new riskAssessment field structure
    if (!job.riskAssessment?.riskFactors?.length) return null;

    return (
      <Tooltip title={job.riskAssessment.riskFactors.join("\n")}>
        <Chip
          icon={<WarningIcon />}
          label={`${job.riskAssessment.overallRiskLevel} Risk`}
          color={
            job.riskAssessment.overallRiskLevel === "High" ? "error" : "warning"
          }
          size="small"
          variant="outlined"
        />
      </Tooltip>
    );
  };

  const renderAIInsightsBadge = (job) => {
    if (!job.aiInsights) return null;

    return (
      <Tooltip title="AI Insights Available">
        <Chip
          icon={<PsychologyIcon />}
          label="AI Insights"
          color="primary"
          size="small"
          variant="outlined"
        />
      </Tooltip>
    );
  };

  return (
    <Box>
      <Box sx={{ display: "flex", justifyContent: "space-between", mb: 3 }}>
        <Typography variant="h4">Jobs</Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={() => navigate("/jobs/create")}
        >
          Create Job
        </Button>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {/* AI Action Buttons */}
      <Paper sx={{ mb: 3, p: 2 }}>
        <Typography variant="h6" gutterBottom>
          AI Assistant
        </Typography>
        <Box sx={{ display: "flex", gap: 2 }}>
          <Button
            variant="outlined"
            startIcon={<AssignmentIndIcon />}
            onClick={handleBulkAssignTechnicians}
            disabled={selectedJobIds.length === 0}
          >
            Auto-Assign Technicians
          </Button>
          <Button
            variant="outlined"
            startIcon={<WarningIcon />}
            onClick={handleAnalyzeRisks}
            disabled={selectedJobIds.length === 0}
          >
            Analyze Risks
          </Button>
          <Typography variant="body2" sx={{ alignSelf: "center", ml: 2 }}>
            {selectedJobIds.length > 0
              ? `${selectedJobIds.length} jobs selected`
              : "Select jobs to enable AI actions"}
          </Typography>
        </Box>
      </Paper>

      {/* Filters and Search */}
      <Paper sx={{ mb: 3, p: 2 }}>
        <Grid container spacing={2}>
          <Grid item xs={12} md={6}>
            <Box component="form" onSubmit={handleSearch}>
              <TextField
                fullWidth
                placeholder="Search jobs..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <SearchIcon />
                    </InputAdornment>
                  ),
                }}
              />
            </Box>
          </Grid>
          <Grid item xs={12} md={6}>
            <Box sx={{ display: "flex", gap: 2 }}>
              <ToggleButtonGroup
                value={statusFilter}
                exclusive
                onChange={handleStatusFilterChange}
                size="small"
              >
                <ToggleButton value="all">All</ToggleButton>
                <ToggleButton value="Scheduled">Scheduled</ToggleButton>
                <ToggleButton value="In Progress">In Progress</ToggleButton>
                <ToggleButton value="Completed">Completed</ToggleButton>
              </ToggleButtonGroup>

              <ToggleButtonGroup
                value={sortOrder}
                exclusive
                onChange={handleSortOrderChange}
                size="small"
              >
                <ToggleButton value="priority">
                  <Tooltip title="Sort by Priority Score">
                    <SortIcon />
                  </Tooltip>
                </ToggleButton>
              </ToggleButtonGroup>

              <Button
                variant={showHighRiskOnly ? "contained" : "outlined"}
                color="error"
                onClick={handleHighRiskToggle}
                startIcon={<WarningIcon />}
                size="small"
              >
                High Risk
              </Button>
            </Box>
          </Grid>
        </Grid>
      </Paper>

      {/* Jobs Table */}
      <TableContainer component={Paper}>
        {loading ? (
          <Box sx={{ display: "flex", justifyContent: "center", p: 3 }}>
            <CircularProgress />
          </Box>
        ) : (
          <>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell padding="checkbox">
                    <Tooltip title="Select for AI actions">
                      <span>
                        <IconButton disabled>
                          <PsychologyIcon color="disabled" fontSize="small" />
                        </IconButton>
                      </span>
                    </Tooltip>
                  </TableCell>
                  <TableCell>Job Number</TableCell>
                  <TableCell>Title</TableCell>
                  <TableCell>Customer</TableCell>
                  <TableCell>Status</TableCell>
                  <TableCell>Priority</TableCell>
                  <TableCell>Scheduled Date</TableCell>
                  <TableCell>AI Insights</TableCell>
                  <TableCell align="right">Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {jobs.map((job) => (
                  <TableRow key={job._id}>
                    <TableCell padding="checkbox">
                      <Tooltip title="Select for AI actions">
                        <IconButton onClick={() => handleJobSelection(job._id)}>
                          <PsychologyIcon
                            color={
                              selectedJobIds.includes(job._id)
                                ? "primary"
                                : "disabled"
                            }
                            fontSize="small"
                          />
                        </IconButton>
                      </Tooltip>
                    </TableCell>
                    <TableCell>{job.jobNumber}</TableCell>
                    <TableCell>{job.title}</TableCell>
                    <TableCell>{job.customer?.businessName}</TableCell>
                    <TableCell>{renderStatusChip(job.status)}</TableCell>
                    <TableCell>
                      {renderPriorityChip(
                        job.priority,
                        job.aiInsights?.priorityScore
                      )}
                    </TableCell>
                    <TableCell>
                      {new Date(job.scheduledDate).toLocaleDateString()}
                    </TableCell>
                    <TableCell>
                      <Box sx={{ display: "flex", gap: 1 }}>
                        {renderRiskIndicators(job)}
                        {renderAIInsightsBadge(job)}
                      </Box>
                    </TableCell>
                    <TableCell align="right">
                      <IconButton
                        color="primary"
                        onClick={() => navigate(`/jobs/${job._id}`)}
                        size="small"
                      >
                        <VisibilityIcon />
                      </IconButton>
                      <IconButton
                        color="info"
                        onClick={() => handlePredictProgress(job._id)}
                        size="small"
                        title="Predict Progress"
                      >
                        <TimelineIcon />
                      </IconButton>
                      <IconButton
                        color="secondary"
                        onClick={() => handleFindSimilarJobs(job._id)}
                        size="small"
                        title="Find Similar Jobs"
                      >
                        <FindInPageIcon />
                      </IconButton>
                      <IconButton
                        color="secondary"
                        onClick={() => navigate(`/jobs/${job._id}/edit`)}
                        size="small"
                      >
                        <EditIcon />
                      </IconButton>
                      <IconButton
                        color="error"
                        onClick={() => handleDeleteJob(job._id)}
                        size="small"
                      >
                        <DeleteIcon />
                      </IconButton>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
            <TablePagination
              rowsPerPageOptions={[5, 10, 25]}
              component="div"
              count={totalPages && rowsPerPage ? totalPages * rowsPerPage : 0}
              rowsPerPage={rowsPerPage}
              page={page}
              onPageChange={handleChangePage}
              onRowsPerPageChange={handleChangeRowsPerPage}
            />
          </>
        )}
      </TableContainer>

      {/* AI Insights Summary */}
      <Grid container spacing={2} sx={{ mt: 3 }}>
        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                High Risk Jobs
              </Typography>
              <Typography variant="h4" color="error">
                {
                  jobs.filter(
                    (job) => job.riskAssessment?.overallRiskLevel === "High"
                  ).length
                }
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Jobs requiring immediate attention
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                AI Analyzed Jobs
              </Typography>
              <Typography variant="h4" color="primary">
                {jobs.filter((job) => job.aiInsights?.lastAnalyzed).length}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Jobs with AI-powered insights
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Average Priority Score
              </Typography>
              <Typography variant="h4" color="warning">
                {Math.round(
                  jobs.reduce(
                    (acc, job) => acc + (job.aiInsights?.priorityScore || 0),
                    0
                  ) /
                    (jobs.filter((job) => job.aiInsights?.priorityScore)
                      .length || 1)
                )}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Overall job priority level
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Dialogs */}
      <Dialog
        open={assignDialogOpen}
        onClose={() => setAssignDialogOpen(false)}
      >
        <DialogTitle>Assign Technicians</DialogTitle>
        <DialogContent>
          <Typography variant="body1" gutterBottom>
            AI will analyze the selected jobs and automatically assign the most
            suitable technicians based on skills, availability, and proximity to
            job location.
          </Typography>
          <Typography variant="body2" color="text.secondary" sx={{ mt: 2 }}>
            {selectedJobIds.length} jobs selected for automatic assignment
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setAssignDialogOpen(false)}>Cancel</Button>
          <Button
            onClick={confirmAssignTechnicians}
            variant="contained"
            startIcon={<AssignmentIndIcon />}
          >
            Auto-Assign
          </Button>
        </DialogActions>
      </Dialog>

      <Dialog
        open={riskAnalysisDialogOpen}
        onClose={() => setRiskAnalysisDialogOpen(false)}
      >
        <DialogTitle>Analyze Job Risks</DialogTitle>
        <DialogContent>
          <Typography variant="body1" gutterBottom>
            AI will analyze potential risks for the selected jobs, including:
          </Typography>
          <ul>
            <li>Weather impacts</li>
            <li>Technical complexity</li>
            <li>Resource availability</li>
            <li>Customer history</li>
            <li>Schedule conflicts</li>
          </ul>
          <Typography variant="body2" color="text.secondary" sx={{ mt: 2 }}>
            {selectedJobIds.length} jobs selected for risk analysis
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setRiskAnalysisDialogOpen(false)}>
            Cancel
          </Button>
          <Button
            onClick={confirmRiskAnalysis}
            variant="contained"
            color="warning"
            startIcon={<WarningIcon />}
          >
            Analyze Risks
          </Button>
        </DialogActions>
      </Dialog>

      {/* Notification */}
      <Snackbar
        open={snackbarOpen}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
        message={snackbarMessage}
      />
    </Box>
  );
};

export default Jobs;
