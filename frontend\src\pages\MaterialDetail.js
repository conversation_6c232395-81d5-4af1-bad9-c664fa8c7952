import React, { useState, useEffect } from "react";
import {
  Container,
  Typography,
  Box,
  Paper,
  Grid,
  TextField,
  Button,
  Divider,
  CircularProgress,
  Alert,
  Card,
  CardContent,
  InputAdornment,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  Breadcrumbs,
  Link,
  Tooltip, // Added Tooltip
} from "@mui/material";
import {
  ArrowBack as ArrowBackIcon,
  Edit as EditIcon,
  Save as SaveIcon,
  Cancel as CancelIcon,
  History as HistoryIcon,
  Category as MaterialIcon, // Changed from Material2
  WarningAmber as WarningAmberIcon,
  Warehouse as WarehouseIcon, // Use WarningAmber for clarity
  LocalShipping as ShippingIcon,
  TextSnippet as InvoiceIcon, // Removed InfoIcon
} from "@mui/icons-material";
import { useParams, useNavigate, Link as RouterLink } from "react-router-dom";
import { useSelector, useDispatch } from "react-redux";
import { Line } from "react-chartjs-2";
// Correctly import actions from the renamed materialSlice
import {
  getMaterialById,
  updateMaterialItem,
  clearMaterialError,
  getMaterialTransactions,
} from "../slices/materialSlice"; // Import getMaterialTransactions
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip as ChartTooltip,
  Legend, // Re-add alias for chart.js Tooltip
} from "chart.js";

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  ChartTooltip,
  Legend // Use the alias here
);

const MaterialDetail = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const dispatch = useDispatch();
  // const { userInfo } = useSelector((state) => state.auth); // Assuming auth state exists - Removed as unused
  // Select from the correct slice name 'materials' and use 'material' state property
  const {
    material,
    sourceStatus,
    loading,
    error,
    transactions,
    transactionsLoading,
    transactionsError,
  } = useSelector((state) => state.materials); // Add sourceStatus selector

  // Local state for editing and other UI elements
  const [editing, setEditing] = useState(false);
  const [editedItem, setEditedItem] = useState(null); // Will hold the material being edited
  const [savingChanges, setSavingChanges] = useState(false);
  // const [transactions, setTransactions] = useState([]); // Removed local state, use Redux state
  const [usageData, setUsageData] = useState(null); // Keep local state for generated chart data

  // Fetch material details on mount/id change
  useEffect(() => {
    if (id) {
      dispatch(getMaterialById(id));
    }
    // Clear error on component unmount
    return () => {
      dispatch(clearMaterialError());
    };
  }, [dispatch, id]);

  // Update local editing state when material data is fetched/updated from Redux
  useEffect(() => {
    if (material) {
      setEditedItem({ ...material }); // Clone material data for editing form
      // Fetch transactions when material data is available
      dispatch(getMaterialTransactions({ id }));
      // Usage data generation moved to a separate useEffect hook depending on 'transactions'
    } else {
      setEditedItem(null); // Clear if material is null (e.g., on error or initial load)
    }
  }, [material, dispatch, id]); // Depend on material, dispatch, and id

  // Handle form field changes during editing
  const handleChange = (e) => {
    const { name, value } = e.target;
    setEditedItem((prev) => ({
      ...prev,
      // Parse numbers for specific fields, otherwise keep as string
      [name]: ["reorderLevel", "unitPrice", "costPrice"].includes(name)
        ? parseFloat(value) || 0 // Use 0 if parsing fails (quantity is read-only)
        : value,
    }));
  };

  // Handle save changes
  const handleSaveChanges = async () => {
    if (!editedItem || !id) return;
    setSavingChanges(true);
    try {
      // Construct the data payload for the update action
      const updateData = {
        name: editedItem.name,
        sku: editedItem.sku,
        category: editedItem.category,
        description: editedItem.description,
        reorderLevel: editedItem.reorderLevel,
        unitPrice: editedItem.unitPrice,
        costPrice: editedItem.costPrice,
        location: editedItem.location,
        supplier: editedItem.supplier,
        // supplierContact: editedItem.supplierContact, // Field does not exist in model
        // notes: editedItem.notes, // Field does not exist in model
        voltage: editedItem.voltage, // Include new fields
        amperage: editedItem.amperage, // Include new fields
        // Do NOT include quantity here - it should be updated via transactions
      };
      // Dispatch updateMaterialItem action
      await dispatch(updateMaterialItem({ id, itemData: updateData })).unwrap();
      setEditing(false); // Exit editing mode on success
    } catch (err) {
      // Error is handled by the slice and should be displayed via the 'error' state
      console.error("Failed to save material changes:", err);
      // Optionally set a local error state for specific feedback here if needed
    } finally {
      setSavingChanges(false);
    }
  };

  // Format date helper
  const formatDate = (dateString) => {
    if (!dateString) return "N/A";
    try {
      const date = new Date(dateString);
      // Check if date is valid before formatting
      if (isNaN(date.getTime())) {
        return "Invalid Date";
      }
      return date.toLocaleDateString("en-US", {
        year: "numeric",
        month: "short",
        day: "numeric",
        hour: "2-digit",
        minute: "2-digit",
      });
    } catch (e) {
      console.error("Error formatting date:", dateString, e);
      return "Invalid Date";
    }
  };

  // --- Generate Usage Data from Transactions ---
  const generateUsageData = (transactionsData) => {
    if (!transactionsData || transactionsData.length === 0) {
      return null;
    }

    // Sort transactions by date
    const sortedTransactions = [...transactionsData].sort(
      (a, b) => new Date(a.date) - new Date(b.date)
    );

    const monthlyData = {};

    sortedTransactions.forEach((tx) => {
      try {
        const date = new Date(tx.date);
        if (isNaN(date.getTime())) return; // Skip invalid dates

        const monthKey = `${date.getFullYear()}-${String(
          date.getMonth() + 1
        ).padStart(2, "0")}`; // YYYY-MM format

        if (!monthlyData[monthKey]) {
          monthlyData[monthKey] = { received: 0, issued: 0 };
        }

        if (tx.type === "receive") {
          monthlyData[monthKey].received += tx.quantity || 0;
        } else if (tx.type === "issue") {
          monthlyData[monthKey].issued += tx.quantity || 0;
        }
      } catch (e) {
        console.error("Error processing transaction date:", tx, e);
      }
    });

    const sortedMonths = Object.keys(monthlyData).sort();

    if (sortedMonths.length === 0) return null;

    const labels = sortedMonths.map((key) => {
      const [year, month] = key.split("-");
      const date = new Date(year, month - 1);
      return date.toLocaleString("default", {
        month: "short",
        year: "numeric",
      });
    });

    const receivedDataset = {
      label: "Monthly Received",
      data: sortedMonths.map((key) => monthlyData[key].received),
      borderColor: "rgb(75, 192, 192)",
      backgroundColor: "rgba(75, 192, 192, 0.5)",
      tension: 0.1,
    };

    const issuedDataset = {
      label: "Monthly Issued",
      data: sortedMonths.map((key) => monthlyData[key].issued),
      borderColor: "rgb(255, 99, 132)",
      backgroundColor: "rgba(255, 99, 132, 0.5)",
      tension: 0.1,
    };

    return { labels, datasets: [receivedDataset, issuedDataset] };
  };

  // --- Effect to generate usage data when transactions change ---
  useEffect(() => {
    if (transactions && transactions.length > 0) {
      const generatedData = generateUsageData(transactions);
      setUsageData(generatedData);
    } else {
      setUsageData(null); // Clear chart data if no transactions
    }
  }, [transactions]); // Re-run when transactions data from Redux changes

  // Chart options
  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: { position: "top" },
      title: { display: true, text: "Material Usage & Stock Levels" },
    },
  };

  return (
    <Container maxWidth="xl" sx={{ mt: 4, mb: 4 }}>
      {/* Breadcrumbs */}
      <Breadcrumbs aria-label="breadcrumb" sx={{ mb: 2 }}>
        <Link
          component={RouterLink}
          underline="hover"
          color="inherit"
          to="/materials"
          sx={{ display: "flex", alignItems: "center" }}
        >
          <MaterialIcon sx={{ mr: 0.5 }} fontSize="inherit" />
          Materials
        </Link>
        <Typography
          color="text.primary"
          sx={{ display: "flex", alignItems: "center" }}
        >
          {loading ? "Loading..." : material?.name || "Material Detail"}
        </Typography>
      </Breadcrumbs>

      {/* Back Button */}
      <Button
        startIcon={<ArrowBackIcon />}
        onClick={() => navigate("/materials")}
        sx={{ mb: 3 }}
      >
        Back to Materials List
      </Button>

      {loading ? (
        <Box display="flex" justifyContent="center" p={3}>
          <CircularProgress />
        </Box>
      ) : error ? (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      ) : material && editedItem ? ( // Ensure material and editedItem are loaded
        <>
          {/* Material Item Details */}
          <Paper sx={{ p: 3, mb: 4 }}>
            <Box
              display="flex"
              justifyContent="space-between"
              alignItems="center"
              mb={2}
            >
              <Typography variant="h5" component="h1">
                {material.name}
              </Typography>
              {!editing ? (
                <Button
                  variant="outlined"
                  startIcon={<EditIcon />}
                  onClick={() => setEditing(true)}
                >
                  Edit
                </Button>
              ) : (
                <Box>
                  <Button
                    variant="outlined"
                    color="secondary"
                    startIcon={<CancelIcon />}
                    onClick={() => {
                      setEditing(false);
                      setEditedItem(material);
                    }}
                    sx={{ mr: 1 }}
                  >
                    Cancel
                  </Button>
                  <Button
                    variant="contained"
                    color="primary"
                    startIcon={
                      savingChanges ? (
                        <CircularProgress size={20} color="inherit" />
                      ) : (
                        <SaveIcon />
                      )
                    }
                    onClick={handleSaveChanges}
                    disabled={savingChanges}
                  >
                    {savingChanges ? "Saving..." : "Save Changes"}
                  </Button>
                </Box>
              )}
            </Box>
            <Divider sx={{ mb: 3 }} />
            <Grid container spacing={3}>
              {/* Quick Stats */}
              <Grid item xs={12} md={4}>
                <Card
                  elevation={0}
                  sx={{ bgcolor: "background.default", height: "100%" }}
                >
                  <CardContent>
                    <Typography
                      variant="subtitle2"
                      color="text.secondary"
                      gutterBottom
                    >
                      Current Stock
                    </Typography>
                    <Box display="flex" alignItems="center">
                      <Typography
                        variant="h4"
                        component="div"
                        color={
                          material.quantity < material.reorderLevel
                            ? "error.main"
                            : "inherit"
                        }
                        sx={{ mr: 1 }}
                      >
                        {material.quantity}
                      </Typography>
                      {material.quantity < material.reorderLevel && (
                        <Chip
                          icon={<WarningAmberIcon />}
                          label="Low Stock"
                          color="error"
                          size="small"
                        />
                      )}
                    </Box>
                    <Typography
                      variant="body2"
                      color="text.secondary"
                      sx={{ mt: 1 }}
                    >
                      Reorder level: {material.reorderLevel}
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
              <Grid item xs={12} md={4}>
                <Card
                  elevation={0}
                  sx={{ bgcolor: "background.default", height: "100%" }}
                >
                  <CardContent>
                    <Typography
                      variant="subtitle2"
                      color="text.secondary"
                      gutterBottom
                    >
                      Unit Price
                    </Typography>
                    <Box display="flex" alignItems="center">
                      <Typography variant="h4" component="div" sx={{ mr: 1 }}>
                        ${material.unitPrice?.toFixed(2) || "0.00"}
                      </Typography>
                      {/* Add Warning Icon if source is Platt and status is BLOCKED */}
                      {material?.source === "PLATT" &&
                        sourceStatus?.status === "BLOCKED" && (
                          <Tooltip
                            title={`Platt pricing may be outdated. Reason: ${
                              sourceStatus.reason
                            }. Last checked: ${formatDate(
                              sourceStatus.lastAttempt
                            )}`}
                          >
                            <WarningAmberIcon
                              color="warning"
                              fontSize="small"
                            />
                          </Tooltip>
                        )}
                    </Box>
                    <Typography
                      variant="body2"
                      color="text.secondary"
                      sx={{ mt: 1 }}
                    >
                      Cost Price: ${material.costPrice?.toFixed(2) || "N/A"}
                    </Typography>
                    <Typography
                      variant="body2"
                      color="text.secondary"
                      sx={{ mt: 1 }}
                    >
                      Total value (Cost): $
                      {(material.quantity * (material.costPrice || 0)).toFixed(
                        2
                      )}
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
              <Grid item xs={12} md={4}>
                <Card
                  elevation={0}
                  sx={{ bgcolor: "background.default", height: "100%" }}
                >
                  <CardContent>
                    <Typography
                      variant="subtitle2"
                      color="text.secondary"
                      gutterBottom
                    >
                      Location
                    </Typography>
                    <Box display="flex" alignItems="center">
                      <WarehouseIcon color="primary" sx={{ mr: 1 }} />
                      {/* Display location from 'editedItem' if editing, otherwise from 'material' */}
                      <Typography variant="body1">
                        {editing ? editedItem.location : material.location}
                      </Typography>
                    </Box>
                    <Typography
                      variant="body2"
                      color="text.secondary"
                      sx={{ mt: 1 }}
                    >
                      Last updated: {formatDate(material.updatedAt)}
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>

              {/* Main Details */}
              <Grid item xs={12} md={6}>
                <Grid container spacing={2}>
                  <Grid item xs={12} sm={6}>
                    <TextField
                      fullWidth
                      label="SKU"
                      name="sku"
                      value={editing ? editedItem.sku : material.sku}
                      onChange={handleChange}
                      disabled={!editing}
                      margin="normal"
                    />
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <TextField
                      fullWidth
                      label="Category"
                      name="category"
                      value={editing ? editedItem.category : material.category}
                      onChange={handleChange}
                      disabled={!editing}
                      margin="normal"
                    />
                  </Grid>
                  <Grid item xs={12}>
                    <TextField
                      fullWidth
                      label="Description"
                      name="description"
                      value={
                        editing ? editedItem.description : material.description
                      }
                      onChange={handleChange}
                      disabled={!editing}
                      multiline
                      rows={3}
                      margin="normal"
                    />
                  </Grid>
                  <Grid item xs={12} sm={4}>
                    <TextField
                      fullWidth
                      label="Quantity"
                      name="quantity"
                      type="number"
                      value={material.quantity}
                      disabled={true}
                      InputProps={{ inputProps: { min: 0 }, readOnly: true }}
                      margin="normal"
                      helperText="Update via transactions"
                    />
                  </Grid>
                  <Grid item xs={12} sm={4}>
                    <TextField
                      fullWidth
                      label="Reorder Level"
                      name="reorderLevel"
                      type="number"
                      value={
                        editing
                          ? editedItem.reorderLevel
                          : material.reorderLevel
                      }
                      onChange={handleChange}
                      disabled={!editing}
                      InputProps={{ inputProps: { min: 0 } }}
                      margin="normal"
                    />
                  </Grid>
                  <Grid item xs={12} sm={4}>
                    <TextField
                      fullWidth
                      label="Unit Price"
                      name="unitPrice"
                      type="number"
                      value={
                        editing ? editedItem.unitPrice : material.unitPrice
                      }
                      onChange={handleChange}
                      disabled={!editing}
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">$</InputAdornment>
                        ),
                        inputProps: { min: 0, step: 0.01 },
                      }}
                      margin="normal"
                    />
                  </Grid>
                  <Grid item xs={12}>
                    <TextField
                      fullWidth
                      label="Location"
                      name="location"
                      value={editing ? editedItem.location : material.location}
                      onChange={handleChange}
                      disabled={!editing}
                      margin="normal"
                    />
                  </Grid>
                </Grid>
              </Grid>
              <Grid item xs={12} md={6}>
                <Grid container spacing={2}>
                  <Grid item xs={12}>
                    <TextField
                      fullWidth
                      label="Supplier"
                      name="supplier"
                      value={editing ? editedItem.supplier : material.supplier}
                      onChange={handleChange}
                      disabled={!editing}
                      margin="normal"
                      InputProps={{
                        endAdornment: material?.source === "PLATT" &&
                          sourceStatus?.status === "BLOCKED" &&
                          !editing && (
                            <InputAdornment position="end">
                              <Tooltip
                                title={`Platt data may be outdated. Reason: ${
                                  sourceStatus.reason
                                }. Last checked: ${formatDate(
                                  sourceStatus.lastAttempt
                                )}`}
                              >
                                <WarningAmberIcon color="warning" />
                              </Tooltip>
                            </InputAdornment>
                          ),
                      }}
                    />
                  </Grid>
                  <Grid item xs={12}>
                    <TextField
                      fullWidth
                      label="Supplier Contact"
                      name="supplierContact"
                      value={
                        editing
                          ? editedItem.supplierContact
                          : material.supplierContact
                      }
                      onChange={handleChange}
                      disabled={!editing}
                      margin="normal"
                    />
                  </Grid>
                  <Grid item xs={12}>
                    <TextField
                      fullWidth
                      label="Notes"
                      name="notes"
                      value={editing ? editedItem.notes : material.notes}
                      onChange={handleChange}
                      disabled={!editing}
                      multiline
                      rows={4}
                      margin="normal"
                    />
                  </Grid>
                  {/* Display New Contractor Fields */}
                  <Grid item xs={12} sm={6}>
                    <TextField
                      fullWidth
                      label="Voltage"
                      name="voltage"
                      value={editing ? editedItem.voltage : material.voltage}
                      onChange={handleChange}
                      disabled={!editing}
                      margin="normal"
                    />
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <TextField
                      fullWidth
                      label="Amperage"
                      name="amperage"
                      value={editing ? editedItem.amperage : material.amperage}
                      onChange={handleChange}
                      disabled={!editing}
                      margin="normal"
                    />
                  </Grid>
                </Grid>
              </Grid>
            </Grid>
          </Paper>

          {/* Usage Chart */}
          <Paper sx={{ p: 3, mb: 4 }}>
            <Typography variant="h6" gutterBottom>
              Monthly Usage
            </Typography>
            <Divider sx={{ mb: 3 }} />
            {transactionsLoading ? (
              <Box display="flex" justifyContent="center" p={3}>
                <CircularProgress />
              </Box>
            ) : transactionsError ? (
              <Alert severity="error">
                Error loading usage data: {transactionsError}
              </Alert>
            ) : usageData ? (
              <Box sx={{ height: 300 }}>
                <Line options={chartOptions} data={usageData} />
              </Box>
            ) : (
              <Typography>
                No transaction data available to generate usage chart.
              </Typography>
            )}
          </Paper>

          {/* Recent Transactions */}
          <Paper sx={{ p: 3 }}>
            <Box display="flex" alignItems="center" mb={2}>
              <HistoryIcon sx={{ mr: 1 }} />
              <Typography variant="h6">Recent Transactions</Typography>
            </Box>
            <Divider sx={{ mb: 3 }} />
            <TableContainer>
              <Table sx={{ minWidth: 650 }}>
                <TableHead>
                  <TableRow>
                    <TableCell>Date</TableCell>
                    <TableCell>Type</TableCell>
                    <TableCell align="right">Quantity</TableCell>
                    <TableCell>Job / Invoice</TableCell>
                    <TableCell>User</TableCell>
                    <TableCell>Notes</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {transactionsLoading ? (
                    <TableRow>
                      <TableCell colSpan={6} align="center">
                        <CircularProgress size={24} />
                      </TableCell>
                    </TableRow>
                  ) : transactionsError ? (
                    <TableRow>
                      <TableCell colSpan={6} align="center">
                        <Alert severity="error" sx={{ width: "100%" }}>
                          Error loading transactions: {transactionsError}
                        </Alert>
                      </TableCell>
                    </TableRow>
                  ) : transactions && transactions.length > 0 ? (
                    transactions.map((transaction) => (
                      <TableRow key={transaction._id || transaction.id} hover>
                        <TableCell>{formatDate(transaction.date)}</TableCell>
                        <TableCell>
                          <Chip
                            icon={
                              transaction.type === "receive" ? (
                                <ShippingIcon />
                              ) : (
                                <InvoiceIcon />
                              )
                            }
                            label={
                              transaction.type === "receive"
                                ? "Received"
                                : "Issued"
                            }
                            color={
                              transaction.type === "receive"
                                ? "success"
                                : "primary"
                            }
                            size="small"
                          />
                        </TableCell>
                        <TableCell align="right">
                          <Typography fontWeight="medium">
                            {transaction.type === "receive" ? "+" : "-"}
                            {transaction.quantity}
                          </Typography>
                        </TableCell>
                        <TableCell>
                          {transaction.jobId ? (
                            <Link
                              component={RouterLink}
                              to={`/jobs/${transaction.jobId}`}
                            >
                              {transaction.jobId}
                            </Link> // Assuming jobId is just the ID
                          ) : transaction.invoiceId ? (
                            <Link
                              component={RouterLink}
                              to={`/invoices/${transaction.invoiceId}`}
                            >
                              {transaction.invoiceId}
                            </Link> // Assuming invoiceId exists
                          ) : (
                            "-"
                          )}
                        </TableCell>
                        <TableCell>
                          {transaction.user?.name ||
                            transaction.userId ||
                            "N/A"}
                        </TableCell>{" "}
                        {/* Display user name if populated */}
                        <TableCell>{transaction.notes}</TableCell>
                      </TableRow>
                    ))
                  ) : (
                    <TableRow>
                      <TableCell colSpan={6} align="center">
                        No transactions found for this item.
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </TableContainer>
            <Box display="flex" justifyContent="center" mt={3}>
              <Button
                variant="outlined"
                onClick={() => console.log("View all transactions")}
              >
                View All Transactions
              </Button>
            </Box>
          </Paper>
        </>
      ) : (
        <Alert severity="warning">
          Material not found. It may have been deleted or the ID is incorrect.
        </Alert>
      )}
    </Container>
  );
};

export default MaterialDetail;
