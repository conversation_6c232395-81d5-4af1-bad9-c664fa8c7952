import React from "react";
import {
  <PERSON>,
  Card,
  CardContent,
  CardActions,
  Button,
  Chip,
  Avatar,
  Typography,
  IconButton,
  Tooltip,
} from "@mui/material";
import PersonIcon from "@mui/icons-material/Person";
import EditIcon from "@mui/icons-material/Edit";
import DeleteIcon from "@mui/icons-material/Delete";
import SmartToyIcon from "@mui/icons-material/SmartToy";
import AssignmentIcon from "@mui/icons-material/Assignment";
import { getStatusColor } from "./utils/technicianHelpers";

/**
 * Component for displaying an individual technician card
 */
const TechnicianCard = ({
  technician,
  onCardClick,
  onEditClick,
  onDeleteClick,
  onAiSuggestionsClick,
  onAssignJobClick,
  userRole,
}) => {
  const handleEditClick = (event) => {
    event.stopPropagation();
    onEditClick(technician);
  };

  const handleDeleteClick = (event) => {
    event.stopPropagation();
    onDeleteClick(technician);
  };

  const handleAiSuggestionsClick = (event) => {
    event.stopPropagation();
    onAiSuggestionsClick(technician);
  };

  const handleAssignJobClick = (event) => {
    event.stopPropagation();
    onAssignJobClick(technician);
  };

  return (
    <Card
      elevation={3}
      sx={{
        cursor: "pointer",
        transition: "transform 0.2s ease-in-out",
        "&:hover": {
          transform: "scale(1.02)",
        },
      }}
    >
      <CardContent onClick={() => onCardClick(technician._id)}>
        <Box display="flex" alignItems="center" mb={2}>
          <Avatar
            src={technician.profileImage}
            alt={`${technician.firstName} ${technician.lastName}`}
            sx={{ width: 56, height: 56, mr: 2 }}
          >
            {!technician.profileImage && <PersonIcon />}
          </Avatar>
          <Box>
            <Typography variant="h6" component="div">
              {technician.firstName} {technician.lastName}
            </Typography>
            <Chip
              label={technician.contractorType || "General"}
              size="small"
              color="primary"
              variant="outlined"
            />
          </Box>
        </Box>

        <Box mb={2}>
          <Chip
            label={technician.availabilityStatus || "Available"}
            size="small"
            color={getStatusColor(technician.availabilityStatus)}
          />
        </Box>

        <Typography variant="body2" color="text.secondary" gutterBottom>
          <strong>Skills:</strong>{" "}
          {technician.skills?.length > 0
            ? technician.skills.join(", ")
            : "No skills listed"}
        </Typography>

        <Typography variant="body2" color="text.secondary">
          <strong>Hourly Rate:</strong> ${technician.hourlyRate || "N/A"}
        </Typography>
      </CardContent>
      <CardActions>
        <Button
          size="small"
          color="primary"
          onClick={() => onCardClick(technician._id)}
        >
          View Details
        </Button>
        <Button
          size="small"
          color="success"
          startIcon={<AssignmentIcon />}
          onClick={handleAssignJobClick}
        >
          Assign Job
        </Button>

        <Box sx={{ ml: "auto", display: "flex" }}>
          <Tooltip title="AI Suggestions">
            <IconButton
              size="small"
              color="secondary"
              onClick={handleAiSuggestionsClick}
            >
              <SmartToyIcon fontSize="small" />
            </IconButton>
          </Tooltip>

          {userRole === "Administrators" && (
            <>
              <Tooltip title="Edit Technician">
                <IconButton
                  size="small"
                  color="primary"
                  onClick={handleEditClick}
                >
                  <EditIcon fontSize="small" />
                </IconButton>
              </Tooltip>

              <Tooltip title="Delete Technician">
                <IconButton
                  size="small"
                  color="error"
                  onClick={handleDeleteClick}
                >
                  <DeleteIcon fontSize="small" />
                </IconButton>
              </Tooltip>
            </>
          )}
        </Box>
      </CardActions>
    </Card>
  );
};

export default TechnicianCard;
