import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import axios from "axios";

const initialState = {
  jobs: [],
  job: null,
  loading: false,
  error: null,
  success: false,
  totalPages: 1,
  currentPage: 1,
  insights: {
    nextSteps: [],
    riskFactors: [],
    similarJobs: [],
  },
  assignmentResult: null,
  riskAnalysis: null,
  similarJobs: [],
  progressPrediction: null,
  availableTechnicians: [],
  technicianLoading: false,
  jobHistory: [],
  historyLoading: false,
};

// Get all jobs with pagination and filters
export const getJobs = createAsyncThunk(
  "jobs/getJobs",
  async (
    { page = 1, limit = 10, filters = {} },
    { getState, rejectWithValue }
  ) => {
    try {
      const { auth } = getState();

      const config = {
        headers: {
          Authorization: `Bearer ${auth.userInfo.token}`,
        },
        params: {
          page,
          limit,
          ...filters,
        },
      };

      const { data } = await axios.get("/api/jobs", config);
      return data;
    } catch (error) {
      return rejectWithValue(
        error.response && error.response.data.message
          ? error.response.data.message
          : error.message
      );
    }
  }
);

// Get job by ID with AI insights
export const getJobById = createAsyncThunk(
  "jobs/getJobById",
  async (id, { getState, rejectWithValue }) => {
    try {
      const { auth } = getState();

      const config = {
        headers: {
          Authorization: `Bearer ${auth.userInfo.token}`,
        },
      };

      const { data } = await axios.get(`/api/jobs/${id}`, config);
      return data;
    } catch (error) {
      return rejectWithValue(
        error.response && error.response.data.message
          ? error.response.data.message
          : error.message
      );
    }
  }
);

// Create new job
export const createJob = createAsyncThunk(
  "jobs/createJob",
  async (jobData, { getState, rejectWithValue }) => {
    try {
      const { auth } = getState();

      const config = {
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${auth.userInfo.token}`,
        },
      };

      const { data } = await axios.post("/api/jobs", jobData, config);
      return data;
    } catch (error) {
      return rejectWithValue(
        error.response && error.response.data.message
          ? error.response.data.message
          : error.message
      );
    }
  }
);

// Update job
export const updateJob = createAsyncThunk(
  "jobs/updateJob",
  async ({ id, jobData }, { getState, rejectWithValue }) => {
    try {
      const { auth } = getState();

      const config = {
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${auth.userInfo.token}`,
        },
      };

      const { data } = await axios.put(`/api/jobs/${id}`, jobData, config);
      return data;
    } catch (error) {
      return rejectWithValue(
        error.response && error.response.data.message
          ? error.response.data.message
          : error.message
      );
    }
  }
);

// Delete job
export const deleteJob = createAsyncThunk(
  "jobs/deleteJob",
  async (id, { getState, rejectWithValue }) => {
    try {
      const { auth } = getState();

      const config = {
        headers: {
          Authorization: `Bearer ${auth.userInfo.token}`,
        },
      };

      await axios.delete(`/api/jobs/${id}`, config);
      return id;
    } catch (error) {
      return rejectWithValue(
        error.response && error.response.data.message
          ? error.response.data.message
          : error.message
      );
    }
  }
);

// Update job status
export const updateJobStatus = createAsyncThunk(
  "jobs/updateStatus",
  async ({ id, status }, { getState, rejectWithValue }) => {
    try {
      const { auth } = getState();

      const config = {
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${auth.userInfo.token}`,
        },
      };

      const { data } = await axios.patch(
        `/api/jobs/${id}/status`,
        { status },
        config
      );
      return data;
    } catch (error) {
      return rejectWithValue(
        error.response && error.response.data.message
          ? error.response.data.message
          : error.message
      );
    }
  }
);

// Add task to job
export const addJobTask = createAsyncThunk(
  "jobs/addTask",
  async ({ id, taskData }, { getState, rejectWithValue }) => {
    try {
      const { auth } = getState();

      const config = {
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${auth.userInfo.token}`,
        },
      };

      const { data } = await axios.post(
        `/api/jobs/${id}/tasks`,
        taskData,
        config
      );
      return data;
    } catch (error) {
      return rejectWithValue(
        error.response && error.response.data.message
          ? error.response.data.message
          : error.message
      );
    }
  }
);

// Update task status
export const updateTaskStatus = createAsyncThunk(
  "jobs/updateTaskStatus",
  async ({ jobId, taskId, isCompleted }, { getState, rejectWithValue }) => {
    try {
      const { auth } = getState();

      const config = {
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${auth.userInfo.token}`,
        },
      };

      const { data } = await axios.patch(
        `/api/jobs/${jobId}/tasks/${taskId}`,
        { isCompleted },
        config
      );
      return data;
    } catch (error) {
      return rejectWithValue(
        error.response && error.response.data.message
          ? error.response.data.message
          : error.message
      );
    }
  }
);

// AI-specific thunks

// Assign technicians to jobs using AI
export const assignTechnicians = createAsyncThunk(
  "jobs/assignTechnicians",
  async (assignmentData, { getState, rejectWithValue }) => {
    try {
      const { auth } = getState();

      const config = {
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${auth.userInfo.token}`,
        },
      };

      const { data } = await axios.post(
        "/api/jobs/assign",
        assignmentData,
        config
      );
      return data;
    } catch (error) {
      return rejectWithValue(
        error.response && error.response.data.message
          ? error.response.data.message
          : error.message
      );
    }
  }
);

// Analyze job risks using AI
export const analyzeJobRisks = createAsyncThunk(
  "jobs/analyzeRisks",
  async (jobData, { getState, rejectWithValue }) => {
    try {
      const { auth } = getState();

      const config = {
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${auth.userInfo.token}`,
        },
      };

      const { data } = await axios.post(
        "/api/jobs/analyze-risks",
        jobData,
        config
      );
      return data;
    } catch (error) {
      return rejectWithValue(
        error.response && error.response.data.message
          ? error.response.data.message
          : error.message
      );
    }
  }
);

// Get similar jobs using AI
export const getSimilarJobs = createAsyncThunk(
  "jobs/getSimilarJobs",
  async (jobId, { getState, rejectWithValue }) => {
    try {
      const { auth } = getState();

      const config = {
        headers: {
          Authorization: `Bearer ${auth.userInfo.token}`,
        },
      };

      const { data } = await axios.get(`/api/jobs/${jobId}/similar`, config);
      return data;
    } catch (error) {
      return rejectWithValue(
        error.response && error.response.data.message
          ? error.response.data.message
          : error.message
      );
    }
  }
);

// Predict job progress using AI
export const predictJobProgress = createAsyncThunk(
  "jobs/predictProgress",
  async (jobId, { getState, rejectWithValue }) => {
    try {
      const { auth } = getState();

      const config = {
        headers: {
          Authorization: `Bearer ${auth.userInfo.token}`,
        },
      };

      const { data } = await axios.get(
        `/api/jobs/${jobId}/predict-progress`,
        config
      );
      return data;
    } catch (error) {
      return rejectWithValue(
        error.response && error.response.data.message
          ? error.response.data.message
          : error.message
      );
    }
  }
);

// Get all available technicians with AI recommendations
export const getAvailableTechnicians = createAsyncThunk(
  "jobs/getAvailableTechnicians",
  async (_, { getState, rejectWithValue }) => {
    try {
      const { auth } = getState();

      const config = {
        headers: {
          Authorization: `Bearer ${auth.userInfo.token}`,
        },
      };

      const { data } = await axios.get("/api/users/technicians", config);
      return data;
    } catch (error) {
      return rejectWithValue(
        error.response && error.response.data.message
          ? error.response.data.message
          : error.message
      );
    }
  }
);

// Get job history
export const getJobHistory = createAsyncThunk(
  "jobs/getJobHistory",
  async (jobId, { getState, rejectWithValue }) => {
    try {
      const { auth } = getState();

      const config = {
        headers: {
          Authorization: `Bearer ${auth.userInfo.token}`,
        },
      };

      const { data } = await axios.get(`/api/jobs/${jobId}/history`, config);
      return data;
    } catch (error) {
      return rejectWithValue(
        error.response && error.response.data.message
          ? error.response.data.message
          : error.message
      );
    }
  }
);

const jobSlice = createSlice({
  name: "jobs",
  initialState,
  reducers: {
    resetJobState: (state) => {
      state.loading = false;
      state.error = null;
      state.success = false;
    },
    clearJob: (state) => {
      state.job = null;
      state.insights = {
        nextSteps: [],
        riskFactors: [],
        similarJobs: [],
      };
      state.assignmentResult = null;
      state.riskAnalysis = null;
      state.similarJobs = [];
      state.progressPrediction = null;
    },
    setJobFilters: (state, action) => {
      state.filters = action.payload;
    },
    sortJobsByPriority: (state) => {
      state.jobs.sort(
        (a, b) => b.aiInsights.priorityScore - a.aiInsights.priorityScore
      );
    },
    filterHighRiskJobs: (state) => {
      state.jobs = state.jobs.filter(
        (job) =>
          job.riskAssessment && job.riskAssessment.overallRiskLevel === "High"
      );
    },
  },
  extraReducers: (builder) => {
    builder
      // Get jobs
      .addCase(getJobs.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getJobs.fulfilled, (state, action) => {
        state.loading = false;
        state.jobs = action.payload.jobs;
        state.totalPages = action.payload.pages;
        state.currentPage = action.payload.page;
      })
      .addCase(getJobs.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      // Get job by ID
      .addCase(getJobById.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getJobById.fulfilled, (state, action) => {
        state.loading = false;
        state.job = action.payload;
        // Update insights from AI data if available
        if (action.payload.aiInsights) {
          state.insights = {
            nextSteps: action.payload.aiInsights.nextSteps || [],
            riskFactors: action.payload.riskAssessment?.riskFactors || [],
            similarJobs: [],
          };
        }
      })
      .addCase(getJobById.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      // Create job
      .addCase(createJob.pending, (state) => {
        state.loading = true;
        state.error = null;
        state.success = false;
      })
      .addCase(createJob.fulfilled, (state, action) => {
        state.loading = false;
        state.success = true;
        state.jobs.push(action.payload);
      })
      .addCase(createJob.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      // Update job
      .addCase(updateJob.pending, (state) => {
        state.loading = true;
        state.error = null;
        state.success = false;
      })
      .addCase(updateJob.fulfilled, (state, action) => {
        state.loading = false;
        state.success = true;
        state.job = action.payload;
        state.jobs = state.jobs.map((job) =>
          job._id === action.payload._id ? action.payload : job
        );
      })
      .addCase(updateJob.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      // Delete job
      .addCase(deleteJob.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(deleteJob.fulfilled, (state, action) => {
        state.loading = false;
        state.success = true;
        state.jobs = state.jobs.filter((job) => job._id !== action.payload);
      })
      .addCase(deleteJob.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      // Update job status
      .addCase(updateJobStatus.fulfilled, (state, action) => {
        state.job = action.payload;
        state.jobs = state.jobs.map((job) =>
          job._id === action.payload._id ? action.payload : job
        );
      })
      // Add task
      .addCase(addJobTask.fulfilled, (state, action) => {
        state.job = action.payload;
        state.jobs = state.jobs.map((job) =>
          job._id === action.payload._id ? action.payload : job
        );
      })
      // Update task status
      .addCase(updateTaskStatus.fulfilled, (state, action) => {
        state.job = action.payload;
        state.jobs = state.jobs.map((job) =>
          job._id === action.payload._id ? action.payload : job
        );
      })
      // AI-specific reducers
      // Assign technicians
      .addCase(assignTechnicians.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(assignTechnicians.fulfilled, (state, action) => {
        state.loading = false;
        state.assignmentResult = action.payload;
        // Update affected jobs in the jobs array
        if (action.payload.jobs) {
          action.payload.jobs.forEach((updatedJob) => {
            state.jobs = state.jobs.map((job) =>
              job._id === updatedJob._id ? updatedJob : job
            );
          });
        }
      })
      .addCase(assignTechnicians.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      // Analyze job risks
      .addCase(analyzeJobRisks.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(analyzeJobRisks.fulfilled, (state, action) => {
        state.loading = false;
        state.riskAnalysis = action.payload;
        // Update job if in state
        if (state.job && state.job._id === action.payload.job._id) {
          state.job = action.payload.job;
        }
        // Update jobs array if the job exists there
        state.jobs = state.jobs.map((job) =>
          job._id === action.payload.job._id ? action.payload.job : job
        );
      })
      .addCase(analyzeJobRisks.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      // Get similar jobs
      .addCase(getSimilarJobs.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getSimilarJobs.fulfilled, (state, action) => {
        state.loading = false;
        state.similarJobs = action.payload.similarJobs;
      })
      .addCase(getSimilarJobs.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      // Predict job progress
      .addCase(predictJobProgress.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(predictJobProgress.fulfilled, (state, action) => {
        state.loading = false;
        state.progressPrediction = action.payload;
        // Update job if in state
        if (state.job && state.job._id === action.payload.job._id) {
          state.job = action.payload.job;
        }
        // Update jobs array if the job exists there
        state.jobs = state.jobs.map((job) =>
          job._id === action.payload.job._id ? action.payload.job : job
        );
      })
      .addCase(predictJobProgress.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      // getAvailableTechnicians
      .addCase(getAvailableTechnicians.pending, (state) => {
        state.technicianLoading = true;
      })
      .addCase(getAvailableTechnicians.fulfilled, (state, action) => {
        state.technicianLoading = false;
        state.availableTechnicians = action.payload;
      })
      .addCase(getAvailableTechnicians.rejected, (state, action) => {
        state.technicianLoading = false;
        state.error = action.payload;
      })
      // getJobHistory
      .addCase(getJobHistory.pending, (state) => {
        state.historyLoading = true;
      })
      .addCase(getJobHistory.fulfilled, (state, action) => {
        state.historyLoading = false;
        state.jobHistory = action.payload;
      })
      .addCase(getJobHistory.rejected, (state, action) => {
        state.historyLoading = false;
        state.error = action.payload;
      });
  },
});

export const {
  resetJobState,
  clearJob,
  setJobFilters,
  sortJobsByPriority,
  filterHighRiskJobs,
} = jobSlice.actions;

export default jobSlice.reducer;
