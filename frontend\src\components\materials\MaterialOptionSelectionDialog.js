import React, { useState, useEffect, useCallback } from "react";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  CircularProgress,
  Typography,
  Box,
  Alert,
  Tooltip,
  IconButton,
  TextField,
  InputAdornment,
} from "@mui/material";
import {
  Search as SearchIcon,
  Refresh as RefreshIcon,
} from "@mui/icons-material";
import { useSnackbar } from "notistack";
import { useSelector } from "react-redux";
import {
  getMaterialOptions,
  initiateAiMaterialScraping,
  selectMaterialOption,
} from "../../services/materialOptionService";
import MaterialOptionsDisplay from "./MaterialOptionsDisplay";

/**
 * Dialog component for material option selection
 * Used when an item has status "pending_user_selection" to let users choose from scraped options
 */
const MaterialOptionSelectionDialog = ({
  open,
  onClose,
  quoteId,
  itemId,
  itemName,
  onMaterialSelected,
}) => {
  const [loading, setLoading] = useState(false);
  const [scraping, setScraping] = useState(false);
  const [error, setError] = useState(null);
  const [options, setOptions] = useState([]);
  const [lookupStatus, setLookupStatus] = useState("not_initiated");
  const [searchQuery, setSearchQuery] = useState("");
  const [lookupQuerySuggestion, setLookupQuerySuggestion] = useState("");
  const { enqueueSnackbar } = useSnackbar();
  const { userInfo } = useSelector((state) => state.auth);

  // Function to get the authentication token - using useCallback to avoid recreating on every render
  const getToken = useCallback(() => userInfo?.token, [userInfo]);

  // Fetch material options from API - using useCallback to avoid recreating on every render
  const fetchMaterialOptions = useCallback(async () => {
    if (!quoteId || !itemId) return;

    setLoading(true);
    setError(null);

    try {
      const response = await getMaterialOptions(quoteId, itemId, getToken);
      setOptions(response.material_options || []);
      setLookupStatus(response.lookup_status || "not_initiated");
      setLookupQuerySuggestion(response.lookup_query_suggestion || "");

      // If there's a suggested query and no search query set yet, use it
      if (response.lookup_query_suggestion && !searchQuery) {
        setSearchQuery(response.lookup_query_suggestion);
      }
    } catch (error) {
      console.error("Error fetching material options:", error);
      setError(error.message || "Failed to fetch material options");
      enqueueSnackbar("Error fetching material options", { variant: "error" });
    } finally {
      setLoading(false);
    }
  }, [quoteId, itemId, getToken, enqueueSnackbar, searchQuery]);

  // Fetch material options when dialog opens
  useEffect(() => {
    if (open && quoteId && itemId) {
      fetchMaterialOptions();
    }
  }, [open, quoteId, itemId, fetchMaterialOptions]);

  // Initiate material scraping with current search query
  const handleInitiateScraping = async () => {
    if (!quoteId || !itemId || !searchQuery.trim()) {
      enqueueSnackbar("Please enter a search query", { variant: "warning" });
      return;
    }

    setScraping(true);
    setError(null);

    try {
      await initiateAiMaterialScraping(
        quoteId,
        itemId,
        {
          searchQuery: searchQuery.trim(),
          toolId: "mcp4_brave_web_search", // Default to Brave search
          limit: 10,
        },
        getToken
      );

      enqueueSnackbar("Material search initiated successfully", {
        variant: "success",
      });

      // Poll for results (this is a simple approach - could be improved with WebSockets)
      const pollInterval = setInterval(async () => {
        try {
          const response = await getMaterialOptions(quoteId, itemId, getToken);
          setOptions(response.material_options || []);
          setLookupStatus(response.lookup_status || "not_initiated");

          // If we have options or the status is no longer "searching", stop polling
          if (
            (response.material_options &&
              response.material_options.length > 0) ||
            (response.lookup_status && response.lookup_status !== "searching")
          ) {
            clearInterval(pollInterval);
            setScraping(false);
          }
        } catch (error) {
          console.error("Error polling for material options:", error);
          clearInterval(pollInterval);
          setScraping(false);
        }
      }, 3000); // Poll every 3 seconds

      // Safety cleanup - stop polling after 30 seconds no matter what
      setTimeout(() => {
        clearInterval(pollInterval);
        setScraping(false);
      }, 30000);
    } catch (error) {
      console.error("Error initiating material scraping:", error);
      setError(error.message || "Failed to initiate material scraping");
      enqueueSnackbar("Error initiating material search", { variant: "error" });
      setScraping(false);
    }
  };

  // Select a material option
  const handleSelectOption = async (optionIndex) => {
    if (
      !quoteId ||
      !itemId ||
      optionIndex < 0 ||
      optionIndex >= options.length
    ) {
      enqueueSnackbar("Invalid option selection", { variant: "error" });
      return;
    }

    setLoading(true);

    try {
      await selectMaterialOption(quoteId, itemId, optionIndex, getToken);
      enqueueSnackbar("Material option selected successfully", {
        variant: "success",
      });

      // If a callback was provided, call it
      if (onMaterialSelected) {
        onMaterialSelected(options[optionIndex]);
      }

      // Close the dialog
      onClose();
    } catch (error) {
      console.error("Error selecting material option:", error);
      setError(error.message || "Failed to select material option");
      enqueueSnackbar("Error selecting material option", { variant: "error" });
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog
      open={open}
      onClose={loading || scraping ? undefined : onClose}
      maxWidth="md"
      fullWidth
      PaperProps={{
        sx: { maxHeight: "80vh" },
      }}
    >
      <DialogTitle>
        Material Options{itemName ? ` for "${itemName}"` : ""}
      </DialogTitle>

      <DialogContent dividers>
        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}

        <Box sx={{ mb: 3 }}>
          <TextField
            label="Search Query"
            fullWidth
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            disabled={loading || scraping}
            placeholder="Enter material description or part number"
            InputProps={{
              endAdornment: (
                <InputAdornment position="end">
                  <Tooltip title="Search for materials">
                    <IconButton
                      onClick={handleInitiateScraping}
                      disabled={loading || scraping || !searchQuery.trim()}
                    >
                      {scraping ? (
                        <CircularProgress size={24} />
                      ) : (
                        <SearchIcon />
                      )}
                    </IconButton>
                  </Tooltip>
                </InputAdornment>
              ),
            }}
            onKeyPress={(e) => {
              if (
                e.key === "Enter" &&
                !loading &&
                !scraping &&
                searchQuery.trim()
              ) {
                handleInitiateScraping();
              }
            }}
          />

          {lookupQuerySuggestion && !searchQuery && (
            <Box sx={{ mt: 1 }}>
              <Typography variant="caption" color="text.secondary">
                Suggested search:
                <Button
                  size="small"
                  sx={{ ml: 1 }}
                  onClick={() => setSearchQuery(lookupQuerySuggestion)}
                >
                  {lookupQuerySuggestion}
                </Button>
              </Typography>
            </Box>
          )}
        </Box>

        {/* Show different UI based on lookup status */}
        {lookupStatus === "searching" ? (
          <Box sx={{ textAlign: "center", py: 4 }}>
            <CircularProgress size={40} />
            <Typography sx={{ mt: 2 }}>Searching for materials...</Typography>
          </Box>
        ) : lookupStatus === "failed" ? (
          <Alert severity="error" sx={{ mb: 3 }}>
            Material search failed. Please try again with a different query.
          </Alert>
        ) : (
          <MaterialOptionsDisplay
            options={options}
            onSelect={handleSelectOption}
            isLoading={loading || scraping}
            itemId={itemId}
            itemName={itemName}
          />
        )}

        {!loading && !scraping && options.length === 0 && (
          <Box sx={{ textAlign: "center", py: 4 }}>
            <Typography color="text.secondary">
              No material options found. Try a different search query.
            </Typography>
            <Button
              startIcon={<RefreshIcon />}
              onClick={fetchMaterialOptions}
              sx={{ mt: 2 }}
            >
              Refresh
            </Button>
          </Box>
        )}
      </DialogContent>

      <DialogActions>
        <Button onClick={onClose} disabled={loading || scraping}>
          Cancel
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default MaterialOptionSelectionDialog;
