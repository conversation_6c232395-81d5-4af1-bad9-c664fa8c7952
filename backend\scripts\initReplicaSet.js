require('dotenv').config();
const mongoose = require('mongoose');
const { setTimeout } = require('timers/promises');

async function initReplicaSet() {
  console.log('Initializing MongoDB replica set...');
  
  try {
    // Connect to MongoDB without replica set requirements first
    // Use environment variable or fallback to container hostname
    const tempUri = process.env.MONGODB_URI || 'mongodb://workiz-mongo-rs:27017/workiz_clone';
    
    console.log('Connecting to MongoDB for replica set initialization...');
    await mongoose.connect(tempUri, {
      serverSelectionTimeoutMS: 10000,
      connectTimeoutMS: 10000
    });
    
    console.log('Connected to MongoDB, checking replica set status...');
    
    const adminDb = mongoose.connection.db.admin();
    
    try {
      // Check if replica set is already initialized
      const status = await adminDb.command({ replSetGetStatus: 1 });
      console.log('Replica set already initialized:', status.set);
      console.log('Primary member:', status.members.find(m => m.stateStr === 'PRIMARY')?.name);
      await mongoose.connection.close();
      return { alreadyInitialized: true, status };
    } catch (statusError) {
      if (statusError.message.includes('no replset config') || 
          statusError.message.includes('NotYetInitialized')) {
        
        console.log('Replica set not initialized, proceeding with initialization...');
        
        // Initialize the replica set
        const initConfig = {
          _id: 'rs0',
          members: [
            { _id: 0, host: 'workiz-mongo-rs:27017' }
          ]
        };
        
        const result = await adminDb.command({ replSetInitiate: initConfig });
        console.log('Replica set initialization started:', result);
        
        // Wait for replica set to become ready
        console.log('Waiting for replica set to become ready...');
        await setTimeout(5000);
        
        // Check the status after initialization
        const newStatus = await adminDb.command({ replSetGetStatus: 1 });
        console.log('Replica set status after initialization:');
        console.log('- Set:', newStatus.set);
        console.log('- Members:', newStatus.members.length);
        console.log('- Primary:', newStatus.members.find(m => m.stateStr === 'PRIMARY')?.name);
        
        await mongoose.connection.close();
        return { initialized: true, status: newStatus };
        
      } else {
        console.error('Error checking replica set status:', statusError.message);
        throw statusError;
      }
    }
    
  } catch (error) {
    console.error('Error initializing replica set:', error.message);
    
    // Try to close connection if it exists
    if (mongoose.connection.readyState !== 0) {
      await mongoose.connection.close().catch(() => {});
    }
    
    throw error;
  }
}

// Run if this script is executed directly
if (require.main === module) {
  initReplicaSet()
    .then(result => {
      console.log('Replica set initialization completed successfully');
      process.exit(0);
    })
    .catch(error => {
      console.error('Replica set initialization failed:', error.message);
      process.exit(1);
    });
}

module.exports = { initReplicaSet };