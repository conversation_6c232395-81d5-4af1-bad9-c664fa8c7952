const mongoose = require("mongoose");

const calendarEventSchema = new mongoose.Schema(
  {
    title: {
      type: String,
      required: true,
      trim: true,
    },
    description: {
      type: String,
      trim: true,
    },
    startTime: {
      type: Date,
      required: true,
    },
    endTime: {
      type: Date,
      required: true,
    },
    allDay: {
      type: Boolean,
      default: false,
    },
    location: {
      type: String,
      trim: true,
    },
    type: {
      type: String,
      enum: ["job", "meeting", "reminder", "other"],
      default: "job",
    },
    relatedTo: {
      model: {
        type: String,
        enum: ["Job", "Customer", "User", "Other"],
      },
      id: {
        type: mongoose.Schema.Types.ObjectId,
        refPath: "relatedTo.model",
      },
    },
    assignedTo: [
      {
        type: mongoose.Schema.Types.ObjectId,
        ref: "User",
      },
    ],
    createdBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
      required: true,
    },
    color: {
      type: String,
      default: "#3788d8",
    },
    isRecurring: {
      type: Boolean,
      default: false,
    },
    recurrencePattern: {
      frequency: {
        type: String,
        enum: ["daily", "weekly", "monthly", "yearly"],
      },
      interval: {
        type: Number,
        min: 1,
        default: 1,
      },
      endDate: Date,
    },
    notifications: [
      {
        time: {
          type: Number, // minutes before event
          required: true,
        },
        type: {
          type: String,
          enum: ["email", "sms", "push"],
          required: true,
        },
      },
    ],
  },
  {
    timestamps: true,
  }
);

// Virtual for duration in minutes
calendarEventSchema.virtual("durationMinutes").get(function () {
  return Math.round((this.endTime - this.startTime) / (1000 * 60));
});

// Method to check if event overlaps with another event
calendarEventSchema.methods.overlaps = function (otherEvent) {
  return (
    (this.startTime < otherEvent.endTime &&
      this.endTime > otherEvent.startTime) ||
    (otherEvent.startTime < this.endTime && otherEvent.endTime > this.startTime)
  );
};

// Method to check if a user is available during this event time
calendarEventSchema.statics.isUserAvailable = async function (
  userId,
  startTime,
  endTime,
  excludeEventId = null
) {
  const query = {
    assignedTo: userId,
    $or: [
      { startTime: { $lt: endTime }, endTime: { $gt: startTime } },
      { startTime: { $gte: startTime, $lt: endTime } },
      { endTime: { $gt: startTime, $lte: endTime } },
    ],
  };

  if (excludeEventId) {
    query._id = { $ne: excludeEventId };
  }

  const conflictingEvents = await this.countDocuments(query);
  return conflictingEvents === 0;
};

// Static method to get events for a specific date range
calendarEventSchema.statics.getEventsInRange = function (
  startDate,
  endDate,
  userId = null
) {
  const query = {
    startTime: { $lt: endDate },
    endTime: { $gt: startDate },
  };

  if (userId) {
    query.assignedTo = userId;
  }

  return this.find(query)
    .populate("assignedTo", "firstName lastName")
    .populate("createdBy", "firstName lastName")
    .sort("startTime");
};

// Static method to get upcoming events for a user
calendarEventSchema.statics.getUpcomingEvents = function (userId, limit = 5) {
  const now = new Date();

  return this.find({
    assignedTo: userId,
    startTime: { $gt: now },
  })
    .limit(limit)
    .sort("startTime")
    .populate("relatedTo.id");
};

// Database indexes for performance optimization
calendarEventSchema.index({ startTime: 1, endTime: 1 }); // Time range queries
calendarEventSchema.index({ assignedTo: 1, startTime: 1 }); // User calendar views
calendarEventSchema.index({ type: 1, startTime: 1 }); // Event type filtering
calendarEventSchema.index({ createdBy: 1, createdAt: -1 }); // User-created events
calendarEventSchema.index({ "relatedTo.model": 1, "relatedTo.id": 1 }); // Related entity lookup
calendarEventSchema.index({ isRecurring: 1 }); // Recurring events
calendarEventSchema.index(
  { startTime: 1 },
  { partialFilterExpression: { startTime: { $gt: new Date() } } }
); // Upcoming events
calendarEventSchema.index({ assignedTo: 1, startTime: 1, endTime: 1 }); // Availability checks

const CalendarEvent = mongoose.model("CalendarEvent", calendarEventSchema);

module.exports = CalendarEvent;
