    "unit": "feet",
    "attributes": {
      "material": "Aluminum",
      "size": "400 kcmil",
      "type": "XHHW-2"
    },
    "lookup_query_suggestion": "price per foot 400 kcmil aluminum XHHW-2 wire"
  },
  {
    "description": "Rigid Metal Conduit (RMC) for service entrance riser.",
    "category": "electrical_material",
    "quantity": 20,
    "unit": "feet",
    "attributes": {
      "material": "Galvanized Steel",
      "conduit_type": "RMC",
      "conduit_size": "3 inch"
    },
    "lookup_query_suggestion": "price 10ft 3 inch rigid metal conduit"
  },
  {
    "description": "Service Entrance Weatherhead.",
    "category": "electrical_material",
    "quantity": 1,
    "unit": "pcs",
    "attributes": {
      "material": "Metal",
      "conduit_size": "3 inch"
    },
    "lookup_query_suggestion": "price 3 inch service entrance weatherhead"
  },
  {
    "description": "Bare Solid Copper Grounding Electrode Conductor.",
    "category": "electrical_material",
    "quantity": 50,
    "unit": "feet",
    "attributes": {
      "material": "Copper",
      "size": "4 AWG",
      "type": "Bare Solid"
    },
    "lookup_query_suggestion": "price 50ft 4 AWG bare copper ground wire"
  },
  {
    "description": "Copper-Clad Ground Rods with Acorn Clamps.",
    "category": "electrical_material",
    "quantity": 2,
    "unit": "pcs",
    "attributes": {
      "material": "Copper-Clad Steel",
      "size": "5/8 inch x 8 feet"
    },
    "lookup_query_suggestion": "price 5/8 inch 8ft ground rod"
  },
  {
    "description": "Miscellaneous conduit fittings, straps, connectors, hubs, and hardware.",
    "category": "electrical_material",
    "quantity": 1,
    "unit": "lot",
    "attributes": {},
    "lookup_query_suggestion": "assorted 3 inch RMC fittings cost"
  },
  {
    "description": "Pressure-Treated Plywood Sheet for mounting backboard.",
    "category": "non_electrical_material",
    "quantity": 1,
    "unit": "pcs",
    "attributes": {
      "size": "4x8 feet",
      "thickness": "3/4 inch"
    },
    "lookup_query_suggestion": "price 4x8 3/4 inch pressure treated plywood"
  },
  {
    "description": "Exterior grade paint and primer for weather protection of backboard.",
    "category": "non_electrical_material",
    "quantity": 1,
    "unit": "gallon",
    "attributes": {},
    "lookup_query_suggestion": "price 1 gallon exterior paint"
  },
  {
    "description": "Weatherproof Silicone Sealant for sealing penetrations and enclosures.",
    "category": "non_electrical_material",
    "quantity": 2,
    "unit": "tubes",
    "attributes": {},
    "lookup_query_suggestion": "price tube of outdoor silicone sealant"
  }
] | {"service":"workiz-api"}
2025-08-27 20:20:04 [info] [general]: [AI Controller] Item "Electrical Permit for main service upgrade. Includes application, processing, and inspection fees.": AI Category: administrative, Suggestion: "local electrical permit fee for 600A service upgrade" | {"service":"workiz-api"}
2025-08-27 20:20:04 [debug] [general]: [AI Controller] FULL ITEM DETAILS for "Electrical Permit for main service upgrade. Includes application, processing, and inspection fees.": {
  "description": "Electrical Permit for main service upgrade. Includes application, processing, and inspection fees.",
  "category": "administrative",
  "quantity": 1,
  "unit": "pcs",
  "attributes": {},
  "lookup_query_suggestion": "local electrical permit fee for 600A service upgrade",
  "lookup_results": []
} | {"service":"workiz-api"}
2025-08-27 20:20:04 [warn] [general]: [AI Controller] ❌ UNEXPECTED category "administrative" for item: "Electrical Permit for main service upgrade. Includes application, processing, and inspection fees." | {"service":"workiz-api"}
2025-08-27 20:20:04 [info] [general]: [AI Controller] Item "Electrical Permit for main service upgrade. Includes application, processing, and inspection fees." will SKIP price lookup. Category: administrative | {"service":"workiz-api"}
2025-08-27 20:20:04 [info] [general]: [AI Controller] Item "Electrical Permit for main service upgrade. Includes application, processing, and inspection fees." categorized as ADMINISTRATIVE by AI - including in output without price lookup | {"service":"workiz-api"}
2025-08-27 20:20:04 [info] [general]: [AI Controller] Item "Utility company coordination for service disconnection and reconnection. Includes scheduling and project liaison.": AI Category: administrative, Suggestion: "" | {"service":"workiz-api"}
2025-08-27 20:20:04 [debug] [general]: [AI Controller] FULL ITEM DETAILS for "Utility company coordination for service disconnection and reconnection. Includes scheduling and project liaison.": {
  "description": "Utility company coordination for service disconnection and reconnection. Includes scheduling and project liaison.",
  "category": "administrative",
  "quantity": 1,
  "unit": "lot",
  "attributes": {},
  "lookup_query_suggestion": "",
  "lookup_results": []
} | {"service":"workiz-api"}
2025-08-27 20:20:04 [warn] [general]: [AI Controller] ❌ UNEXPECTED category "administrative" for item: "Utility company coordination for service disconnection and reconnection. Includes scheduling and project liaison." | {"service":"workiz-api"}
2025-08-27 20:20:04 [info] [general]: [AI Controller] Item "Utility company coordination for service disconnection and reconnection. Includes scheduling and project liaison." will SKIP price lookup. Category: administrative | {"service":"workiz-api"}
2025-08-27 20:20:04 [info] [general]: [AI Controller] Item "Utility company coordination for service disconnection and reconnection. Includes scheduling and project liaison." categorized as ADMINISTRATIVE by AI - including in output without price lookup | {"service":"workiz-api"}
2025-08-27 20:20:04 [info] [general]: [AI Controller] Item "Demolition and removal of existing multi-gang meter bank, main disconnects, service riser, and associated weathered backboard.": AI Category: labor, Suggestion: "" | {"service":"workiz-api"}
2025-08-27 20:20:04 [debug] [general]: [AI Controller] FULL ITEM DETAILS for "Demolition and removal of existing multi-gang meter bank, main disconnects, service riser, and associated weathered backboard.": {
  "description": "Demolition and removal of existing multi-gang meter bank, main disconnects, service riser, and associated weathered backboard.",
  "category": "labor",
  "quantity": 8,
  "unit": "hours",
  "attributes": {},
  "lookup_query_suggestion": "",
  "lookup_results": []
} | {"service":"workiz-api"}
2025-08-27 20:20:04 [info] [general]: [AI Controller] ⚠️  Categorized as LABOR: "Demolition and removal of existing multi-gang meter bank, main disconnects, service riser, and associated weathered backboard." | {"service":"workiz-api"}
2025-08-27 20:20:04 [info] [general]: [AI Controller] Item "Demolition and removal of existing multi-gang meter bank, main disconnects, service riser, and associated weathered backboard." will SKIP price lookup. Category: labor | {"service":"workiz-api"}
2025-08-27 20:20:04 [info] [general]: [AI Controller] Item "Demolition and removal of existing multi-gang meter bank, main disconnects, service riser, and associated weathered backboard." categorized as LABOR by AI - skipping price lookup | {"service":"workiz-api"}
2025-08-27 20:20:04 [info] [general]: [AI Controller] Item "Installation of new pressure-treated plywood backboard for mounting new service equipment.": AI Category: labor, Suggestion: "" | {"service":"workiz-api"}
2025-08-27 20:20:04 [debug] [general]: [AI Controller] FULL ITEM DETAILS for "Installation of new pressure-treated plywood backboard for mounting new service equipment.": {
  "description": "Installation of new pressure-treated plywood backboard for mounting new service equipment.",
  "category": "labor",
  "quantity": 3,
  "unit": "hours",
  "attributes": {},
  "lookup_query_suggestion": "",
  "lookup_results": []
} | {"service":"workiz-api"}
2025-08-27 20:20:04 [info] [general]: [AI Controller] ⚠️  Categorized as LABOR: "Installation of new pressure-treated plywood backboard for mounting new service equipment." | {"service":"workiz-api"}
2025-08-27 20:20:04 [info] [general]: [AI Controller] Item "Installation of new pressure-treated plywood backboard for mounting new service equipment." will SKIP price lookup. Category: labor | {"service":"workiz-api"}
2025-08-27 20:20:04 [info] [general]: [AI Controller] Item "Installation of new pressure-treated plywood backboard for mounting new service equipment." categorized as LABOR by AI - skipping price lookup | {"service":"workiz-api"}
2025-08-27 20:20:04 [info] [general]: [AI Controller] Item "Installation of new 8-gang meter center, service entrance conduit, weatherhead, and conductors.": AI Category: labor, Suggestion: "" | {"service":"workiz-api"}
2025-08-27 20:20:04 [debug] [general]: [AI Controller] FULL ITEM DETAILS for "Installation of new 8-gang meter center, service entrance conduit, weatherhead, and conductors.": {
  "description": "Installation of new 8-gang meter center, service entrance conduit, weatherhead, and conductors.",
  "category": "labor",
  "quantity": 16,
  "unit": "hours",
  "attributes": {},
  "lookup_query_suggestion": "",
  "lookup_results": []
} | {"service":"workiz-api"}
2025-08-27 20:20:04 [info] [general]: [AI Controller] ⚠️  Categorized as LABOR: "Installation of new 8-gang meter center, service entrance conduit, weatherhead, and conductors." | {"service":"workiz-api"}
2025-08-27 20:20:04 [info] [general]: [AI Controller] Item "Installation of new 8-gang meter center, service entrance conduit, weatherhead, and conductors." will SKIP price lookup. Category: labor | {"service":"workiz-api"}
2025-08-27 20:20:04 [info] [general]: [AI Controller] Item "Installation of new 8-gang meter center, service entrance conduit, weatherhead, and conductors." categorized as LABOR by AI - skipping price lookup | {"service":"workiz-api"}
2025-08-27 20:20:04 [info] [general]: [AI Controller] Item "Installation of new feeders from meter center to each of the 8 unit subpanel locations.": AI Category: labor, Suggestion: "" | {"service":"workiz-api"}
2025-08-27 20:20:04 [debug] [general]: [AI Controller] FULL ITEM DETAILS for "Installation of new feeders from meter center to each of the 8 unit subpanel locations.": {
  "description": "Installation of new feeders from meter center to each of the 8 unit subpanel locations.",
  "category": "labor",
  "quantity": 12,
  "unit": "hours",
  "attributes": {},
  "lookup_query_suggestion": "",
  "lookup_results": []
} | {"service":"workiz-api"}
2025-08-27 20:20:04 [info] [general]: [AI Controller] ⚠️  Categorized as LABOR: "Installation of new feeders from meter center to each of the 8 unit subpanel locations." | {"service":"workiz-api"}
2025-08-27 20:20:04 [info] [general]: [AI Controller] Item "Installation of new feeders from meter center to each of the 8 unit subpanel locations." will SKIP price lookup. Category: labor | {"service":"workiz-api"}
2025-08-27 20:20:04 [info] [general]: [AI Controller] Item "Installation of new feeders from meter center to each of the 8 unit subpanel locations." categorized as LABOR by AI - skipping price lookup | {"service":"workiz-api"}
2025-08-27 20:20:04 [info] [general]: [AI Controller] Item "Installation of new grounding electrode system, including ground rods and bonding.": AI Category: labor, Suggestion: "" | {"service":"workiz-api"}
2025-08-27 20:20:04 [debug] [general]: [AI Controller] FULL ITEM DETAILS for "Installation of new grounding electrode system, including ground rods and bonding.": {
  "description": "Installation of new grounding electrode system, including ground rods and bonding.",
  "category": "labor",
  "quantity": 4,
  "unit": "hours",
  "attributes": {},
  "lookup_query_suggestion": "",
  "lookup_results": []
} | {"service":"workiz-api"}
2025-08-27 20:20:04 [info] [general]: [AI Controller] ⚠️  Categorized as LABOR: "Installation of new grounding electrode system, including ground rods and bonding." | {"service":"workiz-api"}
2025-08-27 20:20:04 [info] [general]: [AI Controller] Item "Installation of new grounding electrode system, including ground rods and bonding." will SKIP price lookup. Category: labor | {"service":"workiz-api"}
2025-08-27 20:20:04 [info] [general]: [AI Controller] Item "Installation of new grounding electrode system, including ground rods and bonding." categorized as LABOR by AI - skipping price lookup | {"service":"workiz-api"}
2025-08-27 20:20:04 [info] [general]: [AI Controller] Item "Final termination, testing, and commissioning of the new electrical service.": AI Category: labor, Suggestion: "" | {"service":"workiz-api"}
2025-08-27 20:20:04 [debug] [general]: [AI Controller] FULL ITEM DETAILS for "Final termination, testing, and commissioning of the new electrical service.": {
  "description": "Final termination, testing, and commissioning of the new electrical service.",
  "category": "labor",
  "quantity": 4,
  "unit": "hours",
  "attributes": {},
  "lookup_query_suggestion": "",
  "lookup_results": []
} | {"service":"workiz-api"}
2025-08-27 20:20:04 [info] [general]: [AI Controller] ⚠️  Categorized as LABOR: "Final termination, testing, and commissioning of the new electrical service." | {"service":"workiz-api"}
2025-08-27 20:20:04 [info] [general]: [AI Controller] Item "Final termination, testing, and commissioning of the new electrical service." will SKIP price lookup. Category: labor | {"service":"workiz-api"}
2025-08-27 20:20:04 [info] [general]: [AI Controller] Item "Final termination, testing, and commissioning of the new electrical service." categorized as LABOR by AI - skipping price lookup | {"service":"workiz-api"}
2025-08-27 20:20:04 [info] [general]: [AI Controller] Item "Siemens or Square D 8-Gang Meter Center, Ringless, 600A Main Bus, 125A per position, NEMA 3R Outdoor enclosure.": AI Category: electrical_material, Suggestion: "price Siemens W8M6125 8 gang 600A meter center" | {"service":"workiz-api"}
2025-08-27 20:20:04 [debug] [general]: [AI Controller] FULL ITEM DETAILS for "Siemens or Square D 8-Gang Meter Center, Ringless, 600A Main Bus, 125A per position, NEMA 3R Outdoor enclosure.": {
  "description": "Siemens or Square D 8-Gang Meter Center, Ringless, 600A Main Bus, 125A per position, NEMA 3R Outdoor enclosure.",
  "category": "electrical_material",
  "quantity": 1,
  "unit": "pcs",
  "attributes": {
    "ampacity": "600A",
    "type": "Meter Center, 8-Gang",
    "voltage": "120/240V"
  },
  "lookup_query_suggestion": "price Siemens W8M6125 8 gang 600A meter center",
  "lookup_results": []
} | {"service":"workiz-api"}
2025-08-27 20:20:04 [info] [general]: [AI Controller] ✅ CORRECTLY categorized as ELECTRICAL MATERIAL: "Siemens or Square D 8-Gang Meter Center, Ringless, 600A Main Bus, 125A per position, NEMA 3R Outdoor enclosure." | {"service":"workiz-api"}
2025-08-27 20:20:04 [debug] [general]: [parserUtils] determineLookupTypeForItem: Processing item - Description: "siemens or square d 8-gang meter center, ringless, 600a main bus, 125a per position, nema 3r outdoor enclosure.", Attributes: {"ampacity":"600A","type":"Meter Center, 8-Gang","voltage":"120/240V"}, Suggestion: "price Siemens W8M6125 8 gang 600A meter center" | {"service":"workiz-api"}
2025-08-27 20:20:04 [debug] [general]: [parserUtils] determineLookupTypeForItem: Classified as PRICE_MATERIAL_COMPONENT. | {"service":"workiz-api"}
2025-08-27 20:20:04 [info] [general]: [AI Controller] Item "Siemens or Square D 8-Gang Meter Center, Ringless, 600A Main Bus, 125A per position, NEMA 3R Outdoor enclosure." (type: PRICE_MATERIAL_COMPONENT) flagged for INTERNAL backend price lookup. | {"service":"workiz-api"}
2025-08-27 20:20:04 [debug] [general]: [AI Controller] Item "Siemens or Square D 8-Gang Meter Center, Ringless, 600A Main Bus, 125A per position, NEMA 3R Outdoor enclosure." (type: PRICE_MATERIAL_COMPONENT) - internal lookup query details: "price Siemens W8M6125 8 gang 600A meter center" | {"service":"workiz-api"}
2025-08-27 20:20:04 [info] [general]: [AI Controller] 🚀 SENDING TO CRAWL4AI: "Siemens or Square D 8-Gang Meter Center, Ringless, 600A Main Bus, 125A per position, NEMA 3R Outdoor enclosure." with query: "price Siemens W8M6125 8 gang 600A meter center" | {"service":"workiz-api"}
2025-08-27 20:20:04 [info] [general]: [AI Controller] Item "2-Pole Circuit Breaker for main disconnect for each of 8 tenant units.": AI Category: electrical_material, Suggestion: "price 100A 2-pole circuit breaker Siemens QP" | {"service":"workiz-api"}
2025-08-27 20:20:04 [debug] [general]: [AI Controller] FULL ITEM DETAILS for "2-Pole Circuit Breaker for main disconnect for each of 8 tenant units.": {
  "description": "2-Pole Circuit Breaker for main disconnect for each of 8 tenant units.",
  "category": "electrical_material",
  "quantity": 8,
  "unit": "pcs",
  "attributes": {
    "ampacity": "100A",
    "type": "2-Pole"
  },
  "lookup_query_suggestion": "price 100A 2-pole circuit breaker Siemens QP",
  "lookup_results": []
} | {"service":"workiz-api"}
2025-08-27 20:20:04 [info] [general]: [AI Controller] ✅ CORRECTLY categorized as ELECTRICAL MATERIAL: "2-Pole Circuit Breaker for main disconnect for each of 8 tenant units." | {"service":"workiz-api"}
2025-08-27 20:20:04 [debug] [general]: [parserUtils] determineLookupTypeForItem: Processing item - Description: "2-pole circuit breaker for main disconnect for each of 8 tenant units.", Attributes: {"ampacity":"100A","type":"2-Pole"}, Suggestion: "price 100A 2-pole circuit breaker Siemens QP" | {"service":"workiz-api"}
2025-08-27 20:20:04 [debug] [general]: [parserUtils] determineLookupTypeForItem: Classified as PRICE_MATERIAL_COMPONENT. | {"service":"workiz-api"}
2025-08-27 20:20:04 [info] [general]: [AI Controller] Item "2-Pole Circuit Breaker for main disconnect for each of 8 tenant units." (type: PRICE_MATERIAL_COMPONENT) flagged for INTERNAL backend price lookup. | {"service":"workiz-api"}
2025-08-27 20:20:04 [debug] [general]: [AI Controller] Item "2-Pole Circuit Breaker for main disconnect for each of 8 tenant units." (type: PRICE_MATERIAL_COMPONENT) - internal lookup query details: "price 100A 2-pole circuit breaker Siemens QP" | {"service":"workiz-api"}
2025-08-27 20:20:04 [info] [general]: [AI Controller] 🚀 SENDING TO CRAWL4AI: "2-Pole Circuit Breaker for main disconnect for each of 8 tenant units." with query: "price 100A 2-pole circuit breaker Siemens QP" | {"service":"workiz-api"}
2025-08-27 20:20:04 [info] [general]: [AI Controller] Item "100A Main Breaker Load Center for house/common area loads, NEMA 3R Outdoor.": AI Category: electrical_material, Suggestion: "price 100A 8 space outdoor main breaker panel" | {"service":"workiz-api"}
2025-08-27 20:20:04 [debug] [general]: [AI Controller] FULL ITEM DETAILS for "100A Main Breaker Load Center for house/common area loads, NEMA 3R Outdoor.": {
  "description": "100A Main Breaker Load Center for house/common area loads, NEMA 3R Outdoor.",
  "category": "electrical_material",
  "quantity": 1,
  "unit": "pcs",
  "attributes": {
    "ampacity": "100A",
    "type": "Main Breaker Panel",
    "spaces": "8"
  },
  "lookup_query_suggestion": "price 100A 8 space outdoor main breaker panel",
  "lookup_results": []
} | {"service":"workiz-api"}
2025-08-27 20:20:04 [info] [general]: [AI Controller] ✅ CORRECTLY categorized as ELECTRICAL MATERIAL: "100A Main Breaker Load Center for house/common area loads, NEMA 3R Outdoor." | {"service":"workiz-api"}
2025-08-27 20:20:04 [debug] [general]: [parserUtils] determineLookupTypeForItem: Processing item - Description: "100a main breaker load center for house/common area loads, nema 3r outdoor.", Attributes: {"ampacity":"100A","type":"Main Breaker Panel","spaces":"8"}, Suggestion: "price 100A 8 space outdoor main breaker panel" | {"service":"workiz-api"}
2025-08-27 20:20:04 [debug] [general]: [parserUtils] determineLookupTypeForItem: Classified as PRICE_MATERIAL_COMPONENT. | {"service":"workiz-api"}
2025-08-27 20:20:04 [info] [general]: [AI Controller] Item "100A Main Breaker Load Center for house/common area loads, NEMA 3R Outdoor." (type: PRICE_MATERIAL_COMPONENT) flagged for INTERNAL backend price lookup. | {"service":"workiz-api"}
2025-08-27 20:20:04 [debug] [general]: [AI Controller] Item "100A Main Breaker Load Center for house/common area loads, NEMA 3R Outdoor." (type: PRICE_MATERIAL_COMPONENT) - internal lookup query details: "price 100A 8 space outdoor main breaker panel" | {"service":"workiz-api"}
2025-08-27 20:20:04 [info] [general]: [AI Controller] 🚀 SENDING TO CRAWL4AI: "100A Main Breaker Load Center for house/common area loads, NEMA 3R Outdoor." with query: "price 100A 8 space outdoor main breaker panel" | {"service":"workiz-api"}
2025-08-27 20:20:04 [info] [general]: [AI Controller] Item "Aluminum SER Cable, 3-Conductor with Ground, for feeders to individual unit panels.": AI Category: electrical_material, Suggestion: "price per foot 2 AWG 4-conductor aluminum SER cable" | {"service":"workiz-api"}
2025-08-27 20:20:04 [debug] [general]: [AI Controller] FULL ITEM DETAILS for "Aluminum SER Cable, 3-Conductor with Ground, for feeders to individual unit panels.": {
  "description": "Aluminum SER Cable, 3-Conductor with Ground, for feeders to individual unit panels.",
  "category": "electrical_material",
  "quantity": 200,
  "unit": "feet",
  "attributes": {
    "material": "Aluminum",
    "size": "2 AWG",
    "type": "SER",
    "conductors": "4"
  },
  "lookup_query_suggestion": "price per foot 2 AWG 4-conductor aluminum SER cable",
  "lookup_results": []
} | {"service":"workiz-api"}
2025-08-27 20:20:04 [info] [general]: [AI Controller] ✅ CORRECTLY categorized as ELECTRICAL MATERIAL: "Aluminum SER Cable, 3-Conductor with Ground, for feeders to individual unit panels." | {"service":"workiz-api"}
2025-08-27 20:20:04 [debug] [general]: [parserUtils] determineLookupTypeForItem: Processing item - Description: "aluminum ser cable, 3-conductor with ground, for feeders to individual unit panels.", Attributes: {"material":"Aluminum","size":"2 AWG","type":"SER","conductors":"4"}, Suggestion: "price per foot 2 AWG 4-conductor aluminum SER cable" | {"service":"workiz-api"}
2025-08-27 20:20:04 [debug] [general]: [parserUtils] determineLookupTypeForItem: Classified as PRICE_MATERIAL_COMPONENT. | {"service":"workiz-api"}
2025-08-27 20:20:04 [info] [general]: [AI Controller] Item "Aluminum SER Cable, 3-Conductor with Ground, for feeders to individual unit panels." (type: PRICE_MATERIAL_COMPONENT) flagged for INTERNAL backend price lookup. | {"service":"workiz-api"}
2025-08-27 20:20:04 [debug] [general]: [AI Controller] Item "Aluminum SER Cable, 3-Conductor with Ground, for feeders to individual unit panels." (type: PRICE_MATERIAL_COMPONENT) - internal lookup query details: "price per foot 2 AWG 4-conductor aluminum SER cable" | {"service":"workiz-api"}
2025-08-27 20:20:04 [info] [general]: [AI Controller] 🚀 SENDING TO CRAWL4AI: "Aluminum SER Cable, 3-Conductor with Ground, for feeders to individual unit panels." with query: "price per foot 2 AWG 4-conductor aluminum SER cable" | {"service":"workiz-api"}
2025-08-27 20:20:04 [info] [general]: [AI Controller] Item "Aluminum XHHW-2 Service Entrance Conductors for parallel feed.": AI Category: electrical_material, Suggestion: "price per foot 400 kcmil aluminum XHHW-2 wire" | {"service":"workiz-api"}
2025-08-27 20:20:04 [debug] [general]: [AI Controller] FULL ITEM DETAILS for "Aluminum XHHW-2 Service Entrance Conductors for parallel feed.": {
  "description": "Aluminum XHHW-2 Service Entrance Conductors for parallel feed.",
  "category": "electrical_material",
  "quantity": 200,
  "unit": "feet",
  "attributes": {
    "material": "Aluminum",
    "size": "400 kcmil",
    "type": "XHHW-2"
  },
  "lookup_query_suggestion": "price per foot 400 kcmil aluminum XHHW-2 wire",
  "lookup_results": []
} | {"service":"workiz-api"}
2025-08-27 20:20:04 [info] [general]: [AI Controller] ✅ CORRECTLY categorized as ELECTRICAL MATERIAL: "Aluminum XHHW-2 Service Entrance Conductors for parallel feed." | {"service":"workiz-api"}
2025-08-27 20:20:04 [debug] [general]: [parserUtils] determineLookupTypeForItem: Processing item - Description: "aluminum xhhw-2 service entrance conductors for parallel feed.", Attributes: {"material":"Aluminum","size":"400 kcmil","type":"XHHW-2"}, Suggestion: "price per foot 400 kcmil aluminum XHHW-2 wire" | {"service":"workiz-api"}
2025-08-27 20:20:04 [debug] [general]: [parserUtils] determineLookupTypeForItem: Classified as PRICE_MATERIAL_COMPONENT. | {"service":"workiz-api"}
2025-08-27 20:20:04 [info] [general]: [AI Controller] Item "Aluminum XHHW-2 Service Entrance Conductors for parallel feed." (type: PRICE_MATERIAL_COMPONENT) flagged for INTERNAL backend price lookup. | {"service":"workiz-api"}
2025-08-27 20:20:04 [debug] [general]: [AI Controller] Item "Aluminum XHHW-2 Service Entrance Conductors for parallel feed." (type: PRICE_MATERIAL_COMPONENT) - internal lookup query details: "price per foot 400 kcmil aluminum XHHW-2 wire" | {"service":"workiz-api"}
2025-08-27 20:20:04 [info] [general]: [AI Controller] 🚀 SENDING TO CRAWL4AI: "Aluminum XHHW-2 Service Entrance Conductors for parallel feed." with query: "price per foot 400 kcmil aluminum XHHW-2 wire" | {"service":"workiz-api"}
2025-08-27 20:20:04 [info] [general]: [AI Controller] Item "Rigid Metal Conduit (RMC) for service entrance riser.": AI Category: electrical_material, Suggestion: "price 10ft 3 inch rigid metal conduit" | {"service":"workiz-api"}
2025-08-27 20:20:04 [debug] [general]: [AI Controller] FULL ITEM DETAILS for "Rigid Metal Conduit (RMC) for service entrance riser.": {
  "description": "Rigid Metal Conduit (RMC) for service entrance riser.",
  "category": "electrical_material",
  "quantity": 20,
  "unit": "feet",
  "attributes": {
    "material": "Galvanized Steel",
    "conduit_type": "RMC",
    "conduit_size": "3 inch"
  },
  "lookup_query_suggestion": "price 10ft 3 inch rigid metal conduit",
  "lookup_results": []
} | {"service":"workiz-api"}
2025-08-27 20:20:04 [info] [general]: [AI Controller] ✅ CORRECTLY categorized as ELECTRICAL MATERIAL: "Rigid Metal Conduit (RMC) for service entrance riser." | {"service":"workiz-api"}
2025-08-27 20:20:04 [debug] [general]: [parserUtils] determineLookupTypeForItem: Processing item - Description: "rigid metal conduit (rmc) for service entrance riser.", Attributes: {"material":"Galvanized Steel","conduit_type":"RMC","conduit_size":"3 inch"}, Suggestion: "price 10ft 3 inch rigid metal conduit" | {"service":"workiz-api"}
2025-08-27 20:20:04 [debug] [general]: [parserUtils] determineLookupTypeForItem: Classified as PRICE_MATERIAL_COMPONENT. | {"service":"workiz-api"}
2025-08-27 20:20:04 [info] [general]: [AI Controller] Item "Rigid Metal Conduit (RMC) for service entrance riser." (type: PRICE_MATERIAL_COMPONENT) flagged for INTERNAL backend price lookup. | {"service":"workiz-api"}
2025-08-27 20:20:04 [debug] [general]: [AI Controller] Item "Rigid Metal Conduit (RMC) for service entrance riser." (type: PRICE_MATERIAL_COMPONENT) - internal lookup query details: "price 10ft 3 inch rigid metal conduit" | {"service":"workiz-api"}
2025-08-27 20:20:04 [info] [general]: [AI Controller] 🚀 SENDING TO CRAWL4AI: "Rigid Metal Conduit (RMC) for service entrance riser." with query: "price 10ft 3 inch rigid metal conduit" | {"service":"workiz-api"}
2025-08-27 20:20:04 [info] [general]: [AI Controller] Item "Service Entrance Weatherhead.": AI Category: electrical_material, Suggestion: "price 3 inch service entrance weatherhead" | {"service":"workiz-api"}
2025-08-27 20:20:04 [debug] [general]: [AI Controller] FULL ITEM DETAILS for "Service Entrance Weatherhead.": {
  "description": "Service Entrance Weatherhead.",
  "category": "electrical_material",
  "quantity": 1,
  "unit": "pcs",
  "attributes": {
    "material": "Metal",
    "conduit_size": "3 inch"
  },
  "lookup_query_suggestion": "price 3 inch service entrance weatherhead",
  "lookup_results": []
} | {"service":"workiz-api"}
2025-08-27 20:20:04 [info] [general]: [AI Controller] ✅ CORRECTLY categorized as ELECTRICAL MATERIAL: "Service Entrance Weatherhead." | {"service":"workiz-api"}
2025-08-27 20:20:04 [debug] [general]: [parserUtils] determineLookupTypeForItem: Processing item - Description: "service entrance weatherhead.", Attributes: {"material":"Metal","conduit_size":"3 inch"}, Suggestion: "price 3 inch service entrance weatherhead" | {"service":"workiz-api"}
2025-08-27 20:20:04 [debug] [general]: [parserUtils] determineLookupTypeForItem: Classified as PRICE_MATERIAL_COMPONENT. | {"service":"workiz-api"}
2025-08-27 20:20:04 [info] [general]: [AI Controller] Item "Service Entrance Weatherhead." (type: PRICE_MATERIAL_COMPONENT) flagged for INTERNAL backend price lookup. | {"service":"workiz-api"}
2025-08-27 20:20:04 [debug] [general]: [AI Controller] Item "Service Entrance Weatherhead." (type: PRICE_MATERIAL_COMPONENT) - internal lookup query details: "price 3 inch service entrance weatherhead" | {"service":"workiz-api"}
2025-08-27 20:20:04 [info] [general]: [AI Controller] 🚀 SENDING TO CRAWL4AI: "Service Entrance Weatherhead." with query: "price 3 inch service entrance weatherhead" | {"service":"workiz-api"}
2025-08-27 20:20:04 [info] [general]: [AI Controller] Item "Bare Solid Copper Grounding Electrode Conductor.": AI Category: electrical_material, Suggestion: "price 50ft 4 AWG bare copper ground wire" | {"service":"workiz-api"}
2025-08-27 20:20:04 [debug] [general]: [AI Controller] FULL ITEM DETAILS for "Bare Solid Copper Grounding Electrode Conductor.": {
  "description": "Bare Solid Copper Grounding Electrode Conductor.",
  "category": "electrical_material",
  "quantity": 50,
  "unit": "feet",
  "attributes": {
    "material": "Copper",
    "size": "4 AWG",
    "type": "Bare Solid"
  },
  "lookup_query_suggestion": "price 50ft 4 AWG bare copper ground wire",
  "lookup_results": []
} | {"service":"workiz-api"}
2025-08-27 20:20:04 [info] [general]: [AI Controller] ✅ CORRECTLY categorized as ELECTRICAL MATERIAL: "Bare Solid Copper Grounding Electrode Conductor." | {"service":"workiz-api"}
2025-08-27 20:20:04 [debug] [general]: [parserUtils] determineLookupTypeForItem: Processing item - Description: "bare solid copper grounding electrode conductor.", Attributes: {"material":"Copper","size":"4 AWG","type":"Bare Solid"}, Suggestion: "price 50ft 4 AWG bare copper ground wire" | {"service":"workiz-api"}
2025-08-27 20:20:04 [debug] [general]: [parserUtils] determineLookupTypeForItem: Classified as PRICE_MATERIAL_COMPONENT. | {"service":"workiz-api"}
2025-08-27 20:20:04 [info] [general]: [AI Controller] Item "Bare Solid Copper Grounding Electrode Conductor." (type: PRICE_MATERIAL_COMPONENT) flagged for INTERNAL backend price lookup. | {"service":"workiz-api"}
2025-08-27 20:20:04 [debug] [general]: [AI Controller] Item "Bare Solid Copper Grounding Electrode Conductor." (type: PRICE_MATERIAL_COMPONENT) - internal lookup query details: "price 50ft 4 AWG bare copper ground wire" | {"service":"workiz-api"}
2025-08-27 20:20:04 [info] [general]: [AI Controller] 🚀 SENDING TO CRAWL4AI: "Bare Solid Copper Grounding Electrode Conductor." with query: "price 50ft 4 AWG bare copper ground wire" | {"service":"workiz-api"}
2025-08-27 20:20:04 [info] [general]: [AI Controller] Item "Copper-Clad Ground Rods with Acorn Clamps.": AI Category: electrical_material, Suggestion: "price 5/8 inch 8ft ground rod" | {"service":"workiz-api"}
2025-08-27 20:20:04 [debug] [general]: [AI Controller] FULL ITEM DETAILS for "Copper-Clad Ground Rods with Acorn Clamps.": {
  "description": "Copper-Clad Ground Rods with Acorn Clamps.",
  "category": "electrical_material",
  "quantity": 2,
  "unit": "pcs",
  "attributes": {
    "material": "Copper-Clad Steel",
    "size": "5/8 inch x 8 feet"
  },
  "lookup_query_suggestion": "price 5/8 inch 8ft ground rod",
  "lookup_results": []
} | {"service":"workiz-api"}
2025-08-27 20:20:04 [info] [general]: [AI Controller] ✅ CORRECTLY categorized as ELECTRICAL MATERIAL: "Copper-Clad Ground Rods with Acorn Clamps." | {"service":"workiz-api"}
2025-08-27 20:20:04 [debug] [general]: [parserUtils] determineLookupTypeForItem: Processing item - Description: "copper-clad ground rods with acorn clamps.", Attributes: {"material":"Copper-Clad Steel","size":"5/8 inch x 8 feet"}, Suggestion: "price 5/8 inch 8ft ground rod" | {"service":"workiz-api"}
2025-08-27 20:20:04 [debug] [general]: [parserUtils] determineLookupTypeForItem: Classified as PRICE_MATERIAL_COMPONENT. | {"service":"workiz-api"}
2025-08-27 20:20:04 [info] [general]: [AI Controller] Item "Copper-Clad Ground Rods with Acorn Clamps." (type: PRICE_MATERIAL_COMPONENT) flagged for INTERNAL backend price lookup. | {"service":"workiz-api"}
2025-08-27 20:20:04 [debug] [general]: [AI Controller] Item "Copper-Clad Ground Rods with Acorn Clamps." (type: PRICE_MATERIAL_COMPONENT) - internal lookup query details: "price 5/8 inch 8ft ground rod" | {"service":"workiz-api"}
2025-08-27 20:20:04 [info] [general]: [AI Controller] 🚀 SENDING TO CRAWL4AI: "Copper-Clad Ground Rods with Acorn Clamps." with query: "price 5/8 inch 8ft ground rod" | {"service":"workiz-api"}
2025-08-27 20:20:04 [info] [general]: [AI Controller] Item "Miscellaneous conduit fittings, straps, connectors, hubs, and hardware.": AI Category: electrical_material, Suggestion: "assorted 3 inch RMC fittings cost" | {"service":"workiz-api"}
2025-08-27 20:20:04 [debug] [general]: [AI Controller] FULL ITEM DETAILS for "Miscellaneous conduit fittings, straps, connectors, hubs, and hardware.": {
  "description": "Miscellaneous conduit fittings, straps, connectors, hubs, and hardware.",
  "category": "electrical_material",
  "quantity": 1,
  "unit": "lot",
  "attributes": {},
  "lookup_query_suggestion": "assorted 3 inch RMC fittings cost",
  "lookup_results": []
} | {"service":"workiz-api"}
2025-08-27 20:20:04 [info] [general]: [AI Controller] ✅ CORRECTLY categorized as ELECTRICAL MATERIAL: "Miscellaneous conduit fittings, straps, connectors, hubs, and hardware." | {"service":"workiz-api"}
2025-08-27 20:20:04 [debug] [general]: [parserUtils] determineLookupTypeForItem: Processing item - Description: "miscellaneous conduit fittings, straps, connectors, hubs, and hardware.", Attributes: {}, Suggestion: "assorted 3 inch RMC fittings cost" | {"service":"workiz-api"}
2025-08-27 20:20:04 [debug] [general]: [parserUtils] determineLookupTypeForItem: Classified as PRICE_MATERIAL_COMPONENT. | {"service":"workiz-api"}
2025-08-27 20:20:04 [info] [general]: [AI Controller] Item "Miscellaneous conduit fittings, straps, connectors, hubs, and hardware." (type: PRICE_MATERIAL_COMPONENT) flagged for INTERNAL backend price lookup. | {"service":"workiz-api"}
2025-08-27 20:20:04 [debug] [general]: [AI Controller] Item "Miscellaneous conduit fittings, straps, connectors, hubs, and hardware." (type: PRICE_MATERIAL_COMPONENT) - internal lookup query details: "assorted 3 inch RMC fittings cost" | {"service":"workiz-api"}
2025-08-27 20:20:04 [info] [general]: [AI Controller] 🚀 SENDING TO CRAWL4AI: "Miscellaneous conduit fittings, straps, connectors, hubs, and hardware." with query: "assorted 3 inch RMC fittings cost" | {"service":"workiz-api"}
2025-08-27 20:20:04 [info] [general]: [AI Controller] Item "Pressure-Treated Plywood Sheet for mounting backboard.": AI Category: non_electrical_material, Suggestion: "price 4x8 3/4 inch pressure treated plywood" | {"service":"workiz-api"}
2025-08-27 20:20:04 [debug] [general]: [AI Controller] FULL ITEM DETAILS for "Pressure-Treated Plywood Sheet for mounting backboard.": {
  "description": "Pressure-Treated Plywood Sheet for mounting backboard.",
  "category": "non_electrical_material",
  "quantity": 1,
  "unit": "pcs",
  "attributes": {
    "size": "4x8 feet",
    "thickness": "3/4 inch"
  },
  "lookup_query_suggestion": "price 4x8 3/4 inch pressure treated plywood",
  "lookup_results": []
} | {"service":"workiz-api"}
2025-08-27 20:20:04 [warn] [general]: [AI Controller] ❌ UNEXPECTED category "non_electrical_material" for item: "Pressure-Treated Plywood Sheet for mounting backboard." | {"service":"workiz-api"}
2025-08-27 20:20:04 [info] [general]: [AI Controller] Item "Pressure-Treated Plywood Sheet for mounting backboard." will SKIP price lookup. Category: non_electrical_material | {"service":"workiz-api"}
2025-08-27 20:20:04 [info] [general]: [AI Controller] Item "Pressure-Treated Plywood Sheet for mounting backboard." categorized as NON-ELECTRICAL material by AI - including in output without price lookup | {"service":"workiz-api"}
2025-08-27 20:20:04 [info] [general]: [AI Controller] Item "Exterior grade paint and primer for weather protection of backboard.": AI Category: non_electrical_material, Suggestion: "price 1 gallon exterior paint" | {"service":"workiz-api"}
2025-08-27 20:20:04 [debug] [general]: [AI Controller] FULL ITEM DETAILS for "Exterior grade paint and primer for weather protection of backboard.": {
  "description": "Exterior grade paint and primer for weather protection of backboard.",
  "category": "non_electrical_material",
  "quantity": 1,
  "unit": "gallon",
  "attributes": {},
  "lookup_query_suggestion": "price 1 gallon exterior paint",
  "lookup_results": []
} | {"service":"workiz-api"}
::1 - - [28/Aug/2025:03:20:04 +0000] "POST /api/ai/generate-quote-content HTTP/1.1" 200 25965 "http://localhost:3000/quotes/create" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-08-27 20:20:04 [warn] [general]: [AI Controller] ❌ UNEXPECTED category "non_electrical_material" for item: "Exterior grade paint and primer for weather protection of backboard." | {"service":"workiz-api"}
2025-08-27 20:20:04 [info] [general]: [AI Controller] Item "Exterior grade paint and primer for weather protection of backboard." will SKIP price lookup. Category: non_electrical_material | {"service":"workiz-api"}
2025-08-27 20:20:04 [info] [general]: [AI Controller] Item "Exterior grade paint and primer for weather protection of backboard." categorized as NON-ELECTRICAL material by AI - including in output without price lookup | {"service":"workiz-api"}
2025-08-27 20:20:04 [info] [general]: [AI Controller] Item "Weatherproof Silicone Sealant for sealing penetrations and enclosures.": AI Category: non_electrical_material, Suggestion: "price tube of outdoor silicone sealant" | {"service":"workiz-api"}
2025-08-27 20:20:04 [debug] [general]: [AI Controller] FULL ITEM DETAILS for "Weatherproof Silicone Sealant for sealing penetrations and enclosures.": {
  "description": "Weatherproof Silicone Sealant for sealing penetrations and enclosures.",
  "category": "non_electrical_material",
  "quantity": 2,
  "unit": "tubes",
  "attributes": {},
  "lookup_query_suggestion": "price tube of outdoor silicone sealant",
  "lookup_results": []
} | {"service":"workiz-api"}
2025-08-27 20:20:04 [warn] [general]: [AI Controller] ❌ UNEXPECTED category "non_electrical_material" for item: "Weatherproof Silicone Sealant for sealing penetrations and enclosures." | {"service":"workiz-api"}
2025-08-27 20:20:04 [info] [general]: [AI Controller] Item "Weatherproof Silicone Sealant for sealing penetrations and enclosures." will SKIP price lookup. Category: non_electrical_material | {"service":"workiz-api"}
2025-08-27 20:20:04 [info] [general]: [AI Controller] Item "Weatherproof Silicone Sealant for sealing penetrations and enclosures." categorized as NON-ELECTRICAL material by AI - including in output without price lookup | {"service":"workiz-api"}
2025-08-27 20:20:04 [info] [general]: [AI Controller] Finished processing items for additional lookups. | {"service":"workiz-api"}
2025-08-27 20:20:04 [debug] [ai-generation]: [AI Controller] AI Response with Lookups: {
  "items": [
    {
      "description": "Electrical Permit for main service upgrade. Includes application, processing, and inspection fees.",
      "category": "administrative",
      "quantity": 1,
      "unit": "pcs",
      "attributes": {},
      "lookup_query_suggestion": "local electrical permit fee for 600A service upgrade",
      "lookup_results": [
        {
          "type": "ADMINISTRATIVE_ITEM",
          "status": "administrative_item_no_price",
          "timestamp": "2025-08-28T03:20:04.380Z",
          "reason": "Administrative items included for informational purposes",
          "source": "ai_category_classification"
        }
      ],
      "priceStatus": "not_applicable",
      "source": "Administrative",
      "name": "Electrical Permit for main service upgrade. Includ",
      "price": 0,
      "currency": "USD"
    },
    {
      "description": "Utility company coordination for service disconnection and reconnection. Includes scheduling and project liaison.",
      "category": "administrative",
      "quantity": 1,
      "unit": "lot",
      "attributes": {},
      "lookup_query_suggestion": "",
      "lookup_results": [
        {
          "type": "ADMINISTRATIVE_ITEM",
          "status": "administrative_item_no_price",
          "timestamp": "2025-08-28T03:20:04.381Z",
          "reason": "Administrative items included for informational purposes",
          "source": "ai_category_classification"
        }
      ],
      "priceStatus": "not_applicable",
      "source": "Administrative",
      "name": "Utility company coordination for service disconnec",
      "price": 0,
      "currency": "USD"
    },
    {
      "description": "Demolition and removal of existing multi-gang meter bank, main disconnects, service riser, and associated weathered backboard.",
      "category": "labor",
      "quantity": 8,
      "unit": "hours",
      "attributes": {},
      "lookup_query_suggestion": "",
      "lookup_results": [
        {
          "type": "LABOR_ITEM",
          "status": "labor_item_no_price_lookup",
          "timestamp": "2025-08-28T03:20:04.385Z",
          "reason": "Labor items do not require price lookups",
          "source": "ai_category_classification"
        }
      ],
      "priceStatus": "not_applicable",
      "source": "Labor",
      "name": "Demolition and removal of existing multi",
      "price": 0,
      "currency": "USD"
    },
    {
      "description": "Installation of new pressure-treated plywood backboard for mounting new service equipment.",
      "category": "labor",
      "quantity": 3,
      "unit": "hours",
      "attributes": {},
      "lookup_query_suggestion": "",
      "lookup_results": [
        {
          "type": "LABOR_ITEM",
          "status": "labor_item_no_price_lookup",
          "timestamp": "2025-08-28T03:20:04.387Z",
          "reason": "Labor items do not require price lookups",
          "source": "ai_category_classification"
        }
      ],
      "priceStatus": "not_applicable",
      "source": "Labor",
      "name": "Installation of new pressure",
      "price": 0,
      "currency": "USD"
    },
    {
      "description": "Installation of new 8-gang meter center, service entrance conduit, weatherhead, and conductors.",
      "category": "labor",
      "quantity": 16,
      "unit": "hours",
      "attributes": {},
      "lookup_query_suggestion": "",
      "lookup_results": [
        {
          "type": "LABOR_ITEM",
          "status": "labor_item_no_price_lookup",
          "timestamp": "2025-08-28T03:20:04.389Z",
          "reason": "Labor items do not require price lookups",
          "source": "ai_category_classification"
        }
      ],
      "priceStatus": "not_applicable",
      "source": "Labor",
      "name": "Installation of new 8",
      "price": 0,
      "currency": "USD"
    },
    {
      "description": "Installation of new feeders from meter center to each of the 8 unit subpanel locations.",
      "category": "labor",
      "quantity": 12,
      "unit": "hours",
      "attributes": {},
      "lookup_query_suggestion": "",
      "lookup_results": [
        {
          "type": "LABOR_ITEM",
          "status": "labor_item_no_price_lookup",
          "timestamp": "2025-08-28T03:20:04.391Z",
          "reason": "Labor items do not require price lookups",
          "source": "ai_category_classification"
        }
      ],
      "priceStatus": "not_applicable",
      "source": "Labor",
      "name": "Installation of new feeders from meter center to e",
      "price": 0,
      "currency": "USD"
    },
    {
      "description": "Installation of new grounding electrode system, including ground rods and bonding.",
      "category": "labor",
      "quantity": 4,
      "unit": "hours",
      "attributes": {},
      "lookup_query_suggestion": "",
      "lookup_results": [
        {
          "type": "LABOR_ITEM",
          "status": "labor_item_no_price_lookup",
          "timestamp": "2025-08-28T03:20:04.395Z",
          "reason": "Labor items do not require price lookups",
          "source": "ai_category_classification"
        }
      ],
      "priceStatus": "not_applicable",
      "source": "Labor",
      "name": "Installation of new grounding electrode system",
      "price": 0,
      "currency": "USD"
    },
    {
      "description": "Final termination, testing, and commissioning of the new electrical service.",
      "category": "labor",
      "quantity": 4,
      "unit": "hours",
      "attributes": {},
      "lookup_query_suggestion": "",
      "lookup_results": [
        {
          "type": "LABOR_ITEM",
          "status": "labor_item_no_price_lookup",
          "timestamp": "2025-08-28T03:20:04.397Z",
          "reason": "Labor items do not require price lookups",
          "source": "ai_category_classification"
        }
      ],
      "priceStatus": "not_applicable",
      "source": "Labor",
      "name": "Final termination",
      "price": 0,
      "currency": "USD"
    },
    {
      "description": "Siemens or Square D 8-Gang Meter Center, Ringless, 600A Main Bus, 125A per position, NEMA 3R Outdoor enclosure.",
      "category": "electrical_material",
      "quantity": 1,
      "unit": "pcs",
      "attributes": {
        "ampacity": "600A",
        "type": "Meter Center, 8-Gang",
        "voltage": "120/240V"
      },
      "lookup_query_suggestion": "price Siemens W8M6125 8 gang 600A meter center",
      "lookup_results": [
        {
          "type": "PRICE_MATERIAL_COMPONENT",
          "status": "pending_internal_price_lookup",
          "request_data": {
            "internal_query_details": "price Siemens W8M6125 8 gang 600A meter center"
          },
          "timestamp": "2025-08-28T03:20:04.402Z",
          "source": "ai_suggestion_for_lookup"
        }
      ],
      "name": "Siemens or Square D 8",
      "price": 0,
      "currency": "USD"
    },
    {
      "description": "2-Pole Circuit Breaker for main disconnect for each of 8 tenant units.",
      "category": "electrical_material",
      "quantity": 8,
      "unit": "pcs",
      "attributes": {
        "ampacity": "100A",
        "type": "2-Pole"
      },
      "lookup_query_suggestion": "price 100A 2-pole circuit breaker Siemens QP",
      "lookup_results": [
        {
          "type": "PRICE_MATERIAL_COMPONENT",
          "status": "pending_internal_price_lookup",
          "request_data": {
            "internal_query_details": "price 100A 2-pole circuit breaker Siemens QP"
          },
          "timestamp": "2025-08-28T03:20:04.408Z",
          "source": "ai_suggestion_for_lookup"
        }
      ],
      "name": "2",
      "price": 0,
      "currency": "USD"
    },
    {
      "description": "100A Main Breaker Load Center for house/common area loads, NEMA 3R Outdoor.",
      "category": "electrical_material",
      "quantity": 1,
      "unit": "pcs",
      "attributes": {
        "ampacity": "100A",
        "type": "Main Breaker Panel",
        "spaces": "8"
      },
      "lookup_query_suggestion": "price 100A 8 space outdoor main breaker panel",
      "lookup_results": [
        {
          "type": "PRICE_MATERIAL_COMPONENT",
          "status": "pending_internal_price_lookup",
          "request_data": {
            "internal_query_details": "price 100A 8 space outdoor main breaker panel"
          },
          "timestamp": "2025-08-28T03:20:04.413Z",
          "source": "ai_suggestion_for_lookup"
        }
      ],
      "name": "100A Main Breaker Load Center for house/common are",
      "price": 0,
      "currency": "USD"
    },
    {
      "description": "Aluminum SER Cable, 3-Conductor with Ground, for feeders to individual unit panels.",
      "category": "electrical_material",
      "quantity": 200,
      "unit": "feet",
      "attributes": {
        "material": "Aluminum",
        "size": "2 AWG",
        "type": "SER",
        "conductors": "4"
      },
      "lookup_query_suggestion": "price per foot 2 AWG 4-conductor aluminum SER cable",
      "lookup_results": [
        {
          "type": "PRICE_MATERIAL_COMPONENT",
          "status": "pending_internal_price_lookup",
          "request_data": {
            "internal_query_details": "price per foot 2 AWG 4-conductor aluminum SER cable"
          },
          "timestamp": "2025-08-28T03:20:04.419Z",
          "source": "ai_suggestion_for_lookup"
        }
      ],
      "name": "Aluminum SER Cable",
      "price": 0,
      "currency": "USD"
    },
    {
      "description": "Aluminum XHHW-2 Service Entrance Conductors for parallel feed.",
      "category": "electrical_material",
      "quantity": 200,
      "unit": "feet",
      "attributes": {
        "material": "Aluminum",
        "size": "400 kcmil",
        "type": "XHHW-2"
      },
      "lookup_query_suggestion": "price per foot 400 kcmil aluminum XHHW-2 wire",
      "lookup_results": [
        {
          "type": "PRICE_MATERIAL_COMPONENT",
          "status": "pending_internal_price_lookup",
          "request_data": {
            "internal_query_details": "price per foot 400 kcmil aluminum XHHW-2 wire"
          },
          "timestamp": "2025-08-28T03:20:04.424Z",
          "source": "ai_suggestion_for_lookup"
        }
      ],
      "name": "Aluminum XHHW",
      "price": 0,
      "currency": "USD"
    },
    {
      "description": "Rigid Metal Conduit (RMC) for service entrance riser.",
      "category": "electrical_material",
      "quantity": 20,
      "unit": "feet",
      "attributes": {
        "material": "Galvanized Steel",
        "conduit_type": "RMC",
        "conduit_size": "3 inch"
      },
      "lookup_query_suggestion": "price 10ft 3 inch rigid metal conduit",
      "lookup_results": [
        {
          "type": "PRICE_MATERIAL_COMPONENT",
          "status": "pending_internal_price_lookup",
          "request_data": {
            "internal_query_details": "price 10ft 3 inch rigid metal conduit"
          },
          "timestamp": "2025-08-28T03:20:04.430Z",
          "source": "ai_suggestion_for_lookup"
        }
      ],
      "name": "Rigid Metal Conduit (RMC) for service entrance ris",
      "price": 0,
      "currency": "USD"
    },
    {
      "description": "Service Entrance Weatherhead.",
      "category": "electrical_material",
      "quantity": 1,
      "unit": "pcs",
      "attributes": {
        "material": "Metal",
        "conduit_size": "3 inch"
      },
      "lookup_query_suggestion": "price 3 inch service entrance weatherhead",
      "lookup_results": [
        {
          "type": "PRICE_MATERIAL_COMPONENT",
          "status": "pending_internal_price_lookup",
          "request_data": {
            "internal_query_details": "price 3 inch service entrance weatherhead"
          },
          "timestamp": "2025-08-28T03:20:04.434Z",
          "source": "ai_suggestion_for_lookup"
        }
      ],
      "name": "Service Entrance Weatherhead.",
      "price": 0,
      "currency": "USD"
    },
    {
      "description": "Bare Solid Copper Grounding Electrode Conductor.",
      "category": "electrical_material",
      "quantity": 50,
      "unit": "feet",
      "attributes": {
        "material": "Copper",
        "size": "4 AWG",
        "type": "Bare Solid"
      },
      "lookup_query_suggestion": "price 50ft 4 AWG bare copper ground wire",
      "lookup_results": [
        {
          "type": "PRICE_MATERIAL_COMPONENT",
          "status": "pending_internal_price_lookup",
          "request_data": {
            "internal_query_details": "price 50ft 4 AWG bare copper ground wire"
          },
          "timestamp": "2025-08-28T03:20:04.438Z",
          "source": "ai_suggestion_for_lookup"
        }
      ],
      "name": "Bare Solid Copper Grounding Electrode Conductor.",
      "price": 0,
      "currency": "USD"
    },
    {
      "description": "Copper-Clad Ground Rods with Acorn Clamps.",
      "category": "electrical_material",
      "quantity": 2,
      "unit": "pcs",
      "attributes": {
        "material": "Copper-Clad Steel",
        "size": "5/8 inch x 8 feet"
      },
      "lookup_query_suggestion": "price 5/8 inch 8ft ground rod",
      "lookup_results": [
        {
          "type": "PRICE_MATERIAL_COMPONENT",
          "status": "pending_internal_price_lookup",
          "request_data": {
            "internal_query_details": "price 5/8 inch 8ft ground rod"
          },
          "timestamp": "2025-08-28T03:20:04.440Z",
          "source": "ai_suggestion_for_lookup"
        }
      ],
      "name": "Copper",
      "price": 0,
      "currency": "USD"
    },
    {
      "description": "Miscellaneous conduit fittings, straps, connectors, hubs, and hardware.",
      "category": "electrical_material",
      "quantity": 1,
      "unit": "lot",
      "attributes": {},
      "lookup_query_suggestion": "assorted 3 inch RMC fittings cost",
      "lookup_results": [
        {
          "type": "PRICE_MATERIAL_COMPONENT",
          "status": "pending_internal_price_lookup",
          "request_data": {
            "internal_query_details": "assorted 3 inch RMC fittings cost"
          },
          "timestamp": "2025-08-28T03:20:04.445Z",
          "source": "ai_suggestion_for_lookup"
        }
      ],
      "name": "Miscellaneous conduit fittings",
      "price": 0,
      "currency": "USD"
    },
    {
      "description": "Pressure-Treated Plywood Sheet for mounting backboard.",
      "category": "non_electrical_material",
      "quantity": 1,
      "unit": "pcs",
      "attributes": {
        "size": "4x8 feet",
        "thickness": "3/4 inch"
      },
      "lookup_query_suggestion": "price 4x8 3/4 inch pressure treated plywood",
      "lookup_results": [
        {
          "type": "NON_ELECTRICAL_MATERIAL",
          "status": "non_electrical_material_no_price",
          "timestamp": "2025-08-28T03:20:04.448Z",
          "reason": "Non-electrical construction materials included for informational purposes",
          "source": "ai_category_classification"
        }
      ],
      "priceStatus": "not_applicable",
      "source": "Non-Electrical",
      "name": "Pressure",
      "price": 0,
      "currency": "USD"
    },
    {
      "description": "Exterior grade paint and primer for weather protection of backboard.",
      "category": "non_electrical_material",
      "quantity": 1,
      "unit": "gallon",
      "attributes": {},
      "lookup_query_suggestion": "price 1 gallon exterior paint",
      "lookup_results": [
        {
          "type": "NON_ELECTRICAL_MATERIAL",
          "status": "non_electrical_material_no_price",
          "timestamp": "2025-08-28T03:20:04.448Z",
          "reason": "Non-electrical construction materials included for informational purposes",
          "source": "ai_category_classification"
        }
      ],
      "priceStatus": "not_applicable",
      "source": "Non-Electrical",
      "name": "Exterior grade paint and primer for weather protec",
      "price": 0,
      "currency": "USD"
    },
    {
      "description": "Weatherproof Silicone Sealant for sealing penetrations and enclosures.",
      "category": "non_electrical_material",
      "quantity": 2,
      "unit": "tubes",
      "attributes": {},
      "lookup_query_suggestion": "price tube of outdoor silicone sealant",
      "lookup_results": [
        {
          "type": "NON_ELECTRICAL_MATERIAL",
          "status": "non_electrical_material_no_price",
          "timestamp": "2025-08-28T03:20:04.449Z",
          "reason": "Non-electrical construction materials included for informational purposes",
          "source": "ai_category_classification"
        }
      ],
      "priceStatus": "not_applicable",
      "source": "Non-Electrical",
      "name": "Weatherproof Silicone Sealant for sealing penetrat",
      "price": 0,
      "currency": "USD"
    }
  ],
  "detailed_scope_of_work": "## Scope of Work: Main Electrical Service Replacement for 8-Unit Property\n\n### Project Overview\nThis project involves the complete replacement of the main electrical service equipment for the 8-unit building. The existing service equipment is severely deteriorated, with extensive corrosion that compromises safety and reliability. The upgrade will install a modern, code-compliant system to ensure safe and adequate power distribution to all units and common areas.\n\n### Demolition Phase\n- Coordinate with the local utility provider to schedule a planned power outage for the entire building.\n- Safely de-energize and lock out the existing main electrical service.\n- Systematically dismantle and remove the existing eight meter sockets, main disconnects, enclosures, and the main service disconnect panel.\n- Remove the deteriorated wooden backboard upon which the current equipment is mounted.\n- Dispose of all removed materials in an environmentally responsible manner.\n\n### Installation Phase\n- Prepare the wall surface and install a new, full-size 3/4\" pressure-treated plywood backboard, securely fastened to the building structure. The backboard will be primed and painted with exterior-grade paint for weather protection.\n- Install a new 8-gang, 600-Amp main bus meter center. This unit will house the utility meters and a new 100-Amp main circuit breaker for each of the eight tenant units.\n- Install a new 3-inch Rigid Metal Conduit (RMC) service riser with a new weatherhead to the utility connection point.\n- Pull new, appropriately sized parallel 400 kcmil aluminum service entrance conductors from the weatherhead to the line side of the new meter center.\n- Install a new 100-Amp outdoor-rated load center to serve the building's common area (house panel) loads.\n- Install a new grounding electrode system, consisting of two 8-foot copper-clad ground rods and a continuous #4 AWG copper conductor, bonded to the new service equipment and the metallic water piping system as required by code.\n- Run new #2 AWG aluminum feeder cables from each unit's new 100A breaker to the corresponding interior subpanel.\n\n### System Commissioning\n- Verify all connections are torqued to manufacturer specifications.\n- Conduct thorough testing of the new installation for safety and proper operation.\n- Coordinate with the local electrical inspector for all required inspections.\n- Once the inspection is passed, coordinate with the utility provider to reinstall meters and re-energize the building.\n- Verify power is restored to all units and common areas.\n\n### Exclusions\n- This scope of work does not include the replacement of any electrical panels or wiring located inside individual tenant units.\n- Repair of any pre-existing structural damage to the building wall discovered during demolition is not included.\n- Landscaping or pathway repairs are not included.",
  "overall_summary": "The existing main electrical service for this 8-unit property is in critical condition, exhibiting severe corrosion and deterioration consistent with equipment that has far exceeded its operational lifespan. This presents a significant safety and fire hazard. We recommend a complete replacement of the entire service entrance system. The proposed solution involves installing a new, robust 600A, 8-gang meter center, a new service riser with properly sized conductors, new 100A main breakers for each unit, and a new code-compliant grounding system. This comprehensive upgrade will address the current safety issues, enhance electrical system reliability, provide adequate capacity for modern needs, and ensure full compliance with the National Electrical Code (NEC). This is a necessary capital improvement to protect the property and its tenants.",
  "ai_confidence_score": 0.9
} | {"service":"workiz-api"}
2025-08-27 20:20:04 [info] [general]: [AI Controller] 🔍 Items needing price lookup: 10 | {"service":"workiz-api"}
2025-08-27 20:20:04 [info] [general]: [AI Controller] 🔍 Price lookup item 1: "Siemens or Square D 8-Gang Meter Center, Ringless, 600A Main Bus, 125A per position, NEMA 3R Outdoor enclosure." (Category: electrical_material) | {"service":"workiz-api"}
2025-08-27 20:20:04 [info] [general]: [AI Controller] 🔍 Price lookup item 2: "2-Pole Circuit Breaker for main disconnect for each of 8 tenant units." (Category: electrical_material) | {"service":"workiz-api"}
2025-08-27 20:20:04 [info] [general]: [AI Controller] 🔍 Price lookup item 3: "100A Main Breaker Load Center for house/common area loads, NEMA 3R Outdoor." (Category: electrical_material) | {"service":"workiz-api"}
2025-08-27 20:20:04 [info] [general]: [AI Controller] 🔍 Price lookup item 4: "Aluminum SER Cable, 3-Conductor with Ground, for feeders to individual unit panels." (Category: electrical_material) | {"service":"workiz-api"}
2025-08-27 20:20:04 [info] [general]: [AI Controller] 🔍 Price lookup item 5: "Aluminum XHHW-2 Service Entrance Conductors for parallel feed." (Category: electrical_material) | {"service":"workiz-api"}
2025-08-27 20:20:04 [info] [general]: [AI Controller] 🔍 Price lookup item 6: "Rigid Metal Conduit (RMC) for service entrance riser." (Category: electrical_material) | {"service":"workiz-api"}
2025-08-27 20:20:04 [info] [general]: [AI Controller] 🔍 Price lookup item 7: "Service Entrance Weatherhead." (Category: electrical_material) | {"service":"workiz-api"}
2025-08-27 20:20:04 [info] [general]: [AI Controller] 🔍 Price lookup item 8: "Bare Solid Copper Grounding Electrode Conductor." (Category: electrical_material) | {"service":"workiz-api"}
2025-08-27 20:20:04 [info] [general]: [AI Controller] 🔍 Price lookup item 9: "Copper-Clad Ground Rods with Acorn Clamps." (Category: electrical_material) | {"service":"workiz-api"}
2025-08-27 20:20:04 [info] [general]: [AI Controller] 🔍 Price lookup item 10: "Miscellaneous conduit fittings, straps, connectors, hubs, and hardware." (Category: electrical_material) | {"service":"workiz-api"}
2025-08-27 20:20:04 [info] [general]: [AI Controller] 📋 10 items marked for deferred price lookup. Lookups will be triggered when quote is saved. | {"service":"workiz-api"}
2025-08-27 20:20:04 [info] [general]: [AI Controller] Generating materialsIncluded from items as it was empty | {"service":"workiz-api"}
2025-08-27 20:20:04 [debug] [ai-generation]: [AI Controller] Generated materialsIncluded: ## Materials Included

- Electrical Permit for main service upgrade. Includes application, processing, and inspection fees. (1 pcs)
- Utility company coordination for service disconnection and reconnection. Includes scheduling and project liaison. (1 lot)
- Siemens or Square D 8-Gang Meter Center, Ringless, 600A Main Bus, 125A per position, NEMA 3R Outdoor enclosure. (1 pcs)
  - ampacity: 600A
  - type: Meter Center, 8-Gang
  - voltage: 120/240V
- 2-Pole Circuit Breaker for main disconnect for each of 8 tenant units. (8 pcs)
  - ampacity: 100A
  - type: 2-Pole
- 100A Main Breaker Load Center for house/common area loads, NEMA 3R Outdoor. (1 pcs)
  - ampacity: 100A
  - type: Main Breaker Panel
  - spaces: 8
- Aluminum SER Cable, 3-Conductor with Ground, for feeders to individual unit panels. (200 feet)
  - material: Aluminum
  - size: 2 AWG
  - type: SER
  - conductors: 4
- Aluminum XHHW-2 Service Entrance Conductors for parallel feed. (200 feet)
  - material: Aluminum
  - size: 400 kcmil
  - type: XHHW-2
- Rigid Metal Conduit (RMC) for service entrance riser. (20 feet)
  - material: Galvanized Steel
  - conduit type: RMC
  - conduit size: 3 inch
- Service Entrance Weatherhead. (1 pcs)
  - material: Metal
  - conduit size: 3 inch
- Bare Solid Copper Grounding Electrode Conductor. (50 feet)
  - material: Copper
  - size: 4 AWG
  - type: Bare Solid
- Copper-Clad Ground Rods with Acorn Clamps. (2 pcs)
  - material: Copper-Clad Steel
  - size: 5/8 inch x 8 feet
- Miscellaneous conduit fittings, straps, connectors, hubs, and hardware. (1 lot)
- Pressure-Treated Plywood Sheet for mounting backboard. (1 pcs)
  - size: 4x8 feet
  - thickness: 3/4 inch
- Exterior grade paint and primer for weather protection of backboard. (1 gallon)
- Weatherproof Silicone Sealant for sealing penetrations and enclosures. (2 tubes)
 | {"service":"workiz-api"}
2025-08-27 20:20:04 [info] [ai-generation]: === AI Quote Generation Completed Successfully === | {"service":"workiz-api","status":"pending_price_lookups","hasQuestions":false,"questionCount":0,"itemCount":21,"itemsPendingPriceLookup":10,"aiConfidenceScore":0.9,"processingTimeMs":60738}
2025-08-27 20:20:04 [debug] [ai-generation]: [AI Controller] Final response summary - Status: pending_price_lookups, Items: 21, Questions: 0, Confidence: 0.9 | {"service":"workiz-api"}
2025-08-27 20:20:04 [info] [ai-generation]: === COMPREHENSIVE AI GENERATION LOGGING STOPPED === | {"service":"workiz-api"}