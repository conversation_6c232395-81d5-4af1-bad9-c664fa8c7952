name: Test and Coverage

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest

    strategy:
      matrix:
        node-version: [16.x, 18.x]
        mongodb-version: ['5.0', '6.0']

    steps:
    - uses: actions/checkout@v3

    - name: Use Node.js ${{ matrix.node-version }}
      uses: actions/setup-node@v3
      with:
        node-version: ${{ matrix.node-version }}
        cache: 'npm'

    - name: Start MongoDB
      uses: supercharge/mongodb-github-action@1.10.0
      with:
        mongodb-version: ${{ matrix.mongodb-version }}

    - name: Install dependencies
      run: npm ci

    - name: Create env file
      run: |
        echo "NODE_ENV=test" >> .env
        echo "MONGODB_URI=mongodb://localhost:27017/workiz_test" >> .env
        echo "JWT_SECRET=test-jwt-secret" >> .env
        echo "OPENAI_API_KEY=${{ secrets.OPENAI_API_KEY }}" >> .env
        echo "ENABLE_AI_FEATURES=true" >> .env

    - name: Run tests
      run: npm run test:ci
      env:
        CI: true

    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        token: ${{ secrets.CODECOV_TOKEN }}
        fail_ci_if_error: true
        verbose: true

    - name: Upload test results
      uses: actions/upload-artifact@v3
      if: success() || failure()
      with:
        name: test-results
        path: test-results/junit.xml

  lint:
    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v3

    - name: Use Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18.x'
        cache: 'npm'

    - name: Install dependencies
      run: npm ci

    - name: Run ESLint
      run: npm run lint
      continue-on-error: false

  performance:
    runs-on: ubuntu-latest
    needs: test

    steps:
    - uses: actions/checkout@v3

    - name: Use Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18.x'
        cache: 'npm'

    - name: Install dependencies
      run: npm ci

    - name: Start MongoDB
      uses: supercharge/mongodb-github-action@1.10.0
      with:
        mongodb-version: '6.0'

    - name: Install load testing tools
      run: |
        npm install -g autocannon clinic loadtest

    - name: Run performance tests
      run: |
        npm run test:performance
        npm run perf
      env:
        CI: true
        NODE_ENV: test
        MONGODB_URI: mongodb://localhost:27017/workiz_test
        OPENAI_API_KEY: ${{ secrets.OPENAI_API_KEY }}

    - name: Generate performance report
      run: node tests/performance/generateReport.js
      if: success() || failure()

    - name: Upload performance results
      uses: actions/upload-artifact@v3
      with:
        name: performance-results
        path: |
          test-results/performance.json
          test-results/performance-report.md
          test-results/traces
          test-results/heap-snapshots
          test-results/logs.txt

    - name: Check performance thresholds
      run: |
        node -e "
          const results = require('./test-results/performance.json');
          const failed = results.metrics.some(m => m.actual > m.budget);
          if (failed) {
            console.error('Performance budget exceeded');
            process.exit(1);
          }
        "

    - name: Install performance monitoring tools
      if: github.event_name == 'push' && github.ref == 'refs/heads/main'
      run: |
        npm install -g clinic doctor clinic flame clinic bubbleprof

    - name: Run advanced profiling
      if: github.event_name == 'push' && github.ref == 'refs/heads/main'
      run: |
        clinic doctor -- node tests/performance/runPerformanceTests.js
        clinic flame -- node tests/performance/runPerformanceTests.js
        clinic bubbleprof -- node tests/performance/runPerformanceTests.js

    - name: Upload profiling results
      if: github.event_name == 'push' && github.ref == 'refs/heads/main'
      uses: actions/upload-artifact@v3
      with:
        name: profiling-results
        path: .clinic

    - name: Notify on performance regression
      if: failure()
      uses: actions/github-script@v6
      with:
        script: |
          const report = require('./test-results/performance-report.md');
          github.rest.issues.create({
            owner: context.repo.owner,
            repo: context.repo.repo,
            title: '🚨 Performance Regression Detected',
            body: `Performance regression detected in the latest build.
                  \nSee the [performance report](${context.payload.repository.html_url}/actions/runs/${context.runId}) for details.
                  \n\`\`\`md\n${report}\n\`\`\``
          });

  ai-integration:
    runs-on: ubuntu-latest
    needs: test
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'

    steps:
    - uses: actions/checkout@v3

    - name: Use Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18.x'
        cache: 'npm'

    - name: Install dependencies
      run: npm ci

    - name: Run AI integration tests
      run: npm run test:ai
      env:
        CI: true
        NODE_ENV: test
        OPENAI_API_KEY: ${{ secrets.OPENAI_API_KEY }}

    - name: Upload AI test results
      uses: actions/upload-artifact@v3
      with:
        name: ai-test-results
        path: test-results/ai-tests.json

  deploy-staging:
    runs-on: ubuntu-latest
    needs: [test, lint, performance, ai-integration]
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'

    steps:
    - uses: actions/checkout@v3

    - name: Configure AWS credentials
      uses: aws-actions/configure-aws-credentials@v1
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        aws-region: us-west-2

    - name: Deploy to staging
      run: |
        echo "Deploying to staging..."
        # Add deployment scripts here
