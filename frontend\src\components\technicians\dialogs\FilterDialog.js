import React from "react";
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  IconButton,
} from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";

/**
 * Dialog component for filtering technicians
 */
const FilterDialog = ({
  open,
  onClose,
  filters,
  onFilterChange,
  onResetFilters,
}) => {
  return (
    <Dialog open={open} onClose={onClose} maxWidth="xs" fullWidth>
      <DialogTitle>
        Filter Technicians
        <IconButton
          aria-label="close"
          onClick={onClose}
          sx={{
            position: "absolute",
            right: 8,
            top: 8,
          }}
        >
          <CloseIcon />
        </IconButton>
      </DialogTitle>
      <DialogContent dividers>
        <FormControl fullWidth margin="normal">
          <InputLabel id="status-filter-label">Status</InputLabel>
          <Select
            labelId="status-filter-label"
            name="status"
            value={filters.status}
            label="Status"
            onChange={onFilterChange}
          >
            <MenuItem value="all">All Statuses</MenuItem>
            <MenuItem value="available">Available</MenuItem>
            <MenuItem value="busy">Busy</MenuItem>
            <MenuItem value="off_duty">Off Duty</MenuItem>
            <MenuItem value="on_leave">On Leave</MenuItem>
          </Select>
        </FormControl>

        <FormControl fullWidth margin="normal">
          <InputLabel id="type-filter-label">Contractor Type</InputLabel>
          <Select
            labelId="type-filter-label"
            name="contractorType"
            value={filters.contractorType}
            label="Contractor Type"
            onChange={onFilterChange}
          >
            <MenuItem value="all">All Types</MenuItem>
            <MenuItem value="HVAC">HVAC</MenuItem>
            <MenuItem value="Plumbing">Plumbing</MenuItem>
            <MenuItem value="Electrical">Electrical</MenuItem>
            <MenuItem value="Landscaping">Landscaping</MenuItem>
            <MenuItem value="Cleaning">Cleaning</MenuItem>
            <MenuItem value="General Contractor">General Contractor</MenuItem>
            <MenuItem value="Other">Other</MenuItem>
          </Select>
        </FormControl>
      </DialogContent>
      <DialogActions>
        <Button onClick={onResetFilters}>Reset</Button>
        <Button onClick={onClose} variant="contained" color="primary">
          Apply Filters
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default FilterDialog;
