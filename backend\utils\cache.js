const Redis = require("ioredis");
const { promisify } = require("util");
const ApiError = require("./ApiError");

// Initialize Redis client only if not in test environment
let redis = null;
if (process.env.NODE_ENV !== "test") {
  redis = new Redis({
    host: process.env.REDIS_HOST || "localhost",
    port: process.env.REDIS_PORT || 6380,
    password: process.env.REDIS_PASSWORD || undefined,
    keyPrefix: "workiz:",
    retryStrategy: (times) => {
      const delay = Math.min(times * 50, 2000);
      return delay;
    },
  });
} else {
  // Provide a basic mock object for tests if needed
  redis = {
    get: jest.fn().mockResolvedValue(null),
    setex: jest.fn().mockResolvedValue("OK"),
    del: jest.fn().mockResolvedValue(1),
    keys: jest.fn().mockResolvedValue([]),
    on: jest.fn(),
    quit: jest.fn().mockResolvedValue("OK"),
    disconnect: jest.fn(),
    info: jest.fn().mockResolvedValue(""), // Mock info for getCacheStats
    duplicate: jest.fn().mockReturnThis(), // Mock duplicate for monitorCache
    monitor: jest.fn(), // Mock monitor for monitorCache
  };
}

if (redis && process.env.NODE_ENV !== "test") {
  // Add check before attaching listener
  redis.on("error", (error) => {
    console.error("Redis Error:", error);
  });
}

/**
 * Get cached data
 * @param {string} key - Cache key
 * @returns {Promise<any>} Cached data or null
 */
async function cacheGet(key) {
  try {
    const data = await redis.get(key);
    return data ? JSON.parse(data) : null;
  } catch (error) {
    console.error("Cache get error:", error);
    return null;
  }
}

/**
 * Set cache data
 * @param {string} key - Cache key
 * @param {any} data - Data to cache
 * @param {number} ttl - Time to live in seconds
 */
async function cacheSet(key, data, ttl = 3600) {
  try {
    await redis.setex(key, ttl, JSON.stringify(data));
    return true;
  } catch (error) {
    console.error("Cache set error:", error);
    return false;
  }
}

/**
 * Delete cache entry
 * @param {string} key - Cache key
 */
async function cacheDel(key) {
  try {
    await redis.del(key);
    return true;
  } catch (error) {
    console.error("Cache delete error:", error);
    return false;
  }
}

/**
 * Clear cache by pattern
 * @param {string} pattern - Key pattern to match
 */
async function clearCache(pattern) {
  try {
    const keys = await redis.keys(pattern);
    if (keys.length > 0) {
      await redis.del(keys);
    }
    return true;
  } catch (error) {
    console.error("Cache clear error:", error);
    return false;
  }
}

/**
 * Advanced cache middleware with dynamic TTL and invalidation
 * @param {Object} options - Cache options
 * @param {number} options.duration - Cache duration in seconds (default: 300)
 * @param {Function} options.keyGenerator - Function to generate cache key (default: use URL)
 * @param {Function} options.shouldCache - Function to determine if response should be cached (default: all GET requests)
 * @param {Function} options.getTTL - Function to determine TTL based on response (default: fixed duration)
 */
function cacheMiddleware(options = {}) {
  // Handle both legacy format (duration as number) and options object
  const config =
    typeof options === "number"
      ? { duration: options }
      : { duration: 300, ...options };

  return async (req, res, next) => {
    // Check if request should be cached
    const shouldCache = config.shouldCache
      ? config.shouldCache(req)
      : req.method === "GET";

    if (!shouldCache) {
      return next();
    }

    // Generate cache key
    const key = config.keyGenerator
      ? config.keyGenerator(req)
      : `cache:${req.originalUrl}`;

    try {
      const cached = await cacheGet(key);
      if (cached) {
        return res.json(cached);
      }

      // Store original res.json function
      const originalJson = res.json;

      // Override res.json method to cache response
      res.json = function (data) {
        cacheSet(key, data, duration);
        return originalJson.call(this, data);
      };

      next();
    } catch (error) {
      console.error("Cache middleware error:", error);
      next();
    }
  };
}

/**
 * Cache wrapper for expensive operations
 * @param {Function} fn - Function to cache
 * @param {string} key - Cache key
 * @param {number} ttl - Time to live in seconds
 */
async function withCache(fn, key, ttl = 3600) {
  try {
    const cached = await cacheGet(key);
    if (cached) {
      return cached;
    }

    const data = await fn();
    await cacheSet(key, data, ttl);
    return data;
  } catch (error) {
    console.error("Cache wrapper error:", error);
    throw error;
  }
}

/**
 * Get cache stats
 */
async function getCacheStats() {
  try {
    const info = await redis.info();
    const stats = {};

    info.split("\n").forEach((line) => {
      const [key, value] = line.split(":");
      if (key && value) {
        stats[key.trim()] = value.trim();
      }
    });

    return {
      usedMemory: stats.used_memory_human,
      connectedClients: stats.connected_clients,
      totalKeys: stats.keyspace_hits,
      hitRate: stats.keyspace_hits
        ? (parseInt(stats.keyspace_hits) /
            (parseInt(stats.keyspace_hits) + parseInt(stats.keyspace_misses))) *
          100
        : 0,
    };
  } catch (error) {
    console.error("Cache stats error:", error);
    throw new ApiError(500, "Failed to get cache stats", error.message);
  }
}

/**
 * Monitor cache events
 * @param {Function} callback - Event callback
 */
function monitorCache(callback) {
  const monitor = redis.duplicate();

  monitor.on("error", (error) => {
    console.error("Cache monitor error:", error);
  });

  monitor.monitor((err, reply) => {
    if (err) {
      console.error("Monitor setup error:", err);
      return;
    }

    monitor.on("monitor", (time, args) => {
      callback({
        timestamp: time,
        command: args[0],
        key: args[1],
        value: args[2],
      });
    });
  });

  return () => {
    monitor.disconnect();
  };
}

module.exports = {
  // Export the client instance specifically for test cleanup
  utilsCacheRedisClient: redis,
  cacheGet,
  cacheSet,
  cacheDel,
  clearCache,
  cacheMiddleware,
  withCache,
  getCacheStats,
  monitorCache,
};
