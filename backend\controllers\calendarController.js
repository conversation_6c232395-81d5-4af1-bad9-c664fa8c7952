const CalendarEvent = require("../models/CalendarEvent");
const User = require("../models/User");

// @desc    Create a new calendar event
// @route   POST /api/calendar
// @access  Private
const createCalendarEvent = async (req, res) => {
  try {
    const {
      title,
      description,
      startTime,
      endTime,
      allDay,
      location,
      type,
      relatedTo,
      assignedTo,
      color,
      isRecurring,
      recurrencePattern,
      notifications,
    } = req.body;

    const calendarEvent = await CalendarEvent.create({
      title,
      description,
      startTime,
      endTime,
      allDay,
      location,
      type,
      relatedTo,
      assignedTo,
      createdBy: req.user.id,
      color,
      isRecurring,
      recurrencePattern,
      notifications,
    });

    if (calendarEvent) {
      res.status(201).json(calendarEvent);
    } else {
      res.status(400).json({ message: "Invalid calendar event data" });
    }
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: "Server error" });
  }
};

// @desc    Get all calendar events
// @route   GET /api/calendar
// @access  Private
const getCalendarEvents = async (req, res) => {
  try {
    const startDate = req.query.start ? new Date(req.query.start) : new Date();
    const endDate = req.query.end
      ? new Date(req.query.end)
      : new Date(startDate.getTime() + 30 * 24 * 60 * 60 * 1000); // Default to 30 days from start
    const userId = req.query.assignedTo || null;

    // Use the model's static method for better date range handling
    const calendarEvents = await CalendarEvent.getEventsInRange(
      startDate,
      endDate,
      userId
    );

    // If no events found, create a sample event
    if (calendarEvents.length === 0 && process.env.NODE_ENV === "development") {
      const sampleEvent = await CalendarEvent.create({
        title: "Sample Event",
        description: "This is a sample event for testing",
        startTime: new Date(Date.now() + 24 * 60 * 60 * 1000), // Tomorrow
        endTime: new Date(Date.now() + 25 * 60 * 60 * 1000), // Tomorrow + 1 hour
        type: "meeting",
        createdBy: req.user.id,
        assignedTo: [req.user.id],
      });
      calendarEvents.push(sampleEvent);
    }

    res.json(calendarEvents);
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: "Server error" });
  }
};

// @desc    Get calendar event by ID
// @route   GET /api/calendar/:id
// @access  Private
const getCalendarEventById = async (req, res) => {
  try {
    const calendarEvent = await CalendarEvent.findById(req.params.id)
      .populate("assignedTo", "firstName lastName email")
      .populate("createdBy", "firstName lastName");

    if (calendarEvent) {
      res.json(calendarEvent);
    } else {
      res.status(404).json({ message: "Calendar event not found" });
    }
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: "Server error" });
  }
};

// @desc    Update calendar event
// @route   PUT /api/calendar/:id
// @access  Private
const updateCalendarEvent = async (req, res) => {
  try {
    const calendarEvent = await CalendarEvent.findById(req.params.id);

    if (calendarEvent) {
      calendarEvent.title = req.body.title || calendarEvent.title;
      calendarEvent.description =
        req.body.description || calendarEvent.description;
      calendarEvent.startTime = req.body.startTime || calendarEvent.startTime;
      calendarEvent.endTime = req.body.endTime || calendarEvent.endTime;
      calendarEvent.allDay =
        req.body.allDay !== undefined ? req.body.allDay : calendarEvent.allDay;
      calendarEvent.location = req.body.location || calendarEvent.location;
      calendarEvent.type = req.body.type || calendarEvent.type;
      calendarEvent.relatedTo = req.body.relatedTo || calendarEvent.relatedTo;
      calendarEvent.assignedTo =
        req.body.assignedTo || calendarEvent.assignedTo;
      calendarEvent.color = req.body.color || calendarEvent.color;
      calendarEvent.isRecurring =
        req.body.isRecurring !== undefined
          ? req.body.isRecurring
          : calendarEvent.isRecurring;
      calendarEvent.recurrencePattern =
        req.body.recurrencePattern || calendarEvent.recurrencePattern;
      calendarEvent.notifications =
        req.body.notifications || calendarEvent.notifications;

      const updatedCalendarEvent = await calendarEvent.save();
      res.json(updatedCalendarEvent);
    } else {
      res.status(404).json({ message: "Calendar event not found" });
    }
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: "Server error" });
  }
};

// @desc    Delete calendar event
// @route   DELETE /api/calendar/:id
// @access  Private
const deleteCalendarEvent = async (req, res) => {
  try {
    const calendarEvent = await CalendarEvent.findById(req.params.id);

    if (calendarEvent) {
      await calendarEvent.remove();
      res.json({ message: "Calendar event removed" });
    } else {
      res.status(404).json({ message: "Calendar event not found" });
    }
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: "Server error" });
  }
};

// @desc    Get user's calendar events
// @route   GET /api/calendar/user/:userId
// @access  Private
const getUserCalendarEvents = async (req, res) => {
  try {
    const userId = req.params.userId;

    // Verify user exists
    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({ message: "User not found" });
    }

    // Support for date range filtering
    const dateFilter = {};
    if (req.query.start) {
      dateFilter.startTime = { $gte: new Date(req.query.start) };
    }
    if (req.query.end) {
      dateFilter.endTime = { $lte: new Date(req.query.end) };
    }

    // Find events where user is assigned
    const calendarEvents = await CalendarEvent.find({
      assignedTo: userId,
      ...dateFilter,
    })
      .populate("assignedTo", "firstName lastName")
      .populate("createdBy", "firstName lastName")
      .sort({ startTime: 1 });

    res.json(calendarEvents);
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: "Server error" });
  }
};

// @desc    Check for overlapping events
// @route   POST /api/calendar/check-overlap
// @access  Private
const checkEventOverlap = async (req, res) => {
  try {
    const { startTime, endTime, assignedTo, excludeEventId } = req.body;

    if (!startTime || !endTime || !assignedTo) {
      return res.status(400).json({
        message: "Start time, end time, and assigned users are required",
      });
    }

    // Build query to find overlapping events
    const query = {
      $and: [
        { startTime: { $lt: new Date(endTime) } },
        { endTime: { $gt: new Date(startTime) } },
        { assignedTo: { $in: assignedTo } },
      ],
    };

    // Exclude the current event if updating
    if (excludeEventId) {
      query._id = { $ne: excludeEventId };
    }

    const overlappingEvents = await CalendarEvent.find(query).populate(
      "assignedTo",
      "firstName lastName"
    );

    if (overlappingEvents.length > 0) {
      // Group overlapping events by user
      const userOverlaps = {};

      overlappingEvents.forEach((event) => {
        event.assignedTo.forEach((user) => {
          if (assignedTo.includes(user._id.toString())) {
            if (!userOverlaps[user._id]) {
              userOverlaps[user._id] = {
                user: `${user.firstName} ${user.lastName}`,
                events: [],
              };
            }
            userOverlaps[user._id].events.push({
              title: event.title,
              startTime: event.startTime,
              endTime: event.endTime,
            });
          }
        });
      });

      res.json({
        hasOverlap: true,
        overlaps: Object.values(userOverlaps),
      });
    } else {
      res.json({
        hasOverlap: false,
      });
    }
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: "Server error" });
  }
};

module.exports = {
  createCalendarEvent,
  getCalendarEvents,
  getCalendarEventById,
  updateCalendarEvent,
  deleteCalendarEvent,
  getUserCalendarEvents,
  checkEventOverlap,
};
