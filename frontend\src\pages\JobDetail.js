import React, { useEffect, useState } from "react";
import { usePara<PERSON>, useNavigate } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import axios from "axios"; // Added for direct API calls (e.g., image upload, quote update)
import {
  Box,
  Container,
  Grid,
  Paper,
  Typography,
  Button,
  Chip,
  Divider,
  Card,
  CardContent,
  TextField,
  CircularProgress,
  Alert,
  Tab,
  Tabs,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Rating,
  Menu,
  MenuItem,
  Avatar,
  Checkbox,
  ListItemAvatar,
  ListItemButton,
  Badge,
  Tooltip,
  FormGroup,
  FormControlLabel,
  LinearProgress, // Added for upload progress
  Input, // Added for file input
} from "@mui/material";
import {
  Assignment,
  Schedule as ScheduleIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Add as AddIcon,
  CheckCircle as CheckCircleIcon,
  Timeline as TimelineIcon,
  Flag as FlagIcon,
  Lightbulb as LightbulbIcon,
  Psychology as PsychologyIcon,
  AssignmentInd as AssignmentIndIcon,
  FindInPage as FindInPageIcon,
  BugReport as BugReportIcon,
  Build as BuildIcon,
  Warning as WarningIcon,
  Person as PersonIcon,
  PersonAdd as PersonAddIcon,
  Star as StarIcon,
  Check as CheckIcon,
  InfoOutlined as InfoIcon,
  History as HistoryIcon,
  CloudUpload as CloudUploadIcon, // Added for upload button
  PhotoCamera as PhotoCameraIcon, // Added for attachments tab
  Visibility as VisibilityIcon, // Added for preview functionality
} from "@mui/icons-material";
import {
  getJobById,
  updateJob,
  updateTaskStatus,
  updateJobStatus,
  analyzeJobRisks,
  assignTechnicians,
  predictJobProgress,
  getSimilarJobs,
  getAvailableTechnicians,
  getJobHistory,
  clearJob,
} from "../slices/jobSlice";
import SimilarJobsPanel from "../components/jobs/SimilarJobsPanel";
import GanttChartView from "../components/jobs/GanttChartView";
import JobHistoryTimeline from "../components/jobs/JobHistoryTimeline";
import { formatDate } from "../utils/formatters"; // Import formatDate

function TabPanel(props) {
  const { children, value, index, ...other } = props;
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`job-tabpanel-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

const JobDetail = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const dispatch = useDispatch();

  const [tabValue, setTabValue] = useState(0);
  const [isEditingNotes, setIsEditingNotes] = useState(false);
  const [notesValue, setNotesValue] = useState("");
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);
  const [newTaskDialog, setNewTaskDialog] = useState(false);
  const [newTaskDescription, setNewTaskDescription] = useState("");
  const [aiActionsOpen, setAiActionsOpen] = useState(false);
  const [statusMenuAnchor, setStatusMenuAnchor] = useState(null);
  const [technicianDialogOpen, setTechnicianDialogOpen] = useState(false);
  const [selectedTechnicians, setSelectedTechnicians] = useState([]);
  const [showRecommendedOnly, setShowRecommendedOnly] = useState(false);
  // State for file upload
  const [selectedFiles, setSelectedFiles] = useState([]);
  const [uploadProgress, setUploadProgress] = useState(0); // Progress tracking for file uploads
  const [isUploading, setIsUploading] = useState(false);
  const [uploadError, setUploadError] = useState(null);
  const [uploadSuccess, setUploadSuccess] = useState(false);
  const [attachmentTag, setAttachmentTag] = useState("general"); // Default tag

  const {
    job,
    loading,
    error,
    progressPrediction,
    similarJobs,
    availableTechnicians,
    technicianLoading,
    jobHistory,
    historyLoading,
  } = useSelector((state) => state.jobs);

  useEffect(() => {
    if (id) {
      dispatch(getJobById(id));
    }

    return () => {
      dispatch(clearJob());
    };
  }, [dispatch, id]);

  useEffect(() => {
    if (
      tabValue === 5 &&
      job?._id &&
      (!similarJobs || similarJobs.length === 0)
    ) {
      dispatch(getSimilarJobs(job._id));
    }
  }, [tabValue, job, dispatch, similarJobs]);

  useEffect(() => {
    if (
      tabValue === 6 &&
      job?._id &&
      (!jobHistory || jobHistory.length === 0)
    ) {
      dispatch(getJobHistory(job._id));
    }
  }, [tabValue, job, dispatch, jobHistory]);

  useEffect(() => {
    if (job?.notes) {
      setNotesValue(job.notes);
    }
    if (job?.assignedTechnicians) {
      setSelectedTechnicians(
        job.assignedTechnicians.map((tech) => tech._id || tech)
      );
    }
  }, [job]);

  useEffect(() => {
    if (technicianDialogOpen) {
      dispatch(getAvailableTechnicians());
    }
  }, [technicianDialogOpen, dispatch]);

  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
  };

  const handleSaveNotes = () => {
    dispatch(
      updateJob({
        id: job._id,
        jobData: { ...job, notes: notesValue },
      })
    );
    setIsEditingNotes(false);
  };

  const handleTaskStatusChange = (taskId, isCompleted) => {
    dispatch(
      updateTaskStatus({
        jobId: id,
        taskId,
        isCompleted,
      })
    );
  };

  const handleStatusMenuOpen = (event) => {
    setStatusMenuAnchor(event.currentTarget);
  };

  const handleStatusMenuClose = () => {
    setStatusMenuAnchor(null);
  };

  const handleStatusChange = (newStatus) => {
    dispatch(
      updateJobStatus({
        id: job._id,
        status: newStatus,
      })
    );
    handleStatusMenuClose();
  };

  const handleAddNewTask = () => {
    if (newTaskDescription.trim()) {
      dispatch(
        updateJob({
          id: job._id,
          jobData: {
            ...job,
            tasks: [
              ...job.tasks,
              {
                description: newTaskDescription,
                isCompleted: false,
              },
            ],
          },
        })
      );
      setNewTaskDescription("");
      setNewTaskDialog(false);
    }
  };

  const handleOpenTechnicianDialog = () => {
    setTechnicianDialogOpen(true);
  };

  const handleCloseTechnicianDialog = () => {
    setTechnicianDialogOpen(false);
  };

  const handleTechnicianToggle = (technicianId) => {
    setSelectedTechnicians((prev) => {
      if (prev.includes(technicianId)) {
        return prev.filter((id) => id !== technicianId);
      } else {
        return [...prev, technicianId];
      }
    });
  };

  const handleSaveTechnicianAssignments = () => {
    dispatch(
      updateJob({
        id: job._id,
        jobData: { ...job, assignedTechnicians: selectedTechnicians },
      })
    );
    setTechnicianDialogOpen(false);
  };

  const handleRunAIAssignment = () => {
    dispatch(assignTechnicians({ jobIds: [id] }));
    setTechnicianDialogOpen(false);
  };

  const handleAnalyzeRisks = () => {
    dispatch(analyzeJobRisks({ jobIds: [id] }));
    setAiActionsOpen(false);
  };

  const handleAssignTechnicians = () => {
    dispatch(assignTechnicians({ jobIds: [id] }));
    setAiActionsOpen(false);
  };

  const handlePredictProgress = () => {
    if (job && job._id) {
      dispatch(predictJobProgress(job._id));
    }
  };

  const handleFindSimilarJobs = () => {
    dispatch(getSimilarJobs(id));
    setAiActionsOpen(false);
  };

  const handleGetSimilarJobs = () => {
    if (job && job._id) {
      dispatch(getSimilarJobs(job._id));
    }
  };

  const handleRefreshHistory = () => {
    if (job && job._id) {
      dispatch(getJobHistory(job._id));
    }
  };

  // --- File Upload Handlers ---
  const handleFileSelect = (event) => {
    setSelectedFiles([...event.target.files]);
    setUploadError(null); // Clear previous errors
    setUploadSuccess(false); // Reset success message
    // setUploadProgress(0); // Removed as state is removed
  };

  const handleImageUpload = async () => {
    if (selectedFiles.length === 0) {
      setUploadError("Please select files to upload.");
      return;
    }
    if (!job?._id) {
      setUploadError("Job ID is missing.");
      return;
    }

    setIsUploading(true);
    setUploadError(null);
    setUploadSuccess(false);
    // setUploadProgress(0); // Removed as state is removed

    const formData = new FormData();
    selectedFiles.forEach((file) => {
      formData.append("jobImages", file); // Use the field name expected by multer
    });
    formData.append("tag", attachmentTag); // Add the selected tag

    try {
      const token = localStorage.getItem("userInfo")
        ? JSON.parse(localStorage.getItem("userInfo")).token
        : "";

      const response = await axios.post(
        `/api/jobs/${job._id}/upload`,
        formData,
        {
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "multipart/form-data",
          },
          onUploadProgress: (progressEvent) => {
            const percentCompleted = Math.round(
              (progressEvent.loaded * 100) / progressEvent.total
            );
            setUploadProgress(percentCompleted);
          },
        }
      );

      const data = response.data;

      if (!response.data.success) {
        throw new Error(data.message || "Upload failed");
      }

      setUploadSuccess(true);
      setSelectedFiles([]); // Clear selection on success
      // Optionally refresh job data to show new attachments
      dispatch(getJobById(id));
    } catch (error) {
      console.error("File upload failed:", error); // Use console.error for frontend logging
      setUploadError(error.message || "File upload failed. Please try again.");
    } finally {
      setIsUploading(false);
      setUploadProgress(0); // Reset progress
    }
  };
  // --- End File Upload Handlers ---

  const renderComplexity = (complexity) => {
    const complexityMap = {
      Low: 1,
      Medium: 2,
      High: 3,
    };

    return (
      <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
        <Rating
          value={complexityMap[complexity] || 2}
          max={3}
          readOnly
          icon={<BuildIcon fontSize="inherit" />}
          emptyIcon={<BuildIcon fontSize="inherit" />}
        />
        <Typography variant="body2">
          {complexity || "Medium"} Complexity
        </Typography>
      </Box>
    );
  };

  if (loading) {
    return (
      <Box sx={{ display: "flex", justifyContent: "center", mt: 5 }}>
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Container maxWidth="lg" sx={{ mt: 4 }}>
        <Alert severity="error">{error}</Alert>
      </Container>
    );
  }

  if (!job) {
    return (
      <Container maxWidth="lg" sx={{ mt: 4 }}>
        <Alert severity="warning">Job not found</Alert>
      </Container>
    );
  }

  return (
    <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
      {/* Header */}
      <Box
        sx={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          mb: 3,
        }}
      >
        <Box>
          <Typography variant="h4" gutterBottom>
            {job.title}
          </Typography>
          <Typography color="textSecondary">Job #{job.jobNumber}</Typography>
        </Box>
        <Box>
          <Button
            variant="contained"
            color="primary"
            startIcon={<PsychologyIcon />}
            onClick={() => setAiActionsOpen(true)}
            sx={{ mr: 1 }}
          >
            AI Actions
          </Button>
          <Button
            variant="contained"
            color="secondary"
            startIcon={<EditIcon />}
            onClick={() => navigate(`/jobs/${id}/edit`)}
            sx={{ mr: 1 }}
          >
            Edit Job
          </Button>
          <Button
            variant="outlined"
            color="error"
            startIcon={<DeleteIcon />}
            onClick={() => setShowConfirmDialog(true)}
          >
            Delete
          </Button>
        </Box>
      </Box>

      {/* Status and Priority Cards */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} md={3}>
          <Card>
            <CardContent>
              <Typography variant="subtitle1" gutterBottom>
                Status
              </Typography>
              <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                <Chip
                  label={job.status}
                  color={
                    job.status === "Completed"
                      ? "success"
                      : job.status === "In Progress"
                      ? "warning"
                      : "primary"
                  }
                  icon={<ScheduleIcon />}
                />
                <IconButton size="small" onClick={handleStatusMenuOpen}>
                  <EditIcon fontSize="small" />
                </IconButton>
                <Menu
                  anchorEl={statusMenuAnchor}
                  open={Boolean(statusMenuAnchor)}
                  onClose={handleStatusMenuClose}
                >
                  {[
                    "New",
                    "In Progress",
                    "On Hold",
                    "Completed",
                    "Cancelled",
                  ].map((status) => (
                    <MenuItem
                      key={status}
                      onClick={() => handleStatusChange(status)}
                      disabled={job.status === status}
                    >
                      {status}
                    </MenuItem>
                  ))}
                </Menu>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={3}>
          <Card>
            <CardContent>
              <Typography variant="subtitle1" gutterBottom>
                Priority Score
              </Typography>
              <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                <Chip
                  label={`${job.aiInsights?.priorityScore || 0}/100`}
                  color={
                    job.aiInsights?.priorityScore >= 80
                      ? "error"
                      : job.aiInsights?.priorityScore >= 50
                      ? "warning"
                      : "success"
                  }
                  icon={<FlagIcon />}
                />
                <Typography variant="body2" color="textSecondary">
                  {job.priority} Priority
                </Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={3}>
          <Card>
            <CardContent>
              <Typography variant="subtitle1" gutterBottom>
                Risk Level
              </Typography>
              <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                <Chip
                  label={job.riskAssessment?.overallRiskLevel || "Not Assessed"}
                  color={
                    job.riskAssessment?.overallRiskLevel === "High"
                      ? "error"
                      : job.riskAssessment?.overallRiskLevel === "Medium"
                      ? "warning"
                      : job.riskAssessment?.overallRiskLevel === "Low"
                      ? "success"
                      : "default"
                  }
                  icon={<WarningIcon />}
                />
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={3}>
          <Card>
            <CardContent>
              <Typography variant="subtitle1" gutterBottom>
                Completion
              </Typography>
              <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                <Chip
                  label={
                    job.tasks && job.tasks.length > 0
                      ? `${Math.round(
                          (job.tasks.filter((t) => t.isCompleted).length /
                            job.tasks.length) *
                            100
                        )}%`
                      : "0%"
                  }
                  color="primary"
                  icon={<TimelineIcon />}
                />
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Notes Section */}
      <Paper sx={{ p: 2, mb: 3 }}>
        <Box
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            mb: 2,
          }}
        >
          <Typography variant="h6">Notes</Typography>
          {isEditingNotes ? (
            <Box>
              <Button
                size="small"
                onClick={() => setIsEditingNotes(false)}
                sx={{ mr: 1 }}
              >
                Cancel
              </Button>
              <Button
                size="small"
                variant="contained"
                onClick={handleSaveNotes}
              >
                Save
              </Button>
            </Box>
          ) : (
            <Button
              size="small"
              startIcon={<EditIcon />}
              onClick={() => setIsEditingNotes(true)}
            >
              Edit
            </Button>
          )}
        </Box>
        {isEditingNotes ? (
          <TextField
            fullWidth
            multiline
            rows={4}
            variant="outlined"
            value={notesValue}
            onChange={(e) => setNotesValue(e.target.value)}
          />
        ) : (
          <Typography variant="body2">
            {job.notes || "No notes have been added for this job."}
          </Typography>
        )}
      </Paper>

      {/* Main Content */}
      <Box sx={{ width: "100%" }}>
        <Box sx={{ borderBottom: 1, borderColor: "divider" }}>
          <Tabs
            value={tabValue}
            onChange={handleTabChange}
            variant="scrollable"
            scrollButtons="auto"
            aria-label="job details tabs"
          >
            <Tab
              icon={<LightbulbIcon />}
              iconPosition="start"
              label="AI Insights"
            />
            <Tab icon={<Assignment />} iconPosition="start" label="Tasks" />
            <Tab
              icon={<TimelineIcon />}
              iconPosition="start"
              label="Progress"
            />
            <Tab
              icon={<WarningIcon />}
              iconPosition="start"
              label="Risk Factors"
            />
            <Tab
              icon={<PersonIcon />}
              iconPosition="start"
              label="Technicians"
            />
            <Tab
              icon={<FindInPageIcon />}
              iconPosition="start"
              label="Similar Jobs"
            />
            <Tab icon={<HistoryIcon />} iconPosition="start" label="History" />
            <Tab
              icon={<PhotoCameraIcon />}
              iconPosition="start"
              label="Attachments"
            />
          </Tabs>
        </Box>

        {/* AI Insights Tab */}
        <TabPanel value={tabValue} index={0}>
          <Paper sx={{ p: 2 }}>
            <Typography variant="h6" gutterBottom>
              AI-Generated Insights
            </Typography>

            {job.aiInsights?.lastAnalyzed ? (
              <>
                <Typography variant="body2" color="text.secondary" gutterBottom>
                  Last analyzed:{" "}
                  {new Date(job.aiInsights.lastAnalyzed).toLocaleString()}
                </Typography>

                <Grid container spacing={3} sx={{ mb: 3, mt: 1 }}>
                  <Grid item xs={12} md={6}>
                    <Card variant="outlined">
                      <CardContent>
                        <Typography variant="subtitle1" gutterBottom>
                          Job Complexity
                        </Typography>
                        {renderComplexity(job.aiInsights.complexity)}
                      </CardContent>
                    </Card>
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <Card variant="outlined">
                      <CardContent>
                        <Typography variant="subtitle1" gutterBottom>
                          Recommended Skills
                        </Typography>
                        <Box sx={{ display: "flex", flexWrap: "wrap", gap: 1 }}>
                          {job.aiInsights.recommendedSkills?.map(
                            (skill, idx) => (
                              <Chip key={idx} label={skill} size="small" />
                            )
                          )}
                        </Box>
                      </CardContent>
                    </Card>
                  </Grid>
                </Grid>

                <Typography variant="subtitle1" gutterBottom>
                  Special Considerations
                </Typography>
                <List>
                  {job.aiInsights.specialConsiderations?.map(
                    (consideration, index) => (
                      <ListItem key={index}>
                        <ListItemIcon>
                          <LightbulbIcon color="primary" />
                        </ListItemIcon>
                        <ListItemText primary={consideration} />
                      </ListItem>
                    )
                  )}
                </List>

                <Divider sx={{ my: 2 }} />

                <Typography variant="subtitle1" gutterBottom>
                  Recommended Next Steps
                </Typography>
                <List>
                  {job.aiInsights.nextSteps?.map((step, index) => (
                    <ListItem key={index}>
                      <ListItemIcon>
                        <CheckCircleIcon color="success" />
                      </ListItemIcon>
                      <ListItemText primary={step} />
                    </ListItem>
                  ))}
                </List>

                {job.aiInsights.assignmentReasoning && (
                  <>
                    <Divider sx={{ my: 2 }} />
                    <Typography variant="subtitle1" gutterBottom>
                      Technician Assignment Reasoning
                    </Typography>
                    <Typography variant="body2">
                      {job.aiInsights.assignmentReasoning}
                    </Typography>
                  </>
                )}
              </>
            ) : (
              <Box sx={{ textAlign: "center", py: 4 }}>
                <PsychologyIcon
                  color="action"
                  sx={{ fontSize: 60, opacity: 0.3, mb: 2 }}
                />
                <Typography
                  variant="subtitle1"
                  color="text.secondary"
                  gutterBottom
                >
                  No AI insights available yet
                </Typography>
                <Button
                  variant="contained"
                  color="primary"
                  startIcon={<PsychologyIcon />}
                  onClick={handleAnalyzeRisks}
                  sx={{ mt: 2 }}
                >
                  Generate AI Insights
                </Button>
              </Box>
            )}

            {similarJobs && similarJobs.length > 0 && (
              <>
                <Divider sx={{ my: 2 }} />
                <Typography variant="subtitle1" gutterBottom>
                  Similar Jobs
                </Typography>
                <List>
                  {similarJobs.map((similarJob) => (
                    <ListItem
                      key={similarJob._id}
                      button
                      onClick={() => navigate(`/jobs/${similarJob._id}`)}
                    >
                      <ListItemIcon>
                        <FindInPageIcon color="primary" />
                      </ListItemIcon>
                      <ListItemText
                        primary={similarJob.title}
                        secondary={`Status: ${similarJob.status}`}
                      />
                    </ListItem>
                  ))}
                </List>
              </>
            )}
          </Paper>
        </TabPanel>

        {/* Tasks Tab */}
        <TabPanel value={tabValue} index={1}>
          <Paper sx={{ p: 2 }}>
            <Box
              sx={{ display: "flex", justifyContent: "space-between", mb: 2 }}
            >
              <Typography variant="h6">Tasks</Typography>
              <Button
                startIcon={<AddIcon />}
                onClick={() => setNewTaskDialog(true)}
              >
                Add Task
              </Button>
            </Box>
            {job.tasks && job.tasks.length > 0 ? (
              <List>
                {job.tasks.map((task) => (
                  <ListItem
                    key={task._id}
                    secondaryAction={
                      <IconButton
                        edge="end"
                        onClick={() =>
                          handleTaskStatusChange(task._id, !task.isCompleted)
                        }
                      >
                        <CheckCircleIcon
                          color={task.isCompleted ? "success" : "action"}
                        />
                      </IconButton>
                    }
                  >
                    <ListItemText
                      primary={task.description}
                      secondary={
                        task.isCompleted
                          ? `Completed on ${
                              task.completedAt
                                ? new Date(
                                    task.completedAt
                                  ).toLocaleDateString()
                                : "Unknown date"
                            }`
                          : "Pending"
                      }
                    />
                  </ListItem>
                ))}
              </List>
            ) : (
              <Box sx={{ textAlign: "center", py: 4 }}>
                <Assignment
                  color="action"
                  sx={{ fontSize: 60, opacity: 0.3, mb: 2 }}
                />
                <Typography
                  variant="subtitle1"
                  color="text.secondary"
                  gutterBottom
                >
                  No tasks have been added to this job
                </Typography>
              </Box>
            )}
          </Paper>
        </TabPanel>

        {/* Progress Tab */}
        <TabPanel value={tabValue} index={2}>
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <GanttChartView
                jobId={id}
                loading={loading}
                error={error}
                progressData={progressPrediction}
                onRefreshClick={handlePredictProgress}
              />
            </Grid>
          </Grid>
        </TabPanel>

        {/* Risk Factors Tab */}
        <TabPanel value={tabValue} index={3}>
          <Paper sx={{ p: 2 }}>
            <Box
              sx={{
                display: "flex",
                justifyContent: "space-between",
                alignItems: "center",
                mb: 2,
              }}
            >
              <Typography variant="h6">Risk Assessment</Typography>
              <Chip
                label={job.riskAssessment?.overallRiskLevel || "Not Assessed"}
                color={
                  job.riskAssessment?.overallRiskLevel === "High"
                    ? "error"
                    : job.riskAssessment?.overallRiskLevel === "Medium"
                    ? "warning"
                    : job.riskAssessment?.overallRiskLevel === "Low"
                    ? "success"
                    : "default"
                }
                icon={<WarningIcon />}
              />
            </Box>

            {job.riskAssessment?.lastUpdated && (
              <Typography variant="body2" color="text.secondary" gutterBottom>
                Last assessed:{" "}
                {new Date(job.riskAssessment.lastUpdated).toLocaleString()}
              </Typography>
            )}

            {job.riskAssessment?.riskFactors &&
            job.riskAssessment.riskFactors.length > 0 ? (
              <List>
                {job.riskAssessment.riskFactors.map((risk, index) => (
                  <ListItem key={index}>
                    <ListItemIcon>
                      <WarningIcon
                        color={
                          job.riskAssessment.overallRiskLevel === "High"
                            ? "error"
                            : "warning"
                        }
                      />
                    </ListItemIcon>
                    <ListItemText primary={risk} />
                  </ListItem>
                ))}
              </List>
            ) : (
              <Box sx={{ textAlign: "center", py: 4 }}>
                <BugReportIcon
                  color="action"
                  sx={{ fontSize: 60, opacity: 0.3, mb: 2 }}
                />
                <Typography
                  variant="subtitle1"
                  color="text.secondary"
                  gutterBottom
                >
                  No risk assessment available
                </Typography>
                <Button
                  variant="contained"
                  color="primary"
                  startIcon={<WarningIcon />}
                  onClick={handleAnalyzeRisks}
                  sx={{ mt: 2 }}
                >
                  Analyze Risks
                </Button>
              </Box>
            )}

            {job.riskAssessment?.mitigationSteps &&
              job.riskAssessment.mitigationSteps.length > 0 && (
                <>
                  <Divider sx={{ my: 3 }} />
                  <Typography variant="subtitle1" gutterBottom>
                    Recommended Mitigation Steps
                  </Typography>
                  <List>
                    {job.riskAssessment.mitigationSteps.map((step, index) => (
                      <ListItem key={index}>
                        <ListItemIcon>
                          <CheckCircleIcon color="success" />
                        </ListItemIcon>
                        <ListItemText primary={step} />
                      </ListItem>
                    ))}
                  </List>
                </>
              )}
          </Paper>
        </TabPanel>

        {/* Technicians Tab */}
        <TabPanel value={tabValue} index={4}>
          <Paper sx={{ p: 2 }}>
            <Box
              sx={{
                display: "flex",
                justifyContent: "space-between",
                alignItems: "center",
                mb: 3,
              }}
            >
              <Typography variant="h6">Assigned Technicians</Typography>
              <Button
                variant="contained"
                color="primary"
                startIcon={<PersonAddIcon />}
                onClick={handleOpenTechnicianDialog}
              >
                Manage Technicians
              </Button>
            </Box>

            {job.assignedTechnicians && job.assignedTechnicians.length > 0 ? (
              <List>
                {job.assignedTechnicians.map((tech, idx) => (
                  <Card key={idx} variant="outlined" sx={{ mb: 2 }}>
                    <CardContent>
                      <Grid container spacing={2} alignItems="center">
                        <Grid item>
                          <Avatar sx={{ bgcolor: "primary.main" }}>
                            {tech.firstName ? (
                              tech.firstName[0] + tech.lastName[0]
                            ) : (
                              <PersonIcon />
                            )}
                          </Avatar>
                        </Grid>
                        <Grid item xs>
                          <Typography variant="subtitle1">
                            {tech.firstName
                              ? `${tech.firstName} ${tech.lastName}`
                              : tech.name || "Technician"}
                          </Typography>
                          <Typography variant="body2" color="text.secondary">
                            {tech.skills
                              ? tech.skills.join(", ")
                              : "No skills specified"}
                          </Typography>
                        </Grid>
                        {tech.matchScore && (
                          <Grid item>
                            <Tooltip title="AI Match Score">
                              <Chip
                                icon={<StarIcon />}
                                label={`${Math.round(tech.matchScore * 100)}%`}
                                color={
                                  tech.matchScore > 0.8
                                    ? "success"
                                    : tech.matchScore > 0.5
                                    ? "primary"
                                    : "default"
                                }
                                size="small"
                              />
                            </Tooltip>
                          </Grid>
                        )}
                      </Grid>
                    </CardContent>
                  </Card>
                ))}
              </List>
            ) : (
              <Box sx={{ textAlign: "center", py: 4 }}>
                <PersonAddIcon color="disabled" sx={{ fontSize: 60, mb: 2 }} />
                <Typography variant="body1" color="text.secondary" gutterBottom>
                  No technicians assigned to this job yet.
                </Typography>
                <Button
                  variant="contained"
                  startIcon={<PsychologyIcon />}
                  onClick={handleRunAIAssignment}
                  sx={{ mt: 2, mr: 1 }}
                >
                  AI Recommendations
                </Button>
                <Button
                  variant="outlined"
                  startIcon={<PersonAddIcon />}
                  onClick={handleOpenTechnicianDialog}
                  sx={{ mt: 2 }}
                >
                  Assign Manually
                </Button>
              </Box>
            )}

            {job.aiInsights?.assignmentReasoning && (
              <Box
                sx={{
                  mt: 3,
                  p: 2,
                  bgcolor: "background.paper",
                  borderRadius: 1,
                  border: "1px dashed",
                  borderColor: "divider",
                }}
              >
                <Typography variant="subtitle1" gutterBottom>
                  <InfoIcon
                    fontSize="small"
                    sx={{ mr: 1, verticalAlign: "middle" }}
                  />
                  AI Assignment Reasoning
                </Typography>
                <Typography variant="body2">
                  {job.aiInsights.assignmentReasoning}
                </Typography>
              </Box>
            )}
          </Paper>
        </TabPanel>

        {/* Similar Jobs Tab */}
        <TabPanel value={tabValue} index={5}>
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <SimilarJobsPanel
                jobId={id}
                loading={loading}
                error={error}
                similarJobs={similarJobs}
                onRefreshClick={handleGetSimilarJobs}
              />
            </Grid>
          </Grid>
        </TabPanel>

        {/* History Tab */}
        <TabPanel value={tabValue} index={6}>
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <JobHistoryTimeline
                jobId={id}
                loading={historyLoading}
                error={error}
                history={jobHistory}
                onRefresh={handleRefreshHistory}
              />
            </Grid>
          </Grid>
        </TabPanel>

        {/* Attachments Tab */}
        <TabPanel value={tabValue} index={7}>
          <Paper sx={{ p: 2 }}>
            <Typography variant="h6" gutterBottom>
              Job Attachments
            </Typography>

            {/* Upload Section */}
            <Box
              sx={{ border: "1px dashed grey", p: 2, mb: 3, borderRadius: 1 }}
            >
              <Typography variant="subtitle1" gutterBottom>
                Upload New Images
              </Typography>
              <Grid container spacing={2} alignItems="center">
                <Grid item xs={12} sm={6}>
                  <Input
                    type="file"
                    inputProps={{
                      multiple: true,
                      accept:
                        "image/png, image/jpeg, image/webp, image/heic, image/heif",
                    }}
                    onChange={handleFileSelect}
                    sx={{ display: "block", mb: 1 }} // Basic styling
                    disabled={isUploading}
                  />
                  <Typography variant="caption" color="textSecondary">
                    Selected files:{" "}
                    {selectedFiles.length > 0
                      ? selectedFiles.map((f) => f.name).join(", ")
                      : "None"}{" "}
                    (Max 10MB each)
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={3}>
                  <TextField
                    select
                    label="Tag"
                    value={attachmentTag}
                    onChange={(e) => setAttachmentTag(e.target.value)}
                    size="small"
                    fullWidth
                    disabled={isUploading}
                  >
                    <MenuItem value="general">General</MenuItem>
                    <MenuItem value="before">Before</MenuItem>
                    <MenuItem value="after">After</MenuItem>
                    <MenuItem value="quote_context">Quote Context</MenuItem>
                  </TextField>
                </Grid>
                <Grid item xs={12} sm={3}>
                  <Button
                    variant="contained"
                    startIcon={<CloudUploadIcon />}
                    onClick={handleImageUpload}
                    disabled={isUploading || selectedFiles.length === 0}
                    fullWidth
                  >
                    {isUploading ? "Uploading..." : "Upload"}
                  </Button>
                </Grid>
              </Grid>
              {isUploading && (
                <Box sx={{ width: "100%", mt: 1 }}>
                  <LinearProgress
                    variant="determinate"
                    value={uploadProgress}
                  />
                  <Typography
                    variant="body2"
                    color="text.secondary"
                    align="center"
                    sx={{ mt: 1 }}
                  >
                    {uploadProgress}% uploaded
                  </Typography>
                </Box>
              )}
              {uploadError && (
                <Alert severity="error" sx={{ mt: 2 }}>
                  {uploadError}
                </Alert>
              )}
              {uploadSuccess && (
                <Alert severity="success" sx={{ mt: 2 }}>
                  Upload successful!
                </Alert>
              )}
            </Box>

            {/* Display Existing Attachments (Placeholder) */}
            <Typography variant="subtitle1" gutterBottom>
              Existing Files
            </Typography>
            {job?.attachments && job.attachments.length > 0 ? (
              <List>
                {job.attachments.map((att, index) => (
                  <ListItem key={index} divider>
                    <ListItemIcon>
                      {att.mimeType?.startsWith("image/") ? (
                        <PhotoCameraIcon />
                      ) : (
                        <InfoIcon />
                      )}
                    </ListItemIcon>
                    <ListItemText
                      primary={att.filename}
                      secondary={`Tag: ${
                        att.tag || "general"
                      } | Uploaded: ${formatDate(att.uploadedAt)}`}
                    />
                    <Box sx={{ display: "flex", gap: 1 }}>
                      <Tooltip title="Preview">
                        <IconButton
                          size="small"
                          onClick={() =>
                            window.open(
                              att.url,
                              "_blank",
                              "noopener,noreferrer"
                            )
                          }
                          color="primary"
                        >
                          <VisibilityIcon />
                        </IconButton>
                      </Tooltip>
                      <Tooltip title="Download">
                        <IconButton
                          size="small"
                          onClick={() => {
                            const link = document.createElement("a");
                            link.href = att.url;
                            link.download = att.filename;
                            document.body.appendChild(link);
                            link.click();
                            document.body.removeChild(link);
                          }}
                          color="secondary"
                        >
                          <CloudUploadIcon />
                        </IconButton>
                      </Tooltip>
                      <Tooltip title="Delete">
                        <IconButton
                          size="small"
                          onClick={async () => {
                            if (
                              window.confirm(
                                "Are you sure you want to delete this attachment?"
                              )
                            ) {
                              try {
                                const token = localStorage.getItem("userInfo")
                                  ? JSON.parse(localStorage.getItem("userInfo"))
                                      .token
                                  : "";
                                await axios.delete(
                                  `/api/jobs/${job._id}/attachments/${att._id}`,
                                  {
                                    headers: {
                                      Authorization: `Bearer ${token}`,
                                    },
                                  }
                                );
                                dispatch(getJobById(id)); // Refresh job data
                              } catch (error) {
                                console.error(
                                  "Failed to delete attachment:",
                                  error
                                );
                              }
                            }
                          }}
                          color="error"
                        >
                          <DeleteIcon />
                        </IconButton>
                      </Tooltip>
                    </Box>
                  </ListItem>
                ))}
              </List>
            ) : (
              <Typography variant="body2" color="textSecondary">
                No attachments uploaded yet.
              </Typography>
            )}
          </Paper>
        </TabPanel>
      </Box>

      {/* AI Actions Dialog */}
      <Dialog open={aiActionsOpen} onClose={() => setAiActionsOpen(false)}>
        <DialogTitle>AI Actions</DialogTitle>
        <DialogContent>
          <List>
            <ListItem button onClick={handleAnalyzeRisks}>
              <ListItemIcon>
                <WarningIcon color="warning" />
              </ListItemIcon>
              <ListItemText
                primary="Analyze Job Risks"
                secondary="Identify potential risks and challenges for this job"
              />
            </ListItem>
            <ListItem button onClick={handleAssignTechnicians}>
              <ListItemIcon>
                <AssignmentIndIcon color="primary" />
              </ListItemIcon>
              <ListItemText
                primary="Auto-Assign Technicians"
                secondary="Recommend the best technicians based on skills and availability"
              />
            </ListItem>
            <ListItem button onClick={handlePredictProgress}>
              <ListItemIcon>
                <TimelineIcon color="info" />
              </ListItemIcon>
              <ListItemText
                primary="Predict Progress"
                secondary="Estimate completion time and identify potential delays"
              />
            </ListItem>
            <ListItem button onClick={handleFindSimilarJobs}>
              <ListItemIcon>
                <FindInPageIcon color="secondary" />
              </ListItemIcon>
              <ListItemText
                primary="Find Similar Jobs"
                secondary="Identify similar past jobs to learn from"
              />
            </ListItem>
          </List>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setAiActionsOpen(false)}>Close</Button>
        </DialogActions>
      </Dialog>

      {/* New Task Dialog */}
      <Dialog open={newTaskDialog} onClose={() => setNewTaskDialog(false)}>
        <DialogTitle>Add New Task</DialogTitle>
        <DialogContent>
          <TextField
            autoFocus
            margin="dense"
            label="Task Description"
            fullWidth
            value={newTaskDescription}
            onChange={(e) => setNewTaskDescription(e.target.value)}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setNewTaskDialog(false)}>Cancel</Button>
          <Button onClick={handleAddNewTask} variant="contained">
            Add Task
          </Button>
        </DialogActions>
      </Dialog>

      {/* Confirm Delete Dialog */}
      <Dialog
        open={showConfirmDialog}
        onClose={() => setShowConfirmDialog(false)}
      >
        <DialogTitle>Delete Job</DialogTitle>
        <DialogContent>
          <Typography>
            Are you sure you want to delete this job? This action cannot be
            undone.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowConfirmDialog(false)}>Cancel</Button>
          <Button
            onClick={() => {
              // Handle delete action
              setShowConfirmDialog(false);
              navigate("/jobs");
            }}
            color="error"
          >
            Delete
          </Button>
        </DialogActions>
      </Dialog>

      {/* Technician Assignment Dialog */}
      <Dialog
        open={technicianDialogOpen}
        onClose={handleCloseTechnicianDialog}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>Assign Technicians to Job</DialogTitle>
        <DialogContent dividers>
          {technicianLoading ? (
            <Box sx={{ display: "flex", justifyContent: "center", p: 3 }}>
              <CircularProgress />
            </Box>
          ) : (
            <>
              <Box sx={{ mb: 2 }}>
                <FormGroup>
                  <FormControlLabel
                    control={
                      <Checkbox
                        checked={showRecommendedOnly}
                        onChange={(e) =>
                          setShowRecommendedOnly(e.target.checked)
                        }
                      />
                    }
                    label="Show AI-recommended technicians only"
                  />
                </FormGroup>
              </Box>
              <List>
                {availableTechnicians
                  ?.filter(
                    (tech) =>
                      !showRecommendedOnly ||
                      (tech.aiRecommended && tech.matchScore > 0.6)
                  )
                  .map((tech) => (
                    <ListItem key={tech._id} disablePadding>
                      <ListItemButton
                        onClick={() => handleTechnicianToggle(tech._id)}
                      >
                        <ListItemAvatar>
                          <Badge
                            overlap="circular"
                            anchorOrigin={{
                              vertical: "bottom",
                              horizontal: "right",
                            }}
                            badgeContent={
                              tech.aiRecommended ? (
                                <Tooltip title="AI Recommended">
                                  <Avatar
                                    sx={{
                                      width: 22,
                                      height: 22,
                                      bgcolor: "success.main",
                                      border: "2px solid white",
                                    }}
                                  >
                                    <CheckIcon sx={{ fontSize: 14 }} />
                                  </Avatar>
                                </Tooltip>
                              ) : null
                            }
                          >
                            <Avatar>
                              {tech.firstName ? (
                                tech.firstName[0] + tech.lastName[0]
                              ) : (
                                <PersonIcon />
                              )}
                            </Avatar>
                          </Badge>
                        </ListItemAvatar>
                        <ListItemText
                          primary={`${tech.firstName} ${tech.lastName}`}
                          secondary={
                            <>
                              <Typography
                                component="span"
                                variant="body2"
                                color="text.primary"
                              >
                                {tech.role || "Technician"}
                              </Typography>
                              {tech.skills && tech.skills.length > 0 && (
                                <>
                                  {" — "}
                                  {tech.skills.join(", ")}
                                </>
                              )}
                            </>
                          }
                        />
                        {tech.matchScore && (
                          <Chip
                            size="small"
                            icon={<StarIcon />}
                            label={`${Math.round(tech.matchScore * 100)}%`}
                            color={
                              tech.matchScore > 0.8
                                ? "success"
                                : tech.matchScore > 0.5
                                ? "primary"
                                : "default"
                            }
                            sx={{ mr: 1 }}
                          />
                        )}
                        <Checkbox
                          edge="end"
                          checked={selectedTechnicians.includes(tech._id)}
                          tabIndex={-1}
                          disableRipple
                        />
                      </ListItemButton>
                    </ListItem>
                  ))}
              </List>
            </>
          )}
        </DialogContent>
        <DialogActions>
          <Button
            startIcon={<PsychologyIcon />}
            onClick={handleRunAIAssignment}
          >
            AI Recommendations
          </Button>
          <Button onClick={handleCloseTechnicianDialog}>Cancel</Button>
          <Button
            variant="contained"
            onClick={handleSaveTechnicianAssignments}
            color="primary"
          >
            Save Assignments
          </Button>
        </DialogActions>
      </Dialog>
    </Container>
  );
};

export default JobDetail;
