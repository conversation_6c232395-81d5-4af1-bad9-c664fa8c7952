/**
 * alerter.js
 * Utility for sending alerts via various channels (email, Slack, etc.)
 */

const logger = require("./logger");
const { sendInternalEmail } = require("./internalEmailService");

// Alert configuration from environment variables
const ALERT_CHANNELS = process.env.ALERT_CHANNELS
  ? process.env.ALERT_CHANNELS.split(",")
  : ["log"];
const ALERT_EMAIL_TO = process.env.ALERT_EMAIL_TO || "";
const SLACK_WEBHOOK_URL = process.env.SLACK_WEBHOOK_URL || "";
const ALERT_LOG_LEVEL = process.env.ALERT_LOG_LEVEL || "error";

/**
 * Send an alert through configured channels
 * @param {string} title - Alert title
 * @param {string} message - Alert message
 * @param {Object} options - Additional options
 * @param {string} options.severity - Alert severity (info, warning, error, critical)
 * @param {Object} options.data - Additional data to include
 * @param {Array} options.channels - Override default channels
 * @returns {Promise<Object>} - Result of alert sending
 */
async function sendAlert(title, message, options = {}) {
  const { severity = "error", data = {}, channels = ALERT_CHANNELS } = options;

  const results = {
    success: true,
    channels: {},
    errors: [],
  };

  // Always log the alert
  logAlert(title, message, severity, data);

  // Send through each configured channel
  for (const channel of channels) {
    try {
      switch (channel.toLowerCase().trim()) {
        case "email":
          if (ALERT_EMAIL_TO) {
            const emailResult = await sendEmailAlert(
              title,
              message,
              severity,
              data
            );
            results.channels.email = emailResult;
          } else {
            logger.warn(
              "Email alert requested but ALERT_EMAIL_TO not configured"
            );
            results.channels.email = {
              success: false,
              error: "Not configured",
            };
          }
          break;

        case "slack":
          if (SLACK_WEBHOOK_URL) {
            const slackResult = await sendSlackAlert(
              title,
              message,
              severity,
              data
            );
            results.channels.slack = slackResult;
          } else {
            logger.warn(
              "Slack alert requested but SLACK_WEBHOOK_URL not configured"
            );
            results.channels.slack = {
              success: false,
              error: "Not configured",
            };
          }
          break;

        case "log":
          // Already logged above
          results.channels.log = { success: true };
          break;

        default:
          logger.warn(`Unknown alert channel: ${channel}`);
          results.channels[channel] = {
            success: false,
            error: "Unknown channel",
          };
      }
    } catch (error) {
      logger.error(`Error sending alert via ${channel}:`, error);
      results.errors.push({ channel, error: error.message });
      results.channels[channel] = { success: false, error: error.message };
      results.success = false;
    }
  }

  return results;
}

/**
 * Log alert based on severity
 */
function logAlert(title, message, severity, data) {
  const logMessage = `[ALERT] ${title}: ${message}`;
  const logData = { severity, ...data };

  switch (severity.toLowerCase()) {
    case "critical":
    case "error":
      logger.error(logMessage, logData);
      break;
    case "warning":
      logger.warn(logMessage, logData);
      break;
    case "info":
      logger.info(logMessage, logData);
      break;
    default:
      logger.log(ALERT_LOG_LEVEL, logMessage, logData);
  }
}

/**
 * Send email alert
 */
async function sendEmailAlert(title, message, severity, data) {
  try {
    const emailRecipients = ALERT_EMAIL_TO.split(",").map((email) =>
      email.trim()
    );

    // Format email content
    const emailContent = formatEmailContent(title, message, severity, data);

    // Send email using internal email service
    await sendInternalEmail({
      to: emailRecipients,
      subject: `[${severity.toUpperCase()}] ${title}`,
      html: emailContent,
      priority: severity === "critical" ? "high" : "normal",
    });

    return { success: true, recipients: emailRecipients };
  } catch (error) {
    logger.error("Failed to send email alert:", error);
    throw error;
  }
}

/**
 * Send Slack alert
 */
async function sendSlackAlert(title, message, severity, data) {
  try {
    const axios = require("axios");

    // Determine color based on severity
    const color =
      {
        critical: "#ff0000",
        error: "#ff6b6b",
        warning: "#ffd93d",
        info: "#4ecdc4",
      }[severity.toLowerCase()] || "#808080";

    // Format Slack message
    const slackPayload = {
      text: title,
      attachments: [
        {
          color,
          fields: [
            {
              title: "Message",
              value: message,
              short: false,
            },
            {
              title: "Severity",
              value: severity.toUpperCase(),
              short: true,
            },
            {
              title: "Time",
              value: new Date().toISOString(),
              short: true,
            },
          ],
          footer: "Workiz Alert System",
          ts: Math.floor(Date.now() / 1000),
        },
      ],
    };

    // Add data fields if present
    if (Object.keys(data).length > 0) {
      slackPayload.attachments[0].fields.push({
        title: "Additional Data",
        value: JSON.stringify(data, null, 2),
        short: false,
      });
    }

    const response = await axios.post(SLACK_WEBHOOK_URL, slackPayload);

    return { success: true, status: response.status };
  } catch (error) {
    logger.error("Failed to send Slack alert:", error);
    throw error;
  }
}

/**
 * Format email content with HTML
 */
function formatEmailContent(title, message, severity, data) {
  const severityColors = {
    critical: "#ff0000",
    error: "#ff6b6b",
    warning: "#ffd93d",
    info: "#4ecdc4",
  };

  const color = severityColors[severity.toLowerCase()] || "#808080";

  let html = `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
      <div style="background-color: ${color}; color: white; padding: 20px; border-radius: 5px 5px 0 0;">
        <h2 style="margin: 0;">${title}</h2>
        <p style="margin: 5px 0 0 0;">Severity: ${severity.toUpperCase()}</p>
      </div>
      <div style="background-color: #f5f5f5; padding: 20px; border: 1px solid #ddd; border-top: none;">
        <h3>Alert Message</h3>
        <p style="background-color: white; padding: 15px; border-radius: 5px; border: 1px solid #e0e0e0;">
          ${message}
        </p>
        <h3>Details</h3>
        <table style="background-color: white; width: 100%; border-collapse: collapse; border: 1px solid #e0e0e0;">
          <tr>
            <td style="padding: 10px; border-bottom: 1px solid #e0e0e0;"><strong>Time:</strong></td>
            <td style="padding: 10px; border-bottom: 1px solid #e0e0e0;">${new Date().toLocaleString()}</td>
          </tr>
          <tr>
            <td style="padding: 10px; border-bottom: 1px solid #e0e0e0;"><strong>Environment:</strong></td>
            <td style="padding: 10px; border-bottom: 1px solid #e0e0e0;">${
              process.env.NODE_ENV || "development"
            }</td>
          </tr>
  `;

  // Add additional data if present
  if (Object.keys(data).length > 0) {
    Object.entries(data).forEach(([key, value]) => {
      html += `
          <tr>
            <td style="padding: 10px; border-bottom: 1px solid #e0e0e0;"><strong>${key}:</strong></td>
            <td style="padding: 10px; border-bottom: 1px solid #e0e0e0;">${
              typeof value === "object" ? JSON.stringify(value, null, 2) : value
            }</td>
          </tr>
      `;
    });
  }

  html += `
        </table>
      </div>
      <div style="background-color: #333; color: #ccc; padding: 10px; text-align: center; font-size: 12px; border-radius: 0 0 5px 5px;">
        This is an automated alert from the Workiz system. Do not reply to this email.
      </div>
    </div>
  `;

  return html;
}

/**
 * Create internal email service if it doesn't exist
 */
async function sendInternalEmail(options) {
  const nodemailer = require("nodemailer");

  // Create transporter
  const transporter = nodemailer.createTransport({
    host: process.env.SMTP_HOST,
    port: process.env.SMTP_PORT,
    secure: process.env.SMTP_SECURE === "true",
    auth: {
      user: process.env.SMTP_USER,
      pass: process.env.SMTP_PASS,
    },
  });

  // Send email
  const mailOptions = {
    from: `${process.env.EMAIL_FROM_NAME} Alerts <${process.env.EMAIL_FROM_ADDRESS}>`,
    to: Array.isArray(options.to) ? options.to.join(", ") : options.to,
    subject: options.subject,
    html: options.html,
    priority: options.priority || "normal",
  };

  await transporter.sendMail(mailOptions);
}

// Convenience methods for different severity levels
const alert = {
  critical: (title, message, data = {}) =>
    sendAlert(title, message, { severity: "critical", data }),
  error: (title, message, data = {}) =>
    sendAlert(title, message, { severity: "error", data }),
  warning: (title, message, data = {}) =>
    sendAlert(title, message, { severity: "warning", data }),
  info: (title, message, data = {}) =>
    sendAlert(title, message, { severity: "info", data }),
};

module.exports = {
  sendAlert,
  alert,
  // Export individual channel functions for direct use if needed
  sendEmailAlert,
  sendSlackAlert,
};
