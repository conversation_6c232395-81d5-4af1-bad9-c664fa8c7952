2025-08-29 18:23:49 [[31m[31merror[31m[39m] [general]: Crawl4AI process timed out after 20000ms for URL: https://www.homedepot.com/s/price%20Siemens%20MC4040B1200FEN%20200A%20meter%20main | {"service":"workiz-api"}
2025-08-29 18:23:49 [[31m[31merror[31m[39m] [general]: [HD Crawl4AI Scraper] Error searching for "price Siemens MC4040B1200FEN 200A meter main": Crawl operation timed out after 20000ms | {"service":"workiz-api","stack":"Error: Crawl operation timed out after 20000ms\n    at Timeout._onTimeout (C:\\Projects\\workiz\\backend\\scrapers\\crawl4ai\\crawl4ai-service.js:709:18)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)"}
2025-08-29 18:23:49 [[31m[31merror[31m[39m] [general]: [Home Depot Scraper] Error searching for "price Siemens MC4040B1200FEN 200A meter main": Crawl operation timed out after 20000ms | {"service":"workiz-api","stack":"Error: Crawl operation timed out after 20000ms\n    at Timeout._onTimeout (C:\\Projects\\workiz\\backend\\scrapers\\crawl4ai\\crawl4ai-service.js:709:18)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)"}
2025-08-29 18:23:49 [[31m[31merror[31m[39m] [general]: [ScraperService] Error searching in Home Depot for query "price Siemens MC4040B1200FEN 200A meter main": Crawl operation timed out after 20000ms | {"service":"workiz-api","stack":"Error: Crawl operation timed out after 20000ms\n    at Timeout._onTimeout (C:\\Projects\\workiz\\backend\\scrapers\\crawl4ai\\crawl4ai-service.js:709:18)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)"}
2025-08-29 18:23:49 [[31m[31merror[31m[39m] [general]: [ScraperService Fallback] Error searching primary source (Home Depot ID: 682bdb0e477e692811d861e0): Crawl operation timed out after 20000ms | {"service":"workiz-api","stack":"Error: Crawl operation timed out after 20000ms\n    at Timeout._onTimeout (C:\\Projects\\workiz\\backend\\scrapers\\crawl4ai\\crawl4ai-service.js:709:18)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)"}
2025-08-29 18:23:49 [[31m[31merror[31m[39m] [general]: Crawl4AI process timed out after 20000ms for URL: https://www.homedepot.com/s/price%2020%20ft%204%2F0%20aluminum%20SER%20cable | {"service":"workiz-api"}
2025-08-29 18:23:49 [[31m[31merror[31m[39m] [general]: [HD Crawl4AI Scraper] Error searching for "price 20 ft 4/0 aluminum SER cable": Crawl operation timed out after 20000ms | {"service":"workiz-api","stack":"Error: Crawl operation timed out after 20000ms\n    at Timeout._onTimeout (C:\\Projects\\workiz\\backend\\scrapers\\crawl4ai\\crawl4ai-service.js:709:18)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)"}
2025-08-29 18:23:49 [[31m[31merror[31m[39m] [general]: [Home Depot Scraper] Error searching for "price 20 ft 4/0 aluminum SER cable": Crawl operation timed out after 20000ms | {"service":"workiz-api","stack":"Error: Crawl operation timed out after 20000ms\n    at Timeout._onTimeout (C:\\Projects\\workiz\\backend\\scrapers\\crawl4ai\\crawl4ai-service.js:709:18)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)"}
2025-08-29 18:23:49 [[31m[31merror[31m[39m] [general]: [ScraperService] Error searching in Home Depot for query "price 20 ft 4/0 aluminum SER cable": Crawl operation timed out after 20000ms | {"service":"workiz-api","stack":"Error: Crawl operation timed out after 20000ms\n    at Timeout._onTimeout (C:\\Projects\\workiz\\backend\\scrapers\\crawl4ai\\crawl4ai-service.js:709:18)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)"}
2025-08-29 18:23:49 [[31m[31merror[31m[39m] [general]: [ScraperService Fallback] Error searching primary source (Home Depot ID: 682bdb0e477e692811d861e0): Crawl operation timed out after 20000ms | {"service":"workiz-api","stack":"Error: Crawl operation timed out after 20000ms\n    at Timeout._onTimeout (C:\\Projects\\workiz\\backend\\scrapers\\crawl4ai\\crawl4ai-service.js:709:18)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)"}
2025-08-29 18:23:50 [[31m[31merror[31m[39m] [general]: Crawl4AI process timed out after 20000ms for URL: https://www.homedepot.com/s/price%2010%20ft%202.5%20inch%20schedule%2080%20PVC%20conduit | {"service":"workiz-api"}
2025-08-29 18:23:50 [[31m[31merror[31m[39m] [general]: [HD Crawl4AI Scraper] Error searching for "price 10 ft 2.5 inch schedule 80 PVC conduit": Crawl operation timed out after 20000ms | {"service":"workiz-api","stack":"Error: Crawl operation timed out after 20000ms\n    at Timeout._onTimeout (C:\\Projects\\workiz\\backend\\scrapers\\crawl4ai\\crawl4ai-service.js:709:18)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)"}
2025-08-29 18:23:50 [[31m[31merror[31m[39m] [general]: [Home Depot Scraper] Error searching for "price 10 ft 2.5 inch schedule 80 PVC conduit": Crawl operation timed out after 20000ms | {"service":"workiz-api","stack":"Error: Crawl operation timed out after 20000ms\n    at Timeout._onTimeout (C:\\Projects\\workiz\\backend\\scrapers\\crawl4ai\\crawl4ai-service.js:709:18)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)"}
2025-08-29 18:23:50 [[31m[31merror[31m[39m] [general]: [ScraperService] Error searching in Home Depot for query "price 10 ft 2.5 inch schedule 80 PVC conduit": Crawl operation timed out after 20000ms | {"service":"workiz-api","stack":"Error: Crawl operation timed out after 20000ms\n    at Timeout._onTimeout (C:\\Projects\\workiz\\backend\\scrapers\\crawl4ai\\crawl4ai-service.js:709:18)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)"}
2025-08-29 18:23:50 [[31m[31merror[31m[39m] [general]: [ScraperService Fallback] Error searching primary source (Home Depot ID: 682bdb0e477e692811d861e0): Crawl operation timed out after 20000ms | {"service":"workiz-api","stack":"Error: Crawl operation timed out after 20000ms\n    at Timeout._onTimeout (C:\\Projects\\workiz\\backend\\scrapers\\crawl4ai\\crawl4ai-service.js:709:18)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)"}
2025-08-29 18:23:50 [[31m[31merror[31m[39m] [general]: Crawl4AI process timed out after 20000ms for URL: https://www.homedepot.com/s/price%20Siemens%2020A%20dual%20function%20AFCI%2FGFCI%20breaker | {"service":"workiz-api"}
2025-08-29 18:23:50 [[31m[31merror[31m[39m] [general]: [HD Crawl4AI Scraper] Error searching for "price Siemens 20A dual function AFCI/GFCI breaker": Crawl operation timed out after 20000ms | {"service":"workiz-api","stack":"Error: Crawl operation timed out after 20000ms\n    at Timeout._onTimeout (C:\\Projects\\workiz\\backend\\scrapers\\crawl4ai\\crawl4ai-service.js:709:18)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)"}
2025-08-29 18:23:50 [[31m[31merror[31m[39m] [general]: [Home Depot Scraper] Error searching for "price Siemens 20A dual function AFCI/GFCI breaker": Crawl operation timed out after 20000ms | {"service":"workiz-api","stack":"Error: Crawl operation timed out after 20000ms\n    at Timeout._onTimeout (C:\\Projects\\workiz\\backend\\scrapers\\crawl4ai\\crawl4ai-service.js:709:18)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)"}
2025-08-29 18:23:50 [[31m[31merror[31m[39m] [general]: [ScraperService] Error searching in Home Depot for query "price Siemens 20A dual function AFCI/GFCI breaker": Crawl operation timed out after 20000ms | {"service":"workiz-api","stack":"Error: Crawl operation timed out after 20000ms\n    at Timeout._onTimeout (C:\\Projects\\workiz\\backend\\scrapers\\crawl4ai\\crawl4ai-service.js:709:18)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)"}
2025-08-29 18:23:50 [[31m[31merror[31m[39m] [general]: [ScraperService Fallback] Error searching primary source (Home Depot ID: 682bdb0e477e692811d861e0): Crawl operation timed out after 20000ms | {"service":"workiz-api","stack":"Error: Crawl operation timed out after 20000ms\n    at Timeout._onTimeout (C:\\Projects\\workiz\\backend\\scrapers\\crawl4ai\\crawl4ai-service.js:709:18)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)"}
2025-08-29 18:24:28 [[31m[31merror[31m[39m] [general]: Crawl4AI process timed out after 20000ms for URL: https://www.homedepot.com/s/amp | {"service":"workiz-api"}
2025-08-29 18:24:28 [[31m[31merror[31m[39m] [general]: [HD Crawl4AI Scraper] Error searching for "amp": Crawl operation timed out after 20000ms | {"service":"workiz-api","stack":"Error: Crawl operation timed out after 20000ms\n    at Timeout._onTimeout (C:\\Projects\\workiz\\backend\\scrapers\\crawl4ai\\crawl4ai-service.js:709:18)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)"}
2025-08-29 18:24:28 [[31m[31merror[31m[39m] [general]: [Home Depot Scraper] Error searching for "amp": Crawl operation timed out after 20000ms | {"service":"workiz-api","stack":"Error: Crawl operation timed out after 20000ms\n    at Timeout._onTimeout (C:\\Projects\\workiz\\backend\\scrapers\\crawl4ai\\crawl4ai-service.js:709:18)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)"}
2025-08-29 18:24:28 [[31m[31merror[31m[39m] [general]: [ScraperService] Error searching in Home Depot for query "amp": Crawl operation timed out after 20000ms | {"service":"workiz-api","stack":"Error: Crawl operation timed out after 20000ms\n    at Timeout._onTimeout (C:\\Projects\\workiz\\backend\\scrapers\\crawl4ai\\crawl4ai-service.js:709:18)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)"}
2025-08-29 18:24:30 [[31m[31merror[31m[39m] [general]: [ScraperService] Error searching in Home Depot for query "price 30 ft #4 solid bare copper wire": Circuit breaker is OPEN. Retry in 178 seconds. | {"service":"workiz-api","stack":"Error: Circuit breaker is OPEN. Retry in 178 seconds.\n    at CircuitBreaker.execute (C:\\Projects\\workiz\\backend\\utils\\circuitBreaker.js:40:15)\n    at ScraperService.searchBySource (C:\\Projects\\workiz\\backend\\scrapers\\ScraperService.js:427:56)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async ScraperService.searchBySourceWithVariations (C:\\Projects\\workiz\\backend\\scrapers\\ScraperService.js:336:19)\n    at async ScraperService.searchMaterials (C:\\Projects\\workiz\\backend\\scrapers\\ScraperService.js:243:19)\n    at async processItem (C:\\Projects\\workiz\\backend\\controllers\\quoteController.js:3318:37)\n    at async processItemsInParallel (C:\\Projects\\workiz\\backend\\controllers\\quoteController.js:3428:13)\n    at async lookupPricesWithoutSaving (C:\\Projects\\workiz\\backend\\controllers\\quoteController.js:3436:26)"}
2025-08-29 18:24:30 [[31m[31merror[31m[39m] [general]: [ScraperService Fallback] Error searching primary source (Home Depot ID: 682bdb0e477e692811d861e0): Circuit breaker is OPEN. Retry in 178 seconds. | {"service":"workiz-api","stack":"Error: Circuit breaker is OPEN. Retry in 178 seconds.\n    at CircuitBreaker.execute (C:\\Projects\\workiz\\backend\\utils\\circuitBreaker.js:40:15)\n    at ScraperService.searchBySource (C:\\Projects\\workiz\\backend\\scrapers\\ScraperService.js:427:56)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async ScraperService.searchBySourceWithVariations (C:\\Projects\\workiz\\backend\\scrapers\\ScraperService.js:336:19)\n    at async ScraperService.searchMaterials (C:\\Projects\\workiz\\backend\\scrapers\\ScraperService.js:243:19)\n    at async processItem (C:\\Projects\\workiz\\backend\\controllers\\quoteController.js:3318:37)\n    at async processItemsInParallel (C:\\Projects\\workiz\\backend\\controllers\\quoteController.js:3428:13)\n    at async lookupPricesWithoutSaving (C:\\Projects\\workiz\\backend\\controllers\\quoteController.js:3436:26)"}
2025-08-29 18:24:30 [[31m[31merror[31m[39m] [general]: [ScraperService] Error searching in Home Depot for query "price acorn ground rod clamp 5/8 inch": Circuit breaker is OPEN. Retry in 178 seconds. | {"service":"workiz-api","stack":"Error: Circuit breaker is OPEN. Retry in 178 seconds.\n    at CircuitBreaker.execute (C:\\Projects\\workiz\\backend\\utils\\circuitBreaker.js:40:15)\n    at ScraperService.searchBySource (C:\\Projects\\workiz\\backend\\scrapers\\ScraperService.js:427:56)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async ScraperService.searchBySourceWithVariations (C:\\Projects\\workiz\\backend\\scrapers\\ScraperService.js:336:19)\n    at async ScraperService.searchMaterials (C:\\Projects\\workiz\\backend\\scrapers\\ScraperService.js:243:19)\n    at async processItem (C:\\Projects\\workiz\\backend\\controllers\\quoteController.js:3318:37)\n    at async processItemsInParallel (C:\\Projects\\workiz\\backend\\controllers\\quoteController.js:3428:13)\n    at async lookupPricesWithoutSaving (C:\\Projects\\workiz\\backend\\controllers\\quoteController.js:3436:26)"}
2025-08-29 18:24:30 [[31m[31merror[31m[39m] [general]: [ScraperService Fallback] Error searching primary source (Home Depot ID: 682bdb0e477e692811d861e0): Circuit breaker is OPEN. Retry in 178 seconds. | {"service":"workiz-api","stack":"Error: Circuit breaker is OPEN. Retry in 178 seconds.\n    at CircuitBreaker.execute (C:\\Projects\\workiz\\backend\\utils\\circuitBreaker.js:40:15)\n    at ScraperService.searchBySource (C:\\Projects\\workiz\\backend\\scrapers\\ScraperService.js:427:56)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async ScraperService.searchBySourceWithVariations (C:\\Projects\\workiz\\backend\\scrapers\\ScraperService.js:336:19)\n    at async ScraperService.searchMaterials (C:\\Projects\\workiz\\backend\\scrapers\\ScraperService.js:243:19)\n    at async processItem (C:\\Projects\\workiz\\backend\\controllers\\quoteController.js:3318:37)\n    at async processItemsInParallel (C:\\Projects\\workiz\\backend\\controllers\\quoteController.js:3428:13)\n    at async lookupPricesWithoutSaving (C:\\Projects\\workiz\\backend\\controllers\\quoteController.js:3436:26)"}
2025-08-29 18:24:30 [[31m[31merror[31m[39m] [general]: [ScraperService] Error searching in Home Depot for query "price intersystem bonding bridge": Circuit breaker is OPEN. Retry in 178 seconds. | {"service":"workiz-api","stack":"Error: Circuit breaker is OPEN. Retry in 178 seconds.\n    at CircuitBreaker.execute (C:\\Projects\\workiz\\backend\\utils\\circuitBreaker.js:40:15)\n    at ScraperService.searchBySource (C:\\Projects\\workiz\\backend\\scrapers\\ScraperService.js:427:56)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async ScraperService.searchBySourceWithVariations (C:\\Projects\\workiz\\backend\\scrapers\\ScraperService.js:336:19)\n    at async ScraperService.searchMaterials (C:\\Projects\\workiz\\backend\\scrapers\\ScraperService.js:243:19)\n    at async processItem (C:\\Projects\\workiz\\backend\\controllers\\quoteController.js:3318:37)\n    at async processItemsInParallel (C:\\Projects\\workiz\\backend\\controllers\\quoteController.js:3428:13)\n    at async lookupPricesWithoutSaving (C:\\Projects\\workiz\\backend\\controllers\\quoteController.js:3436:26)"}
2025-08-29 18:24:30 [[31m[31merror[31m[39m] [general]: [ScraperService Fallback] Error searching primary source (Home Depot ID: 682bdb0e477e692811d861e0): Circuit breaker is OPEN. Retry in 178 seconds. | {"service":"workiz-api","stack":"Error: Circuit breaker is OPEN. Retry in 178 seconds.\n    at CircuitBreaker.execute (C:\\Projects\\workiz\\backend\\utils\\circuitBreaker.js:40:15)\n    at ScraperService.searchBySource (C:\\Projects\\workiz\\backend\\scrapers\\ScraperService.js:427:56)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async ScraperService.searchBySourceWithVariations (C:\\Projects\\workiz\\backend\\scrapers\\ScraperService.js:336:19)\n    at async ScraperService.searchMaterials (C:\\Projects\\workiz\\backend\\scrapers\\ScraperService.js:243:19)\n    at async processItem (C:\\Projects\\workiz\\backend\\controllers\\quoteController.js:3318:37)\n    at async processItemsInParallel (C:\\Projects\\workiz\\backend\\controllers\\quoteController.js:3428:13)\n    at async lookupPricesWithoutSaving (C:\\Projects\\workiz\\backend\\controllers\\quoteController.js:3436:26)"}
2025-08-29 18:24:30 [[31m[31merror[31m[39m] [general]: [ScraperService] Error searching in Home Depot for query "price 1lb duct seal compound": Circuit breaker is OPEN. Retry in 178 seconds. | {"service":"workiz-api","stack":"Error: Circuit breaker is OPEN. Retry in 178 seconds.\n    at CircuitBreaker.execute (C:\\Projects\\workiz\\backend\\utils\\circuitBreaker.js:40:15)\n    at ScraperService.searchBySource (C:\\Projects\\workiz\\backend\\scrapers\\ScraperService.js:427:56)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async ScraperService.searchBySourceWithVariations (C:\\Projects\\workiz\\backend\\scrapers\\ScraperService.js:336:19)\n    at async ScraperService.searchMaterials (C:\\Projects\\workiz\\backend\\scrapers\\ScraperService.js:243:19)\n    at async processItem (C:\\Projects\\workiz\\backend\\controllers\\quoteController.js:3318:37)\n    at async processItemsInParallel (C:\\Projects\\workiz\\backend\\controllers\\quoteController.js:3428:13)\n    at async lookupPricesWithoutSaving (C:\\Projects\\workiz\\backend\\controllers\\quoteController.js:3436:26)"}
2025-08-29 18:24:30 [[31m[31merror[31m[39m] [general]: [ScraperService Fallback] Error searching primary source (Home Depot ID: 682bdb0e477e692811d861e0): Circuit breaker is OPEN. Retry in 178 seconds. | {"service":"workiz-api","stack":"Error: Circuit breaker is OPEN. Retry in 178 seconds.\n    at CircuitBreaker.execute (C:\\Projects\\workiz\\backend\\utils\\circuitBreaker.js:40:15)\n    at ScraperService.searchBySource (C:\\Projects\\workiz\\backend\\scrapers\\ScraperService.js:427:56)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async ScraperService.searchBySourceWithVariations (C:\\Projects\\workiz\\backend\\scrapers\\ScraperService.js:336:19)\n    at async ScraperService.searchMaterials (C:\\Projects\\workiz\\backend\\scrapers\\ScraperService.js:243:19)\n    at async processItem (C:\\Projects\\workiz\\backend\\controllers\\quoteController.js:3318:37)\n    at async processItemsInParallel (C:\\Projects\\workiz\\backend\\controllers\\quoteController.js:3428:13)\n    at async lookupPricesWithoutSaving (C:\\Projects\\workiz\\backend\\controllers\\quoteController.js:3436:26)"}
2025-08-29 18:24:31 [[31m[31merror[31m[39m] [general]: [ScraperService] Error searching in Home Depot for query "price panel schedule labels": Circuit breaker is OPEN. Retry in 178 seconds. | {"service":"workiz-api","stack":"Error: Circuit breaker is OPEN. Retry in 178 seconds.\n    at CircuitBreaker.execute (C:\\Projects\\workiz\\backend\\utils\\circuitBreaker.js:40:15)\n    at ScraperService.searchBySource (C:\\Projects\\workiz\\backend\\scrapers\\ScraperService.js:427:56)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async ScraperService.searchBySourceWithVariations (C:\\Projects\\workiz\\backend\\scrapers\\ScraperService.js:336:19)\n    at async ScraperService.searchMaterials (C:\\Projects\\workiz\\backend\\scrapers\\ScraperService.js:243:19)\n    at async processItem (C:\\Projects\\workiz\\backend\\controllers\\quoteController.js:3318:37)\n    at async processItemsInParallel (C:\\Projects\\workiz\\backend\\controllers\\quoteController.js:3428:13)\n    at async lookupPricesWithoutSaving (C:\\Projects\\workiz\\backend\\controllers\\quoteController.js:3436:26)"}
2025-08-29 18:24:31 [[31m[31merror[31m[39m] [general]: [ScraperService Fallback] Error searching primary source (Home Depot ID: 682bdb0e477e692811d861e0): Circuit breaker is OPEN. Retry in 178 seconds. | {"service":"workiz-api","stack":"Error: Circuit breaker is OPEN. Retry in 178 seconds.\n    at CircuitBreaker.execute (C:\\Projects\\workiz\\backend\\utils\\circuitBreaker.js:40:15)\n    at ScraperService.searchBySource (C:\\Projects\\workiz\\backend\\scrapers\\ScraperService.js:427:56)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async ScraperService.searchBySourceWithVariations (C:\\Projects\\workiz\\backend\\scrapers\\ScraperService.js:336:19)\n    at async ScraperService.searchMaterials (C:\\Projects\\workiz\\backend\\scrapers\\ScraperService.js:243:19)\n    at async processItem (C:\\Projects\\workiz\\backend\\controllers\\quoteController.js:3318:37)\n    at async processItemsInParallel (C:\\Projects\\workiz\\backend\\controllers\\quoteController.js:3428:13)\n    at async lookupPricesWithoutSaving (C:\\Projects\\workiz\\backend\\controllers\\quoteController.js:3436:26)"}
2025-08-29 18:24:33 [[31m[31merror[31m[39m] [general]: [ScraperService] Error searching in Home Depot for query "price stucco patch mix": Circuit breaker is OPEN. Retry in 176 seconds. | {"service":"workiz-api","stack":"Error: Circuit breaker is OPEN. Retry in 176 seconds.\n    at CircuitBreaker.execute (C:\\Projects\\workiz\\backend\\utils\\circuitBreaker.js:40:15)\n    at ScraperService.searchBySource (C:\\Projects\\workiz\\backend\\scrapers\\ScraperService.js:427:56)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async ScraperService.searchBySourceWithVariations (C:\\Projects\\workiz\\backend\\scrapers\\ScraperService.js:336:19)\n    at async ScraperService.searchMaterials (C:\\Projects\\workiz\\backend\\scrapers\\ScraperService.js:243:19)\n    at async processItem (C:\\Projects\\workiz\\backend\\controllers\\quoteController.js:3318:37)\n    at async processItemsInParallel (C:\\Projects\\workiz\\backend\\controllers\\quoteController.js:3428:13)\n    at async lookupPricesWithoutSaving (C:\\Projects\\workiz\\backend\\controllers\\quoteController.js:3436:26)"}
2025-08-29 18:24:33 [[31m[31merror[31m[39m] [general]: [ScraperService Fallback] Error searching primary source (Home Depot ID: 682bdb0e477e692811d861e0): Circuit breaker is OPEN. Retry in 176 seconds. | {"service":"workiz-api","stack":"Error: Circuit breaker is OPEN. Retry in 176 seconds.\n    at CircuitBreaker.execute (C:\\Projects\\workiz\\backend\\utils\\circuitBreaker.js:40:15)\n    at ScraperService.searchBySource (C:\\Projects\\workiz\\backend\\scrapers\\ScraperService.js:427:56)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async ScraperService.searchBySourceWithVariations (C:\\Projects\\workiz\\backend\\scrapers\\ScraperService.js:336:19)\n    at async ScraperService.searchMaterials (C:\\Projects\\workiz\\backend\\scrapers\\ScraperService.js:243:19)\n    at async processItem (C:\\Projects\\workiz\\backend\\controllers\\quoteController.js:3318:37)\n    at async processItemsInParallel (C:\\Projects\\workiz\\backend\\controllers\\quoteController.js:3428:13)\n    at async lookupPricesWithoutSaving (C:\\Projects\\workiz\\backend\\controllers\\quoteController.js:3436:26)"}
2025-08-29 18:24:34 [[31m[31merror[31m[39m] [general]: [ScraperService] Error searching in Home Depot for query "price exterior paintable silicone caulk": Circuit breaker is OPEN. Retry in 175 seconds. | {"service":"workiz-api","stack":"Error: Circuit breaker is OPEN. Retry in 175 seconds.\n    at CircuitBreaker.execute (C:\\Projects\\workiz\\backend\\utils\\circuitBreaker.js:40:15)\n    at ScraperService.searchBySource (C:\\Projects\\workiz\\backend\\scrapers\\ScraperService.js:427:56)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async ScraperService.searchBySourceWithVariations (C:\\Projects\\workiz\\backend\\scrapers\\ScraperService.js:336:19)\n    at async ScraperService.searchMaterials (C:\\Projects\\workiz\\backend\\scrapers\\ScraperService.js:243:19)\n    at async processItem (C:\\Projects\\workiz\\backend\\controllers\\quoteController.js:3318:37)\n    at async processItemsInParallel (C:\\Projects\\workiz\\backend\\controllers\\quoteController.js:3428:13)\n    at async lookupPricesWithoutSaving (C:\\Projects\\workiz\\backend\\controllers\\quoteController.js:3436:26)"}
2025-08-29 18:24:34 [[31m[31merror[31m[39m] [general]: [ScraperService Fallback] Error searching primary source (Home Depot ID: 682bdb0e477e692811d861e0): Circuit breaker is OPEN. Retry in 175 seconds. | {"service":"workiz-api","stack":"Error: Circuit breaker is OPEN. Retry in 175 seconds.\n    at CircuitBreaker.execute (C:\\Projects\\workiz\\backend\\utils\\circuitBreaker.js:40:15)\n    at ScraperService.searchBySource (C:\\Projects\\workiz\\backend\\scrapers\\ScraperService.js:427:56)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async ScraperService.searchBySourceWithVariations (C:\\Projects\\workiz\\backend\\scrapers\\ScraperService.js:336:19)\n    at async ScraperService.searchMaterials (C:\\Projects\\workiz\\backend\\scrapers\\ScraperService.js:243:19)\n    at async processItem (C:\\Projects\\workiz\\backend\\controllers\\quoteController.js:3318:37)\n    at async processItemsInParallel (C:\\Projects\\workiz\\backend\\controllers\\quoteController.js:3428:13)\n    at async lookupPricesWithoutSaving (C:\\Projects\\workiz\\backend\\controllers\\quoteController.js:3436:26)"}
2025-08-29 18:24:34 [[31m[31merror[31m[39m] [general]: Crawl4AI process timed out after 20000ms for URL: https://www.homedepot.com/s/price%20Siemens%20QP%2020A%20single%20pole%20breaker | {"service":"workiz-api"}
2025-08-29 18:24:34 [[31m[31merror[31m[39m] [general]: [HD Crawl4AI Scraper] Error searching for "price Siemens QP 20A single pole breaker": Crawl operation timed out after 20000ms | {"service":"workiz-api","stack":"Error: Crawl operation timed out after 20000ms\n    at Timeout._onTimeout (C:\\Projects\\workiz\\backend\\scrapers\\crawl4ai\\crawl4ai-service.js:709:18)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)"}
2025-08-29 18:24:34 [[31m[31merror[31m[39m] [general]: [Home Depot Scraper] Error searching for "price Siemens QP 20A single pole breaker": Crawl operation timed out after 20000ms | {"service":"workiz-api","stack":"Error: Crawl operation timed out after 20000ms\n    at Timeout._onTimeout (C:\\Projects\\workiz\\backend\\scrapers\\crawl4ai\\crawl4ai-service.js:709:18)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)"}
2025-08-29 18:24:34 [[31m[31merror[31m[39m] [general]: [ScraperService] Error searching in Home Depot for query "price Siemens QP 20A single pole breaker": Crawl operation timed out after 20000ms | {"service":"workiz-api","stack":"Error: Crawl operation timed out after 20000ms\n    at Timeout._onTimeout (C:\\Projects\\workiz\\backend\\scrapers\\crawl4ai\\crawl4ai-service.js:709:18)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)"}
2025-08-29 18:24:34 [[31m[31merror[31m[39m] [general]: [ScraperService Fallback] Error searching primary source (Home Depot ID: 682bdb0e477e692811d861e0): Crawl operation timed out after 20000ms | {"service":"workiz-api","stack":"Error: Crawl operation timed out after 20000ms\n    at Timeout._onTimeout (C:\\Projects\\workiz\\backend\\scrapers\\crawl4ai\\crawl4ai-service.js:709:18)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)"}
2025-08-29 18:24:35 [[31m[31merror[31m[39m] [general]: Crawl4AI process timed out after 20000ms for URL: https://www.homedepot.com/s/price%20Siemens%20Q230%2030A%20double%20pole%20breaker | {"service":"workiz-api"}
2025-08-29 18:24:35 [[31m[31merror[31m[39m] [general]: [HD Crawl4AI Scraper] Error searching for "price Siemens Q230 30A double pole breaker": Crawl operation timed out after 20000ms | {"service":"workiz-api","stack":"Error: Crawl operation timed out after 20000ms\n    at Timeout._onTimeout (C:\\Projects\\workiz\\backend\\scrapers\\crawl4ai\\crawl4ai-service.js:709:18)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)"}
2025-08-29 18:24:35 [[31m[31merror[31m[39m] [general]: [Home Depot Scraper] Error searching for "price Siemens Q230 30A double pole breaker": Crawl operation timed out after 20000ms | {"service":"workiz-api","stack":"Error: Crawl operation timed out after 20000ms\n    at Timeout._onTimeout (C:\\Projects\\workiz\\backend\\scrapers\\crawl4ai\\crawl4ai-service.js:709:18)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)"}
2025-08-29 18:24:35 [[31m[31merror[31m[39m] [general]: [ScraperService] Error searching in Home Depot for query "price Siemens Q230 30A double pole breaker": Crawl operation timed out after 20000ms | {"service":"workiz-api","stack":"Error: Crawl operation timed out after 20000ms\n    at Timeout._onTimeout (C:\\Projects\\workiz\\backend\\scrapers\\crawl4ai\\crawl4ai-service.js:709:18)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)"}
2025-08-29 18:24:35 [[31m[31merror[31m[39m] [general]: [ScraperService Fallback] Error searching primary source (Home Depot ID: 682bdb0e477e692811d861e0): Crawl operation timed out after 20000ms | {"service":"workiz-api","stack":"Error: Crawl operation timed out after 20000ms\n    at Timeout._onTimeout (C:\\Projects\\workiz\\backend\\scrapers\\crawl4ai\\crawl4ai-service.js:709:18)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)"}
2025-08-29 18:24:35 [[31m[31merror[31m[39m] [general]: Crawl4AI process timed out after 20000ms for URL: https://www.homedepot.com/s/price%20Siemens%20Q250%2050A%20double%20pole%20breaker | {"service":"workiz-api"}
2025-08-29 18:24:35 [[31m[31merror[31m[39m] [general]: [HD Crawl4AI Scraper] Error searching for "price Siemens Q250 50A double pole breaker": Crawl operation timed out after 20000ms | {"service":"workiz-api","stack":"Error: Crawl operation timed out after 20000ms\n    at Timeout._onTimeout (C:\\Projects\\workiz\\backend\\scrapers\\crawl4ai\\crawl4ai-service.js:709:18)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)"}
2025-08-29 18:24:35 [[31m[31merror[31m[39m] [general]: [Home Depot Scraper] Error searching for "price Siemens Q250 50A double pole breaker": Crawl operation timed out after 20000ms | {"service":"workiz-api","stack":"Error: Crawl operation timed out after 20000ms\n    at Timeout._onTimeout (C:\\Projects\\workiz\\backend\\scrapers\\crawl4ai\\crawl4ai-service.js:709:18)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)"}
2025-08-29 18:24:35 [[31m[31merror[31m[39m] [general]: [ScraperService] Error searching in Home Depot for query "price Siemens Q250 50A double pole breaker": Crawl operation timed out after 20000ms | {"service":"workiz-api","stack":"Error: Crawl operation timed out after 20000ms\n    at Timeout._onTimeout (C:\\Projects\\workiz\\backend\\scrapers\\crawl4ai\\crawl4ai-service.js:709:18)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)"}
2025-08-29 18:24:36 [[31m[31merror[31m[39m] [general]: [ScraperService Fallback] Error searching primary source (Home Depot ID: 682bdb0e477e692811d861e0): Crawl operation timed out after 20000ms | {"service":"workiz-api","stack":"Error: Crawl operation timed out after 20000ms\n    at Timeout._onTimeout (C:\\Projects\\workiz\\backend\\scrapers\\crawl4ai\\crawl4ai-service.js:709:18)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)"}
2025-08-29 18:24:36 [[31m[31merror[31m[39m] [general]: Crawl4AI process timed out after 20000ms for URL: https://www.homedepot.com/s/price%205%2F8%20inch%208%20ft%20ground%20rod | {"service":"workiz-api"}
2025-08-29 18:24:36 [[31m[31merror[31m[39m] [general]: [HD Crawl4AI Scraper] Error searching for "price 5/8 inch 8 ft ground rod": Crawl operation timed out after 20000ms | {"service":"workiz-api","stack":"Error: Crawl operation timed out after 20000ms\n    at Timeout._onTimeout (C:\\Projects\\workiz\\backend\\scrapers\\crawl4ai\\crawl4ai-service.js:709:18)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)"}
2025-08-29 18:24:36 [[31m[31merror[31m[39m] [general]: [Home Depot Scraper] Error searching for "price 5/8 inch 8 ft ground rod": Crawl operation timed out after 20000ms | {"service":"workiz-api","stack":"Error: Crawl operation timed out after 20000ms\n    at Timeout._onTimeout (C:\\Projects\\workiz\\backend\\scrapers\\crawl4ai\\crawl4ai-service.js:709:18)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)"}
2025-08-29 18:24:36 [[31m[31merror[31m[39m] [general]: [ScraperService] Error searching in Home Depot for query "price 5/8 inch 8 ft ground rod": Crawl operation timed out after 20000ms | {"service":"workiz-api","stack":"Error: Crawl operation timed out after 20000ms\n    at Timeout._onTimeout (C:\\Projects\\workiz\\backend\\scrapers\\crawl4ai\\crawl4ai-service.js:709:18)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)"}
2025-08-29 18:24:36 [[31m[31merror[31m[39m] [general]: [ScraperService Fallback] Error searching primary source (Home Depot ID: 682bdb0e477e692811d861e0): Crawl operation timed out after 20000ms | {"service":"workiz-api","stack":"Error: Crawl operation timed out after 20000ms\n    at Timeout._onTimeout (C:\\Projects\\workiz\\backend\\scrapers\\crawl4ai\\crawl4ai-service.js:709:18)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)"}
