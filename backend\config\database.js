const mongoose = require("mongoose");

// Set strictQuery to false to prepare for Mongoose 7
mongoose.set("strictQuery", false);

// MongoDB connection string - explicitly using workiz-mongo-rs for connection
const MONGODB_URI =
  process.env.MONGODB_URI ||
  "mongodb://workiz-mongo-rs:27017/workiz_clone?replicaSet=rs0";

// Print the MongoDB URI being used
console.log("Using MongoDB connection string:", MONGODB_URI);

// MongoDB connection options
const options = {
  autoIndex: true,
  maxPoolSize: 10,
  serverSelectionTimeoutMS: 5000,
  socketTimeoutMS: 45000,
  family: 4, // Use IPv4, skip trying IPv6
};

// Connect to MongoDB with enhanced retry logic
const connectDB = async () => {
  const maxRetries = 5;
  const retryDelay = 5000; // 5 seconds

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      const conn = await mongoose.connect(MONGODB_URI, options);
      console.log(`MongoDB Connected: ${conn.connection.host}`);
      return conn;
    } catch (error) {
      if (attempt === maxRetries) {
        console.error(
          `Failed to connect to MongoDB after ${maxRetries} attempts: ${error.message}`
        );
        process.exit(1);
      }

      console.warn(
        `MongoDB connection attempt ${attempt} failed: ${
          error.message
        }. Retrying in ${retryDelay / 1000} seconds...`
      );
      await new Promise((resolve) => setTimeout(resolve, retryDelay));
    }
  }
};

// Handle connection events
mongoose.connection.on("connected", () => {
  console.log("Mongoose connected to MongoDB");
});

mongoose.connection.on("error", (err) => {
  console.error(`Mongoose connection error: ${err}`);
});

mongoose.connection.on("disconnected", () => {
  console.log("Mongoose disconnected from MongoDB");
});

// Close MongoDB connection when Node process ends
process.on("SIGINT", async () => {
  await mongoose.connection.close();
  console.log("Mongoose connection closed due to app termination");
  process.exit(0);
});

module.exports = connectDB;
