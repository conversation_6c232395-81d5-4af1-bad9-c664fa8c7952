const Customer = require("../models/Customer");
const fs = require("fs");
const path = require("path");
const logger = require("../utils/logger");
const ApiError = require("../utils/ApiError");

/**
 * @desc    Upload images for a customer
 * @route   POST /api/customers/:id/images
 * @access  Private
 */
const uploadCustomerImages = async (req, res) => {
  const customerId = req.params.id;
  const userId = req.user.id; // From auth middleware

  logger.info(
    `Attempting to upload images for customer ${customerId} by user ${userId}`
  );

  try {
    const customer = await Customer.findById(customerId);
    if (!customer) {
      logger.warn(`Customer not found for image upload: ${customerId}`);
      return res.status(404).json({ message: "Customer not found" });
    }

    if (!req.files || req.files.length === 0) {
      logger.warn(`No files uploaded for customer ${customerId}`);
      return res
        .status(400)
        .json({
          message:
            'No files uploaded. Ensure the field name is "customerImages".',
        });
    }

    // Process uploaded files
    const uploadedImages = req.files.map((file) => {
      // Create relative URL path for the file
      const relativeUrl = `/uploads/customers/${customerId}/${file.filename}`;

      return {
        filename: file.filename,
        originalName: file.originalname,
        url: relativeUrl,
        mimeType: file.mimetype,
        size: file.size,
        category: req.body.category || "other", // Default to 'other' if not specified
        title: req.body.title || file.originalname,
        description: req.body.description || "",
        uploadedBy: userId,
        uploadedAt: new Date(),
      };
    });

    // Initialize customerImages array if it doesn't exist
    if (!customer.customerImages) {
      customer.customerImages = [];
    }

    // Add new images to the customer
    customer.customerImages.push(...uploadedImages);

    // Save the customer with the new images
    try {
      await customer.save();

      // After saving, trigger AI analysis for each image asynchronously
      // We don't await this to avoid delaying the response
      uploadedImages.forEach((image) => {
        try {
          // Call the AI analysis endpoint
          const analysisData = {
            imageUrl: image.url,
            imageId: image._id,
            customerId: customerId,
          };

          // Make request to AI analysis endpoint
          require("axios")
            .post(
              `${
                process.env.BASE_URL || "http://localhost:5000"
              }/api/ai/analyze-image`,
              analysisData,
              {
                headers: {
                  "Content-Type": "application/json",
                  Authorization: `Bearer ${
                    req.headers.authorization.split(" ")[1]
                  }`,
                },
              }
            )
            .then((response) => {
              logger.info(`AI analysis completed for image ${image._id}`);
            })
            .catch((error) => {
              logger.error(
                `Error in AI analysis for image ${image._id}: ${error.message}`
              );
            });
        } catch (analysisError) {
          logger.error(
            `Error triggering AI analysis for image ${image._id}: ${analysisError.message}`
          );
          // Continue with the next image
        }
      });
    } catch (saveError) {
      logger.error(
        `Error saving customer with new images: ${saveError.message}`,
        { stack: saveError.stack }
      );
      return res
        .status(500)
        .json({ message: "Error saving customer with new images" });
    }

    logger.info(
      `Successfully uploaded ${uploadedImages.length} images for customer ${customerId}`
    );
    res.status(200).json({
      success: true,
      message: `${uploadedImages.length} images uploaded successfully`,
      images: uploadedImages,
    });
  } catch (error) {
    logger.error(
      `Error uploading images for customer ${customerId}: ${error.message}`,
      { stack: error.stack }
    );

    // Handle potential multer errors
    if (error.code === "LIMIT_FILE_SIZE") {
      return res
        .status(400)
        .json({ message: "File size exceeds the limit (10MB)." });
    }
    if (error.message.includes("Invalid file type")) {
      return res.status(400).json({ message: error.message });
    }

    res.status(500).json({ message: "Server error during file upload" });
  }
};

/**
 * @desc    Get all images for a customer
 * @route   GET /api/customers/:id/images
 * @access  Private
 */
const getCustomerImages = async (req, res) => {
  const customerId = req.params.id;

  try {
    const customer = await Customer.findById(customerId);
    if (!customer) {
      return res.status(404).json({ message: "Customer not found" });
    }

    // Return the images array or empty array if none exist
    res.status(200).json(customer.customerImages || []);
  } catch (error) {
    logger.error(
      `Error fetching images for customer ${customerId}: ${error.message}`
    );
    res.status(500).json({ message: "Server error fetching customer images" });
  }
};

/**
 * @desc    Delete a customer image
 * @route   DELETE /api/customers/:id/images/:imageId
 * @access  Private
 */
const deleteCustomerImage = async (req, res) => {
  const { id: customerId, imageId } = req.params;

  try {
    const customer = await Customer.findById(customerId);
    if (!customer) {
      return res.status(404).json({ message: "Customer not found" });
    }

    // Find the image in the customer's images array
    const imageIndex = customer.customerImages.findIndex(
      (img) => img._id.toString() === imageId
    );
    if (imageIndex === -1) {
      return res.status(404).json({ message: "Image not found" });
    }

    // Get the image to delete
    const imageToDelete = customer.customerImages[imageIndex];

    // Remove the image from the array
    customer.customerImages.splice(imageIndex, 1);
    await customer.save();

    // Try to delete the actual file from the filesystem
    try {
      const filePath = path.join(__dirname, "..", imageToDelete.url);
      if (fs.existsSync(filePath)) {
        fs.unlinkSync(filePath);
        logger.info(`Deleted file: ${filePath}`);
      }
    } catch (fileError) {
      logger.warn(
        `Could not delete file for image ${imageId}: ${fileError.message}`
      );
      // Continue even if file deletion fails
    }

    logger.info(
      `Successfully deleted image ${imageId} from customer ${customerId}`
    );
    res.status(200).json({
      success: true,
      message: "Image deleted successfully",
    });
  } catch (error) {
    logger.error(
      `Error deleting image ${imageId} from customer ${customerId}: ${error.message}`
    );
    res.status(500).json({ message: "Server error deleting customer image" });
  }
};

/**
 * @desc    Update a customer image (metadata only)
 * @route   PUT /api/customers/:id/images/:imageId
 * @access  Private
 */
const updateCustomerImage = async (req, res) => {
  const { id: customerId, imageId } = req.params;
  const { title, description, category } = req.body;

  try {
    const customer = await Customer.findById(customerId);
    if (!customer) {
      return res.status(404).json({ message: "Customer not found" });
    }

    // Find the image in the customer's images array
    const imageIndex = customer.customerImages.findIndex(
      (img) => img._id.toString() === imageId
    );
    if (imageIndex === -1) {
      return res.status(404).json({ message: "Image not found" });
    }

    // Update the image metadata
    if (title) customer.customerImages[imageIndex].title = title;
    if (description)
      customer.customerImages[imageIndex].description = description;
    if (category) customer.customerImages[imageIndex].category = category;

    await customer.save();

    logger.info(
      `Successfully updated image ${imageId} for customer ${customerId}`
    );
    res.status(200).json({
      success: true,
      message: "Image updated successfully",
      image: customer.customerImages[imageIndex],
    });
  } catch (error) {
    logger.error(
      `Error updating image ${imageId} for customer ${customerId}: ${error.message}`
    );
    res.status(500).json({ message: "Server error updating customer image" });
  }
};

module.exports = {
  uploadCustomerImages,
  getCustomerImages,
  deleteCustomerImage,
  updateCustomerImage,
};
