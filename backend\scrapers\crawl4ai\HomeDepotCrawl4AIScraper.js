/**
 * HomeDepotCrawl4AIScraper.js
 * Home Depot scraper implementation using Crawl4AI
 */

const BaseCrawl4AIScraper = require("./BaseCrawl4AIScraper");
const logger = require("../../utils/logger");
const ApiError = require("../../utils/ApiError");
const cheerio = require("cheerio");

/**
 * Home Depot scraper using Crawl4AI
 */
class HomeDepotCrawl4AIScraper extends BaseCrawl4AIScraper {
  /**
   * Constructor
   * @param {Object} source - Material source document
   */
  constructor(source) {
    super(source);

    // Home Depot specific configuration
    this.baseUrl = source.baseUrl || "https://www.homedepot.com";

    // Extraction schema for Home Depot products - Updated for current website structure
    this.extractionSchema = {
      name: "Home Depot Products",
      baseSelector:
        "div[data-component^='product-pod:ProductPod'], div.product-pod, article[class*='product'], div[class*='product-item']",
      fields: [
        // Title - multiple fallbacks for robustness
        {
          name: "title",
          selector:
            "span[data-testid*='product-label'], span[data-testid='attribute-product-label'], .product-pod__title a, a[data-testid*='product-title'], h3 a[href*='/p/'], h2 a[href*='/p/'], .product-title__title",
          type: "text",
        },

        // Price - extract just the dollar amount
        {
          name: "price",
          selector:
            "div[data-testid='price-simple'] span[aria-hidden='true'], div[data-testid*='item-price'] span[aria-hidden='true'], .pip-price__integer, span[class*='price__dollars'], .price-format__large--main-price span:first-child, div[class*='price-format__main-price'] span:not([class*='cents']):not([class*='decimal'])",
          type: "text",
        },

        // Raw price text for complex parsing (includes per-unit pricing)
        {
          name: "priceRaw",
          selector:
            "div[data-testid='price-simple'], div[data-testid*='item-price'], div[data-testid='pod-item-price'], div[data-component='price'], .price-format__main-price, [data-testid*='current-price'], .pip-price",
          type: "text",
        },

        // SKU/Internet number
        {
          name: "sku",
          selector:
            "[data-testid='product-internet-number'], [data-testid*='internet-number'], .product-info-bar__detail-copy:contains('Internet #'), .product-identifier__value",
          type: "text",
        },

        // Model number
        {
          name: "modelNumber",
          selector:
            "[data-testid='product-model-number'], [data-testid*='model-number'], .product-info-bar__detail-copy:contains('Model #'), .product-identifier__model",
          type: "text",
        },

        // Brand
        {
          name: "brand",
          selector:
            "[data-component='BrandNameLink'], a[data-testid='product-brand-name'], .product-pod__brand-name, .brand",
          type: "text",
        },

        // Image URL - prioritize main product images
        {
          name: "imageUrl",
          selector:
            "img[data-testid='hero-product-image'], img[data-testid='main-image'], img.product-image__image--hero, .product-pod__image img, img[src*='product-images'], img",
          type: "attribute",
          attribute: "src",
        },

        // Product URL
        {
          name: "url",
          selector:
            "a[data-testid='product-title-link'], .product-pod__title a, a.product-image__link, a[href*='/p/'][href*='.com/p/'], a[href*='/p/']",
          type: "attribute",
          attribute: "href",
        },

        // Rating
        {
          name: "rating",
          selector:
            "[data-testid='average-rating'], .ratings-summary__average-rating, .ratings-summary, [aria-label*='star']",
          type: "text",
        },

        // Review count
        {
          name: "reviewCount",
          selector:
            "[data-testid='ratings-count'], .ratings-summary__count, [aria-label*='review']",
          type: "text",
        },

        // Pack size/quantity
        {
          name: "packSize",
          selector:
            "[data-testid='attribute-package_quantity'], span[data-testid*='package_quantity'], .product-pod__pack-size, span:contains('-Pack'), span:contains('pack')",
          type: "text",
        },

        // Product details/attributes
        {
          name: "productDetails",
          selector:
            ".product-pod__details, .product-attributes, .product-pod__attribute-list",
          type: "text",
        },

        // Availability
        {
          name: "availability",
          selector:
            ".product-pod__availability, [data-testid='product-availability'], .delivery-availability__primary, [data-testid*='availability']",
          type: "text",
        },
      ],
    };

    // Product detail extraction schema - Updated for current website structure
    this.productDetailSchema = {
      name: "Home Depot Product Detail",
      baseSelector: "body",
      fields: [
        // Product title
        {
          name: "title",
          selector:
            "h1[data-testid='product-title'], h1.product-title__title, h1.sui-text-heading-hero-default, .pip__product-title h1",
          type: "text",
        },

        // Price - just the dollar amount
        {
          name: "price",
          selector:
            ".pip-price__integer, [data-testid*='current-price'] span:first-child, .price-format__large--main-price span:first-child, [data-component='PriceBlock'] .price-format__main-price span:not([class*='cents'])",
          type: "text",
        },

        // Raw price for complex parsing
        {
          name: "priceRaw",
          selector:
            ".pip-price, [data-testid*='current-price'], [data-component='PriceBlock'] .price-format__main-price, div[class*='price-format__main-price']",
          type: "text",
        },

        // SKU/Internet number
        {
          name: "sku",
          selector:
            "[data-testid='product-internet-number'], .product-info-bar__detail-copy:contains('Internet #'), .product-info__internet-number, h2.product-identifier__value:contains('Internet #')",
          type: "text",
        },

        // Model number
        {
          name: "modelNumber",
          selector:
            "[data-testid='product-model-number'], .product-info-bar__detail-copy:contains('Model #'), .product-info__model-number",
          type: "text",
        },

        // Brand
        {
          name: "brand",
          selector:
            "[data-component='BrandNameLink'], a[data-testid='product-brand-name'], .product-title__brand-logo-container .bttn__content",
          type: "text",
        },

        // Main product image
        {
          name: "imageUrl",
          selector:
            "img[data-testid='hero-product-image'], img[data-testid='main-image'], img.product-image__image--hero, .mediagallery__mainimageblock img",
          type: "attribute",
          attribute: "src",
        },

        // Description
        {
          name: "description",
          selector:
            "[data-component='description'], .product-description__content, #product-overview, .product-details__description, .sui-expandable-text__content",
          type: "text",
        },

        // Specifications table
        {
          name: "specifications",
          selector:
            ".specs__table, .specifications__table, [data-testid='specifications-table']",
          type: "html",
        },

        // Availability/Delivery info
        {
          name: "availability",
          selector:
            ".delivery-availability__primary, .fulfillment-fulfillment-tile, [data-component*='Availability'], .pip__fulfillment",
          type: "text",
        },

        // Pack size
        {
          name: "packSize",
          selector:
            "[data-testid='attribute-package_quantity'], .product-info-bar__detail:contains('Pack'), .pip__product-attributes span:contains('pack')",
          type: "text",
        },

        // Rating
        {
          name: "rating",
          selector:
            "[data-testid='average-rating'], .ratings-summary__average-rating",
          type: "text",
        },

        // Review count
        {
          name: "reviewCount",
          selector: "[data-testid='ratings-count'], .ratings-summary__count",
          type: "text",
        },

        // Store SKU (additional identifier)
        {
          name: "storeSku",
          selector:
            ".product-info-bar__detail-copy:contains('Store SKU #'), [data-testid='store-sku']",
          type: "text",
        },
      ],
    };
  }

  /**
   * Initialize provider-specific settings
   * @protected
   */
  async _initializeProvider() {
    logger.info("Initializing Home Depot Crawl4AI scraper");
  }

  /**
   * Provider-specific search method
   * @param {string} query - Search query
   * @param {Object} options - Search options
   * @returns {Promise<Array>} - Array of material objects
   * @protected
   */
  async _searchByDescription(query, options = {}) {
    const searchUrl = `${this.baseUrl}/s/${encodeURIComponent(query)}`;

    try {
      logger.info(`[HD Crawl4AI Scraper] Searching Home Depot for "${query}"`);

      const crawlOptions = {
        waitFor: '.product-pod, div[data-component^="product-pod:ProductPod"]', // Wait for product grid to load
        contentFilter: {
          threshold: 0.3,
          thresholdType: "fixed",
          minWordThreshold: 15 // Slightly higher for product pages
        },
        word_count_threshold: 15,
        remove_overlay_elements: true,
        extractionSchema: this.extractionSchema,
        verbose: false, // Reduce logging noise
        // Pass through timeout and other options from the caller
        ...options
      };

      const result = await this._performCrawl(searchUrl, crawlOptions);

      if (!result.success) {
        throw new Error(`Crawl failed: ${result.error}`);
      }

      if (!result.extractedContent) {
        if (result.html_fallback) {
          logger.warn(
            `[HD Crawl4AI Scraper] No extractedContent for "${query}", CSS selectors may be outdated. Attempting HTML parsing fallback.`
          );
          // Try parsing the HTML directly
          const fallbackProducts = this._parseProductsFromHTML(
            result.html_fallback,
            query
          );
          if (fallbackProducts.length > 0) {
            logger.info(
              `[HD Crawl4AI Scraper] HTML fallback successful, found ${fallbackProducts.length} products`
            );
            // Transform the products to match our expected format
            return fallbackProducts.map((product) =>
              this._transformProduct(product)
            );
          }
        } else {
          logger.warn(`[HD Crawl4AI Scraper] No products found for "${query}"`);
        }
        return [];
      }

      // Parse and transform the extracted content to match our expected format
      let extractedProducts;
      try {
        extractedProducts = JSON.parse(result.extractedContent);
      } catch (parseError) {
        logger.error(
          `[HD Crawl4AI Scraper] Failed to parse search results as JSON for "${query}": ${parseError.message}`
        );
        return [];
      }

      if (!Array.isArray(extractedProducts) || extractedProducts.length === 0) {
        logger.warn(
          `[HD Crawl4AI Scraper] No products extracted for "${query}"`
        );
        if (result.html_fallback) {
          logger.info(
            `[HD Crawl4AI Scraper] HTML content is available but CSS extraction returned no results`
          );
        }
        return [];
      }

      const products = extractedProducts.map((product) =>
        this._transformProduct(product)
      );

      // Check if essential fields are missing and try HTML enhancement
      let hasIncompleteData = false;
      products.forEach((product) => {
        if (!product.imageUrl || !product.url || !product.sku) {
          hasIncompleteData = true;
        }
      });

      // If we have incomplete data and HTML fallback available, enhance the products
      if (hasIncompleteData && result.html_fallback) {
        logger.info(
          `[HD Crawl4AI Scraper] Detected incomplete data for "${query}". Enhancing with HTML parsing.`
        );
        const htmlProducts = this._parseProductsFromHTML(
          result.html_fallback,
          query
        );

        // Merge CSS extracted data with HTML parsed data
        for (
          let i = 0;
          i < Math.min(products.length, htmlProducts.length);
          i++
        ) {
          const cssProduct = products[i];
          const htmlProduct = htmlProducts[i];

          // Fill in missing fields from HTML parsing
          if (!cssProduct.imageUrl && htmlProduct.imageUrl) {
            cssProduct.imageUrl = htmlProduct.imageUrl;
          }
          if (!cssProduct.url && htmlProduct.url) {
            cssProduct.url = htmlProduct.url;
          }
          if (!cssProduct.sku && htmlProduct.sku) {
            cssProduct.sku = htmlProduct.sku;
          }
          if (!cssProduct.brand || cssProduct.brand === "Unknown Brand") {
            cssProduct.brand = htmlProduct.brand || cssProduct.brand;
          }
        }

        logger.info(
          `[HD Crawl4AI Scraper] Enhanced ${products.length} products with HTML parsing data`
        );
      }

      // Clean up URLs and add base URL if needed
      products.forEach((product) => {
        if (product.url && !product.url.startsWith("http")) {
          product.url = `${this.baseUrl}${
            product.url.startsWith("/") ? "" : "/"
          }${product.url}`;
        }
        if (product.imageUrl && !product.imageUrl.startsWith("http")) {
          if (product.imageUrl.startsWith("//")) {
            product.imageUrl = `https:${product.imageUrl}`;
          } else if (this.baseUrl) {
            // Ensure baseUrl is defined
            try {
              const urlObj = new URL(product.imageUrl, this.baseUrl);
              product.imageUrl = urlObj.href;
            } catch (e) {
              logger.warn(
                `[HD Crawl4AI Scraper] Could not construct valid image URL from ${product.imageUrl} and base ${this.baseUrl}: ${e.message}`
              );
            }
          }
        }
      });

      logger.info(
        `[HD Crawl4AI Scraper] Processed ${products.length} products for "${query}" from search results.`
      );
      return products;
    } catch (error) {
      logger.error(
        `[HD Crawl4AI Scraper] Error searching for "${query}": ${error.message}`,
        { stack: error.stack }
      );

      // Determine if this is a CAPTCHA issue
      if (
        error.message.includes("CAPTCHA") ||
        error.message.includes("cloudflare") ||
        error.message.includes("bot detection")
      ) {
        throw new ApiError(
          "CAPTCHA detected on Home Depot site",
          403,
          "CAPTCHA_DETECTED"
        );
      }

      throw error;
    }
  }

  /**
   * Get a product by SKU
   * @param {string} sku - Product SKU
   * @param {Object} options - Options
   * @returns {Promise<Object>} - Product object
   */
  async _getBySku(sku, options = {}) {
    logger.info(`[HD Crawl4AI Scraper] Getting product by SKU: ${sku}`);

    // Home Depot doesn't have a direct SKU search endpoint, so we'll search by SKU
    const results = await this._searchByDescription(sku, options);

    // Find the exact match
    const exactMatch = results.find(
      (product) =>
        product.sku === sku || (product.rawData && product.rawData.sku === sku)
    );

    if (exactMatch) {
      return exactMatch;
    }

    // If no exact match, return the first result
    if (results.length > 0) {
      return results[0];
    }

    throw new Error(`Product with SKU ${sku} not found`);
  }

  /**
   * Public method to get a product by URL
   * @param {string} url - Product URL
   * @param {Object} options - Options
   * @returns {Promise<Object>} - Product object
   */
  async getByUrl(url, options = {}) {
    return this._getByUrl(url, options);
  }

  /**
   * Get a product by URL (private implementation)
   * @param {string} url - Product URL
   * @param {Object} options - Options
   * @returns {Promise<Object>} - Product object
   */
  async _getByUrl(url, options = {}) {
    const cleanUrl = this._cleanUrl(url);
    logger.info(`[HD Crawl4AI Scraper] Getting product by URL: ${cleanUrl}`);

    try {
      const crawlOptions = {
        waitFor:
          'h1[data-testid="product-title"], h1.product-title__title, .product-details__title', // Wait for product title to load
        contentFilter: {
          minTextBlocks: 3,
          minTextLength: 50,
        },
        extractionSchema: this.productDetailSchema,
      };

      const result = await this._performCrawl(cleanUrl, crawlOptions);

      if (!result.success) {
        throw new Error(`Crawl failed: ${result.error}`);
      }

      // Check if we have extracted content or need to fall back to HTML parsing
      if (!result.extractedContent) {
        if (result.html_fallback) {
          logger.warn(
            `[HD Crawl4AI Scraper] No extractedContent, attempting HTML parsing fallback for ${cleanUrl}`
          );
          // Try parsing the HTML directly
          const fallbackProduct = this._parseProductDetailsFromHTML(
            result.html_fallback,
            cleanUrl
          );
          if (fallbackProduct) {
            logger.info(
              `[HD Crawl4AI Scraper] HTML fallback successful for product details`
            );
            // Transform and return the product
            const transformed = this._transformProduct(fallbackProduct);
            transformed.url = cleanUrl;
            return transformed;
          }
          throw new Error(
            `CSS extraction failed and HTML fallback unsuccessful - Home Depot may have changed their page structure. URL: ${cleanUrl}`
          );
        } else {
          logger.warn(
            `[HD Crawl4AI Scraper] No extractedContent and no html_fallback in result for ${cleanUrl}`
          );
          throw new Error(`No product data extracted from ${cleanUrl}`);
        }
      }

      // Parse and transform the extracted content
      let extractedProducts;
      try {
        extractedProducts = JSON.parse(result.extractedContent);
        logger.debug(
          `[HD Crawl4AI Scraper] Parsed extractedContent: ${JSON.stringify(
            extractedProducts
          ).substring(0, 500)}`
        );
      } catch (parseError) {
        logger.error(
          `[HD Crawl4AI Scraper] Failed to parse extractedContent as JSON: ${parseError.message}`
        );
        logger.debug(
          `[HD Crawl4AI Scraper] Raw extractedContent: ${result.extractedContent?.substring(
            0,
            500
          )}`
        );
        throw new Error(
          `Invalid JSON in extracted content: ${parseError.message}`
        );
      }

      if (!Array.isArray(extractedProducts) || extractedProducts.length === 0) {
        logger.warn(
          `[HD Crawl4AI Scraper] Extracted products is empty or not an array`
        );
        logger.debug(
          `[HD Crawl4AI Scraper] Full result object: ${JSON.stringify(
            result
          ).substring(0, 1000)}`
        );

        // Check if we have html_fallback we could use
        if (result.html_fallback) {
          logger.info(
            `[HD Crawl4AI Scraper] CSS selectors may be outdated. HTML fallback is available but not implemented.`
          );
          throw new Error(
            `CSS extraction returned empty results - Home Depot page structure may have changed. URL: ${cleanUrl}`
          );
        }

        throw new Error(`No product data found at ${cleanUrl}`);
      }

      // Product detail page should have only one result
      const productData = extractedProducts[0];
      const product = this._transformProduct(productData);

      // Add the URL
      product.url = cleanUrl;

      // Clean up image URL if needed
      if (product.imageUrl && !product.imageUrl.startsWith("http")) {
        product.imageUrl = `${
          product.imageUrl.startsWith("//") ? "https:" : "https://"
        }${product.imageUrl}`;
      }

      return product;
    } catch (error) {
      logger.error(
        `[HD Crawl4AI Scraper] Error getting product by URL ${cleanUrl}: ${error.message}`,
        { stack: error.stack }
      );

      // Determine if this is a CAPTCHA issue
      if (
        error.message.includes("CAPTCHA") ||
        error.message.includes("cloudflare") ||
        error.message.includes("bot detection")
      ) {
        throw new ApiError(
          "CAPTCHA detected on Home Depot site",
          403,
          "CAPTCHA_DETECTED"
        );
      }

      throw error;
    }
  }

  /**
   * Clean URL by removing tracking parameters
   * @param {string} url - URL to clean
   * @returns {string} - Cleaned URL
   * @protected
   */
  _cleanUrl(url) {
    if (!url) return "";
    try {
      const urlObj = new URL(url);
      ["cid", "irgwc", "cm_mmc", "eeid", "referrer"].forEach((param) => {
        urlObj.searchParams.delete(param);
      });
      return urlObj.toString();
    } catch (error) {
      return url;
    }
  }

  /**
   * Extract SKU from URL or content
   * @param {string} url - Product URL
   * @param {string} pageContent - Page content
   * @returns {string|null} - Extracted SKU or null
   * @protected
   */
  _extractSkuFromUrlOrContent(url, pageContent = "") {
    if (url) {
      const urlMatch = url.match(/\/p\/[^\/]+\/(\d+)/);
      if (urlMatch && urlMatch[1]) return urlMatch[1];
    }
    if (pageContent) {
      const modelMatch = pageContent.match(/Model[:#\s]+(\w+)/i);
      if (modelMatch && modelMatch[1]) return modelMatch[1];
      const internetMatch = pageContent.match(/Internet[:#\s]+(\d+)/i);
      if (internetMatch && internetMatch[1]) return internetMatch[1];
      const skuMatch = pageContent.match(/Store SKU[:#\s]+(\d+)/i);
      if (skuMatch && skuMatch[1]) return skuMatch[1];
    }
    return null;
  }

  /**
   * Parse products from HTML when CSS extraction fails
   * @param {string} html - Raw HTML content
   * @param {string} query - Search query
   * @returns {Array} - Array of parsed products
   * @private
   */
  _parseProductsFromHTML(html, query) {
    const $ = cheerio.load(html);
    const products = [];

    logger.info(
      `[HD HTML Parser] Attempting to parse products from HTML for query: "${query}"`
    );

    // Try multiple selectors to find product containers
    const productSelectors = [
      'div[data-component*="product"]',
      "div.product-pod",
      'article[class*="product"]',
      'div[class*="product-item"]',
      'div[class*="search-result"]',
    ];

    let productElements = null;
    for (const selector of productSelectors) {
      productElements = $(selector);
      if (productElements.length > 0) {
        logger.debug(
          `[HD HTML Parser] Found ${productElements.length} products using selector: ${selector}`
        );
        break;
      }
    }

    if (!productElements || productElements.length === 0) {
      logger.warn(
        `[HD HTML Parser] No products found in HTML for query: "${query}"`
      );
      return [];
    }

    productElements.each((index, element) => {
      try {
        const $product = $(element);

        // Extract title - try multiple approaches
        let title =
          $product.find('a[data-testid*="product-title"]').text().trim() ||
          $product.find(".product-pod__title a").text().trim() ||
          $product.find("h3 a").text().trim() ||
          $product.find("h2 a").text().trim() ||
          $product.find('a[href*="/p/"]').first().text().trim();

        // Extract price
        let priceText =
          $product.find('[data-testid*="price"]').text().trim() ||
          $product.find(".price").text().trim() ||
          $product.find('[class*="price"]').text().trim();

        // Extract URL
        let url =
          $product.find('a[data-testid*="product-title"]').attr("href") ||
          $product.find(".product-pod__title a").attr("href") ||
          $product.find('a[href*="/p/"]').first().attr("href");

        // Extract image
        let imageUrl =
          $product.find("img").first().attr("src") ||
          $product.find("img").first().attr("data-src");

        // Extract brand
        let brand =
          $product.find('[data-component="BrandNameLink"]').text().trim() ||
          $product.find(".brand").text().trim();

        // Extract SKU from URL if possible
        let sku = null;
        if (url) {
          const skuMatch = url.match(/\/(\d+)$/);
          if (skuMatch) {
            sku = skuMatch[1];
          }
        }

        if (title && (priceText || sku)) {
          const product = {
            title,
            price: priceText,
            priceRaw: priceText,
            url: url
              ? url.startsWith("http")
                ? url
                : `https://www.homedepot.com${url}`
              : null,
            imageUrl: imageUrl
              ? imageUrl.startsWith("http")
                ? imageUrl
                : `https:${imageUrl}`
              : null,
            brand: brand || "Unknown",
            sku: sku,
            method: "html_parsing",
          };

          products.push(product);
          logger.debug(`[HD HTML Parser] Extracted product: ${title}`);
        }
      } catch (err) {
        logger.error(
          `[HD HTML Parser] Error parsing product element: ${err.message}`
        );
      }
    });

    logger.info(
      `[HD HTML Parser] Successfully parsed ${products.length} products from HTML`
    );
    return products;
  }

  /**
   * Parse product details from HTML when CSS extraction fails
   * @param {string} html - Raw HTML content
   * @param {string} url - Product URL
   * @returns {Object|null} - Parsed product or null
   * @private
   */
  _parseProductDetailsFromHTML(html, url) {
    const $ = cheerio.load(html);

    logger.info(
      `[HD HTML Parser] Attempting to parse product details from HTML for URL: ${url}`
    );

    try {
      // Extract title
      const title =
        $('h1[data-testid="product-title"]').text().trim() ||
        $("h1.product-title__title").text().trim() ||
        $("h1").first().text().trim();

      // Extract price
      const priceText =
        $(".pip-price").text().trim() ||
        $('[data-testid*="current-price"]').text().trim() ||
        $(".price-format__main-price").text().trim() ||
        $(".price").first().text().trim();

      // Extract SKU/Internet number
      const sku =
        $('[data-testid="product-internet-number"]').text().trim() ||
        $('.product-info-bar__detail-copy:contains("Internet #")')
          .next()
          .text()
          .trim() ||
        this._extractSkuFromUrlOrContent(url, $.html());

      // Extract model number
      const modelNumber =
        $('[data-testid="product-model-number"]').text().trim() ||
        $('.product-info-bar__detail-copy:contains("Model #")')
          .next()
          .text()
          .trim();

      // Extract brand
      const brand =
        $('[data-component="BrandNameLink"]').text().trim() ||
        $('a[data-testid="product-brand-name"]').text().trim() ||
        $(".brand").first().text().trim();

      // Extract image URL
      const imageUrl =
        $('img[data-testid="hero-product-image"]').attr("src") ||
        $('img[data-testid="main-image"]').attr("src") ||
        $("img.product-image__image--hero").attr("src") ||
        $('img[src*="product-images"]').first().attr("src");

      // Extract description
      const description =
        $('[data-component="description"]').text().trim() ||
        $(".product-description__content").text().trim() ||
        $("#product-overview").text().trim();

      if (title) {
        const product = {
          title,
          price: priceText,
          priceRaw: priceText,
          sku: sku || this._extractSkuFromUrlOrContent(url),
          modelNumber,
          brand: brand || "Unknown",
          imageUrl: imageUrl
            ? imageUrl.startsWith("http")
              ? imageUrl
              : `https:${imageUrl}`
            : null,
          url,
          description,
          method: "html_parsing_details",
        };

        logger.info(
          `[HD HTML Parser] Successfully parsed product details: ${title}`
        );
        return product;
      }
    } catch (err) {
      logger.error(
        `[HD HTML Parser] Error parsing product details: ${err.message}`
      );
    }

    return null;
  }
}

module.exports = HomeDepotCrawl4AIScraper;
