const fs = require("fs").promises;
const path = require("path");
const handlebars = require("handlebars");
const { getGeminiResponse } = require("./geminiService");
const { cacheGet, cacheSet } = require("./cache");

/**
 * Template types and their base paths
 */
const TEMPLATE_PATHS = {
  invoice: {
    email: path.join(__dirname, "../templates/email/invoice"),
    pdf: path.join(__dirname, "../templates/pdf/invoice"),
  },
  reminder: {
    email: path.join(__dirname, "../templates/email/reminder"),
    pdf: path.join(__dirname, "../templates/pdf/reminder"),
  },
  receipt: {
    email: path.join(__dirname, "../templates/email/receipt"),
    pdf: path.join(__dirname, "../templates/pdf/receipt"),
  },
};

/**
 * Generate template based on type and options
 */
async function generateTemplate(type, options = {}) {
  // Try to get from cache first
  const cacheKey = `template:${type}:${JSON.stringify(options)}`;
  const cached = await cacheGet(cacheKey);
  if (cached) return cached;

  try {
    // Try to load template from file system
    const template = await loadTemplateFromFile(type, options);
    if (template) {
      await cacheSet(cacheKey, template, 3600); // Cache for 1 hour
      return template;
    }

    // Generate template using AI if file not found
    const generatedTemplate = await generateTemplateWithAI(type, options);
    await cacheSet(cacheKey, generatedTemplate, 3600);

    // Save generated template for future use
    await saveTemplateToFile(type, options, generatedTemplate);

    return generatedTemplate;
  } catch (error) {
    console.error("Template generation error:", error);
    return getFallbackTemplate(type);
  }
}

/**
 * Load template from filesystem
 */
async function loadTemplateFromFile(type, options) {
  try {
    const basePath = TEMPLATE_PATHS[type]?.[options.type || "email"];
    if (!basePath) return null;

    const templatePath = path.join(
      basePath,
      `${options.status || "default"}.hbs`
    );

    const template = await fs.readFile(templatePath, "utf8");
    return handlebars.compile(template);
  } catch (error) {
    if (error.code === "ENOENT") return null;
    throw error;
  }
}

/**
 * Generate template using Gemini AI
 */
async function generateTemplateWithAI(type, options) {
  const prompt = generateTemplatePrompt(type, options);

  // System prompt for template generation
  const systemPrompt =
    "You are an expert at creating professional email and document templates.";

  // Get response from Gemini
  const templateContent = await getGeminiResponse(systemPrompt, prompt, {
    temperature: 0.7,
    maxTokens: 3000, // Email templates need 3000 tokens per guidelines
  });

  return templateContent;
}

/**
 * Generate appropriate prompt based on template type
 */
function generateTemplatePrompt(type, options) {
  const prompts = {
    invoice: `Create a professional ${
      options.type
    } template for an invoice with the following requirements:
      - Modern and clean design
      - Placeholders for company details
      - Space for invoice items table
      - Payment instructions section
      - Terms and conditions area
      - Support for both email and PDF formats
      Current status: ${options.status || "default"}
      Include placeholders like {{customerName}}, {{invoiceNumber}}, etc.`,

    reminder: `Create a payment reminder template with the following characteristics:
      - Professional but firm tone
      - Clear payment instructions
      - Outstanding balance information
      - Due date reminder
      - Payment options
      - Contact information
      Reminder type: ${options.type || "gentle"}`,

    receipt: `Create a payment receipt template that includes:
      - Transaction details
      - Payment method
      - Amount paid
      - Remaining balance
      - Thank you message
      - Contact information
      Format: ${options.type || "email"}`,
  };

  return prompts[type] || prompts.invoice;
}

/**
 * Save generated template to filesystem
 */
async function saveTemplateToFile(type, options, template) {
  try {
    const basePath = TEMPLATE_PATHS[type]?.[options.type || "email"];
    if (!basePath) return;

    // Create directory if it doesn't exist
    await fs.mkdir(basePath, { recursive: true });

    const templatePath = path.join(
      basePath,
      `${options.status || "default"}.hbs`
    );

    await fs.writeFile(templatePath, template);
  } catch (error) {
    console.error("Error saving template:", error);
  }
}

/**
 * Get fallback template if everything else fails
 */
function getFallbackTemplate(type) {
  const fallbacks = {
    invoice: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2>Invoice {{invoiceNumber}}</h2>
        <p>Dear {{customerName}},</p>
        <p>Please find attached your invoice for {{amount}}.</p>
        <p>Due date: {{dueDate}}</p>
        {{items}}
        <p>Thank you for your business!</p>
      </div>
    `,
    reminder: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2>Payment Reminder</h2>
        <p>Dear {{customerName}},</p>
        <p>This is a reminder that invoice {{invoiceNumber}} for {{amount}} is due.</p>
        <p>Please process the payment at your earliest convenience.</p>
      </div>
    `,
    receipt: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2>Payment Receipt</h2>
        <p>Dear {{customerName}},</p>
        <p>Thank you for your payment of {{amount}}.</p>
        <p>Invoice: {{invoiceNumber}}</p>
        <p>Date: {{paymentDate}}</p>
      </div>
    `,
  };

  return fallbacks[type] || fallbacks.invoice;
}

// Register Handlebars helpers
handlebars.registerHelper("formatDate", function (date) {
  return new Date(date).toLocaleDateString();
});

handlebars.registerHelper("formatCurrency", function (amount) {
  return new Intl.NumberFormat("en-US", {
    style: "currency",
    currency: "USD",
  }).format(amount);
});

handlebars.registerHelper("toUpperCase", function (str) {
  return str.toUpperCase();
});

module.exports = {
  generateTemplate,
  loadTemplateFromFile,
  saveTemplateToFile,
};
