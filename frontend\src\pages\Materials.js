import React, { useState, useEffect, useCallback } from "react"; // Added useCallback
import {
  Container,
  Typography,
  Box,
  Paper,
  Grid,
  Button,
  TextField,
  InputAdornment,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  Chip,
  IconButton,
  CircularProgress,
  Alert,
  Tooltip,
  Card,
  CardContent,
} from "@mui/material";
import {
  Search as SearchIcon,
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Refresh as RefreshIcon,
  Inventory as InventoryIcon,
  Category as CategoryIcon,
  Warning as WarningIcon,
} from "@mui/icons-material";
import { useNavigate } from "react-router-dom";
import { useSelector, useDispatch } from "react-redux"; // Added useDispatch
import {
  getMaterialItems,
  deleteMaterialItem,
  getMaterialCategories,
  getMaterialStats,
  clearMaterialError,
} from "../slices/materialSlice"; // Import Redux actions
import { enqueueSnackbar } from "../slices/snackbarSlice"; // For notifications

const Materials = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch();

  // Select state from Redux store
  const {
    materialItems,
    loading,
    error,
    // totalPages, // Not directly used, calculated by pagination component
    // currentPage, // Not directly used, local `page` state controls pagination
    totalItems, // Use totalItems from slice for pagination count
    categories,
    stats,
  } = useSelector((state) => state.materials); // Use 'materials' slice name

  // Local state for UI controls
  const [searchTerm, setSearchTerm] = useState("");
  const [page, setPage] = useState(0); // MUI TablePagination is 0-based
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [activeCategoryFilter, setActiveCategoryFilter] = useState(""); // For category chip selection
  const [showLowStock, setShowLowStock] = useState(false); // For low stock filter

  // Fetch data function using useCallback
  const loadMaterials = useCallback(() => {
    const filters = {};
    if (searchTerm.trim()) {
      filters.keyword = searchTerm.trim();
    }
    if (activeCategoryFilter) {
      filters.category = activeCategoryFilter;
    }
    if (showLowStock) {
      filters.lowStock = "true";
    }

    dispatch(getMaterialItems({ page: page + 1, limit: rowsPerPage, filters }));
  }, [
    dispatch,
    page,
    rowsPerPage,
    searchTerm,
    activeCategoryFilter,
    showLowStock,
  ]);

  // Initial data fetch and fetch on dependency change
  useEffect(() => {
    dispatch(getMaterialCategories());
    dispatch(getMaterialStats());
    loadMaterials(); // Load materials initially

    // Clear error on component unmount
    return () => {
      dispatch(clearMaterialError());
    };
  }, [dispatch, loadMaterials]); // Depend on loadMaterials

  // Handle search input change (debounced search could be added here)
  const handleSearchChange = (event) => {
    setSearchTerm(event.target.value);
    // Optionally trigger search immediately or via a button/debounce
  };

  // Apply filters and search (e.g., called by a search button or on filter change)
  const applyFiltersAndSearch = () => {
    setPage(0); // Reset page when filters change
    loadMaterials();
  };

  // Handle category filter click
  const handleCategoryFilter = (category) => {
    setActiveCategoryFilter(category);
    setShowLowStock(false); // Turn off low stock filter when category is selected
    setPage(0);
    // Trigger data load immediately after setting filter
    // Need to pass the new category directly as state update might not be immediate
    const filters = { category: category };
    if (searchTerm.trim()) filters.keyword = searchTerm.trim();
    dispatch(getMaterialItems({ page: 1, limit: rowsPerPage, filters }));
  };

  // Handle low stock filter click
  const handleLowStockFilter = () => {
    setShowLowStock(true);
    setActiveCategoryFilter(""); // Turn off category filter
    setPage(0);
    // Trigger data load immediately
    const filters = { lowStock: "true" };
    if (searchTerm.trim()) filters.keyword = searchTerm.trim();
    dispatch(getMaterialItems({ page: 1, limit: rowsPerPage, filters }));
  };

  // Handle clearing all filters
  const handleClearFilters = () => {
    setSearchTerm("");
    setActiveCategoryFilter("");
    setShowLowStock(false);
    setPage(0);
    // Trigger data load immediately
    dispatch(getMaterialItems({ page: 1, limit: rowsPerPage, filters: {} }));
  };

  // Handle page change
  const handleChangePage = (event, newPage) => {
    setPage(newPage);
  };

  // Handle rows per page change
  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  // Handle delete action
  const handleDelete = (id) => {
    if (window.confirm("Are you sure you want to delete this material item?")) {
      dispatch(deleteMaterialItem(id))
        .unwrap()
        .then(() => {
          dispatch(
            enqueueSnackbar({
              message: "Material deleted successfully",
              severity: "success",
            })
          );
          // Optionally re-fetch stats if delete affects them significantly
          dispatch(getMaterialStats());
          // List will update automatically via Redux state change if delete was successful
        })
        .catch((err) => {
          dispatch(
            enqueueSnackbar({
              message: `Error deleting material: ${err}`,
              severity: "error",
            })
          );
        });
    }
  };

  // Format date
  const formatDate = (dateString) => {
    if (!dateString) return "N/A";
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  return (
    <Container maxWidth="xl" sx={{ mt: 4, mb: 4 }}>
      {/* Materials Overview Cards - Now uses stats from Redux */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{ height: "100%" }}>
            <CardContent>
              <Typography color="text.secondary" gutterBottom>
                Total Items
              </Typography>
              <Box
                display="flex"
                justifyContent="space-between"
                alignItems="center"
              >
                <Typography variant="h4" component="div">
                  {loading ? <CircularProgress size={24} /> : stats.totalItems}
                </Typography>
                <InventoryIcon fontSize="large" color="primary" />
              </Box>
              <Typography variant="body2" sx={{ mt: 1 }}>
                Across all categories
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{ height: "100%" }}>
            <CardContent>
              <Typography color="text.secondary" gutterBottom>
                Low Stock Items
              </Typography>
              <Box
                display="flex"
                justifyContent="space-between"
                alignItems="center"
              >
                <Typography
                  variant="h4"
                  component="div"
                  color={stats.lowStock > 0 ? "error" : "inherit"}
                >
                  {loading ? <CircularProgress size={24} /> : stats.lowStock}
                </Typography>
                <WarningIcon
                  fontSize="large"
                  color={stats.lowStock > 0 ? "error" : "disabled"}
                />
              </Box>
              <Typography variant="body2" sx={{ mt: 1 }}>
                Needs restocking soon
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{ height: "100%" }}>
            <CardContent>
              <Typography color="text.secondary" gutterBottom>
                Categories
              </Typography>
              <Box
                display="flex"
                justifyContent="space-between"
                alignItems="center"
              >
                <Typography variant="h4" component="div">
                  {loading ? <CircularProgress size={24} /> : stats.categories}
                </Typography>
                <CategoryIcon fontSize="large" color="primary" />
              </Box>
              <Typography variant="body2" sx={{ mt: 1 }}>
                Product categories
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{ height: "100%" }}>
            <CardContent>
              <Typography color="text.secondary" gutterBottom>
                Total Value (Cost)
              </Typography>
              <Box
                display="flex"
                justifyContent="space-between"
                alignItems="center"
              >
                <Typography variant="h4" component="div">
                  {loading ? (
                    <CircularProgress size={24} />
                  ) : (
                    `$${
                      stats.value?.toLocaleString(undefined, {
                        minimumFractionDigits: 2,
                        maximumFractionDigits: 2,
                      }) || "0.00"
                    }`
                  )}
                </Typography>
                <Typography variant="h4" color="primary">
                  $
                </Typography>
              </Box>
              <Typography variant="body2" sx={{ mt: 1 }}>
                Materials asset value
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      <Paper sx={{ p: 2, mb: 4 }}>
        <Box
          display="flex"
          justifyContent="space-between"
          alignItems="center"
          mb={3}
        >
          <Typography component="h1" variant="h5">
            Materials Management
          </Typography>
          <Button
            variant="contained"
            color="primary"
            startIcon={<AddIcon />}
            onClick={() => navigate("/materials/new")}
          >
            Add Item
          </Button>
        </Box>

        {/* Search and Filters */}
        <Grid container spacing={2} sx={{ mb: 3 }}>
          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              placeholder="Search by name, SKU, category..."
              value={searchTerm}
              onChange={handleSearchChange}
              onKeyPress={(e) => e.key === "Enter" && applyFiltersAndSearch()} // Search on Enter
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon />
                  </InputAdornment>
                ),
                endAdornment: (
                  <InputAdornment position="end">
                    <IconButton
                      onClick={applyFiltersAndSearch}
                      aria-label="search"
                      size="small"
                    >
                      <SearchIcon />
                    </IconButton>
                  </InputAdornment>
                ),
              }}
              variant="outlined"
              size="small"
            />
          </Grid>
          <Grid
            item
            xs={12}
            md={6}
            sx={{ display: "flex", justifyContent: "flex-end" }}
          >
            <Button
              startIcon={<RefreshIcon />}
              onClick={loadMaterials}
              disabled={loading}
            >
              Refresh
            </Button>
            <Button onClick={handleClearFilters} sx={{ ml: 1 }}>
              Clear Filters
            </Button>
          </Grid>
        </Grid>

        {/* Category Chips */}
        <Box sx={{ mb: 3, display: "flex", flexWrap: "wrap", gap: 1 }}>
          <Chip
            label="All"
            color={
              !activeCategoryFilter && !showLowStock ? "primary" : "default"
            }
            onClick={handleClearFilters}
            sx={{ mr: 1 }}
          />
          {categories.map((category) => (
            <Chip
              key={category}
              label={category}
              color={activeCategoryFilter === category ? "primary" : "default"}
              onClick={() => handleCategoryFilter(category)}
              sx={{ mr: 1 }}
            />
          ))}
          <Chip
            label="Low Stock"
            color={showLowStock ? "error" : "default"}
            onClick={handleLowStockFilter}
          />
        </Box>

        {/* Error Alert */}
        {error && (
          <Alert severity="error" sx={{ mb: 3 }}>
            {error}
          </Alert>
        )}

        {/* Materials Table */}
        {loading && materialItems.length === 0 ? ( // Show loading only on initial load
          <Box display="flex" justifyContent="center" p={3}>
            <CircularProgress />
          </Box>
        ) : !loading && materialItems.length === 0 && !error ? ( // Show no items found only if not loading and no error
          <Alert severity="info" sx={{ mb: 3 }}>
            No materials items found matching your criteria.
          </Alert>
        ) : (
          <>
            <TableContainer>
              <Table sx={{ minWidth: 650 }}>
                <TableHead>
                  <TableRow>
                    <TableCell>Name</TableCell>
                    <TableCell>SKU</TableCell>
                    <TableCell>Category</TableCell>
                    <TableCell align="right">Quantity</TableCell>
                    <TableCell align="right">Reorder Lvl</TableCell>
                    <TableCell align="right">Unit Price</TableCell>
                    <TableCell>Location</TableCell>
                    <TableCell>Last Updated</TableCell>
                    <TableCell align="center">Actions</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {materialItems.map(
                    (
                      item // Use materialItems from Redux
                    ) => (
                      <TableRow
                        key={item._id}
                        hover
                        sx={{ cursor: "pointer" }}
                        onClick={() => navigate(`/materials/${item._id}`)}
                      >
                        <TableCell component="th" scope="row">
                          <Typography
                            variant="body2"
                            sx={{
                              fontWeight: "medium",
                              "&:hover": { textDecoration: "underline" },
                            }}
                          >
                            {item.name}
                          </Typography>
                        </TableCell>
                        <TableCell>{item.sku}</TableCell>
                        <TableCell>
                          <Chip label={item.category} size="small" />
                        </TableCell>
                        <TableCell align="right">
                          <Typography
                            variant="body2"
                            color={
                              item.quantity < item.reorderLevel
                                ? "error"
                                : "inherit"
                            }
                            fontWeight={
                              item.quantity < item.reorderLevel
                                ? "bold"
                                : "regular"
                            }
                          >
                            {item.quantity}
                          </Typography>
                        </TableCell>
                        <TableCell align="right">{item.reorderLevel}</TableCell>
                        <TableCell align="right">
                          ${item.unitPrice?.toFixed(2) || "0.00"}
                        </TableCell>
                        <TableCell>{item.location}</TableCell>
                        <TableCell>{formatDate(item.updatedAt)}</TableCell>
                        <TableCell
                          align="center"
                          onClick={(e) => e.stopPropagation()}
                        >
                          <Tooltip title="Edit">
                            <IconButton
                              size="small"
                              onClick={() => navigate(`/materials/${item._id}`)}
                            >
                              <EditIcon fontSize="small" />
                            </IconButton>
                          </Tooltip>
                          <Tooltip title="Delete">
                            <IconButton
                              size="small"
                              color="error"
                              onClick={() => handleDelete(item._id)}
                            >
                              <DeleteIcon fontSize="small" />
                            </IconButton>
                          </Tooltip>
                        </TableCell>
                      </TableRow>
                    )
                  )}
                </TableBody>
              </Table>
            </TableContainer>
            <TablePagination
              rowsPerPageOptions={[5, 10, 25]}
              component="div"
              count={totalItems} // Use totalItems from Redux state
              rowsPerPage={rowsPerPage}
              page={page} // Use 0-based page state
              onPageChange={handleChangePage}
              onRowsPerPageChange={handleChangeRowsPerPage}
            />
          </>
        )}
      </Paper>
    </Container>
  );
};

export default Materials;
