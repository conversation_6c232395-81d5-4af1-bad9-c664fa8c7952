# Workiz - Field Service Management System

## Overview
Workiz is an AI-enhanced field service management system designed to streamline job scheduling, technician dispatch, and customer management.

## Key Features
- **AI-Enhanced Quote Generation** - Interactive streaming AI with clarification support
- AI-powered job analysis and technician assignment
- Real-time job tracking and status updates
- Smart scheduling and dispatch optimization
- Customer relationship management
- Automated reporting and analytics

## Getting Started

### Prerequisites
- Node.js >=14.0.0
- MongoDB >=5.0
- Redis >=6.0

### Installation
```bash
# Clone the repository
git clone https://github.com/yourusername/workiz.git

# Install dependencies
npm install

# Set up environment variables
cp .env.example .env

# Start development server
npm run dev

# Start development server with clean logs
npm run dev:clean

# Clear all logs
npm run clear-logs
```

## Testing Infrastructure

### Running Tests
```bash
# Run all tests
npm test

# Run specific test suites
npm run test:unit          # Unit tests
npm run test:integration   # Integration tests
npm run test:ai           # AI feature tests
npm run test:performance  # Performance tests

# Generate coverage report
npm run test:coverage
```

### Test Infrastructure CLI
The project includes a CLI tool for managing the test infrastructure:

```bash
# Show checklist status
npm run checklist status

# Update checklist based on test results
npm run checklist update

# Verify specific section
npm run checklist verify coverage

# Generate detailed report
npm run checklist report
```

To install the CLI tool globally:
```bash
npm install -g .
checklist status
```

### Available Commands
- `status`: Show current test infrastructure status
- `update`: Update checklist based on test results
- `verify`: Verify specific checklist section
- `report`: Generate detailed test infrastructure report

### Options
- `--json`: Output in JSON format
- `--force`: Force update all items
- `--auto-update`: Automatically update checklist on verification
- `--output`: Specify custom output path for reports

## Performance Testing

### Running Performance Tests
```bash
# Run performance test suite
npm run perf

# Generate performance report
npm run perf:report
```

### Performance Metrics
- API response times
- AI service latency
- Database query performance
- Memory usage
- Cache effectiveness

## CI/CD Integration
The project uses GitHub Actions for continuous integration and deployment:

- Automated testing on pull requests
- Performance regression detection
- AI integration validation
- Coverage reporting
- Automated staging deployment

## Development Workflow

### Branch Strategy
- `main`: Production-ready code
- `develop`: Development branch
- `feature/*`: New features
- `bugfix/*`: Bug fixes
- `release/*`: Release preparation

### Log Management
- `npm run dev:clean`: Start development server with clean logs
- `npm run clear-logs`: Clear all log files
- Logs are automatically cleared when using clean start scripts

### Commit Guidelines
```
feat: Add new feature
fix: Fix bug
perf: Performance improvement
test: Add or update tests
docs: Update documentation
```

### Pre-commit Hooks
The project uses husky and lint-staged for pre-commit checks:
- Code linting
- Prettier formatting
- Test execution
- Type checking

## Documentation

### API Documentation
- [AI System Architecture](docs/ai-system.md) - Complete streaming AI and clarification system documentation
- [API Reference](docs/api.md)
- [AI Integration Guide](docs/ai-integration.md)
- [Testing Guide](docs/testing.md)
- [Performance Tuning](docs/performance.md)

### Architecture
- [System Overview](docs/architecture/overview.md)
- [Database Schema](docs/architecture/schema.md)
- [Security](docs/architecture/security.md)

## Contributing
1. Fork the repository
2. Create your feature branch (`git checkout -b feature/amazing-feature`)
3. Run tests (`npm test`)
4. Commit your changes (`git commit -m 'feat: Add amazing feature'`)
5. Push to the branch (`git push origin feature/amazing-feature`)
6. Create a Pull Request

## License
MIT License - see [LICENSE](LICENSE) for details

## Support
- [Issue Tracker](https://github.com/yourusername/workiz/issues)
- [Documentation](docs/README.md)
- [Community Discord](https://discord.gg/workiz)
