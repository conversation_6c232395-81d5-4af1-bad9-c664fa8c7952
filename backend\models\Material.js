const mongoose = require("mongoose");
const MaterialTransaction = require("./MaterialTransaction"); // Import the new model

const materialSchema = new mongoose.Schema(
  {
    name: {
      type: String,
      required: true,
      trim: true,
    },
    description: {
      type: String,
      trim: true,
    },
    sku: {
      type: String,
      unique: true,
      trim: true,
    },
    category: {
      type: String,
      trim: true,
    },
    unitPrice: {
      type: Number,
      required: true,
      min: 0,
    },
    costPrice: {
      type: Number,
      required: true,
      min: 0,
    },
    quantity: {
      type: Number,
      required: true,
      default: 0,
    },
    reorderLevel: {
      type: Number,
      default: 5,
    },
    packQuantity: {
      type: Number,
      default: 1,
    },
    supplier: {
      type: String,
      trim: true,
    },
    brand: {
      type: String,
      trim: true,
    },
    location: {
      type: String,
      trim: true,
    },
    images: [String],
    isActive: {
      type: Boolean,
      default: true,
    },
    // Contractor specific fields (e.g., Electrical)
    voltage: {
      type: String,
      trim: true,
    },
    amperage: {
      type: String,
      trim: true,
    },
    // Material scraping and price estimation fields
    externalSources: [
      {
        source: {
          type: mongoose.Schema.Types.ObjectId,
          ref: "MaterialSource",
        },
        externalSku: {
          type: String,
          trim: true,
        },
        externalName: String,
        urlPattern: String,
        lastPriceCheck: Date,
        priceConfidence: {
          type: Number,
          min: 0,
          max: 1,
          default: 0,
        },
      },
    ],
    priceHistory: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "MaterialPriceHistory",
    },
    lastScrapedPrice: {
      price: Number,
      source: {
        type: mongoose.Schema.Types.ObjectId,
        ref: "MaterialSource",
      },
      sourceUrl: {
        type: String,
        trim: true,
      },
      timestamp: Date,
      isAnomaly: Boolean,
      anomalyScore: Number,
    },
    autoPriceUpdate: {
      enabled: {
        type: Boolean,
        default: false,
      },
      schedule: {
        type: String,
        enum: ["DAILY", "WEEKLY", "MONTHLY", "ON_DEMAND"],
        default: "ON_DEMAND",
      },
      threshold: {
        type: Number,
        min: 0,
        max: 100,
        default: 5, // percentage change that triggers an update
      },
    },
  },
  {
    timestamps: true,
  }
);

// Virtual for profit margin
materialSchema.virtual("profitMargin").get(function () {
  if (this.costPrice === 0) return 0;
  return ((this.unitPrice - this.costPrice) / this.costPrice) * 100;
});

// Virtual for stock value
materialSchema.virtual("stockValue").get(function () {
  return this.quantity * this.costPrice;
});
// Indexes for query optimization
materialSchema.index({ name: 1 });
materialSchema.index({ category: 1 });
materialSchema.index({
  "autoPriceUpdate.enabled": 1,
  "autoPriceUpdate.schedule": 1,
});

// Method to check if item needs reordering
materialSchema.methods.needsReordering = function () {
  return this.quantity <= this.reorderLevel;
};

// Method to update quantity
materialSchema.methods.updateQuantity = async function (
  change,
  transactionDetails = {}
) {
  const quantityBefore = this.quantity;
  let newQuantity = this.quantity + change;

  // Prevent quantity from going below zero
  if (newQuantity < 0) {
    change = -this.quantity; // Adjust change to only subtract available quantity
    newQuantity = 0;
  }

  this.quantity = newQuantity;
  const quantityAfter = this.quantity;

  // Log the transaction
  try {
    await MaterialTransaction.create({
      material: this._id,
      change: change,
      quantityBefore: quantityBefore,
      quantityAfter: quantityAfter,
      type: transactionDetails.type || "ADJUSTMENT",
      user: transactionDetails.user, // User ID must be passed in
      relatedDocId: transactionDetails.relatedDocId,
      relatedDocModel: transactionDetails.relatedDocModel,
      notes: transactionDetails.notes,
    });
  } catch (logError) {
    // Log the error but don't necessarily fail the quantity update
    console.error(
      `Failed to log material transaction for material ${this._id}:`,
      logError
    );
    // Depending on requirements, you might want to throw the error or handle it differently
  }

  await this.save();
  return this;
};

// Static method to get low stock items
materialSchema.statics.getLowStockItems = function () {
  return this.find({
    quantity: { $lte: mongoose.expr({ $field: "reorderLevel" }) },
    isActive: true,
  });
};

// Static method to search materials (formerly inventory)
materialSchema.statics.search = function (query) {
  const searchRegex = new RegExp(query, "i");
  return this.find({
    $or: [
      { name: searchRegex },
      { description: searchRegex },
      { sku: searchRegex },
      { category: searchRegex },
      { supplier: searchRegex },
    ],
    isActive: true,
  });
};

// Price scraping related methods
materialSchema.methods.addExternalSource = async function (
  sourceId,
  externalSku,
  externalName,
  urlPattern
) {
  // Check if this source already exists
  const existingSource = this.externalSources.find(
    (src) => src.source && src.source.toString() === sourceId.toString()
  );

  if (existingSource) {
    // Update existing source
    existingSource.externalSku = externalSku;
    existingSource.externalName = externalName;
    existingSource.urlPattern = urlPattern;
  } else {
    // Add new source
    this.externalSources.push({
      source: sourceId,
      externalSku,
      externalName,
      urlPattern,
      priceConfidence: 0,
    });
  }

  return this.save();
};

materialSchema.methods.removeExternalSource = async function (sourceId) {
  this.externalSources = this.externalSources.filter(
    (src) => !src.source || src.source.toString() !== sourceId.toString()
  );

  return this.save();
};

// Updated signature to accept scrapeData object
materialSchema.methods.updatePriceFromScrape = async function (
  scrapeData,
  sourceId,
  anomalyInfo = {}
) {
  // Find the corresponding external source
  const sourceIndex = this.externalSources.findIndex(
    (src) => src.source && src.source.toString() === sourceId.toString()
  );

  if (sourceIndex !== -1) {
    // Update last price check timestamp
    this.externalSources[sourceIndex].lastPriceCheck = new Date();

    // Update price confidence based on anomaly score
    if (anomalyInfo.isAnomaly === false) {
      // Increase confidence if not an anomaly
      this.externalSources[sourceIndex].priceConfidence = Math.min(
        1,
        this.externalSources[sourceIndex].priceConfidence + 0.1
      );
    } else if (anomalyInfo.isAnomaly === true) {
      // Decrease confidence if an anomaly
      this.externalSources[sourceIndex].priceConfidence = Math.max(
        0,
        this.externalSources[sourceIndex].priceConfidence - 0.2
      );
    }
  }

  // Record the last scraped price, including the source URL
  this.lastScrapedPrice = {
    price: scrapeData.price, // Use price from scrapeData
    source: sourceId,
    sourceUrl: scrapeData.url, // Add the source URL
    timestamp: new Date(),
    isAnomaly: anomalyInfo.isAnomaly || false,
    anomalyScore: anomalyInfo.score || 0,
  };

  // Auto-update the cost price if enabled and not an anomaly
  // NOTE: We update costPrice, not unitPrice (selling price) automatically
  if (this.autoPriceUpdate.enabled && !anomalyInfo.isAnomaly) {
    // Use the calculated unitCostPrice if available, otherwise the main price
    const effectiveScrapedPrice =
      scrapeData.unitCostPrice !== null &&
      scrapeData.unitCostPrice !== undefined
        ? scrapeData.unitCostPrice
        : scrapeData.price;

    // Calculate price difference percentage based on current costPrice
    const priceDiff =
      this.costPrice !== 0
        ? Math.abs(
            ((effectiveScrapedPrice - this.costPrice) / this.costPrice) * 100
          )
        : effectiveScrapedPrice > 0
        ? Infinity
        : 0; // Handle division by zero

    // Only update if difference exceeds threshold
    if (priceDiff > this.autoPriceUpdate.threshold) {
      // Update the cost price to the scraped (unit) price
      this.costPrice = effectiveScrapedPrice;
      // Maybe log this update? logger.info(`Auto-updated costPrice for ${this.sku || this.name} to ${effectiveScrapedPrice}`);
    }
  }

  return this.save();
};

materialSchema.methods.toggleAutoPriceUpdate = async function (
  enabled,
  schedule = "ON_DEMAND",
  threshold = 5
) {
  this.autoPriceUpdate = {
    enabled,
    schedule,
    threshold,
  };

  return this.save();
};

materialSchema.methods.getExternalSourceInfo = function (sourceId) {
  return this.externalSources.find(
    (src) => src.source && src.source.toString() === sourceId.toString()
  );
};

// Static method to find items with external sources
materialSchema.statics.findWithExternalSources = function () {
  return this.find({ "externalSources.0": { $exists: true } })
    .populate("externalSources.source")
    .exec();
};

// Static method to find items due for price update
materialSchema.statics.findDueForPriceUpdate = function (schedule = "DAILY") {
  const dateThreshold = new Date();

  // Set different thresholds based on schedule
  switch (schedule) {
    case "DAILY":
      dateThreshold.setDate(dateThreshold.getDate() - 1);
      break;
    case "WEEKLY":
      dateThreshold.setDate(dateThreshold.getDate() - 7);
      break;
    case "MONTHLY":
      dateThreshold.setMonth(dateThreshold.getMonth() - 1);
      break;
    default:
      // ON_DEMAND: No automatic updates
      return this.find({ "autoPriceUpdate.enabled": false });
  }

  return this.find({
    "autoPriceUpdate.enabled": true,
    "autoPriceUpdate.schedule": schedule,
    $or: [
      { "externalSources.lastPriceCheck": { $lt: dateThreshold } },
      { "externalSources.lastPriceCheck": { $exists: false } },
    ],
  })
    .populate("externalSources.source")
    .exec();
};

// Database indexes for performance optimization
materialSchema.index({ sku: 1 }); // SKU lookup (unique already defined)
materialSchema.index({ name: "text", description: "text" }); // Text search
materialSchema.index({ category: 1, isActive: 1 }); // Category filtering
materialSchema.index({ supplier: 1, brand: 1 }); // Supplier/brand queries
materialSchema.index({ quantity: 1, reorderLevel: 1 }); // Inventory management
materialSchema.index({ unitPrice: 1, costPrice: 1 }); // Price range queries
materialSchema.index({ isActive: 1, createdAt: -1 }); // Active items by date
materialSchema.index({
  "externalSources.source": 1,
  "externalSources.lastPriceCheck": 1,
}); // Price update queries
materialSchema.index({
  "autoPriceUpdate.enabled": 1,
  "autoPriceUpdate.schedule": 1,
}); // Auto-update filtering
materialSchema.index({ voltage: 1, amperage: 1 }); // Electrical specifications
materialSchema.index({ location: 1 }); // Warehouse location

const Material = mongoose.model("Material", materialSchema);

module.exports = Material;
